<?php
/**
 * Finance Dashboard Controller
 * Handles dedicated dashboards for each finance category
 */

require_once 'models/Finance.php';
require_once 'models/Member.php';
require_once 'config/finance_categories.php';
require_once 'helpers/functions.php';

class FinanceDashboardController {
    private $database;
    private $finance;
    private $member;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->finance = new Finance($this->database->getConnection());
        $this->member = new Member($this->database->getConnection());
    }

    /**
     * Tithe Dashboard - Comprehensive tithe tracking and analytics
     */
    public function titheDashboard() {
        try {
            $page_title = getPageTitle('Tithe Dashboard');
            $active_page = 'finance';

            // Get tithe analytics data
            $analytics = $this->getTitheAnalytics();
            
            // Get member tithe tracking data
            $memberTracking = $this->getTitheMemberTracking();
            
            // Get tithe trends data
            $trends = $this->getTitheTrends();

            // Start output buffering
            ob_start();

            // Load tithe dashboard view
            require_once 'views/finances/dashboards/tithe.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading tithe dashboard: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finance');
        }
    }

    /**
     * Pledge Dashboard - Comprehensive pledge tracking and analytics
     */
    public function pledgeDashboard() {
        try {
            $page_title = 'Pledge Dashboard - ICGC Emmanuel Temple';
            $active_page = 'finance';

            // Get pledge analytics data
            $analytics = $this->getPledgeAnalytics();
            
            // Get member pledge tracking data
            $memberTracking = $this->getPledgeMemberTracking();
            
            // Get pledge trends data
            $trends = $this->getPledgeTrends();
            
            // Get recent pledge transactions
            $recentPledges = $this->getRecentPledges(10);

            // Start output buffering
            ob_start();

            // Load pledge dashboard view
            require_once 'views/finances/dashboards/pledge.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading pledge dashboard: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finance');
        }
    }

    /**
     * Pastor Application Dashboard - Dedicated dashboard for pastor applications
     */
    public function pastorApplicationDashboard() {
        try {
            $page_title = 'Pastor Application Dashboard - ICGC Emmanuel Temple';
            $active_page = 'finance';

            // Get pastor application analytics
            $analytics = $this->getPastorApplicationAnalytics();

            // Get member tracking data
            $memberTracking = $this->getPastorApplicationMemberTracking();

            // Get trends data
            $trends = $this->getPastorApplicationTrends();

            // Get recent payments
            $recentPayments = $this->getRecentPastorApplications(10);

            // Get category info
            $categoryInfo = $this->getPastorApplicationCategoryInfo();

            // Start output buffering
            ob_start();

            // Load the pastor application dashboard view
            require_once 'views/finances/dashboards/pastor_application.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading pastor application dashboard: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finance');
        }
    }

    /**
     * Generic Category Dashboard - For any finance category
     */
    public function categoryDashboard() {
        try {
            // Get category from URL parameter
            $category = $_GET['category'] ?? '';
            $type = $_GET['type'] ?? 'income'; // income, expense
            
            if (empty($category)) {
                $_SESSION['flash_message'] = 'Category parameter is required';
                $_SESSION['flash_type'] = 'danger';
                redirect('finance');
                return;
            }

            // Validate category exists
            if (!$this->validateCategory($category, $type)) {
                $_SESSION['flash_message'] = 'Invalid category specified';
                $_SESSION['flash_type'] = 'danger';
                redirect('finance');
                return;
            }

            // Get category configuration
            $categoryConfig = $this->getCategoryConfig($category, $type);
            
            $page_title = $categoryConfig['label'] . ' Dashboard - ICGC Emmanuel Temple';
            $active_page = 'finance';

            // Get category analytics data
            $analytics = $this->getCategoryAnalytics($category, $type);
            
            // Get category trends data
            $trends = $this->getCategoryTrends($category, $type);
            
            // Get recent transactions for this category
            $recentTransactions = $this->getRecentCategoryTransactions($category, $type, 10);

            // Get member tracking if category requires members
            $memberTracking = null;
            if ($categoryConfig['requires_member']) {
                $memberTracking = $this->getCategoryMemberTracking($category, $type);
            }

            // Start output buffering
            ob_start();

            // Load generic category dashboard view
            require_once 'views/finances/dashboards/category.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading category dashboard: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finance');
        }
    }

    /**
     * Get tithe analytics data
     */
    private function getTitheAnalytics() {
        $conn = $this->database->getConnection();

        // Check schema type
        $checkNewSchema = "SHOW COLUMNS FROM finances WHERE Field IN ('transaction_type', 'income_category')";
        $stmt = $conn->prepare($checkNewSchema);
        $stmt->execute();
        $schemaColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasNewSchema = count($schemaColumns) >= 2;

        // Current month tithes
        $currentMonth = date('Y-m');

        if ($hasNewSchema) {
            $whereClause = "transaction_type = 'income' AND income_category = 'tithe'";
        } else {
            $whereClause = "category = 'tithe'";
        }

        $query = "SELECT
                    COUNT(*) as total_transactions,
                    SUM(amount) as total_amount,
                    AVG(amount) as average_amount,
                    COUNT(DISTINCT member_id) as unique_contributors
                  FROM finances
                  WHERE {$whereClause}
                  AND DATE_FORMAT(transaction_date, '%Y-%m') = :current_month";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':current_month', $currentMonth);
        $stmt->execute();
        $currentMonthData = $stmt->fetch(PDO::FETCH_ASSOC);

        // Previous month for comparison
        $previousMonth = date('Y-m', strtotime('-1 month'));
        $stmt->bindParam(':current_month', $previousMonth);
        $stmt->execute();
        $previousMonthData = $stmt->fetch(PDO::FETCH_ASSOC);

        // Calculate growth
        $amountGrowth = $this->calculateGrowthPercentage(
            $previousMonthData['total_amount'], 
            $currentMonthData['total_amount']
        );

        // Year to date
        $currentYear = date('Y');
        $query = "SELECT
                    SUM(amount) as ytd_amount,
                    COUNT(*) as ytd_transactions
                  FROM finances
                  WHERE {$whereClause}
                  AND YEAR(transaction_date) = :current_year";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':current_year', $currentYear);
        $stmt->execute();
        $ytdData = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'current_month' => $currentMonthData,
            'previous_month' => $previousMonthData,
            'amount_growth' => $amountGrowth,
            'ytd' => $ytdData
        ];
    }

    /**
     * Get tithe member tracking data
     */
    private function getTitheMemberTracking() {
        $conn = $this->database->getConnection();

        // Check schema type - check for both transaction_type AND income_category columns
        $checkNewSchema = "SHOW COLUMNS FROM finances WHERE Field IN ('transaction_type', 'income_category')";
        $stmt = $conn->prepare($checkNewSchema);
        $stmt->execute();
        $schemaColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasNewSchema = count($schemaColumns) >= 2; // Both columns must exist

        if ($hasNewSchema) {
            // New schema query
            $query = "SELECT
                        m.id,
                        m.first_name,
                        m.last_name,
                        m.phone_number,
                        m.profile_picture,
                        COUNT(f.id) as total_payments,
                        SUM(f.amount) as total_amount,
                        AVG(f.amount) as average_amount,
                        MAX(f.transaction_date) as last_payment_date,
                        MIN(f.transaction_date) as first_payment_date
                      FROM members m
                      LEFT JOIN finances f ON m.id = f.member_id
                        AND f.transaction_type = 'income'
                        AND f.income_category = 'tithe'
                      WHERE f.id IS NOT NULL
                      GROUP BY m.id
                      ORDER BY total_amount DESC";
        } else {
            // Old schema query
            $query = "SELECT
                        m.id,
                        m.first_name,
                        m.last_name,
                        m.phone_number,
                        m.profile_picture,
                        COUNT(f.id) as total_payments,
                        SUM(f.amount) as total_amount,
                        AVG(f.amount) as average_amount,
                        MAX(f.transaction_date) as last_payment_date,
                        MIN(f.transaction_date) as first_payment_date
                      FROM members m
                      LEFT JOIN finances f ON m.id = f.member_id AND f.category = 'tithe'
                      WHERE f.id IS NOT NULL
                      GROUP BY m.id
                      ORDER BY total_amount DESC";
        }

        $stmt = $conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get tithe trends data for charts
     */
    private function getTitheTrends() {
        $conn = $this->database->getConnection();

        // Check schema type
        $checkNewSchema = "SHOW COLUMNS FROM finances WHERE Field IN ('transaction_type', 'income_category')";
        $stmt = $conn->prepare($checkNewSchema);
        $stmt->execute();
        $schemaColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasNewSchema = count($schemaColumns) >= 2;

        if ($hasNewSchema) {
            $whereClause = "transaction_type = 'income' AND income_category = 'tithe'";
        } else {
            $whereClause = "category = 'tithe'";
        }

        // Monthly trends for the last 12 months
        $query = "SELECT
                    DATE_FORMAT(transaction_date, '%Y-%m') as month,
                    DATE_FORMAT(transaction_date, '%M %Y') as month_label,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count,
                    COUNT(DISTINCT member_id) as unique_contributors
                  FROM finances
                  WHERE {$whereClause}
                  AND transaction_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                  GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
                  ORDER BY month ASC";

        $stmt = $conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get recent tithe transactions
     */
    private function getRecentTithes($limit = 10) {
        $conn = $this->database->getConnection();
        
        $query = "SELECT 
                    f.*,
                    CONCAT(m.first_name, ' ', m.last_name) as member_name,
                    m.phone_number
                  FROM finances f
                  LEFT JOIN members m ON f.member_id = m.id
                  WHERE f.category = 'tithe'
                  ORDER BY f.transaction_date DESC, f.created_at DESC
                  LIMIT :limit";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Calculate growth percentage
     */
    private function calculateGrowthPercentage($previous, $current) {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100, 2);
    }

    /**
     * Validate if category exists in unified database system
     */
    private function validateCategory($category, $type) {
        // UNIFIED SYSTEM: Check if category exists in database
        $stmt = $this->database->getConnection()->prepare("
            SELECT COUNT(*) FROM custom_finance_categories
            WHERE name = ? AND is_active = 1
        ");
        $stmt->execute([$category]);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Get category configuration from unified database system
     */
    private function getCategoryConfig($category, $type) {
        // UNIFIED SYSTEM: Get category from database
        $stmt = $this->database->getConnection()->prepare("
            SELECT * FROM custom_finance_categories
            WHERE name = ? AND is_active = 1
        ");
        $stmt->execute([$category]);
        $categoryData = $stmt->fetch(PDO::FETCH_OBJ);

        if ($categoryData) {
            // Return in format expected by dashboard
            return [
                'label' => $categoryData->label,
                'description' => $categoryData->description ?? $categoryData->label,
                'icon' => 'fas fa-chart-bar', // Default icon
                'color' => 'blue', // Default color
                'requires_member' => isset($categoryData->requires_member) ? $categoryData->requires_member : false
            ];
        }

        // Fallback for system categories not in database
        return [
            'label' => ucfirst(str_replace('_', ' ', $category)),
            'description' => ucfirst(str_replace('_', ' ', $category)),
            'icon' => 'fas fa-chart-bar',
            'color' => 'blue',
            'requires_member' => false // Default to false for fallback
        ];
    }

    /**
     * Get pledge analytics data
     */
    private function getPledgeAnalytics() {
        $conn = $this->database->getConnection();

        // Current month pledges
        $currentMonth = date('Y-m');
        $query = "SELECT
                    COUNT(*) as total_transactions,
                    SUM(amount) as total_amount,
                    AVG(amount) as average_amount,
                    COUNT(DISTINCT member_id) as unique_contributors
                  FROM finances
                  WHERE category = 'pledge'
                  AND DATE_FORMAT(transaction_date, '%Y-%m') = :current_month";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':current_month', $currentMonth);
        $stmt->execute();
        $currentMonthData = $stmt->fetch(PDO::FETCH_ASSOC);

        // Previous month for comparison
        $previousMonth = date('Y-m', strtotime('-1 month'));
        $stmt->bindParam(':current_month', $previousMonth);
        $stmt->execute();
        $previousMonthData = $stmt->fetch(PDO::FETCH_ASSOC);

        // Calculate growth
        $amountGrowth = $this->calculateGrowthPercentage(
            $previousMonthData['total_amount'],
            $currentMonthData['total_amount']
        );

        // Year to date
        $currentYear = date('Y');
        $query = "SELECT
                    SUM(amount) as ytd_amount,
                    COUNT(*) as ytd_transactions
                  FROM finances
                  WHERE category = 'pledge'
                  AND YEAR(transaction_date) = :current_year";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':current_year', $currentYear);
        $stmt->execute();
        $ytdData = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'current_month' => $currentMonthData,
            'previous_month' => $previousMonthData,
            'amount_growth' => $amountGrowth,
            'ytd' => $ytdData
        ];
    }

    /**
     * Get pledge member tracking data
     */
    private function getPledgeMemberTracking() {
        $conn = $this->database->getConnection();

        $query = "SELECT
                    m.id,
                    m.first_name,
                    m.last_name,
                    m.phone_number,
                    m.profile_picture,
                    COUNT(f.id) as total_payments,
                    SUM(f.amount) as total_amount,
                    AVG(f.amount) as average_amount,
                    MAX(f.transaction_date) as last_payment_date,
                    MIN(f.transaction_date) as first_payment_date
                  FROM members m
                  LEFT JOIN finances f ON m.id = f.member_id AND f.category = 'pledge'
                  WHERE f.id IS NOT NULL
                  GROUP BY m.id
                  ORDER BY total_amount DESC";

        $stmt = $conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get pledge trends data for charts
     */
    private function getPledgeTrends() {
        $conn = $this->database->getConnection();

        // Monthly trends for the last 12 months
        $query = "SELECT
                    DATE_FORMAT(transaction_date, '%Y-%m') as month,
                    DATE_FORMAT(transaction_date, '%M %Y') as month_label,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count,
                    COUNT(DISTINCT member_id) as unique_contributors
                  FROM finances
                  WHERE category = 'pledge'
                  AND transaction_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                  GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
                  ORDER BY month ASC";

        $stmt = $conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get recent pledge transactions
     */
    private function getRecentPledges($limit = 10) {
        $conn = $this->database->getConnection();

        $query = "SELECT
                    f.*,
                    CONCAT(m.first_name, ' ', m.last_name) as member_name,
                    m.phone_number
                  FROM finances f
                  LEFT JOIN members m ON f.member_id = m.id
                  WHERE f.category = 'pledge'
                  ORDER BY f.transaction_date DESC, f.created_at DESC
                  LIMIT :limit";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get generic category analytics
     */
    private function getCategoryAnalytics($category, $type) {
        $conn = $this->database->getConnection();

        // Current month data
        $currentMonth = date('Y-m');
        $query = "SELECT
                    COUNT(*) as total_transactions,
                    SUM(amount) as total_amount,
                    AVG(amount) as average_amount";

        if ($type === 'income') {
            $query .= ", COUNT(DISTINCT member_id) as unique_contributors";
        }

        $query .= " FROM finances
                   WHERE category = :category
                   AND DATE_FORMAT(transaction_date, '%Y-%m') = :current_month";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':category', $category);
        $stmt->bindParam(':current_month', $currentMonth);
        $stmt->execute();
        $currentMonthData = $stmt->fetch(PDO::FETCH_ASSOC);

        // Previous month for comparison
        $previousMonth = date('Y-m', strtotime('-1 month'));
        $stmt->bindParam(':current_month', $previousMonth);
        $stmt->execute();
        $previousMonthData = $stmt->fetch(PDO::FETCH_ASSOC);

        // Calculate growth
        $amountGrowth = $this->calculateGrowthPercentage(
            $previousMonthData['total_amount'],
            $currentMonthData['total_amount']
        );

        // Year to date
        $currentYear = date('Y');
        $query = "SELECT
                    SUM(amount) as ytd_amount,
                    COUNT(*) as ytd_transactions
                  FROM finances
                  WHERE category = :category
                  AND YEAR(transaction_date) = :current_year";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':category', $category);
        $stmt->bindParam(':current_year', $currentYear);
        $stmt->execute();
        $ytdData = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'current_month' => $currentMonthData,
            'previous_month' => $previousMonthData,
            'amount_growth' => $amountGrowth,
            'ytd' => $ytdData
        ];
    }

    /**
     * Get category trends data
     */
    private function getCategoryTrends($category, $type) {
        $conn = $this->database->getConnection();

        // Monthly trends for the last 12 months
        $query = "SELECT
                    DATE_FORMAT(transaction_date, '%Y-%m') as month,
                    DATE_FORMAT(transaction_date, '%M %Y') as month_label,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count";

        if ($type === 'income') {
            $query .= ", COUNT(DISTINCT member_id) as unique_contributors";
        }

        $query .= " FROM finances
                   WHERE category = :category
                   AND transaction_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                   GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
                   ORDER BY month ASC";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':category', $category);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get recent transactions for a category
     */
    private function getRecentCategoryTransactions($category, $type, $limit = 10) {
        $conn = $this->database->getConnection();

        $query = "SELECT f.*";

        if ($type === 'income') {
            $query .= ", CONCAT(m.first_name, ' ', m.last_name) as member_name, m.phone_number";
        }

        $query .= " FROM finances f";

        if ($type === 'income') {
            $query .= " LEFT JOIN members m ON f.member_id = m.id";
        }

        $query .= " WHERE f.category = :category
                   ORDER BY f.transaction_date DESC, f.created_at DESC
                   LIMIT :limit";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':category', $category);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get member tracking for a category
     */
    private function getCategoryMemberTracking($category, $type) {
        $conn = $this->database->getConnection();

        $query = "SELECT
                    m.id,
                    m.first_name,
                    m.last_name,
                    m.phone_number,
                    m.profile_picture,
                    COUNT(f.id) as total_payments,
                    SUM(f.amount) as total_amount,
                    AVG(f.amount) as average_amount,
                    MAX(f.transaction_date) as last_payment_date,
                    MIN(f.transaction_date) as first_payment_date
                  FROM members m
                  LEFT JOIN finances f ON m.id = f.member_id AND f.category = :category
                  WHERE f.id IS NOT NULL
                  GROUP BY m.id
                  ORDER BY total_amount DESC";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':category', $category);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get member tithe payment history via AJAX
     */
    public function memberTitheHistory() {
        try {
            $memberId = $_GET['member_id'] ?? '';

            if (empty($memberId) || !is_numeric($memberId)) {
                echo json_encode(['success' => false, 'message' => 'Invalid member ID']);
                return;
            }

            $conn = $this->database->getConnection();

            // Check schema type - check for both transaction_type AND income_category columns
            $checkNewSchema = "SHOW COLUMNS FROM finances WHERE Field IN ('transaction_type', 'income_category')";
            $stmt = $conn->prepare($checkNewSchema);
            $stmt->execute();
            $schemaColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $hasNewSchema = count($schemaColumns) >= 2; // Both columns must exist

            if ($hasNewSchema) {
                // New schema query
                $query = "SELECT
                            f.id,
                            f.amount,
                            f.description,
                            f.transaction_date,
                            f.created_at,
                            f.income_category,
                            DATE_FORMAT(f.transaction_date, '%M %d, %Y') as formatted_date,
                            DATE_FORMAT(f.transaction_date, '%h:%i %p') as formatted_time
                          FROM finances f
                          WHERE f.transaction_type = 'income'
                          AND f.income_category = 'tithe'
                          AND f.member_id = :member_id
                          ORDER BY f.transaction_date DESC, f.created_at DESC
                          LIMIT 20";
            } else {
                // Old schema query
                $query = "SELECT
                            f.id,
                            f.amount,
                            f.description,
                            f.transaction_date,
                            f.created_at,
                            f.category,
                            DATE_FORMAT(f.transaction_date, '%M %d, %Y') as formatted_date,
                            DATE_FORMAT(f.transaction_date, '%h:%i %p') as formatted_time
                          FROM finances f
                          WHERE f.category = 'tithe'
                          AND f.member_id = :member_id
                          ORDER BY f.transaction_date DESC, f.created_at DESC
                          LIMIT 20";
            }

            $stmt = $conn->prepare($query);
            $stmt->bindParam(':member_id', $memberId, PDO::PARAM_INT);
            $stmt->execute();

            $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'payments' => $payments,
                'total' => count($payments)
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error loading payment history: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Debug method to check database directly
     */
    public function debugTithes() {
        try {
            $conn = $this->database->getConnection();

            // Get all tithe records
            $query = "SELECT
                        f.id,
                        f.member_id,
                        f.amount,
                        f.description,
                        f.transaction_date,
                        f.category,
                        f.created_at,
                        CONCAT(m.first_name, ' ', m.last_name) as member_name
                      FROM finances f
                      LEFT JOIN members m ON f.member_id = m.id
                      WHERE f.category = 'tithe'
                      ORDER BY f.transaction_date DESC";

            $stmt = $conn->prepare($query);
            $stmt->execute();
            $allTithes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Filter Emmanuel's records
            $emmanuelTithes = array_filter($allTithes, function($tithe) {
                return stripos($tithe['member_name'], 'emmanuel') !== false;
            });

            echo "<h2>Database Debug - All Tithe Records</h2>";
            echo "<h3>Total Tithe Records: " . count($allTithes) . "</h3>";
            echo "<h3>Emmanuel's Records: " . count($emmanuelTithes) . "</h3>";

            echo "<h4>Emmanuel's Tithe Records:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Member ID</th><th>Name</th><th>Amount</th><th>Date</th><th>Description</th><th>Created</th></tr>";

            foreach ($emmanuelTithes as $tithe) {
                echo "<tr>";
                echo "<td>" . $tithe['id'] . "</td>";
                echo "<td>" . $tithe['member_id'] . "</td>";
                echo "<td>" . $tithe['member_name'] . "</td>";
                echo "<td>GH₵ " . number_format($tithe['amount'], 2) . "</td>";
                echo "<td>" . $tithe['transaction_date'] . "</td>";
                echo "<td>" . ($tithe['description'] ?: 'N/A') . "</td>";
                echo "<td>" . $tithe['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";

            // Show all members with tithes
            $memberQuery = "SELECT
                              m.id,
                              CONCAT(m.first_name, ' ', m.last_name) as name,
                              COUNT(f.id) as tithe_count,
                              SUM(f.amount) as total_amount
                            FROM members m
                            LEFT JOIN finances f ON m.id = f.member_id AND f.category = 'tithe'
                            WHERE f.id IS NOT NULL
                            GROUP BY m.id
                            ORDER BY tithe_count DESC";

            $stmt = $conn->prepare($memberQuery);
            $stmt->execute();
            $memberSummary = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo "<h4>Member Summary (All Members with Tithes):</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Member ID</th><th>Name</th><th>Tithe Count</th><th>Total Amount</th></tr>";

            foreach ($memberSummary as $member) {
                $highlight = stripos($member['name'], 'emmanuel') !== false ? 'style="background-color: yellow;"' : '';
                echo "<tr $highlight>";
                echo "<td>" . $member['id'] . "</td>";
                echo "<td>" . $member['name'] . "</td>";
                echo "<td>" . $member['tithe_count'] . "</td>";
                echo "<td>GH₵ " . number_format($member['total_amount'], 2) . "</td>";
                echo "</tr>";
            }
            echo "</table>";

        } catch (Exception $e) {
            echo "Error: " . $e->getMessage();
        }
    }

    /**
     * Get Pastor Application Analytics Data
     */
    private function getPastorApplicationAnalytics() {
        try {
            $conn = $this->database->getConnection();

            // Get total amount and count
            $totalQuery = "SELECT
                COUNT(*) as total_count,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(AVG(amount), 0) as average_amount
                FROM finance_transactions
                WHERE category = 'Pastor application'
                AND transaction_type = 'income'";

            $stmt = $conn->prepare($totalQuery);
            $stmt->execute();
            $totals = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get current month data
            $currentMonthQuery = "SELECT
                COUNT(*) as current_month_count,
                COALESCE(SUM(amount), 0) as current_month_amount
                FROM finance_transactions
                WHERE category = 'Pastor application'
                AND transaction_type = 'income'
                AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
                AND YEAR(transaction_date) = YEAR(CURRENT_DATE())";

            $stmt = $conn->prepare($currentMonthQuery);
            $stmt->execute();
            $currentMonth = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get last month for growth calculation
            $lastMonthQuery = "SELECT
                COALESCE(SUM(amount), 0) as last_month_amount
                FROM finance_transactions
                WHERE category = 'Pastor application'
                AND transaction_type = 'income'
                AND MONTH(transaction_date) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH)
                AND YEAR(transaction_date) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)";

            $stmt = $conn->prepare($lastMonthQuery);
            $stmt->execute();
            $lastMonth = $stmt->fetch(PDO::FETCH_ASSOC);

            // Calculate growth percentage
            $growthPercentage = 0;
            if ($lastMonth['last_month_amount'] > 0) {
                $growthPercentage = (($currentMonth['current_month_amount'] - $lastMonth['last_month_amount']) / $lastMonth['last_month_amount']) * 100;
            }

            // Get payment method breakdown
            $paymentMethodQuery = "SELECT
                payment_method,
                COALESCE(SUM(amount), 0) as amount
                FROM finance_transactions
                WHERE category = 'Pastor application'
                AND transaction_type = 'income'
                GROUP BY payment_method";

            $stmt = $conn->prepare($paymentMethodQuery);
            $stmt->execute();
            $paymentMethods = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $analytics = array_merge($totals, $currentMonth, [
                'growth_percentage' => round($growthPercentage, 1),
                'new_this_month' => $currentMonth['current_month_count'],
                'cash_amount' => 0,
                'bank_amount' => 0,
                'mobile_amount' => 0,
                'cheque_amount' => 0
            ]);

            // Process payment methods
            foreach ($paymentMethods as $method) {
                switch ($method['payment_method']) {
                    case 'cash':
                        $analytics['cash_amount'] = $method['amount'];
                        break;
                    case 'bank_transfer':
                        $analytics['bank_amount'] = $method['amount'];
                        break;
                    case 'mobile_money':
                        $analytics['mobile_amount'] = $method['amount'];
                        break;
                    case 'cheque':
                        $analytics['cheque_amount'] = $method['amount'];
                        break;
                }
            }

            return $analytics;

        } catch (Exception $e) {
            error_log("Error getting pastor application analytics: " . $e->getMessage());
            return [
                'total_count' => 0,
                'total_amount' => 0,
                'average_amount' => 0,
                'current_month_count' => 0,
                'current_month_amount' => 0,
                'growth_percentage' => 0,
                'new_this_month' => 0,
                'cash_amount' => 0,
                'bank_amount' => 0,
                'mobile_amount' => 0,
                'cheque_amount' => 0
            ];
        }
    }

    /**
     * Get Pastor Application Member Tracking Data
     */
    private function getPastorApplicationMemberTracking() {
        try {
            $conn = $this->database->getConnection();

            $query = "SELECT
                ft.member_id,
                m.first_name,
                m.last_name,
                COUNT(*) as payment_count,
                SUM(ft.amount) as total_amount,
                MAX(ft.transaction_date) as last_payment_date
                FROM finance_transactions ft
                LEFT JOIN members m ON ft.member_id = m.id
                WHERE ft.category = 'Pastor application'
                AND ft.transaction_type = 'income'
                GROUP BY ft.member_id, m.first_name, m.last_name
                ORDER BY total_amount DESC
                LIMIT 10";

            $stmt = $conn->prepare($query);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_OBJ);

        } catch (Exception $e) {
            error_log("Error getting pastor application member tracking: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get Pastor Application Trends Data
     */
    private function getPastorApplicationTrends() {
        try {
            $conn = $this->database->getConnection();

            $query = "SELECT
                DATE_FORMAT(transaction_date, '%Y-%m') as month,
                SUM(amount) as amount,
                COUNT(*) as count
                FROM finance_transactions
                WHERE category = 'Pastor application'
                AND transaction_type = 'income'
                AND transaction_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
                ORDER BY month ASC";

            $stmt = $conn->prepare($query);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error getting pastor application trends: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get Recent Pastor Applications
     */
    private function getRecentPastorApplications($limit = 10) {
        try {
            $conn = $this->database->getConnection();

            $query = "SELECT
                ft.*,
                CONCAT(m.first_name, ' ', m.last_name) as member_name
                FROM finance_transactions ft
                LEFT JOIN members m ON ft.member_id = m.id
                WHERE ft.category = 'Pastor application'
                AND ft.transaction_type = 'income'
                ORDER BY ft.transaction_date DESC, ft.created_at DESC
                LIMIT :limit";

            $stmt = $conn->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_OBJ);

        } catch (Exception $e) {
            error_log("Error getting recent pastor applications: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get Pastor Application Category Info
     */
    private function getPastorApplicationCategoryInfo() {
        try {
            $conn = $this->database->getConnection();

            $query = "SELECT * FROM custom_finance_categories
                      WHERE name = 'Pastor application'
                      OR label = 'Pastor application'
                      LIMIT 1";

            $stmt = $conn->prepare($query);
            $stmt->execute();

            return $stmt->fetch(PDO::FETCH_OBJ);

        } catch (Exception $e) {
            error_log("Error getting pastor application category info: " . $e->getMessage());
            return null;
        }
    }
}
