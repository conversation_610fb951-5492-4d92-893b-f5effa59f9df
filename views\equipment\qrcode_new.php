<div class="container mx-auto px-4 py-6 max-w-6xl">
    <!-- Header with Hero Section -->
    <div class="bg-gradient-to-r from-gray-50 to-white rounded-xl shadow-lg border border-gray-100 mb-8 overflow-hidden">
        <div class="p-6 md:p-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                <div class="flex items-center">
                    <div class="bg-gradient-to-r from-purple-600 to-purple-500 p-3 rounded-full shadow-lg mr-4">
                        <i class="fas fa-qrcode text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mb-1">QR Code</h1>
                        <p class="text-gray-600"><?php echo $this->equipment->name; ?> • ID #<?php echo $this->equipment->id; ?></p>
                    </div>
                </div>
                <div class="flex flex-wrap gap-2">
                    <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->equipment->id; ?>" class="bg-white border border-gray-300 hover:bg-gray-100 text-gray-700 py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-arrow-left mr-2 text-primary"></i> Back to Equipment
                    </a>
                    <button id="printButton" class="bg-gradient-to-r from-indigo-600 to-indigo-500 hover:from-indigo-700 hover:to-indigo-600 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-print mr-2"></i> Print QR Code
                    </button>
                    <button id="downloadButton" class="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-download mr-2"></i> Download QR Code
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- QR Code Card -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <!-- QR Code Section -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-8 transition-all duration-300 hover:shadow-xl flex flex-col items-center justify-center">
            <div class="relative mb-6 p-4 bg-white rounded-xl border-2 border-purple-100 shadow-lg">
                <div class="absolute -top-3 -left-3 w-6 h-6 bg-purple-600 rounded-full"></div>
                <div class="absolute -top-3 -right-3 w-6 h-6 bg-purple-600 rounded-full"></div>
                <div class="absolute -bottom-3 -left-3 w-6 h-6 bg-purple-600 rounded-full"></div>
                <div class="absolute -bottom-3 -right-3 w-6 h-6 bg-purple-600 rounded-full"></div>
                
                <!-- QR code image -->
                <img id="qrCodeImage" src="https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=<?php echo urlencode(BASE_URL . 'equipment/view?id=' . $this->equipment->id); ?>" 
                     alt="QR Code" class="mx-auto w-64 h-64">
                
                <!-- Fallback text in case image doesn't load -->
                <div class="hidden">Scan to view: <?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->equipment->id; ?></div>
            </div>

            <div class="text-center w-full">
                <div class="bg-purple-50 rounded-lg p-4 border border-purple-100 mb-4">
                    <p class="text-sm text-purple-700 mb-2">Scan this QR code to view equipment details</p>
                    <p class="text-xs font-mono bg-white p-2 rounded border border-purple-100 break-all"><?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->equipment->id; ?></p>
                </div>

                <div class="flex justify-center space-x-3 mb-4">
                    <button id="downloadButtonInline" class="bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-download mr-2"></i> Download
                    </button>
                    <button id="printButtonInline" class="bg-gradient-to-r from-indigo-600 to-indigo-500 hover:from-indigo-700 hover:to-indigo-600 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-print mr-2"></i> Print
                    </button>
                </div>
                
                <!-- Direct download link as fallback -->
                <div class="text-center text-sm text-gray-500 mb-4">
                    <p class="mb-2">If buttons don't work, use this direct link:</p>
                    <a href="https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=<?php echo urlencode(BASE_URL . 'equipment/view?id=' . $this->equipment->id); ?>" 
                       download="QR_Code_<?php echo str_replace(' ', '_', $this->equipment->name); ?>.png"
                       class="text-primary hover:text-primary-dark underline">
                        Direct Download Link
                    </a>
                </div>
            </div>
        </div>

        <!-- Equipment Details Section -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-xl">
            <div class="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-100">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                    <div class="bg-blue-100 text-blue-600 p-2 rounded-full mr-3">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    Equipment Information
                </h2>
            </div>
            
            <div class="p-6">
                <div class="flex items-center mb-6 pb-4 border-b border-gray-100">
                    <div class="bg-gray-100 p-3 rounded-full mr-4">
                        <i class="fas fa-desktop text-gray-500 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-800"><?php echo $this->equipment->name; ?></h3>
                        <p class="text-gray-600">ID: <?php echo $this->equipment->id; ?></p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                        <div class="flex items-center mb-1 text-gray-500">
                            <i class="fas fa-th-large mr-2"></i>
                            <p class="text-sm">Category</p>
                        </div>
                        <p class="font-medium text-gray-800"><?php echo ucfirst($this->equipment->category); ?></p>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                        <div class="flex items-center mb-1 text-gray-500">
                            <i class="fas fa-heartbeat mr-2"></i>
                            <p class="text-sm">Condition</p>
                        </div>
                        <p>
                            <?php if ($condition === 'excellent') : ?>
                                <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full bg-green-100 text-green-800 border border-green-200">
                                    <i class="fas fa-check-circle mr-1"></i> Excellent
                                </span>
                            <?php elseif ($condition === 'good') : ?>
                                <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                    <i class="fas fa-thumbs-up mr-1"></i> Good
                                </span>
                            <?php elseif ($condition === 'fair') : ?>
                                <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200">
                                    <i class="fas fa-exclamation-circle mr-1"></i> Fair
                                </span>
                            <?php else : ?>
                                <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full bg-red-100 text-red-800 border border-red-200">
                                    <i class="fas fa-exclamation-triangle mr-1"></i> Poor
                                </span>
                            <?php endif; ?>
                        </p>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                        <div class="flex items-center mb-1 text-gray-500">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <p class="text-sm">Location</p>
                        </div>
                        <p class="font-medium text-gray-800"><?php echo $this->equipment->location ?: 'N/A'; ?></p>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                        <div class="flex items-center mb-1 text-gray-500">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            <p class="text-sm">Purchase Date</p>
                        </div>
                        <p class="font-medium text-gray-800"><?php echo $this->equipment->purchase_date ? format_date($this->equipment->purchase_date) : 'N/A'; ?></p>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->equipment->id; ?>" class="text-primary hover:text-primary-dark flex items-center">
                        <i class="fas fa-external-link-alt mr-2"></i> View Full Equipment Details
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-xl mb-8">
        <div class="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-100">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <div class="bg-green-100 text-green-600 p-2 rounded-full mr-3">
                    <i class="fas fa-question-circle"></i>
                </div>
                How to Use This QR Code
            </h2>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-50 rounded-lg p-5 border border-gray-100 relative overflow-hidden">
                    <div class="absolute top-0 right-0 w-16 h-16 bg-primary opacity-10 rounded-full transform translate-x-4 -translate-y-4"></div>
                    <div class="flex items-start relative z-10">
                        <div class="flex-shrink-0 h-8 w-8 flex items-center justify-center rounded-full bg-gradient-to-r from-primary to-primary-light text-white font-bold text-sm mr-3 shadow-sm">
                            1
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-1">Print & Attach</h3>
                            <p class="text-gray-600">Print this QR code and attach it securely to the equipment using tape or a label.</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-5 border border-gray-100 relative overflow-hidden">
                    <div class="absolute top-0 right-0 w-16 h-16 bg-primary opacity-10 rounded-full transform translate-x-4 -translate-y-4"></div>
                    <div class="flex items-start relative z-10">
                        <div class="flex-shrink-0 h-8 w-8 flex items-center justify-center rounded-full bg-gradient-to-r from-primary to-primary-light text-white font-bold text-sm mr-3 shadow-sm">
                            2
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-1">Scan with Phone</h3>
                            <p class="text-gray-600">Use any QR code scanner app on your smartphone to scan the code when needed.</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-5 border border-gray-100 relative overflow-hidden">
                    <div class="absolute top-0 right-0 w-16 h-16 bg-primary opacity-10 rounded-full transform translate-x-4 -translate-y-4"></div>
                    <div class="flex items-start relative z-10">
                        <div class="flex-shrink-0 h-8 w-8 flex items-center justify-center rounded-full bg-gradient-to-r from-primary to-primary-light text-white font-bold text-sm mr-3 shadow-sm">
                            3
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-1">Access Details</h3>
                            <p class="text-gray-600">The scanner will open the equipment details page in your browser automatically.</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-5 border border-gray-100 relative overflow-hidden">
                    <div class="absolute top-0 right-0 w-16 h-16 bg-primary opacity-10 rounded-full transform translate-x-4 -translate-y-4"></div>
                    <div class="flex items-start relative z-10">
                        <div class="flex-shrink-0 h-8 w-8 flex items-center justify-center rounded-full bg-gradient-to-r from-primary to-primary-light text-white font-bold text-sm mr-3 shadow-sm">
                            4
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-1">Manage Equipment</h3>
                            <p class="text-gray-600">View details, maintenance history, and add new maintenance records as needed.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 pt-6 border-t border-gray-100 text-center">
                <p class="text-gray-600 mb-2">Need help with equipment management?</p>
                <p class="text-sm text-gray-500">Contact the ICGC Emmanuel Temple administration office for assistance.</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation for page elements
    const elements = document.querySelectorAll('.bg-white.rounded-xl');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 100 + (index * 150));
    });
    
    // Print functionality
    function printQRCode() {
        const equipmentName = "<?php echo $this->equipment->name; ?>";
        const equipmentId = "<?php echo $this->equipment->id; ?>";
        const equipmentUrl = "<?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->equipment->id; ?>";
        const qrImageUrl = "https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=" + encodeURIComponent(equipmentUrl);
        
        // Open a new window for printing
        const printWindow = window.open('', '_blank');
        
        if (!printWindow) {
            alert('Please allow pop-ups to print the QR code');
            return;
        }
        
        // Create print layout
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Print QR Code - ${equipmentName}</title>
                <meta charset="UTF-8">
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        padding: 30px;
                        margin: 0;
                    }
                    .container {
                        max-width: 500px;
                        margin: 0 auto;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                        border-radius: 12px;
                        padding: 20px;
                        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
                    }
                    .header {
                        margin-bottom: 20px;
                        font-size: 18px;
                        font-weight: bold;
                        color: #374151;
                    }
                    .qr-container {
                        position: relative;
                        margin: 0 auto 20px;
                        width: 280px;
                        height: 280px;
                        padding: 10px;
                        border: 2px solid #f3e8ff;
                        border-radius: 8px;
                    }
                    .corner {
                        position: absolute;
                        width: 12px;
                        height: 12px;
                        background-color: #9333ea;
                        border-radius: 50%;
                    }
                    .top-left { top: -6px; left: -6px; }
                    .top-right { top: -6px; right: -6px; }
                    .bottom-left { bottom: -6px; left: -6px; }
                    .bottom-right { bottom: -6px; right: -6px; }
                    .qr-image {
                        width: 100%;
                        height: 100%;
                    }
                    .equipment-name {
                        font-size: 20px;
                        font-weight: bold;
                        margin-bottom: 5px;
                        color: #1f2937;
                    }
                    .equipment-id {
                        color: #6b7280;
                        margin-bottom: 15px;
                    }
                    .footer {
                        margin-top: 20px;
                        padding-top: 15px;
                        border-top: 1px solid #e5e7eb;
                        font-size: 14px;
                        color: #6b7280;
                    }
                    .url {
                        font-size: 12px;
                        word-break: break-all;
                        color: #9ca3af;
                    }
                    .timestamp {
                        text-align: center;
                        margin-top: 20px;
                        font-size: 12px;
                        color: #9ca3af;
                    }
                    @media print {
                        body {
                            padding: 0;
                        }
                        .container {
                            border: none;
                            box-shadow: none;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">ICGC Emmanuel Temple</div>
                    <div class="qr-container">
                        <div class="corner top-left"></div>
                        <div class="corner top-right"></div>
                        <div class="corner bottom-left"></div>
                        <div class="corner bottom-right"></div>
                        <img src="${qrImageUrl}" alt="QR Code" class="qr-image" onload="window.setTimeout(function() { window.print(); window.close(); }, 500);">
                    </div>
                    <h2 class="equipment-name">${equipmentName}</h2>
                    <p class="equipment-id">ID: ${equipmentId}</p>
                    <div class="footer">
                        <p>Scan this QR code to view equipment details</p>
                        <p class="url">${equipmentUrl}</p>
                    </div>
                </div>
                <div class="timestamp">Printed on ${new Date().toLocaleDateString()}</div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
    }
    
    // Download functionality
    function downloadQRCode() {
        const equipmentName = "<?php echo $this->equipment->name; ?>";
        const equipmentUrl = "<?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->equipment->id; ?>";
        const qrImageUrl = "https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=" + encodeURIComponent(equipmentUrl);
        
        // Create a direct download link
        const link = document.createElement('a');
        link.href = qrImageUrl;
        link.download = `QR_Code_${equipmentName.replace(/\s+/g, '_')}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    // Attach event listeners to buttons
    document.getElementById('printButton').addEventListener('click', printQRCode);
    document.getElementById('printButtonInline').addEventListener('click', printQRCode);
    document.getElementById('downloadButton').addEventListener('click', downloadQRCode);
    document.getElementById('downloadButtonInline').addEventListener('click', downloadQRCode);
});
</script>
