<!-- QR Analytics Report Selection Page -->
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-lg shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">QR Analytics Reports</h1>
                <p class="text-green-100">Generate comprehensive attendance reports with detailed analytics</p>
            </div>
            <div class="text-right">
                <div class="bg-white bg-opacity-20 rounded-lg p-3">
                    <i class="fas fa-chart-line text-3xl mb-2"></i>
                    <div class="text-sm font-medium">Professional Reports</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="<?php echo BASE_URL; ?>dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <a href="<?php echo BASE_URL; ?>attendance" class="text-sm font-medium text-gray-700 hover:text-blue-600">Attendance</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <a href="<?php echo BASE_URL; ?>attendance/qr-analytics" class="text-sm font-medium text-gray-700 hover:text-blue-600">QR Analytics</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="text-sm font-medium text-gray-500">Reports</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Report Selection Form -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <form id="reportForm" method="GET" action="<?php echo BASE_URL; ?>attendance/qr-analytics-report">
            <input type="hidden" name="generate" value="true">
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Column: Report Configuration -->
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-cog text-green-600 mr-2"></i>
                            Report Configuration
                        </h3>
                        
                        <!-- Report Type -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                            <select name="format" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                <option value="detailed">Detailed Report</option>
                                <option value="summary">Summary Report</option>
                                <option value="excel">Excel Export</option>
                                <option value="pdf">PDF Report</option>
                            </select>
                        </div>

                        <!-- Period Type -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Period Type</label>
                            <div class="grid grid-cols-2 gap-2">
                                <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="period" value="weekly" class="mr-2" onchange="updatePeriodOptions()">
                                    <span class="text-sm">Weekly</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="period" value="monthly" class="mr-2" checked onchange="updatePeriodOptions()">
                                    <span class="text-sm">Monthly</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="period" value="quarterly" class="mr-2" onchange="updatePeriodOptions()">
                                    <span class="text-sm">Quarterly</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="period" value="yearly" class="mr-2" onchange="updatePeriodOptions()">
                                    <span class="text-sm">Yearly</span>
                                </label>
                            </div>
                        </div>

                        <!-- Period Selection -->
                        <div id="periodSelection" class="mb-4">
                            <!-- Monthly Selection (Default) -->
                            <div id="monthlySelection">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Select Month</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <select name="year" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <?php foreach ($available_years as $year): ?>
                                            <option value="<?php echo $year; ?>" <?php echo $year == date('Y') ? 'selected' : ''; ?>>
                                                <?php echo $year; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <select name="month" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <?php for ($i = 1; $i <= 12; $i++): ?>
                                            <option value="<?php echo sprintf('%02d', $i); ?>" <?php echo $i == date('n') ? 'selected' : ''; ?>>
                                                <?php echo date('F', mktime(0, 0, 0, $i, 1)); ?>
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Custom Date Range -->
                        <div class="mb-4">
                            <label class="flex items-center mb-2">
                                <input type="checkbox" id="customRange" class="mr-2" onchange="toggleCustomRange()">
                                <span class="text-sm font-medium text-gray-700">Custom Date Range</span>
                            </label>
                            <div id="customRangeInputs" class="hidden grid grid-cols-2 gap-2">
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">Start Date</label>
                                    <input type="date" name="start_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">End Date</label>
                                    <input type="date" name="end_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Report Preview -->
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-eye text-green-600 mr-2"></i>
                            Report Preview
                        </h3>
                        
                        <div class="bg-gray-50 rounded-lg p-4 border-2 border-dashed border-gray-300">
                            <div class="text-center">
                                <i class="fas fa-file-alt text-4xl text-gray-400 mb-3"></i>
                                <h4 class="font-medium text-gray-900 mb-2">Professional QR Analytics Report</h4>
                                <p class="text-sm text-gray-600 mb-4">Your report will include:</p>
                                
                                <div class="text-left space-y-2">
                                    <div class="flex items-center text-sm text-gray-700">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Overview Statistics & KPIs
                                    </div>
                                    <div class="flex items-center text-sm text-gray-700">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Member Engagement Analysis
                                    </div>
                                    <div class="flex items-center text-sm text-gray-700">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Gender & Department Distribution
                                    </div>
                                    <div class="flex items-center text-sm text-gray-700">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Attendance Trends & Patterns
                                    </div>
                                    <div class="flex items-center text-sm text-gray-700">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Service Performance Metrics
                                    </div>
                                    <div class="flex items-center text-sm text-gray-700">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Quarterly Comparisons
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-8 flex justify-between items-center">
                <a href="<?php echo BASE_URL; ?>attendance/qr-analytics" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Analytics
                </a>
                
                <div class="space-x-3">
                    <button type="button" onclick="previewReport()" class="inline-flex items-center px-4 py-2 border border-green-600 rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-green-50">
                        <i class="fas fa-eye mr-2"></i>
                        Preview Report
                    </button>
                    <button type="submit" class="inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                        <i class="fas fa-chart-line mr-2"></i>
                        Generate Report
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Quick Report Templates -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-bolt text-yellow-500 mr-2"></i>
            Quick Report Templates
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="<?php echo BASE_URL; ?>attendance/qr-analytics-report?generate=true&period=monthly&value=<?php echo date('Y-m'); ?>&format=detailed" 
               class="block p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:shadow-md transition-all">
                <div class="text-center">
                    <i class="fas fa-calendar-alt text-2xl text-blue-500 mb-2"></i>
                    <h4 class="font-medium text-gray-900">Current Month</h4>
                    <p class="text-sm text-gray-600">Detailed monthly report</p>
                </div>
            </a>
            
            <a href="<?php echo BASE_URL; ?>attendance/qr-analytics-report?generate=true&period=quarterly&value=<?php echo date('Y'); ?>-Q<?php echo ceil(date('n')/3); ?>&format=detailed" 
               class="block p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:shadow-md transition-all">
                <div class="text-center">
                    <i class="fas fa-chart-bar text-2xl text-green-500 mb-2"></i>
                    <h4 class="font-medium text-gray-900">Current Quarter</h4>
                    <p class="text-sm text-gray-600">Quarterly performance</p>
                </div>
            </a>
            
            <a href="<?php echo BASE_URL; ?>attendance/qr-analytics-report?generate=true&period=yearly&value=<?php echo date('Y'); ?>&format=detailed" 
               class="block p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:shadow-md transition-all">
                <div class="text-center">
                    <i class="fas fa-chart-line text-2xl text-purple-500 mb-2"></i>
                    <h4 class="font-medium text-gray-900">Current Year</h4>
                    <p class="text-sm text-gray-600">Annual summary</p>
                </div>
            </a>
            
            <a href="<?php echo BASE_URL; ?>attendance/qr-analytics-report?generate=true&period=monthly&value=<?php echo date('Y-m', strtotime('-1 month')); ?>&format=summary" 
               class="block p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:shadow-md transition-all">
                <div class="text-center">
                    <i class="fas fa-file-alt text-2xl text-orange-500 mb-2"></i>
                    <h4 class="font-medium text-gray-900">Last Month</h4>
                    <p class="text-sm text-gray-600">Summary report</p>
                </div>
            </a>
        </div>
    </div>
</div>

<script>
function updatePeriodOptions() {
    const periodType = document.querySelector('input[name="period"]:checked').value;
    const periodSelection = document.getElementById('periodSelection');
    
    // Clear existing content
    periodSelection.innerHTML = '';
    
    if (periodType === 'weekly') {
        periodSelection.innerHTML = `
            <label class="block text-sm font-medium text-gray-700 mb-2">Select Week</label>
            <div class="grid grid-cols-2 gap-2">
                <select name="year" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    <?php foreach ($available_years as $year): ?>
                        <option value="<?php echo $year; ?>" <?php echo $year == date('Y') ? 'selected' : ''; ?>><?php echo $year; ?></option>
                    <?php endforeach; ?>
                </select>
                <select name="week" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    ${Array.from({length: 52}, (_, i) => `<option value="W${String(i+1).padStart(2, '0')}" ${i+1 == getCurrentWeek() ? 'selected' : ''}>Week ${i+1}</option>`).join('')}
                </select>
            </div>
        `;
    } else if (periodType === 'quarterly') {
        periodSelection.innerHTML = `
            <label class="block text-sm font-medium text-gray-700 mb-2">Select Quarter</label>
            <div class="grid grid-cols-2 gap-2">
                <select name="year" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    <?php foreach ($available_years as $year): ?>
                        <option value="<?php echo $year; ?>" <?php echo $year == date('Y') ? 'selected' : ''; ?>><?php echo $year; ?></option>
                    <?php endforeach; ?>
                </select>
                <select name="quarter" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    <option value="Q1" ${getCurrentQuarter() == 1 ? 'selected' : ''}>Q1 (Jan-Mar)</option>
                    <option value="Q2" ${getCurrentQuarter() == 2 ? 'selected' : ''}>Q2 (Apr-Jun)</option>
                    <option value="Q3" ${getCurrentQuarter() == 3 ? 'selected' : ''}>Q3 (Jul-Sep)</option>
                    <option value="Q4" ${getCurrentQuarter() == 4 ? 'selected' : ''}>Q4 (Oct-Dec)</option>
                </select>
            </div>
        `;
    } else if (periodType === 'yearly') {
        periodSelection.innerHTML = `
            <label class="block text-sm font-medium text-gray-700 mb-2">Select Year</label>
            <select name="year" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                <?php foreach ($available_years as $year): ?>
                    <option value="<?php echo $year; ?>" <?php echo $year == date('Y') ? 'selected' : ''; ?>><?php echo $year; ?></option>
                <?php endforeach; ?>
            </select>
        `;
    } else {
        // Monthly (default)
        periodSelection.innerHTML = document.getElementById('monthlySelection').innerHTML;
    }
}

function toggleCustomRange() {
    const checkbox = document.getElementById('customRange');
    const inputs = document.getElementById('customRangeInputs');
    
    if (checkbox.checked) {
        inputs.classList.remove('hidden');
        // Disable period selection
        document.querySelectorAll('input[name="period"]').forEach(input => input.disabled = true);
    } else {
        inputs.classList.add('hidden');
        // Enable period selection
        document.querySelectorAll('input[name="period"]').forEach(input => input.disabled = false);
    }
}

function getCurrentWeek() {
    const now = new Date();
    const start = new Date(now.getFullYear(), 0, 1);
    const diff = now - start;
    const oneWeek = 1000 * 60 * 60 * 24 * 7;
    return Math.ceil(diff / oneWeek);
}

function getCurrentQuarter() {
    return Math.ceil(new Date().getMonth() / 3);
}

function previewReport() {
    const form = document.getElementById('reportForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    // Open preview in new tab
    window.open(form.action + '?' + params.toString(), '_blank');
}

// Update form submission to include period value
document.getElementById('reportForm').addEventListener('submit', function(e) {
    const periodType = document.querySelector('input[name="period"]:checked').value;
    const customRange = document.getElementById('customRange').checked;
    
    if (!customRange) {
        let periodValue = '';
        
        if (periodType === 'weekly') {
            const year = document.querySelector('select[name="year"]').value;
            const week = document.querySelector('select[name="week"]').value;
            periodValue = year + '-' + week;
        } else if (periodType === 'monthly') {
            const year = document.querySelector('select[name="year"]').value;
            const month = document.querySelector('select[name="month"]').value;
            periodValue = year + '-' + month;
        } else if (periodType === 'quarterly') {
            const year = document.querySelector('select[name="year"]').value;
            const quarter = document.querySelector('select[name="quarter"]').value;
            periodValue = year + '-' + quarter;
        } else if (periodType === 'yearly') {
            periodValue = document.querySelector('select[name="year"]').value;
        }
        
        // Add hidden input for period value
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'value';
        hiddenInput.value = periodValue;
        this.appendChild(hiddenInput);
    }
});
</script>
