<?php
/**
 * Parent-Child Relationship Management Tool
 * 
 * Admin interface for managing and monitoring automatic parent-child relationships
 */

require_once '../config/database.php';
require_once '../utils/AutoParentChildMapper.php';

// Check if user is admin (add your authentication logic here)
session_start();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parent-Child Relationship Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-users text-blue-600 mr-3"></i>
                Parent-Child Relationship Management
            </h1>
            
            <!-- Status Overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <?php
                try {
                    $database = new Database();
                    $conn = $database->getConnection();
                    
                    // Get statistics
                    $stats_query = "SELECT 
                                      (SELECT COUNT(*) FROM family_relationships WHERE relationship_type = 'parent') as total_relationships,
                                      (SELECT COUNT(DISTINCT parent_id) FROM family_relationships WHERE relationship_type = 'parent') as total_parents,
                                      (SELECT COUNT(DISTINCT child_id) FROM family_relationships WHERE relationship_type = 'parent') as total_children";
                    
                    $stmt = $conn->prepare($stats_query);
                    $stmt->execute();
                    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
                ?>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-link text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">Total Relationships</h3>
                            <p class="text-2xl font-bold text-blue-600"><?php echo $stats['total_relationships']; ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-user-friends text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">Parents</h3>
                            <p class="text-2xl font-bold text-green-600"><?php echo $stats['total_parents']; ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-child text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">Children</h3>
                            <p class="text-2xl font-bold text-purple-600"><?php echo $stats['total_children']; ?></p>
                        </div>
                    </div>
                </div>
                
                <?php
                } catch (Exception $e) {
                    echo "<div class='col-span-3 bg-red-50 border border-red-200 rounded-lg p-4'>";
                    echo "<p class='text-red-600'>Error loading statistics: " . $e->getMessage() . "</p>";
                    echo "</div>";
                }
                ?>
            </div>
            
            <!-- Action Buttons -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <button onclick="runFullScan()" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                    <i class="fas fa-search mr-2"></i>
                    Run Full System Scan
                </button>
                
                <button onclick="showSystemStatus()" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                    <i class="fas fa-cog mr-2"></i>
                    Check System Status
                </button>
            </div>
            
            <!-- Results Area -->
            <div id="results" class="hidden">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Results</h2>
                <div id="results-content" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <!-- Results will be loaded here -->
                </div>
            </div>
            
            <!-- Recent Relationships -->
            <div class="mt-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Recent Relationships</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Child</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php
                            try {
                                $recent_query = "SELECT fr.*,
                                               p.first_name as parent_first_name, p.last_name as parent_last_name,
                                               c.first_name as child_first_name, c.last_name as child_last_name
                                               FROM family_relationships fr
                                               JOIN members p ON fr.parent_id = p.id
                                               JOIN members c ON fr.child_id = c.id
                                               WHERE fr.relationship_type = 'parent'
                                               ORDER BY fr.created_at DESC
                                               LIMIT 10";
                                
                                $stmt = $conn->prepare($recent_query);
                                $stmt->execute();
                                $recent_relationships = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                
                                if (empty($recent_relationships)) {
                                    echo "<tr><td colspan='4' class='px-6 py-4 text-center text-gray-500'>No relationships found</td></tr>";
                                } else {
                                    foreach ($recent_relationships as $rel) {
                                        echo "<tr>";
                                        echo "<td class='px-6 py-4 whitespace-nowrap'>";
                                        echo "<div class='text-sm font-medium text-gray-900'>{$rel['parent_first_name']} {$rel['parent_last_name']}</div>";
                                        echo "<div class='text-sm text-gray-500'>ID: {$rel['parent_id']}</div>";
                                        echo "</td>";
                                        echo "<td class='px-6 py-4 whitespace-nowrap'>";
                                        echo "<div class='text-sm font-medium text-gray-900'>{$rel['child_first_name']} {$rel['child_last_name']}</div>";
                                        echo "<div class='text-sm text-gray-500'>ID: {$rel['child_id']}</div>";
                                        echo "</td>";
                                        echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>";
                                        echo date('M j, Y g:i A', strtotime($rel['created_at']));
                                        echo "</td>";
                                        echo "<td class='px-6 py-4 text-sm text-gray-500'>";
                                        echo htmlspecialchars($rel['notes'] ?? '');
                                        echo "</td>";
                                        echo "</tr>";
                                    }
                                }
                            } catch (Exception $e) {
                                echo "<tr><td colspan='4' class='px-6 py-4 text-center text-red-500'>Error loading relationships: " . $e->getMessage() . "</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function runFullScan() {
            const resultsDiv = document.getElementById('results');
            const resultsContent = document.getElementById('results-content');
            
            resultsDiv.classList.remove('hidden');
            resultsContent.innerHTML = '<div class="flex items-center"><i class="fas fa-spinner fa-spin mr-2"></i>Running full system scan...</div>';
            
            fetch('run_relationship_scan.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                let html = '<div class="space-y-4">';
                html += `<div class="bg-blue-50 border border-blue-200 rounded p-3">`;
                html += `<h3 class="font-semibold text-blue-800">Scan Results</h3>`;
                html += `<p>Total checked: ${data.total_checked}</p>`;
                html += `<p>New relationships created: ${data.relationships_created}</p>`;
                html += `<p>Existing relationships found: ${data.relationships_found}</p>`;
                html += `</div>`;
                
                if (data.details && data.details.length > 0) {
                    html += `<div class="bg-green-50 border border-green-200 rounded p-3">`;
                    html += `<h3 class="font-semibold text-green-800">New Relationships Created</h3>`;
                    data.details.forEach(detail => {
                        html += `<p>• ${detail.child_name} linked to parent(s)</p>`;
                    });
                    html += `</div>`;
                }
                
                if (data.errors && data.errors.length > 0) {
                    html += `<div class="bg-red-50 border border-red-200 rounded p-3">`;
                    html += `<h3 class="font-semibold text-red-800">Errors</h3>`;
                    data.errors.forEach(error => {
                        html += `<p>• ${error}</p>`;
                    });
                    html += `</div>`;
                }
                
                html += '</div>';
                resultsContent.innerHTML = html;
            })
            .catch(error => {
                resultsContent.innerHTML = `<div class="text-red-600">Error: ${error.message}</div>`;
            });
        }
        
        function showSystemStatus() {
            const resultsDiv = document.getElementById('results');
            const resultsContent = document.getElementById('results-content');
            
            resultsDiv.classList.remove('hidden');
            resultsContent.innerHTML = '<div class="flex items-center"><i class="fas fa-spinner fa-spin mr-2"></i>Checking system status...</div>';
            
            fetch('check_system_status.php')
            .then(response => response.json())
            .then(data => {
                let html = '<div class="space-y-4">';
                html += `<div class="bg-blue-50 border border-blue-200 rounded p-3">`;
                html += `<h3 class="font-semibold text-blue-800">System Status</h3>`;
                html += `<p>Auto-mapping enabled: ${data.auto_mapping_enabled ? 'Yes' : 'No'}</p>`;
                html += `<p>Database triggers: ${data.triggers_count} active</p>`;
                html += `<p>Max child age: ${data.max_child_age} years</p>`;
                html += `</div>`;
                html += '</div>';
                resultsContent.innerHTML = html;
            })
            .catch(error => {
                resultsContent.innerHTML = `<div class="text-red-600">Error: ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
