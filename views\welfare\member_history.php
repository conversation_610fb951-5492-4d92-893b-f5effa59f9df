<?php
$page_title = "Member Welfare History - " . $member['first_name'] . ' ' . $member['last_name'];
$active_page = "welfare";
?>

<div class="max-w-7xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center mb-6">
        <a href="<?php echo BASE_URL; ?>welfare/history" class="text-gray-600 hover:text-gray-800 mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div class="flex-1">
            <h1 class="text-3xl font-bold text-gray-900">
                <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
            </h1>
            <p class="text-gray-600 mt-1">Welfare assistance history</p>
        </div>
        <a href="<?php echo BASE_URL; ?>welfare/add" 
           class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-plus mr-2"></i>
            Add Payment
        </a>
    </div>

    <!-- Member Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-emerald-100">
                    <i class="fas fa-coins text-emerald-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Paid</p>
                    <p class="text-2xl font-bold text-emerald-700">₵<?php echo number_format($member_stats['total_paid'], 2); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100">
                    <i class="fas fa-hand-holding-heart text-orange-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Claimed</p>
                    <p class="text-2xl font-bold text-orange-700">₵<?php echo number_format($member_stats['total_claimed'], 2); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="fas fa-list text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Transactions</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo $member_stats['payment_count'] + $member_stats['claim_count']; ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <i class="fas fa-calendar text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Last Activity</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <?php echo $member_stats['last_activity'] ? date('M d, Y', strtotime($member_stats['last_activity'])) : 'N/A'; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                    <select id="typeFilter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">All Transactions</option>
                        <option value="payment">Payments Only</option>
                        <option value="claim">Claims Only</option>
                    </select>
                    <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">All Status</option>
                        <option value="disbursed">Disbursed</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                    </select>
                </div>
                <div class="text-sm text-gray-600">
                    Total: <span class="font-medium"><?php echo count($payments); ?></span> transactions
                </div>
            </div>
        </div>
    </div>

    <!-- Welfare History -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Welfare History</h2>
            <p class="text-sm text-gray-600 mt-1">Monthly dues payments and welfare assistance claims</p>
        </div>

        <div class="overflow-x-auto">
            <?php if (empty($payments)): ?>
                <div class="text-center py-12 px-6">
                    <i class="fas fa-hand-holding-heart text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Welfare History Found</h3>
                    <p class="text-gray-500 mb-6">This member has no welfare history yet.</p>
                    <div class="flex justify-center space-x-3">
                        <a href="<?php echo BASE_URL; ?>welfare/add"
                           class="bg-emerald-500 hover:bg-emerald-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-coins mr-2"></i>
                            Record Dues Payment
                        </a>
                        <a href="<?php echo BASE_URL; ?>welfare/claim"
                           class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-hand-holding-heart mr-2"></i>
                            Record Claim
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($payments as $index => $transaction): ?>
                            <tr class="hover:bg-gray-50 transition-colors duration-150" data-type="<?php echo $transaction['transaction_type']; ?>" data-status="<?php echo $transaction['status']; ?>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full flex items-center justify-center mr-3
                                            <?php echo $transaction['transaction_type'] === 'payment' ? 'bg-emerald-100' : 'bg-orange-100'; ?>">
                                            <i class="<?php echo $transaction['transaction_type'] === 'payment' ? 'fas fa-coins text-emerald-600 text-sm' : 'fas fa-hand-holding-heart text-orange-600 text-sm'; ?>"></i>
                                        </div>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            <?php echo $transaction['transaction_type'] === 'payment' ? 'bg-emerald-100 text-emerald-800' : 'bg-orange-100 text-orange-800'; ?>">
                                            <?php echo ucfirst($transaction['transaction_type']); ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo date('M d, Y', strtotime($transaction['transaction_date'])); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($transaction['created_at'])); ?></div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($transaction['category_name']); ?></div>
                                    <?php if ($transaction['is_monthly_welfare']): ?>
                                        <div class="text-xs text-emerald-600 font-medium">Monthly Dues</div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-bold <?php echo $transaction['transaction_type'] === 'payment' ? 'text-emerald-700' : 'text-orange-700'; ?>">
                                        <?php echo $transaction['transaction_type'] === 'payment' ? '+' : '-'; ?>₵<?php echo number_format($transaction['amount'], 2); ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php
                                        switch($transaction['status']) {
                                            case 'disbursed': echo 'bg-green-100 text-green-800'; break;
                                            case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                            case 'approved': echo 'bg-blue-100 text-blue-800'; break;
                                            default: echo 'bg-gray-100 text-gray-800';
                                        }
                                        ?>">
                                        <?php echo ucfirst($transaction['status']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <button onclick="toggleDetails(<?php echo $index; ?>)" class="text-emerald-600 hover:text-emerald-900 font-medium">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>
                                </td>
                            </tr>
                            <!-- Expandable Details Row -->
                            <tr id="details-<?php echo $index; ?>" class="hidden bg-gray-50">
                                <td colspan="6" class="px-6 py-4">
                                    <div class="bg-white rounded-lg p-4 border border-gray-200">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <div>
                                                <h4 class="font-medium text-gray-900 mb-2">
                                                    <?php echo $transaction['transaction_type'] === 'payment' ? 'Payment Details' : 'Reason for Assistance'; ?>
                                                </h4>
                                                <p class="text-gray-700 text-sm"><?php echo htmlspecialchars($transaction['reason']); ?></p>
                                            </div>
                                            <div class="space-y-2 text-sm">
                                                <?php if ($transaction['transaction_type'] === 'payment' && !empty($transaction['payment_method'])): ?>
                                                <div>
                                                    <span class="font-medium text-gray-600">Payment Method:</span>
                                                    <span class="ml-2 text-gray-900"><?php echo ucfirst(str_replace('_', ' ', $transaction['payment_method'])); ?></span>
                                                </div>
                                                <?php endif; ?>
                                                <?php if (!empty($transaction['reference_number'])): ?>
                                                <div>
                                                    <span class="font-medium text-gray-600">Reference:</span>
                                                    <span class="ml-2 text-gray-900"><?php echo htmlspecialchars($transaction['reference_number']); ?></span>
                                                </div>
                                                <?php endif; ?>
                                                <?php if (!empty($transaction['approved_by_name'])): ?>
                                                <div>
                                                    <span class="font-medium text-gray-600">
                                                        <?php echo $transaction['transaction_type'] === 'payment' ? 'Recorded By:' : 'Approved By:'; ?>
                                                    </span>
                                                    <span class="ml-2 text-gray-900"><?php echo htmlspecialchars($transaction['approved_by_name']); ?></span>
                                                </div>
                                                <?php endif; ?>
                                                <?php if ($transaction['is_monthly_welfare']): ?>
                                                <div>
                                                    <span class="font-medium text-gray-600">Month/Year:</span>
                                                    <span class="ml-2 text-gray-900"><?php echo date('F Y', mktime(0, 0, 0, $transaction['payment_month'], 1, $transaction['payment_year'])); ?></span>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php if (!empty($transaction['notes'])): ?>
                                        <div class="pt-4 border-t border-gray-200">
                                            <h4 class="font-medium text-gray-600 mb-2">Notes:</h4>
                                            <p class="text-gray-700 text-sm"><?php echo htmlspecialchars($transaction['notes']); ?></p>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Toggle expandable details
function toggleDetails(index) {
    const detailsRow = document.getElementById('details-' + index);
    const button = event.target.closest('button');
    const icon = button.querySelector('i');

    if (detailsRow.classList.contains('hidden')) {
        detailsRow.classList.remove('hidden');
        icon.className = 'fas fa-eye-slash mr-1';
        button.innerHTML = '<i class="fas fa-eye-slash mr-1"></i>Hide';
    } else {
        detailsRow.classList.add('hidden');
        icon.className = 'fas fa-eye mr-1';
        button.innerHTML = '<i class="fas fa-eye mr-1"></i>View';
    }
}

// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const typeFilter = document.getElementById('typeFilter');
    const statusFilter = document.getElementById('statusFilter');
    const tableRows = document.querySelectorAll('tbody tr[data-type]');

    function applyFilters() {
        const selectedType = typeFilter.value;
        const selectedStatus = statusFilter.value;
        let visibleCount = 0;

        tableRows.forEach(function(row) {
            const rowType = row.getAttribute('data-type');
            const rowStatus = row.getAttribute('data-status');
            const detailsRow = row.nextElementSibling;

            let showRow = true;

            if (selectedType && rowType !== selectedType) {
                showRow = false;
            }

            if (selectedStatus && rowStatus !== selectedStatus) {
                showRow = false;
            }

            if (showRow) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
                // Hide details row if main row is hidden
                if (detailsRow && detailsRow.id.startsWith('details-')) {
                    detailsRow.classList.add('hidden');
                }
            }
        });

        // Update count display
        const countDisplay = document.querySelector('.text-sm.text-gray-600 span');
        if (countDisplay) {
            countDisplay.textContent = visibleCount;
        }
    }

    typeFilter.addEventListener('change', applyFilters);
    statusFilter.addEventListener('change', applyFilters);
});

// Add keyboard navigation for accessibility
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        // Close all expanded details
        document.querySelectorAll('[id^="details-"]:not(.hidden)').forEach(function(row) {
            row.classList.add('hidden');
        });
        // Reset all buttons
        document.querySelectorAll('button[onclick^="toggleDetails"]').forEach(function(button) {
            button.innerHTML = '<i class="fas fa-eye mr-1"></i>View';
        });
    }
});
</script>


