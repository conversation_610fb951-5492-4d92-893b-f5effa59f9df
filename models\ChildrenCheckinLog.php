<?php
/**
 * Children Check-in Log Model
 * Manages QR-based check-in for children's services (check-out functionality removed)
 */

class ChildrenCheckinLog {
    // Database connection and table name
    private $conn;
    private $table_name = "children_checkin_log";

    // Object properties
    public $id;
    public $child_id;
    public $service_id;
    public $checked_in_by;
    // REMOVED: checked_out_by - Using QR codes for check-in only
    public $check_in_time;
    // REMOVED: check_out_time - Using QR codes for check-in only
    public $attendance_date;
    public $notes;
    public $security_code;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Check in a child
     *
     * @return bool
     */
    public function checkIn() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET child_id = :child_id,
                      service_id = :service_id,
                      checked_in_by = :checked_in_by,
                      check_in_time = :check_in_time,
                      attendance_date = :attendance_date,
                      notes = :notes,
                      security_code = :security_code,
                      created_at = :created_at,
                      updated_at = :updated_at";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->child_id = htmlspecialchars(strip_tags($this->child_id));
        $this->service_id = htmlspecialchars(strip_tags($this->service_id));
        $this->checked_in_by = htmlspecialchars(strip_tags($this->checked_in_by));
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        $this->security_code = htmlspecialchars(strip_tags($this->security_code));

        // Bind data
        $stmt->bindParam(':child_id', $this->child_id);
        $stmt->bindParam(':service_id', $this->service_id);
        $stmt->bindParam(':checked_in_by', $this->checked_in_by);
        $stmt->bindParam(':check_in_time', $this->check_in_time);
        $stmt->bindParam(':attendance_date', $this->attendance_date);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':security_code', $this->security_code);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Create a new check-in record (alias for checkIn for AttendanceRouter compatibility)
     *
     * @return bool
     */
    public function create() {
        return $this->checkIn();
    }

    /**
     * Update an existing check-in record
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET child_id = :child_id,
                      service_id = :service_id,
                      checked_in_by = :checked_in_by,
                      check_in_time = :check_in_time,
                      attendance_date = :attendance_date,
                      notes = :notes,
                      security_code = :security_code,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->child_id = htmlspecialchars(strip_tags($this->child_id));
        $this->service_id = htmlspecialchars(strip_tags($this->service_id));
        $this->checked_in_by = htmlspecialchars(strip_tags($this->checked_in_by));
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        $this->security_code = htmlspecialchars(strip_tags($this->security_code));

        // Bind data
        $stmt->bindParam(':child_id', $this->child_id);
        $stmt->bindParam(':service_id', $this->service_id);
        $stmt->bindParam(':checked_in_by', $this->checked_in_by);
        $stmt->bindParam(':check_in_time', $this->check_in_time);
        $stmt->bindParam(':attendance_date', $this->attendance_date);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':security_code', $this->security_code);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // REMOVED: Check out functionality - Using QR codes for check-in only

    /**
     * Get currently checked-in children
     *
     * @param int $service_id
     * @param string $date
     * @return PDOStatement
     */
    public function getCurrentlyCheckedIn($service_id = null, $date = null) {
        $query = "SELECT ccl.*, 
                         m.first_name, m.last_name, m.profile_picture, m.date_of_birth,
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                         s.name as service_name,
                         ci.first_name as checked_in_by_first_name,
                         ci.last_name as checked_in_by_last_name
                  FROM " . $this->table_name . " ccl
                  JOIN members m ON ccl.child_id = m.id
                  JOIN services s ON ccl.service_id = s.id
                  JOIN members ci ON ccl.checked_in_by = ci.id
                  WHERE ccl.attendance_date = CURDATE()";

        $params = [];

        if ($service_id) {
            $query .= " AND ccl.service_id = :service_id";
            $params[':service_id'] = $service_id;
        }

        if ($date) {
            $query .= " AND ccl.attendance_date = :date";
            $params[':date'] = $date;
        } else {
            $query .= " AND ccl.attendance_date = CURDATE()";
        }

        $query .= " ORDER BY ccl.check_in_time DESC";

        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get check-in record by child and date
     *
     * @param int $child_id
     * @param string $date
     * @param int $service_id
     * @return array|false
     */
    public function getByChildAndDate($child_id, $date, $service_id = null) {
        $query = "SELECT ccl.*, 
                         m.first_name, m.last_name,
                         s.name as service_name
                  FROM " . $this->table_name . " ccl
                  JOIN members m ON ccl.child_id = m.id
                  JOIN services s ON ccl.service_id = s.id
                  WHERE ccl.child_id = :child_id 
                  AND ccl.attendance_date = :date";

        $params = [':child_id' => $child_id, ':date' => $date];

        if ($service_id) {
            $query .= " AND ccl.service_id = :service_id";
            $params[':service_id'] = $service_id;
        }

        $query .= " ORDER BY ccl.check_in_time DESC LIMIT 1";

        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get attendance history for a child
     *
     * @param int $child_id
     * @param int $limit
     * @return PDOStatement
     */
    public function getChildAttendanceHistory($child_id, $limit = 20) {
        $query = "SELECT ccl.*,
                         s.name as service_name,
                         ci.first_name as checked_in_by_first_name,
                         ci.last_name as checked_in_by_last_name,
                         TIMESTAMPDIFF(MINUTE, ccl.check_in_time, NOW()) as duration_minutes
                  FROM " . $this->table_name . " ccl
                  JOIN services s ON ccl.service_id = s.id
                  JOIN members ci ON ccl.checked_in_by = ci.id
                  WHERE ccl.child_id = :child_id
                  ORDER BY ccl.attendance_date DESC, ccl.check_in_time DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get attendance statistics for date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function getAttendanceStats($start_date, $end_date) {
        $query = "SELECT
                    COUNT(DISTINCT ccl.child_id) as unique_children,
                    COUNT(*) as total_checkins,
                    AVG(TIMESTAMPDIFF(MINUTE, ccl.check_in_time, NOW())) as avg_duration_minutes
                  FROM " . $this->table_name . " ccl
                  WHERE ccl.attendance_date BETWEEN :start_date AND :end_date";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Generate security code
     *
     * @param int $length
     * @return string
     */
    public function generateSecurityCode($length = 4) {
        return str_pad(rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }

    // REMOVED: Security code verification for checkout - Using QR codes for check-in only

    /**
     * Get daily attendance report
     *
     * @param string $date
     * @return PDOStatement
     */
    public function getDailyAttendanceReport($date) {
        $query = "SELECT ccl.*, 
                         m.first_name, m.last_name, m.date_of_birth,
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                         s.name as service_name,
                         ci.first_name as checked_in_by_first_name,
                         ci.last_name as checked_in_by_last_name,
                         co.first_name as checked_out_by_first_name,
                         co.last_name as checked_out_by_last_name,
                         CASE 
                            WHEN ccl.check_out_time IS NULL THEN 'Still Checked In'
                            ELSE CONCAT(TIMESTAMPDIFF(MINUTE, ccl.check_in_time, ccl.check_out_time), ' minutes')
                         END as duration
                  FROM " . $this->table_name . " ccl
                  JOIN members m ON ccl.child_id = m.id
                  JOIN services s ON ccl.service_id = s.id
                  JOIN members ci ON ccl.checked_in_by = ci.id
                  LEFT JOIN members co ON ccl.checked_out_by = co.id
                  WHERE ccl.attendance_date = :date
                  ORDER BY s.name, ccl.check_in_time";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);
        $stmt->execute();

        return $stmt;
    }
}
