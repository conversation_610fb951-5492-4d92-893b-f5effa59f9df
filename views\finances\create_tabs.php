<div class="fade-in mx-auto" style="width: 85%; max-width: 1200px;">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Add Financial Transaction</h1>
            <p class="text-gray-600 mt-1">Record church income and expenses efficiently</p>
        </div>
        <button onclick="history.back()" class="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 font-medium">
            ← Back
        </button>
    </div>

    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-50 border-l-4 border-red-400 text-red-800 p-5 rounded-r-lg mb-8 shadow-sm">
            <div class="flex items-center mb-3">
                <h4 class="font-semibold text-red-900">Please fix the following errors:</h4>
            </div>
            <ul class="list-disc ml-6 space-y-1">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li class="text-red-700"><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <!-- Enhanced Tab Navigation -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-1 rounded-t-xl">
            <nav class="flex space-x-1">
                <button type="button" id="tab-member-payments" class="tab-button active flex-1 py-4 px-6 text-sm font-semibold rounded-lg bg-white text-green-700 shadow-sm border border-green-200" data-tab="member-payments">
                    Member Payments
                </button>
                <button type="button" id="tab-general-income" class="tab-button flex-1 py-4 px-6 text-sm font-medium rounded-lg text-gray-600 hover:text-green-700 hover:bg-green-50 transition-all duration-200" data-tab="general-income">
                    General Income
                </button>
                <button type="button" id="tab-expenses" class="tab-button flex-1 py-4 px-6 text-sm font-medium rounded-lg text-gray-600 hover:text-red-700 hover:bg-red-50 transition-all duration-200" data-tab="expenses">
                    Expenses
                </button>
            </nav>
        </div>

        <!-- Form Content Area -->
        <div class="p-8">

            <form action="<?php echo url('finances/store'); ?>" method="POST" id="financeForm">
                <!-- CSRF Token -->
                <?php echo csrf_field(); ?>

                <!-- Dynamic transaction type field -->
                <input type="hidden" name="transaction_type" id="transaction_type" value="income">

                <!-- Member Payments Tab -->
                <div id="member-payments-content" class="tab-content">
                    <div class="mb-8">
                        <div class="bg-green-50 border-l-4 border-green-400 p-4 rounded-r-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-green-900 mb-1">Member Payment Details</h3>
                                    <p class="text-green-700 text-sm">Record payments from specific church members for tracking purposes</p>
                                </div>
                                <div class="flex gap-2 flex-wrap">
                                    <?php
                                    // UNIFIED SYSTEM - Generate dashboard buttons for all member payment categories
                                    if (isset($memberPaymentCategories) && !empty($memberPaymentCategories)):
                                        foreach ($memberPaymentCategories as $category):
                                            // FIXED: Generate correct dashboard URL with proper fallback
                                            if (!empty($category->dashboard_route)) {
                                                $dashboardUrl = $category->dashboard_route;
                                            } else {
                                                // Generate correct dynamic route instead of old slug-based route
                                                $dashboardUrl = "finance/dashboard/category?category=" . urlencode($category->name) . "&type=income";
                                            }

                                            // Use different colors for core categories
                                            if ($category->name === 'tithe') {
                                                $buttonClass = 'bg-green-600 hover:bg-green-700';
                                                $icon = 'fas fa-chart-line';
                                            } elseif ($category->name === 'pledge') {
                                                $buttonClass = 'bg-blue-600 hover:bg-blue-700';
                                                $icon = 'fas fa-handshake';
                                            } else {
                                                $buttonClass = 'bg-purple-600 hover:bg-purple-700';
                                                $icon = 'fas fa-chart-bar';
                                            }
                                    ?>
                                        <a href="<?php echo BASE_URL . $dashboardUrl; ?>"
                                           class="<?php echo $buttonClass; ?> text-white px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center">
                                            <i class="<?php echo $icon; ?> mr-1"></i> <?php echo htmlspecialchars($category->label); ?> Dashboard
                                        </a>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <label class="block text-sm font-semibold text-gray-800">Payment Type *</label>
                                    <a href="<?php echo BASE_URL; ?>finance/categories" target="_blank"
                                       class="text-green-600 hover:text-green-700 text-sm font-medium flex items-center transition-colors duration-200">
                                        <i class="fas fa-cog mr-1"></i>Manage Categories
                                    </a>
                                </div>
                                <select name="income_category" id="member-payment-type" class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white transition-all duration-200 hover:border-green-300">
                                    <option value="">Select payment type...</option>
                                    <?php
                                    // UNIFIED CATEGORY SYSTEM - Load all member payment categories from database
                                    $selectedCategory = $_SESSION['form_data']['income_category'] ?? '';

                                    if (isset($memberPaymentCategories) && !empty($memberPaymentCategories)) :
                                        foreach ($memberPaymentCategories as $category) :
                                            $selected = ($selectedCategory == $category->name) ? 'selected' : '';
                                            $isSystem = isset($category->is_system) && $category->is_system;
                                            $label = htmlspecialchars($category->label);

                                            // No need to mark system categories differently - unified system
                                    ?>
                                    <option value="<?php echo htmlspecialchars($category->name); ?>" <?php echo $selected; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                    <?php
                                        endforeach;
                                    else:
                                    ?>
                                    <option value="" disabled>No member payment categories available</option>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-800">Member *</label>
                                <div class="relative">
                                    <input type="text" id="member-search" placeholder="Search member by name..."
                                           class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 hover:border-green-300">
                                    <input type="hidden" name="member_id" id="selected-member-id">

                                    <!-- Hidden select with real member data for JavaScript -->
                                    <select id="member-data-source" class="hidden">
                                        <option value="">Select member</option>
                                        <?php
                                        require_once 'models/Member.php';
                                        $db = new Database();
                                        $conn = $db->getConnection();
                                        $member = new Member($conn);
                                        $members = $member->getByStatus('active');

                                        foreach ($members as $member_item) :
                                            $selected = (isset($_SESSION['form_data']['member_id']) && $_SESSION['form_data']['member_id'] == $member_item->id) ? 'selected' : '';
                                            $memberName = htmlspecialchars($member_item->first_name . ' ' . $member_item->last_name);
                                        ?>
                                        <option value="<?php echo $member_item->id; ?>"
                                                data-name="<?php echo $memberName; ?>"
                                                data-phone="<?php echo htmlspecialchars($member_item->phone_number ?? ''); ?>"
                                                <?php echo $selected; ?>>
                                            <?php echo $memberName; ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div id="member-search-results" class="hidden mt-3">
                                    <div class="bg-green-50 border-2 border-green-200 rounded-lg p-4">
                                        <div id="search-results-list" class="space-y-2 max-h-48 overflow-y-auto">
                                            <!-- Results populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>

                                <div id="selected-member-info" class="hidden mt-3">
                                    <div class="bg-green-50 border-2 border-green-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <div id="member-details" class="flex items-center">
                                                <!-- Member details populated by JavaScript -->
                                            </div>
                                            <button type="button" id="change-member" class="text-sm text-green-700 hover:text-green-900 font-medium">
                                                Change
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- General Income Tab -->
                <div id="general-income-content" class="tab-content hidden">
                    <div class="mb-8">
                        <div class="bg-emerald-50 border-l-4 border-emerald-400 p-4 rounded-r-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-emerald-900 mb-1">General Income Details</h3>
                                    <p class="text-emerald-700 text-sm">Record general church income not tied to specific members</p>
                                </div>
                                <div class="flex gap-2 flex-wrap">
                                    <?php
                                    // UNIFIED SYSTEM - Generate dashboard buttons for all general income categories
                                    if (isset($generalIncomeCategories) && !empty($generalIncomeCategories)):
                                        foreach ($generalIncomeCategories as $category):
                                            // FIXED: Generate correct dashboard URL with proper fallback
                                            if (!empty($category->dashboard_route)) {
                                                $dashboardUrl = $category->dashboard_route;
                                            } else {
                                                // Generate correct dynamic route instead of old slug-based route
                                                $dashboardUrl = "finance/dashboard/category?category=" . urlencode($category->name) . "&type=income";
                                            }

                                            // Use different colors for core categories
                                            if ($category->name === 'offering') {
                                                $buttonClass = 'bg-emerald-600 hover:bg-emerald-700';
                                                $icon = 'fas fa-chart-line';
                                            } elseif ($category->name === 'project_offering') {
                                                $buttonClass = 'bg-purple-600 hover:bg-purple-700';
                                                $icon = 'fas fa-building';
                                            } else {
                                                $buttonClass = 'bg-orange-600 hover:bg-orange-700';
                                                $icon = 'fas fa-chart-bar';
                                            }
                                    ?>
                                        <a href="<?php echo BASE_URL . $dashboardUrl; ?>"
                                           class="<?php echo $buttonClass; ?> text-white px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center">
                                            <i class="<?php echo $icon; ?> mr-1"></i> <?php echo htmlspecialchars($category->label); ?> Dashboard
                                        </a>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-8">
                        <div class="grid grid-cols-1 gap-8">
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <label class="block text-sm font-semibold text-gray-800">Income Type *</label>
                                    <a href="<?php echo BASE_URL; ?>finance/categories" target="_blank"
                                       class="text-emerald-600 hover:text-emerald-700 text-sm font-medium flex items-center transition-colors duration-200">
                                        <i class="fas fa-cog mr-1"></i>Manage Categories
                                    </a>
                                </div>
                                <select name="income_category" id="general-income-type" class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white transition-all duration-200 hover:border-emerald-300">
                                    <option value="">Select income type...</option>
                                    <?php
                                    // UNIFIED CATEGORY SYSTEM - Load all general income categories from database
                                    if (isset($generalIncomeCategories) && !empty($generalIncomeCategories)) :
                                        foreach ($generalIncomeCategories as $category) :
                                            $selected = ($selectedCategory == $category->name) ? 'selected' : '';
                                            $label = htmlspecialchars($category->label);
                                    ?>
                                    <option value="<?php echo htmlspecialchars($category->name); ?>" <?php echo $selected; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                    <?php
                                        endforeach;
                                    else:
                                    ?>
                                    <option value="" disabled>No general income categories available</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Expenses Tab -->
                <div id="expenses-content" class="tab-content hidden">
                    <div class="mb-8">
                        <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-red-900 mb-1">Expense Details</h3>
                                    <p class="text-red-700 text-sm">Record church expenses and expenditures</p>
                                </div>
                                <div class="flex gap-2 flex-wrap">
                                    <?php
                                    // UNIFIED SYSTEM - Generate dashboard buttons for all expense categories
                                    if (isset($expenseCategories) && !empty($expenseCategories)):
                                        foreach ($expenseCategories as $category):
                                            // FIXED: Generate correct dashboard URL with proper fallback
                                            if (!empty($category->dashboard_route)) {
                                                $dashboardUrl = $category->dashboard_route;
                                            } else {
                                                // Generate correct dynamic route instead of old slug-based route
                                                $dashboardUrl = "finance/dashboard/category?category=" . urlencode($category->name) . "&type=expense";
                                            }

                                            // Use different colors for core categories
                                            if ($category->name === 'utilities') {
                                                $buttonClass = 'bg-red-600 hover:bg-red-700';
                                                $icon = 'fas fa-bolt';
                                            } elseif ($category->name === 'events') {
                                                $buttonClass = 'bg-orange-600 hover:bg-orange-700';
                                                $icon = 'fas fa-calendar';
                                            } else {
                                                $buttonClass = 'bg-gray-600 hover:bg-gray-700';
                                                $icon = 'fas fa-chart-bar';
                                            }
                                    ?>
                                        <a href="<?php echo BASE_URL . $dashboardUrl; ?>"
                                           class="<?php echo $buttonClass; ?> text-white px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center">
                                            <i class="<?php echo $icon; ?> mr-1"></i> <?php echo htmlspecialchars($category->label); ?> Dashboard
                                        </a>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-8">
                        <div class="grid grid-cols-1 gap-8">
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <label class="block text-sm font-semibold text-gray-800">Expense Type *</label>
                                    <a href="<?php echo BASE_URL; ?>finance/categories" target="_blank"
                                       class="text-red-600 hover:text-red-700 text-sm font-medium flex items-center transition-colors duration-200">
                                        <i class="fas fa-cog mr-1"></i>Manage Categories
                                    </a>
                                </div>
                                <select name="expense_category" id="expense-type" class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white transition-all duration-200 hover:border-red-300">
                                    <option value="">Select expense type...</option>
                                    <?php
                                    // UNIFIED CATEGORY SYSTEM - Load all expense categories from database
                                    if (isset($expenseCategories) && !empty($expenseCategories)) :
                                        foreach ($expenseCategories as $category) :
                                            $selected = ($selectedCategory == $category->name) ? 'selected' : '';
                                            $label = htmlspecialchars($category->label);
                                    ?>
                                    <option value="<?php echo htmlspecialchars($category->name); ?>" <?php echo $selected; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                    <?php
                                        endforeach;
                                    else:
                                    ?>
                                    <option value="" disabled>No expense categories available</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Common Transaction Details -->
                <div class="common-fields mt-12 pt-8 border-t-2 border-gray-100">
                    <div class="mb-8">
                        <div class="bg-purple-50 border-l-4 border-purple-400 p-4 rounded-r-lg">
                            <h3 class="text-lg font-semibold text-purple-900 mb-1">Transaction Details</h3>
                            <p class="text-purple-700 text-sm">Enter the transaction amount and other details</p>
                        </div>
                    </div>

                    <div class="space-y-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-800">Amount *</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <span class="text-purple-600 font-semibold text-sm">GH₵</span>
                                    </div>
                                    <input type="number" name="amount" step="0.01" min="0"
                                           class="w-full pl-12 px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-purple-300 text-lg font-medium"
                                           placeholder="0.00"
                                           value="<?php echo isset($_SESSION['form_data']['amount']) ? htmlspecialchars($_SESSION['form_data']['amount']) : ''; ?>"
                                           required>
                                </div>
                            </div>

                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-800">Payment Method *</label>
                                <select name="payment_method"
                                        class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white transition-all duration-200 hover:border-purple-300"
                                        required>
                                    <option value="">Select payment method...</option>
                                    <option value="cash" <?php echo (isset($_SESSION['form_data']['payment_method']) && $_SESSION['form_data']['payment_method'] == 'cash') ? 'selected' : ''; ?>>Cash</option>
                                    <option value="bank_transfer" <?php echo (isset($_SESSION['form_data']['payment_method']) && $_SESSION['form_data']['payment_method'] == 'bank_transfer') ? 'selected' : ''; ?>>Bank Transfer</option>
                                    <option value="mobile_money" <?php echo (isset($_SESSION['form_data']['payment_method']) && $_SESSION['form_data']['payment_method'] == 'mobile_money') ? 'selected' : ''; ?>>Mobile Money</option>
                                    <option value="cheque" <?php echo (isset($_SESSION['form_data']['payment_method']) && $_SESSION['form_data']['payment_method'] == 'cheque') ? 'selected' : ''; ?>>Cheque</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-800">Transaction Date *</label>
                                <input type="date" name="transaction_date"
                                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-purple-300"
                                       value="<?php echo isset($_SESSION['form_data']['transaction_date']) ? htmlspecialchars($_SESSION['form_data']['transaction_date']) : date('Y-m-d'); ?>"
                                       required>
                            </div>

                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-800">Reference Number</label>
                                <input type="text" name="reference_number"
                                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-purple-300"
                                       placeholder="Optional reference number"
                                       value="<?php echo isset($_SESSION['form_data']['reference_number']) ? htmlspecialchars($_SESSION['form_data']['reference_number']) : ''; ?>">
                            </div>
                        </div>

                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-gray-800">Description</label>
                            <textarea name="description" rows="4"
                                      class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none transition-all duration-200 hover:border-purple-300"
                                      placeholder="Optional description or notes..."><?php echo isset($_SESSION['form_data']['description']) ? htmlspecialchars($_SESSION['form_data']['description']) : ''; ?></textarea>
                        </div>

                        <div class="flex justify-end space-x-6 pt-8 border-t-2 border-gray-100">
                            <button type="button" onclick="history.back()"
                               class="px-8 py-3 border-2 border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-8 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl">
                                Save Transaction
                            </button>
                        </div>
                    </div>
                </div>

        </form>
    </div>
</div>

<style>
/* Enhanced Tab Styling with Strategic Colors */
.tab-button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.tab-button.active {
    background-color: white !important;
    color: #15803d !important; /* Green-700 */
    border: 2px solid #22c55e !important; /* Green-500 */
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
    transform: translateY(-1px);
}

.tab-button:not(.active):hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Tab Content Animation */
.tab-content {
    animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Form Field Focus Effects */
input:focus, select:focus, textarea:focus {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Smooth Page Transitions */
.fade-in {
    animation: pageLoad 0.6s ease-out;
}

@keyframes pageLoad {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Button Hover Effects */
button[type="submit"]:hover {
    transform: translateY(-2px);
}

/* Color-coded Focus States */
#member-payment-type:focus {
    border-color: #22c55e !important;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

#general-income-type:focus {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

#expense-type:focus {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    const memberSearch = document.getElementById('member-search');
    const memberSearchResults = document.getElementById('member-search-results');
    const searchResultsList = document.getElementById('search-results-list');
    const selectedMemberInfo = document.getElementById('selected-member-info');
    const selectedMemberDetails = document.getElementById('member-details');
    const changeMemberBtn = document.getElementById('change-member');
    const selectedMemberIdInput = document.getElementById('selected-member-id');
    const memberDataSource = document.getElementById('member-data-source');

    // Load real member data from PHP
    let allMembers = [];
    if (memberDataSource) {
        const options = memberDataSource.querySelectorAll('option');
        allMembers = Array.from(options)
            .filter(option => option.value !== '')
            .map(option => ({
                id: option.value,
                name: option.dataset.name || option.textContent.trim(),
                phone: option.dataset.phone || ''
            }));
    }

    // Enhanced Tab switching with color strategy
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Update button states with enhanced styling
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-white', 'text-green-700', 'border-green-200', 'shadow-sm');

                // Reset to default state based on tab type
                if (btn.dataset.tab === 'member-payments') {
                    btn.classList.add('text-gray-600', 'hover:text-green-700', 'hover:bg-green-50');
                } else if (btn.dataset.tab === 'general-income') {
                    btn.classList.add('text-gray-600', 'hover:text-green-700', 'hover:bg-green-50');
                } else if (btn.dataset.tab === 'expenses') {
                    btn.classList.add('text-gray-600', 'hover:text-red-700', 'hover:bg-red-50');
                }
            });

            // Activate selected tab with appropriate colors
            this.classList.remove('text-gray-600', 'hover:text-green-700', 'hover:text-green-700', 'hover:text-red-700', 'hover:bg-green-50', 'hover:bg-green-50', 'hover:bg-red-50');
            this.classList.add('active', 'bg-white', 'text-green-700', 'border-green-200', 'shadow-sm');

            // Update content visibility with animation
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            const targetContent = document.getElementById(targetTab + '-content');
            if (targetContent) {
                targetContent.classList.remove('hidden');
            }

            // Update transaction type based on active tab
            const transactionTypeInput = document.getElementById('transaction_type');
            if (transactionTypeInput) {
                if (targetTab === 'expenses') {
                    transactionTypeInput.value = 'expense';
                } else {
                    transactionTypeInput.value = 'income';
                }
            }
        });
    });

    // Member search functionality
    if (memberSearch) {
        let searchTimeout;

        memberSearch.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                hideSearchResults();
                return;
            }

            searchTimeout = setTimeout(() => {
                searchMembers(query);
            }, 300);
        });
    }

    function searchMembers(query) {
        if (!query.trim() || allMembers.length === 0) {
            displaySearchResults([]);
            return;
        }

        const searchTerm = query.toLowerCase();
        const results = allMembers.filter(member => {
            const name = (member.name || '').toLowerCase();
            const phone = (member.phone || '').toLowerCase();

            return name.includes(searchTerm) || phone.includes(searchTerm);
        }).slice(0, 8); // Limit to 8 results

        displaySearchResults(results);
    }

    function displaySearchResults(results) {
        if (!searchResultsList) return;

        searchResultsList.innerHTML = '';

        if (results.length === 0) {
            searchResultsList.innerHTML = `
                <div class="text-center py-4">
                    <p class="text-gray-500 text-sm">No members found</p>
                    <p class="text-gray-400 text-xs mt-1">Try a different search term</p>
                </div>
            `;
        } else {
            results.forEach(member => {
                const div = document.createElement('div');
                div.className = 'p-3 hover:bg-green-100 cursor-pointer border-b border-green-200 last:border-b-0 transition-all duration-200 rounded-lg mb-2 last:mb-0';
                div.innerHTML = `
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mr-3 text-white font-medium text-sm">
                            ${member.name.charAt(0).toUpperCase()}
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-gray-800">${member.name}</div>
                            <div class="text-sm text-gray-600">${member.phone || 'No phone'}</div>
                        </div>
                        <div class="text-green-600 text-xs font-medium">
                            Select
                        </div>
                    </div>
                `;
                div.addEventListener('click', () => selectMember(member));
                searchResultsList.appendChild(div);
            });
        }

        showSearchResults();
    }

    function selectMember(member) {
        memberSearch.value = member.name;
        selectedMemberIdInput.value = member.id;

        selectedMemberDetails.innerHTML = `
            <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mr-3 text-white font-bold text-lg">
                    ${member.name.charAt(0).toUpperCase()}
                </div>
                <div class="flex-1">
                    <div class="font-semibold text-green-800">${member.name}</div>
                    <div class="text-sm text-green-600">${member.phone || 'No phone'}</div>
                </div>
            </div>
        `;

        hideSearchResults();
        selectedMemberInfo.classList.remove('hidden');
    }

    function showSearchResults() {
        if (memberSearchResults) {
            memberSearchResults.classList.remove('hidden');
        }
    }

    function hideSearchResults() {
        if (memberSearchResults) {
            memberSearchResults.classList.add('hidden');
        }
    }

    if (changeMemberBtn) {
        changeMemberBtn.addEventListener('click', function() {
            selectedMemberInfo.classList.add('hidden');
            selectedMemberIdInput.value = '';
            memberSearch.value = '';
            memberSearch.focus();
        });
    }

    // Form submission handling
    const form = document.getElementById('financeForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Get the active tab
            const activeTab = document.querySelector('.tab-button.active');
            const activeTabType = activeTab ? activeTab.dataset.tab : 'member-payments';

            // Update transaction type one more time before submission
            const transactionTypeInput = document.getElementById('transaction_type');
            if (transactionTypeInput) {
                if (activeTabType === 'expenses') {
                    transactionTypeInput.value = 'expense';
                } else {
                    transactionTypeInput.value = 'income';
                }
            }

            // Clear fields that don't belong to the active tab to prevent conflicts
            if (activeTabType === 'member-payments') {
                // Clear general income and expense fields
                const generalIncomeSelect = document.getElementById('general-income-type');
                const expenseSelect = document.getElementById('expense-type');
                if (generalIncomeSelect) generalIncomeSelect.removeAttribute('name');
                if (expenseSelect) expenseSelect.removeAttribute('name');
            } else if (activeTabType === 'general-income') {
                // Clear member payment and expense fields
                const memberPaymentSelect = document.getElementById('member-payment-type');
                const expenseSelect = document.getElementById('expense-type');
                if (memberPaymentSelect) memberPaymentSelect.removeAttribute('name');
                if (expenseSelect) expenseSelect.removeAttribute('name');
            } else if (activeTabType === 'expenses') {
                // Clear income fields
                const memberPaymentSelect = document.getElementById('member-payment-type');
                const generalIncomeSelect = document.getElementById('general-income-type');
                if (memberPaymentSelect) memberPaymentSelect.removeAttribute('name');
                if (generalIncomeSelect) generalIncomeSelect.removeAttribute('name');
            }
        });
    }
});

// Form validation and submission
// (Modal functions removed - using existing categories page instead)
</script>

<!-- Modal removed - using existing categories page at /finance/categories -->

<script>
    // Dashboard navigation function - completely isolated from form
    function openDashboard(url) {
        console.log('Opening dashboard:', url);

        // Multiple fallback methods to ensure navigation works
        try {
            // Method 1: window.open in new tab
            const newWindow = window.open(url, '_blank');

            // Method 2: If popup blocked, try location.href
            if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                console.log('Popup blocked, using location.href');
                window.location.href = url;
            } else {
                console.log('Dashboard opened in new tab successfully');
            }
        } catch (error) {
            console.error('Error opening dashboard:', error);
            // Method 3: Fallback to direct navigation
            window.location.href = url;
        }
    }

    // Additional protection for any remaining dashboard links
    document.addEventListener('DOMContentLoaded', function() {
        const dashboardBtns = document.querySelectorAll('.dashboard-btn');
        dashboardBtns.forEach(btn => {
            // Ensure buttons don't submit the form
            btn.setAttribute('type', 'button');
        });

        // Check URL parameters and switch to the correct tab
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');

        if (tabParam) {
            // Valid tab parameters
            const validTabs = ['member-payments', 'general-income', 'expenses'];

            if (validTabs.includes(tabParam)) {
                // Find and click the corresponding tab button
                const targetTabButton = document.getElementById('tab-' + tabParam);
                if (targetTabButton) {
                    targetTabButton.click();
                }
            }
        }
    });
</script>

<?php
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>
