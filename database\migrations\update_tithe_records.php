<?php
// Define application root directory
define('APP_ROOT', dirname(dirname(__DIR__)));

/**
 * Update Tithe Records
 *
 * This script updates existing tithe records to associate them with members.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Output basic HTML structure
echo "<!DOCTYPE html>
<html>
<head>
    <title>Update Tithe Records</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Update Tithe Records</h1>";

try {
    // Include necessary files
    require_once APP_ROOT . '/config/config.php';
    echo "<p class='success'>Successfully included config/config.php</p>";

    require_once APP_ROOT . '/config/database.php';
    echo "<p class='success'>Successfully included config/database.php</p>";

    require_once APP_ROOT . '/models/Finance.php';
    echo "<p class='success'>Successfully included models/Finance.php</p>";

    require_once APP_ROOT . '/models/Member.php';
    echo "<p class='success'>Successfully included models/Member.php</p>";

    if (file_exists('utils/helpers.php')) {
        require_once APP_ROOT . '/utils/helpers.php';
        echo "<p class='success'>Successfully included utils/helpers.php</p>";
    } else {
        echo "<p class='error'>utils/helpers.php not found</p>";
    }

    // Try to create database connection
    $database = new Database();
    $conn = $database->getConnection();
    echo "<p class='success'>Successfully connected to database</p>";

    // Create finance and member objects
    $finance = new Finance($conn);
    echo "<p class='success'>Successfully created Finance object</p>";

    $member = new Member($conn);
    echo "<p class='success'>Successfully created Member object</p>";

    // Get all tithe records
    $query = "SELECT * FROM finances WHERE category = 'tithe' AND (member_id IS NULL OR member_id = 0)";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $tithe_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p class='success'>Found " . count($tithe_records) . " tithe records without member associations</p>";

    // Get all active members
    $stmt = $member->getByStatus('active');
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p class='success'>Found " . count($members) . " active members</p>";

    // Display form if there are tithe records and members
    if (!empty($tithe_records) && !empty($members)) {
        echo "<form method='POST' action=''>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Amount</th><th>Date</th><th>Member</th></tr>";

        foreach ($tithe_records as $tithe) {
            echo "<tr>";
            echo "<td>{$tithe['id']}</td>";
            echo "<td>GH₵ " . number_format($tithe['amount'], 2) . "</td>";
            echo "<td>" . date('M d, Y', strtotime($tithe['transaction_date'])) . "</td>";
            echo "<td>";
            echo "<select name='tithe_records[{$tithe['id']}]'>";
            echo "<option value=''>Select Member</option>";

            foreach ($members as $member) {
                echo "<option value='{$member['id']}'>";
                echo "{$member['first_name']} {$member['last_name']}";
                echo "</option>";
            }

            echo "</select>";
            echo "</td>";
            echo "</tr>";
        }

        echo "</table>";
        echo "<p><input type='submit' value='Update Tithe Records'></p>";
        echo "</form>";
    } else {
        if (empty($members)) {
            echo "<p class='error'>No active members found. Please add members first.</p>";
        }
        if (empty($tithe_records)) {
            echo "<p class='success'>All tithe records already have member associations.</p>";
        }
    }

    // Process form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['tithe_records'])) {
        echo "<h2>Processing Form Submission</h2>";
        $updated_count = 0;

        foreach ($_POST['tithe_records'] as $tithe_id => $member_id) {
            if (!empty($member_id)) {
                $update_query = "UPDATE finances SET member_id = :member_id WHERE id = :tithe_id";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bindParam(':member_id', $member_id);
                $update_stmt->bindParam(':tithe_id', $tithe_id);

                if ($update_stmt->execute()) {
                    $updated_count++;
                    echo "<p class='success'>Updated tithe ID {$tithe_id} to associate with member ID {$member_id}</p>";
                } else {
                    echo "<p class='error'>Failed to update tithe ID {$tithe_id}</p>";
                }
            }
        }

        echo "<p class='success'>Successfully updated {$updated_count} tithe records</p>";
        echo "<p><a href='finance/tithes'>Go to Tithe Tracking</a></p>";
    }

} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
    echo "<p class='error'>File: " . $e->getFile() . "</p>";
    echo "<p class='error'>Line: " . $e->getLine() . "</p>";
}

echo "</body>
</html>";
?>
