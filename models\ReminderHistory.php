<?php
/**
 * Reminder History Model
 */

class ReminderHistory {
    // Database connection and table name
    private $conn;
    private $table_name = "reminder_history";

    // Object properties
    public $id;
    public $member_id;
    public $message;
    public $sent_at;
    public $status;
    public $created_at;
    public $updated_at;

    /**
     * Constructor
     *
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create reminder history record
     *
     * @return boolean
     */
    public function create() {
        // Create query
        $query = "INSERT INTO " . $this->table_name . "
                  SET member_id = :member_id,
                      message = :message,
                      sent_at = :sent_at,
                      status = :status,
                      created_at = :created_at,
                      updated_at = :updated_at";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->member_id = htmlspecialchars(strip_tags($this->member_id));
        $this->message = htmlspecialchars(strip_tags($this->message));
        $this->sent_at = htmlspecialchars(strip_tags($this->sent_at));
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->created_at = htmlspecialchars(strip_tags($this->created_at));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind data
        $stmt->bindParam(":member_id", $this->member_id);
        $stmt->bindParam(":message", $this->message);
        $stmt->bindParam(":sent_at", $this->sent_at);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":created_at", $this->created_at);
        $stmt->bindParam(":updated_at", $this->updated_at);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Get all reminder history records
     *
     * @return PDOStatement
     */
    public function getAll() {
        // Create query
        $query = "SELECT rh.*, m.first_name, m.last_name, CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " rh
                  LEFT JOIN members m ON rh.member_id = m.id
                  ORDER BY rh.sent_at DESC";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Execute query
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get reminder history by member ID
     *
     * @param int $member_id
     * @return PDOStatement
     */
    public function getByMemberId($member_id) {
        // Create query
        $query = "SELECT rh.*, m.first_name, m.last_name, CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " rh
                  LEFT JOIN members m ON rh.member_id = m.id
                  WHERE rh.member_id = :member_id
                  ORDER BY rh.sent_at DESC";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bindParam(":member_id", $member_id);

        // Execute query
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get reminder history by status
     *
     * @param string $status
     * @return PDOStatement
     */
    public function getByStatus($status) {
        // Create query
        $query = "SELECT rh.*, m.first_name, m.last_name, CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " rh
                  LEFT JOIN members m ON rh.member_id = m.id
                  WHERE rh.status = :status
                  ORDER BY rh.sent_at DESC";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bindParam(":status", $status);

        // Execute query
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get reminder history by date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return PDOStatement
     */
    public function getByDateRange($start_date, $end_date) {
        // Create query
        $query = "SELECT rh.*, m.first_name, m.last_name, CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " rh
                  LEFT JOIN members m ON rh.member_id = m.id
                  WHERE DATE(rh.sent_at) BETWEEN :start_date AND :end_date
                  ORDER BY rh.sent_at DESC";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bindParam(":start_date", $start_date);
        $stmt->bindParam(":end_date", $end_date);

        // Execute query
        $stmt->execute();

        return $stmt;
    }

    /**
     * Update reminder history status
     *
     * @return boolean
     */
    public function updateStatus() {
        // Create query
        $query = "UPDATE " . $this->table_name . "
                  SET status = :status,
                      updated_at = :updated_at
                  WHERE id = :id";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind data
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":updated_at", $this->updated_at);
        $stmt->bindParam(":id", $this->id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }
}
?>
