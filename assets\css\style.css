/**
 * ICGC Emmanuel Temple Church Management System
 * Main stylesheet
 */

/* Reset some default styles */
html, body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

:root {
    --primary: #3F7D58;
    --primary-light: #5a9e76;
    --primary-dark: #2c5a3f;
    --secondary: #D3E671;
    --secondary-light: #e1ef9a;
    --secondary-dark: #b8c95c;
    --sidebar-left: #3F7D58;
    --sidebar-middle: #8AAC65;
    --sidebar-right: #D3E671;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}
::-webkit-scrollbar-track {
    background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
    background: var(--secondary);
    border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-dark);
}

/* Sidebar navigation styles removed - using Tailwind CSS only */

/* All sidebar styles removed - using pure Tailwind CSS */

/* All sidebar container and navigation styles removed - using pure Tailwind CSS */



/* Mobile responsiveness - sidebar styles removed, using Tailwind CSS */

/* Utility classes */
.text-primary {
    color: var(--primary);
}
.text-primary-light {
    color: var(--primary-light);
}
.text-primary-dark {
    color: var(--primary-dark);
}
.text-sidebar-left {
    color: var(--sidebar-left);
}
.text-sidebar-middle {
    color: var(--sidebar-middle);
}
.text-sidebar-right {
    color: var(--sidebar-right);
}
.bg-primary {
    background-color: var(--primary);
}
.bg-primary-light {
    background-color: var(--primary-light);
}
.bg-primary-dark {
    background-color: var(--primary-dark);
}
.bg-secondary {
    background-color: var(--secondary);
}
.bg-secondary-light {
    background-color: var(--secondary-light);
}
.bg-secondary-dark {
    background-color: var(--secondary-dark);
}
.bg-sidebar-left {
    background-color: var(--sidebar-left);
}
.bg-sidebar-middle {
    background-color: var(--sidebar-middle);
}
.bg-sidebar-right {
    background-color: var(--sidebar-right);
}
.bg-sidebar-gradient {
    background: var(--sidebar-left);
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    .sidebar.active {
        transform: translateX(0);
    }
}

/*
 * LAYOUT FIX: Prevent double container issue
 *
 * IMPORTANT: When creating new pages, DO NOT use "container mx-auto px-4"
 * in your page content as it's already inside the main layout container.
 *
 * The main layout (views/layouts/main.php) already provides:
 * - <main class="flex-1 p-6 overflow-y-auto"> which gives proper padding
 *
 * For page content, simply use:
 * - <div class="fade-in"> for the main wrapper
 * - Add your content directly without additional containers
 *
 * This prevents the "two-column layout" issue that keeps recurring.
 */

/* Helper class for page content that needs to be full-width within the layout */
.page-content {
    /* Use this class for page content instead of container mx-auto px-4 */
    width: 100%;
}

/* Helper class for content that needs to be centered with max width */
.page-content-centered {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Helper class for narrow content (forms, etc.) */
.page-content-narrow {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

/* Family Management Table Enhancements */
.family-table {
    font-size: 0.875rem;
}

.family-table th {
    font-weight: 600;
    letter-spacing: 0.05em;
}

.family-table td {
    vertical-align: top;
}

.child-card {
    transition: all 0.2s ease-in-out;
}

.child-card:hover {
    background-color: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button {
    transition: all 0.2s ease-in-out;
    padding: 0.25rem;
    border-radius: 0.25rem;
}

.action-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(1.1);
}

/* Search and Filter Enhancements */
.search-container {
    position: relative;
}

.search-container input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Table Row Hover Effects */
.family-row {
    transition: background-color 0.2s ease-in-out;
}

.family-row:hover {
    background-color: #f9fafb;
}

/* Dropdown Styles */
.dropdown-menu {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    background-color: white;
    z-index: 50;
}

.dropdown-item {
    transition: background-color 0.2s ease-in-out;
}

.dropdown-item:hover {
    background-color: #f3f4f6;
}

/* Action Button Enhancements */
.action-button {
    transition: all 0.2s ease-in-out;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.25rem;
    height: 2.25rem;
    font-size: 0.875rem;
    text-decoration: none;
    background-color: white;
}

.action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Responsive Table */
@media (max-width: 768px) {
    .family-table {
        font-size: 0.75rem;
    }

    .family-table th,
    .family-table td {
        padding: 0.5rem 0.25rem;
    }

    .child-card {
        padding: 0.5rem;
        margin-bottom: 0.25rem;
    }

    .action-button {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    /* Stack action buttons vertically on very small screens */
    @media (max-width: 480px) {
        .action-button {
            width: 1.75rem;
            height: 1.75rem;
            margin-bottom: 0.25rem;
        }
    }
}

/* Age Group Progress Bar Enhancements */
.age-group-card {
    transition: all 0.3s ease-in-out;
}

.age-group-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
