<?php
/**
 * SMS Service Class
 * Handles all SMS business logic and operations
 */

require_once 'models/Sms.php';
require_once 'models/Member.php';
require_once 'models/Setting.php';
require_once 'models/User.php';
require_once 'utils/sms_helper.php';

class SmsService {
    private $database;
    private $sms;
    private $member;
    private $setting;
    
    // Error handling
    public $error;
    
    /**
     * Constructor
     *
     * @param Database $database
     */
    public function __construct($database) {
        $this->database = $database;
        $this->sms = new Sms($database->getConnection());
        $this->member = new Member($database->getConnection());
        $this->setting = new Setting($database->getConnection());
    }
    
    /**
     * Get SMS balance with caching
     *
     * @return int SMS balance
     */
    public function getSmsBalance() {
        $settings = $this->setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';
        
        // Check if we have a cached balance and it's less than 15 minutes old
        $cached_balance = isset($_SESSION['sms_balance']) ? $_SESSION['sms_balance'] : null;
        $cached_time = isset($_SESSION['sms_balance_time']) ? $_SESSION['sms_balance_time'] : 0;
        $cache_lifetime = 15 * 60; // 15 minutes in seconds
        
        if ($cached_balance && (time() - $cached_time < $cache_lifetime)) {
            // Use cached balance
            return $cached_balance;
        } else {
            // Get fresh balance with timeout protection
            try {
                $balance_result = check_sms_balance($api_key);
                
                if ($balance_result['status']) {
                    $sms_balance = $balance_result['balance'];
                    // Cache the successful result
                    $_SESSION['sms_balance'] = $sms_balance;
                    $_SESSION['sms_balance_time'] = time();
                    return $sms_balance;
                } else {
                    // API failed, use cached balance if available, otherwise default to 0
                    $sms_balance = $cached_balance !== null ? $cached_balance : 0;
                    error_log("SMS Balance API failed: " . $balance_result['message']);
                    return $sms_balance;
                }
            } catch (Exception $e) {
                // Exception occurred, use cached balance if available, otherwise default to 0
                $sms_balance = $cached_balance !== null ? $cached_balance : 0;
                error_log("SMS Balance Exception: " . $e->getMessage());
                return $sms_balance;
            }
        }
    }
    
    /**
     * Process recipients based on selection method
     *
     * @param array $postData POST data from form
     * @return array Array with 'recipients' and 'phone_numbers'
     */
    public function processRecipients($postData) {
        $recipients = [];
        $phone_numbers = [];
        $debug_info = [];
        
        // 1. All Members
        if (isset($postData['send_to_all']) && $postData['send_to_all'] === 'all') {
            $debug_info['selection_method'] = 'all_members';
            
            $allMembers = $this->member->getAll();
            $debug_info['all_members_count'] = count($allMembers);
            
            foreach ($allMembers as $member) {
                if (!empty($member->phone_number)) {
                    $recipients[] = $member->id;
                    $phone_numbers[] = $member->phone_number;
                }
            }
            
            $debug_info['valid_phone_numbers'] = count($phone_numbers);
        }
        
        // 2. By Group (Department or Role)
        elseif (!empty($postData['recipient_groups']) && is_array($postData['recipient_groups'])) {
            $debug_info['selection_method'] = 'by_group';
            $debug_info['groups'] = $postData['recipient_groups'];
            
            // Track unique members to avoid duplicates
            $uniqueMembers = [];
            
            // Process each selected group
            foreach ($postData['recipient_groups'] as $group) {
                // Get members by group
                if (strpos($group, 'department_') === 0) {
                    $department = str_replace('department_', '', $group);
                    $stmt = $this->member->getByDepartment($department);
                    $debug_info['departments'][] = $department;
                } elseif (strpos($group, 'role_') === 0) {
                    $role = str_replace('role_', '', $group);
                    $stmt = $this->member->getByRole($role);
                    $debug_info['roles'][] = $role;
                } else {
                    continue; // Skip unknown group types
                }
                
                if ($stmt) {
                    $members = $stmt->fetchAll();
                    foreach ($members as $member) {
                        // Skip if already processed or no phone number
                        if (empty($member->phone_number) || isset($uniqueMembers[$member->id])) {
                            continue;
                        }
                        
                        // Add to recipients
                        $recipients[] = $member->id;
                        $phone_numbers[] = $member->phone_number;
                        $uniqueMembers[$member->id] = true;
                    }
                }
            }
            
            $debug_info['total_group_members'] = count($uniqueMembers);
            $debug_info['valid_phone_numbers'] = count($phone_numbers);
        }
        
        // 3. Individual Members
        elseif (!empty($postData['recipients']) && is_array($postData['recipients'])) {
            $debug_info['selection_method'] = 'individual_members';
            $debug_info['selected_members'] = count($postData['recipients']);
            $debug_info['selected_phone_numbers'] = $postData['recipients'];
            
            // Process phone numbers directly
            foreach ($postData['recipients'] as $phoneNumber) {
                if (!empty($phoneNumber)) {
                    // Find the member with this phone number to store in recipients list
                    $memberData = $this->member->getByPhoneNumber($phoneNumber);
                    
                    if ($memberData) {
                        $recipients[] = $memberData->id;
                    } else {
                        // If no member found, just use the phone number as identifier
                        $recipients[] = 'phone_' . $phoneNumber;
                    }
                    
                    // Add the phone number to the list
                    $phone_numbers[] = $phoneNumber;
                }
            }
            
            $debug_info['valid_phone_numbers'] = count($phone_numbers);
        }
        
        return [
            'recipients' => $recipients,
            'phone_numbers' => $phone_numbers,
            'debug_info' => $debug_info
        ];
    }
    
    /**
     * Validate SMS sending requirements
     *
     * @param array $postData POST data from form
     * @return bool True if validation passes, false otherwise
     */
    public function validateSmsRequest($postData) {
        $this->error = null;
        
        // Validate message
        if (empty($postData['message'])) {
            $this->error = 'Message is required.';
            return false;
        }
        
        // Validate message length (typical SMS limit is 160 characters)
        if (strlen($postData['message']) > 1000) {
            $this->error = 'Message is too long. Maximum 1000 characters allowed.';
            return false;
        }
        
        // Check if API key is configured
        $settings = $this->setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';
        
        if (empty($api_key)) {
            $this->error = 'SMS API key is not configured. Please set it in the settings.';
            return false;
        }
        
        return true;
    }
    
    /**
     * Create SMS record in database
     *
     * @param array $data SMS data
     * @return bool True if successful, false otherwise
     */
    public function createSmsRecord($data) {
        // Set SMS properties
        $this->sms->message = sanitize($data['message']);
        $this->sms->recipients = $data['recipients'];
        $this->sms->sent_date = date('Y-m-d H:i:s');
        $this->sms->status = 'sent'; // Initialize with 'sent' status

        // Handle user ID
        $user_id = $data['user_id'] ?? $_SESSION['user_id'] ?? null;
        if ($user_id) {
            // Verify that the user exists in the database
            $user = new User($this->database->getConnection());
            if ($user->getById($user_id)) {
                $this->sms->sent_by = $user_id;
            } else {
                $this->sms->sent_by = null;
            }
        } else {
            $this->sms->sent_by = null;
        }

        // Create SMS record
        if ($this->sms->create()) {
            $this->sms->id = $this->database->getConnection()->lastInsertId();
            return true;
        } else {
            $this->error = 'Failed to create SMS record.';
            return false;
        }
    }
    
    /**
     * Send SMS to recipients
     *
     * @param array $phone_numbers Array of phone numbers
     * @param string $message Message to send
     * @param array $recipients Array of recipient data for personalization
     * @return array Results of SMS sending
     */
    public function sendSmsToRecipients($phone_numbers, $message, $recipients) {
        // Get settings for SMS API
        $settings = $this->setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';
        $sender_id = $settings['sms_sender_id'] ?? 'ICGC';
        
        $success_count = 0;
        $failed_count = 0;
        $results = [];
        
        // Get all members for placeholder replacement
        $members_data = [];
        foreach ($recipients as $recipient_id) {
            // Check if recipient_id is a member ID or a phone number
            if (strpos($recipient_id, 'phone_') === 0) {
                // It's a phone number, not a member ID
                $phoneNumber = substr($recipient_id, 6); // Remove 'phone_' prefix
                $members_data[$phoneNumber] = ['phone_number' => $phoneNumber];
            } else {
                // Try to get member by ID
                $memberObj = new Member($this->database->getConnection());
                if ($memberObj->getById($recipient_id)) {
                    $members_data[$memberObj->phone_number] = [
                        'id' => $memberObj->id,
                        'first_name' => $memberObj->first_name,
                        'last_name' => $memberObj->last_name,
                        'phone_number' => $memberObj->phone_number,
                        'email' => $memberObj->email
                    ];
                }
            }
        }
        
        // Process each phone number
        foreach ($phone_numbers as $phone) {
            // Skip empty phone numbers
            if (empty($phone)) {
                continue;
            }
            
            // Personalize message for this recipient
            $personalized_message = $message;
            if (isset($members_data[$phone])) {
                $personalized_message = replace_sms_placeholders($personalized_message, $members_data[$phone]);
            }
            
            // Send SMS using primary method
            $result = send_single_sms($phone, $personalized_message, $sender_id, $api_key);
            $result['phone'] = $phone;
            
            // If primary method failed with 405 error, try alternative endpoint
            if (!$result['status'] &&
                isset($result['details']['http_code']) &&
                $result['details']['http_code'] == 405) {
                
                error_log("Primary SMS method failed with 405 error, trying alternative endpoint for {$phone}");
                $alt_result = try_alternative_endpoint($phone, $personalized_message, $sender_id, $api_key);
                $alt_result['phone'] = $phone;
                
                // Use alternative result if it was successful
                if ($alt_result['status']) {
                    $result = $alt_result;
                    error_log("Alternative SMS endpoint succeeded for {$phone}");
                } else {
                    error_log("Alternative SMS endpoint also failed for {$phone}: {$alt_result['message']}");
                }
            }
            
            $results[] = $result;
            
            // Log the result for debugging
            error_log("SMS to {$phone}: " . ($result['status'] ? 'SUCCESS' : 'FAILED') .
                     " - " . $result['message'] .
                     " - HTTP Code: " . ($result['details']['http_code'] ?? 'N/A'));
            
            // Consider it a success if either:
            // 1. The result status is true
            // 2. The HTTP code is 200 (success)
            // 3. The message indicates success
            if ($result['status'] ||
                (isset($result['details']['http_code']) && $result['details']['http_code'] == 200) ||
                (isset($result['message']) && strpos(strtolower($result['message']), 'success') !== false)) {
                $success_count++;
                error_log("SMS to {$phone} marked as successful");
            } else {
                $failed_count++;
                error_log("SMS to {$phone} marked as failed");
            }
        }
        
        return [
            'results' => $results,
            'success_count' => $success_count,
            'failed_count' => $failed_count
        ];
    }
    
    /**
     * Update SMS status based on sending results
     *
     * @param int $success_count Number of successful sends
     * @param int $failed_count Number of failed sends
     * @return bool True if status updated successfully
     */
    public function updateSmsStatus($success_count, $failed_count) {
        // Determine the appropriate status based on results
        if ($success_count > 0) {
            if ($failed_count > 0) {
                $new_status = 'partial';
            } else {
                $new_status = 'sent';
            }
        } else {
            $new_status = 'failed';
        }
        
        error_log("SMS ID: {$this->sms->id} - Success count: {$success_count}, Failed count: {$failed_count}, New status: {$new_status}");
        
        // Try multiple methods to ensure the status is updated correctly
        try {
            // Method 1: Direct SQL update (most reliable)
            $direct_update_query = "UPDATE sms_messages SET status = :status, updated_at = NOW() WHERE id = :id";
            $direct_update_stmt = $this->database->getConnection()->prepare($direct_update_query);
            $direct_update_stmt->bindParam(':status', $new_status);
            $direct_update_stmt->bindParam(':id', $this->sms->id);
            $direct_update_result = $direct_update_stmt->execute();
            
            error_log("SMS Service: Method 1 (Direct SQL) result: " . ($direct_update_result ? 'Success' : 'Failed'));
            
            // Verify the update was successful
            $verify_query = "SELECT status FROM sms_messages WHERE id = :id";
            $verify_stmt = $this->database->getConnection()->prepare($verify_query);
            $verify_stmt->bindParam(':id', $this->sms->id);
            $verify_stmt->execute();
            $current_status = $verify_stmt->fetchColumn();
            
            error_log("SMS Service: Status verification: Current status is '{$current_status}'");
            
            if ($current_status === $new_status) {
                error_log("SMS Service: Status successfully updated to '{$new_status}'");
                $this->sms->status = $new_status;
                return true;
            } else {
                // If Method 1 failed, try Method 2
                error_log("SMS Service: Method 1 verification failed, trying Method 2...");
                
                // Method 2: Use model's updateStatus method
                $this->sms->status = $new_status;
                $update_status_result = $this->sms->updateStatus($new_status);
                error_log("SMS Service: Method 2 (Model updateStatus) result: " . ($update_status_result ? 'Success' : 'Failed'));
                
                return $update_status_result;
            }
            
        } catch (Exception $e) {
            error_log("SMS Service: Exception during status update: {$e->getMessage()}");
            $this->error = 'Failed to update SMS status: ' . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Send bulk SMS - Complete SMS sending process
     *
     * @param string $message SMS message content
     * @param array $postData Form data containing recipient selection
     * @param int $userId User ID of sender
     * @return array Result with success status and message
     */
    public function sendBulkSms($message, $postData, $userId) {
        try {
            // Validate SMS request
            if (!$this->validateSmsRequest(['message' => $message] + $postData)) {
                return [
                    'success' => false,
                    'message' => $this->error
                ];
            }

            // Process recipients
            $recipientData = $this->processRecipients($postData);
            $recipients = $recipientData['recipients'];
            $phone_numbers = $recipientData['phone_numbers'];

            // Check if we have any recipients
            if (empty($phone_numbers)) {
                return [
                    'success' => false,
                    'message' => 'No recipients with valid phone numbers found. Please select at least one recipient with a valid phone number.'
                ];
            }

            // Create SMS record
            $smsData = [
                'message' => $message,
                'recipients' => implode(',', $recipients),
                'user_id' => $userId
            ];

            if (!$this->createSmsRecord($smsData)) {
                return [
                    'success' => false,
                    'message' => $this->error ?? 'Failed to create SMS record'
                ];
            }

            // Send SMS to recipients
            $sendResults = $this->sendSmsToRecipients($phone_numbers, $message, $recipients);
            $success_count = $sendResults['success_count'];
            $failed_count = $sendResults['failed_count'];

            // Update SMS status
            $this->updateSmsStatus($success_count, $failed_count);

            // Return appropriate result
            if ($success_count > 0) {
                if ($failed_count > 0) {
                    return [
                        'success' => true,
                        'message' => "SMS sent to {$success_count} recipients. Failed: {$failed_count}",
                        'type' => 'warning'
                    ];
                } else {
                    return [
                        'success' => true,
                        'message' => "SMS sent successfully to {$success_count} recipients"
                    ];
                }
            } else {
                // Check for specific error patterns
                $results = $sendResults['results'];
                $http_codes = [];
                foreach ($results as $result) {
                    if (isset($result['details']['http_code'])) {
                        $http_codes[] = $result['details']['http_code'];
                    }
                }

                if (in_array(200, $http_codes)) {
                    return [
                        'success' => true,
                        'message' => "SMS might have been sent successfully. Please check if the message was received.",
                        'type' => 'warning'
                    ];
                } elseif (in_array(405, $http_codes)) {
                    return [
                        'success' => false,
                        'message' => 'SMS API returned Method Not Allowed (405). The API endpoint may have changed. Please check the Arkesel documentation.'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Failed to send SMS to any recipients. Please check your SMS configuration.'
                    ];
                }
            }

        } catch (Exception $e) {
            error_log("SmsService::sendBulkSms Exception: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while sending SMS: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get SMS object
     *
     * @return Sms SMS object
     */
    public function getSms() {
        return $this->sms;
    }
}
