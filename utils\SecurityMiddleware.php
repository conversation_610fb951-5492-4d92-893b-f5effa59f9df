<?php
/**
 * Security Middleware System
 * 
 * Provides a structured approach to applying security checks to routes.
 * Supports multiple middleware types that can be chained together.
 */

require_once __DIR__ . '/csrf.php';
require_once __DIR__ . '/../config/database.php';

class SecurityMiddleware {
    private static $middlewares = [];
    
    /**
     * Register a middleware
     * 
     * @param string $name Middleware name
     * @param callable $handler Middleware handler function
     */
    public static function register($name, $handler) {
        self::$middlewares[$name] = $handler;
    }
    
    /**
     * Apply middleware to a request
     * 
     * @param array $middlewareList List of middleware names to apply
     * @param array $options Options for middleware
     * @return bool True if all middleware passed, false otherwise
     */
    public static function apply($middlewareList, $options = []) {
        foreach ($middlewareList as $middleware) {
            if (isset(self::$middlewares[$middleware])) {
                $result = call_user_func(self::$middlewares[$middleware], $options);
                if (!$result) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * Initialize default middleware
     */
    public static function init() {
        // CSRF Protection Middleware
        self::register('csrf', function($options) {
            if (in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT', 'PATCH', 'DELETE'])) {
                $token = $_POST['csrf_token'] ?? '';
                if (!CSRFProtection::validateToken($token)) {
                    self::handleSecurityFailure('Invalid CSRF token');
                    return false;
                }
            }
            return true;
        });
        
        // Role-based Access Control Middleware
        self::register('role', function($options) {
            $requiredRole = $options['role'] ?? null;
            if (!$requiredRole) {
                return true;
            }
            
            if (!isset($_SESSION['user_role'])) {
                self::handleSecurityFailure('Authentication required');
                return false;
            }
            
            $userRole = $_SESSION['user_role'];
            
            // Admin has access to everything
            if ($userRole === 'admin') {
                return true;
            }
            
            // Define role hierarchy
            $roleHierarchy = [
                'staff' => 1,
                'admin' => 2
            ];
            
            $userLevel = $roleHierarchy[$userRole] ?? 0;
            $requiredLevel = $roleHierarchy[$requiredRole] ?? 0;
            
            if ($userLevel < $requiredLevel) {
                self::handleSecurityFailure('Insufficient permissions');
                return false;
            }
            
            return true;
        });
        
        // Rate Limiting Middleware
        self::register('rate_limit', function($options) {
            $limit = $options['limit'] ?? 60; // requests per minute
            $window = $options['window'] ?? 60; // seconds
            
            $key = 'rate_limit_' . ($_SESSION['user_id'] ?? $_SERVER['REMOTE_ADDR']);
            $current_time = time();
            
            if (!isset($_SESSION[$key])) {
                $_SESSION[$key] = ['count' => 0, 'start_time' => $current_time];
            }
            
            $rate_data = $_SESSION[$key];
            
            // Reset if window has passed
            if ($current_time - $rate_data['start_time'] > $window) {
                $_SESSION[$key] = ['count' => 1, 'start_time' => $current_time];
                return true;
            }
            
            // Check if limit exceeded
            if ($rate_data['count'] >= $limit) {
                self::handleSecurityFailure('Rate limit exceeded');
                return false;
            }
            
            // Increment counter
            $_SESSION[$key]['count']++;
            return true;
        });
        
        // Token Authentication Middleware (for QR routes)
        self::register('token_auth', function($options) {
            $token = $_GET['token'] ?? $_POST['token'] ?? null;
            
            if (!$token) {
                self::handleSecurityFailure('Missing authentication token');
                return false;
            }
            
            try {
                require_once dirname(__DIR__) . '/models/AttendanceQrSession.php';
                $database = new Database();
                $qr_session = new AttendanceQrSession($database->getConnection());
                
                if (!$qr_session->getByToken($token)) {
                    self::handleSecurityFailure('Invalid authentication token');
                    return false;
                }
                
                // Check if session is active and not expired
                if ($qr_session->status !== 'active') {
                    self::handleSecurityFailure('Authentication session is not active');
                    return false;
                }
                
                if (strtotime($qr_session->expires_at) < time()) {
                    // Mark session as expired
                    $qr_session->status = 'expired';
                    $qr_session->updateStatus();
                    self::handleSecurityFailure('Authentication session has expired');
                    return false;
                }
                
                // Store validated session data
                $_SESSION['validated_qr_session'] = [
                    'id' => $qr_session->id,
                    'service_id' => $qr_session->service_id,
                    'attendance_date' => $qr_session->attendance_date,
                    'token' => $qr_session->token,
                    'expires_at' => $qr_session->expires_at
                ];
                
                return true;
                
            } catch (Exception $e) {
                error_log("Token validation error: " . $e->getMessage());
                self::handleSecurityFailure('Authentication system error');
                return false;
            }
        });
        
        // Input Sanitization Middleware
        self::register('sanitize', function($options) {
            // Sanitize POST data
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST)) {
                $_POST = self::sanitizeArray($_POST);
            }
            
            // Sanitize GET data
            if (!empty($_GET)) {
                $_GET = self::sanitizeArray($_GET);
            }
            
            return true;
        });
        
        // Audit Logging Middleware
        self::register('audit', function($options) {
            $action = $options['action'] ?? $_SERVER['REQUEST_URI'];
            $user_id = $_SESSION['user_id'] ?? null;
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            
            error_log("AUDIT: User {$user_id} from {$ip_address} accessed {$action}");
            
            return true;
        });
    }
    
    /**
     * Sanitize an array recursively
     * 
     * @param array $data Data to sanitize
     * @return array Sanitized data
     */
    private static function sanitizeArray($data) {
        $sanitized = [];
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = self::sanitizeArray($value);
            } else {
                $sanitized[$key] = htmlspecialchars(strip_tags($value), ENT_QUOTES, 'UTF-8');
            }
        }
        return $sanitized;
    }
    
    /**
     * Handle security failures
     * 
     * @param string $message Error message
     */
    private static function handleSecurityFailure($message) {
        if (self::isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'error' => $message]);
            exit;
        } else {
            set_flash_message($message, 'danger');
            redirect($_SERVER['HTTP_REFERER'] ?? 'dashboard');
            exit;
        }
    }
    
    /**
     * Check if request is AJAX
     * 
     * @return bool Whether request is AJAX
     */
    private static function isAjaxRequest() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}

// Initialize default middleware
SecurityMiddleware::init();
