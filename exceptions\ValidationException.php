<?php
/**
 * Validation Exception
 * 
 * Exception class for validation errors.
 * 
 * @package Exceptions
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

require_once 'exceptions/BaseException.php';

class ValidationException extends BaseException
{
    /**
     * @var array Validation errors
     */
    private $validationErrors = [];

    /**
     * ValidationException constructor
     * 
     * @param string $message Error message
     * @param array $validationErrors Specific validation errors
     * @param int $code Error code
     * @param Exception|null $previous Previous exception
     */
    public function __construct(string $message = "", array $validationErrors = [], int $code = 0, Exception $previous = null)
    {
        $this->validationErrors = $validationErrors;
        
        // If validation errors are provided, include them in the message
        if (!empty($validationErrors)) {
            $message .= ' Errors: ' . implode(', ', $validationErrors);
        }
        
        parent::__construct($message, $code, $previous, ['validation_errors' => $validationErrors]);
    }

    /**
     * Get validation errors
     * 
     * @return array Validation errors
     */
    public function getValidationErrors(): array
    {
        return $this->validationErrors;
    }

    /**
     * Get user-friendly error message
     * 
     * @return string User-friendly error message
     */
    public function getUserMessage(): string
    {
        if (!empty($this->validationErrors)) {
            return "Please correct the following errors: " . implode(', ', $this->validationErrors);
        }
        
        return "The submitted data is invalid. Please check your input and try again.";
    }

    /**
     * Convert to array with validation errors
     * 
     * @return array Exception data with validation errors
     */
    public function toArray(): array
    {
        $data = parent::toArray();
        $data['validation_errors'] = $this->getValidationErrors();
        return $data;
    }
}
