<?php
/**
 * Edit Category View
 * Form to edit an existing program category
 */
?>

<div class="max-w-4xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center mb-6">
        <a href="<?php echo BASE_URL; ?>categories/<?php echo $category['id']; ?>" class="text-gray-600 hover:text-gray-800 mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Edit Category</h1>
            <p class="text-gray-600 mt-1">Update category information</p>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form action="<?php echo BASE_URL; ?>categories/<?php echo $category['id']; ?>" method="POST" class="space-y-6">
            <!-- CSRF Token -->
            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
            <!-- Method Override for PUT -->
            <input type="hidden" name="_method" value="PUT">

            <!-- Category Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Name <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       required
                       maxlength="100"
                       value="<?php echo htmlspecialchars($category['name']); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                       placeholder="Enter category name">
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          maxlength="500"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                          placeholder="Enter category description (optional)"><?php echo htmlspecialchars($category['description'] ?? ''); ?></textarea>
            </div>

            <!-- Color and Icon Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Color Code -->
                <div>
                    <label for="color_code" class="block text-sm font-medium text-gray-700 mb-2">
                        Color
                    </label>
                    <div class="flex items-center space-x-3">
                        <input type="color" 
                               id="color_code" 
                               name="color_code" 
                               value="<?php echo htmlspecialchars($category['color_code'] ?? '#10B981'); ?>"
                               class="w-12 h-10 border border-gray-300 rounded-md cursor-pointer">
                        <input type="text" 
                               id="color_text" 
                               value="<?php echo htmlspecialchars($category['color_code'] ?? '#10B981'); ?>"
                               readonly
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-600">
                    </div>
                </div>

                <!-- Icon -->
                <div>
                    <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
                        Icon
                    </label>
                    <div class="flex items-center space-x-3">
                        <div id="icon-preview" class="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center">
                            <i class="<?php echo htmlspecialchars($category['icon'] ?? 'fas fa-folder'); ?> text-gray-500"></i>
                        </div>
                        <select id="icon" 
                                name="icon" 
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500">
                            <option value="fas fa-folder" <?php echo ($category['icon'] ?? '') === 'fas fa-folder' ? 'selected' : ''; ?>>📁 Folder</option>
                            <option value="fas fa-church" <?php echo ($category['icon'] ?? '') === 'fas fa-church' ? 'selected' : ''; ?>>⛪ Church</option>
                            <option value="fas fa-users" <?php echo ($category['icon'] ?? '') === 'fas fa-users' ? 'selected' : ''; ?>>👥 Users</option>
                            <option value="fas fa-heart" <?php echo ($category['icon'] ?? '') === 'fas fa-heart' ? 'selected' : ''; ?>>❤️ Heart</option>
                            <option value="fas fa-music" <?php echo ($category['icon'] ?? '') === 'fas fa-music' ? 'selected' : ''; ?>>🎵 Music</option>
                            <option value="fas fa-book" <?php echo ($category['icon'] ?? '') === 'fas fa-book' ? 'selected' : ''; ?>>📖 Book</option>
                            <option value="fas fa-pray" <?php echo ($category['icon'] ?? '') === 'fas fa-pray' ? 'selected' : ''; ?>>🙏 Pray</option>
                            <option value="fas fa-cross" <?php echo ($category['icon'] ?? '') === 'fas fa-cross' ? 'selected' : ''; ?>>✝️ Cross</option>
                            <option value="fas fa-star" <?php echo ($category['icon'] ?? '') === 'fas fa-star' ? 'selected' : ''; ?>>⭐ Star</option>
                            <option value="fas fa-calendar" <?php echo ($category['icon'] ?? '') === 'fas fa-calendar' ? 'selected' : ''; ?>>📅 Calendar</option>
                            <option value="fas fa-microphone" <?php echo ($category['icon'] ?? '') === 'fas fa-microphone' ? 'selected' : ''; ?>>🎤 Microphone</option>
                            <option value="fas fa-graduation-cap" <?php echo ($category['icon'] ?? '') === 'fas fa-graduation-cap' ? 'selected' : ''; ?>>🎓 Education</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Status -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Status
                </label>
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_active" 
                           name="is_active" 
                           value="1" 
                           <?php echo $category['is_active'] ? 'checked' : ''; ?>
                           class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 block text-sm text-gray-700">
                        Active (category will be available for use)
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="<?php echo BASE_URL; ?>categories/<?php echo $category['id']; ?>" 
                   class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Update Category
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Update color text input when color picker changes
document.getElementById('color_code').addEventListener('change', function() {
    document.getElementById('color_text').value = this.value;
    updateIconPreview();
});

// Update icon preview when icon changes
document.getElementById('icon').addEventListener('change', function() {
    updateIconPreview();
});

// Function to update icon preview
function updateIconPreview() {
    const iconSelect = document.getElementById('icon');
    const colorInput = document.getElementById('color_code');
    const preview = document.getElementById('icon-preview');
    
    const iconClass = iconSelect.value;
    const color = colorInput.value;
    
    preview.innerHTML = `<i class="${iconClass}" style="color: ${color};"></i>`;
    preview.style.backgroundColor = color + '20'; // Add transparency
}

// Initialize preview
updateIconPreview();
</script>
