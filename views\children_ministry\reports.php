<?php
/**
 * Children's Ministry Reports View
 */

// Ensure we have the required data
if (!isset($attendance_stats)) {
    $attendance_stats = [];
}

if (!isset($daily_attendance)) {
    $daily_attendance = [];
}

// Set default date range if not provided
$start_date = $start_date ?? date('Y-m-01');
$end_date = $end_date ?? date('Y-m-d');
?>

<div class="container mx-auto fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-100">
        <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 p-8 text-white relative overflow-hidden">
            <div class="absolute right-0 top-0 opacity-10">
                <i class="fas fa-chart-bar text-9xl transform -rotate-12 translate-x-8 -translate-y-8"></i>
            </div>
            <h1 class="text-3xl font-bold">Attendance Reports</h1>
            <p class="mt-2 opacity-90 max-w-2xl">Comprehensive attendance analytics and reporting for children's ministry activities.</p>
        </div>
        <div class="p-6 bg-white">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-3 mb-4 md:mb-0">
                    <a href="<?php echo BASE_URL; ?>children-ministry" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 px-5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-md font-medium">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                    </a>
                    <button onclick="exportReport()" class="bg-gradient-to-r from-primary to-primary-dark text-white py-3 px-5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-md font-medium">
                        <i class="fas fa-download mr-2"></i> Export Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Report Filters</h3>
        
        <form method="GET" action="<?php echo BASE_URL; ?>children-ministry/reports" class="flex flex-wrap gap-4 items-end">
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input type="date" id="start_date" name="start_date" value="<?php echo htmlspecialchars($start_date); ?>"
                       class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
            </div>
            
            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input type="date" id="end_date" name="end_date" value="<?php echo htmlspecialchars($end_date); ?>"
                       class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
            </div>
            
            <div class="flex space-x-2">
                <button type="submit" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-md flex items-center">
                    <i class="fas fa-search mr-2"></i>
                    Generate Report
                </button>
                <button type="button" onclick="setQuickDateRange('today')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-md text-sm">
                    Today
                </button>
                <button type="button" onclick="setQuickDateRange('week')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-md text-sm">
                    This Week
                </button>
                <button type="button" onclick="setQuickDateRange('month')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-md text-sm">
                    This Month
                </button>
            </div>
        </form>
    </div>

    <!-- Attendance Statistics -->
    <?php if (!empty($attendance_stats)): ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-primary">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-light text-white mr-4">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Unique Children</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($attendance_stats['unique_children'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-500 text-white mr-4">
                        <i class="fas fa-sign-in-alt text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Check-ins</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($attendance_stats['total_checkins'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-500 text-white mr-4">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Completed Visits</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo number_format($attendance_stats['completed_visits'] ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-500 text-white mr-4">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Avg Duration</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php 
                            $avg_duration = $attendance_stats['avg_duration_minutes'] ?? 0;
                            $hours = floor($avg_duration / 60);
                            $minutes = $avg_duration % 60;
                            echo $hours > 0 ? "{$hours}h {$minutes}m" : "{$minutes}m";
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Daily Attendance Report -->
    <?php if (!empty($daily_attendance) && $start_date === $end_date): ?>
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                Daily Attendance Report - <?php echo date('F j, Y', strtotime($start_date)); ?>
            </h3>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Child</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-in</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-out</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Checked In By</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($daily_attendance as $record): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <?php if ($record['profile_picture']): ?>
                                            <img src="<?php echo BASE_URL . $record['profile_picture']; ?>" alt="Profile" class="w-8 h-8 rounded-full mr-3">
                                        <?php else: ?>
                                            <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                                                <i class="fas fa-child text-gray-600 text-sm"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($record['first_name'] . ' ' . $record['last_name']); ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $record['age']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($record['service_name']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('g:i A', strtotime($record['check_in_time'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $record['check_out_time'] ? date('g:i A', strtotime($record['check_out_time'])) : '-'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($record['duration']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($record['checked_in_by_first_name'] . ' ' . $record['checked_in_by_last_name']); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>

    <!-- Summary Charts Placeholder -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Attendance Trends -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Attendance Trends</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div class="text-center text-gray-500">
                    <i class="fas fa-chart-line text-4xl mb-2"></i>
                    <p>Attendance trend chart would be displayed here</p>
                    <p class="text-sm">Integration with Chart.js or similar library needed</p>
                </div>
            </div>
        </div>

        <!-- Age Group Distribution -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Age Group Distribution</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div class="text-center text-gray-500">
                    <i class="fas fa-chart-pie text-4xl mb-2"></i>
                    <p>Age group distribution chart would be displayed here</p>
                    <p class="text-sm">Integration with Chart.js or similar library needed</p>
                </div>
            </div>
        </div>
    </div>

    <!-- No Data Message -->
    <?php if (empty($attendance_stats) || ($attendance_stats['total_checkins'] ?? 0) == 0): ?>
        <div class="bg-white rounded-lg shadow-md p-12 text-center">
            <div class="text-gray-400 mb-4">
                <i class="fas fa-chart-bar text-6xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">No Attendance Data</h3>
            <p class="text-gray-500 mb-4">
                No attendance records found for the selected date range.
                <?php if ($start_date === $end_date): ?>
                    Try selecting a different date or date range.
                <?php else: ?>
                    Try selecting a different date range.
                <?php endif; ?>
            </p>
            <div class="flex justify-center space-x-4">
                <a href="<?php echo BASE_URL; ?>children-ministry/checkin" class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-lg inline-flex items-center">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Start Check-in
                </a>
                <button onclick="setQuickDateRange('month')" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg inline-flex items-center">
                    <i class="fas fa-calendar mr-2"></i>
                    View This Month
                </button>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function setQuickDateRange(range) {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const today = new Date();
    
    switch (range) {
        case 'today':
            const todayStr = today.toISOString().split('T')[0];
            startDateInput.value = todayStr;
            endDateInput.value = todayStr;
            break;
            
        case 'week':
            const startOfWeek = new Date(today);
            startOfWeek.setDate(today.getDate() - today.getDay());
            const endOfWeek = new Date(startOfWeek);
            endOfWeek.setDate(startOfWeek.getDate() + 6);
            
            startDateInput.value = startOfWeek.toISOString().split('T')[0];
            endDateInput.value = endOfWeek.toISOString().split('T')[0];
            break;
            
        case 'month':
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            startDateInput.value = startOfMonth.toISOString().split('T')[0];
            endDateInput.value = endOfMonth.toISOString().split('T')[0];
            break;
    }
    
    // Auto-submit the form
    document.querySelector('form').submit();
}

function exportReport() {
    // In a real implementation, this would generate and download a report
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    alert(`Export functionality would be implemented here.\nDate range: ${startDate} to ${endDate}`);
    
    // Example: window.open(`${BASE_URL}children-ministry/export-report?start_date=${startDate}&end_date=${endDate}`);
}

// Validate date range
document.getElementById('start_date').addEventListener('change', function() {
    const endDateInput = document.getElementById('end_date');
    if (endDateInput.value && this.value > endDateInput.value) {
        endDateInput.value = this.value;
    }
});

document.getElementById('end_date').addEventListener('change', function() {
    const startDateInput = document.getElementById('start_date');
    if (startDateInput.value && this.value < startDateInput.value) {
        startDateInput.value = this.value;
    }
});
</script>
