<?php
$page_title = "Delete Payment Confirmation";
$active_page = "welfare";
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Delete Payment Confirmation</h1>
            <p class="text-gray-600 mt-1">Are you sure you want to delete this payment?</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>welfare"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Welfare
            </a>
        </div>
    </div>

    <!-- Confirmation Card -->
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200 bg-red-50">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-lg font-semibold text-red-900">Confirm Payment Deletion</h2>
                        <p class="text-sm text-red-700">This action cannot be undone.</p>
                    </div>
                </div>
            </div>
            
            <div class="p-6">
                <!-- Payment Details -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Member Name</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']); ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Amount</label>
                            <p class="mt-1 text-sm text-gray-900 font-semibold">
                                ₵<?php echo number_format($payment['amount'], 2); ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Payment Date</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo date('F d, Y', strtotime($payment['payment_date'])); ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Payment Method</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo ucfirst(str_replace('_', ' ', $payment['payment_method'])); ?>
                            </p>
                        </div>
                        <?php if (!empty($payment['reference_number'])): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Reference Number</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo htmlspecialchars($payment['reference_number']); ?>
                            </p>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($payment['notes'])): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Notes</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo htmlspecialchars($payment['notes']); ?>
                            </p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Warning Message -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>Deleting this payment will:</p>
                                <ul class="list-disc list-inside mt-1">
                                    <li>Permanently remove the payment record</li>
                                    <li>Update the member's payment history</li>
                                    <li>Affect welfare statistics and reports</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3">
                    <a href="<?php echo BASE_URL; ?>welfare"
                       class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                        Cancel
                    </a>
                    <form method="POST" class="inline">
                        <input type="hidden" name="payment_id" value="<?php echo $payment['id']; ?>">
                        <button type="submit"
                                class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                                onclick="return confirm('Are you absolutely sure you want to delete this payment? This action cannot be undone.')">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Payment
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
