<?php
// Convert attendances to array format for easier use in the view
$attendances_array = [];
foreach ($attendances as $attendance) {
    $attendances_array[] = [
        'id' => $attendance->id,
        'service_id' => $attendance->service_id,
        'service_name' => $attendance->service_name ?? 'Unknown Service',
        'member_id' => $attendance->member_id,
        'member_name' => $attendance->member_name ?? null,
        'status' => $attendance->status,
        'attendance_date' => $attendance->attendance_date,
        'created_at' => $attendance->created_at,
        'updated_at' => $attendance->updated_at
    ];
}

// Convert services to array format
$services_array = [];
foreach ($services as $service) {
    $services_array[] = [
        'id' => $service->id,
        'name' => $service->name,
        'day_of_week' => $service->day_of_week,
        'time' => $service->time
    ];
}
?>

<div class="container mx-auto px-4 max-w-6xl">
    <!-- Modern Header Section -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-5">
        <div class="bg-gradient-to-r from-primary to-primary-dark p-4 text-white">
            <div class="flex flex-row justify-between items-center">
                <div>
                    <h1 class="text-xl font-bold">All Attendance Records</h1>
                    <p class="text-sm opacity-90 mt-1">View and manage all attendance records</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-1.5 px-3 rounded-md flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-arrow-left mr-1.5"></i> Back
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/bulk" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-1.5 px-3 rounded-md flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-plus-circle mr-1.5"></i> Mark Attendance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-lg shadow-sm p-3 mb-5">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="p-1.5 rounded-full bg-blue-100 text-blue-600 mr-2">
                    <i class="fas fa-filter text-sm"></i>
                </div>
                <span class="text-sm font-medium text-gray-700">Filter Records</span>
            </div>
            <div class="flex items-center space-x-2">
                <select id="service-filter" class="rounded-md border border-gray-300 text-sm py-1.5 px-2 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">All Services</option>
                    <?php foreach ($services_array as $service) : ?>
                        <option value="<?php echo $service['id']; ?>"><?php echo $service['name']; ?></option>
                    <?php endforeach; ?>
                </select>
                <input type="date" id="date-filter" class="rounded-md border border-gray-300 text-sm py-1.5 px-2 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                <select id="status-filter" class="rounded-md border border-gray-300 text-sm py-1.5 px-2 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">All Statuses</option>
                    <option value="present">Present</option>
                    <option value="absent">Absent</option>
                    <option value="late">Late</option>
                </select>
                <button id="apply-filter" class="bg-primary hover:bg-primary-dark text-white py-1.5 px-3 rounded-md text-xs flex items-center">
                    <i class="fas fa-filter mr-1.5"></i> Apply
                </button>
                <button id="reset-filter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-1.5 px-3 rounded-md text-xs flex items-center">
                    <i class="fas fa-redo-alt mr-1.5"></i> Reset
                </button>
            </div>
        </div>
    </div>

    <!-- Attendance Records Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-100">
            <div class="flex items-center">
                <div class="p-1.5 rounded-full bg-indigo-100 text-indigo-600 mr-2">
                    <i class="fas fa-clipboard-list text-sm"></i>
                </div>
                <h3 class="text-base font-medium text-gray-800">Attendance Records</h3>
            </div>
            <div class="text-xs text-gray-500">
                <?php echo $total_records; ?> total records | Page <?php echo $page; ?> of <?php echo $total_pages; ?>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-100">
                <thead>
                    <tr class="bg-gray-50">
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500">Date</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500">Service</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500">Member</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500">Status</th>
                        <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-100">
                    <?php if (empty($attendances_array)) : ?>
                        <tr>
                            <td colspan="5" class="px-4 py-8 text-center text-sm text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <div class="p-2 rounded-full bg-gray-100 text-gray-400 mb-2">
                                        <i class="fas fa-calendar-times"></i>
                                    </div>
                                    <p>No attendance records found</p>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ($attendances_array as $attendance) : ?>
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700"><?php echo format_date($attendance['attendance_date']); ?></td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700"><?php echo $attendance['service_name']; ?></td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700">
                                    <?php echo $attendance['member_name'] ? $attendance['member_name'] : '<span class="text-gray-500">General Attendance</span>'; ?>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <?php if ($attendance['status'] === 'present') : ?>
                                        <span class="px-2 py-0.5 inline-flex text-xs font-medium rounded-full bg-green-100 text-green-800">Present</span>
                                    <?php elseif ($attendance['status'] === 'absent') : ?>
                                        <span class="px-2 py-0.5 inline-flex text-xs font-medium rounded-full bg-red-100 text-red-800">Absent</span>
                                    <?php else : ?>
                                        <span class="px-2 py-0.5 inline-flex text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Late</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-right">
                                    <a href="<?php echo BASE_URL; ?>attendance/view-by-date?date=<?php echo $attendance['attendance_date']; ?>" class="text-blue-600 hover:text-blue-900 mr-3 p-1 rounded-full hover:bg-blue-50 inline-flex items-center justify-center" title="View Date">
                                        <i class="fas fa-calendar-day"></i>
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>attendance/edit?id=<?php echo $attendance['id']; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3 p-1 rounded-full hover:bg-indigo-50 inline-flex items-center justify-center" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $attendance['id']; ?>)" class="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 inline-flex items-center justify-center" title="Delete">
                                        <i class="fas fa-trash-alt"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1) : ?>
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                <?php if ($page > 1) : ?>
                    <a href="<?php echo BASE_URL; ?>attendance/view-all?page=<?php echo $page - 1; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                <?php endif; ?>
                <?php if ($page < $total_pages) : ?>
                    <a href="<?php echo BASE_URL; ?>attendance/view-all?page=<?php echo $page + 1; ?>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                <?php endif; ?>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium"><?php echo ($page - 1) * $limit + 1; ?></span> to <span class="font-medium"><?php echo min($page * $limit, $total_records); ?></span> of <span class="font-medium"><?php echo $total_records; ?></span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <?php if ($page > 1) : ?>
                            <a href="<?php echo BASE_URL; ?>attendance/view-all?page=<?php echo $page - 1; ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Previous</span>
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                        
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);
                        
                        if ($start_page > 1) : ?>
                            <a href="<?php echo BASE_URL; ?>attendance/view-all?page=1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                1
                            </a>
                            <?php if ($start_page > 2) : ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php for ($i = $start_page; $i <= $end_page; $i++) : ?>
                            <a href="<?php echo BASE_URL; ?>attendance/view-all?page=<?php echo $i; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 <?php echo $i === $page ? 'bg-primary text-white' : 'bg-white text-gray-700 hover:bg-gray-50'; ?> text-sm font-medium">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($end_page < $total_pages) : ?>
                            <?php if ($end_page < $total_pages - 1) : ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            <?php endif; ?>
                            <a href="<?php echo BASE_URL; ?>attendance/view-all?page=<?php echo $total_pages; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                <?php echo $total_pages; ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($page < $total_pages) : ?>
                            <a href="<?php echo BASE_URL; ?>attendance/view-all?page=<?php echo $page + 1; ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Next</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal - Modern Design -->
<div id="deleteModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 backdrop-blur-sm">
    <div class="relative top-20 mx-auto p-4 border border-gray-200 w-80 shadow-lg rounded-lg bg-white">
        <div class="absolute top-2 right-2">
            <button id="closeModal" class="text-gray-400 hover:text-gray-500 focus:outline-none p-1">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mt-2 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
            </div>
            <h3 class="text-base font-semibold text-gray-900 mt-3">Delete Attendance Record</h3>
            <div class="mt-2 px-4 py-2">
                <p class="text-xs text-gray-500">Are you sure you want to delete this attendance record? This action cannot be undone.</p>
            </div>
            <div class="flex justify-center mt-3 space-x-2">
                <button id="cancelDelete" class="bg-gray-100 hover:bg-gray-200 px-4 py-1.5 rounded text-gray-700 text-xs transition-colors duration-200">
                    <i class="fas fa-times mr-1"></i> Cancel
                </button>
                <a id="confirmDelete" href="#" class="bg-red-500 hover:bg-red-600 px-4 py-1.5 rounded text-white text-xs transition-colors duration-200">
                    <i class="fas fa-trash-alt mr-1"></i> Delete
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter functionality
        const applyFilterBtn = document.getElementById('apply-filter');
        const resetFilterBtn = document.getElementById('reset-filter');
        
        if (applyFilterBtn) {
            applyFilterBtn.addEventListener('click', function() {
                const serviceId = document.getElementById('service-filter').value;
                const date = document.getElementById('date-filter').value;
                const status = document.getElementById('status-filter').value;
                
                let url = '<?php echo BASE_URL; ?>attendance/view-all?page=1';
                
                if (serviceId) {
                    url += '&service_id=' + serviceId;
                }
                
                if (date) {
                    url += '&date=' + date;
                }
                
                if (status) {
                    url += '&status=' + status;
                }
                
                window.location.href = url;
            });
        }
        
        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', function() {
                window.location.href = '<?php echo BASE_URL; ?>attendance/view-all';
            });
        }

        // Delete confirmation
        function confirmDelete(id) {
            const modal = document.getElementById('deleteModal');
            const confirmBtn = document.getElementById('confirmDelete');

            modal.classList.remove('hidden');
            confirmBtn.href = '<?php echo BASE_URL; ?>attendance/delete?id=' + id + '&redirect=view-all';

            // Close modal when cancel button is clicked
            document.getElementById('cancelDelete').addEventListener('click', function() {
                modal.classList.add('hidden');
            });
            
            // Close modal when X button is clicked
            document.getElementById('closeModal').addEventListener('click', function() {
                modal.classList.add('hidden');
            });
            
            // Close modal when clicking outside the modal
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        }

        // Make confirmDelete function available globally
        window.confirmDelete = confirmDelete;
        
        // Set filter values from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        
        if (urlParams.has('service_id')) {
            document.getElementById('service-filter').value = urlParams.get('service_id');
        }
        
        if (urlParams.has('date')) {
            document.getElementById('date-filter').value = urlParams.get('date');
        }
        
        if (urlParams.has('status')) {
            document.getElementById('status-filter').value = urlParams.get('status');
        }
    });
</script>
