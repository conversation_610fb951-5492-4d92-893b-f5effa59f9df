<div class="page-content-centered py-6 fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border-2 border-gray-100">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <h1 class="text-2xl font-bold"><?php echo $report['title']; ?></h1>
                <div class="flex space-x-2 mt-4 md:mt-0">
                    <a href="<?php echo BASE_URL; ?>reports" class="bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                    </a>
                </div>
            </div>
        </div>

        <!-- Report Info -->
        <div class="p-6">
            <div class="flex flex-col md:flex-row md:justify-between">
                <div class="flex items-start">
                    <div class="p-3 rounded-full bg-primary bg-opacity-10 text-primary mr-4">
                        <i class="fas fa-calendar-alt text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-800">Report Period</h2>
                        <p class="text-gray-600">
                            <?php echo format_date($report['start_date']); ?> to <?php echo format_date($report['end_date']); ?>
                        </p>
                    </div>
                </div>
                <div class="mt-4 md:mt-0 flex items-start">
                    <div class="p-3 rounded-full bg-primary bg-opacity-10 text-primary mr-4">
                        <i class="fas fa-info-circle text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-800">Report Info</h2>
                        <p class="text-gray-600">
                            Generated on: <?php echo date('F j, Y, g:i a'); ?><br>
                            Generated by: <?php echo function_exists('current_user') && current_user() ? current_user()['name'] : ($_SESSION['username'] ?? 'System User'); ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Export Options -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-md font-semibold text-gray-700 mb-3 flex items-center">
                    <i class="fas fa-file-export mr-2 text-primary"></i> Export Options
                </h3>
                <div class="flex flex-wrap gap-3">
                    <button onclick="printReport()" class="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                        <i class="fas fa-print mr-2 text-primary"></i> Print
                    </button>
                    <form action="<?php echo BASE_URL; ?>reports/generate" method="POST" class="inline">
                        <input type="hidden" name="report_type" value="<?php echo $report['type']; ?>">
                        <input type="hidden" name="start_date" value="<?php echo $report['start_date']; ?>">
                        <input type="hidden" name="end_date" value="<?php echo $report['end_date']; ?>">
                        <?php if (isset($_POST['department'])): ?>
                            <input type="hidden" name="department" value="<?php echo $_POST['department']; ?>">
                        <?php endif; ?>
                        <?php if (isset($_POST['service_id'])): ?>
                            <input type="hidden" name="service_id" value="<?php echo $_POST['service_id']; ?>">
                        <?php endif; ?>
                        <?php if (isset($_POST['category'])): ?>
                            <input type="hidden" name="category" value="<?php echo $_POST['category']; ?>">
                        <?php endif; ?>
                        <?php if (isset($_POST['group_id'])): ?>
                            <input type="hidden" name="group_id" value="<?php echo $_POST['group_id']; ?>">
                        <?php endif; ?>
                        <input type="hidden" name="export_format" value="pdf">
                        <button type="submit" class="bg-red-100 hover:bg-red-200 text-gray-800 py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                            <i class="fas fa-file-pdf mr-2 text-red-600"></i> PDF
                        </button>
                    </form>
                    <form action="<?php echo BASE_URL; ?>reports/generate" method="POST" class="inline">
                        <input type="hidden" name="report_type" value="<?php echo $report['type']; ?>">
                        <input type="hidden" name="start_date" value="<?php echo $report['start_date']; ?>">
                        <input type="hidden" name="end_date" value="<?php echo $report['end_date']; ?>">
                        <?php if (isset($_POST['department'])): ?>
                            <input type="hidden" name="department" value="<?php echo $_POST['department']; ?>">
                        <?php endif; ?>
                        <?php if (isset($_POST['service_id'])): ?>
                            <input type="hidden" name="service_id" value="<?php echo $_POST['service_id']; ?>">
                        <?php endif; ?>
                        <?php if (isset($_POST['category'])): ?>
                            <input type="hidden" name="category" value="<?php echo $_POST['category']; ?>">
                        <?php endif; ?>
                        <?php if (isset($_POST['group_id'])): ?>
                            <input type="hidden" name="group_id" value="<?php echo $_POST['group_id']; ?>">
                        <?php endif; ?>
                        <input type="hidden" name="export_format" value="excel">
                        <button type="submit" class="bg-green-100 hover:bg-green-200 text-gray-800 py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                            <i class="fas fa-file-excel mr-2 text-green-600"></i> Excel
                        </button>
                    </form>
                    <form action="<?php echo BASE_URL; ?>reports/generate" method="POST" class="inline">
                        <input type="hidden" name="report_type" value="<?php echo $report['type']; ?>">
                        <input type="hidden" name="start_date" value="<?php echo $report['start_date']; ?>">
                        <input type="hidden" name="end_date" value="<?php echo $report['end_date']; ?>">
                        <?php if (isset($_POST['department'])): ?>
                            <input type="hidden" name="department" value="<?php echo $_POST['department']; ?>">
                        <?php endif; ?>
                        <?php if (isset($_POST['service_id'])): ?>
                            <input type="hidden" name="service_id" value="<?php echo $_POST['service_id']; ?>">
                        <?php endif; ?>
                        <?php if (isset($_POST['category'])): ?>
                            <input type="hidden" name="category" value="<?php echo $_POST['category']; ?>">
                        <?php endif; ?>
                        <?php if (isset($_POST['group_id'])): ?>
                            <input type="hidden" name="group_id" value="<?php echo $_POST['group_id']; ?>">
                        <?php endif; ?>
                        <input type="hidden" name="export_format" value="csv">
                        <button type="submit" class="bg-blue-100 hover:bg-blue-200 text-gray-800 py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                            <i class="fas fa-file-csv mr-2 text-blue-600"></i> CSV
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Content -->
    <div id="report-content" class="bg-white rounded-xl shadow-md overflow-hidden border-2 border-gray-100">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-primary bg-opacity-10 text-primary mr-3">
                    <i class="fas fa-table text-xl"></i>
                </div>
                <h2 class="text-lg font-semibold text-gray-800">Report Data</h2>
            </div>
        </div>

        <?php if (empty($report['data'])) : ?>
            <div class="p-12 text-center">
                <div class="inline-flex rounded-full bg-yellow-100 p-4 mb-4">
                    <div class="rounded-full bg-yellow-200 p-4">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-3xl"></i>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">No Data Found</h3>
                <p class="text-gray-500 max-w-md mx-auto">There is no data available for the selected period and criteria. Try adjusting your filters or date range.</p>
                <a href="<?php echo BASE_URL; ?>reports" class="mt-6 inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-300">
                    <i class="fas fa-redo mr-2"></i> Try Another Report
                </a>
            </div>
        <?php else : ?>
            <?php if ($report['type'] === 'member') : ?>
                <!-- Member Report -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr class="bg-gray-50">
                                <?php foreach ($report['columns'] as $column) : ?>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?php echo $column; ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($report['data'] as $member) : ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo $member['id']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo $member['first_name'] . ' ' . $member['last_name']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $member['phone_number']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $member['email'] ?: 'N/A'; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo ucfirst(str_replace('_', ' ', $member['department'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo format_date($member['membership_date']); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Summary -->
                <div class="p-6 bg-gray-50 border-t border-gray-200">
                    <div class="flex items-center">
                        <div class="p-2 rounded-full bg-blue-100 text-blue-600 mr-3">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div>
                            <h3 class="text-md font-semibold text-gray-800">Report Summary</h3>
                            <p class="text-gray-600 mt-1">
                                <span class="font-medium">Total Members:</span> <?php echo count($report['data']); ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php elseif ($report['type'] === 'attendance') : ?>
                <!-- Attendance Report -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr class="bg-gray-50">
                                <?php foreach ($report['columns'] as $column) : ?>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?php echo $column; ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($report['data'] as $attendance) : ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo format_date($attendance['attendance_date']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $attendance['service_name']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $attendance['total_present']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $attendance['male_count']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $attendance['female_count']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $attendance['children_count']; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Summary -->
                <div class="p-6 bg-gray-50 border-t border-gray-200">
                    <div class="flex items-center">
                        <div class="p-2 rounded-full bg-green-100 text-green-600 mr-3">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div>
                            <h3 class="text-md font-semibold text-gray-800">Report Summary</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                                <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-200">
                                    <p class="text-xs text-gray-500 uppercase">Total Records</p>
                                    <p class="text-xl font-semibold text-gray-800"><?php echo count($report['data']); ?></p>
                                </div>
                                <?php
                                    $total_attendance = 0;
                                    foreach ($report['data'] as $attendance) {
                                        $total_attendance += $attendance['total_present'];
                                    }
                                ?>
                                <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-200">
                                    <p class="text-xs text-gray-500 uppercase">Total Attendance</p>
                                    <p class="text-xl font-semibold text-gray-800"><?php echo $total_attendance; ?></p>
                                </div>
                                <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-200">
                                    <p class="text-xs text-gray-500 uppercase">Average Attendance</p>
                                    <p class="text-xl font-semibold text-gray-800"><?php echo count($report['data']) > 0 ? round($total_attendance / count($report['data'])) : 0; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php elseif ($report['type'] === 'finance') : ?>
                <!-- Finance Report -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr class="bg-gray-50">
                                <?php foreach ($report['columns'] as $column) : ?>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?php echo $column; ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($report['data'] as $finance) : ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo format_date($finance['transaction_date']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($finance['category'] === 'tithe') : ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Tithe</span>
                                        <?php elseif ($finance['category'] === 'offering') : ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Offering</span>
                                        <?php elseif ($finance['category'] === 'project_offering') : ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Project Offering</span>
                                        <?php elseif ($finance['category'] === 'pledge') : ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800">Pledge</span>
                                        <?php elseif ($finance['category'] === 'seed') : ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Seed</span>
                                        <?php elseif ($finance['category'] === 'welfare') : ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-pink-100 text-pink-800">Welfare</span>
                                        <?php else : ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Expense</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm <?php echo $finance['type'] === 'expense' ? 'text-red-600' : 'text-green-600'; ?> font-medium">
                                        GH₵ <?php echo number_format($finance['amount'], 2); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        <?php echo !empty($finance['description']) ? $finance['description'] : 'N/A'; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $finance['type']; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Summary -->
                <div class="p-6 bg-gray-50 border-t border-gray-200">
                    <div class="flex items-center mb-4">
                        <div class="p-2 rounded-full bg-yellow-100 text-yellow-600 mr-3">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h3 class="text-md font-semibold text-gray-800">Financial Summary</h3>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 mr-4">
                                    <i class="fas fa-arrow-up text-green-600"></i>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500 uppercase">Total Income</p>
                                    <p class="text-xl font-semibold text-gray-800">GH₵ <?php echo number_format($report['summary']['total_income'], 2); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-red-100 mr-4">
                                    <i class="fas fa-arrow-down text-red-600"></i>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500 uppercase">Total Expenses</p>
                                    <p class="text-xl font-semibold text-gray-800">GH₵ <?php echo number_format($report['summary']['total_expenses'], 2); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full <?php echo $report['summary']['net'] >= 0 ? 'bg-green-100' : 'bg-red-100'; ?> mr-4">
                                    <i class="fas fa-balance-scale <?php echo $report['summary']['net'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>"></i>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500 uppercase">Net Balance</p>
                                    <p class="text-xl font-semibold <?php echo $report['summary']['net'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                        GH₵ <?php echo number_format($report['summary']['net'], 2); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Breakdown -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Category Breakdown</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                                <h5 class="text-sm font-medium text-gray-700 mb-3">Income by Category</h5>
                                <div class="h-64">
                                    <canvas id="income-category-chart"></canvas>
                                </div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                                <h5 class="text-sm font-medium text-gray-700 mb-3">Income vs Expenses</h5>
                                <div class="h-64">
                                    <canvas id="income-expense-chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize charts if finance report
        if ('<?php echo $report["type"]; ?>' === 'finance') {
            initializeFinanceCharts();
        }
    });

    // Print report with enhanced styling
    function printReport() {
        const printContents = document.getElementById('report-content').innerHTML;
        const originalContents = document.body.innerHTML;

        document.body.innerHTML = `
            <div style="padding: 20px; font-family: Arial, sans-serif;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #3F7D58; margin-bottom: 10px;"><?php echo $report['title']; ?></h1>
                    <p style="color: #666; font-size: 14px;">
                        Period: <?php echo format_date($report['start_date']); ?> to <?php echo format_date($report['end_date']); ?><br>
                        Generated on: <?php echo date('F j, Y, g:i a'); ?><br>
                        Generated by: <?php echo function_exists('current_user') && current_user() ? current_user()['name'] : ($_SESSION['username'] ?? 'System User'); ?>
                    </p>
                </div>
                <div style="border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;">
                    <div style="background-color: #f7fafc; padding: 15px; border-bottom: 1px solid #e2e8f0;">
                        <h2 style="margin: 0; color: #2d3748; font-size: 18px;">Report Data</h2>
                    </div>
                    ${printContents}
                </div>
                <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #718096;">
                    <p>ICGC Emmanuel Temple - Church Management System</p>
                </div>
            </div>
        `;

        window.print();
        document.body.innerHTML = originalContents;

        // Reinitialize charts after printing
        if ('<?php echo $report["type"]; ?>' === 'finance') {
            setTimeout(initializeFinanceCharts, 1000);
        }
    }

    // Initialize finance charts
    function initializeFinanceCharts() {
        <?php if ($report['type'] === 'finance'): ?>
        // Prepare data for income by category chart
        const categoryData = {};
        const categoryColors = {
            'tithe': 'rgba(59, 130, 246, 0.7)',
            'offering': 'rgba(16, 185, 129, 0.7)',
            'project_offering': 'rgba(139, 92, 246, 0.7)',
            'pledge': 'rgba(99, 102, 241, 0.7)',
            'seed': 'rgba(245, 158, 11, 0.7)',
            'welfare': 'rgba(236, 72, 153, 0.7)',
            'expense': 'rgba(239, 68, 68, 0.7)'
        };

        <?php foreach ($report['data'] as $finance): ?>
            <?php if ($finance['type'] === 'income'): ?>
                if (!categoryData['<?php echo $finance["category"]; ?>']) {
                    categoryData['<?php echo $finance["category"]; ?>'] = 0;
                }
                categoryData['<?php echo $finance["category"]; ?>'] += <?php echo $finance["amount"]; ?>;
            <?php endif; ?>
        <?php endforeach; ?>

        // Create income by category chart
        const incomeCategoryCtx = document.getElementById('income-category-chart').getContext('2d');
        new Chart(incomeCategoryCtx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(categoryData).map(cat => cat.charAt(0).toUpperCase() + cat.slice(1).replace('_', ' ')),
                datasets: [{
                    data: Object.values(categoryData),
                    backgroundColor: Object.keys(categoryData).map(cat => categoryColors[cat] || 'rgba(156, 163, 175, 0.7)'),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: GH₵ ${value.toLocaleString('en-US')} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Create income vs expenses chart
        const incomeExpenseCtx = document.getElementById('income-expense-chart').getContext('2d');
        new Chart(incomeExpenseCtx, {
            type: 'bar',
            data: {
                labels: ['Income vs Expenses'],
                datasets: [
                    {
                        label: 'Income',
                        data: [<?php echo $report['summary']['total_income']; ?>],
                        backgroundColor: 'rgba(16, 185, 129, 0.7)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Expenses',
                        data: [<?php echo $report['summary']['total_expenses']; ?>],
                        backgroundColor: 'rgba(239, 68, 68, 0.7)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'GH₵ ' + value.toLocaleString('en-US');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += 'GH₵ ' + context.parsed.y.toLocaleString('en-US');
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>
    }
</script>
