<?php
/**
 * Children's Ministry Attendance List View
 */
?>

<div class="container mx-auto px-4 py-6 max-w-7xl">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Children's Attendance List</h1>
                <p class="text-gray-600">Detailed attendance tracking for <?php echo date('F j, Y', strtotime($selected_date)); ?></p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="<?php echo BASE_URL; ?>children-ministry"
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Attendance Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-check text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-green-700">Present (On Time)</p>
                    <p class="text-2xl font-bold text-green-900"><?php echo $attendance_stats['present']; ?></p>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-yellow-700">Late Arrivals</p>
                    <p class="text-2xl font-bold text-yellow-900"><?php echo $attendance_stats['late']; ?></p>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-blue-700">Total Attended</p>
                    <p class="text-2xl font-bold text-blue-900"><?php echo $attendance_stats['total']; ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Date and Service Filter -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 overflow-hidden">
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h3 class="text-lg font-semibold text-white flex items-center">
                <i class="fas fa-filter text-white mr-2"></i>
                Filter Attendance
            </h3>
        </div>
        <div class="p-6">
            <form method="GET" action="<?php echo BASE_URL; ?>children-ministry/attendance-list" class="flex flex-wrap gap-4 items-end">
                <div>
                    <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <input type="date" id="date" name="date"
                           value="<?php echo htmlspecialchars($selected_date); ?>"
                           class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label for="service_id" class="block text-sm font-medium text-gray-700 mb-1">Service</label>
                    <select id="service_id" name="service_id" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Services</option>
                        <?php foreach ($services as $service): ?>
                            <option value="<?php echo $service['id']; ?>"
                                    <?php echo ($selected_service_id == $service['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($service['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Attended</option>
                        <option value="present" <?php echo ($selected_status === 'present') ? 'selected' : ''; ?>>Present (On Time)</option>
                        <option value="late" <?php echo ($selected_status === 'late') ? 'selected' : ''; ?>>Late</option>
                    </select>
                </div>
                <div>
                    <label for="per_page" class="block text-sm font-medium text-gray-700 mb-1">Show</label>
                    <select id="per_page" name="per_page" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="25" <?php echo (($_GET['per_page'] ?? 25) == 25) ? 'selected' : ''; ?>>25 per page</option>
                        <option value="50" <?php echo (($_GET['per_page'] ?? 25) == 50) ? 'selected' : ''; ?>>50 per page</option>
                        <option value="100" <?php echo (($_GET['per_page'] ?? 25) == 100) ? 'selected' : ''; ?>>100 per page</option>
                        <option value="all" <?php echo (($_GET['per_page'] ?? 25) == 'all') ? 'selected' : ''; ?>>Show All</option>
                    </select>
                </div>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
                <button type="button" onclick="resetFilters()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    <i class="fas fa-undo mr-2"></i>Reset
                </button>
            </form>
        </div>
    </div>

    <!-- Attendance List -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
        <div class="bg-white border-b border-gray-200 px-6 py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-list text-gray-600 mr-2"></i>
                    Attendance List
                    <span class="ml-2 bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
                        <?php echo $pagination_info['total']; ?> children
                    </span>
                </h3>
                <div class="flex items-center space-x-3">
                    <?php if ($pagination_info['total'] > 0): ?>
                        <span class="text-sm text-gray-500">
                            Showing <?php echo $pagination_info['start']; ?>-<?php echo $pagination_info['end']; ?> of <?php echo $pagination_info['total']; ?>
                        </span>
                    <?php endif; ?>
                    <button onclick="exportAttendanceList()"
                            class="bg-green-600 hover:bg-green-700 text-white px-3 py-1.5 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-download mr-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
        
        <?php if (empty($attendance_list)): ?>
            <div class="p-8 text-center">
                <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500 text-lg">No attendance records found for the selected criteria.</p>
                <p class="text-gray-400 text-sm mt-2">Try adjusting your filters or selecting a different date.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Child</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-in Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Checked in by</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Security Code</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($attendance_list as $record): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-blue-500 flex items-center justify-center mr-3 text-white text-sm font-semibold">
                                            <?php echo strtoupper(substr($record['first_name'], 0, 1) . substr($record['last_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($record['first_name'] . ' ' . $record['last_name']); ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <?php echo $record['gender'] ?? 'N/A'; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $record['age']; ?> years
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($record['service_name']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                    $status = $record['status'] ?? 'present';
                                    $status_classes = [
                                        'present' => 'bg-green-100 text-green-800',
                                        'late' => 'bg-yellow-100 text-yellow-800',
                                        'absent' => 'bg-red-100 text-red-800'
                                    ];
                                    $status_icons = [
                                        'present' => 'fas fa-check',
                                        'late' => 'fas fa-clock',
                                        'absent' => 'fas fa-times'
                                    ];
                                    ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $status_classes[$status] ?? $status_classes['present']; ?>">
                                        <i class="<?php echo $status_icons[$status] ?? $status_icons['present']; ?> mr-1"></i>
                                        <?php echo ucfirst($status); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php if ($record['check_in_time']): ?>
                                        <?php echo date('g:i A', strtotime($record['check_in_time'])); ?>
                                    <?php else: ?>
                                        <span class="text-gray-400">—</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php if ($record['parent_first_name'] && $record['parent_last_name']): ?>
                                        <?php echo htmlspecialchars($record['parent_first_name'] . ' ' . $record['parent_last_name']); ?>
                                    <?php else: ?>
                                        <span class="text-gray-400">—</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-blue-600">
                                    <?php if ($record['security_code']): ?>
                                        <?php echo $record['security_code']; ?>
                                    <?php else: ?>
                                        <span class="text-gray-400">—</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                    $method = $record['check_in_method'] ?? 'Not Checked In';
                                    $method_classes = [
                                        'QR Code' => 'bg-blue-100 text-blue-800',
                                        'QR Family' => 'bg-purple-100 text-purple-800',
                                        'Manual' => 'bg-gray-100 text-gray-800',
                                        'Not Checked In' => 'bg-red-100 text-red-800'
                                    ];
                                    $method_icons = [
                                        'QR Code' => 'fas fa-qrcode',
                                        'QR Family' => 'fas fa-qrcode',
                                        'Manual' => 'fas fa-hand-paper',
                                        'Not Checked In' => 'fas fa-times'
                                    ];
                                    ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $method_classes[$method] ?? $method_classes['Manual']; ?>">
                                        <i class="<?php echo $method_icons[$method] ?? $method_icons['Manual']; ?> mr-1"></i>
                                        <?php echo $method; ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($pagination_info['total_pages'] > 1): ?>
                <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing <?php echo $pagination_info['start']; ?> to <?php echo $pagination_info['end']; ?> of <?php echo $pagination_info['total']; ?> results
                        </div>
                        <div class="flex items-center space-x-2">
                            <!-- Previous Page -->
                            <?php if ($pagination_info['current_page'] > 1): ?>
                                <a href="<?php echo $pagination_info['prev_url']; ?>"
                                   class="bg-white border border-gray-300 text-gray-500 hover:text-gray-700 hover:bg-gray-50 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php for ($i = max(1, $pagination_info['current_page'] - 2); $i <= min($pagination_info['total_pages'], $pagination_info['current_page'] + 2); $i++): ?>
                                <?php if ($i == $pagination_info['current_page']): ?>
                                    <span class="bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium">
                                        <?php echo $i; ?>
                                    </span>
                                <?php else: ?>
                                    <a href="<?php echo $pagination_info['base_url'] . '&page=' . $i; ?>"
                                       class="bg-white border border-gray-300 text-gray-500 hover:text-gray-700 hover:bg-gray-50 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <!-- Next Page -->
                            <?php if ($pagination_info['current_page'] < $pagination_info['total_pages']): ?>
                                <a href="<?php echo $pagination_info['next_url']; ?>"
                                   class="bg-white border border-gray-300 text-gray-500 hover:text-gray-700 hover:bg-gray-50 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script>
function exportAttendanceList() {
    const params = new URLSearchParams(window.location.search);
    const exportUrl = `<?php echo BASE_URL; ?>children-ministry/export-attendance-list?${params.toString()}&format=pdf`;
    window.open(exportUrl, '_blank');
}

function resetFilters() {
    window.location.href = '<?php echo BASE_URL; ?>children-ministry/attendance-list';
}

// Auto-submit form when pagination dropdown changes
document.getElementById('per_page').addEventListener('change', function() {
    this.form.submit();
});
</script>
