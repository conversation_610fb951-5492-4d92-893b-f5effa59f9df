<?php
/**
 * Database Query Optimization Utilities
 * Provides common query patterns and optimization helpers
 */

class QueryOptimizer {
    
    /**
     * Get age calculation SQL fragment
     * 
     * @param string $dateColumn Date column name
     * @return string SQL fragment for age calculation
     */
    public static function getAgeCalculationSQL(string $dateColumn = 'date_of_birth'): string {
        return "TIMESTAMPDIFF(YEAR, {$dateColumn}, CURDATE())";
    }
    
    /**
     * Get children filter SQL
     * 
     * @param int $maxAge Maximum child age
     * @param string $dateColumn Date column name
     * @return string SQL WHERE clause for children
     */
    public static function getChildrenFilterSQL(int $maxAge, string $dateColumn = 'date_of_birth'): string {
        $ageSQL = self::getAgeCalculationSQL($dateColumn);
        return "{$ageSQL} <= {$maxAge}";
    }
    
    /**
     * Get adults filter SQL
     * 
     * @param int $maxChildAge Maximum child age
     * @param string $dateColumn Date column name
     * @return string SQL WHERE clause for adults
     */
    public static function getAdultsFilterSQL(int $maxChildAge, string $dateColumn = 'date_of_birth'): string {
        $ageSQL = self::getAgeCalculationSQL($dateColumn);
        return "{$ageSQL} > {$maxChildAge}";
    }
    
    /**
     * Add pagination to query
     * 
     * @param string $query Base query
     * @param int $page Page number (1-based)
     * @param int $limit Items per page
     * @return string Query with LIMIT and OFFSET
     */
    public static function addPagination(string $query, int $page = 1, int $limit = 50): string {
        $offset = ($page - 1) * $limit;
        return $query . " LIMIT {$limit} OFFSET {$offset}";
    }
    
    /**
     * Get optimized children query with all necessary joins
     * 
     * @param int $maxAge Maximum child age
     * @param bool $activeOnly Include only active members
     * @return string Optimized SQL query
     */
    public static function getOptimizedChildrenQuery(int $maxAge, bool $activeOnly = true): string {
        $ageSQL = self::getAgeCalculationSQL('m.date_of_birth');
        
        $query = "SELECT m.*, 
                         {$ageSQL} as age,
                         ag.name as age_group_name,
                         ag.id as age_group_id
                  FROM members m
                  LEFT JOIN children_age_groups ag ON (
                      {$ageSQL} BETWEEN ag.min_age AND ag.max_age
                      AND ag.is_active = 1
                  )
                  WHERE {$ageSQL} <= :max_age";
        
        if ($activeOnly) {
            $query .= " AND m.member_status = 'active'";
        }

        $query .= " ORDER BY m.created_at DESC, m.last_name, m.first_name";

        return $query;
    }
    
    /**
     * Get attendance statistics query
     * 
     * @param string $date Attendance date
     * @param int|null $serviceId Service ID (optional)
     * @return string SQL query for attendance stats
     */
    public static function getAttendanceStatsQuery(string $date, ?int $serviceId = null): string {
        $serviceFilter = $serviceId ? "AND ccl.service_id = {$serviceId}" : "";
        
        return "SELECT 
                    COUNT(DISTINCT ccl.child_id) as present_count,
                    COUNT(DISTINCT CASE WHEN TIME(ccl.check_in_time) > TIME(s.time) THEN ccl.child_id END) as late_count,
                    COUNT(DISTINCT CASE WHEN ccl.notes LIKE '%QR%' THEN ccl.child_id END) as qr_count
                FROM children_checkin_log ccl
                JOIN services s ON ccl.service_id = s.id
                WHERE ccl.attendance_date = '{$date}' {$serviceFilter}";
    }
    
    /**
     * Check if query needs optimization
     * 
     * @param PDO $conn Database connection
     * @param string $query SQL query
     * @return array Analysis results
     */
    public static function analyzeQuery(PDO $conn, string $query): array {
        try {
            $stmt = $conn->prepare("EXPLAIN " . $query);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $analysis = [
                'needs_optimization' => false,
                'issues' => [],
                'recommendations' => []
            ];
            
            foreach ($results as $row) {
                // Check for table scans
                if ($row['type'] === 'ALL') {
                    $analysis['needs_optimization'] = true;
                    $analysis['issues'][] = "Full table scan on {$row['table']}";
                    $analysis['recommendations'][] = "Consider adding index on {$row['table']}";
                }
                
                // Check for large row examinations
                if (isset($row['rows']) && $row['rows'] > 1000) {
                    $analysis['needs_optimization'] = true;
                    $analysis['issues'][] = "Examining {$row['rows']} rows in {$row['table']}";
                }
            }
            
            return $analysis;
            
        } catch (PDOException $e) {
            return [
                'error' => 'Could not analyze query: ' . $e->getMessage()
            ];
        }
    }
}
?>
