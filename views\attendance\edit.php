<div class="container mx-auto max-w-4xl">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Edit Attendance Record</h1>
            <p class="text-gray-600 mt-1">Update attendance information for this record</p>
        </div>
        <a href="<?php echo BASE_URL; ?>attendance" class="bg-gray-500 hover:bg-gray-600 text-white py-2.5 px-5 rounded-md flex items-center shadow-sm transition-all duration-200">
            <i class="fas fa-arrow-left mr-2"></i> Back to Attendance
        </a>
    </div>

    <!-- Error Messages -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-8 rounded-r-md shadow-sm" role="alert">
            <div class="flex items-center mb-1">
                <i class="fas fa-exclamation-circle mr-2 text-red-500"></i>
                <p class="font-semibold">Please correct the following errors:</p>
            </div>
            <ul class="list-disc list-inside pl-4">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Success Message -->
    <?php if (isset($_SESSION['success_message'])) : ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-8 rounded-r-md shadow-sm" role="alert">
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2 text-green-500"></i>
                <p><?php echo $_SESSION['success_message']; ?></p>
            </div>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <!-- Attendance Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- Form Header -->
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="p-2 rounded-full bg-primary bg-opacity-10 text-primary mr-3">
                        <i class="fas fa-edit text-lg"></i>
                    </div>
                    <h2 class="text-lg font-semibold text-gray-800">Attendance Information</h2>
                </div>
                <div class="text-sm text-gray-600">
                    <span class="font-medium">Record ID:</span> #<?php echo $attendance->id; ?>
                </div>
            </div>
        </div>

        <!-- Form Content -->
        <div class="p-8">
            <form action="<?php echo BASE_URL; ?>attendance/update" method="POST">
                <input type="hidden" name="id" value="<?php echo $attendance->id; ?>">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <!-- Service -->
                    <div>
                        <label for="service_id" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-church text-primary mr-2"></i> Service <span class="text-red-500 ml-1">*</span>
                        </label>
                        <select id="service_id" name="service_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2.5" required>
                            <option value="">Select Service</option>
                            <?php foreach ($services as $service) : ?>
                                <option value="<?php echo $service['id']; ?>" <?php echo ($service['id'] == $attendance->service_id) ? 'selected' : ''; ?>>
                                    <?php echo $service['name']; ?> (<?php echo ucfirst($service['day_of_week']); ?> - <?php echo date('h:i A', strtotime($service['time'])); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Attendance Date -->
                    <div>
                        <label for="attendance_date" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-calendar-alt text-primary mr-2"></i> Date <span class="text-red-500 ml-1">*</span>
                        </label>
                        <input type="date" id="attendance_date" name="attendance_date" value="<?php echo $attendance->attendance_date; ?>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2.5" required>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <!-- Member -->
                    <div>
                        <label for="member_id" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-user text-primary mr-2"></i> Member
                        </label>
                        <select id="member_id" name="member_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2.5">
                            <option value="">General Attendance (No specific member)</option>
                            <?php foreach ($members as $member) : ?>
                                <option value="<?php echo $member['id']; ?>" <?php echo ($member['id'] == $attendance->member_id) ? 'selected' : ''; ?>>
                                    <?php echo $member['first_name'] . ' ' . $member['last_name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Status -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-clipboard-check text-primary mr-2"></i> Status
                        </label>
                        <div class="grid grid-cols-3 gap-4 mt-2">
                            <label class="relative flex items-center p-3 rounded-md border border-gray-200 hover:border-primary cursor-pointer transition-all duration-200 <?php echo ($attendance->status == 'present') ? 'bg-green-50 border-green-200' : ''; ?>">
                                <input type="radio" name="status" value="present" class="form-radio text-primary focus:ring-primary" <?php echo ($attendance->status == 'present') ? 'checked' : ''; ?>>
                                <span class="ml-2 flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-1"></i> Present
                                </span>
                            </label>
                            <label class="relative flex items-center p-3 rounded-md border border-gray-200 hover:border-primary cursor-pointer transition-all duration-200 <?php echo ($attendance->status == 'absent') ? 'bg-red-50 border-red-200' : ''; ?>">
                                <input type="radio" name="status" value="absent" class="form-radio text-red-500 focus:ring-red-500" <?php echo ($attendance->status == 'absent') ? 'checked' : ''; ?>>
                                <span class="ml-2 flex items-center">
                                    <i class="fas fa-times-circle text-red-500 mr-1"></i> Absent
                                </span>
                            </label>
                            <label class="relative flex items-center p-3 rounded-md border border-gray-200 hover:border-primary cursor-pointer transition-all duration-200 <?php echo ($attendance->status == 'late') ? 'bg-yellow-50 border-yellow-200' : ''; ?>">
                                <input type="radio" name="status" value="late" class="form-radio text-yellow-500 focus:ring-yellow-500" <?php echo ($attendance->status == 'late') ? 'checked' : ''; ?>>
                                <span class="ml-2 flex items-center">
                                    <i class="fas fa-clock text-yellow-500 mr-1"></i> Late
                                </span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="border-t border-gray-200 pt-6 mt-6 flex justify-end space-x-3">
                    <a href="<?php echo BASE_URL; ?>attendance" class="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2.5 px-5 rounded-md shadow-sm transition-all duration-200 flex items-center">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                    <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2.5 px-5 rounded-md shadow-sm transition-all duration-200 flex items-center">
                        <i class="fas fa-save mr-2"></i> Update Attendance
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Status radio button enhancement
        const statusRadios = document.querySelectorAll('input[name="status"]');
        const statusLabels = document.querySelectorAll('input[name="status"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // Remove highlight from all labels
                document.querySelectorAll('input[name="status"]').forEach(r => {
                    const label = r.closest('label');
                    label.classList.remove('bg-green-50', 'bg-red-50', 'bg-yellow-50', 'border-green-200', 'border-red-200', 'border-yellow-200');
                });

                // Add highlight to selected label
                const selectedLabel = this.closest('label');
                if (this.value === 'present') {
                    selectedLabel.classList.add('bg-green-50', 'border-green-200');
                } else if (this.value === 'absent') {
                    selectedLabel.classList.add('bg-red-50', 'border-red-200');
                } else if (this.value === 'late') {
                    selectedLabel.classList.add('bg-yellow-50', 'border-yellow-200');
                }
            });
        });
    });
</script>
