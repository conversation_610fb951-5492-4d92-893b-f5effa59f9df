# Database Architecture Refactoring

## Overview

This document describes the systematic refactoring of the ICGC Emmanuel Temple Church Management System's database architecture to eliminate conflicting initialization systems and consolidate on a single, centralized approach.

## Problem Statement

### Before Refactoring

The application had **two conflicting database initialization systems**:

1. **Modern Centralized System** (`bootstrap.php`)
   - Uses PDO database connections
   - Provides global `$db` variable via `db_connect()` function
   - Includes security, error handling, logging, session management
   - Used by: Controllers, API files, most modern parts of the application

2. **Legacy Manual System** (broken)
   - Expected a global `$conn` mysqli variable that **didn't exist**
   - Files like `view_members.php` included only `config/config.php`
   - Used mysqli syntax (`bind_param`, `get_result`, `fetch_assoc`)
   - **Was completely broken** - the `$conn` variable was undefined

### Core Issues

- **Undefined Variables**: Legacy files tried to use `$conn` mysqli variable that was never created
- **Code Duplication**: Database connection logic was scattered across multiple files
- **Technology Inconsistency**: PD<PERSON> vs mysqli created maintenance nightmares
- **Missing Security Features**: Legacy files bypassed security, logging, and error handling
- **Maintenance Burden**: Changes required updates in multiple files instead of one central location

## Solution: Centralized Architecture

### Refactoring Strategy

1. **Standardize on `bootstrap.php`** for ALL pages
2. **Remove manual database setup** from individual pages
3. **Use the centralized PDO connection** consistently
4. **Leverage existing security and error handling**

### Files Refactored

#### 1. `view_members.php`
**Before:**
```php
require_once __DIR__ . '/../config/config.php';
$stmt = $conn->prepare("SELECT group_name FROM groups WHERE id = ?");
$stmt->bind_param("i", $group_id);
$stmt->execute();
$group_result = $stmt->get_result();
$group = $group_result->fetch_assoc();
```

**After:**
```php
require_once __DIR__ . '/bootstrap.php';
check_auth();
$db = db_connect();
$stmt = $db->prepare("SELECT group_name FROM groups WHERE group_id = ?");
$stmt->execute([$group_id]);
$group = $stmt->fetch(PDO::FETCH_ASSOC);
```

#### 2. `controllers/SettingController.php`
**Before:**
```php
$host = DB_HOST;
$user = DB_USER;
$pass = DB_PASS;
$name = DB_NAME;
$mysqli = new mysqli($host, $user, $pass, $name);
```

**After:**
```php
$host = EnvironmentConfig::get('database.host', 'localhost');
$user = EnvironmentConfig::get('database.username', 'root');
$pass = EnvironmentConfig::get('database.password', '');
$name = EnvironmentConfig::get('database.name', 'icgc_db');
$mysqli = new mysqli($host, $user, $pass, $name);
```

#### 3. `models/DatabaseStats.php`
**Before:**
```php
$mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
```

**After:**
```php
$host = EnvironmentConfig::get('database.host', 'localhost');
$user = EnvironmentConfig::get('database.username', 'root');
$pass = EnvironmentConfig::get('database.password', '');
$name = EnvironmentConfig::get('database.name', 'icgc_db');
$mysqli = new mysqli($host, $user, $pass, $name);
```

#### 4. `bootstrap.php`
**Removed redundant Config.php loading:**
```php
// REMOVED: Conflicting configuration system
// if (file_exists(BASE_DIR . '/config/Config.php')) {
//     require_once BASE_DIR . '/config/Config.php';
// }
```

## Current Architecture

### Centralized Database System

```
bootstrap.php
├── config/environment.php (EnvironmentConfig)
├── config/database.php (Database class)
├── Global $db variable (PDO)
├── db_connect() function
├── Security & session management
├── Error handling & logging
└── Helper functions
```

### Database Connection Flow

1. **Environment Configuration**: `EnvironmentConfig::load()` loads database settings
2. **Database Class**: `Database` class uses environment config for connection parameters
3. **Global Connection**: `db_connect()` function provides singleton PDO connection
4. **Global Variable**: `$db` variable available throughout application

### Backward Compatibility

- **Legacy Constants**: `DB_HOST`, `DB_USER`, `DB_PASS`, `DB_NAME` still defined for backup/restore operations
- **mysqli Support**: Backup and optimization operations still use mysqli where appropriate
- **Environment Variables**: `.env` file support maintained

## Benefits Achieved

### 1. **Consistency**
- Single database initialization system
- Consistent PDO usage across application
- Unified error handling and logging

### 2. **Security**
- Centralized authentication checks
- Consistent session management
- Proper error handling without exposing sensitive information

### 3. **Maintainability**
- Single point of configuration
- Easier to update database settings
- Reduced code duplication

### 4. **Performance**
- Singleton database connection
- Proper connection pooling
- Optimized query execution

### 5. **Reliability**
- Eliminated undefined variable errors
- Consistent error handling
- Proper transaction management

## Usage Guidelines

### For New Files

Always include bootstrap.php at the top of new PHP files:

```php
<?php
require_once __DIR__ . '/bootstrap.php';
check_auth(); // If authentication required

// Use global $db variable or db_connect() function
$stmt = $db->prepare("SELECT * FROM table WHERE id = ?");
$stmt->execute([$id]);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
```

### For Database Operations

Use PDO prepared statements:

```php
// SELECT
$stmt = $db->prepare("SELECT * FROM members WHERE status = ?");
$stmt->execute(['active']);
$members = $stmt->fetchAll(PDO::FETCH_ASSOC);

// INSERT
$stmt = $db->prepare("INSERT INTO members (name, email) VALUES (?, ?)");
$stmt->execute([$name, $email]);
$newId = $db->lastInsertId();

// UPDATE
$stmt = $db->prepare("UPDATE members SET status = ? WHERE id = ?");
$stmt->execute(['inactive', $memberId]);
```

### For Special Operations

Use mysqli only when PDO is insufficient (e.g., OPTIMIZE TABLE):

```php
$host = EnvironmentConfig::get('database.host', 'localhost');
$user = EnvironmentConfig::get('database.username', 'root');
$pass = EnvironmentConfig::get('database.password', '');
$name = EnvironmentConfig::get('database.name', 'icgc_db');

$mysqli = new mysqli($host, $user, $pass, $name);
// Perform mysqli-specific operations
$mysqli->close();
```

## Testing

The refactored system has been tested and verified:

- ✅ Bootstrap loads without errors
- ✅ PDO database connection successful
- ✅ Database queries work correctly
- ✅ Environment configuration accessible
- ✅ Legacy constants defined for backward compatibility
- ✅ All refactored files have no syntax errors

## Conclusion

The refactoring successfully eliminated the conflicting database initialization systems and consolidated the application on a single, robust, centralized architecture. This provides better security, maintainability, and reliability while maintaining backward compatibility where necessary.
