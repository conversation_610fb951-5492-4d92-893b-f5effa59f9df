<?php
// Include necessary files and initialize variables
// Note: $page_title is set by the controller with dynamic church name
$member_count = isset($member_count) ? $member_count : 0;
$sms_balance = isset($sms_balance) ? $sms_balance : 0;
$messages = isset($messages) ? $messages : [];
$sent = array_filter($messages, function($msg) { return $msg['status'] === 'sent'; });
$pending = array_filter($messages, function($msg) { return $msg['status'] === 'pending'; });
$failed = array_filter($messages, function($msg) { return $msg['status'] === 'failed'; });
$partial = array_filter($messages, function($msg) { return $msg['status'] === 'partial'; });
?>

<div class="container mx-auto max-w-6xl">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div class="mb-4 md:mb-0">
                <h1 class="text-2xl font-bold text-gray-800 mb-2">SMS Broadcast</h1>
                <div class="flex flex-col space-y-2">
                    <div class="flex items-center bg-gray-50 px-4 py-2 rounded-lg border border-gray-200">
                        <i class="fas fa-coins mr-3 text-yellow-500 text-xl"></i>
                        <div>
                            <span class="text-sm text-gray-600">Credit Balance:</span>
                            <div class="font-bold <?php echo $sms_balance > 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo number_format($sms_balance); ?> SMS
                                <?php if ($sms_balance <= 100): ?>
                                    <span class="ml-2 px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">Low Balance</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center bg-gray-50 px-4 py-2 rounded-lg border border-gray-200">
                        <i class="fas fa-paper-plane mr-3 text-blue-500 text-xl"></i>
                        <div>
                            <span class="text-sm text-gray-600">Credits Used:</span>
                            <div class="font-bold text-gray-800">
                                <?php
                                    // Estimate credits used (1 credit per message)
                                    $total_recipients = 0;
                                    foreach ($messages as $msg) {
                                        if ($msg['status'] === 'sent' || $msg['status'] === 'partial') {
                                            // Count recipients for each message
                                            $recipients = explode(',', $msg['recipients']);
                                            $total_recipients += count($recipients);
                                        }
                                    }
                                    echo number_format($total_recipients);
                                ?> SMS
                                <span class="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                                    <?php
                                        $total = count($messages);
                                        $success_rate = $total > 0 ? round((count($sent) / $total) * 100) : 0;
                                        echo $success_rate . '% Success Rate';
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                <button id="refresh-status" class="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-4 rounded-md flex items-center justify-center transition-colors duration-200">
                    <i class="fas fa-sync-alt mr-2"></i> Refresh Status
                </button>
                <a href="<?php echo BASE_URL; ?>sms/messages" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md flex items-center justify-center transition-colors duration-200">
                    <i class="fas fa-history mr-2"></i> View Messages
                </a>
                <a href="<?php echo BASE_URL; ?>sms/create" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center justify-center transition-colors duration-200">
                    <i class="fas fa-paper-plane mr-2"></i> Compose SMS
                </a>
            </div>
        </div>
    </div>

    <!-- SMS Stats Cards - Redesigned for better visual experience -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">SMS Statistics</h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <!-- First Card: Total SMS -->
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-5 border border-blue-200 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-blue-700 font-medium mb-1">Total SMS</p>
                        <p class="text-3xl font-bold text-gray-800"><?php echo count($messages); ?></p>
                    </div>
                    <div class="rounded-full bg-blue-200 p-3">
                        <i class="fas fa-paper-plane text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-blue-200">
                    <div class="flex justify-between items-center">
                        <p class="text-sm text-blue-700">Today's Messages</p>
                        <p class="text-xl font-bold text-gray-800">
                            <?php
                                // Count messages sent today
                                $today = date('Y-m-d');
                                $today_messages = array_filter($messages, function($msg) use ($today) {
                                    return date('Y-m-d', strtotime($msg['sent_date'])) === $today;
                                });
                                echo count($today_messages);
                            ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Second Card: Sent Messages -->
            <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-5 border border-green-200 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-green-700 font-medium mb-1">Sent</p>
                        <p class="text-3xl font-bold text-gray-800">
                            <?php
                                $sent = array_filter($messages, function($msg) { return $msg['status'] === 'sent'; });
                                echo count($sent);
                            ?>
                        </p>
                    </div>
                    <div class="rounded-full bg-green-200 p-3">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-green-200">
                    <div class="flex justify-between items-center">
                        <p class="text-sm text-green-700">Partial</p>
                        <p class="text-xl font-bold text-gray-800">
                            <?php
                                $partial = array_filter($messages, function($msg) { return $msg['status'] === 'partial'; });
                                echo count($partial);
                            ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Third Card: Pending Messages -->
            <div class="bg-gradient-to-r from-amber-50 to-amber-100 rounded-lg p-5 border border-amber-200 shadow-sm">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-amber-700 font-medium mb-1">Pending</p>
                        <p class="text-3xl font-bold text-gray-800">
                            <?php
                                $pending = array_filter($messages, function($msg) { return $msg['status'] === 'pending'; });
                                echo count($pending);
                            ?>
                        </p>
                    </div>
                    <div class="rounded-full bg-amber-200 p-3">
                        <i class="fas fa-clock text-amber-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-amber-200">
                    <div class="flex justify-between items-center">
                        <p class="text-sm text-amber-700">Failed</p>
                        <p class="text-xl font-bold text-gray-800">
                            <?php
                                $failed = array_filter($messages, function($msg) { return $msg['status'] === 'failed'; });
                                echo count($failed);
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Refresh status button
        const refreshButton = document.getElementById('refresh-status');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                window.location.reload();
            });
        }
        
        // Initialize tooltips
        initTooltips();
    });
    
    // Function to initialize tooltips
    function initTooltips() {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        
        tooltips.forEach(tooltip => {
            tooltip.addEventListener('mouseenter', function() {
                const title = this.getAttribute('title') || '';
                if (!title) return;
                
                this.setAttribute('data-tooltip', title);
                this.setAttribute('title', '');
                
                const tooltipEl = document.createElement('div');
                tooltipEl.classList.add('tooltip-box');
                tooltipEl.textContent = title;
                tooltipEl.style.position = 'absolute';
                tooltipEl.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                tooltipEl.style.color = 'white';
                tooltipEl.style.padding = '5px 10px';
                tooltipEl.style.borderRadius = '4px';
                tooltipEl.style.fontSize = '12px';
                tooltipEl.style.zIndex = '100';
                tooltipEl.style.maxWidth = '300px';
                tooltipEl.style.wordBreak = 'break-word';
                
                document.body.appendChild(tooltipEl);
                
                const rect = this.getBoundingClientRect();
                const tooltipRect = tooltipEl.getBoundingClientRect();
                
                tooltipEl.style.top = (rect.top - tooltipRect.height - 10) + 'px';
                tooltipEl.style.left = (rect.left + (rect.width / 2) - (tooltipRect.width / 2)) + 'px';
                
                this.tooltipElement = tooltipEl;
            });
            
            tooltip.addEventListener('mouseleave', function() {
                if (this.tooltipElement) {
                    document.body.removeChild(this.tooltipElement);
                    this.setAttribute('title', this.getAttribute('data-tooltip'));
                    this.removeAttribute('data-tooltip');
                    this.tooltipElement = null;
                }
            });
        });
    }
</script>
