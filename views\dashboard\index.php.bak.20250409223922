<div class="container mx-auto">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">Dashboard</h1>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Members -->
        <div class="gradient-card card-blue">
            <div class="card-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="card-title">Total Members</div>
            <div class="card-value"><?php echo $total_members; ?></div>
            <div class="card-subtitle">
                <a href="<?php echo BASE_URL; ?>members" class="hover:underline flex items-center">
                    <span>View All Members</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
        </div>

        <!-- New Members -->
        <div class="gradient-card card-yellow">
            <div class="card-icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="card-title">New Members (30 days)</div>
            <div class="card-value"><?php echo $new_members; ?></div>
            <div class="card-subtitle">
                <a href="<?php echo BASE_URL; ?>members?filter=new" class="hover:underline flex items-center">
                    <span>View New Members</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
        </div>

        <!-- Financial Balance -->
        <div class="gradient-card card-green">
            <div class="card-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="card-title">Financial Balance</div>
            <div class="card-value">GH₵ <?php echo number_format($financial_balance, 2); ?></div>
            <div class="card-subtitle">
                <a href="<?php echo BASE_URL; ?>finance" class="hover:underline flex items-center">
                    <span>View Finances</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="gradient-card card-purple">
            <div class="card-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="card-title">Upcoming Events</div>
            <div class="card-value"><?php echo $upcoming_events; ?></div>
            <div class="card-subtitle">
                <a href="<?php echo BASE_URL; ?>events" class="hover:underline flex items-center">
                    <span>View Events</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
        </div>
    </div>
                <div>
                    <p class="text-gray-500 text-sm">Total Members</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo $total_members; ?></h3>
                </div>
            </div>
            <div class="mt-4">
                <a href="<?php echo BASE_URL; ?>members" class="text-primary hover:underline text-sm flex items-center">
                    <span>View All Members</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
        </div>

        <!-- New Members -->
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-blue-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500 mr-4">
                    <i class="fas fa-user-plus text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">New Members (30 days)</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo $new_members; ?></h3>
                </div>
            </div>
            <div class="mt-4">
                <a href="<?php echo BASE_URL; ?>members?filter=new" class="text-blue-500 hover:underline text-sm flex items-center">
                    <span>View New Members</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
        </div>

        <!-- Financial Balance -->
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-green-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500 mr-4">
                    <i class="fas fa-money-bill-wave text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Current Balance</p>
                    <h3 class="text-2xl font-bold <?php echo $balance >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                        GH₵ <?php echo number_format($balance, 2); ?>
                    </h3>
                </div>
            </div>
            <div class="mt-4">
                <a href="<?php echo BASE_URL; ?>finances" class="text-green-500 hover:underline text-sm flex items-center">
                    <span>View Finances</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-purple-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-500 mr-4">
                    <i class="fas fa-calendar-alt text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Upcoming Events</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo count($upcoming_events); ?></h3>
                </div>
            </div>
            <div class="mt-4">
                <a href="<?php echo BASE_URL; ?>events" class="text-purple-500 hover:underline text-sm flex items-center">
                    <span>View All Events</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Member Demographics -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Member Demographics</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Gender Distribution -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Gender Distribution</h3>
                        <div class="flex items-center mb-2">
                            <div class="w-full bg-gray-200 rounded-full h-4">
                                <?php
                                    $male_percentage = $total_members > 0 ? ($male_members / $total_members) * 100 : 0;
                                    $female_percentage = $total_members > 0 ? ($female_members / $total_members) * 100 : 0;
                                ?>
                                <div class="bg-blue-500 h-4 rounded-l-full" style="width: <?php echo $male_percentage; ?>%"></div>
                                <div class="bg-pink-500 h-4 rounded-r-full" style="width: <?php echo $female_percentage; ?>%"></div>
                            </div>
                        </div>
                        <div class="flex justify-between text-xs text-gray-600">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
                                <span>Male (<?php echo $male_members; ?>)</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-pink-500 rounded-full mr-1"></div>
                                <span>Female (<?php echo $female_members; ?>)</span>
                            </div>
                        </div>
                    </div>

                    <!-- Member Status -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Member Status</h3>
                        <div class="flex items-center mb-2">
                            <div class="w-full bg-gray-200 rounded-full h-4">
                                <?php
                                    $active_percentage = $total_members > 0 ? ($active_members / $total_members) * 100 : 0;
                                    $inactive_percentage = $total_members > 0 ? ($inactive_members / $total_members) * 100 : 0;
                                ?>
                                <div class="bg-green-500 h-4 rounded-l-full" style="width: <?php echo $active_percentage; ?>%"></div>
                                <div class="bg-red-500 h-4 rounded-r-full" style="width: <?php echo $inactive_percentage; ?>%"></div>
                            </div>
                        </div>
                        <div class="flex justify-between text-xs text-gray-600">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
                                <span>Active (<?php echo $active_members; ?>)</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
                                <span>Inactive (<?php echo $inactive_members; ?>)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Departments Distribution -->
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Departments Distribution</h3>
                    <div class="space-y-2">
                        <?php foreach ($members_by_department as $dept) : ?>
                            <?php
                                $dept_percentage = $total_members > 0 ? ($dept['count'] / $total_members) * 100 : 0;
                                $dept_name = ucwords(str_replace('_', ' ', $dept['department']));
                            ?>
                            <div>
                                <div class="flex justify-between text-xs text-gray-600 mb-1">
                                    <span><?php echo $dept_name; ?></span>
                                    <span><?php echo $dept['count']; ?> (<?php echo round($dept_percentage, 1); ?>%)</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-primary h-2 rounded-full" style="width: <?php echo $dept_percentage; ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Attendance -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Recent Attendance</h2>
                    <a href="<?php echo BASE_URL; ?>attendance" class="text-primary hover:underline text-sm">View All</a>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php if (empty($recent_attendance)) : ?>
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">No attendance records found</td>
                                </tr>
                            <?php else : ?>
                                <?php foreach ($recent_attendance as $attendance) : ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo format_date($attendance['attendance_date']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo $attendance['service_name']; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo $attendance['member_name'] ?: 'General Attendance'; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($attendance['status'] === 'present') : ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Present</span>
                                            <?php elseif ($attendance['status'] === 'absent') : ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Absent</span>
                                            <?php else : ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Late</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Recent Transactions</h2>
                    <a href="<?php echo BASE_URL; ?>finances" class="text-primary hover:underline text-sm">View All</a>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php if (empty($recent_transactions)) : ?>
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">No transactions found</td>
                                </tr>
                            <?php else : ?>
                                <?php foreach ($recent_transactions as $transaction) : ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo format_date($transaction['transaction_date']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($transaction['category'] === 'tithe') : ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Tithe</span>
                                            <?php elseif ($transaction['category'] === 'offering') : ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Offering</span>
                                            <?php elseif ($transaction['category'] === 'special_offering') : ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Special Offering</span>
                                            <?php elseif ($transaction['category'] === 'donation') : ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800">Donation</span>
                                            <?php else : ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Expense</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm <?php echo $transaction['category'] === 'expense' ? 'text-red-600' : 'text-green-600'; ?> font-medium">
                                            GH₵ <?php echo number_format($transaction['amount'], 2); ?>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">
                                            <?php echo !empty($transaction['description']) ? $transaction['description'] : 'N/A'; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
            <!-- Financial Summary -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Financial Summary</h2>
                <div class="space-y-4">
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-gray-600">Total Income</span>
                            <span class="font-medium text-green-600">GH₵ <?php echo number_format($total_income, 2); ?></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-gray-600">Total Expenses</span>
                            <span class="font-medium text-red-600">GH₵ <?php echo number_format($total_expenses, 2); ?></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <?php $expense_percentage = $total_income > 0 ? ($total_expenses / $total_income) * 100 : 0; ?>
                            <div class="bg-red-500 h-2 rounded-full" style="width: <?php echo min($expense_percentage, 100); ?>%"></div>
                        </div>
                    </div>
                    <div class="pt-2 border-t">
                        <div class="flex justify-between text-sm">
                            <span class="font-medium text-gray-700">Balance</span>
                            <span class="font-medium <?php echo $balance >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                GH₵ <?php echo number_format($balance, 2); ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Income by Category -->
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Income by Category</h3>
                    <div class="space-y-2">
                        <?php foreach ($income_by_category as $income) : ?>
                            <?php
                                $income_percentage = $total_income > 0 ? ($income['total'] / $total_income) * 100 : 0;
                                $category_name = ucwords(str_replace('_', ' ', $income['category']));
                            ?>
                            <div>
                                <div class="flex justify-between text-xs text-gray-600 mb-1">
                                    <span><?php echo $category_name; ?></span>
                                    <span>GH₵ <?php echo number_format($income['total'], 2); ?> (<?php echo round($income_percentage, 1); ?>%)</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: <?php echo $income_percentage; ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Upcoming Events -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Upcoming Events</h2>
                    <a href="<?php echo BASE_URL; ?>events" class="text-primary hover:underline text-sm">View All</a>
                </div>
                <?php if (empty($upcoming_events)) : ?>
                    <p class="text-gray-500 text-center py-4">No upcoming events</p>
                <?php else : ?>
                    <div class="space-y-4">
                        <?php foreach ($upcoming_events as $event) : ?>
                            <div class="border-l-4 border-primary pl-4 py-1">
                                <div class="text-sm font-medium text-gray-900"><?php echo $event['title']; ?></div>
                                <div class="text-xs text-gray-500 mt-1">
                                    <i class="far fa-calendar-alt mr-1"></i> <?php echo format_date($event['start_date'], 'M d, Y'); ?>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">
                                    <i class="far fa-clock mr-1"></i> <?php echo date('h:i A', strtotime($event['start_date'])); ?>
                                </div>
                                <?php if (!empty($event['location'])) : ?>
                                    <div class="text-xs text-gray-500 mt-1">
                                        <i class="fas fa-map-marker-alt mr-1"></i> <?php echo $event['location']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Birthdays This Month -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Birthdays This Month</h2>
                    <span class="text-sm text-gray-500"><?php echo date('F Y'); ?></span>
                </div>
                <?php if (empty($birthdays_this_month)) : ?>
                    <p class="text-gray-500 text-center py-4">No birthdays this month</p>
                <?php else : ?>
                    <div class="space-y-3">
                        <?php foreach ($birthdays_this_month as $member) : ?>
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <?php if (!empty($member['profile_picture'])) : ?>
                                        <img class="h-10 w-10 rounded-full" src="<?php echo BASE_URL . 'uploads/' . $member['profile_picture']; ?>" alt="<?php echo $member['first_name']; ?>">
                                    <?php else : ?>
                                        <div class="h-10 w-10 rounded-full bg-primary-light flex items-center justify-center text-white font-bold">
                                            <?php echo strtoupper(substr($member['first_name'], 0, 1) . substr($member['last_name'], 0, 1)); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $member['first_name'] . ' ' . $member['last_name']; ?></div>
                                    <div class="text-xs text-gray-500">
                                        <i class="fas fa-birthday-cake mr-1 text-pink-500"></i>
                                        <?php echo date('F d', strtotime($member['date_of_birth'])); ?>
                                        <?php
                                            $birth_day = date('d', strtotime($member['date_of_birth']));
                                            $today = date('d');
                                            if ($birth_day == $today) {
                                                echo ' <span class="text-pink-500 font-bold">(Today!)</span>';
                                            }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h2>
                <div class="grid grid-cols-2 gap-3">
                    <a href="<?php echo BASE_URL; ?>members/add" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="p-2 rounded-full bg-primary-light text-white mb-2">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <span class="text-xs text-gray-700">Add New Member</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/add" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="p-2 rounded-full bg-blue-100 text-blue-500 mb-2">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <span class="text-xs text-gray-700">Record Attendance</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>finances/add" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="p-2 rounded-full bg-green-100 text-green-500 mb-2">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <span class="text-xs text-gray-700">Add Transaction</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>events/add" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="p-2 rounded-full bg-purple-100 text-purple-500 mb-2">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <span class="text-xs text-gray-700">Add Event</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>sms/create" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="p-2 rounded-full bg-yellow-100 text-yellow-500 mb-2">
                            <i class="fas fa-sms"></i>
                        </div>
                        <span class="text-xs text-gray-700">Send SMS</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>reports" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="p-2 rounded-full bg-red-100 text-red-500 mb-2">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <span class="text-xs text-gray-700">Generate Report</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Add any JavaScript for the dashboard here
    document.addEventListener('DOMContentLoaded', function() {
        // You can add charts or other interactive elements here
    });
</script>
