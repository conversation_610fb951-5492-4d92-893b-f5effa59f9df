<?php
/**
 * Controller Helper Functions
 * Provides consistent error handling, logging, and response patterns for controllers
 */

trait ControllerHelpers {
    protected $logger;
    
    /**
     * Initialize controller helpers
     */
    protected function initializeHelpers(): void {
        $this->logger = AppLogger::getInstance();
        $this->logger->setContext([
            'controller' => get_class($this),
            'method' => debug_backtrace()[1]['function'] ?? 'unknown'
        ]);
    }
    
    /**
     * Handle controller action with consistent error handling
     */
    protected function handleAction(callable $action, array $context = []): void {
        try {
            $this->initializeHelpers();
            $this->logger->info("Controller action started", $context);
            
            $result = $action();
            
            $this->logger->info("Controller action completed successfully", $context);
            
        } catch (SecurityException $e) {
            $this->logger->security("Security violation in controller", array_merge($context, [
                'error' => $e->getMessage(),
                'context' => $e->getContext()
            ]));
            
            $this->handleSecurityError($e);
            
        } catch (ValidationException $e) {
            $this->logger->warning("Validation error in controller", array_merge($context, [
                'error' => $e->getMessage(),
                'validation_errors' => $e->getValidationErrors()
            ]));
            
            $this->handleValidationError($e);
            
        } catch (DatabaseException $e) {
            $this->logger->error("Database error in controller", array_merge($context, [
                'error' => $e->getMessage(),
                'query' => $e->getQuery() ?? 'unknown'
            ]));
            
            $this->handleDatabaseError($e);
            
        } catch (Exception $e) {
            $this->logger->error("Unexpected error in controller", array_merge($context, [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]));
            
            $this->handleGenericError($e);
        }
    }
    
    /**
     * Handle AJAX requests with consistent error handling
     */
    protected function handleAjaxAction(callable $action, array $context = []): void {
        try {
            $this->initializeHelpers();
            $this->logger->api("AJAX action started", $context);
            
            $result = $action();
            
            if ($result !== null) {
                $this->sendJsonResponse(['success' => true, 'data' => $result]);
            }
            
        } catch (SecurityException $e) {
            $this->logger->security("Security violation in AJAX", array_merge($context, [
                'error' => $e->getMessage()
            ]));
            
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Security violation detected'
            ], 403);
            
        } catch (ValidationException $e) {
            $this->logger->warning("Validation error in AJAX", array_merge($context, [
                'error' => $e->getMessage()
            ]));
            
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Validation failed',
                'validation_errors' => $e->getValidationErrors()
            ], 400);
            
        } catch (Exception $e) {
            $this->logger->error("Error in AJAX action", array_merge($context, [
                'error' => $e->getMessage()
            ]));
            
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'An error occurred. Please try again.'
            ], 500);
        }
    }
    
    /**
     * Send JSON response
     */
    protected function sendJsonResponse(array $data, int $statusCode = 200): void {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCSRF(): void {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = $_POST['csrf_token'] ?? '';
            if (!CSRFProtection::validateToken($token)) {
                throw new SecurityException('Invalid CSRF token', ['action' => 'csrf_validation_failed']);
            }
        }
    }
    
    /**
     * Sanitize input data
     */
    protected function sanitizeInput(array $data): array {
        return InputSanitizer::sanitizeString($data);
    }
    
    /**
     * Handle security errors
     */
    protected function handleSecurityError(SecurityException $e): void {
        if ($this->isAjaxRequest()) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Security violation'], 403);
        } else {
            set_flash_message('Security violation detected.', 'danger');
            redirect('dashboard');
        }
    }
    
    /**
     * Handle validation errors
     */
    protected function handleValidationError(ValidationException $e): void {
        if ($this->isAjaxRequest()) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Validation failed',
                'validation_errors' => $e->getValidationErrors()
            ], 400);
        } else {
            set_flash_message('Please correct the errors and try again.', 'danger');
            // Redirect back or to appropriate page
            $this->redirectBack();
        }
    }
    
    /**
     * Handle database errors
     */
    protected function handleDatabaseError(DatabaseException $e): void {
        if ($this->isAjaxRequest()) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Database error occurred'], 500);
        } else {
            set_flash_message('A database error occurred. Please try again.', 'danger');
            $this->redirectBack();
        }
    }
    
    /**
     * Handle generic errors
     */
    protected function handleGenericError(Exception $e): void {
        if ($this->isAjaxRequest()) {
            $this->sendJsonResponse(['success' => false, 'error' => 'An error occurred'], 500);
        } else {
            set_flash_message('An unexpected error occurred. Please try again.', 'danger');
            $this->redirectBack();
        }
    }
    
    /**
     * Check if request is AJAX
     */
    protected function isAjaxRequest(): bool {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Redirect back to previous page
     */
    protected function redirectBack(): void {
        $referer = $_SERVER['HTTP_REFERER'] ?? BASE_URL . 'dashboard';
        redirect($referer);
    }
    
    /**
     * Log database query for debugging
     */
    protected function logQuery(string $query, array $params = []): void {
        if (EnvironmentConfig::isDebug()) {
            $this->logger->debug("Database query executed", [
                'query' => $query,
                'params' => $params
            ]);
        }
    }
}

/**
 * Base Controller Class
 */
abstract class BaseController {
    use ControllerHelpers;
    
    protected $database;
    
    public function __construct() {
        $this->database = new Database();
        $this->initializeHelpers();
    }
}
?>
