/**
 * Groups Common JavaScript Functions
 * Shared functionality across all group-related pages
 */

// Configuration constants
const GROUPS_CONFIG = {
    baseUrl: (window.BASE_URL || '/icgc/') + 'groups',
    confirmMessages: {
        delete: 'Are you sure you want to delete this item?',
        remove: 'Are you sure you want to remove this member from the group?',
        deleteSchedule: 'Are you sure you want to delete the meeting schedule? This will remove the regular meeting time for this group.',
        deleteAnnouncement: 'Are you sure you want to delete this announcement?'
    },
    selectors: {
        groupTypeSelect: '#group_type_id',
        newTypeContainer: '#newTypeContainer',
        newTypeInput: '#new_group_type'
    }
};

// Utility function to generate URLs consistently
function generateUrl(path) {
    const baseUrl = window.BASE_URL || '/icgc/';
    return baseUrl + path.replace(/^\/+/, '');
}

/**
 * Toggle new group type input visibility
 */
function toggleNewTypeInput() {
    const select = document.getElementById('group_type_id');
    const newTypeContainer = document.getElementById('newTypeContainer');
    const newTypeInput = document.getElementById('new_group_type');

    if (!select || !newTypeContainer || !newTypeInput) {
        console.warn('Required elements not found for toggleNewTypeInput');
        return;
    }

    if (select.value === 'new') {
        newTypeContainer.classList.remove('hidden');
        newTypeInput.setAttribute('required', 'required');
        newTypeInput.focus();
    } else {
        newTypeContainer.classList.add('hidden');
        newTypeInput.removeAttribute('required');
        newTypeInput.value = '';
    }
}

/**
 * Toggle new group type input for edit form (alias for consistency)
 */
function toggleNewTypeInputEdit() {
    toggleNewTypeInput();
}

/**
 * Show confirmation dialog with custom message
 * @param {string} message - Confirmation message
 * @param {function} callback - Function to execute if confirmed
 */
function showConfirmation(message, callback) {
    if (confirm(message)) {
        if (typeof callback === 'function') {
            callback();
        }
    }
}

/**
 * Redirect to URL with error handling
 * @param {string} url - URL to redirect to
 */
function safeRedirect(url) {
    try {
        if (url && typeof url === 'string') {
            window.location.href = url;
        } else {
            console.error('Invalid URL for redirect:', url);
        }
    } catch (error) {
        console.error('Error during redirect:', error);
    }
}

/**
 * Show notification - fallback function if not available elsewhere
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    // Check if a global showNotification function exists
    if (window.showNotification && typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }

    // Fallback to alert for now
    console.log(`Notification (${type}):`, message);
    alert(message);
}

/**
 * Remove member from group with confirmation
 * @param {number} memberId - Member ID
 * @param {string} memberName - Member name for confirmation
 * @param {number} groupId - Group ID
 */
function confirmRemove(memberId, memberName, groupId) {
    console.log('Global confirmRemove called with:', { memberId, memberName, groupId });
    console.log('Function arguments length:', arguments.length);

    // Check if this is being called with wrong number of parameters
    if (arguments.length < 3) {
        console.error('confirmRemove called with insufficient parameters. Expected 3, got:', arguments.length);
        console.error('This might be a function name conflict. Check if there are multiple confirmRemove functions.');

        // Try to show a helpful error message
        if (typeof showNotification === 'function') {
            showNotification('Error: Function called incorrectly. Please refresh the page and try again.', 'error');
        } else {
            alert('Error: Function called incorrectly. Please refresh the page and try again.');
        }
        return;
    }

    if (!memberId || !groupId) {
        console.error('Missing required parameters:', { memberId, groupId });
        const errorMsg = 'Error: Missing required information (Member ID or Group ID)';
        if (typeof showNotification === 'function') {
            showNotification(errorMsg, 'error');
        } else {
            alert(errorMsg);
        }
        return;
    }

    const message = `Are you sure you want to remove ${memberName} from this group?`;
    showConfirmation(message, () => {
        const url = `${GROUPS_CONFIG.baseUrl}/remove-member/${groupId}/${memberId}`;
        console.log('Submitting POST request to:', url);

        // Create a form to submit the POST request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = url;

        // Add the form to the page and submit it
        document.body.appendChild(form);
        form.submit();
    });
}

/**
 * Delete schedule with confirmation
 * @param {number} groupId - Group ID
 */
function deleteSchedule(groupId) {
    showConfirmation(GROUPS_CONFIG.confirmMessages.deleteSchedule, () => {
        safeRedirect(`${GROUPS_CONFIG.baseUrl}/delete-schedule/${groupId}`);
    });
}

/**
 * Delete announcement with confirmation
 * @param {number} announcementId - Announcement ID
 */
function deleteAnnouncement(announcementId) {
    showConfirmation(GROUPS_CONFIG.confirmMessages.deleteAnnouncement, () => {
        safeRedirect(`${GROUPS_CONFIG.baseUrl}/delete-announcement/${announcementId}`);
    });
}

/**
 * Show notification message
 * @param {string} message - Message to display
 * @param {string} type - Type of notification (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    try {
        // Check if there's a different notification function available (avoid recursion)
        if (typeof window.showBirthdayNotification === 'function') {
            window.showBirthdayNotification(message, type);
            return;
        }

        // Fallback notification
        const notification = document.createElement('div');
        const typeClasses = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };

        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transition-all duration-300 transform translate-x-full ${typeClasses[type] || typeClasses.info}`;
        notification.textContent = message;
        notification.setAttribute('role', 'alert');

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 3000);

    } catch (error) {
        console.error('Notification error:', error);
        // Fallback to alert if all else fails
        alert(message);
    }
}

/**
 * Initialize common functionality when DOM is ready
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize group type toggle if elements exist
    const groupTypeSelect = document.querySelector(GROUPS_CONFIG.selectors.groupTypeSelect);
    if (groupTypeSelect) {
        groupTypeSelect.addEventListener('change', toggleNewTypeInput);
    }

    // Add form validation for group forms
    const groupForms = document.querySelectorAll('form[action*="groups"]');
    groupForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const groupNameInput = form.querySelector('input[name="group_name"]');
            if (groupNameInput && !groupNameInput.value.trim()) {
                e.preventDefault();
                showNotification('Group name is required', 'error');
                groupNameInput.focus();
            }
        });
    });
});

// Export functions for use in other scripts
window.GroupsCommon = {
    toggleNewTypeInput,
    toggleNewTypeInputEdit,
    showConfirmation,
    safeRedirect,
    confirmRemove,
    deleteSchedule,
    deleteAnnouncement,
    showNotification,
    config: GROUPS_CONFIG
};
