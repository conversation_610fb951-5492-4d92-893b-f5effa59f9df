<div class="container mx-auto px-4 py-6 max-w-7xl fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border-2 border-gray-100">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-6 text-white">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <h1 class="text-2xl font-bold"><?php echo $report['title']; ?></h1>
                <div class="flex space-x-2 mt-4 md:mt-0">
                    <a href="<?php echo BASE_URL; ?>reports" class="bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                    </a>
                </div>
            </div>
        </div>

        <!-- Report Info -->
        <div class="p-6">
            <div class="flex flex-col md:flex-row md:justify-between">
                <div class="flex items-start">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                        <i class="fas fa-calendar-alt text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-800">Report Period</h2>
                        <p class="text-gray-600">
                            <?php echo format_date($report['start_date']); ?> to <?php echo format_date($report['end_date']); ?>
                        </p>
                    </div>
                </div>
                <div class="mt-4 md:mt-0 flex items-start">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                        <i class="fas fa-info-circle text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-800">Report Info</h2>
                        <p class="text-gray-600">
                            Generated on: <?php echo date('F j, Y, g:i a'); ?><br>
                            Generated by: <?php echo function_exists('current_user') && current_user() ? current_user()['name'] : ($_SESSION['username'] ?? 'System User'); ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Period Selection -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-md font-semibold text-gray-700 mb-3 flex items-center">
                    <i class="fas fa-filter mr-2 text-blue-600"></i> Filter by Period
                </h3>
                <div class="flex flex-wrap gap-3">
                    <a href="<?php echo BASE_URL; ?>reports/members/new?period=week" class="<?php echo $report['period'] === 'week' ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'; ?> py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                        <i class="fas fa-calendar-week mr-2"></i> This Week
                    </a>
                    <a href="<?php echo BASE_URL; ?>reports/members/new?period=month" class="<?php echo $report['period'] === 'month' ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'; ?> py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                        <i class="fas fa-calendar-alt mr-2"></i> This Month
                    </a>
                    <a href="<?php echo BASE_URL; ?>reports/members/new?period=quarter" class="<?php echo $report['period'] === 'quarter' ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'; ?> py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                        <i class="fas fa-calendar mr-2"></i> This Quarter
                    </a>
                    <a href="<?php echo BASE_URL; ?>reports/members/new?period=year" class="<?php echo $report['period'] === 'year' ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'; ?> py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                        <i class="fas fa-calendar-check mr-2"></i> This Year
                    </a>
                </div>
            </div>

            <!-- Export Options -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-md font-semibold text-gray-700 mb-3 flex items-center">
                    <i class="fas fa-file-export mr-2 text-blue-600"></i> Export Options
                </h3>
                <div class="flex flex-wrap gap-3">
                    <button onclick="printReport()" class="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                        <i class="fas fa-print mr-2 text-blue-600"></i> Print
                    </button>
                    <form action="<?php echo BASE_URL; ?>reports/generate" method="POST" class="inline">
                        <input type="hidden" name="report_type" value="member">
                        <input type="hidden" name="start_date" value="<?php echo $report['start_date']; ?>">
                        <input type="hidden" name="end_date" value="<?php echo $report['end_date']; ?>">
                        <input type="hidden" name="export_format" value="pdf">
                        <button type="submit" class="bg-red-100 hover:bg-red-200 text-gray-800 py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                            <i class="fas fa-file-pdf mr-2 text-red-600"></i> PDF
                        </button>
                    </form>
                    <form action="<?php echo BASE_URL; ?>reports/generate" method="POST" class="inline">
                        <input type="hidden" name="report_type" value="member">
                        <input type="hidden" name="start_date" value="<?php echo $report['start_date']; ?>">
                        <input type="hidden" name="end_date" value="<?php echo $report['end_date']; ?>">
                        <input type="hidden" name="export_format" value="excel">
                        <button type="submit" class="bg-green-100 hover:bg-green-200 text-gray-800 py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                            <i class="fas fa-file-excel mr-2 text-green-600"></i> Excel
                        </button>
                    </form>
                    <form action="<?php echo BASE_URL; ?>reports/generate" method="POST" class="inline">
                        <input type="hidden" name="report_type" value="member">
                        <input type="hidden" name="start_date" value="<?php echo $report['start_date']; ?>">
                        <input type="hidden" name="end_date" value="<?php echo $report['end_date']; ?>">
                        <input type="hidden" name="export_format" value="csv">
                        <button type="submit" class="bg-blue-100 hover:bg-blue-200 text-gray-800 py-2 px-4 rounded-lg flex items-center transition-all duration-300">
                            <i class="fas fa-file-csv mr-2 text-blue-600"></i> CSV
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Content -->
    <div id="report-content" class="bg-white rounded-xl shadow-md overflow-hidden border-2 border-gray-100">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-blue-100 text-blue-600 mr-3">
                    <i class="fas fa-user-plus text-xl"></i>
                </div>
                <h2 class="text-lg font-semibold text-gray-800">New Members - <?php echo $report['period_text']; ?></h2>
            </div>
        </div>

        <?php if (empty($report['data'])) : ?>
            <div class="p-12 text-center">
                <div class="inline-flex rounded-full bg-yellow-100 p-4 mb-4">
                    <div class="rounded-full bg-yellow-200 p-4">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-3xl"></i>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">No New Members Found</h3>
                <p class="text-gray-500 max-w-md mx-auto">There are no new members registered during the selected period. Try selecting a different time period.</p>
                <a href="<?php echo BASE_URL; ?>reports" class="mt-6 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                    <i class="fas fa-redo mr-2"></i> Try Another Report
                </a>
            </div>
        <?php else : ?>
            <!-- Member Report -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <?php foreach ($report['columns'] as $column) : ?>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?php echo $column; ?></th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($report['data'] as $member) : ?>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <?php echo $member['id']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <?php echo $member['first_name'] . ' ' . $member['last_name']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $member['phone_number']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $member['email'] ?: 'N/A'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo ucfirst(str_replace('_', ' ', $member['department'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo format_date($member['membership_date']); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Summary -->
            <div class="p-6 bg-gray-50 border-t border-gray-200">
                <div class="flex items-center">
                    <div class="p-2 rounded-full bg-blue-100 text-blue-600 mr-3">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div>
                        <h3 class="text-md font-semibold text-gray-800">Report Summary</h3>
                        <p class="text-gray-600 mt-1">
                            <span class="font-medium">Total New Members:</span> <?php echo count($report['data']); ?>
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    // Print report with enhanced styling
    function printReport() {
        const printContents = document.getElementById('report-content').innerHTML;
        const originalContents = document.body.innerHTML;

        document.body.innerHTML = `
            <div style="padding: 20px; font-family: Arial, sans-serif;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #3F7D58; margin-bottom: 10px;"><?php echo $report['title']; ?></h1>
                    <p style="color: #666; font-size: 14px;">
                        Period: <?php echo format_date($report['start_date']); ?> to <?php echo format_date($report['end_date']); ?><br>
                        Generated on: <?php echo date('F j, Y, g:i a'); ?><br>
                        Generated by: <?php echo function_exists('current_user') && current_user() ? current_user()['name'] : ($_SESSION['username'] ?? 'System User'); ?>
                    </p>
                </div>
                <div style="border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;">
                    <div style="background-color: #f7fafc; padding: 15px; border-bottom: 1px solid #e2e8f0;">
                        <h2 style="margin: 0; color: #2d3748; font-size: 18px;">New Members - <?php echo $report['period_text']; ?></h2>
                    </div>
                    ${printContents}
                </div>
                <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #718096;">
                    <p>ICGC Emmanuel Temple - Church Management System</p>
                </div>
            </div>
        `;

        window.print();
        document.body.innerHTML = originalContents;
    }
</script>
