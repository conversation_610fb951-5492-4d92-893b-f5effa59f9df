<?php
/**
 * Partial view for displaying flash messages.
 *
 * This partial handles the logic for displaying and styling flash messages
 * from the session, supporting both array and string formats for flexibility.
 */

if (empty($_SESSION['flash_message'])) {
    return;
}

// Handle both array and string flash message formats for flexibility
$message = is_array($_SESSION['flash_message']) ? ($_SESSION['flash_message']['message'] ?? '') : (string) $_SESSION['flash_message'];
$type = is_array($_SESSION['flash_message']) ? ($_SESSION['flash_message']['type'] ?? 'info') : ($_SESSION['flash_type'] ?? 'info');

$alert_classes = [
    'success' => 'bg-green-100 border-green-500 text-green-700',
    'danger'  => 'bg-red-100 border-red-500 text-red-700',
    'warning' => 'bg-yellow-100 border-yellow-500 text-yellow-700',
    'info'    => 'bg-blue-100 border-blue-500 text-blue-700',
];

$class_string = $alert_classes[$type] ?? $alert_classes['info'];
?>
<div class="flash-message border-l-4 p-4 mb-4 <?php echo $class_string; ?>" role="alert">
    <p><?php echo htmlspecialchars($message); ?></p>
</div>
<?php
// Clean up session variables after displaying
unset($_SESSION['flash_message'], $_SESSION['flash_type']);
