/**
 * ULTIMATE Chart.js Conflict Prevention System
 * NUCLEAR APPROACH: Completely replaces Chart.js to prevent ALL conflicts
 */

// IMMEDIATE CHART.JS TAKEOVER - Must run before ANY other chart script
(function() {
    'use strict';

    console.log('🚀 ICGC Chart Takeover System - Initializing...');

    // Prevent multiple executions
    if (window.ICGC_CHART_TAKEOVER_ACTIVE) {
        console.log('Chart takeover already active, skipping...');
        return;
    }
    window.ICGC_CHART_TAKEOVER_ACTIVE = true;

    // Global chart registry
    window.ICGC_CHARTS = window.ICGC_CHARTS || {};

    // Store original Chart constructor if it exists
    const OriginalChart = window.Chart;

    // NUCLEAR OPTION: Completely replace Chart constructor
    window.Chart = function(ctx, config) {
        console.log('🎯 Chart creation intercepted');

        // Handle different context types
        let canvas, canvasId;
        if (typeof ctx === 'string') {
            canvas = document.getElementById(ctx);
            canvasId = ctx;
        } else if (ctx && ctx.canvas) {
            canvas = ctx.canvas;
            canvasId = canvas.id || 'unknown-' + Date.now();
        } else if (ctx && ctx.id) {
            canvas = ctx;
            canvasId = ctx.id || 'unknown-' + Date.now();
        } else {
            console.error('Invalid chart context:', ctx);
            return null;
        }

        console.log('📊 Creating chart on canvas:', canvasId);

        // DESTROY ALL EXISTING CHARTS ON THIS CANVAS
        try {
            // Method 1: Our registry
            if (window.ICGC_CHARTS[canvasId]) {
                console.log('🗑️ Destroying our registered chart:', canvasId);
                window.ICGC_CHARTS[canvasId].destroy();
                delete window.ICGC_CHARTS[canvasId];
            }

            // Method 2: Chart.js registry (if original Chart exists)
            if (OriginalChart && OriginalChart.getChart && canvas) {
                const existingChart = OriginalChart.getChart(canvas);
                if (existingChart) {
                    console.log('🗑️ Destroying Chart.js registry chart:', canvasId);
                    existingChart.destroy();
                }
            }

            // Method 3: Brute force - check all Chart instances
            if (OriginalChart && OriginalChart.instances) {
                Object.keys(OriginalChart.instances).forEach(key => {
                    const chart = OriginalChart.instances[key];
                    if (chart && chart.canvas === canvas) {
                        console.log('🗑️ Destroying Chart instance:', key);
                        chart.destroy();
                    }
                });
            }

        } catch (error) {
            console.warn('Error during chart cleanup:', error);
        }

        // CREATE NEW CHART
        let newChart;
        try {
            if (OriginalChart) {
                // Use original Chart.js constructor
                newChart = new OriginalChart(ctx, config);
            } else {
                console.error('Original Chart constructor not available');
                return null;
            }

            // Store in our registry
            window.ICGC_CHARTS[canvasId] = newChart;
            console.log('✅ Chart created and registered:', canvasId);

            return newChart;

        } catch (error) {
            console.error('Error creating chart:', error);
            return null;
        }
    };

    // Copy ALL static methods and properties from original Chart
    if (OriginalChart) {
        Object.setPrototypeOf(window.Chart, OriginalChart);
        Object.assign(window.Chart, OriginalChart);

        // Ensure static methods work
        window.Chart.getChart = OriginalChart.getChart;
        window.Chart.register = OriginalChart.register;
        window.Chart.unregister = OriginalChart.unregister;
        window.Chart.defaults = OriginalChart.defaults;
        window.Chart.instances = OriginalChart.instances;
    }

    // Disable other chart scripts to prevent conflicts
    window.DISABLE_OTHER_CHART_SCRIPTS = true;

    // Override common chart initialization functions from other scripts
    window.initializeCharts = function() {
        console.log('🚫 Blocked initializeCharts() from other script - using ICGC Chart Manager');
    };

    window.initializeFinancialChart = function() {
        console.log('🚫 Blocked initializeFinancialChart() from other script - using ICGC Chart Manager');
    };

    window.initializeDashboardCharts = function() {
        console.log('🚫 Blocked initializeDashboardCharts() from other script - using ICGC Chart Manager');
    };

    console.log('🛡️ Chart.js takeover complete - ALL conflicts prevented');

    // Dashboard chart initialization flag
    let dashboardChartsInitialized = false;

    // Wait for everything to load
    window.addEventListener('load', function() {
        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not available');
            return;
        }

        // Prevent multiple dashboard chart initializations
        if (dashboardChartsInitialized) {
            console.log('Dashboard charts already initialized, skipping...');
            return;
        }

        // Only initialize if we're on the dashboard page
        if (document.getElementById('gender-chart') ||
            document.getElementById('age-chart') ||
            document.getElementById('department-chart')) {

            console.log('Dashboard detected, initializing dashboard charts...');
            initializeDashboardCharts();
            dashboardChartsInitialized = true;
        }
    });

    // Simplified chart creation function
    function initializeDashboardCharts() {
        console.log('Creating dashboard charts with global manager...');
        // Gender Chart
        const genderCanvas = document.getElementById('gender-chart');
        if (genderCanvas) {
            console.log('Creating gender chart...');

            // Get data from DOM
            const maleElement = document.querySelector('[data-gender="male"]');
            const femaleElement = document.querySelector('[data-gender="female"]');

            let malePercentage = 47; // Default values
            let femalePercentage = 53;

            if (maleElement && femaleElement) {
                malePercentage = parseFloat(maleElement.textContent) || 47;
                femalePercentage = parseFloat(femaleElement.textContent) || 53;
            }

            // Chart will be automatically managed by global system
            new Chart(genderCanvas, {
                type: 'doughnut',
                data: {
                    labels: ['Male', 'Female'],
                    datasets: [{
                        data: [malePercentage, femalePercentage],
                        backgroundColor: ['#3b82f6', '#ec4899'],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            console.log('Gender chart created successfully');
        }
    
        // Age Chart
        const ageCanvas = document.getElementById('age-chart');
        if (ageCanvas) {
            console.log('Creating age chart...');

            // Default age data
            const ageData = [15, 40, 35, 10]; // Under 18, 18-35, 36-60, Over 60
            const ageLabels = ['Under 18', '18-35', '36-60', 'Over 60'];

            // Chart will be automatically managed by global system
            new Chart(ageCanvas, {
                type: 'doughnut',
                data: {
                    labels: ageLabels,
                    datasets: [{
                        data: ageData,
                        backgroundColor: ['#10b981', '#3b82f6', '#8b5cf6', '#f59e0b'],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            console.log('Age chart created successfully');
        }
    
        // Department Chart
        const deptCanvas = document.getElementById('department-chart');
        if (deptCanvas) {
            console.log('Creating department chart...');

            // Get department data from DOM
            const deptNameElements = document.querySelectorAll('[data-dept-name]');
            const deptCountElements = document.querySelectorAll('[data-dept-count]');

            let deptLabels = ['Choir', 'Ushering', 'Youth']; // Default
            let deptData = [120, 95, 80]; // Default

            if (deptNameElements.length > 0 && deptCountElements.length > 0) {
                deptLabels = Array.from(deptNameElements).map(el => el.textContent);
                deptData = Array.from(deptCountElements).map(el => parseInt(el.textContent) || 0);
            }

            const colors = ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ef4444', '#06b6d4'];

            // Chart will be automatically managed by global system
            new Chart(deptCanvas, {
                type: 'bar',
                data: {
                    labels: deptLabels,
                    datasets: [{
                        label: 'Members',
                        data: deptData,
                        backgroundColor: colors.slice(0, deptLabels.length).map(color => color + '40'),
                        borderColor: colors.slice(0, deptLabels.length),
                        borderWidth: 2,
                        borderRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
            console.log('Department chart created successfully');
        }

        console.log('All dashboard charts initialized successfully');
    }

    // Export functions for global access
    window.initializeDashboardCharts = initializeDashboardCharts;
    window.ICGC_CHART_MANAGER = {
        charts: window.ICGC_CHARTS,
        destroyAll: function() {
            Object.values(window.ICGC_CHARTS).forEach(chart => {
                if (chart && typeof chart.destroy === 'function') {
                    chart.destroy();
                }
            });
            window.ICGC_CHARTS = {};
            console.log('All ICGC charts destroyed');
        },
        reinitialize: function() {
            this.destroyAll();
            if (typeof initializeDashboardCharts === 'function') {
                initializeDashboardCharts();
            }
        }
    };

})(); // End of global chart manager

console.log('ICGC Chart Management System loaded successfully');


