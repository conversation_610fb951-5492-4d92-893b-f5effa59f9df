<?php
// Make sure we have the database connection for the category dropdown
require_once 'config/database.php';
require_once 'utils/helpers.php';
?>
<div class="container mx-auto px-4 py-6 max-w-7xl">
    <!-- Enhanced Hero Banner -->
    <div class="bg-gradient-to-r from-amber-600 to-amber-500 rounded-xl shadow-lg p-6 mb-8 text-white overflow-hidden relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
                <defs>
                    <pattern id="pattern" width="40" height="40" patternUnits="userSpaceOnUse">
                        <circle cx="20" cy="20" r="2" fill="#fff"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#pattern)"/>
            </svg>
        </div>

        <div class="flex flex-col md:flex-row justify-between items-start md:items-center relative z-10">
            <div class="mb-6 md:mb-0">
                <div class="flex items-center mb-3">
                    <div class="bg-white p-3 rounded-full shadow-md mr-4 flex items-center justify-center">
                        <i class="fas fa-archive text-amber-500 text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold mb-1">Archived Transactions</h1>
                        <p class="opacity-90">View historical financial records</p>
                    </div>
                </div>

                <!-- Transaction Stats -->
                <div class="mt-4 flex flex-wrap gap-4">
                    <div class="bg-white/20 py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <span>Historical Data</span>
                    </div>
                    <div class="bg-white/20 py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-receipt mr-2"></i>
                        <span>Total Records: <span class="font-semibold"><?php echo count($transactions); ?></span></span>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap gap-3">
                <a href="<?php echo BASE_URL; ?>finance/transactions" class="bg-white text-amber-600 hover:bg-gray-100 py-3 px-6 rounded-lg flex items-center text-sm font-medium transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
                    <i class="fas fa-list-alt mr-2"></i> Current Transactions
                </a>
                <a href="<?php echo BASE_URL; ?>finance" class="bg-white/20 hover:bg-white/30 text-white py-2.5 px-5 rounded-lg flex items-center text-sm font-medium transition-all duration-300">
                    <i class="fas fa-chart-pie mr-2"></i> Finance Dashboard
                </a>
            </div>
        </div>
    </div>

    <?php if (!$archivedExists): ?>
    <!-- No Archive Table Exists Yet -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 p-8 text-center">
        <div class="mb-4">
            <div class="bg-amber-100 text-amber-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-archive text-2xl"></i>
            </div>
            <h2 class="text-xl font-bold text-gray-800 mb-2">No Archived Transactions Yet</h2>
            <p class="text-gray-600 max-w-lg mx-auto mb-6">
                You haven't archived any transactions yet. Archiving helps improve system performance by moving older transactions to a separate storage area.
            </p>
            <?php if (isset($_SESSION['user_role']) && ($_SESSION['user_role'] === 'admin' || $_SESSION['user_role'] === 'super_admin')): ?>
            <a href="<?php echo BASE_URL; ?>finance/archive-old" class="bg-amber-500 hover:bg-amber-600 text-white py-2 px-6 rounded-lg inline-flex items-center text-sm font-medium transition-all duration-300 shadow-md">
                <i class="fas fa-archive mr-2"></i> Archive Old Transactions
            </a>
            <?php endif; ?>
        </div>
    </div>
    <?php else: ?>

    <!-- Simplified Filter Bar -->
    <div class="bg-white rounded-lg shadow-md p-5 mb-6 border border-gray-200">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <div class="bg-amber-100 p-2 rounded-full mr-3">
                    <i class="fas fa-filter text-amber-500"></i>
                </div>
                <h3 class="text-base font-medium text-gray-800">Quick Filter</h3>
            </div>
            <button id="toggle-filters" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 py-1.5 px-3 rounded-full flex items-center transition-colors duration-200 focus:outline-none">
                <span id="toggle-text">Show Options</span>
                <i class="fas fa-chevron-down ml-1.5 text-xs"></i>
            </button>
        </div>

        <!-- Always visible search bar -->
        <div class="relative mt-3 mb-4">
            <input type="text" id="table-search" class="pl-10 w-full rounded-full border-2 border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-20 text-sm py-3 px-4 hover:border-amber-400 transition-colors duration-200" placeholder="Search archived transactions...">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-500"></i>
            </div>
            <button id="reset-search" class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200 focus:outline-none">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Expandable advanced filters -->
        <div id="filters-container" class="hidden mt-4 pt-4 border-t border-gray-200">
            <form action="<?php echo BASE_URL; ?>finance/archived" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <div class="relative">
                        <select id="category-filter" name="category" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-20 text-sm pl-9 pr-9 py-2.5 appearance-none hover:border-amber-400 transition-colors duration-200">
                            <option value="">All Categories</option>
                            <option value="tithe" <?php echo isset($_GET['category']) && $_GET['category'] === 'tithe' ? 'selected' : ''; ?>>Tithe</option>
                            <option value="offering" <?php echo isset($_GET['category']) && $_GET['category'] === 'offering' ? 'selected' : ''; ?>>Offering</option>
                            <option value="project_offering" <?php echo isset($_GET['category']) && $_GET['category'] === 'project_offering' ? 'selected' : ''; ?>>Project Offering</option>
                            <option value="donation" <?php echo isset($_GET['category']) && $_GET['category'] === 'donation' ? 'selected' : ''; ?>>Donation</option>
                            <option value="seed" <?php echo isset($_GET['category']) && $_GET['category'] === 'seed' ? 'selected' : ''; ?>>Seed</option>
                            <option value="pladge" <?php echo isset($_GET['category']) && $_GET['category'] === 'pladge' ? 'selected' : ''; ?>>Pledge</option>
                            <option value="welfare" <?php echo isset($_GET['category']) && $_GET['category'] === 'welfare' ? 'selected' : ''; ?>>Welfare</option>
                            <option value="others" <?php echo isset($_GET['category']) && $_GET['category'] === 'others' ? 'selected' : ''; ?>>Others</option>
                            <option value="expense" <?php echo isset($_GET['category']) && $_GET['category'] === 'expense' ? 'selected' : ''; ?>>Expense</option>
                            <?php
                            // Get any additional categories from the database that might not be in our hardcoded list
                            $db = new Database();
                            $conn = $db->getConnection();
                            $stmt = $conn->query('SELECT DISTINCT category FROM finances_archive WHERE category NOT IN ("tithe", "offering", "project_offering", "donation", "seed", "pladge", "welfare", "others", "expense") ORDER BY category');
                            $additionalCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);

                            // Add any additional categories found
                            foreach ($additionalCategories as $cat) {
                                $displayCat = ucfirst(str_replace('_', ' ', $cat));
                                $selected = isset($_GET['category']) && $_GET['category'] === $cat ? 'selected' : '';
                                echo "<option value=\"$cat\" $selected>$displayCat</option>";
                            }
                            ?>
                        </select>
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-tag text-gray-500 text-xs"></i>
                        </div>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-500 text-xs"></i>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <div class="relative flex-1">
                        <input type="date" id="start-date" name="start_date" value="<?php echo isset($_GET['start_date']) ? htmlspecialchars($_GET['start_date']) : ''; ?>" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-20 text-sm pl-9 py-2.5 hover:border-amber-400 transition-colors duration-200" placeholder="From">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-500 text-xs"></i>
                        </div>
                    </div>
                    <div class="relative flex-1">
                        <input type="date" id="end-date" name="end_date" value="<?php echo isset($_GET['end_date']) ? htmlspecialchars($_GET['end_date']) : ''; ?>" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-20 text-sm pl-9 py-2.5 hover:border-amber-400 transition-colors duration-200" placeholder="To">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-500 text-xs"></i>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <input type="text" id="search-input" name="search" value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-20 text-sm pl-9 py-2.5 hover:border-amber-400 transition-colors duration-200" placeholder="Search by description or member">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-500 text-xs"></i>
                    </div>
                </div>
                <div class="md:col-span-3 flex justify-end">
                    <a href="<?php echo BASE_URL; ?>finance/archived" class="bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm py-2.5 px-4 rounded-md mr-3 transition-colors duration-200 flex items-center border border-gray-200 shadow-sm">
                        <i class="fas fa-undo-alt mr-2 text-xs"></i> Reset
                    </a>
                    <button type="submit" class="bg-amber-500 hover:bg-amber-600 text-white text-sm py-2.5 px-4 rounded-md transition-colors duration-200 flex items-center shadow-sm">
                        <i class="fas fa-filter mr-2 text-xs"></i> Apply
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Finances Table -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-all duration-300 animate-fadeIn" style="animation-delay: 0.2s;">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
            <div>
                <h3 class="text-base font-bold text-gray-800">Archived Transaction Records</h3>
                <p class="text-xs text-gray-600">Showing <span class="font-medium"><?php echo count($finances); ?></span> of <span class="font-medium"><?php echo $totalCount; ?></span> archived transactions</p>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 transactions-table">
                <thead>
                    <tr>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Archived</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($finances)) : ?>
                        <tr>
                            <td colspan="6" class="px-4 py-8 text-center">
                                <div class="flex flex-col items-center justify-center text-gray-500 py-4">
                                    <p class="text-base font-bold text-gray-700 mb-1">No Archived Records Found</p>
                                    <p class="text-gray-500 max-w-md mb-4 text-sm">No archived transactions found matching your search criteria.</p>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php $rowIndex = 0; ?>
                        <?php foreach ($finances as $finance) : ?>
                            <?php $rowIndex++; ?>
                            <tr class="<?php echo $rowIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'; ?> hover:bg-amber-50 transition-all duration-200 group">
                                <!-- Date Column -->
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-semibold text-gray-900"><?php echo format_date($finance['transaction_date']); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($finance['created_at'])); ?></div>
                                    </div>
                                </td>

                                <!-- Category Column -->
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <?php
                                    $categoryClass = '';
                                    $categoryIcon = '';
                                    $categoryBgClass = '';
                                    $categoryBorderClass = '';

                                    if ($finance['category'] === 'expense') {
                                        $categoryClass = 'text-red-600';
                                        $categoryIcon = 'fa-arrow-circle-down';
                                        $categoryBgClass = 'bg-red-50';
                                        $categoryBorderClass = 'border-red-100';
                                    } else {
                                        $categoryClass = 'text-green-600';
                                        $categoryIcon = 'fa-arrow-circle-up';
                                        $categoryBgClass = 'bg-green-50';
                                        $categoryBorderClass = 'border-green-100';
                                    }
                                    ?>
                                    <div class="flex items-center">
                                        <div class="<?php echo $categoryBgClass; ?> p-2 rounded-full mr-3">
                                            <i class="fas <?php echo $categoryIcon; ?> <?php echo $categoryClass; ?>"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php
                                                // Format the category for display
                                                $displayCategory = str_replace('_', ' ', $finance['category']);
                                                $displayCategory = ucwords($displayCategory);
                                                echo $displayCategory;
                                                ?>
                                            </div>
                                            <div class="text-xs <?php echo $categoryClass; ?>">
                                                <?php echo $finance['category'] === 'expense' ? 'Expense' : 'Income'; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>

                                <!-- Amount Column -->
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="text-sm font-bold <?php echo $finance['category'] === 'expense' ? 'text-red-600' : 'text-green-600'; ?>">
                                        GH₵ <?php echo number_format($finance['amount'], 2); ?>
                                    </div>
                                </td>

                                <!-- Description Column -->
                                <td class="px-4 py-3">
                                    <div class="text-sm text-gray-900 max-w-xs truncate" title="<?php echo htmlspecialchars($finance['description']); ?>">
                                        <?php echo htmlspecialchars($finance['description']); ?>
                                    </div>
                                </td>

                                <!-- Member Column -->
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <?php if (!empty($finance['member_id']) && !empty($finance['member_name'])) : ?>
                                        <a href="<?php echo BASE_URL; ?>members/view?id=<?php echo $finance['member_id']; ?>" class="text-sm text-blue-600 hover:text-blue-800 hover:underline">
                                            <?php echo htmlspecialchars($finance['member_name']); ?>
                                        </a>
                                    <?php else : ?>
                                        <span class="text-sm text-gray-500">N/A</span>
                                    <?php endif; ?>
                                </td>

                                <!-- Archived Date Column -->
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="text-xs text-gray-500">
                                        <span class="bg-amber-100 text-amber-800 px-2 py-1 rounded-full">
                                            <?php echo date('M d, Y', strtotime($finance['archived_at'])); ?>
                                        </span>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <!-- Pagination Controls -->
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium"><?php echo count($finances) > 0 ? ($page - 1) * $limit + 1 : 0; ?></span> to <span class="font-medium"><?php echo min($page * $limit, $totalCount); ?></span> of <span class="font-medium"><?php echo $totalCount; ?></span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <!-- Previous Page Link -->
                            <?php if ($page > 1): ?>
                                <a href="<?php echo BASE_URL; ?>finance/archived?page=<?php echo $page - 1; ?><?php echo isset($_GET['limit']) ? '&limit=' . htmlspecialchars($_GET['limit']) : ''; ?><?php echo isset($_GET['category']) ? '&category=' . htmlspecialchars($_GET['category']) : ''; ?><?php echo isset($_GET['start_date']) ? '&start_date=' . htmlspecialchars($_GET['start_date']) : ''; ?><?php echo isset($_GET['end_date']) ? '&end_date=' . htmlspecialchars($_GET['end_date']) : ''; ?><?php echo isset($_GET['search']) ? '&search=' . htmlspecialchars($_GET['search']) : ''; ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php else: ?>
                                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $startPage + 4);
                            if ($endPage - $startPage < 4 && $totalPages > 5) {
                                $startPage = max(1, $endPage - 4);
                            }

                            // Build query string for pagination links
                            $queryParams = [];
                            if (isset($_GET['limit'])) $queryParams['limit'] = htmlspecialchars($_GET['limit']);
                            if (isset($_GET['category'])) $queryParams['category'] = htmlspecialchars($_GET['category']);
                            if (isset($_GET['start_date'])) $queryParams['start_date'] = htmlspecialchars($_GET['start_date']);
                            if (isset($_GET['end_date'])) $queryParams['end_date'] = htmlspecialchars($_GET['end_date']);
                            if (isset($_GET['search'])) $queryParams['search'] = htmlspecialchars($_GET['search']);

                            $queryString = '';
                            foreach ($queryParams as $key => $value) {
                                $queryString .= "&{$key}={$value}";
                            }

                            for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="relative inline-flex items-center px-4 py-2 border border-amber-500 bg-amber-50 text-sm font-medium text-amber-700">
                                        <?php echo $i; ?>
                                    </span>
                                <?php else: ?>
                                    <a href="<?php echo BASE_URL; ?>finance/archived?page=<?php echo $i; ?><?php echo $queryString; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <!-- Next Page Link -->
                            <?php if ($page < $totalPages): ?>
                                <a href="<?php echo BASE_URL; ?>finance/archived?page=<?php echo $page + 1; ?><?php echo $queryString; ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php else: ?>
                                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle filters
        const toggleFiltersButton = document.getElementById('toggle-filters');
        const filtersContainer = document.getElementById('filters-container');
        const toggleText = document.getElementById('toggle-text');

        if (toggleFiltersButton && filtersContainer && toggleText) {
            toggleFiltersButton.addEventListener('click', function() {
                const icon = this.querySelector('i');

                if (filtersContainer.classList.contains('hidden')) {
                    filtersContainer.classList.remove('hidden');
                    toggleText.textContent = 'Hide Options';
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    filtersContainer.classList.add('hidden');
                    toggleText.textContent = 'Show Options';
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            });
        }

        // Client-side search
        const searchInput = document.getElementById('table-search');
        const resetSearchBtn = document.getElementById('reset-search');
        const tableRows = document.querySelectorAll('tbody tr');

        if (searchInput && resetSearchBtn && tableRows.length > 0) {
            // Function to filter table rows based on search text
            function filterTable(searchText) {
                const lowerSearchText = searchText.toLowerCase();
                let visibleCount = 0;

                tableRows.forEach(row => {
                    // Skip the "no records" row if it exists
                    if (row.cells.length === 1 && row.cells[0].colSpan > 1) {
                        return;
                    }

                    const text = row.textContent.toLowerCase();
                    if (text.includes(lowerSearchText)) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Show/hide reset button
                if (searchText) {
                    resetSearchBtn.style.display = 'flex';
                } else {
                    resetSearchBtn.style.display = 'none';
                }

                // Update the count of visible transactions
                updateVisibleCount(visibleCount);
            }

            // Function to update the visible count display
            function updateVisibleCount(visibleCount) {
                const countElement = document.querySelector('.text-xs.text-gray-600 span.font-medium');
                if (countElement) {
                    countElement.textContent = visibleCount;
                }
            }

            // Initialize reset button visibility
            resetSearchBtn.style.display = searchInput.value ? 'flex' : 'none';

            // Add event listeners for real-time search
            searchInput.addEventListener('input', function() {
                filterTable(this.value);
            });

            // Reset search
            resetSearchBtn.addEventListener('click', function() {
                searchInput.value = '';
                filterTable('');
                searchInput.focus();
            });

            // Initialize table filtering with any existing search text
            filterTable(searchInput.value);
        }
    });
</script>
