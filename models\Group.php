<?php
/**
 * Group Model
 * Handles all database operations related to church groups
 */
require_once 'utils/db_helpers.php';
require_once 'config/database.php';

class Group {
    private $conn;
    private $table_name = 'groups';

    // Error handling
    public $error;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Get all groups
     * @param string $status Filter by status (active, inactive, or all)
     * @return array Array of groups
     */
    public function getAllGroups($status = 'active') {
        $sql = "SELECT g.*, gt.type_name,
                (SELECT COUNT(*) FROM member_groups WHERE group_id = g.group_id AND status = 'active') as member_count,
                (SELECT group_name FROM groups WHERE group_id = g.parent_group_id) as parent_group_name
                FROM groups g
                LEFT JOIN group_types gt ON g.group_type_id = gt.group_type_id";

        if ($status !== 'all') {
            $sql .= " WHERE g.status = :status";
            $params = [':status' => $status];
            return fetchAll($this->conn, $sql, $params);
        }

        return fetchAll($this->conn, $sql);
    }

    /**
     * Get a single group by ID
     * @param int $groupId Group ID
     * @return object|bool Group object or false if not found
     */
    public function getGroupById($groupId) {
        $sql = "SELECT g.*, gt.type_name,
                (SELECT COUNT(*) FROM member_groups WHERE group_id = g.group_id AND status = 'active') as member_count,
                (SELECT group_name FROM groups WHERE group_id = g.parent_group_id) as parent_group_name
                FROM groups g
                LEFT JOIN group_types gt ON g.group_type_id = gt.group_type_id
                WHERE g.group_id = :group_id";

        $params = [':group_id' => $groupId];
        return fetchOne($this->conn, $sql, $params);
    }

    /**
     * Validate group data before create/update operations
     *
     * @param array $data Group data to validate
     * @return bool True if validation passes, false otherwise
     */
    public function validate($data) {
        $this->error = null;

        // Required field validation
        if (empty($data['group_name'])) {
            $this->error = 'Group name is required.';
            return false;
        }

        // Group name format validation
        if (!preg_match('/^[a-zA-Z0-9\s\'-\.&()]+$/', $data['group_name'])) {
            $this->error = 'Group name contains invalid characters.';
            return false;
        }

        // Group name length validation
        if (strlen($data['group_name']) > 100) {
            $this->error = 'Group name cannot exceed 100 characters.';
            return false;
        }

        // Description length validation (if provided)
        if (!empty($data['group_description']) && strlen($data['group_description']) > 500) {
            $this->error = 'Group description cannot exceed 500 characters.';
            return false;
        }

        // Group type validation (if provided)
        if (!empty($data['group_type_id'])) {
            if (!is_numeric($data['group_type_id']) || $data['group_type_id'] <= 0) {
                $this->error = 'Invalid group type selected.';
                return false;
            }

            // Check if group type exists
            if (!$this->groupTypeExists($data['group_type_id'])) {
                $this->error = 'Selected group type does not exist.';
                return false;
            }
        }

        // Parent group validation (if provided)
        if (!empty($data['parent_group_id'])) {
            if (!is_numeric($data['parent_group_id']) || $data['parent_group_id'] <= 0) {
                $this->error = 'Invalid parent group selected.';
                return false;
            }

            // Check if parent group exists
            if (!$this->groupExists($data['parent_group_id'])) {
                $this->error = 'Selected parent group does not exist.';
                return false;
            }
        }

        // Status validation (if provided)
        if (!empty($data['status'])) {
            $valid_statuses = ['active', 'inactive', 'suspended'];
            if (!in_array(strtolower($data['status']), $valid_statuses)) {
                $this->error = 'Invalid group status.';
                return false;
            }
        }

        return true;
    }

    /**
     * Check if a group type exists
     * @param int $groupTypeId Group type ID
     * @return bool True if exists, false otherwise
     */
    private function groupTypeExists($groupTypeId) {
        $sql = "SELECT COUNT(*) FROM group_types WHERE group_type_id = :group_type_id";
        $stmt = $this->conn->prepare($sql);
        $stmt->bindParam(':group_type_id', $groupTypeId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Check if a group exists
     * @param int $groupId Group ID
     * @return bool True if exists, false otherwise
     */
    private function groupExists($groupId) {
        $sql = "SELECT COUNT(*) FROM groups WHERE group_id = :group_id";
        $stmt = $this->conn->prepare($sql);
        $stmt->bindParam(':group_id', $groupId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Create a new group with validation
     * @param array $data Group data
     * @return int|bool New group ID or false on failure
     */
    public function createGroup($data) {
        // Validate data before creating
        if (!$this->validate($data)) {
            return false;
        }
        $sql = "INSERT INTO groups (group_name, group_description, group_type_id, parent_group_id, status)
                VALUES (:group_name, :group_description, :group_type_id, :parent_group_id, :status)";

        $params = [
            ':group_name' => $data['group_name'],
            ':group_description' => $data['group_description'] ?? null,
            ':group_type_id' => $data['group_type_id'] ?? null,
            ':parent_group_id' => !empty($data['parent_group_id']) ? $data['parent_group_id'] : null,
            ':status' => $data['status'] ?? 'active'
        ];

        try {
            $result = executeInsert($this->conn, $sql, $params);
            if ($result === false) {
                $this->error = 'Failed to create group record.';
                return false;
            }
            return $result;
        } catch (PDOException $e) {
            // Check for duplicate group name error
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'group_name') !== false) {
                $this->error = 'A group with this name already exists.';
            } else {
                $this->error = 'Database error occurred while creating group.';
                error_log("Group creation failed: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Update an existing group with validation
     * @param int $groupId Group ID
     * @param array $data Group data
     * @return bool Success or failure
     */
    public function updateGroup($groupId, $data) {
        // Validate data before updating
        if (!$this->validate($data)) {
            return false;
        }
        $sql = "UPDATE groups SET
                group_name = :group_name,
                group_description = :group_description,
                group_type_id = :group_type_id,
                parent_group_id = :parent_group_id,
                status = :status
                WHERE group_id = :group_id";

        $params = [
            ':group_id' => $groupId,
            ':group_name' => $data['group_name'],
            ':group_description' => $data['group_description'] ?? null,
            ':group_type_id' => $data['group_type_id'] ?? null,
            ':parent_group_id' => !empty($data['parent_group_id']) ? $data['parent_group_id'] : null,
            ':status' => $data['status'] ?? 'active'
        ];

        try {
            $result = executeUpdate($this->conn, $sql, $params);
            if ($result === false) {
                $this->error = 'Failed to update group record.';
                return false;
            }
            return $result;
        } catch (PDOException $e) {
            // Check for duplicate group name error
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'group_name') !== false) {
                $this->error = 'A group with this name already exists.';
            } else {
                $this->error = 'Database error occurred while updating group.';
                error_log("Group update failed: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Delete a group
     * @param int $groupId Group ID
     * @return bool Success or failure
     */
    public function deleteGroup($groupId) {
        // First check if there are any members in this group
        $sql = "SELECT COUNT(*) as count FROM member_groups WHERE group_id = :group_id";
        $params = [':group_id' => $groupId];
        $result = fetchOne($this->conn, $sql, $params);

        if ($result && $result->count > 0) {
            // Group has members, just mark it as inactive
            $sql = "UPDATE groups SET status = 'inactive' WHERE group_id = :group_id";
            return executeUpdate($this->conn, $sql, $params);
        } else {
            // No members, safe to delete
            $sql = "DELETE FROM groups WHERE group_id = :group_id";
            return executeUpdate($this->conn, $sql, $params);
        }
    }

    /**
     * Get all group types
     * @return array Array of group types
     */
    public function getAllGroupTypes() {
        $sql = "SELECT * FROM group_types ORDER BY type_name";
        return fetchAll($this->conn, $sql);
    }

    /**
     * Create a new group type
     * @param string $typeName Name of the new group type
     * @return int|bool Group type ID if successful, false otherwise
     */
    public function createGroupType($typeName) {
        try {
            // First, check the table structure to see what columns exist
            $columns = [];
            $stmt = $this->conn->query("DESCRIBE group_types");
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $columns[] = $row['Field'];
            }

            // Determine the primary key column name
            $pkColumn = in_array('group_type_id', $columns) ? 'group_type_id' : 'id';

            // Check if group type already exists
            $sql = "SELECT {$pkColumn} FROM group_types WHERE type_name = :type_name";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':type_name', $typeName);
            $stmt->execute();
            $existing = $stmt->fetch(PDO::FETCH_OBJ);

            if ($existing) {
                // Group type already exists, return its ID
                return $existing->{$pkColumn};
            }

            // Build INSERT query based on available columns
            $insertColumns = ['type_name'];
            $insertValues = [':type_name'];
            $params = [':type_name' => $typeName];

            // Add created_at if it exists
            if (in_array('created_at', $columns)) {
                $insertColumns[] = 'created_at';
                $insertValues[] = 'NOW()';
            }

            // Add updated_at if it exists
            if (in_array('updated_at', $columns)) {
                $insertColumns[] = 'updated_at';
                $insertValues[] = 'NOW()';
            }

            $sql = "INSERT INTO group_types (" . implode(', ', $insertColumns) . ") VALUES (" . implode(', ', $insertValues) . ")";
            $stmt = $this->conn->prepare($sql);

            if ($stmt->execute($params)) {
                return $this->conn->lastInsertId();
            }

        } catch (PDOException $e) {
            error_log("Error creating group type: " . $e->getMessage());
            return false;
        }

        return false;
    }

    /**
     * Add a member to a group
     * @param int $memberId Member ID
     * @param int $groupId Group ID
     * @param string $role Role in the group
     * @return bool Success or failure
     */
    public function addMemberToGroup($memberId, $groupId, $role = 'member') {
        // Check if member is already in the group
        $sql = "SELECT id FROM member_groups WHERE member_id = :member_id AND group_id = :group_id";
        $params = [
            ':member_id' => $memberId,
            ':group_id' => $groupId
        ];
        $existing = fetchOne($this->conn, $sql, $params);

        if ($existing) {
            // Member already exists in group, update their status and role
            $sql = "UPDATE member_groups SET status = 'active', role_in_group = :role
                    WHERE member_id = :member_id AND group_id = :group_id";
            $params[':role'] = $role;
            return executeUpdate($this->conn, $sql, $params);
        } else {
            // Add member to group
            $sql = "INSERT INTO member_groups (member_id, group_id, role_in_group, joined_date, status)
                    VALUES (:member_id, :group_id, :role, CURDATE(), 'active')";
            $params[':role'] = $role;
            return executeUpdate($this->conn, $sql, $params);
        }
    }

    /**
     * Remove a member from a group
     * @param int $memberId Member ID
     * @param int $groupId Group ID
     * @return bool Success or failure
     */
    public function removeMemberFromGroup($memberId, $groupId) {
        $sql = "DELETE FROM member_groups WHERE member_id = :member_id AND group_id = :group_id";
        $params = [
            ':member_id' => $memberId,
            ':group_id' => $groupId
        ];
        return executeUpdate($this->conn, $sql, $params);
    }

    /**
     * Get all members in a group (legacy method - use getGroupMembersPaginated for large datasets)
     * @param int $groupId Group ID
     * @param string $status Filter by status (active, inactive, or all)
     * @return array Array of members
     */
    public function getGroupMembers($groupId, $status = 'active') {
        $sql = "SELECT m.*, mg.role_in_group, mg.joined_date, mg.status as group_status
                FROM members m
                JOIN member_groups mg ON m.id = mg.member_id
                WHERE mg.group_id = :group_id";

        if ($status !== 'all') {
            $sql .= " AND mg.status = :status";
            $params = [':group_id' => $groupId, ':status' => $status];
            return fetchAll($this->conn, $sql, $params);
        }

        $params = [':group_id' => $groupId];
        return fetchAll($this->conn, $sql, $params);
    }

    /**
     * Get paginated members in a group with search and filtering
     * @param int $groupId Group ID
     * @param int $page Current page (1-based)
     * @param int $limit Members per page
     * @param string $search Search term for name, email, or phone
     * @param string $status Filter by status (active, inactive, or all)
     * @param string $role Filter by role
     * @param string $sortBy Sort field (name, joined_date, role)
     * @param string $sortOrder Sort order (ASC, DESC)
     * @return array Array with 'members', 'total', 'pages', 'current_page'
     */
    public function getGroupMembersPaginated($groupId, $page = 1, $limit = 20, $search = '', $status = 'active', $role = '', $sortBy = 'name', $sortOrder = 'ASC') {
        $offset = ($page - 1) * $limit;

        // Base query
        $baseWhere = "mg.group_id = :group_id";
        $params = [':group_id' => $groupId];

        // Status filter
        if ($status !== 'all') {
            $baseWhere .= " AND mg.status = :status";
            $params[':status'] = $status;
        }

        // Role filter
        if (!empty($role)) {
            $baseWhere .= " AND mg.role_in_group = :role";
            $params[':role'] = $role;
        }

        // Search filter
        $searchWhere = '';
        if (!empty($search)) {
            $searchWhere = " AND (CONCAT(m.first_name, ' ', m.last_name) LIKE :search
                            OR m.email LIKE :search
                            OR m.phone_number LIKE :search)";
            $params[':search'] = "%{$search}%";
        }

        // Sort mapping
        $sortFields = [
            'name' => 'CONCAT(m.first_name, " ", m.last_name)',
            'joined_date' => 'mg.joined_date',
            'role' => 'mg.role_in_group',
            'email' => 'm.email'
        ];
        $orderBy = isset($sortFields[$sortBy]) ? $sortFields[$sortBy] : $sortFields['name'];
        $sortOrder = strtoupper($sortOrder) === 'DESC' ? 'DESC' : 'ASC';

        // Count total records
        $countSql = "SELECT COUNT(*) as total
                     FROM members m
                     JOIN member_groups mg ON m.id = mg.member_id
                     WHERE {$baseWhere}{$searchWhere}";

        $totalResult = fetchOne($this->conn, $countSql, $params);
        $total = $totalResult ? $totalResult->total : 0;

        // Get paginated data
        $dataSql = "SELECT m.*, mg.role_in_group, mg.joined_date, mg.status as group_status
                    FROM members m
                    JOIN member_groups mg ON m.id = mg.member_id
                    WHERE {$baseWhere}{$searchWhere}
                    ORDER BY {$orderBy} {$sortOrder}
                    LIMIT " . (int)$limit . " OFFSET " . (int)$offset;

        // Don't add limit/offset to params since they're now in the SQL directly
        $members = fetchAll($this->conn, $dataSql, $params);

        return [
            'members' => $members ?: [],
            'total' => (int)$total,
            'pages' => ceil($total / $limit),
            'current_page' => $page,
            'per_page' => $limit,
            'has_next' => $page < ceil($total / $limit),
            'has_prev' => $page > 1
        ];
    }

    /**
     * Get group member count
     * @param int $groupId Group ID
     * @param string $status Filter by status
     * @return int Member count
     */
    public function getGroupMemberCount($groupId, $status = 'active') {
        $sql = "SELECT COUNT(*) as count
                FROM member_groups mg
                WHERE mg.group_id = :group_id";

        $params = [':group_id' => $groupId];

        if ($status !== 'all') {
            $sql .= " AND mg.status = :status";
            $params[':status'] = $status;
        }

        $result = fetchOne($this->conn, $sql, $params);
        return $result ? (int)$result->count : 0;
    }

    /**
     * Get available roles in a group
     * @param int $groupId Group ID
     * @return array Array of unique roles
     */
    public function getGroupRoles($groupId) {
        $sql = "SELECT DISTINCT mg.role_in_group
                FROM member_groups mg
                WHERE mg.group_id = :group_id
                AND mg.role_in_group IS NOT NULL
                AND mg.role_in_group != ''
                ORDER BY mg.role_in_group";

        $params = [':group_id' => $groupId];
        $results = fetchAll($this->conn, $sql, $params);

        return array_column($results ?: [], 'role_in_group');
    }

    /**
     * Get all groups a member belongs to
     * @param int $memberId Member ID
     * @param string $status Filter by status (active, inactive, or all)
     * @return array Array of groups
     */
    public function getMemberGroups($memberId, $status = 'active') {
        $sql = "SELECT g.*, gt.type_name, mg.role_in_group, mg.joined_date, mg.status as member_status
                FROM groups g
                JOIN member_groups mg ON g.group_id = mg.group_id
                LEFT JOIN group_types gt ON g.group_type_id = gt.group_type_id
                WHERE mg.member_id = :member_id";

        if ($status !== 'all') {
            $sql .= " AND mg.status = :status AND g.status = :g_status";
            $params = [':member_id' => $memberId, ':status' => $status, ':g_status' => $status];
            return fetchAll($this->conn, $sql, $params);
        }

        $params = [':member_id' => $memberId];
        return fetchAll($this->conn, $sql, $params);
    }

    /**
     * Update a member's role in a group
     * @param int $memberId Member ID
     * @param int $groupId Group ID
     * @param string $role New role
     * @return bool Success or failure
     */
    public function updateMemberRole($memberId, $groupId, $role) {
        $sql = "UPDATE member_groups SET role_in_group = :role
                WHERE member_id = :member_id AND group_id = :group_id";
        $params = [
            ':member_id' => $memberId,
            ':group_id' => $groupId,
            ':role' => $role
        ];
        return executeUpdate($this->conn, $sql, $params);
    }

    /**
     * Add multiple members to a group
     * @param array $memberIds Array of member IDs
     * @param int $groupId Group ID
     * @param string $role Role in the group
     * @return array Result with counts of added, updated, and failed members
     */
    public function addMembersToGroup($memberIds, $groupId, $role = 'member') {
        $added = 0;
        $updated = 0;
        $failed = 0;

        foreach ($memberIds as $memberId) {
            // Check if member is already in the group
            $sql = "SELECT id FROM member_groups WHERE member_id = :member_id AND group_id = :group_id";
            $params = [':member_id' => $memberId, ':group_id' => $groupId];
            $existing = fetchOne($this->conn, $sql, $params);

            if ($existing) {
                // Member already exists, update their status and role
                $sql = "UPDATE member_groups SET status = 'active', role_in_group = :role
                        WHERE member_id = :member_id AND group_id = :group_id";
                $params[':role'] = $role;
                if (executeUpdate($this->conn, $sql, $params)) {
                    $updated++;
                } else {
                    $failed++;
                }
            } else {
                // Add new member to group
                $sql = "INSERT INTO member_groups (member_id, group_id, role_in_group, joined_date, status)
                        VALUES (:member_id, :group_id, :role, CURDATE(), 'active')";
                $params[':role'] = $role;
                if (executeUpdate($this->conn, $sql, $params)) {
                    $added++;
                } else {
                    $failed++;
                }
            }
        }

        return [
            'added' => $added,
            'updated' => $updated,
            'failed' => $failed,
            'total' => count($memberIds)
        ];
    }

    /**
     * Get meeting schedule for a group
     * @param int $groupId Group ID
     * @return object|bool Meeting schedule object or false if not found
     */
    public function getMeetingSchedule($groupId) {
        $sql = "SELECT * FROM group_meeting_schedule WHERE group_id = :group_id AND is_active = 1";
        $params = [':group_id' => $groupId];
        return fetchOne($this->conn, $sql, $params);
    }





    /**
     * Get announcements for a group
     * @param int $groupId Group ID
     * @param int $limit Number of announcements to fetch
     * @return array Array of announcements
     */
    public function getGroupAnnouncements($groupId, $limit = 10) {
        $sql = "SELECT ga.*, m.first_name, m.last_name
                FROM group_announcements ga
                JOIN members m ON ga.posted_by = m.id
                WHERE ga.group_id = ? AND ga.is_active = 1
                AND (ga.expires_at IS NULL OR ga.expires_at > NOW())
                ORDER BY ga.created_at DESC
                LIMIT " . (int)$limit;

        $params = [$groupId];
        return fetchAll($this->conn, $sql, $params);
    }

    /**
     * Create a new group announcement
     * @param array $announcementData Announcement data
     * @return bool Success status
     */
    public function createAnnouncement($announcementData) {
        $sql = "INSERT INTO group_announcements
                (group_id, title, content, announcement_type, posted_by, expires_at)
                VALUES (:group_id, :title, :content, :announcement_type, :posted_by, :expires_at)";

        $params = [
            ':group_id' => $announcementData['group_id'],
            ':title' => $announcementData['title'],
            ':content' => $announcementData['content'],
            ':announcement_type' => $announcementData['announcement_type'],
            ':posted_by' => $announcementData['posted_by'],
            ':expires_at' => $announcementData['expires_at']
        ];

        return executeUpdate($this->conn, $sql, $params);
    }

    /**
     * Update an announcement
     * @param int $announcementId Announcement ID
     * @param array $announcementData Announcement data
     * @return bool Success status
     */
    public function updateAnnouncement($announcementId, $announcementData) {
        $sql = "UPDATE group_announcements SET
                title = :title,
                content = :content,
                announcement_type = :announcement_type,
                expires_at = :expires_at,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = :id";

        $params = [
            ':id' => $announcementId,
            ':title' => $announcementData['title'],
            ':content' => $announcementData['content'],
            ':announcement_type' => $announcementData['announcement_type'],
            ':expires_at' => $announcementData['expires_at']
        ];

        return executeUpdate($this->conn, $sql, $params);
    }

    /**
     * Delete an announcement (soft delete)
     * @param int $announcementId Announcement ID
     * @return bool Success status
     */
    public function deleteAnnouncement($announcementId) {
        $sql = "UPDATE group_announcements SET is_active = 0 WHERE id = :id";
        $params = [':id' => $announcementId];
        return executeUpdate($this->conn, $sql, $params);
    }

    /**
     * Get a single announcement by ID
     * @param int $announcementId Announcement ID
     * @return object|bool Announcement object or false if not found
     */
    public function getAnnouncementById($announcementId) {
        $sql = "SELECT ga.*, m.first_name, m.last_name
                FROM group_announcements ga
                JOIN members m ON ga.posted_by = m.id
                WHERE ga.id = :id AND ga.is_active = 1";

        $params = [':id' => $announcementId];
        return fetchOne($this->conn, $sql, $params);
    }

    /**
     * Get meetings for a group
     * @param int $groupId Group ID
     * @param int $limit Number of meetings to fetch
     * @return array Array of meetings
     */
    public function getGroupMeetings($groupId, $limit = 10) {
        $sql = "SELECT gm.*, m.first_name, m.last_name,
                (SELECT COUNT(*) FROM meeting_attendance WHERE meeting_id = gm.id) as total_attendance
                FROM group_meetings gm
                JOIN members m ON gm.created_by = m.id
                WHERE gm.group_id = ?
                ORDER BY gm.meeting_date DESC, gm.meeting_time DESC
                LIMIT " . (int)$limit;

        $params = [$groupId];
        return fetchAll($this->conn, $sql, $params);
    }

    /**
     * Create a new meeting
     * @param array $meetingData Meeting data
     * @return int|bool Meeting ID or false on failure
     */
    public function createMeeting($meetingData) {
        $sql = "INSERT INTO group_meetings
                (group_id, meeting_date, meeting_time, meeting_location, meeting_topic, meeting_notes, created_by)
                VALUES (:group_id, :meeting_date, :meeting_time, :meeting_location, :meeting_topic, :meeting_notes, :created_by)";

        $params = [
            ':group_id' => $meetingData['group_id'],
            ':meeting_date' => $meetingData['meeting_date'],
            ':meeting_time' => $meetingData['meeting_time'],
            ':meeting_location' => $meetingData['meeting_location'],
            ':meeting_topic' => $meetingData['meeting_topic'],
            ':meeting_notes' => $meetingData['meeting_notes'],
            ':created_by' => $meetingData['created_by']
        ];

        return executeInsert($this->conn, $sql, $params);
    }

    /**
     * Get a single meeting by ID
     * @param int $meetingId Meeting ID
     * @return object|bool Meeting object or false if not found
     */
    public function getMeetingById($meetingId) {
        $sql = "SELECT gm.*, m.first_name, m.last_name, g.group_name
                FROM group_meetings gm
                JOIN members m ON gm.created_by = m.id
                JOIN groups g ON gm.group_id = g.group_id
                WHERE gm.id = ?";

        $params = [$meetingId];
        return fetchOne($this->conn, $sql, $params);
    }

    /**
     * Record attendance for a meeting
     * @param array $attendanceData Attendance data
     * @return bool Success status
     */
    public function recordAttendance($attendanceData) {
        $sql = "INSERT INTO meeting_attendance
                (meeting_id, member_id, attendance_status, check_in_time, notes, recorded_by)
                VALUES (:meeting_id, :member_id, :attendance_status, :check_in_time, :notes, :recorded_by)
                ON DUPLICATE KEY UPDATE
                attendance_status = VALUES(attendance_status),
                check_in_time = VALUES(check_in_time),
                notes = VALUES(notes),
                recorded_by = VALUES(recorded_by)";

        $params = [
            ':meeting_id' => $attendanceData['meeting_id'],
            ':member_id' => $attendanceData['member_id'],
            ':attendance_status' => $attendanceData['attendance_status'],
            ':check_in_time' => $attendanceData['check_in_time'],
            ':notes' => $attendanceData['notes'],
            ':recorded_by' => $attendanceData['recorded_by']
        ];

        return executeUpdate($this->conn, $sql, $params);
    }

    /**
     * Get attendance for a specific meeting
     * @param int $meetingId Meeting ID
     * @return array Array of attendance records
     */
    public function getMeetingAttendance($meetingId) {
        $sql = "SELECT ma.*, m.first_name, m.last_name, m.profile_picture
                FROM meeting_attendance ma
                JOIN members m ON ma.member_id = m.id
                WHERE ma.meeting_id = ?
                ORDER BY m.first_name, m.last_name";

        $params = [$meetingId];
        return fetchAll($this->conn, $sql, $params);
    }

    /**
     * Get attendance statistics for a group
     * @param int $groupId Group ID
     * @return array Attendance statistics
     */
    public function getAttendanceStats($groupId) {
        $sql = "SELECT
                COUNT(DISTINCT gm.id) as total_meetings,
                COUNT(ma.id) as total_attendance_records,
                AVG(CASE WHEN ma.attendance_status = 'present' THEN 1 ELSE 0 END) * 100 as average_attendance_rate
                FROM group_meetings gm
                LEFT JOIN meeting_attendance ma ON gm.id = ma.meeting_id
                WHERE gm.group_id = :group_id AND gm.status = 'completed'";

        $params = [':group_id' => $groupId];
        return fetchOne($this->conn, $sql, $params);
    }

    /**
     * Get dues settings for a group
     * @param int $groupId Group ID
     * @return object|bool Dues settings object or false if not found
     */
    public function getDuesSettings($groupId) {
        $sql = "SELECT * FROM group_dues_settings WHERE group_id = ? AND is_active = 1";
        $params = [$groupId];
        return fetchOne($this->conn, $sql, $params);
    }

    /**
     * Create or update dues settings for a group
     * @param int $groupId Group ID
     * @param array $duesData Dues settings data
     * @return bool Success status
     */
    public function saveDuesSettings($groupId, $duesData) {
        // Check if settings exist
        $existing = $this->getDuesSettings($groupId);

        if ($existing) {
            // Update existing settings
            $sql = "UPDATE group_dues_settings SET
                    dues_amount = ?,
                    dues_frequency = ?,
                    due_date = ?,
                    currency = ?,
                    updated_at = CURRENT_TIMESTAMP
                    WHERE group_id = ? AND is_active = 1";
            $params = [
                $duesData['dues_amount'],
                $duesData['dues_frequency'],
                $duesData['due_date'],
                $duesData['currency'],
                $groupId
            ];
        } else {
            // Create new settings
            $sql = "INSERT INTO group_dues_settings
                    (group_id, dues_amount, dues_frequency, due_date, currency)
                    VALUES (?, ?, ?, ?, ?)";
            $params = [
                $groupId,
                $duesData['dues_amount'],
                $duesData['dues_frequency'],
                $duesData['due_date'],
                $duesData['currency']
            ];
        }

        return executeUpdate($this->conn, $sql, $params);
    }

    /**
     * Get dues payments for a group
     * @param int $groupId Group ID
     * @param string $period Optional period filter (e.g., '2024-01')
     * @return array Array of payment records
     */
    public function getDuesPayments($groupId, $period = null) {
        $sql = "SELECT dp.*, m.first_name, m.last_name, r.first_name as recorded_by_first_name, r.last_name as recorded_by_last_name
                FROM group_dues_payments dp
                JOIN members m ON dp.member_id = m.id
                JOIN members r ON dp.recorded_by = r.id
                WHERE dp.group_id = ?";

        $params = [$groupId];

        if ($period) {
            $sql .= " AND dp.payment_period = ?";
            $params[] = $period;
        }

        $sql .= " ORDER BY dp.payment_date DESC, m.first_name, m.last_name";

        return fetchAll($this->conn, $sql, $params);
    }

    /**
     * Record a dues payment
     * @param array $paymentData Payment data
     * @return bool Success status
     */
    public function recordDuesPayment($paymentData) {
        // First check if there's an existing payment for this member and period
        $checkSql = "SELECT id, payment_amount FROM group_dues_payments
                     WHERE group_id = ? AND member_id = ? AND payment_period = ?";

        $checkParams = [
            $paymentData['group_id'],
            $paymentData['member_id'],
            $paymentData['payment_period']
        ];

        $existingPayment = fetchOne($this->conn, $checkSql, $checkParams);

        if ($existingPayment) {
            // Update existing payment - always add to existing amount for partial payment completion
            $isAdditionalPayment = isset($paymentData['is_additional']) && $paymentData['is_additional'];

            if ($isAdditionalPayment) {
                // Add to existing amount (for completing partial payments)
                $newAmount = $existingPayment->payment_amount + $paymentData['payment_amount'];
            } else {
                // Replace existing amount (for updating full payments)
                $newAmount = $paymentData['payment_amount'];
            }

            $updateSql = "UPDATE group_dues_payments SET
                         payment_amount = ?,
                         payment_date = ?,
                         payment_method = ?,
                         payment_reference = ?,
                         payment_notes = ?,
                         recorded_by = ?
                         WHERE id = ?";

            $updateParams = [
                $newAmount,
                $paymentData['payment_date'],
                $paymentData['payment_method'],
                $paymentData['payment_reference'],
                $paymentData['payment_notes'],
                $paymentData['recorded_by'],
                $existingPayment->id
            ];

            return executeUpdate($this->conn, $updateSql, $updateParams);
        } else {
            // Insert new payment
            $insertSql = "INSERT INTO group_dues_payments
                         (group_id, member_id, payment_amount, payment_date, payment_period, payment_method, payment_reference, payment_notes, recorded_by)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $insertParams = [
                $paymentData['group_id'],
                $paymentData['member_id'],
                $paymentData['payment_amount'],
                $paymentData['payment_date'],
                $paymentData['payment_period'],
                $paymentData['payment_method'],
                $paymentData['payment_reference'],
                $paymentData['payment_notes'],
                $paymentData['recorded_by']
            ];

            return executeUpdate($this->conn, $insertSql, $insertParams);
        }
    }

    /**
     * Get dues status for all members in a group for a specific period
     * @param int $groupId Group ID
     * @param string $period Period (e.g., '2024-01')
     * @return array Array of member dues status
     */
    public function getMemberDuesStatus($groupId, $period) {
        $sql = "SELECT
                    mg.member_id,
                    m.first_name,
                    m.last_name,
                    m.profile_picture,
                    mg.role_in_group,
                    dp.payment_amount,
                    dp.payment_date,
                    dp.payment_method,
                    dp.payment_reference,
                    dp.status as payment_status,
                    ds.dues_amount as expected_amount,
                    ds.currency,
                    CASE
                        WHEN dp.id IS NULL THEN 'unpaid'
                        WHEN dp.payment_amount >= ds.dues_amount THEN 'paid_full'
                        WHEN dp.payment_amount < ds.dues_amount THEN 'paid_partial'
                        ELSE 'unpaid'
                    END as dues_status,
                    CASE
                        WHEN dp.id IS NOT NULL AND dp.payment_amount < ds.dues_amount
                        THEN ROUND(((dp.payment_amount / ds.dues_amount) * 100), 1)
                        ELSE NULL
                    END as payment_percentage,
                    CASE
                        WHEN dp.id IS NOT NULL AND dp.payment_amount < ds.dues_amount
                        THEN (ds.dues_amount - dp.payment_amount)
                        ELSE NULL
                    END as remaining_amount,
                    CASE WHEN de.id IS NOT NULL AND de.is_active = 1
                         AND (de.exemption_end_date IS NULL OR de.exemption_end_date >= CURDATE())
                         THEN 'exempt' ELSE 'liable' END as exemption_status,
                    de.exemption_reason
                FROM member_groups mg
                JOIN members m ON mg.member_id = m.id
                LEFT JOIN group_dues_payments dp ON mg.group_id = dp.group_id AND mg.member_id = dp.member_id AND dp.payment_period = ?
                LEFT JOIN group_dues_exemptions de ON mg.group_id = de.group_id AND mg.member_id = de.member_id
                    AND de.is_active = 1
                    AND de.exemption_start_date <= CURDATE()
                    AND (de.exemption_end_date IS NULL OR de.exemption_end_date >= CURDATE())
                LEFT JOIN group_dues_settings ds ON mg.group_id = ds.group_id AND ds.is_active = 1
                WHERE mg.group_id = ? AND mg.status = 'active'
                ORDER BY m.first_name, m.last_name";

        $params = [$period, $groupId];
        return fetchAll($this->conn, $sql, $params);
    }

    /**
     * Get dues statistics for a group
     * @param int $groupId Group ID
     * @param string $period Optional period filter
     * @return object Dues statistics
     */
    public function getDuesStatistics($groupId, $period = null) {
        $currentPeriod = $period ?: date('Y-m');

        $sql = "SELECT
                    COUNT(DISTINCT mg.member_id) as total_members,
                    COUNT(DISTINCT CASE WHEN dp.payment_amount >= ds.dues_amount THEN dp.member_id END) as paid_full_members,
                    COUNT(DISTINCT CASE WHEN dp.payment_amount > 0 AND dp.payment_amount < ds.dues_amount THEN dp.member_id END) as paid_partial_members,
                    COUNT(DISTINCT CASE WHEN dp.id IS NOT NULL THEN dp.member_id END) as paid_members,
                    COUNT(DISTINCT CASE WHEN de.id IS NOT NULL AND de.is_active = 1
                          AND de.exemption_start_date <= CURDATE()
                          AND (de.exemption_end_date IS NULL OR de.exemption_end_date >= CURDATE())
                          THEN mg.member_id END) as exempt_members,
                    COALESCE(SUM(dp.payment_amount), 0) as total_collected,
                    ds.dues_amount,
                    ds.currency
                FROM member_groups mg
                LEFT JOIN group_dues_payments dp ON mg.group_id = dp.group_id AND mg.member_id = dp.member_id AND dp.payment_period = ?
                LEFT JOIN group_dues_exemptions de ON mg.group_id = de.group_id AND mg.member_id = de.member_id
                    AND de.is_active = 1
                    AND de.exemption_start_date <= CURDATE()
                    AND (de.exemption_end_date IS NULL OR de.exemption_end_date >= CURDATE())
                LEFT JOIN group_dues_settings ds ON mg.group_id = ds.group_id AND ds.is_active = 1
                WHERE mg.group_id = ? AND mg.status = 'active'";

        $params = [$currentPeriod, $groupId];
        return fetchOne($this->conn, $sql, $params);
    }

    /**
     * Save meeting schedule for a group
     * @param array $scheduleData Schedule data
     * @return bool Success status
     */
    public function saveSchedule($scheduleData) {
        // Check if schedule already exists
        $existingSql = "SELECT id FROM group_meeting_schedule WHERE group_id = :group_id";
        $existing = fetchOne($this->conn, $existingSql, [':group_id' => $scheduleData['group_id']]);

        if ($existing) {
            // Update existing schedule
            $sql = "UPDATE group_meeting_schedule SET
                    meeting_day = :meeting_day,
                    meeting_time = :meeting_time,
                    meeting_location = :meeting_location,
                    description = :description,
                    duration = :duration,
                    is_active = 1,
                    updated_at = CURRENT_TIMESTAMP
                    WHERE group_id = :group_id";
        } else {
            // Create new schedule
            $sql = "INSERT INTO group_meeting_schedule
                    (group_id, meeting_day, meeting_time, meeting_location, description, duration, is_active)
                    VALUES (:group_id, :meeting_day, :meeting_time, :meeting_location, :description, :duration, 1)";
        }

        $params = [
            ':group_id' => $scheduleData['group_id'],
            ':meeting_day' => $scheduleData['meeting_day'],
            ':meeting_time' => $scheduleData['meeting_time'],
            ':meeting_location' => $scheduleData['meeting_location'],
            ':description' => $scheduleData['description'],
            ':duration' => $scheduleData['duration']
        ];

        return executeUpdate($this->conn, $sql, $params);
    }

    /**
     * Delete meeting schedule for a group
     * @param int $groupId Group ID
     * @return bool Success status
     */
    public function deleteSchedule($groupId) {
        $sql = "DELETE FROM group_meeting_schedule WHERE group_id = :group_id";
        $params = [':group_id' => $groupId];
        return executeUpdate($this->conn, $sql, $params);
    }
}
