<?php
/**
 * API Key Model
 * Manages API keys for secure API access
 */

class ApiKey {
    private $conn;
    private $table_name = "api_keys";
    
    // Properties
    public $id;
    public $user_id;
    public $key;
    public $description;
    public $permissions;
    public $last_used;
    public $created_at;
    public $status;
    
    /**
     * Constructor
     * 
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }
    
    /**
     * Create a new API key
     * 
     * @return bool
     */
    public function create() {
        // Generate a secure random API key
        $this->key = bin2hex(random_bytes(32));
        
        $query = "INSERT INTO " . $this->table_name . "
                SET
                    user_id = :user_id,
                    api_key = :api_key,
                    description = :description,
                    permissions = :permissions,
                    status = :status";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize and bind
        $user_id = htmlspecialchars(strip_tags($this->user_id));
        $key = htmlspecialchars(strip_tags($this->key));
        $description = htmlspecialchars(strip_tags($this->description));
        $permissions = htmlspecialchars(strip_tags($this->permissions));
        $tenant_id = htmlspecialchars(strip_tags($this->tenant_id));
        $status = htmlspecialchars(strip_tags($this->status));
        
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":api_key", $key);
        $stmt->bindParam(":description", $description);
        $stmt->bindParam(":permissions", $permissions);
        $stmt->bindParam(":tenant_id", $tenant_id);
        $stmt->bindParam(":status", $status);
        
        if ($stmt->execute()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Validate an API key
     * 
     * @param string $key The API key to validate
     * @param string $permission The required permission
     * @return bool True if valid, false otherwise
     */
    public function validate($key, $permission = null) {
        $query = "SELECT * FROM " . $this->table_name . "
                WHERE api_key = :api_key AND status = 'active'
                LIMIT 0,1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":api_key", $key);
        $stmt->execute();
        
        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // Update the last used timestamp
            $update_query = "UPDATE " . $this->table_name . "
                    SET last_used = NOW()
                    WHERE id = :id";
            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(":id", $row['id']);
            $update_stmt->execute();
            
            // Set properties
            $this->id = $row['id'];
            $this->user_id = $row['user_id'];
            $this->key = $row['api_key'];
            $this->description = $row['description'];
            $this->permissions = $row['permissions'];
            $this->last_used = $row['last_used'];
            $this->created_at = $row['created_at'];
            $this->tenant_id = $row['tenant_id'];
            $this->status = $row['status'];
            
            // Check permission if specified
            if ($permission !== null) {
                $permissions = explode(',', $this->permissions);
                if (!in_array($permission, $permissions) && !in_array('*', $permissions)) {
                    return false;
                }
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Get API keys for a user
     * 
     * @param int $user_id
     * @return PDOStatement
     */
    public function getByUser($user_id) {
        $query = "SELECT * FROM " . $this->table_name . "
                WHERE user_id = :user_id
                ORDER BY created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        
        return $stmt;
    }
    
    /**
     * Disable an API key
     * 
     * @return bool
     */
    public function disable() {
        $query = "UPDATE " . $this->table_name . "
                SET status = 'disabled'
                WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        
        if ($stmt->execute()) {
            return true;
        }
        
        return false;
    }
} 