/**
 * ICGC Emmanuel Temple Church Management System
 * Main JavaScript file
 */

console.log('main.js loaded successfully');

// Simple test function
function testClick() {
    alert('Test click works!');
}

// Make it globally available
window.testClick = testClick;

// Toggle submenu function (legacy - keeping for compatibility)
function toggleSubmenu(id) {
    const submenu = document.getElementById(id);
    if (submenu) {
        submenu.classList.toggle('hidden');

        // Rotate chevron icon
        if (id === 'settings-submenu') {
            const chevron = document.getElementById('settings-chevron');
            if (chevron) {
                if (submenu.classList.contains('hidden')) {
                    chevron.style.transform = 'rotate(0deg)';
                } else {
                    chevron.style.transform = 'rotate(180deg)';
                }
            }
        }
    }
}

/**
 * Collapsible Menu Groups System - Complete Rewrite
 */
class CollapsibleMenuGroups {
    constructor() {
        console.log('CollapsibleMenuGroups constructor called');
        this.init();
    }

    init() {
        console.log('Initializing CollapsibleMenuGroups...');

        // Find all menu group toggles
        const toggles = document.querySelectorAll('.menu-group-toggle');
        console.log('Found toggles:', toggles.length);

        // Add click event listeners
        toggles.forEach((toggle, index) => {
            console.log(`Adding listener to toggle ${index}:`, toggle);

            toggle.addEventListener('click', (e) => {
                console.log('Toggle clicked!', e.target);
                e.preventDefault();
                e.stopPropagation();

                const groupId = toggle.getAttribute('data-menu-group');
                console.log('Group ID:', groupId);

                if (groupId) {
                    this.toggleGroup(groupId, toggle);
                }
            });
        });

        // Initialize states
        this.initializeStates();
    }

    toggleGroup(groupId, toggleButton) {
        console.log('toggleGroup called with:', groupId);

        const content = document.querySelector(`[data-group="${groupId}"]`);
        const chevron = toggleButton.querySelector('.fa-chevron-down');

        console.log('Found elements:', {
            content: !!content,
            chevron: !!chevron,
            contentClasses: content ? content.className : 'not found'
        });

        if (!content || !chevron) {
            console.error('Missing elements for group:', groupId);
            return;
        }

        const isCollapsed = content.classList.contains('collapsed');
        console.log('Current state - isCollapsed:', isCollapsed);

        if (isCollapsed) {
            // Expand
            content.classList.remove('collapsed');
            toggleButton.classList.remove('collapsed');
            localStorage.setItem(`menu-group-${groupId}`, 'open');
            console.log('Expanded group:', groupId);
        } else {
            // Collapse
            content.classList.add('collapsed');
            toggleButton.classList.add('collapsed');
            localStorage.setItem(`menu-group-${groupId}`, 'closed');
            console.log('Collapsed group:', groupId);
        }

        console.log('Final content classes:', content.className);
    }

    initializeStates() {
        const groups = ['people-group', 'operations-group', 'financial-group', 'communications-group', 'admin-group'];

        groups.forEach(groupId => {
            const toggle = document.querySelector(`[data-menu-group="${groupId}"]`);
            const content = document.querySelector(`[data-group="${groupId}"]`);

            if (!toggle || !content) return;

            const savedState = localStorage.getItem(`menu-group-${groupId}`);
            let shouldBeOpen = false;

            if (savedState === 'open') {
                shouldBeOpen = true;
            } else if (savedState === null) {
                // Check if group contains active menu item
                const activeItem = content.querySelector('.menu-item.active');
                shouldBeOpen = activeItem !== null;
            }

            if (shouldBeOpen) {
                content.classList.remove('collapsed');
                toggle.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                toggle.classList.add('collapsed');
            }
        });
    }
}

// Initialize when DOM is ready
let menuGroupsInstance = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded event fired');

    // Initialize collapsible menu groups with new system
    console.log('Creating CollapsibleMenuGroups instance...');
    menuGroupsInstance = new CollapsibleMenuGroups();
    console.log('CollapsibleMenuGroups instance created:', menuGroupsInstance);

    // Toggle user menu
    const userMenuButton = document.getElementById('user-menu-button');
    const userMenu = document.getElementById('user-menu');

    if (userMenuButton && userMenu) {
        userMenuButton.addEventListener('click', () => {
            userMenu.classList.toggle('hidden');
        });

        // Close user menu when clicking outside
        document.addEventListener('click', (event) => {
            if (!userMenuButton.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
    }

    // Toggle sidebar on mobile
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.getElementById('sidebar');

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (event) => {
            if (window.innerWidth < 768 && !sidebar.contains(event.target) && !sidebarToggle.contains(event.target)) {
                sidebar.classList.add('-translate-x-full');
            }
        });
    }

    // Flash message auto-dismiss
    const flashMessages = document.querySelectorAll('.flash-message');
    flashMessages.forEach(message => {
        setTimeout(() => {
            message.classList.add('opacity-0');
            setTimeout(() => {
                message.remove();
            }, 500);
        }, 5000);
    });

    // Initialize any charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        initializeCharts();
    }
});

// Function to initialize charts
function initializeCharts() {
    // Attendance chart
    const attendanceCtx = document.getElementById('attendance-chart');
    if (attendanceCtx) {
        new Chart(attendanceCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Attendance',
                    data: [150, 170, 180, 160, 190, 200],
                    backgroundColor: 'rgba(116, 227, 154, 0.2)',
                    borderColor: '#3BD16F',
                    borderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Offerings chart
    const offeringsCtx = document.getElementById('offerings-chart');
    if (offeringsCtx) {
        new Chart(offeringsCtx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Offerings (GH₵)',
                    data: [4500, 5200, 4800, 5500, 6000, 5200],
                    backgroundColor: '#74E39A'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}
