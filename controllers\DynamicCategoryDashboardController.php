<?php
/**
 * Dynamic Category Dashboard Controller - Complete Rewrite
 * Handles all dynamic category dashboards with robust routing and analytics
 */

class DynamicCategoryDashboardController {
    private $database;
    private $conn;

    public function __construct() {
        $this->database = new Database();
        $this->conn = $this->database->getConnection();
    }

    /**
     * Main dashboard method for any dynamic category
     */
    public function showDashboard($categorySlug) {
        try {
            // Get category information
            $category = $this->getCategoryBySlug($categorySlug);
            
            if (!$category) {
                $_SESSION['flash_message'] = "Category '{$categorySlug}' not found.";
                $_SESSION['flash_type'] = 'danger';
                redirect('finance');
                return;
            }

            // Set page variables
            $page_title = $category->label . ' Dashboard - ICGC Emmanuel Temple';
            $active_page = 'finance';

            // Get analytics data
            $analytics = $this->getCategoryAnalytics($category);
            
            // Get trends data
            $trends = $this->getCategoryTrends($category);
            
            // Get recent transactions
            $recentTransactions = $this->getRecentTransactions($category, 10);
            
            // Get member tracking (if applicable)
            $memberTracking = [];
            if ($category->requires_member) {
                $memberTracking = $this->getMemberTracking($category);
            }

            // Start output buffering
            ob_start();

            // Load the dynamic category dashboard view
            require_once 'views/finances/dashboards/dynamic_category.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("Error in DynamicCategoryDashboardController::showDashboard: " . $e->getMessage());
            $_SESSION['flash_message'] = 'Error loading category dashboard: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finance');
        }
    }

    /**
     * Get category by slug
     */
    private function getCategoryBySlug($slug) {
        try {
            $query = "SELECT * FROM custom_finance_categories WHERE slug = ? AND is_active = 1";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$slug]);
            
            return $stmt->fetch(PDO::FETCH_OBJ);
            
        } catch (Exception $e) {
            error_log("Error getting category by slug: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get category analytics
     */
    private function getCategoryAnalytics($category) {
        try {
            // Get total amount and count - Check both name and slug for compatibility
            $totalQuery = "SELECT
                COUNT(*) as total_count,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(AVG(amount), 0) as average_amount
                FROM finances
                WHERE category = ? OR category = ?";

            $stmt = $this->conn->prepare($totalQuery);
            $stmt->execute([$category->name, $category->slug]);
            $totals = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Get current month data
            $currentMonthQuery = "SELECT
                COUNT(*) as current_month_count,
                COALESCE(SUM(amount), 0) as current_month_amount
                FROM finances
                WHERE category = ?
                AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
                AND YEAR(transaction_date) = YEAR(CURRENT_DATE())";
            
            $stmt = $this->conn->prepare($currentMonthQuery);
            $stmt->execute([$category->name]);
            $currentMonth = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Get last month for growth calculation
            $lastMonthQuery = "SELECT
                COALESCE(SUM(amount), 0) as last_month_amount
                FROM finances
                WHERE category = ?
                AND MONTH(transaction_date) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH)
                AND YEAR(transaction_date) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)";
            
            $stmt = $this->conn->prepare($lastMonthQuery);
            $stmt->execute([$category->name]);
            $lastMonth = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Calculate growth percentage
            $growthPercentage = 0;
            if ($lastMonth['last_month_amount'] > 0) {
                $growthPercentage = (($currentMonth['current_month_amount'] - $lastMonth['last_month_amount']) / $lastMonth['last_month_amount']) * 100;
            }
            
            // Get payment method breakdown
            $paymentMethodQuery = "SELECT
                payment_method,
                COALESCE(SUM(amount), 0) as amount
                FROM finances
                WHERE category = ?
                GROUP BY payment_method";
            
            $stmt = $this->conn->prepare($paymentMethodQuery);
            $stmt->execute([$category->name]);
            $paymentMethods = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $analytics = array_merge($totals, $currentMonth, [
                'growth_percentage' => round($growthPercentage, 1),
                'new_this_month' => $currentMonth['current_month_count'],
                'cash_amount' => 0,
                'bank_amount' => 0,
                'mobile_amount' => 0,
                'cheque_amount' => 0
            ]);
            
            // Process payment methods
            foreach ($paymentMethods as $method) {
                switch ($method['payment_method']) {
                    case 'cash':
                        $analytics['cash_amount'] = $method['amount'];
                        break;
                    case 'bank_transfer':
                        $analytics['bank_amount'] = $method['amount'];
                        break;
                    case 'mobile_money':
                        $analytics['mobile_amount'] = $method['amount'];
                        break;
                    case 'cheque':
                        $analytics['cheque_amount'] = $method['amount'];
                        break;
                }
            }
            
            return $analytics;
            
        } catch (Exception $e) {
            error_log("Error getting category analytics: " . $e->getMessage());
            return [
                'total_count' => 0,
                'total_amount' => 0,
                'average_amount' => 0,
                'current_month_count' => 0,
                'current_month_amount' => 0,
                'growth_percentage' => 0,
                'new_this_month' => 0,
                'cash_amount' => 0,
                'bank_amount' => 0,
                'mobile_amount' => 0,
                'cheque_amount' => 0
            ];
        }
    }

    /**
     * Get category trends
     */
    private function getCategoryTrends($category) {
        try {
            $query = "SELECT
                DATE_FORMAT(transaction_date, '%Y-%m') as month,
                SUM(amount) as amount,
                COUNT(*) as count
                FROM finances
                WHERE category = ?
                AND transaction_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
                ORDER BY month ASC";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$category->name]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting category trends: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent transactions
     */
    private function getRecentTransactions($category, $limit = 10) {
        try {
            $query = "SELECT
                f.*,
                CONCAT(m.first_name, ' ', m.last_name) as member_name
                FROM finances f
                LEFT JOIN members m ON f.member_id = m.id
                WHERE f.category = ?
                ORDER BY f.transaction_date DESC, f.created_at DESC
                LIMIT ?";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$category->name, $limit]);
            
            return $stmt->fetchAll(PDO::FETCH_OBJ);
            
        } catch (Exception $e) {
            error_log("Error getting recent transactions: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get member tracking data
     */
    private function getMemberTracking($category) {
        try {
            $query = "SELECT
                f.member_id,
                m.first_name,
                m.last_name,
                COUNT(*) as payment_count,
                SUM(f.amount) as total_amount,
                MAX(f.transaction_date) as last_payment_date
                FROM finances f
                LEFT JOIN members m ON f.member_id = m.id
                WHERE f.category = ?
                GROUP BY f.member_id, m.first_name, m.last_name
                ORDER BY total_amount DESC
                LIMIT 10";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$category->name]);
            
            return $stmt->fetchAll(PDO::FETCH_OBJ);
            
        } catch (Exception $e) {
            error_log("Error getting member tracking: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Export category data
     */
    public function exportData($categorySlug, $format = 'csv') {
        try {
            $category = $this->getCategoryBySlug($categorySlug);
            
            if (!$category) {
                http_response_code(404);
                echo "Category not found";
                return;
            }

            // Get all transactions for this category
            $query = "SELECT
                f.*,
                CONCAT(m.first_name, ' ', m.last_name) as member_name
                FROM finances f
                LEFT JOIN members m ON f.member_id = m.id
                WHERE f.category = ?
                ORDER BY f.transaction_date DESC";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$category->name]);
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if ($format === 'csv') {
                header('Content-Type: text/csv');
                header('Content-Disposition: attachment; filename="' . $category->slug . '_transactions.csv"');
                
                $output = fopen('php://output', 'w');
                
                // CSV headers
                fputcsv($output, ['Date', 'Member', 'Amount', 'Payment Method', 'Description']);
                
                // CSV data
                foreach ($transactions as $transaction) {
                    fputcsv($output, [
                        $transaction['transaction_date'],
                        $transaction['member_name'] ?? 'N/A',
                        $transaction['amount'],
                        $transaction['payment_method'],
                        $transaction['description'] ?? ''
                    ]);
                }
                
                fclose($output);
            }
            
        } catch (Exception $e) {
            error_log("Error exporting category data: " . $e->getMessage());
            http_response_code(500);
            echo "Export error";
        }
    }
}
