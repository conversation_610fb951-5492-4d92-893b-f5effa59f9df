<?php
/**
 * Programs Dashboard View - Church Program & Activities Planner
 */

// Header configuration
$header_title = 'Program Dashboard';
$header_subtitle = 'Comprehensive overview of all church programs and activities with detailed analytics and insights.';
$header_icon = 'fas fa-chart-pie';
$header_width = 'container mx-auto';

// Custom navigation buttons for dashboard (exclude back/dashboard buttons since this IS the dashboard)
$navigation_buttons = [
    'create' => [
        'url' => BASE_URL . 'programs/create',
        'text' => 'New Program',
        'icon' => 'fas fa-plus',
        'style' => 'bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white'
    ],
    'list' => [
        'url' => BASE_URL . 'programs/list',
        'text' => 'All Programs',
        'icon' => 'fas fa-list',
        'style' => 'bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-gray-800'
    ],
    'timeline' => [
        'url' => BASE_URL . 'programs/timeline',
        'text' => 'Timeline View',
        'icon' => 'fas fa-timeline',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ],
    // Explicitly exclude default buttons that don't make sense on dashboard
    'back' => ['show' => false],
    'dashboard' => ['show' => false]
];

// Include shared header
include 'components/header.php';
?>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white shadow-lg transform hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm font-medium uppercase tracking-wide">Total Programs</p>
                    <p class="text-3xl font-bold mt-2"><?php echo $stats['total'] ?? 0; ?></p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-full p-4">
                    <i class="fas fa-calendar-check text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white shadow-lg transform hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium uppercase tracking-wide">This Year</p>
                    <p class="text-3xl font-bold mt-2"><?php echo $stats['this_year'] ?? 0; ?></p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-full p-4">
                    <i class="fas fa-calendar-year text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white shadow-lg transform hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm font-medium uppercase tracking-wide">This Month</p>
                    <p class="text-3xl font-bold mt-2"><?php echo $stats['this_month'] ?? 0; ?></p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-full p-4">
                    <i class="fas fa-calendar-day text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-6 text-white shadow-lg transform hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100 text-sm font-medium uppercase tracking-wide">In Progress</p>
                    <p class="text-3xl font-bold mt-2"><?php echo $stats['by_status']['in_progress'] ?? 0; ?></p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-full p-4">
                    <i class="fas fa-tasks text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="grid grid-cols-1 xl:grid-cols-4 gap-8 mb-8">
        <!-- Upcoming Programs - Takes up 3 columns -->
        <div class="xl:col-span-3">
            <div class="bg-white rounded-xl shadow-md overflow-hidden h-full">
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800 flex items-center">
                                <i class="fas fa-calendar-alt mr-3 text-primary"></i>
                                Upcoming Programs
                            </h2>
                            <p class="text-gray-600 mt-2">Programs scheduled for the coming weeks</p>
                        </div>
                        <a href="<?php echo BASE_URL; ?>programs" class="inline-flex items-center px-4 py-2 bg-primary hover:bg-primary-dark text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-sm">
                            <i class="fas fa-list mr-2"></i> View All
                        </a>
                    </div>
                </div>
                <div class="p-6">
                    <?php if (empty($upcoming_programs)): ?>
                        <div class="text-center py-16">
                            <div class="mx-auto w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-6">
                                <i class="fas fa-calendar-plus text-3xl text-gray-400"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-3">No upcoming programs</h3>
                            <p class="text-gray-600 mb-6 max-w-md mx-auto">Start planning your next church program to keep your community engaged and growing.</p>
                            <a href="<?php echo BASE_URL; ?>programs/create" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white text-sm font-medium rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md">
                                <i class="fas fa-plus mr-2"></i> Create Program
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($upcoming_programs as $program): ?>
                                <div class="flex items-center space-x-4 p-5 bg-gradient-to-r from-gray-50 to-white rounded-xl hover:shadow-md transition-all duration-200 group border border-gray-100">
                                    <div class="flex-shrink-0 w-14 h-14 rounded-xl flex items-center justify-center shadow-sm" style="background-color: <?php echo $program['color_code'] ?? '#3F7D58'; ?>20;">
                                        <i class="<?php echo $program['icon'] ?? 'fas fa-calendar'; ?> text-xl" style="color: <?php echo $program['color_code'] ?? '#3F7D58'; ?>"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-lg font-semibold text-gray-900 group-hover:text-primary transition-colors duration-200 mb-1">
                                            <a href="<?php echo BASE_URL; ?>programs/show?id=<?php echo $program['id']; ?>" class="hover:underline">
                                                <?php echo htmlspecialchars($program['title']); ?>
                                            </a>
                                        </h4>
                                        <div class="flex flex-wrap items-center gap-6 text-sm text-gray-600">
                                            <span class="flex items-center font-medium">
                                                <i class="fas fa-calendar mr-2 text-primary"></i>
                                                <?php echo date('M j, Y', strtotime($program['start_date'])); ?>
                                            </span>
                                            <span class="flex items-center">
                                                <i class="fas fa-building mr-2 text-gray-400"></i>
                                                <?php echo htmlspecialchars($program['department_name']); ?>
                                            </span>
                                            <?php if (!empty($program['coordinator_name'])): ?>
                                                <span class="flex items-center">
                                                    <i class="fas fa-user mr-2 text-gray-400"></i>
                                                    <?php echo htmlspecialchars($program['coordinator_name']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <?php
                                        $status_styles = [
                                            'planned' => 'bg-blue-100 text-blue-800 border-blue-200',
                                            'in_progress' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
                                            'completed' => 'bg-green-100 text-green-800 border-green-200',
                                            'cancelled' => 'bg-red-100 text-red-800 border-red-200',
                                            'postponed' => 'bg-gray-100 text-gray-800 border-gray-200'
                                        ];
                                        $status_style = $status_styles[$program['status']] ?? 'bg-gray-100 text-gray-800 border-gray-200';
                                        ?>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border <?php echo $status_style; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $program['status'])); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar - Takes up 1 column -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
                    <h2 class="text-lg font-bold text-gray-800 flex items-center">
                        <i class="fas fa-bolt mr-2 text-primary"></i>
                        Quick Actions
                    </h2>
                </div>
                <div class="p-4">
                    <div class="space-y-3">
                        <a href="<?php echo BASE_URL; ?>programs/create" class="flex items-center w-full px-4 py-3 bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm group">
                            <i class="fas fa-plus mr-3 group-hover:scale-110 transition-transform"></i>
                            <span class="font-medium">Create Program</span>
                        </a>
                        <a href="<?php echo BASE_URL; ?>programs/calendar" class="flex items-center w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-700 rounded-lg transition-all duration-200 group">
                            <i class="fas fa-calendar mr-3 text-blue-500 group-hover:scale-110 transition-transform"></i>
                            <span class="font-medium">View Calendar</span>
                        </a>
                        <a href="<?php echo BASE_URL; ?>programs/timeline" class="flex items-center w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-700 rounded-lg transition-all duration-200 group">
                            <i class="fas fa-timeline mr-3 text-purple-500 group-hover:scale-110 transition-transform"></i>
                            <span class="font-medium">Annual Timeline</span>
                        </a>
                        <a href="<?php echo BASE_URL; ?>programs/export" class="flex items-center w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-700 rounded-lg transition-all duration-200 group">
                            <i class="fas fa-download mr-3 text-green-500 group-hover:scale-110 transition-transform"></i>
                            <span class="font-medium">Export Data</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Program Status Distribution -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
                    <h2 class="text-lg font-bold text-gray-800 flex items-center">
                        <i class="fas fa-chart-bar mr-2 text-primary"></i>
                        Status Overview
                    </h2>
                </div>
                <div class="p-4">
                    <?php
                    $status_data = $stats['by_status'] ?? [];
                    $total_programs = array_sum($status_data);
                    ?>
                    <?php if ($total_programs > 0): ?>
                        <div class="space-y-4">
                            <?php foreach ($status_data as $status => $count): ?>
                                <?php
                                $percentage = ($count / $total_programs) * 100;
                                $status_info = [
                                    'planned' => ['color' => '#3b82f6', 'label' => 'Planned', 'icon' => 'fas fa-clock'],
                                    'in_progress' => ['color' => '#f59e0b', 'label' => 'In Progress', 'icon' => 'fas fa-play'],
                                    'completed' => ['color' => '#10b981', 'label' => 'Completed', 'icon' => 'fas fa-check'],
                                    'cancelled' => ['color' => '#ef4444', 'label' => 'Cancelled', 'icon' => 'fas fa-times'],
                                    'postponed' => ['color' => '#6b7280', 'label' => 'Postponed', 'icon' => 'fas fa-pause']
                                ];
                                $info = $status_info[$status] ?? ['color' => '#6b7280', 'label' => ucfirst($status), 'icon' => 'fas fa-circle'];
                                ?>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex justify-between items-center mb-2">
                                        <div class="flex items-center">
                                            <i class="<?php echo $info['icon']; ?> mr-2 text-sm" style="color: <?php echo $info['color']; ?>"></i>
                                            <span class="text-sm font-medium text-gray-700"><?php echo $info['label']; ?></span>
                                        </div>
                                        <span class="text-sm font-bold text-gray-800"><?php echo $count; ?></span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full transition-all duration-500" style="width: <?php echo $percentage; ?>%; background-color: <?php echo $info['color']; ?>"></div>
                                    </div>
                                    <div class="text-xs text-gray-500 mt-1"><?php echo round($percentage, 1); ?>% of total</div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <i class="fas fa-chart-bar text-3xl text-gray-300 mb-3"></i>
                            <p class="text-gray-500">No programs to display</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories and Departments Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Categories Overview -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-tags mr-3 text-primary"></i>
                    Programs by Category
                </h2>
                <p class="text-gray-600 text-sm mt-2">Distribution of programs across different categories</p>
            </div>
            <div class="p-6">
                <?php if (empty($categories_stats)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-tags text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500 text-lg">No categories available</p>
                        <p class="text-gray-400 text-sm mt-2">Create program categories to organize your activities</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($categories_stats as $category): ?>
                            <div class="bg-gradient-to-r from-gray-50 to-white p-4 rounded-lg border border-gray-100 hover:shadow-md transition-all duration-200">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 rounded-full shadow-sm" style="background-color: <?php echo $category['color_code']; ?>"></div>
                                        <span class="font-semibold text-gray-900"><?php echo htmlspecialchars($category['name']); ?></span>
                                    </div>
                                    <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-bold">
                                        <?php echo $category['program_count']; ?> total
                                    </span>
                                </div>
                                <div class="grid grid-cols-3 gap-3">
                                    <div class="text-center">
                                        <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-sm font-semibold">
                                            <?php echo $category['planned_count']; ?>
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">Planned</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-lg text-sm font-semibold">
                                            <?php echo $category['in_progress_count']; ?>
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">Active</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="bg-green-100 text-green-800 px-2 py-1 rounded-lg text-sm font-semibold">
                                            <?php echo $category['completed_count']; ?>
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">Completed</div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Departments Overview -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-building mr-3 text-primary"></i>
                    Programs by Department
                </h2>
                <p class="text-gray-600 text-sm mt-2">Department-wise program distribution and budgets</p>
            </div>
            <div class="p-6">
                <?php if (empty($departments_stats)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-building text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500 text-lg">No departments available</p>
                        <p class="text-gray-400 text-sm mt-2">Set up ministry departments to organize your programs</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($departments_stats as $department): ?>
                            <div class="bg-gradient-to-r from-gray-50 to-white p-5 rounded-lg border border-gray-100 hover:shadow-md transition-all duration-200">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-building text-primary"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-gray-900"><?php echo htmlspecialchars($department['name']); ?></h3>
                                            <?php if (!empty($department['head_pastor_name'])): ?>
                                                <p class="text-sm text-gray-600">Led by <?php echo htmlspecialchars($department['head_pastor_name']); ?></p>
                                            <?php else: ?>
                                                <p class="text-sm text-gray-500 italic">No head assigned</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-bold mb-2">
                                            <?php echo $department['program_count']; ?> programs
                                        </div>
                                        <?php if ($department['total_budget_allocated'] > 0): ?>
                                            <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-bold">
                                                $<?php echo number_format($department['total_budget_allocated'], 0); ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="bg-gray-100 text-gray-500 px-3 py-1 rounded-full text-sm">
                                                No budget
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>


    </div>
</div>


