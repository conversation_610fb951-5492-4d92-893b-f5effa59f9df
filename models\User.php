<?php
/**
 * User Model
 */

class User {
    // Database connection and table name
    private $conn;
    private $table_name = "users";

    // Object properties
    public $id;
    public $username;
    public $email;
    public $password;
    public $full_name;
    public $profile_picture;
    public $role;
    public $status;
    public $last_login;
    public $created_at;
    public $updated_at;

    // Error handling
    public $error;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db = null) {
        $this->conn = $db;

        // If no database connection is provided, get one from the Database class
        if ($this->conn === null) {
            require_once 'config/database.php';
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }

    /**
     * Populate object properties from array
     *
     * @param array $row
     */
    protected function populateFromArray($row) {
        $this->id = $row['id'];
        $this->username = $row['username'];
        $this->email = $row['email'];
        $this->password = $row['password'];
        $this->full_name = $row['full_name'] ?? '';
        $this->profile_picture = $row['profile_picture'] ?? '';
        $this->role = $row['role'];
        $this->status = $row['status'];
        $this->last_login = $row['last_login'] ?? null;
        $this->created_at = $row['created_at'];
        $this->updated_at = $row['updated_at'];
    }

    /**
     * Get all users
     *
     * @return PDOStatement
     */
    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY username";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get user by ID
     *
     * @param int $id
     * @return bool
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 0,1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row) {
            $this->populateFromArray($row);
            return true;
        }
        return false;
    }

    /**
     * Get filtered users with pagination
     *
     * @param array $filters
     * @param int $limit
     * @param int $offset
     * @param string $orderBy
     * @return PDOStatement
     */
    public function getFiltered($filters = [], $limit = 100, $offset = 0, $orderBy = 'created_at DESC') {
        $query = "SELECT * FROM " . $this->table_name;

        $whereClause = [];
        $params = [];

        // Add custom filters
        $this->addCustomFilters($filters, $whereClause, $params);

        // Add WHERE clause if there are conditions
        if (!empty($whereClause)) {
            $query .= " WHERE " . implode(" AND ", $whereClause);
        }

        $query .= " ORDER BY " . $orderBy . " LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        // Bind pagination parameters
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt;
    }

    /**
     * Get filtered count
     *
     * @param array $filters
     * @return int
     */
    public function getFilteredCount($filters = []) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name;

        $whereClause = [];
        $params = [];

        // Add custom filters
        $this->addCustomFilters($filters, $whereClause, $params);

        // Add WHERE clause if there are conditions
        if (!empty($whereClause)) {
            $query .= " WHERE " . implode(" AND ", $whereClause);
        }

        $stmt = $this->conn->prepare($query);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }

    /**
     * Add custom filters for User-specific filtering
     *
     * @param array $filters
     * @param array $whereClause
     * @param array $params
     */
    protected function addCustomFilters($filters, &$whereClause, &$params) {
        // Add role filter
        if (isset($filters['role']) && !empty($filters['role'])) {
            $whereClause[] = "role = :role";
            $params[':role'] = $filters['role'];
        }

        // Add status filter
        if (isset($filters['status']) && !empty($filters['status'])) {
            $whereClause[] = "status = :status";
            $params[':status'] = $filters['status'];
        }

        // Add search filter (username or email)
        if (isset($filters['search']) && !empty($filters['search'])) {
            $whereClause[] = "(username LIKE :search OR email LIKE :search)";
            $params[':search'] = "%{$filters['search']}%";
        }
    }

    /**
     * Get user by email
     *
     * @param string $email
     * @return bool
     */
    public function getByEmail($email) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE email = :email LIMIT 0,1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":email", $email);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            $this->populateFromArray($row);

            // Ensure status is properly set
            if (empty($this->status)) {
                $this->status = 'active';
                // Update the database to set the default status
                $update_query = "UPDATE " . $this->table_name . " SET status = 'active' WHERE id = :id";
                $update_stmt = $this->conn->prepare($update_query);
                $update_stmt->bindParam(":id", $this->id);
                $update_stmt->execute();
            }

            return true;
        }

        return false;
    }

    // Removed getByEmailAndTenant method - not needed for single tenant

    /**
     * Get user by username
     *
     * @param string $username
     * @return array|false
     */
    public function getByUsername($username) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE username = :username LIMIT 0,1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":username", $username);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row ? $row : false;
    }

    // Removed duplicate getById method

    /**
     * Validate user data before create/update operations
     *
     * @param bool $is_update Whether this is an update operation
     * @return bool True if validation passes, false otherwise
     */
    public function validate($is_update = false) {
        $this->error = null;

        // Required fields
        if (empty($this->username)) {
            $this->error = 'Username is required.';
            return false;
        }

        if (empty($this->email)) {
            $this->error = 'Email is required.';
            return false;
        }

        // Format validation
        if (!filter_var($this->email, FILTER_VALIDATE_EMAIL)) {
            $this->error = 'Invalid email format.';
            return false;
        }

        // Password validation (only for create or when password is being updated)
        if (!$is_update && empty($this->password)) {
            $this->error = 'Password is required.';
            return false;
        }

        if (!empty($this->password) && strlen($this->password) < 6) {
            $this->error = 'Password must be at least 6 characters long.';
            return false;
        }

        // Username format validation
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $this->username)) {
            $this->error = 'Username can only contain letters, numbers, and underscores.';
            return false;
        }

        // Username length validation
        if (strlen($this->username) < 3 || strlen($this->username) > 50) {
            $this->error = 'Username must be between 3 and 50 characters.';
            return false;
        }

        // Email uniqueness validation
        $exclude_id = $is_update ? $this->id : null;
        if ($this->emailExists($this->email, $exclude_id)) {
            $this->error = 'This email address is already in use.';
            return false;
        }

        // Role validation
        if (!empty($this->role)) {
            $valid_roles = ['admin', 'staff', 'user'];
            if (!in_array(strtolower($this->role), $valid_roles)) {
                $this->error = 'Invalid user role.';
                return false;
            }
        }

        // Status validation
        if (!empty($this->status)) {
            $valid_statuses = ['active', 'inactive', 'suspended'];
            if (!in_array(strtolower($this->status), $valid_statuses)) {
                $this->error = 'Invalid user status.';
                return false;
            }
        }

        return true;
    }

    /**
     * Create user with validation
     *
     * @return bool
     */
    public function create() {
        // CRITICAL: Validate data before creating
        if (!$this->validate(false)) {
            // The 'error' property is set by the validate() method
            return false;
        }
        $query = "INSERT INTO " . $this->table_name . "
                SET
                    username = :username,
                    email = :email,
                    password = :password,
                    role = :role,
                    status = :status,
                    created_at = :created_at,
                    updated_at = :updated_at";

        $stmt = $this->conn->prepare($query);

        // Sanitize and bind data
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->password = password_hash($this->password, PASSWORD_DEFAULT);
        $this->role = htmlspecialchars(strip_tags($this->role));
        $this->status = $this->status ?? 'active';
        $this->created_at = date('Y-m-d H:i:s');
        $this->updated_at = date('Y-m-d H:i:s');

        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":password", $this->password);
        $stmt->bindParam(":role", $this->role);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":created_at", $this->created_at);
        $stmt->bindParam(":updated_at", $this->updated_at);

        // Execute query
        try {
            if ($stmt->execute()) {
                $this->id = $this->conn->lastInsertId();
                return true;
            } else {
                $this->error = 'Failed to create user record.';
                return false;
            }
        } catch (PDOException $e) {
            // Check for duplicate email error
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'email') !== false) {
                $this->error = 'Email address already exists.';
            } elseif ($e->getCode() == 23000 && strpos($e->getMessage(), 'username') !== false) {
                $this->error = 'Username already exists.';
            } else {
                $this->error = 'Database error occurred while creating user.';
                error_log("User creation failed: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Update user with validation
     *
     * @return bool
     */
    public function update() {
        // CRITICAL: Validate data before updating
        if (!$this->validate(true)) {
            // The 'error' property is set by the validate() method
            return false;
        }
        $query = "UPDATE " . $this->table_name . "
                SET
                    username = :username,
                    email = :email,
                    full_name = :full_name,
                    profile_picture = :profile_picture,
                    role = :role,
                    status = :status,
                    updated_at = :updated_at
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize and bind data
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->full_name = htmlspecialchars(strip_tags($this->full_name ?? ''));
        $this->profile_picture = htmlspecialchars(strip_tags($this->profile_picture ?? ''));
        $this->role = htmlspecialchars(strip_tags($this->role));
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->updated_at = date('Y-m-d H:i:s');
        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":full_name", $this->full_name);
        $stmt->bindParam(":profile_picture", $this->profile_picture);
        $stmt->bindParam(":role", $this->role);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":updated_at", $this->updated_at);
        $stmt->bindParam(":id", $this->id);

        // Execute query
        try {
            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Failed to update user record.';
                return false;
            }
        } catch (PDOException $e) {
            // Check for duplicate email error
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'email') !== false) {
                $this->error = 'Email address already exists.';
            } elseif ($e->getCode() == 23000 && strpos($e->getMessage(), 'username') !== false) {
                $this->error = 'Username already exists.';
            } else {
                $this->error = 'Database error occurred while updating user.';
                error_log("User update failed: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Update password with validation
     *
     * @param string $new_password The new password to set
     * @return bool
     */
    public function updatePassword($new_password = null) {
        $this->error = null;

        // Use provided password or object property
        $password_to_validate = $new_password ?? $this->password;

        // CRITICAL: Validate password before updating
        if (empty($password_to_validate)) {
            $this->error = 'Password is required.';
            return false;
        }

        if (strlen($password_to_validate) < 6) {
            $this->error = 'Password must be at least 6 characters long.';
            return false;
        }

        // Additional password strength validation
        if (!preg_match('/^(?=.*[a-zA-Z])/', $password_to_validate)) {
            $this->error = 'Password must contain at least one letter.';
            return false;
        }
        $query = "UPDATE " . $this->table_name . "
                SET
                    password = :password,
                    updated_at = :updated_at
                WHERE
                    id = :id";

        $stmt = $this->conn->prepare($query);

        // Hash password and bind data
        // Use the validated password, not the object property
        $hashed_password = password_hash($password_to_validate, PASSWORD_DEFAULT);
        $this->updated_at = date('Y-m-d H:i:s');
        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":password", $hashed_password);
        $stmt->bindParam(":updated_at", $this->updated_at);
        $stmt->bindParam(":id", $this->id);

        // Execute query
        try {
            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Failed to update password in the database.';
                return false;
            }
        } catch (PDOException $e) {
            $this->error = 'Database error occurred while updating password.';
            error_log("Password update failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Deletes a user record from the database.
     * Prevents a user from deleting their own account.
     *
     * @param int $current_user_id The ID of the currently logged-in user.
     * @return bool True on success, false on failure.
     */
    public function delete($current_user_id) {
        $this->error = null;

        // CRITICAL: Prevent a user from deleting their own account
        if ($this->id == $current_user_id) {
            $this->error = 'You cannot delete your own account.';
            return false;
        }

        // RECOMMENDED: Add a check to prevent deleting the last admin
        if ($this->role === 'admin' || $this->role === 'super_admin') {
            $admin_count_query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE role IN ('admin', 'super_admin')";
            $admin_stmt = $this->conn->prepare($admin_count_query);
            $admin_stmt->execute();
            $admin_count = $admin_stmt->fetch(PDO::FETCH_ASSOC)['count'];
            if ($admin_count <= 1) {
                $this->error = 'Cannot delete the last administrator account.';
                return false;
            }
        }

        try {
            $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $this->id);

            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Error deleting user from the database.';
                return false;
            }
        } catch (PDOException $e) {
            // Log the detailed database error
            error_log("User deletion failed: " . $e->getMessage());
            $this->error = 'A database error occurred during user deletion.';
            return false;
        }
    }

    /**
     * Verify password
     *
     * @param string $password
     * @return bool
     */
    public function verifyPassword($password) {
        return password_verify($password, $this->password);
    }

    /**
     * Check if email exists
     *
     * @param string $email
     * @param int|null $exclude_id
     * @return bool
     */
    public function emailExists($email, $exclude_id = null) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email";

        // Exclude current user when updating
        if ($exclude_id) {
            $query .= " AND id != :exclude_id";
        }

        $query .= " LIMIT 0,1";
        $stmt = $this->conn->prepare($query);

        // Bind email
        $stmt->bindParam(":email", $email);

        // Bind exclude_id if provided
        if ($exclude_id) {
            $stmt->bindParam(":exclude_id", $exclude_id);
        }

        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    /**
     * Toggle user status (active/blocked)
     *
     * @return bool
     */
    public function toggleStatus() {
        // Debug log
        error_log('User model toggleStatus called for ID: ' . $this->id);

        // Get current status using inherited getById method
        $this->getById($this->id);
        error_log('Current status before toggle: ' . ($this->status ?? 'not set'));

        // Toggle status
        $new_status = ($this->status === 'active') ? 'blocked' : 'active';
        error_log('New status will be: ' . $new_status);

        $query = "UPDATE " . $this->table_name . "
                SET
                    status = :status,
                    updated_at = :updated_at
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Bind data
        $this->status = $new_status;
        $this->updated_at = date('Y-m-d H:i:s');

        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":updated_at", $this->updated_at);
        $stmt->bindParam(":id", $this->id);

        // Execute query
        try {
            $result = $stmt->execute();
            error_log('Toggle status query executed. Result: ' . ($result ? 'success' : 'failed'));

            if ($result) {
                // Verify the update by fetching the user again
                $this->getById($this->id);
                error_log('Status after toggle: ' . ($this->status ?? 'not set'));
                return true;
            }

            return false;
        } catch (PDOException $e) {
            error_log('Error toggling status: ' . $e->getMessage());
            return false;
        }
    }
}
