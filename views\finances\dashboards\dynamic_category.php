<?php
/**
 * Dynamic Category Dashboard View - Complete Rewrite
 * Universal dashboard for all custom finance categories
 */

// Ensure we have the required data
$analytics = $analytics ?? [];
$memberTracking = $memberTracking ?? [];
$trends = $trends ?? [];
$recentTransactions = $recentTransactions ?? [];
$category = $category ?? null;

// Determine category color scheme based on type - ENHANCED CONSISTENCY
$colorScheme = [
    'member_payments' => [
        'primary' => 'purple',
        'secondary' => 'indigo',
        'gradient' => 'from-purple-600 to-purple-700',
        'bg' => 'purple-50',
        'border' => 'purple-200',
        'text' => 'purple-800'
    ],
    'general_income' => [
        'primary' => 'green',
        'secondary' => 'emerald',
        'gradient' => 'from-green-600 to-green-700',
        'bg' => 'green-50',
        'border' => 'green-200',
        'text' => 'green-800'
    ],
    'expenses' => [
        'primary' => 'red',
        'secondary' => 'rose',
        'gradient' => 'from-red-600 to-red-700',
        'bg' => 'red-50',
        'border' => 'red-200',
        'text' => 'red-800'
    ]
];

$colors = $colorScheme[$category->category_type] ?? [
    'primary' => 'blue',
    'secondary' => 'sky',
    'gradient' => 'from-blue-600 to-blue-700',
    'bg' => 'blue-50',
    'border' => 'blue-200',
    'text' => 'blue-800'
];

// Extract colors for easier use
$primaryColor = $colors['primary'];
$secondaryColor = $colors['secondary'];
$gradientClass = $colors['gradient'];
$bgClass = $colors['bg'];
$borderClass = $colors['border'];
$textClass = $colors['text'];

// Determine if this is a core category
$isCore = isset($category->is_core) && $category->is_core;
?>

<div class="min-h-screen bg-gradient-to-br from-<?php echo $primaryColor; ?>-50 to-<?php echo $secondaryColor; ?>-50">
    <!-- Enhanced Header Section with Consistent Design -->
    <div class="bg-gradient-to-r <?php echo $gradientClass; ?> text-white py-8 px-6 rounded-b-3xl shadow-xl mb-8 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>');"></div>
        </div>

        <div class="max-w-7xl mx-auto relative">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-3">
                        <!-- Breadcrumb Navigation -->
                        <nav class="flex items-center text-sm mb-2">
                            <a href="<?php echo BASE_URL; ?>finance" class="text-white opacity-75 hover:opacity-100 transition-opacity">
                                <i class="fas fa-home mr-1"></i> Finance
                            </a>
                            <i class="fas fa-chevron-right mx-2 text-white opacity-50"></i>
                            <span class="text-white">
                                <?php echo htmlspecialchars($category->label); ?> Dashboard
                            </span>
                        </nav>
                    </div>

                    <div class="flex items-center mb-2">
                        <h1 class="text-3xl font-bold flex items-center">
                            <?php
                            $icon = match($category->category_type) {
                                'member_payments' => '👥',
                                'general_income' => '💰',
                                'expenses' => '💸',
                                default => '📊'
                            };
                            echo '<span class="mr-3">' . $icon . '</span>';
                            echo htmlspecialchars($category->label) . ' Dashboard';

                            // Core category indicator
                            if ($isCore): ?>
                                <span class="ml-3 px-3 py-1 bg-yellow-400 text-yellow-900 text-sm font-medium rounded-full">
                                    <i class="fas fa-star mr-1"></i>Core
                                </span>
                            <?php endif; ?>
                        </h1>
                    </div>

                    <p class="text-white opacity-90 text-lg">
                        <?php echo htmlspecialchars($category->description ?: 'Comprehensive ' . strtolower($category->label) . ' tracking and analytics'); ?>
                    </p>

                    <!-- Category Type Badge -->
                    <div class="mt-3">
                        <span class="inline-flex items-center px-3 py-1 bg-white bg-opacity-20 text-white text-sm font-medium rounded-full">
                            <i class="fas fa-tag mr-2"></i>
                            <?php echo ucfirst(str_replace('_', ' ', $category->category_type)); ?>
                        </span>
                    </div>
                </div>
                <div class="flex gap-3">
                    <?php
                    // Determine the correct tab based on category type
                    $tabParam = '';
                    if ($category->category_type === 'member_payments') {
                        $tabParam = '?tab=member-payments';
                    } elseif ($category->category_type === 'general_income') {
                        $tabParam = '?tab=general-income';
                    } elseif ($category->category_type === 'expenses') {
                        $tabParam = '?tab=expenses';
                    }
                    ?>
                    <a href="<?php echo BASE_URL; ?>finance/add<?php echo $tabParam; ?>"
                       class="bg-white text-<?php echo $primaryColor; ?>-600 px-6 py-3 rounded-lg hover:bg-<?php echo $primaryColor; ?>-50 transition-all duration-200 flex items-center shadow-md">
                        <i class="fas fa-plus mr-2"></i>
                        <span>Add <?php echo $category->category_type === 'expenses' ? 'Expense' : 'Payment'; ?></span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>finance/categories" 
                       class="bg-<?php echo $primaryColor; ?>-800 text-white px-6 py-3 rounded-lg hover:bg-<?php echo $primaryColor; ?>-900 transition-all duration-200 flex items-center shadow-md">
                        <i class="fas fa-cog mr-2"></i>
                        <span>Settings</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 pb-8">
        <!-- Analytics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Collected -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Total Collected</p>
                        <p class="text-2xl font-bold text-<?php echo $primaryColor; ?>-600">
                            GH₵ <?php echo number_format($analytics['total_amount'] ?? 0, 2); ?>
                        </p>
                    </div>
                    <div class="p-3 rounded-full bg-<?php echo $primaryColor; ?>-100">
                        <i class="fas fa-money-bill-wave text-<?php echo $primaryColor; ?>-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-green-600 font-medium">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <?php echo $analytics['growth_percentage'] ?? 0; ?>%
                    </span>
                    <span class="text-gray-500 ml-2">from last month</span>
                </div>
            </div>

            <!-- Total Transactions -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Total Transactions</p>
                        <p class="text-2xl font-bold text-<?php echo $secondaryColor; ?>-600">
                            <?php echo number_format($analytics['total_count'] ?? 0); ?>
                        </p>
                    </div>
                    <div class="p-3 rounded-full bg-<?php echo $secondaryColor; ?>-100">
                        <i class="fas fa-receipt text-<?php echo $secondaryColor; ?>-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-blue-600 font-medium">
                        <i class="fas fa-plus mr-1"></i>
                        <?php echo $analytics['new_this_month'] ?? 0; ?>
                    </span>
                    <span class="text-gray-500 ml-2">this month</span>
                </div>
            </div>

            <!-- Average Amount -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Average Amount</p>
                        <p class="text-2xl font-bold text-green-600">
                            GH₵ <?php echo number_format($analytics['average_amount'] ?? 0, 2); ?>
                        </p>
                    </div>
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-gray-600">
                        <i class="fas fa-calculator mr-1"></i>
                        Per transaction
                    </span>
                </div>
            </div>

            <!-- This Month -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">This Month</p>
                        <p class="text-2xl font-bold text-orange-600">
                            GH₵ <?php echo number_format($analytics['current_month_amount'] ?? 0, 2); ?>
                        </p>
                    </div>
                    <div class="p-3 rounded-full bg-orange-100">
                        <i class="fas fa-calendar text-orange-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-orange-600 font-medium">
                        <?php echo $analytics['current_month_count'] ?? 0; ?>
                    </span>
                    <span class="text-gray-500 ml-2">transactions</span>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Monthly Trends Chart -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">Monthly Trends</h3>
                    <div class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        Last 12 months
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="trendsChart"></canvas>
                </div>
            </div>

            <!-- Payment Distribution -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">Payment Distribution</h3>
                    <div class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        Current year
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="distributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="bg-white rounded-xl shadow-md border border-gray-100 mb-8">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">Recent <?php echo htmlspecialchars($category->label); ?> Transactions</h3>
                        <p class="text-gray-600 text-sm">Latest payments and transaction details</p>
                    </div>
                    <a href="<?php echo BASE_URL; ?>finance/add<?php echo $tabParam; ?>"
                       class="bg-gradient-to-r from-<?php echo $primaryColor; ?>-600 to-<?php echo $primaryColor; ?>-700 hover:from-<?php echo $primaryColor; ?>-700 hover:to-<?php echo $primaryColor; ?>-800 text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center shadow-md hover:shadow-lg">
                        <i class="fas fa-plus mr-2"></i>
                        <span>Add Transaction</span>
                    </a>
                </div>

                <?php if (!empty($recentTransactions)): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <?php if ($category->requires_member): ?>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                    <?php endif; ?>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($recentTransactions as $transaction): ?>
                                <tr class="hover:bg-gray-50">
                                    <?php if ($category->requires_member): ?>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-<?php echo $primaryColor; ?>-100 flex items-center justify-center">
                                                    <i class="fas fa-user text-<?php echo $primaryColor; ?>-600"></i>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($transaction->member_name ?? 'Unknown Member'); ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    ID: <?php echo $transaction->member_id ?? 'N/A'; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <?php endif; ?>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            GH₵ <?php echo number_format($transaction->amount ?? 0, 2); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?php echo ucfirst(str_replace('_', ' ', $transaction->payment_method ?? 'cash')); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('M j, Y', strtotime($transaction->transaction_date ?? 'now')); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Completed
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <div class="mx-auto h-24 w-24 text-gray-300 mb-4">
                            <i class="fas fa-file-invoice-dollar text-6xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No <?php echo htmlspecialchars($category->label); ?> Transactions Yet</h3>
                        <p class="text-gray-500 mb-6">Start tracking <?php echo strtolower($category->label); ?> by adding the first transaction.</p>
                        <a href="<?php echo BASE_URL; ?>finance/add<?php echo $tabParam; ?>"
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-<?php echo $primaryColor; ?>-600 hover:bg-<?php echo $primaryColor; ?>-700">
                            <i class="fas fa-plus mr-2"></i>
                            Add First Transaction
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Export Section -->
        <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-bold text-gray-800 mb-2">Export Data</h3>
                    <p class="text-gray-600 text-sm">Download <?php echo strtolower($category->label); ?> transaction data for analysis</p>
                </div>
                <div class="flex gap-3">
                    <button onclick="exportData('csv')" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-file-csv mr-2"></i>
                        Export CSV
                    </button>
                    <button onclick="exportData('excel')" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-file-excel mr-2"></i>
                        Export Excel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart initialization
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Monthly Trends Chart
    const trendsCtx = document.getElementById('trendsChart');
    if (trendsCtx) {
        new Chart(trendsCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_column($trends, 'month') ?? []); ?>,
                datasets: [{
                    label: '<?php echo htmlspecialchars($category->label); ?>',
                    data: <?php echo json_encode(array_column($trends, 'amount') ?? []); ?>,
                    borderColor: 'rgb(<?php echo $primaryColor === "purple" ? "147, 51, 234" : ($primaryColor === "orange" ? "249, 115, 22" : "107, 114, 128"); ?>)',
                    backgroundColor: 'rgba(<?php echo $primaryColor === "purple" ? "147, 51, 234" : ($primaryColor === "orange" ? "249, 115, 22" : "107, 114, 128"); ?>, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'GH₵ ' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    // Distribution Chart
    const distributionCtx = document.getElementById('distributionChart');
    if (distributionCtx) {
        new Chart(distributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Cash', 'Bank Transfer', 'Mobile Money', 'Cheque'],
                datasets: [{
                    data: [
                        <?php echo $analytics['cash_amount'] ?? 0; ?>,
                        <?php echo $analytics['bank_amount'] ?? 0; ?>,
                        <?php echo $analytics['mobile_amount'] ?? 0; ?>,
                        <?php echo $analytics['cheque_amount'] ?? 0; ?>
                    ],
                    backgroundColor: [
                        '#10B981',
                        '#3B82F6', 
                        '#F59E0B',
                        '#EF4444'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// Export functionality
function exportData(format) {
    const url = `<?php echo BASE_URL; ?>dynamic-category/export/<?php echo $category->slug; ?>?format=${format}`;
    window.open(url, '_blank');
}
</script>
