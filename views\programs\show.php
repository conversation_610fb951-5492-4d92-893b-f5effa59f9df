<?php
/**
 * Program Details View - Church Program & Activities Planner
 */

// Header configuration - will use program data for title
$header_subtitle = htmlspecialchars($program['category_name'] ?? 'Program') . ' Details';
$header_icon = 'fas fa-eye';
$header_width = 'w-[90%] mx-auto';

// Custom navigation for show page
$navigation_buttons = [
    'back' => [
        'url' => BASE_URL . 'programs',
        'text' => 'Back to Programs',
        'icon' => 'fas fa-arrow-left',
        'style' => 'bg-gray-100 hover:bg-gray-200 text-gray-700'
    ],
    'edit' => [
        'url' => BASE_URL . 'programs/edit?id=' . $program['id'],
        'text' => 'Edit Program',
        'icon' => 'fas fa-edit',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ],
    'dashboard' => [
        'url' => BASE_URL . 'programs/dashboard',
        'text' => 'Dashboard',
        'icon' => 'fas fa-chart-pie',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ],
    'calendar' => [
        'url' => BASE_URL . 'programs/calendar',
        'text' => 'Calendar',
        'icon' => 'fas fa-calendar-alt',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ]
];

// Include shared header and form components
include 'components/header.php';
include 'components/form_elements.php';
?>

    <!-- Program Information Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Main Program Details -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Basic Information -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800 flex items-center">
                        <i class="fas fa-info-circle mr-3 text-primary"></i>
                        Program Information
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <?php if (!empty($program['description'])): ?>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Description</h3>
                            <p class="text-gray-600 leading-relaxed"><?php echo nl2br(htmlspecialchars($program['description'])); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Program Details Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-calendar text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">Start Date</p>
                                    <p class="font-medium text-gray-900"><?php echo date('F j, Y', strtotime($program['start_date'])); ?></p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-calendar-times text-red-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">End Date</p>
                                    <p class="font-medium text-gray-900"><?php echo date('F j, Y', strtotime($program['end_date'])); ?></p>
                                </div>
                            </div>
                            
                            <?php if (!empty($program['start_time'])): ?>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-clock text-green-600"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">Time</p>
                                        <p class="font-medium text-gray-900">
                                            <?php echo date('g:i A', strtotime($program['start_time'])); ?>
                                            <?php if (!empty($program['end_time'])): ?>
                                                - <?php echo date('g:i A', strtotime($program['end_time'])); ?>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="space-y-4">
                            <?php if (!empty($program['location'])): ?>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-map-marker-alt text-purple-600"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">Location</p>
                                        <p class="font-medium text-gray-900"><?php echo htmlspecialchars($program['location']); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($program['coordinator_name'])): ?>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-user text-yellow-600"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">Coordinator</p>
                                        <p class="font-medium text-gray-900"><?php echo htmlspecialchars($program['coordinator_name']); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($program['department_name'])): ?>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-building text-indigo-600"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">Department</p>
                                        <p class="font-medium text-gray-900"><?php echo htmlspecialchars($program['department_name']); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Additional Notes -->
            <?php if (!empty($program['notes'])): ?>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-800 flex items-center">
                            <i class="fas fa-sticky-note mr-3 text-primary"></i>
                            Additional Notes
                        </h2>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 leading-relaxed"><?php echo nl2br(htmlspecialchars($program['notes'])); ?></p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-8">
            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800 flex items-center">
                        <i class="fas fa-chart-bar mr-3 text-primary"></i>
                        Quick Stats
                    </h2>
                </div>
                <div class="p-6 space-y-4">
                    <?php if (isset($program['budget_allocated']) && $program['budget_allocated'] > 0): ?>
                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-dollar-sign text-green-600"></i>
                                <span class="text-sm font-medium text-gray-700">Budget</span>
                            </div>
                            <span class="font-bold text-green-600">$<?php echo number_format($program['budget_allocated'], 2); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($program['expected_attendance']) && $program['expected_attendance'] > 0): ?>
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-users text-blue-600"></i>
                                <span class="text-sm font-medium text-gray-700">Expected</span>
                            </div>
                            <span class="font-bold text-blue-600"><?php echo number_format($program['expected_attendance']); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($program['priority'])): ?>
                        <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-flag text-purple-600"></i>
                                <span class="text-sm font-medium text-gray-700">Priority</span>
                            </div>
                            <span class="font-bold text-purple-600"><?php echo ucfirst($program['priority']); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($program['requires_registration']) && $program['requires_registration']): ?>
                        <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-user-plus text-yellow-600"></i>
                                <span class="text-sm font-medium text-gray-700">Registration</span>
                            </div>
                            <span class="font-bold text-yellow-600">Required</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Program Category -->
            <?php if (!empty($program['category_name'])): ?>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-800 flex items-center">
                            <i class="fas fa-tag mr-3 text-primary"></i>
                            Category
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-4">
                            <?php if (!empty($program['color_code'])): ?>
                                <div class="w-12 h-12 rounded-lg flex items-center justify-center" style="background-color: <?php echo $program['color_code']; ?>20;">
                                    <i class="<?php echo $program['icon'] ?? 'fas fa-calendar'; ?> text-xl" style="color: <?php echo $program['color_code']; ?>"></i>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h3 class="font-semibold text-gray-900"><?php echo htmlspecialchars($program['category_name']); ?></h3>
                                <?php if (!empty($program['category_description'])): ?>
                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($program['category_description']); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Invited Guests Section -->
    <?php if (!empty($guests)): ?>
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-user-friends mr-3 text-primary"></i>
                    Invited Guests
                    <span class="ml-3 px-2 py-1 bg-primary text-white text-sm rounded-full"><?php echo count($guests); ?></span>
                </h2>
                <p class="text-gray-600 text-sm mt-2">Special guests, speakers, and ministry leaders for this program</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($guests as $guest): ?>
                        <div class="bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-200 p-6 hover:shadow-md transition-all duration-300">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-user text-primary text-lg"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <h4 class="font-semibold text-gray-900 mb-1"><?php echo htmlspecialchars($guest['guest_name']); ?></h4>

                                    <?php if (!empty($guest['guest_role'])): ?>
                                        <p class="text-sm font-medium text-primary mb-2"><?php echo htmlspecialchars($guest['guest_role']); ?></p>
                                    <?php endif; ?>

                                    <?php if (!empty($guest['organization'])): ?>
                                        <div class="flex items-center text-xs text-gray-500 mb-2">
                                            <i class="fas fa-building mr-1"></i>
                                            <?php echo htmlspecialchars($guest['organization']); ?>
                                        </div>
                                    <?php endif; ?>

                                    <div class="space-y-1">
                                        <?php if (!empty($guest['contact_email'])): ?>
                                            <div class="flex items-center text-xs text-gray-600">
                                                <i class="fas fa-envelope mr-2 w-3"></i>
                                                <a href="mailto:<?php echo htmlspecialchars($guest['contact_email']); ?>" class="hover:text-primary transition-colors">
                                                    <?php echo htmlspecialchars($guest['contact_email']); ?>
                                                </a>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (!empty($guest['contact_phone'])): ?>
                                            <div class="flex items-center text-xs text-gray-600">
                                                <i class="fas fa-phone mr-2 w-3"></i>
                                                <a href="tel:<?php echo htmlspecialchars($guest['contact_phone']); ?>" class="hover:text-primary transition-colors">
                                                    <?php echo htmlspecialchars($guest['contact_phone']); ?>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <?php if (!empty($guest['special_notes'])): ?>
                                        <div class="mt-3 p-2 bg-yellow-50 rounded border-l-2 border-yellow-200">
                                            <p class="text-xs text-gray-600">
                                                <i class="fas fa-sticky-note mr-1 text-yellow-500"></i>
                                                <?php echo htmlspecialchars($guest['special_notes']); ?>
                                            </p>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Invitation Status -->
                                    <?php if (!empty($guest['invitation_status'])): ?>
                                        <?php
                                        $status_styles = [
                                            'invited' => 'bg-blue-100 text-blue-800 border-blue-200',
                                            'confirmed' => 'bg-green-100 text-green-800 border-green-200',
                                            'declined' => 'bg-red-100 text-red-800 border-red-200',
                                            'attended' => 'bg-purple-100 text-purple-800 border-purple-200',
                                            'no_show' => 'bg-gray-100 text-gray-800 border-gray-200'
                                        ];
                                        $status_style = $status_styles[$guest['invitation_status']] ?? 'bg-gray-100 text-gray-800 border-gray-200';
                                        ?>
                                        <div class="mt-3">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border <?php echo $status_style; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $guest['invitation_status'])); ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Guest Statistics -->
    <?php if (!empty($guest_stats) && $guest_stats['total_guests'] > 0): ?>
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-chart-line mr-3 text-primary"></i>
                    Guest Statistics
                </h2>
                <p class="text-gray-600 text-sm mt-2">Overview of invited guests and their response status</p>
            </div>

            <div class="p-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                    <!-- Total Guests -->
                    <div class="relative overflow-hidden bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 rounded-xl p-6 border border-blue-200 hover:shadow-lg transition-all duration-300">
                        <div class="absolute top-0 right-0 w-20 h-20 bg-blue-500 bg-opacity-20 rounded-full -mr-10 -mt-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <i class="fas fa-users text-white text-xl"></i>
                                </div>
                                <span class="text-xs font-semibold text-blue-700 bg-blue-200 px-2 py-1 rounded-full">TOTAL</span>
                            </div>
                            <h3 class="text-3xl font-bold text-blue-700 mb-1"><?php echo $guest_stats['total_guests']; ?></h3>
                            <p class="text-sm font-medium text-blue-600">Total Guests</p>
                        </div>
                    </div>

                    <!-- Invited -->
                    <?php if ($guest_stats['invited'] > 0): ?>
                        <div class="relative overflow-hidden bg-gradient-to-br from-yellow-50 via-yellow-100 to-yellow-200 rounded-xl p-6 border border-yellow-200 hover:shadow-lg transition-all duration-300">
                            <div class="absolute top-0 right-0 w-20 h-20 bg-yellow-500 bg-opacity-20 rounded-full -mr-10 -mt-10"></div>
                            <div class="relative">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center shadow-lg">
                                        <i class="fas fa-envelope text-white text-xl"></i>
                                    </div>
                                    <span class="text-xs font-semibold text-yellow-700 bg-yellow-200 px-2 py-1 rounded-full">INVITED</span>
                                </div>
                                <h3 class="text-3xl font-bold text-yellow-700 mb-1"><?php echo $guest_stats['invited']; ?></h3>
                                <p class="text-sm font-medium text-yellow-600">Invited</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Confirmed -->
                    <?php if ($guest_stats['confirmed'] > 0): ?>
                        <div class="relative overflow-hidden bg-gradient-to-br from-green-50 via-green-100 to-green-200 rounded-xl p-6 border border-green-200 hover:shadow-lg transition-all duration-300">
                            <div class="absolute top-0 right-0 w-20 h-20 bg-green-500 bg-opacity-20 rounded-full -mr-10 -mt-10"></div>
                            <div class="relative">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center shadow-lg">
                                        <i class="fas fa-check-circle text-white text-xl"></i>
                                    </div>
                                    <span class="text-xs font-semibold text-green-700 bg-green-200 px-2 py-1 rounded-full">CONFIRMED</span>
                                </div>
                                <h3 class="text-3xl font-bold text-green-700 mb-1"><?php echo $guest_stats['confirmed']; ?></h3>
                                <p class="text-sm font-medium text-green-600">Confirmed</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Attended -->
                    <?php if ($guest_stats['attended'] > 0): ?>
                        <div class="relative overflow-hidden bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 rounded-xl p-6 border border-purple-200 hover:shadow-lg transition-all duration-300">
                            <div class="absolute top-0 right-0 w-20 h-20 bg-purple-500 bg-opacity-20 rounded-full -mr-10 -mt-10"></div>
                            <div class="relative">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                                        <i class="fas fa-user-check text-white text-xl"></i>
                                    </div>
                                    <span class="text-xs font-semibold text-purple-700 bg-purple-200 px-2 py-1 rounded-full">ATTENDED</span>
                                </div>
                                <h3 class="text-3xl font-bold text-purple-700 mb-1"><?php echo $guest_stats['attended']; ?></h3>
                                <p class="text-sm font-medium text-purple-600">Attended</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Declined -->
                    <?php if ($guest_stats['declined'] > 0): ?>
                        <div class="relative overflow-hidden bg-gradient-to-br from-red-50 via-red-100 to-red-200 rounded-xl p-6 border border-red-200 hover:shadow-lg transition-all duration-300">
                            <div class="absolute top-0 right-0 w-20 h-20 bg-red-500 bg-opacity-20 rounded-full -mr-10 -mt-10"></div>
                            <div class="relative">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center shadow-lg">
                                        <i class="fas fa-times-circle text-white text-xl"></i>
                                    </div>
                                    <span class="text-xs font-semibold text-red-700 bg-red-200 px-2 py-1 rounded-full">DECLINED</span>
                                </div>
                                <h3 class="text-3xl font-bold text-red-700 mb-1"><?php echo $guest_stats['declined']; ?></h3>
                                <p class="text-sm font-medium text-red-600">Declined</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Program Activities Section -->
    <?php if (!empty($activities)): ?>
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-tasks mr-3 text-primary"></i>
                    Program Activities
                    <span class="ml-3 px-2 py-1 bg-primary text-white text-sm rounded-full"><?php echo count($activities); ?></span>
                </h2>
                <p class="text-gray-600 text-sm mt-2">Activities and tasks associated with this program</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($activities as $activity): ?>
                        <div class="bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-300">
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-check-circle text-primary"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <h4 class="font-semibold text-gray-900 mb-1"><?php echo htmlspecialchars($activity['title']); ?></h4>
                                    <?php if (!empty($activity['description'])): ?>
                                        <p class="text-sm text-gray-600 mb-2"><?php echo htmlspecialchars(substr($activity['description'], 0, 100)); ?><?php echo strlen($activity['description']) > 100 ? '...' : ''; ?></p>
                                    <?php endif; ?>

                                    <?php if (!empty($activity['due_date'])): ?>
                                        <div class="flex items-center text-xs text-gray-500 mb-2">
                                            <i class="fas fa-calendar mr-1"></i>
                                            Due: <?php echo date('M j, Y', strtotime($activity['due_date'])); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($activity['status'])): ?>
                                        <?php
                                        $activity_status_styles = [
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'in_progress' => 'bg-blue-100 text-blue-800',
                                            'completed' => 'bg-green-100 text-green-800',
                                            'cancelled' => 'bg-red-100 text-red-800'
                                        ];
                                        $activity_status_style = $activity_status_styles[$activity['status']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo $activity_status_style; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $activity['status'])); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>





</div>
