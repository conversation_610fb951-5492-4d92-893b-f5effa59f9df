<?php
/**
 * AJAX endpoint for triggering automatic status updates
 */

header('Content-Type: application/json');

// CRITICAL SECURITY FIX: Restrict CORS to application domain only
// The wildcard '*' is dangerous and allows any website to access this API
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false || 
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
    // If origin is not allowed, we don't set the CORS header
    // This will cause the browser to block the request
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    require_once '../config/database.php';
    require_once '../controllers/AutoStatusManager.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Failed to connect to database");
    }
    
    $autoStatusManager = new AutoStatusManager($db);
    
    // Run the status update
    $updatedCount = $autoStatusManager->updateProgramStatuses();
    
    if ($updatedCount === false) {
        throw new Exception("Auto status update failed");
    }
    
    // Get recent updates to show
    $recentUpdates = $autoStatusManager->getRecentAutoUpdates(5);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'updated_count' => $updatedCount,
        'message' => "Auto status update completed. Updated {$updatedCount} programs.",
        'recent_updates' => $recentUpdates,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
