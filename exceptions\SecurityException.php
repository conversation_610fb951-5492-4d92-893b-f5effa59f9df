<?php
/**
 * Security Exception
 * 
 * Exception class for security-related errors.
 * 
 * @package Exceptions
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

require_once 'exceptions/BaseException.php';

class SecurityException extends BaseException
{
    /**
     * Get user-friendly error message
     * 
     * @return string User-friendly error message
     */
    public function getUserMessage(): string
    {
        $message = $this->getMessage();
        
        // Map technical messages to user-friendly ones
        if (strpos($message, 'CSRF') !== false) {
            return "Security token mismatch. Please refresh the page and try again.";
        }
        
        if (strpos($message, 'rate limit') !== false) {
            return "Too many requests. Please wait a moment before trying again.";
        }
        
        if (strpos($message, 'unauthorized') !== false) {
            return "You are not authorized to perform this action.";
        }
        
        if (strpos($message, 'authentication') !== false) {
            return "Please log in to continue.";
        }
        
        return "A security error occurred. Please try again or contact support.";
    }

    /**
     * Log security exception with additional details
     * 
     * @return void
     */
    protected function logException(): void
    {
        // Call parent logging
        parent::logException();
        
        // Additional security-specific logging
        $securityLogData = [
            'security_event' => 'SECURITY_EXCEPTION',
            'exception_class' => get_class($this),
            'message' => $this->getMessage(),
            'context' => $this->getContext(),
            'user_id' => $_SESSION['user_id'] ?? null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log to security log
        error_log('SECURITY_EXCEPTION: ' . json_encode($securityLogData));
    }
}
