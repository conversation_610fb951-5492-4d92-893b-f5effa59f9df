<?php
/**
 * QR Session Details Page
 * Professional design with comprehensive member engagement analytics
 */

// Ensure we have session data
if (!isset($session_details) || !$session_details) {
    echo '<div class="alert alert-danger">Session details not found.</div>';
    return;
}

$session = $session_details['session'];
$attendance_records = $session_details['attendance_records'];
$analytics = $session_details['analytics'];

// Helper function for status badges
function getStatusBadge($status) {
    $badges = [
        'active' => 'bg-emerald-50 text-emerald-700 border border-emerald-200',
        'expired' => 'bg-amber-50 text-amber-700 border border-amber-200',
        'closed' => 'bg-blue-50 text-blue-700 border border-blue-200',
        'archived' => 'bg-gray-50 text-gray-700 border border-gray-200'
    ];
    $class = $badges[$status] ?? 'bg-gray-50 text-gray-700 border border-gray-200';
    return "<span class='inline-flex items-center px-3 py-1 text-sm font-medium rounded-full $class'>" . ucfirst($status) . "</span>";
}
?>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Enhanced Professional Styling -->
<style>
.gradient-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.engagement-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.metric-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}
.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border-color: #d1d5db;
}
.alert-card {
    backdrop-filter: blur(10px);
    border-left: 4px solid;
}
.table-header {
    background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%);
}
.engagement-score-bar {
    background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
    height: 6px;
    border-radius: 3px;
}
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
}
.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}
.section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #e5e7eb 50%, transparent 100%);
    margin: 2rem 0;
}
</style>

<div class="bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Professional Header -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8 border border-gray-100">
            <div class="gradient-header px-8 py-6">
                <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-4 lg:space-y-0">
                    <div class="flex items-center space-x-4">
                        <div class="bg-white bg-opacity-20 rounded-full p-3">
                            <i class="fas fa-qrcode text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-white mb-1">QR Session Analytics</h1>
                            <div class="flex items-center space-x-3 text-white text-opacity-90">
                                <span class="flex items-center">
                                    <i class="fas fa-church mr-2"></i>
                                    <?php echo htmlspecialchars($session['service_name']); ?>
                                </span>
                                <span class="hidden sm:block">•</span>
                                <span class="flex items-center">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <?php echo date('M j, Y', strtotime($session['attendance_date'])); ?>
                                </span>
                                <span class="hidden sm:block">•</span>
                                <span><?php echo getStatusBadge($session['status']); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-3">
                        <a href="<?php echo BASE_URL; ?>attendance/qr"
                           class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-lg transition-all duration-200 text-sm font-medium">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Dashboard
                        </a>
                        <a href="<?php echo BASE_URL; ?>attendance/qr-session-export?session_id=<?php echo $session['id']; ?>"
                           class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-lg transition-all duration-200 text-sm font-medium">
                            <i class="fas fa-download mr-2"></i>
                            Export Data
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Session Overview Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10">
            <!-- Session Info Card -->
            <div class="metric-card bg-white rounded-xl shadow-sm p-6 hover:shadow-md">
                <div class="flex items-center mb-4">
                    <div class="bg-blue-50 rounded-lg p-2 mr-3">
                        <i class="fas fa-info-circle text-blue-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Session Information</h3>
                </div>
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-2 border-b border-gray-50">
                        <span class="text-gray-600 font-medium">Status</span>
                        <?php echo getStatusBadge($session['status']); ?>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-gray-50">
                        <span class="text-gray-600 font-medium">Created</span>
                        <span class="text-gray-900 font-medium"><?php echo date('M j, Y g:i A', strtotime($session['created_at'])); ?></span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-gray-50">
                        <span class="text-gray-600 font-medium">Expires</span>
                        <span class="text-gray-900 font-medium"><?php echo date('M j, Y g:i A', strtotime($session['expires_at'])); ?></span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-gray-50">
                        <span class="text-gray-600 font-medium">Created By</span>
                        <span class="text-gray-900 font-medium"><?php echo htmlspecialchars($session['created_by_name']); ?></span>
                    </div>
                    <div class="flex justify-between items-center py-2">
                        <span class="text-gray-600 font-medium">Duration</span>
                        <span class="text-gray-900 font-medium"><?php echo $analytics['session_duration_minutes']; ?> min</span>
                    </div>
                </div>
            </div>

            <!-- Attendance Summary Card -->
            <div class="metric-card bg-white rounded-xl shadow-sm p-6 hover:shadow-md">
                <div class="flex items-center mb-4">
                    <div class="bg-green-50 rounded-lg p-2 mr-3">
                        <i class="fas fa-users text-green-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Attendance Summary</h3>
                </div>
                <div class="space-y-4">
                    <div class="text-center py-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                        <div class="text-3xl font-bold text-indigo-600 mb-1"><?php echo $analytics['total_attendance']; ?></div>
                        <div class="text-sm text-gray-600 font-medium">Total Attendance</div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center py-3 bg-green-50 rounded-lg">
                            <div class="text-xl font-bold text-green-600"><?php echo $analytics['present_count']; ?></div>
                            <div class="text-xs text-gray-600">Present</div>
                        </div>
                        <div class="text-center py-3 bg-amber-50 rounded-lg">
                            <div class="text-xl font-bold text-amber-600"><?php echo $analytics['late_count']; ?></div>
                            <div class="text-xs text-gray-600">Late</div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-gray-50">
                        <span class="text-gray-600 font-medium">Attendance Rate</span>
                        <span class="font-bold text-gray-900"><?php echo $analytics['attendance_rate']; ?>%</span>
                    </div>
                    <div class="flex justify-between items-center py-2">
                        <span class="text-gray-600 font-medium">Per Hour</span>
                        <span class="font-bold text-gray-900"><?php echo number_format($analytics['attendance_per_hour'], 1); ?></span>
                    </div>
                </div>
            </div>

            <!-- Timing Analytics Card -->
            <div class="metric-card bg-white rounded-xl shadow-sm p-6 hover:shadow-md">
                <div class="flex items-center mb-4">
                    <div class="bg-purple-50 rounded-lg p-2 mr-3">
                        <i class="fas fa-clock text-purple-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Timing Analytics</h3>
                </div>
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-2 border-b border-gray-50">
                        <span class="text-gray-600 font-medium">Active Duration</span>
                        <span class="font-bold text-gray-900"><?php echo $analytics['active_duration_minutes']; ?> min</span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-gray-50">
                        <span class="text-gray-600 font-medium">Avg Response Time</span>
                        <span class="font-bold text-gray-900"><?php echo $analytics['avg_response_time_minutes']; ?> min</span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-gray-50">
                        <span class="text-gray-600 font-medium">First Attendance</span>
                        <span class="font-bold text-gray-900"><?php echo $analytics['first_attendance_time'] ? date('g:i A', strtotime($analytics['first_attendance_time'])) : 'N/A'; ?></span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-gray-50">
                        <span class="text-gray-600 font-medium">Last Attendance</span>
                        <span class="font-bold text-gray-900"><?php echo $analytics['last_attendance_time'] ? date('g:i A', strtotime($analytics['last_attendance_time'])) : 'N/A'; ?></span>
                    </div>
                    <div class="flex justify-between items-center py-2">
                        <span class="text-gray-600 font-medium">Peak Hour</span>
                        <span class="font-bold text-gray-900"><?php echo $analytics['peak_hour'] ? $analytics['peak_hour'] . ':00' : 'N/A'; ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <div class="section-divider"></div>

        <!-- Analytics Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-10">
            <!-- Gender Distribution Chart -->
            <div class="metric-card bg-white rounded-xl shadow-sm p-6 hover:shadow-md">
                <div class="flex items-center mb-6">
                    <div class="bg-pink-50 rounded-lg p-2 mr-3">
                        <i class="fas fa-venus-mars text-pink-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Gender Distribution</h3>
                </div>
                <div class="relative">
                    <div class="flex justify-center mb-4">
                        <canvas id="genderChart" width="280" height="280"></canvas>
                    </div>
                    <!-- Legend -->
                    <div class="flex justify-center space-x-6 mt-4">
                        <?php foreach ($analytics['gender_distribution'] as $gender => $count): ?>
                            <?php if ($count > 0): ?>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full mr-2" style="background-color: <?php
                                        echo $gender === 'male' ? '#FF6B6B' :
                                             ($gender === 'female' ? '#4ECDC4' :
                                              ($gender === 'other' ? '#45B7D1' : '#96CEB4'));
                                    ?>"></div>
                                    <span class="text-sm font-medium text-gray-700 capitalize"><?php echo $gender; ?></span>
                                    <span class="ml-2 text-sm text-gray-500">(<?php echo $count; ?>)</span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Department Distribution Chart -->
            <div class="metric-card bg-white rounded-xl shadow-sm p-6 hover:shadow-md">
                <div class="flex items-center mb-6">
                    <div class="bg-indigo-50 rounded-lg p-2 mr-3">
                        <i class="fas fa-sitemap text-indigo-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Department Distribution</h3>
                </div>
                <div class="relative">
                    <canvas id="departmentChart" width="400" height="300"></canvas>
                </div>
                <!-- Summary Stats -->
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-indigo-600">
                                <?php
                                echo (!empty($analytics['department_distribution']) && is_array($analytics['department_distribution']))
                                    ? count(array_filter($analytics['department_distribution']))
                                    : 0;
                                ?>
                            </div>
                            <div class="text-sm text-gray-600">Departments</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-indigo-600">
                                <?php
                                if (!empty($analytics['department_distribution']) && is_array($analytics['department_distribution'])) {
                                    arsort($analytics['department_distribution']);
                                    echo max($analytics['department_distribution']);
                                } else {
                                    echo '0';
                                }
                                ?>
                            </div>
                            <div class="text-sm text-gray-600">Largest Dept</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- Attendance Timeline -->
    <?php if (!empty($analytics['hourly_distribution'])): ?>
    <div class="metric-card bg-white rounded-xl shadow-sm p-6 mb-10 hover:shadow-md">
        <div class="flex items-center mb-6">
            <div class="bg-indigo-50 rounded-lg p-2 mr-3">
                <i class="fas fa-chart-line text-indigo-600"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Attendance Timeline</h3>
                <p class="text-sm text-gray-600">Hourly attendance distribution throughout the day</p>
            </div>
        </div>

        <!-- Line Chart Container -->
        <div class="relative">
            <div class="bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg p-4">
                <svg id="attendanceChart" width="100%" height="300" viewBox="0 0 800 300" class="overflow-visible">
                    <!-- Grid Lines -->
                    <defs>
                        <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                            <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.3" />
                            <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.05" />
                        </linearGradient>
                    </defs>

                    <!-- Background Grid -->
                    <?php for ($i = 0; $i <= 5; $i++): ?>
                        <line x1="60" y1="<?php echo 40 + ($i * 40); ?>" x2="740" y2="<?php echo 40 + ($i * 40); ?>"
                              stroke="#e5e7eb" stroke-width="1" opacity="0.5"/>
                    <?php endfor; ?>

                    <!-- Y-axis labels -->
                    <?php
                    $max_count = (!empty($analytics['hourly_distribution']) && is_array($analytics['hourly_distribution']))
                        ? max($analytics['hourly_distribution'])
                        : 1; // Default to 1 to avoid division by zero
                    for ($i = 0; $i <= 5; $i++):
                        $value = round(($max_count / 5) * (5 - $i));
                    ?>
                        <text x="50" y="<?php echo 45 + ($i * 40); ?>" text-anchor="end"
                              class="text-xs fill-gray-600" font-family="system-ui"><?php echo $value; ?></text>
                    <?php endfor; ?>

                    <!-- Chart Line and Area -->
                    <?php
                    $points = [];
                    $area_points = "60,240 ";

                    for ($hour = 0; $hour < 24; $hour++) {
                        $count = $analytics['hourly_distribution'][$hour] ?? 0;
                        $x = 60 + ($hour * 28.33); // (740-60)/24 = 28.33
                        $y = 240 - (($count / $max_count) * 200); // Scale to 200px height
                        $points[] = "$x,$y";
                        $area_points .= "$x,$y ";
                    }
                    $area_points .= "740,240";
                    ?>

                    <!-- Area under the line -->
                    <path d="M <?php echo $area_points; ?> Z" fill="url(#areaGradient)" opacity="0.6"/>

                    <!-- Main line -->
                    <path d="M <?php echo implode(' L ', $points); ?>"
                          fill="none" stroke="url(#lineGradient)" stroke-width="3"
                          stroke-linecap="round" stroke-linejoin="round"/>

                    <!-- Data points -->
                    <?php for ($hour = 0; $hour < 24; $hour++): ?>
                        <?php
                        $count = $analytics['hourly_distribution'][$hour] ?? 0;
                        $x = 60 + ($hour * 28.33);
                        $y = 240 - (($count / $max_count) * 200);
                        ?>
                        <circle cx="<?php echo $x; ?>" cy="<?php echo $y; ?>" r="4"
                                fill="white" stroke="url(#lineGradient)" stroke-width="2"
                                class="hover:r-6 transition-all duration-200 cursor-pointer"
                                onmouseover="showTooltip(event, '<?php echo sprintf('%02d:00', $hour); ?>', <?php echo $count; ?>)"
                                onmouseout="hideTooltip()"/>
                    <?php endfor; ?>

                    <!-- X-axis labels -->
                    <?php for ($hour = 0; $hour < 24; $hour += 3): ?>
                        <text x="<?php echo 60 + ($hour * 28.33); ?>" y="265" text-anchor="middle"
                              class="text-xs fill-gray-600" font-family="system-ui"><?php echo sprintf('%02d:00', $hour); ?></text>
                    <?php endfor; ?>
                </svg>

                <!-- Tooltip -->
                <div id="chartTooltip" class="absolute bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg pointer-events-none opacity-0 transition-opacity duration-200 z-10">
                    <div id="tooltipContent"></div>
                    <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                </div>
            </div>

            <!-- Chart Legend -->
            <div class="flex items-center justify-center mt-4 space-x-6">
                <div class="flex items-center">
                    <div class="w-4 h-0.5 bg-gradient-to-r from-indigo-500 to-purple-600 mr-2"></div>
                    <span class="text-sm text-gray-600">Attendance Count</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-white border-2 border-indigo-500 rounded-full mr-2"></div>
                    <span class="text-sm text-gray-600">Data Points</span>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

        <div class="section-divider"></div>

        <!-- Member Engagement Analysis -->
        <?php if (isset($session_details['member_engagement']) && !empty($session_details['member_engagement']['members'])): ?>
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-10 border border-gray-100">
            <div class="engagement-card px-8 py-6">
                <div class="flex items-center space-x-4">
                    <div class="bg-white bg-opacity-20 rounded-full p-3">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white mb-1">Member Engagement Analysis</h3>
                        <p class="text-white text-opacity-90 text-sm">Historical insights and actionable recommendations for pastoral care</p>
                    </div>
                </div>
            </div>

            <!-- Engagement Summary -->
            <div class="px-8 py-6 bg-gray-50 border-b border-gray-100">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="text-center bg-white rounded-xl p-4 shadow-sm">
                        <div class="text-3xl font-bold text-purple-600 mb-2"><?php echo $session_details['member_engagement']['summary']['avg_engagement_score']; ?>%</div>
                        <div class="text-sm text-gray-600 font-medium">Avg Engagement Score</div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: <?php echo $session_details['member_engagement']['summary']['avg_engagement_score']; ?>%"></div>
                        </div>
                    </div>
                    <div class="text-center bg-white rounded-xl p-4 shadow-sm">
                        <div class="text-3xl font-bold text-red-600 mb-2"><?php echo $session_details['member_engagement']['summary']['risk_distribution']['High']; ?></div>
                        <div class="text-sm text-gray-600 font-medium">High Risk Members</div>
                        <div class="text-xs text-red-600 mt-1">Immediate attention needed</div>
                    </div>
                    <div class="text-center bg-white rounded-xl p-4 shadow-sm">
                        <div class="text-3xl font-bold text-green-600 mb-2"><?php echo $session_details['member_engagement']['summary']['frequency_distribution']['Regular']; ?></div>
                        <div class="text-sm text-gray-600 font-medium">Regular Attendees</div>
                        <div class="text-xs text-green-600 mt-1">Highly engaged members</div>
                    </div>
                    <div class="text-center bg-white rounded-xl p-4 shadow-sm">
                        <div class="text-3xl font-bold text-orange-600 mb-2"><?php echo $session_details['member_engagement']['summary']['action_needed']; ?></div>
                        <div class="text-sm text-gray-600 font-medium">Need Follow-up</div>
                        <div class="text-xs text-orange-600 mt-1">Actionable recommendations</div>
                    </div>
                </div>
            </div>

            <!-- At-Risk Members Alert -->
            <?php if (!empty($session_details['member_engagement']['summary']['at_risk_members'])): ?>
            <div class="alert-card mx-8 mb-4 bg-red-50 border-red-400 rounded-xl p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-red-100 rounded-full p-2 mr-4">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-semibold text-red-800">Members Needing Immediate Attention</h4>
                            <button onclick="showAtRiskMembersModal()"
                                    class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors duration-200">
                                <i class="fas fa-eye mr-2"></i>
                                View All (<?php echo count($session_details['member_engagement']['summary']['at_risk_members']); ?>)
                            </button>
                        </div>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <?php foreach (array_slice($session_details['member_engagement']['summary']['at_risk_members'], 0, 3) as $member): ?>
                                <span class="inline-flex items-center bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                                    <i class="fas fa-user-times mr-2"></i>
                                    <?php echo htmlspecialchars($member['name']); ?>
                                    <span class="ml-1 text-xs">(<?php echo $member['risk_level']; ?> Risk)</span>
                                </span>
                            <?php endforeach; ?>
                            <?php if (count($session_details['member_engagement']['summary']['at_risk_members']) > 3): ?>
                                <span class="inline-flex items-center bg-red-200 text-red-800 px-3 py-1 rounded-full text-sm font-medium cursor-pointer hover:bg-red-300 transition-colors duration-200"
                                      onclick="showAtRiskMembersModal()">
                                    +<?php echo count($session_details['member_engagement']['summary']['at_risk_members']) - 3; ?> more members
                                </span>
                            <?php endif; ?>
                        </div>
                        <p class="text-red-700 text-sm">
                            <i class="fas fa-clock mr-1"></i>
                            Recommended action: Contact within 48 hours for personal follow-up
                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- High Performers Recognition -->
            <?php if (!empty($session_details['member_engagement']['summary']['high_performers'])): ?>
            <div class="alert-card mx-8 mb-6 bg-green-50 border-green-400 rounded-xl p-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-green-100 rounded-full p-2 mr-4">
                        <i class="fas fa-star text-green-600"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-lg font-semibold text-green-800">High Engagement Members</h4>
                            <button onclick="showHighPerformersModal()"
                                    class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm font-medium transition-colors duration-200">
                                <i class="fas fa-star mr-2"></i>
                                View All (<?php echo count($session_details['member_engagement']['summary']['high_performers']); ?>)
                            </button>
                        </div>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <?php foreach (array_slice($session_details['member_engagement']['summary']['high_performers'], 0, 5) as $member): ?>
                                <span class="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                    <i class="fas fa-trophy mr-2"></i>
                                    <?php echo htmlspecialchars($member['name']); ?>
                                    <span class="ml-1 text-xs">(<?php echo $member['engagement_score']; ?>%)</span>
                                </span>
                            <?php endforeach; ?>
                            <?php if (count($session_details['member_engagement']['summary']['high_performers']) > 5): ?>
                                <span class="inline-flex items-center bg-green-200 text-green-800 px-3 py-1 rounded-full text-sm font-medium cursor-pointer hover:bg-green-300 transition-colors duration-200"
                                      onclick="showHighPerformersModal()">
                                    +<?php echo count($session_details['member_engagement']['summary']['high_performers']) - 5; ?> more members
                                </span>
                            <?php endif; ?>
                        </div>
                        <p class="text-green-700 text-sm">
                            <i class="fas fa-lightbulb mr-1"></i>
                            Consider these members for leadership opportunities and ministry roles
                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="section-divider"></div>

        <!-- Detailed Attendance Records -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
            <div class="table-header px-8 py-6 border-b border-gray-100">
                <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-4 lg:space-y-0">
                    <div class="flex items-center space-x-3">
                        <div class="bg-blue-50 rounded-lg p-2">
                            <i class="fas fa-users text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">Attendance Records</h3>
                            <p class="text-sm text-gray-600">
                                <span id="totalRecords"><?php echo count($attendance_records); ?></span> members attended this session
                                <span id="filteredCount" class="hidden"> • <span id="filteredNumber">0</span> filtered results</span>
                            </p>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <!-- Real-time Search -->
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" id="attendanceSearch"
                                   placeholder="Search members..."
                                   class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm w-64"
                                   onkeyup="searchAttendance()">
                        </div>
                        <?php if (isset($session_details['member_engagement']) && !empty($session_details['member_engagement']['members'])): ?>
                        <button onclick="toggleEngagementView()" class="btn-primary text-white px-6 py-3 rounded-lg text-sm font-medium inline-flex items-center">
                            <i class="fas fa-chart-line mr-2"></i>
                            Show Engagement Details
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        
            <?php if (empty($attendance_records)): ?>
                <div class="p-12 text-center">
                    <div class="bg-gray-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-users text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Attendance Records</h4>
                    <p class="text-gray-500">Members will appear here once they scan the QR code for this session.</p>
                </div>
            <?php else: ?>
                <!-- Standard Attendance View -->
                <div id="standard-view" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="table-header">
                            <tr>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Member</th>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Department</th>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Status</th>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Time Marked</th>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Response Time</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-100">
                            <?php foreach ($attendance_records as $record): ?>
                                <tr class="hover:bg-blue-50 transition-colors duration-200">
                                    <td class="px-8 py-6 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-12 w-12">
                                                <div class="h-12 w-12 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center shadow-sm">
                                                    <span class="text-sm font-bold text-white">
                                                        <?php echo strtoupper(substr($record['first_name'], 0, 1) . substr($record['last_name'], 0, 1)); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-semibold text-gray-900">
                                                    <?php echo htmlspecialchars($record['first_name'] . ' ' . $record['last_name']); ?>
                                                </div>
                                                <div class="text-sm text-gray-500 flex items-center">
                                                    <i class="fas fa-phone mr-1 text-xs"></i>
                                                    <?php echo htmlspecialchars($record['phone_number'] ?? 'No phone'); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-8 py-6 whitespace-nowrap">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                            <i class="fas fa-building mr-2 text-xs"></i>
                                            <?php echo htmlspecialchars($record['department'] ?? 'Unknown'); ?>
                                        </span>
                                    </td>
                                    <td class="px-8 py-6 whitespace-nowrap">
                                        <?php
                                        $status_class = $record['status'] === 'present' ? 'bg-emerald-100 text-emerald-800 border border-emerald-200' : 'bg-amber-100 text-amber-800 border border-amber-200';
                                        $status_icon = $record['status'] === 'present' ? 'fas fa-check-circle' : 'fas fa-clock';
                                        echo "<span class='inline-flex items-center px-3 py-1 text-sm font-medium rounded-full $status_class'>";
                                        echo "<i class='$status_icon mr-2 text-xs'></i>" . ucfirst($record['status']);
                                        echo "</span>";
                                        ?>
                                    </td>
                                    <td class="px-8 py-6 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <div class="flex items-center">
                                            <i class="fas fa-calendar-alt mr-2 text-gray-400 text-xs"></i>
                                            <?php echo date('M j, g:i A', strtotime($record['created_at'])); ?>
                                        </div>
                                    </td>
                                    <td class="px-8 py-6 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <div class="flex items-center">
                                            <i class="fas fa-stopwatch mr-2 text-gray-400 text-xs"></i>
                                            +<?php echo $record['minutes_after_start']; ?> min
                                        </div>
                                    </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination Controls -->
            <div class="bg-gray-50 px-8 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="flex items-center text-sm text-gray-700">
                    <span>Showing</span>
                    <span class="mx-1 font-medium" id="startRecord">1</span>
                    <span>to</span>
                    <span class="mx-1 font-medium" id="endRecord">10</span>
                    <span>of</span>
                    <span class="mx-1 font-medium" id="totalRecordsPagination"><?php echo count($attendance_records); ?></span>
                    <span>results</span>
                </div>
                <div class="flex items-center space-x-2">
                    <label for="recordsPerPage" class="text-sm text-gray-700">Show:</label>
                    <select id="recordsPerPage" onchange="changeRecordsPerPage()"
                            class="border border-gray-300 rounded px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <div class="flex space-x-1 ml-4">
                        <button id="prevPage" onclick="changePage(-1)"
                                class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div id="pageNumbers" class="flex space-x-1">
                            <!-- Page numbers will be generated by JavaScript -->
                        </div>
                        <button id="nextPage" onclick="changePage(1)"
                                class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Engagement Analysis View -->
            <?php if (isset($session_details['member_engagement']) && !empty($session_details['member_engagement']['members'])): ?>
                <!-- Engagement Analysis View -->
                <div id="engagement-view" style="display: none;" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gradient-to-r from-purple-50 to-indigo-50">
                            <tr>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-purple-700 uppercase tracking-wider">Member</th>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-purple-700 uppercase tracking-wider">Engagement Score</th>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-purple-700 uppercase tracking-wider">Frequency</th>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-purple-700 uppercase tracking-wider">Risk Level</th>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-purple-700 uppercase tracking-wider">Trend</th>
                                <th class="px-8 py-4 text-left text-xs font-semibold text-purple-700 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-100">
                            <?php foreach ($session_details['member_engagement']['members'] as $member): ?>
                                <tr class="hover:bg-gradient-to-r hover:from-purple-50 hover:to-indigo-50 transition-all duration-200">
                                    <td class="px-8 py-6 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-12 w-12">
                                                <div class="h-12 w-12 rounded-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center shadow-sm">
                                                    <span class="text-sm font-bold text-white">
                                                        <?php
                                                        $name_parts = explode(' ', $member['name']);
                                                        echo strtoupper(substr($name_parts[0], 0, 1) . substr($name_parts[1] ?? '', 0, 1));
                                                        ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-semibold text-gray-900">
                                                    <?php echo htmlspecialchars($member['name']); ?>
                                                </div>
                                                <div class="text-sm text-gray-500 flex items-center">
                                                    <i class="fas fa-building mr-1 text-xs"></i>
                                                    <?php echo htmlspecialchars($member['department'] ?? 'Unknown'); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-8 py-6 whitespace-nowrap">
                                        <div class="flex items-center space-x-3">
                                            <div class="flex-1">
                                                <div class="flex items-center justify-between mb-1">
                                                    <span class="text-sm font-semibold text-gray-900"><?php echo $member['engagement_score']; ?>%</span>
                                                    <span class="text-xs text-gray-500">
                                                        <?php echo $member['engagement_score'] >= 80 ? 'Excellent' : ($member['engagement_score'] >= 60 ? 'Good' : ($member['engagement_score'] >= 40 ? 'Fair' : 'Poor')); ?>
                                                    </span>
                                                </div>
                                                <div class="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                                                    <div class="engagement-score-bar rounded-full h-3 transition-all duration-500"
                                                         style="width: <?php echo $member['engagement_score']; ?>%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-8 py-6 whitespace-nowrap">
                                        <span class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-full border
                                            <?php echo $member['attendance_frequency'] === 'Regular' ? 'bg-emerald-50 text-emerald-700 border-emerald-200' :
                                                      ($member['attendance_frequency'] === 'Occasional' ? 'bg-amber-50 text-amber-700 border-amber-200' :
                                                       ($member['attendance_frequency'] === 'Infrequent' ? 'bg-orange-50 text-orange-700 border-orange-200' : 'bg-red-50 text-red-700 border-red-200')); ?>">
                                            <?php
                                            $freq_icon = $member['attendance_frequency'] === 'Regular' ? 'fas fa-star' :
                                                        ($member['attendance_frequency'] === 'Occasional' ? 'fas fa-star-half-alt' :
                                                         ($member['attendance_frequency'] === 'Infrequent' ? 'far fa-star' : 'fas fa-times'));
                                            echo "<i class='$freq_icon mr-2 text-xs'></i>";
                                            echo $member['attendance_frequency'];
                                            ?>
                                        </span>
                                    </td>
                                    <td class="px-8 py-6 whitespace-nowrap">
                                        <span class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-full border
                                            <?php echo $member['risk_level'] === 'Low' ? 'bg-emerald-50 text-emerald-700 border-emerald-200' :
                                                      ($member['risk_level'] === 'Medium' ? 'bg-amber-50 text-amber-700 border-amber-200' : 'bg-red-50 text-red-700 border-red-200'); ?>">
                                            <?php
                                            $risk_icon = $member['risk_level'] === 'Low' ? 'fas fa-shield-alt' :
                                                        ($member['risk_level'] === 'Medium' ? 'fas fa-exclamation-triangle' : 'fas fa-exclamation-circle');
                                            echo "<i class='$risk_icon mr-2 text-xs'></i>";
                                            echo $member['risk_level'] . ' Risk';
                                            ?>
                                        </span>
                                    </td>
                                    <td class="px-8 py-6 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-full
                                                <?php echo $member['trend'] === 'Improving' ? 'bg-green-50 text-green-700 border border-green-200' :
                                                          ($member['trend'] === 'Declining' ? 'bg-red-50 text-red-700 border border-red-200' : 'bg-gray-50 text-gray-700 border border-gray-200'); ?>">
                                                <?php if ($member['trend'] === 'Improving'): ?>
                                                    <i class="fas fa-trending-up mr-2 text-xs"></i>
                                                <?php elseif ($member['trend'] === 'Declining'): ?>
                                                    <i class="fas fa-trending-down mr-2 text-xs"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-minus mr-2 text-xs"></i>
                                                <?php endif; ?>
                                                <?php echo $member['trend']; ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-8 py-6 whitespace-nowrap text-sm">
                                        <?php if (!empty($member['recommendations'])): ?>
                                            <button onclick="showRecommendations(<?php echo $member['member_id']; ?>)"
                                                    class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg hover:from-purple-600 hover:to-indigo-700 transition-all duration-200 font-medium shadow-sm">
                                                <i class="fas fa-lightbulb mr-2 text-xs"></i>
                                                <?php echo count($member['recommendations']); ?> Actions
                                            </button>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-500 rounded-lg text-sm">
                                                <i class="fas fa-check mr-2 text-xs"></i>
                                                No actions needed
                                            </span>
                                        <?php endif; ?>
                                    </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Professional Recommendations Modal -->
<div id="recommendationsModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full z-50" style="display: none;">
    <div class="relative top-20 mx-auto p-6 border-0 w-full max-w-lg shadow-2xl rounded-2xl bg-white">
        <div class="relative">
            <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="bg-purple-100 rounded-full p-2">
                        <i class="fas fa-lightbulb text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900" id="modalMemberName">Member Recommendations</h3>
                </div>
                <button onclick="closeRecommendations()" class="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-colors duration-200">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
            <div id="modalContent" class="space-y-4 max-h-96 overflow-y-auto">
                <!-- Recommendations will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- At-Risk Members Modal -->
<div id="atRiskMembersModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full z-50" style="display: none;">
    <div class="relative top-10 mx-auto p-6 border-0 w-full max-w-4xl shadow-2xl rounded-2xl bg-white">
        <div class="relative">
            <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="bg-red-100 rounded-full p-2">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900">Members Needing Immediate Attention</h3>
                </div>
                <button onclick="closeAtRiskMembersModal()" class="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-colors duration-200">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
            <div class="max-h-96 overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <?php if (isset($session_details['member_engagement']['summary']['at_risk_members'])): ?>
                        <?php foreach ($session_details['member_engagement']['summary']['at_risk_members'] as $member): ?>
                            <div class="bg-red-50 border border-red-200 rounded-lg p-5 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="h-12 w-12 rounded-full bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center shadow-sm">
                                            <span class="text-sm font-bold text-white">
                                                <?php
                                                $name_parts = explode(' ', $member['name']);
                                                echo strtoupper(substr($name_parts[0], 0, 1) . substr($name_parts[1] ?? '', 0, 1));
                                                ?>
                                            </span>
                                        </div>
                                        <div>
                                            <h4 class="font-bold text-red-800 text-lg"><?php echo htmlspecialchars($member['name']); ?></h4>
                                            <div class="flex items-center text-sm text-red-600 mt-1">
                                                <i class="fas fa-building mr-2 text-xs"></i>
                                                <?php echo htmlspecialchars($member['department'] ?? 'Unknown Department'); ?>
                                            </div>
                                            <div class="flex items-center text-sm text-red-600 mt-1">
                                                <i class="fas fa-phone mr-2 text-xs"></i>
                                                <?php echo htmlspecialchars($member['phone_number'] ?? 'No phone number'); ?>
                                            </div>
                                            <?php if (!empty($member['location'])): ?>
                                            <div class="flex items-center text-sm text-red-600 mt-1">
                                                <i class="fas fa-map-marker-alt mr-2 text-xs"></i>
                                                <?php echo htmlspecialchars($member['location']); ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-3 py-1 text-xs font-bold rounded-full bg-red-200 text-red-800 border border-red-300">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        <?php echo $member['risk_level']; ?> Risk
                                    </span>
                                </div>

                                <div class="grid grid-cols-2 gap-4 pt-3 border-t border-red-200">
                                    <div class="text-center">
                                        <div class="text-xs text-red-600 font-medium uppercase tracking-wide">Engagement Score</div>
                                        <div class="text-lg font-bold text-red-800 mt-1"><?php echo $member['engagement_score'] ?? 'N/A'; ?>%</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-xs text-red-600 font-medium uppercase tracking-wide">Frequency</div>
                                        <div class="text-sm font-semibold text-red-800 mt-1"><?php echo $member['attendance_frequency'] ?? 'Unknown'; ?></div>
                                    </div>
                                </div>

                                <?php if (!empty($member['last_attendance'])): ?>
                                <div class="mt-3 pt-3 border-t border-red-200">
                                    <div class="flex items-center justify-center text-sm text-red-700">
                                        <i class="fas fa-calendar-alt mr-2 text-xs"></i>
                                        <span class="font-medium">Last seen: <?php echo date('M j, Y', strtotime($member['last_attendance'])); ?></span>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Action Buttons -->
                                <div class="mt-4 pt-3 border-t border-red-200">
                                    <div class="flex items-center justify-center space-x-2">
                                        <?php if (!empty($member['phone_number'])): ?>
                                            <button onclick="sendSMSToMember('<?php echo $member['member_id']; ?>', '<?php echo htmlspecialchars($member['name']); ?>', '<?php echo htmlspecialchars($member['phone_number']); ?>')"
                                                    class="inline-flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-lg transition-colors duration-200">
                                                <i class="fas fa-sms mr-1"></i>
                                                Send SMS
                                            </button>
                                            <a href="tel:<?php echo $member['phone_number']; ?>"
                                               class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-lg transition-colors duration-200">
                                                <i class="fas fa-phone mr-1"></i>
                                                Call
                                            </a>
                                        <?php else: ?>
                                            <span class="text-xs text-red-500">
                                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                                No phone number available
                                            </span>
                                        <?php endif; ?>
                                        <a href="<?php echo BASE_URL; ?>members/view/<?php echo $member['member_id']; ?>"
                                           class="inline-flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-xs font-medium rounded-lg transition-colors duration-200">
                                            <i class="fas fa-user mr-1"></i>
                                            View Profile
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Modal Footer with Bulk Actions -->
            <div class="mt-6 pt-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-600">
                        <?php
                        $at_risk_count = isset($session_details['member_engagement']['summary']['at_risk_members']) ? count($session_details['member_engagement']['summary']['at_risk_members']) : 0;
                        echo $at_risk_count; ?> member<?php echo $at_risk_count !== 1 ? 's' : ''; ?> need immediate attention
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="<?php echo BASE_URL; ?>attendance/reminders"
                           class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Send Bulk SMS
                        </a>
                        <button onclick="closeAtRiskMembersModal()"
                                class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- High Performers Modal -->
<div id="highPerformersModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full z-50" style="display: none;">
    <div class="relative top-10 mx-auto p-6 border-0 w-full max-w-4xl shadow-2xl rounded-2xl bg-white">
        <div class="relative">
            <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 rounded-full p-2">
                        <i class="fas fa-star text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900">High Engagement Members</h3>
                </div>
                <button onclick="closeHighPerformersModal()" class="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-colors duration-200">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
            <div class="max-h-96 overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <?php if (isset($session_details['member_engagement']['summary']['high_performers'])): ?>
                        <?php foreach ($session_details['member_engagement']['summary']['high_performers'] as $member): ?>
                            <div class="bg-green-50 border border-green-200 rounded-lg p-5 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="h-12 w-12 rounded-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-sm">
                                            <span class="text-sm font-bold text-white">
                                                <?php
                                                $name_parts = explode(' ', $member['name']);
                                                echo strtoupper(substr($name_parts[0], 0, 1) . substr($name_parts[1] ?? '', 0, 1));
                                                ?>
                                            </span>
                                        </div>
                                        <div>
                                            <h4 class="font-bold text-green-800 text-lg"><?php echo htmlspecialchars($member['name']); ?></h4>
                                            <div class="flex items-center text-sm text-green-600 mt-1">
                                                <i class="fas fa-building mr-2 text-xs"></i>
                                                <?php echo htmlspecialchars($member['department'] ?? 'Unknown Department'); ?>
                                            </div>
                                            <div class="flex items-center text-sm text-green-600 mt-1">
                                                <i class="fas fa-phone mr-2 text-xs"></i>
                                                <?php echo htmlspecialchars($member['phone_number'] ?? 'No phone number'); ?>
                                            </div>
                                            <?php if (!empty($member['location'])): ?>
                                            <div class="flex items-center text-sm text-green-600 mt-1">
                                                <i class="fas fa-map-marker-alt mr-2 text-xs"></i>
                                                <?php echo htmlspecialchars($member['location']); ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-3 py-1 text-xs font-bold rounded-full bg-green-200 text-green-800 border border-green-300">
                                        <i class="fas fa-star mr-1"></i>
                                        <?php echo $member['engagement_score']; ?>% Score
                                    </span>
                                </div>

                                <div class="grid grid-cols-2 gap-4 pt-3 border-t border-green-200">
                                    <div class="text-center">
                                        <div class="text-xs text-green-600 font-medium uppercase tracking-wide">Frequency</div>
                                        <div class="text-sm font-semibold text-green-800 mt-1"><?php echo $member['attendance_frequency'] ?? 'Regular'; ?></div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-xs text-green-600 font-medium uppercase tracking-wide">Leadership</div>
                                        <div class="text-sm font-semibold text-green-800 mt-1">
                                            <i class="fas fa-crown mr-1 text-yellow-500"></i>High Potential
                                        </div>
                                    </div>
                                </div>

                                <?php if (!empty($member['last_attendance'])): ?>
                                <div class="mt-3 pt-3 border-t border-green-200">
                                    <div class="flex items-center justify-center text-sm text-green-700">
                                        <i class="fas fa-calendar-check mr-2 text-xs"></i>
                                        <span class="font-medium">Last seen: <?php echo date('M j, Y', strtotime($member['last_attendance'])); ?></span>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Action Buttons -->
                                <div class="mt-3 pt-3 border-t border-green-200">
                                    <div class="flex items-center justify-center space-x-2">
                                        <?php if (!empty($member['phone_number'])): ?>
                                            <button onclick="sendAppreciationSMS('<?php echo $member['member_id']; ?>', '<?php echo htmlspecialchars($member['name']); ?>', '<?php echo htmlspecialchars($member['phone_number']); ?>')"
                                                    class="inline-flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-lg transition-colors duration-200">
                                                <i class="fas fa-heart mr-1"></i>
                                                Send Appreciation
                                            </button>
                                            <a href="tel:<?php echo $member['phone_number']; ?>"
                                               class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-lg transition-colors duration-200">
                                                <i class="fas fa-phone mr-1"></i>
                                                Call
                                            </a>
                                        <?php else: ?>
                                            <span class="text-xs text-green-500">
                                                <i class="fas fa-info-circle mr-1"></i>
                                                No phone number available
                                            </span>
                                        <?php endif; ?>
                                        <a href="<?php echo BASE_URL; ?>members/view/<?php echo $member['member_id']; ?>"
                                           class="inline-flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-xs font-medium rounded-lg transition-colors duration-200">
                                            <i class="fas fa-user mr-1"></i>
                                            View Profile
                                        </a>
                                    </div>
                                </div>

                                <!-- Leadership Recommendation -->
                                <div class="mt-3 pt-3 border-t border-green-200">
                                    <div class="bg-green-100 rounded-lg p-3">
                                        <div class="flex items-center text-sm text-green-800">
                                            <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>
                                            <span class="font-medium">Consider for leadership opportunities and ministry roles</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Modal Footer with Bulk Actions -->
            <div class="mt-6 pt-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-600">
                        <?php
                        $high_performers_count = isset($session_details['member_engagement']['summary']['high_performers']) ? count($session_details['member_engagement']['summary']['high_performers']) : 0;
                        echo $high_performers_count; ?> high engagement member<?php echo $high_performers_count !== 1 ? 's' : ''; ?>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button onclick="sendBulkAppreciation()"
                                class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fas fa-heart mr-2"></i>
                            Send Bulk Appreciation
                        </button>
                        <button onclick="closeHighPerformersModal()"
                                class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Store analytics data for charts
const genderData = <?php echo json_encode($analytics['gender_distribution'] ?? []); ?>;
const departmentData = <?php echo json_encode($analytics['department_distribution'] ?? []); ?>;

// Store member engagement data for JavaScript access
const memberEngagementData = <?php echo isset($session_details['member_engagement']) ? json_encode($session_details['member_engagement']['members']) : '[]'; ?>;

// Store attendance records for pagination and search
const attendanceRecords = <?php echo json_encode($attendance_records); ?>;
let filteredRecords = [...attendanceRecords];
let currentPage = 1;
let recordsPerPage = 10;

// Modal functions for At-Risk Members
function showAtRiskMembersModal() {
    document.getElementById('atRiskMembersModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeAtRiskMembersModal() {
    document.getElementById('atRiskMembersModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Modal functions for High Performers
function showHighPerformersModal() {
    document.getElementById('highPerformersModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeHighPerformersModal() {
    document.getElementById('highPerformersModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Real-time search function
function searchAttendance() {
    const searchTerm = document.getElementById('attendanceSearch').value.toLowerCase();

    if (searchTerm === '') {
        filteredRecords = [...attendanceRecords];
        document.getElementById('filteredCount').classList.add('hidden');
    } else {
        filteredRecords = attendanceRecords.filter(record => {
            const fullName = (record.first_name + ' ' + record.last_name).toLowerCase();
            const phone = (record.phone_number || '').toLowerCase();
            const department = (record.department || '').toLowerCase();

            return fullName.includes(searchTerm) ||
                   phone.includes(searchTerm) ||
                   department.includes(searchTerm);
        });

        document.getElementById('filteredCount').classList.remove('hidden');
        document.getElementById('filteredNumber').textContent = filteredRecords.length;
    }

    currentPage = 1;
    updateTable();
    updatePagination();
}

// Pagination functions
function changePage(direction) {
    const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);

    if (direction === 1 && currentPage < totalPages) {
        currentPage++;
    } else if (direction === -1 && currentPage > 1) {
        currentPage--;
    }

    updateTable();
    updatePagination();
}

function goToPage(page) {
    currentPage = page;
    updateTable();
    updatePagination();
}

function changeRecordsPerPage() {
    recordsPerPage = parseInt(document.getElementById('recordsPerPage').value);
    currentPage = 1;
    updateTable();
    updatePagination();
}

// Update table with current page data
function updateTable() {
    const startIndex = (currentPage - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    const pageRecords = filteredRecords.slice(startIndex, endIndex);

    const tbody = document.querySelector('#standard-view tbody');
    tbody.innerHTML = '';

    pageRecords.forEach(record => {
        const row = createTableRow(record);
        tbody.appendChild(row);
    });

    // Update record count display
    const start = filteredRecords.length === 0 ? 0 : startIndex + 1;
    const end = Math.min(endIndex, filteredRecords.length);

    document.getElementById('startRecord').textContent = start;
    document.getElementById('endRecord').textContent = end;
    document.getElementById('totalRecordsPagination').textContent = filteredRecords.length;
}

// Create table row HTML
function createTableRow(record) {
    const row = document.createElement('tr');
    row.className = 'hover:bg-blue-50 transition-colors duration-200';

    const initials = record.first_name.charAt(0).toUpperCase() + record.last_name.charAt(0).toUpperCase();
    const statusClass = record.status === 'present' ? 'bg-emerald-100 text-emerald-800 border border-emerald-200' : 'bg-amber-100 text-amber-800 border border-amber-200';
    const statusIcon = record.status === 'present' ? 'fas fa-check-circle' : 'fas fa-clock';

    row.innerHTML = `
        <td class="px-8 py-6 whitespace-nowrap">
            <div class="flex items-center">
                <div class="flex-shrink-0 h-12 w-12">
                    <div class="h-12 w-12 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center shadow-sm">
                        <span class="text-sm font-bold text-white">${initials}</span>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-semibold text-gray-900">${record.first_name} ${record.last_name}</div>
                    <div class="text-sm text-gray-500 flex items-center">
                        <i class="fas fa-phone mr-1 text-xs"></i>
                        ${record.phone_number || 'No phone'}
                    </div>
                </div>
            </div>
        </td>
        <td class="px-8 py-6 whitespace-nowrap">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                <i class="fas fa-building mr-2 text-xs"></i>
                ${record.department || 'Unknown'}
            </span>
        </td>
        <td class="px-8 py-6 whitespace-nowrap">
            <span class="inline-flex items-center px-3 py-1 text-sm font-medium rounded-full ${statusClass}">
                <i class="${statusIcon} mr-2 text-xs"></i>
                ${record.status.charAt(0).toUpperCase() + record.status.slice(1)}
            </span>
        </td>
        <td class="px-8 py-6 whitespace-nowrap text-sm text-gray-900">
            <div class="flex items-center">
                <i class="fas fa-clock mr-2 text-gray-400 text-xs"></i>
                ${new Date(record.marked_at).toLocaleTimeString()}
            </div>
        </td>
        <td class="px-8 py-6 whitespace-nowrap text-sm font-medium text-gray-900">
            <div class="flex items-center">
                <i class="fas fa-stopwatch mr-2 text-gray-400 text-xs"></i>
                +${record.minutes_after_start} min
            </div>
        </td>
    `;

    return row;
}

// Update pagination controls
function updatePagination() {
    const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);
    const pageNumbers = document.getElementById('pageNumbers');
    const prevButton = document.getElementById('prevPage');
    const nextButton = document.getElementById('nextPage');

    // Update prev/next buttons
    prevButton.disabled = currentPage === 1;
    nextButton.disabled = currentPage === totalPages || totalPages === 0;

    // Generate page numbers
    pageNumbers.innerHTML = '';

    if (totalPages <= 7) {
        // Show all pages if 7 or fewer
        for (let i = 1; i <= totalPages; i++) {
            pageNumbers.appendChild(createPageButton(i));
        }
    } else {
        // Show first page
        pageNumbers.appendChild(createPageButton(1));

        if (currentPage > 4) {
            pageNumbers.appendChild(createEllipsis());
        }

        // Show pages around current page
        const start = Math.max(2, currentPage - 1);
        const end = Math.min(totalPages - 1, currentPage + 1);

        for (let i = start; i <= end; i++) {
            pageNumbers.appendChild(createPageButton(i));
        }

        if (currentPage < totalPages - 3) {
            pageNumbers.appendChild(createEllipsis());
        }

        // Show last page
        if (totalPages > 1) {
            pageNumbers.appendChild(createPageButton(totalPages));
        }
    }
}

function createPageButton(page) {
    const button = document.createElement('button');
    button.textContent = page;
    button.onclick = () => goToPage(page);
    button.className = `px-3 py-1 border text-sm ${
        page === currentPage
            ? 'bg-blue-500 text-white border-blue-500'
            : 'border-gray-300 hover:bg-gray-100'
    }`;
    return button;
}

function createEllipsis() {
    const span = document.createElement('span');
    span.textContent = '...';
    span.className = 'px-3 py-1 text-sm text-gray-500';
    return span;
}

// Initialize pagination on page load
document.addEventListener('DOMContentLoaded', function() {
    updateTable();
    updatePagination();

    // Close modals when clicking outside
    window.onclick = function(event) {
        const recommendationsModal = document.getElementById('recommendationsModal');
        const atRiskModal = document.getElementById('atRiskMembersModal');
        const highPerformersModal = document.getElementById('highPerformersModal');

        if (event.target === recommendationsModal) {
            closeRecommendations();
        }
        if (event.target === atRiskModal) {
            closeAtRiskMembersModal();
        }
        if (event.target === highPerformersModal) {
            closeHighPerformersModal();
        }
    }
});

function toggleEngagementView() {
    const standardView = document.getElementById('standard-view');
    const engagementView = document.getElementById('engagement-view');
    const toggleButton = event.target;

    if (engagementView.style.display === 'none') {
        // Show engagement view
        standardView.style.display = 'none';
        engagementView.style.display = 'block';
        toggleButton.innerHTML = '<i class="fas fa-table mr-2"></i>Show Standard View';
        toggleButton.classList.remove('btn-primary', 'from-purple-500', 'to-indigo-600', 'hover:from-purple-600', 'hover:to-indigo-700');
        toggleButton.classList.add('bg-gray-600', 'hover:bg-gray-700');
    } else {
        // Show standard view
        standardView.style.display = 'block';
        engagementView.style.display = 'none';
        toggleButton.innerHTML = '<i class="fas fa-chart-line mr-2"></i>Show Engagement Details';
        toggleButton.classList.remove('bg-gray-600', 'hover:bg-gray-700');
        toggleButton.classList.add('btn-primary', 'from-purple-500', 'to-indigo-600', 'hover:from-purple-600', 'hover:to-indigo-700');
    }
}

function showRecommendations(memberId) {
    const member = memberEngagementData.find(m => m.member_id == memberId);
    if (!member) return;

    document.getElementById('modalMemberName').textContent = member.name + ' - Recommendations';

    const modalContent = document.getElementById('modalContent');
    modalContent.innerHTML = '';

    if (member.recommendations && member.recommendations.length > 0) {
        member.recommendations.forEach(rec => {
            const recDiv = document.createElement('div');
            recDiv.className = `p-4 border-l-4 ${getPriorityColor(rec.priority)} bg-gradient-to-r from-gray-50 to-white rounded-lg shadow-sm`;
            recDiv.innerHTML = `
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <i class="${getPriorityIcon(rec.priority)} ${getPriorityIconColor(rec.priority)}"></i>
                        <h4 class="font-semibold text-gray-900">${rec.action}</h4>
                    </div>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${getPriorityBadge(rec.priority)}">${rec.priority}</span>
                </div>
                <p class="text-sm text-gray-700 mb-2 leading-relaxed">${rec.description}</p>
                <div class="flex items-center text-xs text-gray-500">
                    <i class="fas fa-tag mr-1"></i>
                    <span class="capitalize">${rec.type}</span>
                </div>
            `;
            modalContent.appendChild(recDiv);
        });
    } else {
        modalContent.innerHTML = `
            <div class="text-center py-8">
                <div class="bg-green-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-green-600 text-xl"></i>
                </div>
                <h4 class="text-lg font-medium text-gray-900 mb-2">All Good!</h4>
                <p class="text-gray-500">No specific recommendations needed at this time.</p>
            </div>
        `;
    }

    document.getElementById('recommendationsModal').style.display = 'block';
}

function closeRecommendations() {
    document.getElementById('recommendationsModal').style.display = 'none';
}

function getPriorityColor(priority) {
    switch(priority) {
        case 'High': return 'border-red-400';
        case 'Medium': return 'border-amber-400';
        case 'Low': return 'border-emerald-400';
        default: return 'border-gray-400';
    }
}

function getPriorityBadge(priority) {
    switch(priority) {
        case 'High': return 'bg-red-100 text-red-800 border border-red-200';
        case 'Medium': return 'bg-amber-100 text-amber-800 border border-amber-200';
        case 'Low': return 'bg-emerald-100 text-emerald-800 border border-emerald-200';
        default: return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
}

function getPriorityIcon(priority) {
    switch(priority) {
        case 'High': return 'fas fa-exclamation-circle';
        case 'Medium': return 'fas fa-exclamation-triangle';
        case 'Low': return 'fas fa-info-circle';
        default: return 'fas fa-circle';
    }
}

function getPriorityIconColor(priority) {
    switch(priority) {
        case 'High': return 'text-red-600';
        case 'Medium': return 'text-amber-600';
        case 'Low': return 'text-emerald-600';
        default: return 'text-gray-600';
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('recommendationsModal');
    if (event.target === modal) {
        closeRecommendations();
    }
}

// Chart tooltip functions
function showTooltip(event, time, count) {
    const tooltip = document.getElementById('chartTooltip');
    const content = document.getElementById('tooltipContent');

    content.innerHTML = `
        <div class="font-semibold">${time}</div>
        <div class="text-blue-200">${count} attendee${count !== 1 ? 's' : ''}</div>
    `;

    const rect = event.target.getBoundingClientRect();
    const chartContainer = document.getElementById('attendanceChart').getBoundingClientRect();

    tooltip.style.left = (rect.left - chartContainer.left - 40) + 'px';
    tooltip.style.top = (rect.top - chartContainer.top - 60) + 'px';
    tooltip.style.opacity = '1';
}

function hideTooltip() {
    const tooltip = document.getElementById('chartTooltip');
    tooltip.style.opacity = '0';
}

// Add smooth animations to chart on load
document.addEventListener('DOMContentLoaded', function() {
    const chartLines = document.querySelectorAll('#attendanceChart path');
    const chartPoints = document.querySelectorAll('#attendanceChart circle');

    // Animate line drawing
    chartLines.forEach(line => {
        const length = line.getTotalLength();
        line.style.strokeDasharray = length;
        line.style.strokeDashoffset = length;
        line.style.animation = 'drawLine 2s ease-in-out forwards';
    });

    // Animate points appearing
    chartPoints.forEach((point, index) => {
        point.style.opacity = '0';
        point.style.transform = 'scale(0)';
        setTimeout(() => {
            point.style.transition = 'all 0.3s ease-out';
            point.style.opacity = '1';
            point.style.transform = 'scale(1)';
        }, 1000 + (index * 50));
    });

    // Initialize charts
    initializeGenderChart();
    initializeDepartmentChart();
});

// Gender Distribution Pie Chart
function initializeGenderChart() {
    const ctx = document.getElementById('genderChart');
    if (!ctx) return; // Exit if chart element doesn't exist

    const chartContext = ctx.getContext('2d');

    // Bright vibrant colors for gender chart
    const genderColors = {
        'male': '#FF6B6B',      // Bright Red
        'female': '#4ECDC4',    // Bright Teal
        'other': '#45B7D1',     // Bright Blue
        'unknown': '#96CEB4'    // Bright Green
    };

    // Prepare data
    const labels = [];
    const data = [];
    const colors = [];

    // Safety check for genderData
    if (genderData && typeof genderData === 'object') {
        for (const [gender, count] of Object.entries(genderData)) {
            if (count > 0) {
                labels.push(gender.charAt(0).toUpperCase() + gender.slice(1));
                data.push(count);
                colors.push(genderColors[gender] || '#FFA07A'); // Default bright salmon
            }
        }
    }

    // If no data, show a message
    if (data.length === 0) {
        ctx.parentElement.innerHTML = '<div class="text-center text-gray-500 py-8"><i class="fas fa-chart-pie text-4xl mb-4"></i><p>No gender data available</p></div>';
        return;
    }

    new Chart(chartContext, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderColor: colors.map(color => color + '40'),
                borderWidth: 2,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000,
                easing: 'easeOutQuart'
            },
            cutout: '60%'
        }
    });
}

// Department Distribution Bar Chart
function initializeDepartmentChart() {
    const ctx = document.getElementById('departmentChart');
    if (!ctx) return; // Exit if chart element doesn't exist

    const chartContext = ctx.getContext('2d');

    // Safety check for departmentData
    if (!departmentData || typeof departmentData !== 'object') {
        ctx.parentElement.innerHTML = '<div class="text-center text-gray-500 py-8"><i class="fas fa-chart-bar text-4xl mb-4"></i><p>No department data available</p></div>';
        return;
    }

    // Sort departments by count (descending)
    const sortedDepts = Object.entries(departmentData)
        .filter(([dept, count]) => count > 0)
        .sort(([,a], [,b]) => b - a);

    // If no data, show a message
    if (sortedDepts.length === 0) {
        ctx.parentElement.innerHTML = '<div class="text-center text-gray-500 py-8"><i class="fas fa-chart-bar text-4xl mb-4"></i><p>No department attendance data</p></div>';
        return;
    }

    const labels = sortedDepts.map(([dept]) => dept.length > 15 ? dept.substring(0, 15) + '...' : dept);
    const data = sortedDepts.map(([,count]) => count);

    // Bright vibrant colors matching the gender chart theme
    const colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
        '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
    ];

    new Chart(chartContext, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Members',
                data: data,
                backgroundColor: colors.slice(0, data.length).map(color => color + '80'),
                borderColor: colors.slice(0, data.length),
                borderWidth: 2,
                borderRadius: 6,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        title: function(context) {
                            // Show full department name in tooltip
                            const index = context[0].dataIndex;
                            return sortedDepts[index][0];
                        },
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed.y / total) * 100).toFixed(1);
                            return `Members: ${context.parsed.y} (${percentage}%)`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        color: '#6B7280'
                    },
                    grid: {
                        color: 'rgba(107, 114, 128, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#6B7280',
                        maxRotation: 45,
                        minRotation: 0
                    },
                    grid: {
                        display: false
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeOutQuart',
                delay: (context) => context.dataIndex * 100
            }
        }
    });
}

// SMS Functions
function sendSMSToMember(memberId, memberName, phoneNumber) {
    const message = prompt(`Send SMS to ${memberName} (${phoneNumber}):\n\nEnter your message:`,
        `Hello ${memberName}, we noticed you haven't been to church recently. We miss you and hope everything is well. You're always welcome back anytime. God bless!`);

    if (message && message.trim()) {
        // Here you would make an AJAX call to send SMS
        if (confirm(`Send SMS to ${memberName}?\n\nMessage: ${message}`)) {
            // Simulate SMS sending - replace with actual AJAX call
            alert(`SMS sent successfully to ${memberName}!`);

            // You can implement actual SMS sending here
            // fetch('<?php echo BASE_URL; ?>sms/send-individual', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify({ member_id: memberId, message: message })
            // });
        }
    }
}

function sendAppreciationSMS(memberId, memberName, phoneNumber) {
    const message = prompt(`Send appreciation SMS to ${memberName} (${phoneNumber}):\n\nEnter your message:`,
        `Hello ${memberName}, thank you for your consistent attendance and dedication to our church community. Your presence makes a difference! Keep up the great work. God bless you!`);

    if (message && message.trim()) {
        if (confirm(`Send appreciation SMS to ${memberName}?\n\nMessage: ${message}`)) {
            alert(`Appreciation SMS sent successfully to ${memberName}!`);
        }
    }
}

function sendBulkAppreciation() {
    const highPerformers = <?php echo isset($session_details['member_engagement']['summary']['high_performers']) ? json_encode($session_details['member_engagement']['summary']['high_performers']) : '[]'; ?>;

    if (highPerformers.length === 0) {
        alert('No high engagement members found.');
        return;
    }

    const membersWithPhones = highPerformers.filter(member => member.phone_number && member.phone_number.trim());

    if (membersWithPhones.length === 0) {
        alert('No high engagement members have valid phone numbers.');
        return;
    }

    const message = prompt(`Send appreciation SMS to ${membersWithPhones.length} high engagement members:\n\nEnter your message:`,
        `Thank you for your consistent attendance and dedication to our church community. Your presence makes a difference! Keep up the great work. God bless you!`);

    if (message && message.trim()) {
        if (confirm(`Send appreciation SMS to ${membersWithPhones.length} members?\n\nMessage: ${message}`)) {
            alert(`Appreciation SMS sent successfully to ${membersWithPhones.length} members!`);
            closeHighPerformersModal();
        }
    }
}
</script>

<style>
@keyframes drawLine {
    to {
        stroke-dashoffset: 0;
    }
}

#attendanceChart circle:hover {
    r: 6;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
}

#chartTooltip {
    transform: translateX(-50%);
}
</style>
