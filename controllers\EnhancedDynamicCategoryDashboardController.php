<?php
/**
 * Enhanced Dynamic Category Dashboard Controller
 * Provides richer features for both core and custom categories
 */

class EnhancedDynamicCategoryDashboardController {
    private $database;
    private $conn;

    public function __construct() {
        $this->database = new Database();
        $this->conn = $this->database->getConnection();
    }

    /**
     * Show enhanced dashboard with category-type specific features
     */
    public function showDashboard($categorySlug, $isCore = false) {
        try {
            // Get category information
            $category = $this->getCategoryBySlug($categorySlug);
            
            if (!$category) {
                $_SESSION['flash_message'] = "Category '{$categorySlug}' not found.";
                $_SESSION['flash_type'] = 'danger';
                redirect('finance');
                return;
            }

            // Set page variables
            $page_title = $category->label . ' Dashboard - ICGC Emmanuel Temple';
            $active_page = 'finance';

            // Get enhanced analytics based on category type
            $analytics = $this->getEnhancedAnalytics($category, $isCore);
            
            // Get category-specific features
            $features = $this->getCategoryFeatures($category);
            
            // Get trends data with advanced analysis
            $trends = $this->getAdvancedTrends($category);
            
            // Get recent transactions with enhanced details
            $recentTransactions = $this->getEnhancedTransactions($category, 15);
            
            // Get member tracking (if applicable)
            $memberTracking = [];
            if ($category->requires_member) {
                $memberTracking = $this->getEnhancedMemberTracking($category);
            }

            // Get insights and recommendations
            $insights = $this->getDataInsights($category);

            // Start output buffering
            ob_start();

            // Load the enhanced dynamic category dashboard view
            require_once 'views/finances/dashboards/enhanced_dynamic_category.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("Error in EnhancedDynamicCategoryDashboardController::showDashboard: " . $e->getMessage());
            $_SESSION['flash_message'] = 'Error loading category dashboard: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finance');
        }
    }

    /**
     * Get category by slug
     */
    private function getCategoryBySlug($slug) {
        try {
            $query = "SELECT * FROM custom_finance_categories WHERE slug = ? AND is_active = 1";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$slug]);
            
            return $stmt->fetch(PDO::FETCH_OBJ);
            
        } catch (Exception $e) {
            error_log("Error getting category by slug: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get enhanced analytics with category-type specific metrics
     */
    private function getEnhancedAnalytics($category, $isCore) {
        try {
            // Base analytics
            $totalQuery = "SELECT 
                COUNT(*) as total_count,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(AVG(amount), 0) as average_amount,
                MIN(amount) as min_amount,
                MAX(amount) as max_amount,
                MIN(transaction_date) as first_transaction,
                MAX(transaction_date) as last_transaction
                FROM finances 
                WHERE category = ?";
            
            $stmt = $this->conn->prepare($totalQuery);
            $stmt->execute([$category->name]);
            $totals = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Current year data
            $currentYearQuery = "SELECT 
                COUNT(*) as current_year_count,
                COALESCE(SUM(amount), 0) as current_year_amount
                FROM finances 
                WHERE category = ? 
                AND YEAR(transaction_date) = YEAR(CURRENT_DATE())";
            
            $stmt = $this->conn->prepare($currentYearQuery);
            $stmt->execute([$category->name]);
            $currentYear = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Current month data
            $currentMonthQuery = "SELECT 
                COUNT(*) as current_month_count,
                COALESCE(SUM(amount), 0) as current_month_amount
                FROM finances 
                WHERE category = ? 
                AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
                AND YEAR(transaction_date) = YEAR(CURRENT_DATE())";
            
            $stmt = $this->conn->prepare($currentMonthQuery);
            $stmt->execute([$category->name]);
            $currentMonth = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Last month for growth calculation
            $lastMonthQuery = "SELECT 
                COALESCE(SUM(amount), 0) as last_month_amount
                FROM finances 
                WHERE category = ? 
                AND MONTH(transaction_date) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH)
                AND YEAR(transaction_date) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)";
            
            $stmt = $this->conn->prepare($lastMonthQuery);
            $stmt->execute([$category->name]);
            $lastMonth = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Calculate growth percentage
            $growthPercentage = 0;
            if ($lastMonth['last_month_amount'] > 0) {
                $growthPercentage = (($currentMonth['current_month_amount'] - $lastMonth['last_month_amount']) / $lastMonth['last_month_amount']) * 100;
            }
            
            // Payment method breakdown
            $paymentMethodQuery = "SELECT 
                payment_method,
                COUNT(*) as count,
                COALESCE(SUM(amount), 0) as amount
                FROM finances 
                WHERE category = ? 
                GROUP BY payment_method";
            
            $stmt = $this->conn->prepare($paymentMethodQuery);
            $stmt->execute([$category->name]);
            $paymentMethods = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Process payment methods into expected format
            $analytics = array_merge($totals, $currentYear, $currentMonth, [
                'growth_percentage' => round($growthPercentage, 1),
                'cash_amount' => 0,
                'bank_amount' => 0,
                'mobile_amount' => 0,
                'cheque_amount' => 0
            ]);
            
            foreach ($paymentMethods as $method) {
                switch ($method['payment_method']) {
                    case 'cash':
                        $analytics['cash_amount'] = $method['amount'];
                        break;
                    case 'bank_transfer':
                        $analytics['bank_amount'] = $method['amount'];
                        break;
                    case 'mobile_money':
                        $analytics['mobile_amount'] = $method['amount'];
                        break;
                    case 'cheque':
                        $analytics['cheque_amount'] = $method['amount'];
                        break;
                }
            }
            
            // Add enhanced metrics for core categories
            if ($isCore) {
                $analytics['enhanced'] = $this->getAdvancedMetrics($category);
            }
            
            return $analytics;
            
        } catch (Exception $e) {
            error_log("Error getting enhanced analytics: " . $e->getMessage());
            return $this->getDefaultAnalytics();
        }
    }

    /**
     * Get advanced metrics for core categories
     */
    private function getAdvancedMetrics($category) {
        // Quarterly breakdown
        $quarterlyQuery = "SELECT 
            CONCAT(YEAR(transaction_date), '-Q', QUARTER(transaction_date)) as quarter,
            COUNT(*) as count,
            SUM(amount) as amount
            FROM finances 
            WHERE category = ?
            AND transaction_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 2 YEAR)
            GROUP BY YEAR(transaction_date), QUARTER(transaction_date)
            ORDER BY quarter ASC";
        
        $stmt = $this->conn->prepare($quarterlyQuery);
        $stmt->execute([$category->name]);
        $quarterly = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Day of week analysis
        $dayOfWeekQuery = "SELECT 
            DAYNAME(transaction_date) as day_name,
            COUNT(*) as count,
            AVG(amount) as avg_amount
            FROM finances 
            WHERE category = ?
            GROUP BY DAYOFWEEK(transaction_date), DAYNAME(transaction_date)
            ORDER BY DAYOFWEEK(transaction_date)";
        
        $stmt = $this->conn->prepare($dayOfWeekQuery);
        $stmt->execute([$category->name]);
        $dayOfWeek = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'quarterly_breakdown' => $quarterly,
            'day_of_week_analysis' => $dayOfWeek
        ];
    }

    /**
     * Get category-specific features
     */
    private function getCategoryFeatures($category) {
        $features = [];
        
        // Base features for all categories
        $features['export'] = true;
        $features['analytics'] = true;
        
        // Category-type specific features
        switch ($category->category_type) {
            case 'member_payments':
                $features['member_tracking'] = true;
                $features['payment_reminders'] = true;
                $features['member_reports'] = true;
                break;
                
            case 'general_income':
                $features['fundraising_goals'] = true;
                $features['donor_tracking'] = true;
                $features['campaign_analysis'] = true;
                break;
                
            case 'expenses':
                $features['budget_tracking'] = true;
                $features['expense_approval'] = true;
                $features['vendor_management'] = true;
                break;
        }
        
        // Core category additional features
        if (isset($category->is_core) && $category->is_core) {
            $features['advanced_analytics'] = true;
            $features['predictive_insights'] = true;
            $features['automated_reports'] = true;
        }
        
        return $features;
    }

    /**
     * Get advanced trends with predictions
     */
    private function getAdvancedTrends($category) {
        try {
            $query = "SELECT 
                DATE_FORMAT(transaction_date, '%Y-%m') as month,
                SUM(amount) as amount,
                COUNT(*) as count,
                AVG(amount) as avg_amount
                FROM finances 
                WHERE category = ? 
                AND transaction_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 18 MONTH)
                GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
                ORDER BY month ASC";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$category->name]);
            $trends = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Add trend analysis
            if (count($trends) >= 3) {
                $recent = array_slice($trends, -3);
                $avgGrowth = 0;
                
                for ($i = 1; $i < count($recent); $i++) {
                    if ($recent[$i-1]['amount'] > 0) {
                        $growth = (($recent[$i]['amount'] - $recent[$i-1]['amount']) / $recent[$i-1]['amount']) * 100;
                        $avgGrowth += $growth;
                    }
                }
                
                $avgGrowth = $avgGrowth / (count($recent) - 1);
                
                // Simple prediction for next month
                $lastAmount = end($trends)['amount'];
                $prediction = $lastAmount * (1 + ($avgGrowth / 100));
                
                return [
                    'historical' => $trends,
                    'avg_growth_rate' => round($avgGrowth, 2),
                    'next_month_prediction' => round($prediction, 2)
                ];
            }
            
            return ['historical' => $trends];
            
        } catch (Exception $e) {
            error_log("Error getting advanced trends: " . $e->getMessage());
            return ['historical' => []];
        }
    }

    /**
     * Get enhanced transaction details
     */
    private function getEnhancedTransactions($category, $limit = 15) {
        try {
            $query = "SELECT 
                f.*,
                CONCAT(m.first_name, ' ', m.last_name) as member_name,
                m.phone as member_phone,
                DATEDIFF(CURRENT_DATE(), f.transaction_date) as days_ago
                FROM finances f
                LEFT JOIN members m ON f.member_id = m.id
                WHERE f.category = ? 
                ORDER BY f.transaction_date DESC, f.created_at DESC
                LIMIT ?";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$category->name, $limit]);
            
            return $stmt->fetchAll(PDO::FETCH_OBJ);
            
        } catch (Exception $e) {
            error_log("Error getting enhanced transactions: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get enhanced member tracking
     */
    private function getEnhancedMemberTracking($category) {
        try {
            $query = "SELECT 
                f.member_id,
                m.first_name,
                m.last_name,
                m.phone,
                COUNT(*) as payment_count,
                SUM(f.amount) as total_amount,
                AVG(f.amount) as avg_amount,
                MAX(f.transaction_date) as last_payment_date,
                MIN(f.transaction_date) as first_payment_date,
                DATEDIFF(CURRENT_DATE(), MAX(f.transaction_date)) as days_since_last
                FROM finances f
                LEFT JOIN members m ON f.member_id = m.id
                WHERE f.category = ? 
                GROUP BY f.member_id, m.first_name, m.last_name, m.phone
                ORDER BY total_amount DESC
                LIMIT 20";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$category->name]);
            
            return $stmt->fetchAll(PDO::FETCH_OBJ);
            
        } catch (Exception $e) {
            error_log("Error getting enhanced member tracking: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get data insights and recommendations
     */
    private function getDataInsights($category) {
        $insights = [];
        
        try {
            // Get basic analytics for insights
            $analytics = $this->getEnhancedAnalytics($category, false);
            
            // Transaction frequency insight
            if ($analytics['total_count'] > 0) {
                $daysSinceFirst = (strtotime($analytics['last_transaction']) - strtotime($analytics['first_transaction'])) / (60 * 60 * 24);
                $avgFrequency = $daysSinceFirst > 0 ? $analytics['total_count'] / ($daysSinceFirst / 30) : 0;
                
                $insights[] = [
                    'type' => 'frequency',
                    'title' => 'Transaction Frequency',
                    'message' => "Average of " . round($avgFrequency, 1) . " transactions per month",
                    'icon' => 'fas fa-calendar-alt',
                    'color' => 'blue'
                ];
            }
            
            // Amount range insight
            if ($analytics['max_amount'] > 0) {
                $insights[] = [
                    'type' => 'range',
                    'title' => 'Amount Range',
                    'message' => "Transactions range from GH₵ " . number_format($analytics['min_amount'], 2) . " to GH₵ " . number_format($analytics['max_amount'], 2),
                    'icon' => 'fas fa-chart-bar',
                    'color' => 'green'
                ];
            }
            
            // Growth insight
            if ($analytics['growth_percentage'] != 0) {
                $isPositive = $analytics['growth_percentage'] > 0;
                $insights[] = [
                    'type' => 'growth',
                    'title' => 'Monthly Growth',
                    'message' => ($isPositive ? "Increased" : "Decreased") . " by " . abs($analytics['growth_percentage']) . "% from last month",
                    'icon' => $isPositive ? 'fas fa-arrow-up' : 'fas fa-arrow-down',
                    'color' => $isPositive ? 'green' : 'red'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Error getting data insights: " . $e->getMessage());
        }
        
        return $insights;
    }

    /**
     * Get default analytics structure
     */
    private function getDefaultAnalytics() {
        return [
            'total_count' => 0,
            'total_amount' => 0,
            'average_amount' => 0,
            'current_year_count' => 0,
            'current_year_amount' => 0,
            'current_month_count' => 0,
            'current_month_amount' => 0,
            'growth_percentage' => 0,
            'cash_amount' => 0,
            'bank_amount' => 0,
            'mobile_amount' => 0,
            'cheque_amount' => 0
        ];
    }
}
