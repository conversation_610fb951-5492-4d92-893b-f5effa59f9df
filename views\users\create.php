<div class="container mx-auto fade-in max-w-3xl px-4">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border-2 border-gray-100">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white">
            <h1 class="text-3xl font-bold">Add New User</h1>
            <p class="mt-2 opacity-90">Create a new system user account</p>
        </div>
        <div class="p-6 bg-white">
            <div class="flex justify-end">
                <a href="<?php echo BASE_URL; ?>users" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg flex items-center transition-all duration-300 transform hover:scale-105 shadow-sm">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Users
                </a>
            </div>
        </div>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-r-lg shadow-sm" role="alert">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i>
                </div>
                <div>
                    <p class="font-bold">Please fix the following errors:</p>
                    <ul class="list-disc ml-5 mt-2">
                        <?php foreach ($_SESSION['errors'] as $error) : ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- User Form -->
    <div class="bg-white rounded-2xl shadow-lg overflow-hidden card-form border-2 border-gray-100 relative">
        <!-- Decorative elements -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-primary bg-opacity-5 rounded-full -mr-10 -mt-10"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-secondary bg-opacity-10 rounded-full -ml-8 -mb-8"></div>
        <div class="absolute top-20 left-0 w-16 h-16 bg-blue-500 bg-opacity-5 rounded-full -ml-8"></div>
        <div class="absolute bottom-20 right-0 w-16 h-16 bg-purple-500 bg-opacity-5 rounded-full -mr-8"></div>
        <div class="absolute top-1/2 right-0 w-3 h-3 bg-primary rounded-full -mr-1.5 animate-ping"></div>
        <div class="absolute top-1/4 left-0 w-3 h-3 bg-secondary rounded-full -ml-1.5 animate-ping" style="animation-delay: 1s"></div>
        <div class="card-header relative z-10 bg-gradient-to-r from-primary-light to-secondary-light bg-opacity-20 p-4">
            <h2 class="card-title flex items-center text-primary text-lg">
                <span class="bg-white p-2 rounded-full shadow-sm mr-3">
                    <i class="fas fa-user-plus text-primary"></i>
                </span>
                User Information
            </h2>
        </div>
        <div class="card-body p-8 relative z-10">
            <form action="<?php echo BASE_URL; ?>users/store" method="POST">
                <div class="grid grid-cols-1 gap-6">
                    <!-- Username -->
                    <div class="form-group mb-2">
                        <label for="username" class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                            <span class="bg-primary bg-opacity-10 text-primary p-2 rounded-full mr-2 shadow-sm">
                                <i class="fas fa-user"></i>
                            </span>
                            Username <span class="text-red-500 ml-1">*</span>
                        </label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 w-10 flex items-center justify-center pointer-events-none bg-gray-50 border-r-2 border-gray-400 rounded-l-lg transition-colors group-hover:bg-primary group-hover:bg-opacity-10">
                                <i class="fas fa-user text-primary"></i>
                            </div>
                            <input type="text" id="username" name="username" value="<?php echo isset($_SESSION['form_data']['username']) ? $_SESSION['form_data']['username'] : ''; ?>"
                                class="w-full rounded-lg border-2 border-gray-400 pl-12 py-3 bg-gray-50 shadow-sm
                                hover:border-primary hover:bg-white focus:bg-white focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-30
                                transition-all duration-200"
                                required>
                        </div>
                        <p class="text-xs text-gray-500 mt-1 ml-1">Choose a unique username for this account</p>
                    </div>

                    <!-- Email -->
                    <div class="form-group mb-2">
                        <label for="email" class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                            <span class="bg-blue-100 text-blue-600 p-2 rounded-full mr-2 shadow-sm">
                                <i class="fas fa-envelope"></i>
                            </span>
                            Email <span class="text-red-500 ml-1">*</span>
                        </label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 w-10 flex items-center justify-center pointer-events-none bg-gray-50 border-r-2 border-gray-400 rounded-l-lg transition-colors group-hover:bg-blue-100">
                                <i class="fas fa-envelope text-blue-600"></i>
                            </div>
                            <input type="email" id="email" name="email" value="<?php echo isset($_SESSION['form_data']['email']) ? $_SESSION['form_data']['email'] : ''; ?>"
                                class="w-full rounded-lg border-2 border-gray-400 pl-12 py-3 bg-gray-50 shadow-sm
                                hover:border-blue-400 hover:bg-white focus:bg-white focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-30
                                transition-all duration-200"
                                required>
                        </div>
                        <p class="text-xs text-gray-500 mt-1 ml-1">Enter a valid email address for account recovery</p>
                    </div>

                    <!-- Password -->
                    <div class="form-group mb-2">
                        <label for="password" class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                            <span class="bg-purple-100 text-purple-600 p-2 rounded-full mr-2 shadow-sm">
                                <i class="fas fa-lock"></i>
                            </span>
                            Password <span class="text-red-500 ml-1">*</span>
                        </label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 w-10 flex items-center justify-center pointer-events-none bg-gray-50 border-r-2 border-gray-400 rounded-l-lg transition-colors group-hover:bg-purple-100">
                                <i class="fas fa-lock text-purple-600"></i>
                            </div>
                            <input type="password" id="password" name="password"
                                class="w-full rounded-lg border-2 border-gray-400 pl-12 py-3 bg-gray-50 shadow-sm
                                hover:border-purple-400 hover:bg-white focus:bg-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-30
                                transition-all duration-200"
                                required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" id="togglePassword" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="text-xs text-gray-500 mb-1">Password strength:</div>
                            <div class="flex space-x-1">
                                <div id="strength-1" class="h-1 w-1/4 bg-gray-200 rounded-full"></div>
                                <div id="strength-2" class="h-1 w-1/4 bg-gray-200 rounded-full"></div>
                                <div id="strength-3" class="h-1 w-1/4 bg-gray-200 rounded-full"></div>
                                <div id="strength-4" class="h-1 w-1/4 bg-gray-200 rounded-full"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1 ml-1">Password must be at least 6 characters</p>
                        </div>
                    </div>

                    <!-- Confirm Password -->
                    <div class="form-group mb-2">
                        <label for="confirm_password" class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                            <span class="bg-purple-100 text-purple-600 p-2 rounded-full mr-2 shadow-sm">
                                <i class="fas fa-check-double"></i>
                            </span>
                            Confirm Password <span class="text-red-500 ml-1">*</span>
                        </label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 w-10 flex items-center justify-center pointer-events-none bg-gray-50 border-r-2 border-gray-400 rounded-l-lg transition-colors group-hover:bg-purple-100">
                                <i class="fas fa-check-double text-purple-600"></i>
                            </div>
                            <input type="password" id="confirm_password" name="confirm_password"
                                class="w-full rounded-lg border-2 border-gray-400 pl-12 py-3 bg-gray-50 shadow-sm
                                hover:border-purple-400 hover:bg-white focus:bg-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:ring-opacity-30
                                transition-all duration-200"
                                required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" id="toggleConfirmPassword" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1 ml-1">Re-enter your password to confirm</p>
                    </div>

                    <!-- Role -->
                    <div class="form-group mb-2">
                        <label for="role" class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                            <span class="bg-green-100 text-green-600 p-2 rounded-full mr-2 shadow-sm">
                                <i class="fas fa-user-tag"></i>
                            </span>
                            User Role
                        </label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 w-10 flex items-center justify-center pointer-events-none bg-gray-50 border-r-2 border-gray-400 rounded-l-lg transition-colors group-hover:bg-green-100">
                                <i class="fas fa-user-tag text-green-600"></i>
                            </div>
                            <select id="role" name="role"
                                class="w-full rounded-lg border-2 border-gray-400 pl-12 py-3 bg-gray-50 shadow-sm appearance-none
                                hover:border-green-400 hover:bg-white focus:bg-white focus:border-green-400 focus:ring-2 focus:ring-green-400 focus:ring-opacity-30
                                transition-all duration-200">
                                <option value="staff" <?php echo (isset($_SESSION['form_data']['role']) && $_SESSION['form_data']['role'] == 'staff') ? 'selected' : ''; ?> data-icon="fas fa-user">Staff</option>
                                <option value="admin" <?php echo (isset($_SESSION['form_data']['role']) && $_SESSION['form_data']['role'] == 'admin') ? 'selected' : ''; ?> data-icon="fas fa-user-cog">Admin</option>
                                <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'super_admin') : ?>
                                    <option value="super_admin" <?php echo (isset($_SESSION['form_data']['role']) && $_SESSION['form_data']['role'] == 'super_admin') ? 'selected' : ''; ?> data-icon="fas fa-user-shield">Super Admin</option>
                                <?php endif; ?>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1 ml-1">Select the appropriate access level for this user</p>
                    </div>
                </div>

                <div class="mt-10 flex justify-center">
                    <button type="reset" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-full flex items-center transition-all duration-300 mr-4 shadow-sm hover:shadow transform hover:-translate-y-1">
                        <i class="fas fa-undo mr-2"></i> Reset Form
                    </button>
                    <button type="submit" class="bg-gradient-to-r from-primary to-green-500 hover:from-primary-dark hover:to-green-600 text-white py-3 px-8 rounded-full flex items-center transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                        <i class="fas fa-save mr-2"></i> Save User
                    </button>
                </div>
                <div class="text-center mt-4 text-xs text-gray-500">
                    <p>All fields marked with <span class="text-red-500 font-bold">*</span> are required</p>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Clear form data
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>

<!-- Add fade-in animation CSS -->
<style>
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Form field focus effect */
    .form-group input:focus, .form-group select:focus {
        border-color: var(--primary);
        border-width: 2px;
        box-shadow: 0 0 0 2px rgba(63, 125, 88, 0.2);
        background-color: white;
    }

    /* Required field indicator */
    .text-red-500 {
        color: #ef4444;
        font-weight: bold;
    }

    /* Form group hover effect */
    .form-group:hover label {
        color: var(--primary);
        transition: all 0.3s ease;
    }

    /* Cute form styling */
    .card-form {
        transition: all 0.3s ease;
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%233f7d58' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
    }

    .card-form:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }

    /* Input field transitions */
    .form-group input, .form-group select {
        transition: all 0.3s ease;
        border-width: 2px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    /* Form field animation */
    .form-group input:focus, .form-group select:focus {
        transform: translateY(-2px);
    }

    /* Cute label styling */
    .form-group label span {
        transition: all 0.3s ease;
    }

    .form-group:hover label span {
        transform: rotate(5deg) scale(1.1);
    }

    /* Animation for decorative elements */
    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0px); }
    }

    .card-form .absolute:nth-child(2) {
        animation: float 6s ease-in-out infinite;
    }

    .card-form .absolute:nth-child(3) {
        animation: float 8s ease-in-out infinite;
        animation-delay: 1s;
    }

    .card-form .absolute:nth-child(4) {
        animation: float 7s ease-in-out infinite;
        animation-delay: 2s;
    }

    .card-form .absolute:nth-child(5) {
        animation: float 9s ease-in-out infinite;
        animation-delay: 3s;
    }

    /* Custom select styling */
    select {
        background-image: none !important;
    }

    /* Password strength indicators */
    #strength-1.weak, #strength-2.medium, #strength-3.strong, #strength-4.very-strong {
        background-color: #ef4444;
    }

    #strength-2.medium, #strength-3.strong, #strength-4.very-strong {
        background-color: #f59e0b;
    }

    #strength-3.strong, #strength-4.very-strong {
        background-color: #10b981;
    }

    #strength-4.very-strong {
        background-color: #3b82f6;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle password visibility
        const togglePassword = document.getElementById('togglePassword');
        const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
        const passwordField = document.getElementById('password');
        const confirmPasswordField = document.getElementById('confirm_password');

        togglePassword.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
            this.querySelector('i').classList.toggle('fa-eye');
            this.querySelector('i').classList.toggle('fa-eye-slash');
        });

        toggleConfirmPassword.addEventListener('click', function() {
            const type = confirmPasswordField.getAttribute('type') === 'password' ? 'text' : 'password';
            confirmPasswordField.setAttribute('type', type);
            this.querySelector('i').classList.toggle('fa-eye');
            this.querySelector('i').classList.toggle('fa-eye-slash');
        });

        // Password strength meter
        const strengthBars = [
            document.getElementById('strength-1'),
            document.getElementById('strength-2'),
            document.getElementById('strength-3'),
            document.getElementById('strength-4')
        ];

        passwordField.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;

            // Reset all bars
            strengthBars.forEach(bar => {
                bar.className = 'h-1 w-1/4 bg-gray-200 rounded-full';
            });

            if (password.length >= 6) {
                strength++;
            }

            if (password.length >= 8) {
                strength++;
            }

            if (/[A-Z]/.test(password) && /[a-z]/.test(password)) {
                strength++;
            }

            if (/[0-9]/.test(password) || /[^A-Za-z0-9]/.test(password)) {
                strength++;
            }

            // Update strength bars
            for (let i = 0; i < strength; i++) {
                if (strength === 1) {
                    strengthBars[i].classList.add('weak');
                } else if (strength === 2) {
                    strengthBars[i].classList.add('medium');
                } else if (strength === 3) {
                    strengthBars[i].classList.add('strong');
                } else if (strength === 4) {
                    strengthBars[i].classList.add('very-strong');
                }
            }
        });

        // Check password match
        confirmPasswordField.addEventListener('input', function() {
            if (this.value === passwordField.value) {
                this.classList.add('border-green-500');
                this.classList.remove('border-red-500');
            } else {
                this.classList.add('border-red-500');
                this.classList.remove('border-green-500');
            }
        });
    });
</script>
