<?php
/**
 * Attendance API
 *
 * This file handles API requests for attendance data
 */

// Include necessary files
require_once '../config/database.php';
require_once '../models/Attendance.php';
require_once '../models/Member.php';
require_once '../models/Service.php';
require_once '../utils/helpers.php';
require_once '../utils/api_auth.php';

// Set headers for JSON response
header('Content-Type: application/json');

// CRITICAL SECURITY FIX: Restrict CORS to application domain only
// The wildcard '*' is dangerous and allows any website to access this API
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false || 
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
    // If origin is not allowed, we don't set the CORS header
    // This will cause the browser to block the request
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, PUT');
header('Access-Control-Allow-Headers: Content-Type, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Authenticate API request - requires 'attendance' permission
$auth = authenticate_api_request('attendance');
if (!$auth['authenticated']) {
    send_error_response($auth['error'], 401);
}

// Initialize database and models
$database = new Database();
$attendance = new Attendance($database->getConnection());
$member = new Member($database->getConnection());
$service = new Service($database->getConnection());

// Single tenant - no tenant context needed

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle different HTTP methods
switch ($method) {
    case 'GET':
        // Get attendance data
        handleGetRequest();
        break;
    case 'POST':
        // Create attendance record
        handlePostRequest();
        break;
    case 'PUT':
        // Update attendance record
        handlePutRequest();
        break;
    default:
        send_error_response('Method not allowed', 405);
        break;
}

/**
 * Handle GET requests
 */
function handleGetRequest() {
    global $attendance, $service;
    
    // Get query parameters
    $service_id = isset($_GET['service_id']) ? (int)sanitize($_GET['service_id']) : null;
    $date = isset($_GET['date']) ? sanitize($_GET['date']) : null;
    $member_id = isset($_GET['member_id']) ? (int)sanitize($_GET['member_id']) : null;
    
    // Get pagination parameters
    $page = isset($_GET['page']) ? (int)sanitize($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? (int)sanitize($_GET['limit']) : 100;
    
    // Ensure valid pagination values
    $page = max(1, $page);
    $limit = min(500, max(10, $limit));
    $offset = ($page - 1) * $limit;
    
    // Process request based on parameters
    if ($member_id) {
        // Get attendance for a specific member
        $result = $attendance->getByMember($member_id, $limit, $offset);
    } else if ($service_id && $date) {
        // Get attendance for a specific service and date
        $result = $attendance->getByServiceAndDate($service_id, $date);
    } else if ($service_id) {
        // Get attendance for a specific service
        $result = $attendance->getByService($service_id, $limit, $offset);
    } else if ($date) {
        // Get attendance for a specific date
        $result = $attendance->getByDate($date, $limit, $offset);
    } else {
        // Get all attendance records
        $result = $attendance->getAll($limit, $offset);
    }
    
    // Prepare response
    $data = [];
    $totalCount = 0;
    
    if ($result) {
        $data = $result['data'];
        $totalCount = $result['total'] ?? count($data);
    }
    
    $response = [
        'data' => $data,
        'total' => $totalCount,
        'page' => $page,
        'limit' => $limit,
        'pages' => ceil($totalCount / $limit)
    ];
    
    send_json_response($response);
}

/**
 * Handle POST requests
 */
function handlePostRequest() {
    global $attendance, $member;
    
    // Get JSON data
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Validate input
    if (!$data || !isset($data['service_id']) || !isset($data['date']) || !isset($data['members'])) {
        send_error_response('Invalid data. Required fields: service_id, date, members', 400);
    }
    
    $service_id = (int)sanitize($data['service_id']);
    $date = sanitize($data['date']);
    $members = $data['members'];
    
    // Validate members array
    if (!is_array($members) || empty($members)) {
        send_error_response('Members must be a non-empty array', 400);
    }
    
    // Process attendance for each member
    $results = [];
    foreach ($members as $member_data) {
        if (!isset($member_data['member_id'])) {
            continue;
        }
        
        $member_id = (int)sanitize($member_data['member_id']);
        $status = isset($member_data['status']) ? sanitize($member_data['status']) : 'present';
        $notes = isset($member_data['notes']) ? sanitize($member_data['notes']) : '';
        
        // Verify member exists
        if (!$member->getById($member_id)) {
            $results[] = [
                'member_id' => $member_id,
                'success' => false,
                'message' => 'Member not found'
            ];
            continue;
        }
        
        // Mark attendance
        $attendance->member_id = $member_id;
        $attendance->service_id = $service_id;
        $attendance->attendance_date = $date;
        $attendance->status = $status;
        $attendance->notes = $notes;
        
        if ($attendance->create()) {
            $results[] = [
                'member_id' => $member_id,
                'success' => true
            ];
        } else {
            $results[] = [
                'member_id' => $member_id,
                'success' => false,
                'message' => 'Failed to mark attendance'
            ];
        }
    }
    
    send_json_response(['results' => $results], 201);
}

/**
 * Handle PUT requests
 */
function handlePutRequest() {
    global $attendance;
    
    // Get JSON data
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Validate input
    if (!$data || !isset($data['id'])) {
        send_error_response('Invalid data. Required field: id', 400);
    }
    
    // Set attendance properties
    $attendance->id = (int)sanitize($data['id']);
    
    // First, check if the attendance record exists
    if (!$attendance->read()) {
        send_error_response('Attendance record not found', 404);
    }
    
    // Update properties if provided
    if (isset($data['status'])) {
        $attendance->status = sanitize($data['status']);
    }
    
    if (isset($data['notes'])) {
        $attendance->notes = sanitize($data['notes']);
    }
    
    // Update attendance record
    if ($attendance->update()) {
        send_json_response(['message' => 'Attendance updated successfully']);
    } else {
        send_error_response('Failed to update attendance', 500);
    }
}
