<?php
// Convert attendances to array format for easier use in the view
$attendances_array = [];
foreach ($attendances as $attendance) {
    $attendances_array[] = [
        'id' => $attendance->id,
        'service_id' => $attendance->service_id,
        'service_name' => $attendance->service_name ?? 'Unknown Service',
        'member_id' => $attendance->member_id,
        'member_name' => $attendance->member_name ?? null,
        'status' => $attendance->status,
        'attendance_date' => $attendance->attendance_date,
        'created_at' => $attendance->created_at,
        'updated_at' => $attendance->updated_at
    ];
}

// Convert services to array format
$services_array = [];
foreach ($services as $service) {
    $services_array[] = [
        'id' => $service->id,
        'name' => $service->name,
        'day_of_week' => $service->day_of_week,
        'time' => $service->time
    ];
}
?>

<div class="container mx-auto px-4 max-w-6xl">
    <!-- Modern Header Section -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-5">
        <div class="bg-gradient-to-r from-primary to-primary-dark p-4 text-white">
            <div class="flex flex-row justify-between items-center">
                <div>
                    <h1 class="text-xl font-bold">Attendance for <?php echo format_date($date); ?></h1>
                    <p class="text-sm opacity-90 mt-1">View and manage attendance records for this date</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-1.5 px-3 rounded-md flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-arrow-left mr-1.5"></i> Back
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/bulk" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-1.5 px-3 rounded-md flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-plus-circle mr-1.5"></i> Mark Attendance
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/qr" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-1.5 px-3 rounded-md flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-qrcode mr-1.5"></i> QR Attendance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Summary - Modern Design -->
    <div class="bg-white rounded-lg shadow-sm mb-5">
        <div class="grid grid-cols-3 divide-x divide-gray-100">
            <!-- Present Members -->
            <div class="p-4 flex flex-col items-center justify-center text-center">
                <div class="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center mb-2">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
                <p class="text-xs text-gray-500 mb-1">Present</p>
                <h3 class="text-xl font-bold text-gray-800"><?php echo $present_count; ?></h3>
                <div class="mt-1 text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-700">
                    <?php echo ($present_count + $absent_count + $late_count) > 0 ? round($present_count / ($present_count + $absent_count + $late_count) * 100) : 0; ?>%
                </div>
            </div>

            <!-- Absent Members -->
            <div class="p-4 flex flex-col items-center justify-center text-center">
                <div class="w-10 h-10 rounded-full bg-red-500 flex items-center justify-center mb-2">
                    <i class="fas fa-times-circle text-white"></i>
                </div>
                <p class="text-xs text-gray-500 mb-1">Absent</p>
                <h3 class="text-xl font-bold text-gray-800"><?php echo $absent_count; ?></h3>
                <div class="mt-1 text-xs px-2 py-0.5 rounded-full bg-red-100 text-red-700">
                    <?php echo ($present_count + $absent_count + $late_count) > 0 ? round($absent_count / ($present_count + $absent_count + $late_count) * 100) : 0; ?>%
                </div>
            </div>

            <!-- Late Members -->
            <div class="p-4 flex flex-col items-center justify-center text-center">
                <div class="w-10 h-10 rounded-full bg-yellow-500 flex items-center justify-center mb-2">
                    <i class="fas fa-clock text-white"></i>
                </div>
                <p class="text-xs text-gray-500 mb-1">Late</p>
                <h3 class="text-xl font-bold text-gray-800"><?php echo $late_count; ?></h3>
                <div class="mt-1 text-xs px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-700">
                    <?php echo ($present_count + $absent_count + $late_count) > 0 ? round($late_count / ($present_count + $absent_count + $late_count) * 100) : 0; ?>%
                </div>
            </div>
        </div>
    </div>

    <!-- Service Filter - Modern Design -->
    <?php if (count($services_array) > 1) : ?>
    <div class="bg-white rounded-lg shadow-sm p-3 mb-5">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="p-1.5 rounded-full bg-blue-100 text-blue-600 mr-2">
                    <i class="fas fa-filter text-sm"></i>
                </div>
                <span class="text-sm font-medium text-gray-700">Filter by Service</span>
            </div>
            <div class="flex items-center space-x-2">
                <select id="service-filter" class="rounded-md border border-gray-300 text-sm py-1.5 px-2 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">All Services</option>
                    <?php foreach ($services_array as $service) : ?>
                        <option value="<?php echo $service['id']; ?>" <?php echo (isset($service_id) && $service_id == $service['id']) ? 'selected' : ''; ?>><?php echo $service['name']; ?></option>
                    <?php endforeach; ?>
                </select>
                <button id="apply-filter" class="bg-primary hover:bg-primary-dark text-white py-1.5 px-3 rounded-md text-xs flex items-center">
                    <i class="fas fa-filter mr-1.5"></i> Apply
                </button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Attendance Table - Modern Design -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="flex items-center justify-between p-4 border-b border-gray-100">
            <div class="flex items-center">
                <div class="p-1.5 rounded-full bg-indigo-100 text-indigo-600 mr-2">
                    <i class="fas fa-clipboard-list text-sm"></i>
                </div>
                <h3 class="text-base font-medium text-gray-800">Attendance Records</h3>
            </div>
            <div class="text-xs text-gray-500">
                <?php echo count($attendances_array); ?> records found
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-100">
                <thead>
                    <tr class="bg-gray-50">
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500">Service</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500">Member</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500">Status</th>
                        <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-100">
                    <?php if (empty($attendances_array)) : ?>
                        <tr>
                            <td colspan="4" class="px-4 py-8 text-center text-sm text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <div class="p-2 rounded-full bg-gray-100 text-gray-400 mb-2">
                                        <i class="fas fa-calendar-times"></i>
                                    </div>
                                    <p>No attendance records found for this date</p>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ($attendances_array as $attendance) : ?>
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700"><?php echo $attendance['service_name']; ?></td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700">
                                    <?php echo $attendance['member_name'] ? $attendance['member_name'] : '<span class="text-gray-500">General Attendance</span>'; ?>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <?php if ($attendance['status'] === 'present') : ?>
                                        <span class="px-2 py-0.5 inline-flex text-xs font-medium rounded-full bg-green-100 text-green-800">Present</span>
                                    <?php elseif ($attendance['status'] === 'absent') : ?>
                                        <span class="px-2 py-0.5 inline-flex text-xs font-medium rounded-full bg-red-100 text-red-800">Absent</span>
                                    <?php else : ?>
                                        <span class="px-2 py-0.5 inline-flex text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Late</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-right">
                                    <a href="<?php echo BASE_URL; ?>attendance/edit?id=<?php echo $attendance['id']; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3 p-1 rounded-full hover:bg-indigo-50 inline-flex items-center justify-center">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $attendance['id']; ?>)" class="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 inline-flex items-center justify-center">
                                        <i class="fas fa-trash-alt"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal - Modern Design -->
<div id="deleteModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 backdrop-blur-sm">
    <div class="relative top-20 mx-auto p-4 border border-gray-200 w-80 shadow-lg rounded-lg bg-white">
        <div class="absolute top-2 right-2">
            <button id="closeModal" class="text-gray-400 hover:text-gray-500 focus:outline-none p-1">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mt-2 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
            </div>
            <h3 class="text-base font-semibold text-gray-900 mt-3">Delete Attendance Record</h3>
            <div class="mt-2 px-4 py-2">
                <p class="text-xs text-gray-500">Are you sure you want to delete this attendance record? This action cannot be undone.</p>
            </div>
            <div class="flex justify-center mt-3 space-x-2">
                <button id="cancelDelete" class="bg-gray-100 hover:bg-gray-200 px-4 py-1.5 rounded text-gray-700 text-xs transition-colors duration-200">
                    <i class="fas fa-times mr-1"></i> Cancel
                </button>
                <a id="confirmDelete" href="#" class="bg-red-500 hover:bg-red-600 px-4 py-1.5 rounded text-white text-xs transition-colors duration-200">
                    <i class="fas fa-trash-alt mr-1"></i> Delete
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Service filter functionality
        const applyFilterBtn = document.getElementById('apply-filter');
        if (applyFilterBtn) {
            applyFilterBtn.addEventListener('click', function() {
                const serviceId = document.getElementById('service-filter').value;
                window.location.href = '<?php echo BASE_URL; ?>attendance/view-by-date?date=<?php echo $date; ?>' + (serviceId ? '&service_id=' + serviceId : '');
            });
        }

        // Delete confirmation
        function confirmDelete(id) {
            const modal = document.getElementById('deleteModal');
            const confirmBtn = document.getElementById('confirmDelete');

            modal.classList.remove('hidden');
            confirmBtn.href = '<?php echo BASE_URL; ?>attendance/delete?id=' + id + '&redirect=view-by-date?date=<?php echo $date; ?>';

            // Close modal when cancel button is clicked
            document.getElementById('cancelDelete').addEventListener('click', function() {
                modal.classList.add('hidden');
            });

            // Close modal when X button is clicked
            document.getElementById('closeModal').addEventListener('click', function() {
                modal.classList.add('hidden');
            });

            // Close modal when clicking outside the modal
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        }

        // Make confirmDelete function available globally
        window.confirmDelete = confirmDelete;
    });
</script>
