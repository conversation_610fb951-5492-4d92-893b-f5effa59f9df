<?php
try {
    $db = new PDO('mysql:host=localhost;dbname=icgc_db', 'root', '');
    
    // Sample services for ICGC Emmanuel Temple
    $services = [
        [
            'name' => 'Sunday Morning Service',
            'description' => 'Main Sunday worship service',
            'day_of_week' => 'sunday',
            'time' => '09:00:00'
        ],
        [
            'name' => 'Sunday Evening Service',
            'description' => 'Evening worship and prayer service',
            'day_of_week' => 'sunday',
            'time' => '17:00:00'
        ],
        [
            'name' => 'Wednesday Bible Study',
            'description' => 'Midweek Bible study and prayer meeting',
            'day_of_week' => 'wednesday',
            'time' => '18:30:00'
        ],
        [
            'name' => 'Friday Prayer Meeting',
            'description' => 'Intercessory prayer service',
            'day_of_week' => 'friday',
            'time' => '19:00:00'
        ],
        [
            'name' => 'Saturday Youth Service',
            'description' => 'Service for youth and young adults',
            'day_of_week' => 'saturday',
            'time' => '16:00:00'
        ]
    ];
    
    // Insert services
    $query = "INSERT INTO services (name, description, day_of_week, time, created_at, updated_at) 
              VALUES (:name, :description, :day_of_week, :time, NOW(), NOW())";
    $stmt = $db->prepare($query);
    
    $count = 0;
    foreach ($services as $service) {
        $stmt->bindParam(':name', $service['name']);
        $stmt->bindParam(':description', $service['description']);
        $stmt->bindParam(':day_of_week', $service['day_of_week']);
        $stmt->bindParam(':time', $service['time']);
        $stmt->execute();
        $count++;
    }
    
    echo "Successfully added $count services to the database.";
    
} catch (PDOException $e) {
    echo 'Error: ' . $e->getMessage();
}
?>
