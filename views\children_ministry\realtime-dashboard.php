<?php
// Get date and token from URL or use defaults
$display_date = isset($dashboard_date) ? $dashboard_date : date('Y-m-d');
$token = isset($_GET['token']) ? sanitize($_GET['token']) : null;

// Get QR session info if available
$qr_session = isset($qr_session_data) ? $qr_session_data : null;

// Determine if we have an active QR session
$has_active_qr = $qr_session && isset($qr_session['is_active']) && $qr_session['is_active'];
?>

<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Children's Ministry Attendance Dashboard</h1>
                    <p class="text-sm text-gray-600 mt-1">
                        <i class="fas fa-calendar mr-1"></i>
                        <?php echo format_date($display_date); ?>
                        <span class="mx-2">•</span>
                        <i class="fas fa-clock mr-1"></i>
                        <span id="current-time"><?php echo date('g:i A'); ?></span>
                        <?php if ($has_active_qr): ?>
                            <span class="mx-2">•</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-wifi mr-1"></i>Live QR Session
                            </span>
                        <?php elseif ($qr_session): ?>
                            <span class="mx-2">•</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-clock mr-1"></i>QR Session Expired
                            </span>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="flex space-x-3">
                    <button onclick="exportChildrenData()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm flex items-center">
                        <i class="fas fa-download mr-2"></i> Export
                    </button>
                    <a href="<?php echo BASE_URL; ?>children-ministry" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        
        <!-- Summary Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Children Card -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Children</p>
                        <p class="text-3xl font-bold text-blue-600" id="total-children-count">0</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <span id="present-percentage">0%</span> present today
                        </p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-child text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Present Children Card -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Present</p>
                        <p class="text-3xl font-bold text-green-600" id="present-count">0</p>
                        <p class="text-xs text-gray-500 mt-1">
                            On time arrivals
                        </p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-check text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Late Arrivals Card -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Late Arrivals</p>
                        <p class="text-3xl font-bold text-yellow-600" id="late-count">0</p>
                        <p class="text-xs text-gray-500 mt-1">
                            After service time
                        </p>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Gender Distribution Card -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Gender Split</p>
                        <div class="flex items-center space-x-4 mt-2">
                            <div class="text-center">
                                <p class="text-lg font-bold text-blue-600" id="male-count">0</p>
                                <p class="text-xs text-gray-500">Boys</p>
                            </div>
                            <div class="text-center">
                                <p class="text-lg font-bold text-pink-600" id="female-count">0</p>
                                <p class="text-xs text-gray-500">Girls</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-users text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Attendance Tracking -->
        <div class="bg-white rounded-xl shadow-md border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">Real-time Attendance Tracking</h3>
                    <div class="flex items-center space-x-4">
                        <div class="text-sm text-gray-600">
                            <span id="showing-count">0</span> of <span id="total-children">0</span> children
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-xs text-gray-500">Live</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter and Search -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <input type="text" id="search-children" placeholder="Search children..." 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <select id="filter-status" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Status</option>
                        <option value="present">Present</option>
                        <option value="late">Late</option>
                    </select>
                    <select id="filter-gender" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Gender</option>
                        <option value="male">Boys</option>
                        <option value="female">Girls</option>
                    </select>
                    <select id="filter-age-group" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Ages</option>
                        <option value="toddlers">Toddlers (0-3)</option>
                        <option value="preschool">Preschool (4-6)</option>
                        <option value="elementary">Elementary (7-12)</option>
                        <option value="teens">Teens (13+)</option>
                    </select>
                </div>
            </div>
            
            <!-- Loading State -->
            <div id="loading-state" class="p-8 text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading children's attendance data...</p>
            </div>

            <!-- Children List Container -->
            <div id="children-container" class="hidden">
                <div class="divide-y divide-gray-200 max-h-96 overflow-y-auto" id="children-list">
                    <!-- Children will be loaded here via JavaScript -->
                </div>
                
                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            Showing <span id="page-start">1</span> to <span id="page-end">10</span> of <span id="page-total">0</span> results
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="previousPage()" id="prev-btn" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50" disabled>
                                Previous
                            </button>
                            <button onclick="nextPage()" id="next-btn" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50" disabled>
                                Next
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div id="empty-state" class="hidden p-8 text-center">
                <i class="fas fa-child text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No children checked in yet</h3>
                <p class="text-gray-600">Children will appear here as they check in via QR codes</p>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Real-time Updates -->
<script>
let autoRefreshEnabled = true;
let refreshInterval;
let currentPage = 1;
let itemsPerPage = 20;
let allChildren = [];
let filteredChildren = [];
let refreshCountdown = 10;
const token = '<?php echo $token ?? ""; ?>';
const displayDate = '<?php echo $display_date; ?>';
const hasActiveQR = <?php echo $has_active_qr ? 'true' : 'false'; ?>;

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadChildrenAttendanceData();

    // Only start auto-refresh if we have an active QR session
    if (hasActiveQR) {
        startAutoRefresh();
    } else {
        // Show manual refresh option for expired/no QR sessions
        showManualRefreshOption();
    }

    updateClock();

    // Set up search and filter event listeners
    document.getElementById('search-children').addEventListener('input', filterChildren);
    document.getElementById('filter-status').addEventListener('change', filterChildren);
    document.getElementById('filter-gender').addEventListener('change', filterChildren);
    document.getElementById('filter-age-group').addEventListener('change', filterChildren);
});

// Update clock every second
function updateClock() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
    });
    document.getElementById('current-time').textContent = timeString;
    setTimeout(updateClock, 1000);
}

// Load children's attendance data
async function loadChildrenAttendanceData() {
    try {
        // Build URL with date and optional token
        let url = `<?php echo BASE_URL; ?>children-ministry/realtime-data?date=${displayDate}`;
        if (token) {
            url += `&token=${token}`;
        }

        const response = await fetch(url);
        const data = await response.json();

        if (data.success) {
            allChildren = data.children;
            updateStatistics(data.stats);
            filterChildren();

            if (allChildren.length > 0) {
                showChildrenContainer();
            } else {
                showEmptyState();
            }
        } else {
            showEmptyState();
        }
    } catch (error) {
        console.error('Error loading children attendance data:', error);
        showEmptyState();
    }
}

// Update statistics cards
function updateStatistics(stats) {
    document.getElementById('total-children-count').textContent = stats.total_children;
    document.getElementById('present-count').textContent = stats.present_count;
    document.getElementById('late-count').textContent = stats.late_count;
    document.getElementById('male-count').textContent = stats.male_count;
    document.getElementById('female-count').textContent = stats.female_count;
    
    const presentPercentage = stats.total_children > 0 ?
        Math.round((stats.present_count / stats.total_children) * 100) : 0;
    document.getElementById('present-percentage').textContent = presentPercentage + '%';
}

// Filter children based on search and filters
function filterChildren() {
    const searchTerm = document.getElementById('search-children').value.toLowerCase();
    const statusFilter = document.getElementById('filter-status').value;
    const genderFilter = document.getElementById('filter-gender').value;
    const ageGroupFilter = document.getElementById('filter-age-group').value;

    filteredChildren = allChildren.filter(child => {
        const matchesSearch = child.first_name.toLowerCase().includes(searchTerm) ||
                            child.last_name.toLowerCase().includes(searchTerm);
        const matchesStatus = !statusFilter || child.status === statusFilter;
        const matchesGender = !genderFilter || child.gender.toLowerCase() === genderFilter;

        let matchesAgeGroup = true;
        if (ageGroupFilter) {
            const age = parseInt(child.age);
            switch(ageGroupFilter) {
                case 'toddlers': matchesAgeGroup = age <= 3; break;
                case 'preschool': matchesAgeGroup = age >= 4 && age <= 6; break;
                case 'elementary': matchesAgeGroup = age >= 7 && age <= 12; break;
                case 'teens': matchesAgeGroup = age >= 13; break;
            }
        }

        return matchesSearch && matchesStatus && matchesGender && matchesAgeGroup;
    });

    currentPage = 1;
    displayChildren();
    updatePagination();
}

// Display children list
function displayChildren() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageChildren = filteredChildren.slice(startIndex, endIndex);

    const childrenList = document.getElementById('children-list');
    childrenList.innerHTML = '';

    pageChildren.forEach(child => {
        const childElement = createChildElement(child);
        childrenList.appendChild(childElement);
    });

    // Update counts
    document.getElementById('showing-count').textContent = filteredChildren.length;
    document.getElementById('total-children').textContent = allChildren.length;
}

// Create child element
function createChildElement(child) {
    const div = document.createElement('div');
    div.className = `flex items-center justify-between p-4 hover:bg-gray-50 ${child.status === 'late' ? 'bg-yellow-50' : ''}`;

    const statusBadge = child.status === 'late' ?
        '<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Late</span>' :
        '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Present</span>';

    const parentInfo = child.parent_first_name ?
        `<p class="text-xs text-gray-500">Parent: ${child.parent_first_name} ${child.parent_last_name}</p>` : '';

    div.innerHTML = `
        <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3">
                ${child.first_name.charAt(0)}${child.last_name.charAt(0)}
            </div>
            <div>
                <p class="font-medium text-gray-900">${child.first_name} ${child.last_name}</p>
                <p class="text-sm text-gray-600">Age ${child.age} • ${child.gender}</p>
                ${parentInfo}
            </div>
        </div>
        <div class="text-right">
            ${statusBadge}
            <p class="text-xs text-gray-500 mt-1">${formatTime(child.check_in_time)}</p>
            <p class="text-xs text-gray-400">${child.minutes_checked_in} min ago</p>
        </div>
    `;

    return div;
}

// Format time
function formatTime(timeString) {
    const date = new Date(timeString);
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// Pagination functions
function updatePagination() {
    const totalPages = Math.ceil(filteredChildren.length / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, filteredChildren.length);

    document.getElementById('page-start').textContent = filteredChildren.length > 0 ? startItem : 0;
    document.getElementById('page-end').textContent = endItem;
    document.getElementById('page-total').textContent = filteredChildren.length;

    document.getElementById('prev-btn').disabled = currentPage <= 1;
    document.getElementById('next-btn').disabled = currentPage >= totalPages;
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        displayChildren();
        updatePagination();
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredChildren.length / itemsPerPage);
    if (currentPage < totalPages) {
        currentPage++;
        displayChildren();
        updatePagination();
    }
}

// Show/hide states
function showChildrenContainer() {
    document.getElementById('loading-state').classList.add('hidden');
    document.getElementById('empty-state').classList.add('hidden');
    document.getElementById('children-container').classList.remove('hidden');
}

function showEmptyState() {
    document.getElementById('loading-state').classList.add('hidden');
    document.getElementById('children-container').classList.add('hidden');
    document.getElementById('empty-state').classList.remove('hidden');
}

// Auto refresh (only for active QR sessions)
function startAutoRefresh() {
    if (hasActiveQR) {
        refreshInterval = setInterval(() => {
            if (autoRefreshEnabled) {
                loadChildrenAttendanceData();
            }
        }, 10000); // Refresh every 10 seconds
    }
}

// Show manual refresh option for expired/no QR sessions
function showManualRefreshOption() {
    const headerDiv = document.querySelector('.container.max-w-7xl .flex.justify-between');
    if (headerDiv) {
        const refreshBtn = document.createElement('button');
        refreshBtn.onclick = loadChildrenAttendanceData;
        refreshBtn.className = 'bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center ml-3';
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i> Refresh';

        const exportBtn = headerDiv.querySelector('button[onclick="exportChildrenData()"]');
        if (exportBtn) {
            exportBtn.parentNode.insertBefore(refreshBtn, exportBtn);
        }
    }
}

// Export function
function exportChildrenData() {
    const csvContent = generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `children_attendance_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
}

function generateCSV() {
    const headers = ['Name', 'Age', 'Gender', 'Status', 'Check-in Time', 'Parent'];
    const rows = allChildren.map(child => [
        `${child.first_name} ${child.last_name}`,
        child.age,
        child.gender,
        child.status,
        child.check_in_time,
        child.parent_first_name ? `${child.parent_first_name} ${child.parent_last_name}` : ''
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}
</script>
