<?php
/**
 * Create Program View - Church Program & Activities Planner
 */

// Header configuration
$header_title = 'Create New Program';
$header_subtitle = 'Plan and organize a new church program or activity for your ministry.';
$header_icon = 'fas fa-plus-circle';
$header_width = 'w-[85%] mx-auto';

// Custom navigation for create page
$navigation_buttons = [
    'back' => [
        'url' => BASE_URL . 'programs',
        'text' => 'Back to Programs',
        'icon' => 'fas fa-arrow-left',
        'style' => 'bg-gray-100 hover:bg-gray-200 text-gray-700'
    ],
    'dashboard' => [
        'url' => BASE_URL . 'programs/dashboard',
        'text' => 'Dashboard',
        'icon' => 'fas fa-chart-pie',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ],
    'calendar' => [
        'url' => BASE_URL . 'programs/calendar',
        'text' => 'Calendar',
        'icon' => 'fas fa-calendar-alt',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ]
];

// Include shared header and form components
include 'components/header.php';
include 'components/form_elements.php';
?>

<div class="w-[85%] mx-auto">
    <!-- Create Form -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
        <div class="bg-gradient-to-r from-blue-50 to-emerald-50 p-4 border-b border-gray-100">
            <div class="flex items-center justify-center">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center mr-3">
                    <i class="fas fa-plus text-white text-lg"></i>
                </div>
                <div class="text-center">
                    <h2 class="text-xl font-bold text-gray-800">Create New Program</h2>
                    <p class="text-gray-600 text-xs">Fill in the details below ✨</p>
                </div>
            </div>
        </div>

        <div class="p-6">
            <form method="POST" action="<?php echo BASE_URL; ?>programs/store" class="space-y-6">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                <!-- Basic Information -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-info-circle text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">Basic Information</h3>
                            <p class="text-xs text-gray-500">Essential details about your program</p>
                        </div>
                    </div>

                    <?php render_form_field('text', 'title', 'Program Title', [
                        'required' => true,
                        'placeholder' => 'Enter the program title'
                    ]); ?>

                    <?php render_form_field('textarea', 'description', 'Description', [
                        'placeholder' => 'Describe the program, its purpose, and what participants can expect',
                        'rows' => 3
                    ]); ?>

                    <!-- Category and Department -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Category -->
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <label for="category_id" class="block text-sm font-medium text-gray-700">
                                    Category <span class="text-red-500">*</span>
                                </label>
                                <button type="button" onclick="openCategoryModal()"
                                        class="inline-flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-md transition-all duration-200">
                                    <i class="fas fa-cog mr-1"></i> Manage
                                </button>
                            </div>
                            <select id="category_id" name="category_id" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="">Select a category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>"
                                            data-color="<?php echo $category['color_code']; ?>"
                                            data-icon="<?php echo $category['icon']; ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Department -->
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <label for="department_id" class="block text-sm font-medium text-gray-700">
                                    Ministry Department <span class="text-red-500">*</span>
                                </label>
                                <button type="button" onclick="openDepartmentModal()"
                                        class="inline-flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-md transition-all duration-200">
                                    <i class="fas fa-cog mr-1"></i> Manage
                                </button>
                            </div>
                            <select id="department_id" name="department_id" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="">Select a department</option>
                                <?php foreach ($departments as $department): ?>
                                    <option value="<?php echo $department['id']; ?>">
                                        <?php echo htmlspecialchars($department['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                    <!-- Coordinator -->
                    <div class="mb-6">
                        <div class="relative">
                            <label for="coordinator_search" class="block text-sm font-medium text-gray-700 mb-2">Program Coordinator</label>
                            <div class="relative">
                                <input type="text"
                                       id="coordinator_search"
                                       placeholder="Search for a coordinator by name..."
                                       autocomplete="off"
                                       class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <!-- Hidden input to store the selected coordinator ID -->
                                <input type="hidden" id="coordinator_id" name="coordinator_id" value="">
                            </div>

                            <!-- Dropdown for search results -->
                            <div id="coordinator_dropdown" class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                                <div id="coordinator_results" class="py-1">
                                    <!-- Search results will be populated here -->
                                </div>
                            </div>

                            <!-- Selected coordinator display -->
                            <div id="selected_coordinator" class="hidden mt-2 p-3 bg-primary bg-opacity-10 border border-primary border-opacity-20 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-white text-sm"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900" id="selected_coordinator_name"></p>
                                            <p class="text-sm text-gray-600">Selected as Program Coordinator</p>
                                        </div>
                                    </div>
                                    <button type="button" onclick="clearCoordinatorSelection()" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Location -->
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                        <input type="text" id="location" name="location"
                               placeholder="Church sanctuary, fellowship hall, etc."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                    </div>
                </div>

                <!-- Schedule Information -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-clock text-emerald-600 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">Schedule</h3>
                            <p class="text-xs text-gray-500">Set the dates and times ⏰</p>
                        </div>
                    </div>

                    <!-- Dates -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">
                                Start Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" id="start_date" name="start_date" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>

                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">
                                End Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" id="end_date" name="end_date" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>
                    </div>

                    <!-- Times -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="start_time" class="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                            <input type="time" id="start_time" name="start_time"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>

                        <div>
                            <label for="end_time" class="block text-sm font-medium text-gray-700 mb-2">End Time</label>
                            <input type="time" id="end_time" name="end_time"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>
                    </div>

                    <!-- Recurring Options -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" id="is_recurring" name="is_recurring" value="1"
                                       class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                <label for="is_recurring" class="text-sm font-medium text-gray-700">This is a recurring program</label>
                            </div>
                        </div>

                        <div id="recurrence_group" style="display: none;">
                            <label for="recurrence_pattern" class="block text-sm font-medium text-gray-700 mb-2">Recurrence Pattern</label>
                            <select id="recurrence_pattern" name="recurrence_pattern"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="">Select pattern</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="quarterly">Quarterly</option>
                                <option value="annually">Annually</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Program Settings -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-cogs mr-2 text-primary"></i>
                            Program Settings
                        </h3>
                        <p class="text-gray-600 text-sm mt-1">Configure program status, priority, and budget</p>
                    </div>

                    <!-- Status and Priority -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select id="status" name="status"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="planned" selected>Planned</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                                <option value="postponed">Postponed</option>
                            </select>
                        </div>

                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                            <select id="priority" name="priority"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                    </div>

                    <!-- Budget and Attendance -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="budget_allocated" class="block text-sm font-medium text-gray-700 mb-2">Budget Allocated</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 text-sm">$</span>
                                </div>
                                <input type="number" id="budget_allocated" name="budget_allocated"
                                       step="0.01" min="0" placeholder="0.00"
                                       class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                            </div>
                        </div>

                        <div>
                            <label for="expected_attendance" class="block text-sm font-medium text-gray-700 mb-2">Expected Attendance</label>
                            <input type="number" id="expected_attendance" name="expected_attendance"
                                   min="0" placeholder="Number of expected participants"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>
                    </div>
                </div>

                <!-- Registration Settings -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-user-plus mr-2 text-primary"></i>
                            Registration
                        </h3>
                        <p class="text-gray-600 text-sm mt-1">Set up registration requirements and limits</p>
                    </div>

                    <!-- Registration Options -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" id="requires_registration" name="requires_registration" value="1"
                                       class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                <label for="requires_registration" class="text-sm font-medium text-gray-700">Requires registration</label>
                            </div>
                        </div>

                        <div id="max_participants_group" style="display: none;">
                            <label for="max_participants" class="block text-sm font-medium text-gray-700 mb-2">Maximum Participants</label>
                            <input type="number" id="max_participants" name="max_participants"
                                   min="1" placeholder="Leave blank for unlimited"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>
                    </div>
                </div>

                <!-- Invited Guests (Optional) -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <i class="fas fa-user-friends mr-2 text-primary"></i>
                                    Invited Guests
                                    <span class="ml-2 text-sm text-gray-500 font-normal">(Optional)</span>
                                </h3>
                                <p class="text-gray-600 text-sm mt-1">Add special guests, speakers, or ministry leaders if this program involves invited guests</p>
                            </div>
                            <button type="button" id="toggle_guests" onclick="toggleGuestsSection()"
                                    class="inline-flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors">
                                <i class="fas fa-plus mr-1" id="guests_toggle_icon"></i>
                                <span id="guests_toggle_text">Add Guests</span>
                            </button>
                        </div>
                    </div>

                    <!-- Guest List Container -->
                    <div id="guests_section" class="hidden">
                        <div id="guests_container" class="space-y-4">
                        <!-- Initial guest entry -->
                        <div class="guest-entry bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-medium text-gray-800 flex items-center">
                                    <i class="fas fa-user mr-2 text-primary"></i>
                                    Guest #1
                                </h4>
                                <button type="button" onclick="removeGuest(this)" class="text-red-500 hover:text-red-700 transition-colors duration-200" style="display: none;">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Guest Name</label>
                                    <input type="text" name="guests[0][name]"
                                           placeholder="Enter guest's full name"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Role/Title</label>
                                    <input type="text" name="guests[0][role]"
                                           placeholder="e.g., Guest Speaker, Worship Leader, Pastor"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                                    <input type="email" name="guests[0][email]"
                                           placeholder="<EMAIL>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                                    <input type="tel" name="guests[0][phone]"
                                           placeholder="(*************"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                </div>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Organization/Church</label>
                                <input type="text" name="guests[0][organization]"
                                       placeholder="Guest's home church or organization"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Special Notes</label>
                                <textarea name="guests[0][notes]" rows="2"
                                          placeholder="Any special requirements, dietary restrictions, or notes about this guest"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"></textarea>
                            </div>
                        </div>
                        </div>

                        <!-- Add Guest Button -->
                        <div class="flex justify-center mt-4">
                            <button type="button" onclick="addGuest()" class="inline-flex items-center px-4 py-2 bg-secondary hover:bg-secondary-dark text-gray-800 font-medium rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i> Add Another Guest
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Additional Notes -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-sticky-note mr-2 text-primary"></i>
                            Additional Information
                        </h3>
                        <p class="text-gray-600 text-sm mt-1">Any extra details or special requirements</p>
                    </div>

                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                        <textarea id="notes" name="notes" rows="4"
                                  placeholder="Any additional notes, special requirements, or instructions"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"></textarea>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-center gap-3 pt-4 border-t border-gray-100">
                    <button type="submit" class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 to-emerald-500 hover:from-blue-600 hover:to-emerald-600 text-white text-sm font-medium rounded-full transition-all duration-300 transform hover:scale-105 shadow-md">
                        <i class="fas fa-sparkles mr-2"></i> Create Program ✨
                    </button>
                    <a href="<?php echo BASE_URL; ?>programs" class="inline-flex items-center px-6 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-full transition-all duration-200 shadow-sm">
                        <i class="fas fa-arrow-left mr-2"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
</div>

<!-- Category Management Modal -->
<div id="categoryModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center hidden z-50 p-4">
    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white rounded-t-2xl flex-shrink-0">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tags text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-2xl font-bold">Manage Categories</h3>
                        <p class="text-sm opacity-90">Add, edit, and organize program categories</p>
                    </div>
                </div>
                <button onclick="closeCategoryModal()" class="w-8 h-8 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg flex items-center justify-center transition-all duration-200">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body with Internal Scroll -->
        <div class="flex-1 overflow-y-auto p-6 space-y-8">
            <!-- Add New Category Form -->
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-plus text-white text-sm"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800">Add New Category</h4>
                </div>
                <form id="addCategoryForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="new_category_name" class="block text-sm font-semibold text-gray-700 mb-2">Category Name</label>
                            <input type="text" id="new_category_name" name="name" required
                                   placeholder="Enter category name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                        <div>
                            <label for="new_category_color" class="block text-sm font-semibold text-gray-700 mb-2">Color Theme</label>
                            <div class="flex items-center space-x-3">
                                <input type="color" id="new_category_color" name="color_code" value="#3F7D58"
                                       class="w-16 h-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent cursor-pointer">
                                <div class="flex-1">
                                    <div class="text-xs text-gray-500">Choose a color that represents this category</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="new_category_icon" class="block text-sm font-semibold text-gray-700 mb-2">Icon</label>
                            <select id="new_category_icon" name="icon"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                <option value="fas fa-calendar">📅 Calendar</option>
                                <option value="fas fa-pray">🙏 Prayer</option>
                                <option value="fas fa-users">👥 Community</option>
                                <option value="fas fa-child">👶 Children</option>
                                <option value="fas fa-bullhorn">📢 Outreach</option>
                                <option value="fas fa-graduation-cap">🎓 Education</option>
                                <option value="fas fa-handshake">🤝 Fellowship</option>
                                <option value="fas fa-star">⭐ Special Events</option>
                                <option value="fas fa-donate">💝 Giving</option>
                                <option value="fas fa-heart">❤️ Care</option>
                                <option value="fas fa-cogs">⚙️ Administration</option>
                            </select>
                        </div>
                        <div>
                            <label for="new_category_description" class="block text-sm font-semibold text-gray-700 mb-2">Description</label>
                            <input type="text" id="new_category_description" name="description"
                                   placeholder="Brief description of this category"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-plus mr-2"></i> Add Category
                        </button>
                    </div>
                </form>
            </div>

            <!-- Categories List -->
            <div>
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                            <i class="fas fa-list text-gray-700 text-sm"></i>
                        </div>
                        <h4 class="text-xl font-bold text-gray-800">Existing Categories</h4>
                    </div>
                    <div class="text-sm text-gray-500">
                        <span id="categoryCount">0</span> categories
                    </div>
                </div>
                <div id="categoriesContainer" class="space-y-3">
                    <!-- Categories will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Department Management Modal -->
<div id="departmentModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center hidden z-50 p-4">
    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white rounded-t-2xl flex-shrink-0">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-building text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-2xl font-bold">Manage Ministry Departments</h3>
                        <p class="text-sm opacity-90">Add, edit, and organize ministry departments</p>
                    </div>
                </div>
                <button onclick="closeDepartmentModal()" class="w-8 h-8 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg flex items-center justify-center transition-all duration-200">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body with Internal Scroll -->
        <div class="flex-1 overflow-y-auto p-6 space-y-8">
            <!-- Add New Department Form -->
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-plus text-white text-sm"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800">Add New Department</h4>
                </div>
                <form id="addDepartmentForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="new_department_name" class="block text-sm font-semibold text-gray-700 mb-2">Department Name</label>
                            <input type="text" id="new_department_name" name="name" required
                                   placeholder="Enter department name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                        <div>
                            <label for="new_department_description" class="block text-sm font-semibold text-gray-700 mb-2">Description</label>
                            <input type="text" id="new_department_description" name="description"
                                   placeholder="Brief description of this department"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="new_department_email" class="block text-sm font-semibold text-gray-700 mb-2">Contact Email</label>
                            <input type="email" id="new_department_email" name="contact_email"
                                   placeholder="<EMAIL>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                        <div>
                            <label for="new_department_phone" class="block text-sm font-semibold text-gray-700 mb-2">Contact Phone</label>
                            <input type="tel" id="new_department_phone" name="contact_phone"
                                   placeholder="(*************"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-plus mr-2"></i> Add Department
                        </button>
                    </div>
                </form>
            </div>

            <!-- Departments List -->
            <div>
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                            <i class="fas fa-list text-gray-700 text-sm"></i>
                        </div>
                        <h4 class="text-xl font-bold text-gray-800">Existing Departments</h4>
                    </div>
                    <div class="text-sm text-gray-500">
                        <span id="departmentCount">0</span> departments
                    </div>
                </div>
                <div id="departmentsContainer" class="space-y-3">
                    <!-- Departments will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form interactivity
document.addEventListener('DOMContentLoaded', function() {
    // Toggle recurrence pattern field
    const isRecurringCheckbox = document.getElementById('is_recurring');
    const recurrenceGroup = document.getElementById('recurrence_group');

    isRecurringCheckbox.addEventListener('change', function() {
        if (this.checked) {
            recurrenceGroup.style.display = 'block';
        } else {
            recurrenceGroup.style.display = 'none';
            document.getElementById('recurrence_pattern').value = '';
        }
    });

    // Toggle max participants field
    const requiresRegistrationCheckbox = document.getElementById('requires_registration');
    const maxParticipantsGroup = document.getElementById('max_participants_group');

    requiresRegistrationCheckbox.addEventListener('change', function() {
        if (this.checked) {
            maxParticipantsGroup.style.display = 'block';
        } else {
            maxParticipantsGroup.style.display = 'none';
            document.getElementById('max_participants').value = '';
        }
    });

    // Set minimum end date based on start date
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');

    startDateInput.addEventListener('change', function() {
        endDateInput.min = this.value;
        if (endDateInput.value && endDateInput.value < this.value) {
            endDateInput.value = this.value;
        }
    });

    // Set default dates
    const today = new Date().toISOString().split('T')[0];
    startDateInput.min = today;
    endDateInput.min = today;

    if (!startDateInput.value) {
        startDateInput.value = today;
    }
    if (!endDateInput.value) {
        endDateInput.value = today;
    }
});

// Modal Functions
function openCategoryModal() {
    document.getElementById('categoryModal').classList.remove('hidden');
    loadCategories();
}

function closeCategoryModal() {
    document.getElementById('categoryModal').classList.add('hidden');
}

function openDepartmentModal() {
    document.getElementById('departmentModal').classList.remove('hidden');
    loadDepartments();
}

function closeDepartmentModal() {
    document.getElementById('departmentModal').classList.add('hidden');
}

// Close modals when clicking outside
window.onclick = function(event) {
    const categoryModal = document.getElementById('categoryModal');
    const departmentModal = document.getElementById('departmentModal');

    if (event.target === categoryModal) {
        closeCategoryModal();
    }
    if (event.target === departmentModal) {
        closeDepartmentModal();
    }
}

// Load Categories
function loadCategories() {
    console.log('Loading categories for modal...');

    return new Promise((resolve, reject) => {
        fetch('<?php echo BASE_URL; ?>categories/get-categories', {
            headers: {
                'Cache-Control': 'no-cache',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('Categories response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(categories => {
            console.log('Categories loaded for modal:', categories);
            const container = document.getElementById('categoriesContainer');
            container.innerHTML = '';

            // Update category count
            document.getElementById('categoryCount').textContent = categories ? categories.length : 0;

            if (!categories || categories.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-tags text-2xl text-gray-400"></i>
                        </div>
                        <p class="text-gray-500 text-sm">No categories found. Add some categories to get started.</p>
                    </div>
                `;
                resolve([]);
                return;
            }

            categories.forEach(category => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-200';
                categoryDiv.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-4 h-4 rounded-full flex-shrink-0" style="background-color: ${category.color_code}"></div>
                            <div class="w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0" style="background-color: ${category.color_code}20;">
                                <i class="${category.icon} text-lg" style="color: ${category.color_code}"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <h5 class="font-semibold text-gray-900">${category.name}</h5>
                                <p class="text-sm text-gray-500 truncate">${category.description || 'No description'}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="inline-flex items-center px-3 py-1.5 bg-blue-50 hover:bg-blue-100 text-blue-600 text-xs font-medium rounded-md transition-all duration-200" type="button" onclick="event.stopPropagation(); editCategory(${category.id})">
                                <i class="fas fa-edit mr-1"></i> Edit
                            </button>
                            <button class="inline-flex items-center px-3 py-1.5 bg-red-50 hover:bg-red-100 text-red-600 text-xs font-medium rounded-md transition-all duration-200" type="button" onclick="event.stopPropagation(); deleteCategory(${category.id}, '${category.name}')">
                                <i class="fas fa-trash mr-1"></i> Delete
                            </button>
                        </div>
                    </div>
                `;
                container.appendChild(categoryDiv);
            });

            console.log('Modal categories list updated');
            resolve(categories);
        })
        .catch(error => {
            console.error('Error loading categories:', error);
            const container = document.getElementById('categoriesContainer');
            container.innerHTML = '<p class="text-danger">Error loading categories. Please try again.</p>';
            reject(error);
        });
    });
}

// Load Departments
function loadDepartments() {
    console.log('Loading departments...');
    fetch('<?php echo BASE_URL; ?>ministry-departments/get-departments')
        .then(response => {
            console.log('Departments response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(departments => {
            console.log('Departments loaded:', departments);
            const container = document.getElementById('departmentsContainer');
            container.innerHTML = '';

            // Update department count
            document.getElementById('departmentCount').textContent = departments ? departments.length : 0;

            if (!departments || departments.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-building text-2xl text-gray-400"></i>
                        </div>
                        <p class="text-gray-500 text-sm">No departments found. Add some departments to get started.</p>
                    </div>
                `;
                return;
            }

            departments.forEach(department => {
                const departmentDiv = document.createElement('div');
                departmentDiv.className = 'bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-200';
                departmentDiv.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-building text-primary"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <h5 class="font-semibold text-gray-900">${department.name}</h5>
                                <p class="text-sm text-gray-500 truncate">${department.description || 'No description'}</p>
                                ${department.contact_email ? `<p class="text-xs text-gray-400 mt-1"><i class="fas fa-envelope mr-1"></i>${department.contact_email}</p>` : ''}
                                ${department.contact_phone ? `<p class="text-xs text-gray-400"><i class="fas fa-phone mr-1"></i>${department.contact_phone}</p>` : ''}
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="inline-flex items-center px-3 py-1.5 bg-blue-50 hover:bg-blue-100 text-blue-600 text-xs font-medium rounded-md transition-all duration-200" type="button" onclick="event.stopPropagation(); editDepartment(${department.id})">
                                <i class="fas fa-edit mr-1"></i> Edit
                            </button>
                            <button class="inline-flex items-center px-3 py-1.5 bg-red-50 hover:bg-red-100 text-red-600 text-xs font-medium rounded-md transition-all duration-200" type="button" onclick="event.stopPropagation(); deleteDepartment(${department.id}, '${department.name}')">
                                <i class="fas fa-trash mr-1"></i> Delete
                            </button>
                        </div>
                    </div>
                `;
                container.appendChild(departmentDiv);
            });
        })
        .catch(error => {
            console.error('Error loading departments:', error);
            const container = document.getElementById('departmentsContainer');
            container.innerHTML = '<p class="text-danger">Error loading departments. Please try again.</p>';
        });
}

// Add Category
document.getElementById('addCategoryForm').addEventListener('submit', function(e) {
    e.preventDefault();

    console.log('=== ADDING NEW CATEGORY ===');

    const formData = new FormData(this);
    formData.append('csrf_token', '<?php echo generate_csrf_token(); ?>');

    fetch('<?php echo BASE_URL; ?>categories/store', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(result => {
        console.log('Add category result:', result);

        if (result.success) {
            console.log('Category added successfully, refreshing UI...');

            // Reset form first
            this.reset();

            // Refresh modal list
            loadCategories().then(() => {
                console.log('Modal refreshed after add');

                // Refresh dropdown
                refreshCategoryDropdown().then(() => {
                    console.log('Dropdown refreshed after add');
                    alert(result.message);
                });
            });
        } else {
            alert('Error: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error adding category:', error);
        alert('Error adding category. Please try again.');
    });
});

// Add Department
document.getElementById('addDepartmentForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('csrf_token', '<?php echo generate_csrf_token(); ?>');

    fetch('<?php echo BASE_URL; ?>ministry-departments/store', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(result => {
        // Reload departments and update dropdown
        loadDepartments();
        refreshDepartmentDropdown();
        this.reset();

        if (result.success) {
            alert(result.message);
        } else {
            alert('Error: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error adding department:', error);
        alert('Error adding department. Please try again.');
    });
});

// Refresh dropdowns with proper data fetching
function refreshCategoryDropdown(selectedId = null) {
    console.log('Refreshing category dropdown, selectedId:', selectedId);

    return new Promise((resolve, reject) => {
        fetch('<?php echo BASE_URL; ?>categories/get-categories', {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(categories => {
            console.log('Fresh categories received:', categories);
            const select = document.getElementById('category_id');

            // Store current selection if not provided
            const currentValue = selectedId || select.value;

            // Clear and rebuild dropdown
            select.innerHTML = '<option value="">Select a category</option>';

            if (categories && categories.length > 0) {
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    option.setAttribute('data-color', category.color_code || '#3B82F6');
                    option.setAttribute('data-icon', category.icon || 'fas fa-calendar');

                    // Restore selection
                    if (category.id == currentValue) {
                        option.selected = true;
                        console.log('Restored selection for category:', category.name);
                    }

                    select.appendChild(option);
                });
            }

            console.log('Category dropdown rebuilt with', categories.length, 'categories');
            resolve(categories);
        })
        .catch(error => {
            console.error('Error refreshing category dropdown:', error);
            reject(error);
        });
    });
}

function refreshDepartmentDropdown(selectedId = null) {
    console.log('Refreshing department dropdown, selectedId:', selectedId);

    return new Promise((resolve, reject) => {
        fetch('<?php echo BASE_URL; ?>ministry-departments/get-departments', {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(departments => {
            console.log('Fresh departments received:', departments);
            const select = document.getElementById('department_id');

            // Store current selection if not provided
            const currentValue = selectedId || select.value;

            // Clear and rebuild dropdown
            select.innerHTML = '<option value="">Select a department</option>';

            if (departments && departments.length > 0) {
                departments.forEach(department => {
                    const option = document.createElement('option');
                    option.value = department.id;
                    option.textContent = department.name;

                    // Restore selection
                    if (department.id == currentValue) {
                        option.selected = true;
                        console.log('Restored selection for department:', department.name);
                    }

                    select.appendChild(option);
                });
            }

            console.log('Department dropdown rebuilt with', departments.length, 'departments');
            resolve(departments);
        })
        .catch(error => {
            console.error('Error refreshing department dropdown:', error);
            reject(error);
        });
    });
}

// Edit Category Function
function editCategory(id) {
    console.log('Editing category with ID:', id);

    // Find the category data
    fetch('<?php echo BASE_URL; ?>categories/get-categories')
        .then(response => {
            console.log('Edit category response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(categories => {
            console.log('Categories received for edit:', categories);
            const category = categories.find(c => c.id == id);
            if (!category) {
                alert('Category not found');
                return;
            }

            console.log('Found category for edit:', category);

            // Create edit form with Tailwind styling
            const editForm = document.createElement('div');
            editForm.className = 'bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-6 border border-yellow-200';
            editForm.id = `editCategoryForm_container_${id}`;
            editForm.innerHTML = `
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-edit text-white text-sm"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800">Edit Category: ${category.name}</h4>
                </div>
                <form id="editCategoryForm_${id}" class="space-y-6">
                    <input type="hidden" name="id" value="${id}">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="edit_category_name_${id}" class="block text-sm font-semibold text-gray-700 mb-2">Category Name</label>
                            <input type="text" id="edit_category_name_${id}" name="name" value="${category.name}" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                        <div>
                            <label for="edit_category_color_${id}" class="block text-sm font-semibold text-gray-700 mb-2">Color Theme</label>
                            <div class="flex items-center space-x-3">
                                <input type="color" id="edit_category_color_${id}" name="color_code" value="${category.color_code}"
                                       class="w-16 h-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent cursor-pointer">
                                <div class="flex-1">
                                    <div class="text-xs text-gray-500">Choose a color that represents this category</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="edit_category_icon_${id}" class="block text-sm font-semibold text-gray-700 mb-2">Icon</label>
                            <select id="edit_category_icon_${id}" name="icon"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                                <option value="fas fa-calendar" ${category.icon === 'fas fa-calendar' ? 'selected' : ''}>📅 Calendar</option>
                                <option value="fas fa-pray" ${category.icon === 'fas fa-pray' ? 'selected' : ''}>🙏 Prayer</option>
                                <option value="fas fa-users" ${category.icon === 'fas fa-users' ? 'selected' : ''}>👥 Community</option>
                                <option value="fas fa-child" ${category.icon === 'fas fa-child' ? 'selected' : ''}>👶 Children</option>
                                <option value="fas fa-bullhorn" ${category.icon === 'fas fa-bullhorn' ? 'selected' : ''}>📢 Outreach</option>
                                <option value="fas fa-graduation-cap" ${category.icon === 'fas fa-graduation-cap' ? 'selected' : ''}>🎓 Education</option>
                                <option value="fas fa-handshake" ${category.icon === 'fas fa-handshake' ? 'selected' : ''}>🤝 Fellowship</option>
                                <option value="fas fa-star" ${category.icon === 'fas fa-star' ? 'selected' : ''}>⭐ Special Events</option>
                                <option value="fas fa-donate" ${category.icon === 'fas fa-donate' ? 'selected' : ''}>💝 Giving</option>
                                <option value="fas fa-heart" ${category.icon === 'fas fa-heart' ? 'selected' : ''}>❤️ Care</option>
                                <option value="fas fa-cogs" ${category.icon === 'fas fa-cogs' ? 'selected' : ''}>⚙️ Administration</option>
                            </select>
                        </div>
                        <div>
                            <label for="edit_category_description_${id}" class="block text-sm font-semibold text-gray-700 mb-2">Description</label>
                            <input type="text" id="edit_category_description_${id}" name="description" value="${category.description || ''}"
                                   placeholder="Brief description of this category"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" class="inline-flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold rounded-lg transition-all duration-200" onclick="cancelEditCategory(${id})">
                            <i class="fas fa-times mr-2"></i> Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-save mr-2"></i> Update Category
                        </button>
                    </div>
                </form>
            `;

            // Find the add form section and replace it temporarily
            const addFormSection = document.querySelector('#categoryModal .bg-gradient-to-br.from-gray-50.to-gray-100');
            if (addFormSection) {
                const originalAddForm = addFormSection.cloneNode(true);
                addFormSection.style.display = 'none';
                addFormSection.parentNode.insertBefore(editForm, addFormSection);

                // Store original form for restoration
                editForm.originalAddForm = originalAddForm;
            } else {
                console.error('Could not find add form section');
                alert('Error setting up edit form. Please try again.');
                return;
            }

            // Add event listener for the edit form
            const editFormElement = document.getElementById(`editCategoryForm_${id}`);
            editFormElement.addEventListener('submit', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Edit form submitted for category ID:', id);
                updateCategory(id, this);
            });
        })
        .catch(error => {
            console.error('Error loading category for edit:', error);
            alert('Error loading category data. Please try again.');
        });
}

// Cancel Edit Category
function cancelEditCategory(id) {
    console.log('Cancelling edit for category:', id);

    const editForm = document.getElementById(`editCategoryForm_container_${id}`);
    const addFormSection = document.querySelector('#categoryModal .bg-gradient-to-br.from-gray-50.to-gray-100');

    if (editForm && addFormSection) {
        addFormSection.style.display = 'block';
        editForm.remove();
        console.log('Edit form cancelled and add form restored');
    } else {
        console.error('Could not find edit form or add form section');
    }
}

// Update Category
function updateCategory(id, form) {
    console.log('=== UPDATING CATEGORY ===');
    console.log('Category ID:', id);

    // Store the current dropdown selection
    const categorySelect = document.getElementById('category_id');
    const wasSelected = categorySelect.value == id;
    console.log('Was this category selected in dropdown?', wasSelected);

    const formData = new FormData(form);
    formData.append('csrf_token', '<?php echo generate_csrf_token(); ?>');

    // Show what we're sending
    console.log('Sending update data for category ID:', id);
    console.log('Form element:', form);
    console.log('FormData contents:');
    for (let pair of formData.entries()) {
        console.log('  ' + pair[0] + ': ' + pair[1]);
    }

    // Verify the ID is being sent correctly
    if (!formData.get('id')) {
        console.error('ERROR: No ID found in form data!');
        alert('Error: Category ID is missing. Please try again.');
        return;
    }

    fetch('<?php echo BASE_URL; ?>categories/update', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Update response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(result => {
        console.log('Update result:', result);

        if (result.success) {
            console.log('Update successful, refreshing UI...');

            // Cancel edit mode first
            cancelEditCategory(id);

            // Add a small delay before refreshing to ensure the backend has processed the update
            setTimeout(() => {
                // Refresh the modal list
                loadCategories().then(() => {
                    console.log('Modal list refreshed after update');

                    // Refresh the dropdown with the updated category selected if it was selected
                    const selectedId = wasSelected ? id : null;
                    refreshCategoryDropdown(selectedId).then(() => {
                        console.log('Dropdown refresh complete after update');
                        alert(result.message);
                    }).catch(error => {
                        console.error('Dropdown refresh failed:', error);
                        alert(result.message + ' (Note: Please refresh the page to see updated dropdown)');
                    });
                }).catch(error => {
                    console.error('Error refreshing categories after update:', error);
                    alert('Category updated but there was an error refreshing the list. Please refresh the page.');
                });
            }, 100); // Small delay to ensure backend processing is complete
        } else {
            console.error('Update failed:', result);
            alert('Error: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error updating category:', error);
        alert('Error updating category. Please try again.');
    });
}

function deleteCategory(id, name) {
    if (confirm(`Are you sure you want to delete the category "${name}"?`)) {
        console.log('=== DELETING CATEGORY ===');
        console.log('Category ID:', id, 'Name:', name);

        // Check if this category is currently selected
        const categorySelect = document.getElementById('category_id');
        const wasSelected = categorySelect.value == id;
        console.log('Was selected category being deleted?', wasSelected);

        const formData = new FormData();
        formData.append('id', id);
        formData.append('csrf_token', '<?php echo generate_csrf_token(); ?>');

        fetch('<?php echo BASE_URL; ?>categories/delete', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(result => {
            console.log('Delete result:', result);

            if (result.success) {
                console.log('Category deleted successfully, refreshing UI...');

                // Refresh modal list
                loadCategories().then(() => {
                    console.log('Modal refreshed after delete');

                    // Refresh dropdown (clear selection if deleted category was selected)
                    const selectedId = wasSelected ? null : categorySelect.value;
                    refreshCategoryDropdown(selectedId).then(() => {
                        console.log('Dropdown refreshed after delete');
                        alert(result.message);
                    });
                });
            } else {
                alert('Error: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error deleting category:', error);
            alert('Error deleting category. Please try again.');
        });
    }
}

function editDepartment(id) {
    console.log('Editing department with ID:', id);

    // Find the department data
    fetch('<?php echo BASE_URL; ?>ministry-departments/get-departments')
        .then(response => {
            console.log('Edit department response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(departments => {
            console.log('Departments received for edit:', departments);
            const department = departments.find(d => d.id == id);
            if (!department) {
                alert('Department not found');
                return;
            }

            console.log('Found department for edit:', department);

            // Create edit form with Tailwind styling
            const editForm = document.createElement('div');
            editForm.className = 'bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-6 border border-yellow-200';
            editForm.id = `editDepartmentForm_container_${id}`;
            editForm.innerHTML = `
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-edit text-white text-sm"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800">Edit Department: ${department.name}</h4>
                </div>
                <form id="editDepartmentForm_${id}" class="space-y-6">
                    <input type="hidden" name="id" value="${id}">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="edit_department_name_${id}" class="block text-sm font-semibold text-gray-700 mb-2">Department Name</label>
                            <input type="text" id="edit_department_name_${id}" name="name" value="${department.name}" required
                                   placeholder="Enter department name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                        <div>
                            <label for="edit_department_description_${id}" class="block text-sm font-semibold text-gray-700 mb-2">Description</label>
                            <input type="text" id="edit_department_description_${id}" name="description" value="${department.description || ''}"
                                   placeholder="Brief description of this department"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="edit_department_email_${id}" class="block text-sm font-semibold text-gray-700 mb-2">Contact Email</label>
                            <input type="email" id="edit_department_email_${id}" name="contact_email" value="${department.contact_email || ''}"
                                   placeholder="<EMAIL>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                        <div>
                            <label for="edit_department_phone_${id}" class="block text-sm font-semibold text-gray-700 mb-2">Contact Phone</label>
                            <input type="tel" id="edit_department_phone_${id}" name="contact_phone" value="${department.contact_phone || ''}"
                                   placeholder="(*************"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 bg-white shadow-sm">
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" class="inline-flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold rounded-lg transition-all duration-200" onclick="cancelEditDepartment(${id})">
                            <i class="fas fa-times mr-2"></i> Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-save mr-2"></i> Update Department
                        </button>
                    </div>
                </form>
            `;

            // Find the add form section and replace it temporarily
            const addFormSection = document.querySelector('#departmentModal .bg-gradient-to-br.from-gray-50.to-gray-100');
            if (addFormSection) {
                const originalAddForm = addFormSection.cloneNode(true);
                addFormSection.style.display = 'none';
                addFormSection.parentNode.insertBefore(editForm, addFormSection);

                // Store original form for restoration
                editForm.originalAddForm = originalAddForm;
            } else {
                console.error('Could not find add form section');
                alert('Error setting up edit form. Please try again.');
                return;
            }

            // Add event listener for the edit form
            document.getElementById(`editDepartmentForm_${id}`).addEventListener('submit', function(e) {
                e.preventDefault();
                updateDepartment(id, this);
            });
        })
        .catch(error => {
            console.error('Error loading department for edit:', error);
            alert('Error loading department data. Please try again.');
        });
}

// Cancel Edit Department
function cancelEditDepartment(id) {
    console.log('Cancelling edit for department:', id);

    const editForm = document.getElementById(`editDepartmentForm_container_${id}`);
    const addFormSection = document.querySelector('#departmentModal .bg-gradient-to-br.from-gray-50.to-gray-100');

    if (editForm && addFormSection) {
        addFormSection.style.display = 'block';
        editForm.remove();
        console.log('Edit form cancelled and add form restored');
    } else {
        console.error('Could not find edit form or add form section');
    }
}

// Update Department
function updateDepartment(id, form) {
    console.log('Updating department with ID:', id);

    // Store the current dropdown selection
    const departmentSelect = document.getElementById('department_id');
    const wasSelected = departmentSelect.value == id;

    const formData = new FormData(form);
    formData.append('csrf_token', '<?php echo generate_csrf_token(); ?>');

    fetch('<?php echo BASE_URL; ?>ministry-departments/update', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(result => {
        console.log('Department update result:', result);

        // Cancel edit mode
        cancelEditDepartment(id);

        // Reload departments and update dropdown
        loadDepartments();

        // Refresh dropdown and restore selection if it was selected
        setTimeout(() => {
            refreshDepartmentDropdown();
            if (wasSelected) {
                setTimeout(() => {
                    document.getElementById('department_id').value = id;
                }, 100);
            }
        }, 100);

        if (result.success) {
            alert(result.message);
        } else {
            alert('Error: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error updating department:', error);
        alert('Error updating department. Please try again.');
    });
}

function deleteDepartment(id, name) {
    if (confirm(`Are you sure you want to delete the department "${name}"?`)) {
        const formData = new FormData();
        formData.append('id', id);
        formData.append('csrf_token', '<?php echo generate_csrf_token(); ?>');

        fetch('<?php echo BASE_URL; ?>ministry-departments/delete', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(result => {
            loadDepartments();
            refreshDepartmentDropdown();

            if (result.success) {
                alert(result.message);
            } else {
                alert('Error: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error deleting department:', error);
            alert('Error deleting department. Please try again.');
        });
    }
}

// Coordinator Search Functionality
let coordinatorMembers = [];
let coordinatorSearchTimeout;

// Load all members for coordinator search
function loadCoordinatorMembers() {
    // Get members data from PHP
    coordinatorMembers = [
        <?php foreach ($members as $member): ?>
        {
            id: <?php echo $member['id']; ?>,
            name: "<?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>",
            first_name: "<?php echo htmlspecialchars($member['first_name']); ?>",
            last_name: "<?php echo htmlspecialchars($member['last_name']); ?>",
            email: "<?php echo htmlspecialchars($member['email'] ?? ''); ?>"
        },
        <?php endforeach; ?>
    ];
}

// Initialize coordinator search
function initializeCoordinatorSearch() {
    loadCoordinatorMembers();

    const searchInput = document.getElementById('coordinator_search');
    const dropdown = document.getElementById('coordinator_dropdown');
    const resultsContainer = document.getElementById('coordinator_results');

    // Search input event listener
    searchInput.addEventListener('input', function() {
        clearTimeout(coordinatorSearchTimeout);
        const query = this.value.trim();

        if (query.length < 2) {
            hideCoordinatorDropdown();
            return;
        }

        coordinatorSearchTimeout = setTimeout(() => {
            searchCoordinators(query);
        }, 300);
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.relative')) {
            hideCoordinatorDropdown();
        }
    });

    // Handle keyboard navigation
    searchInput.addEventListener('keydown', function(event) {
        const items = dropdown.querySelectorAll('.coordinator-item');
        const activeItem = dropdown.querySelector('.coordinator-item.active');
        let currentIndex = Array.from(items).indexOf(activeItem);

        switch(event.key) {
            case 'ArrowDown':
                event.preventDefault();
                currentIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                setActiveCoordinatorItem(items, currentIndex);
                break;
            case 'ArrowUp':
                event.preventDefault();
                currentIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                setActiveCoordinatorItem(items, currentIndex);
                break;
            case 'Enter':
                event.preventDefault();
                if (activeItem) {
                    selectCoordinator(activeItem.dataset.id, activeItem.dataset.name);
                }
                break;
            case 'Escape':
                hideCoordinatorDropdown();
                break;
        }
    });
}

// Search coordinators
function searchCoordinators(query) {
    const results = coordinatorMembers.filter(member =>
        member.name.toLowerCase().includes(query.toLowerCase()) ||
        member.first_name.toLowerCase().includes(query.toLowerCase()) ||
        member.last_name.toLowerCase().includes(query.toLowerCase()) ||
        (member.email && member.email.toLowerCase().includes(query.toLowerCase()))
    );

    displayCoordinatorResults(results);
}

// Display search results
function displayCoordinatorResults(results) {
    const resultsContainer = document.getElementById('coordinator_results');
    const dropdown = document.getElementById('coordinator_dropdown');

    if (results.length === 0) {
        resultsContainer.innerHTML = `
            <div class="px-4 py-3 text-sm text-gray-500 text-center">
                <i class="fas fa-search text-gray-400 mb-2"></i>
                <p>No coordinators found</p>
            </div>
        `;
    } else {
        resultsContainer.innerHTML = results.map(member => `
            <div class="coordinator-item px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150"
                 data-id="${member.id}"
                 data-name="${member.name}"
                 onclick="selectCoordinator(${member.id}, '${member.name}')">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-primary text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="font-medium text-gray-900 truncate">${member.name}</p>
                        ${member.email ? `<p class="text-sm text-gray-500 truncate">${member.email}</p>` : ''}
                    </div>
                </div>
            </div>
        `).join('');
    }

    showCoordinatorDropdown();
}

// Set active item for keyboard navigation
function setActiveCoordinatorItem(items, index) {
    items.forEach(item => item.classList.remove('active', 'bg-primary', 'bg-opacity-10'));
    if (items[index]) {
        items[index].classList.add('active', 'bg-primary', 'bg-opacity-10');
    }
}

// Select coordinator
function selectCoordinator(id, name) {
    document.getElementById('coordinator_id').value = id;
    document.getElementById('coordinator_search').value = name;
    document.getElementById('selected_coordinator_name').textContent = name;

    // Show selected coordinator display
    document.getElementById('selected_coordinator').classList.remove('hidden');

    hideCoordinatorDropdown();

    console.log('Selected coordinator:', { id, name });
}

// Clear coordinator selection
function clearCoordinatorSelection() {
    document.getElementById('coordinator_id').value = '';
    document.getElementById('coordinator_search').value = '';
    document.getElementById('selected_coordinator').classList.add('hidden');
    hideCoordinatorDropdown();
}

// Show dropdown
function showCoordinatorDropdown() {
    document.getElementById('coordinator_dropdown').classList.remove('hidden');
}

// Hide dropdown
function hideCoordinatorDropdown() {
    document.getElementById('coordinator_dropdown').classList.add('hidden');
}

// Guest Management Functions
let guestCounter = 1;

function toggleGuestsSection() {
    const guestsSection = document.getElementById('guests_section');
    const toggleIcon = document.getElementById('guests_toggle_icon');
    const toggleText = document.getElementById('guests_toggle_text');

    if (guestsSection.classList.contains('hidden')) {
        // Show guests section
        guestsSection.classList.remove('hidden');
        toggleIcon.className = 'fas fa-minus mr-1';
        toggleText.textContent = 'Hide Guests';

        // If no guests exist, add one
        if (document.querySelectorAll('.guest-entry').length === 0) {
            addGuest();
        }
    } else {
        // Hide guests section
        guestsSection.classList.add('hidden');
        toggleIcon.className = 'fas fa-plus mr-1';
        toggleText.textContent = 'Add Guests';
    }
}

function addGuest() {
    const container = document.getElementById('guests_container');
    const guestEntry = document.createElement('div');
    guestEntry.className = 'guest-entry bg-gray-50 rounded-lg p-4 border border-gray-200';

    guestEntry.innerHTML = `
        <div class="flex items-center justify-between mb-4">
            <h4 class="font-medium text-gray-800 flex items-center">
                <i class="fas fa-user mr-2 text-primary"></i>
                Guest #${guestCounter + 1}
            </h4>
            <button type="button" onclick="removeGuest(this)" class="text-red-500 hover:text-red-700 transition-colors duration-200">
                <i class="fas fa-trash"></i>
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Guest Name</label>
                <input type="text" name="guests[${guestCounter}][name]"
                       placeholder="Enter guest's full name"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Role/Title</label>
                <input type="text" name="guests[${guestCounter}][role]"
                       placeholder="e.g., Guest Speaker, Worship Leader, Pastor"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                <input type="email" name="guests[${guestCounter}][email]"
                       placeholder="<EMAIL>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                <input type="tel" name="guests[${guestCounter}][phone]"
                       placeholder="(*************"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
            </div>
        </div>

        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Organization/Church</label>
            <input type="text" name="guests[${guestCounter}][organization]"
                   placeholder="Guest's home church or organization"
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
        </div>

        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Special Notes</label>
            <textarea name="guests[${guestCounter}][notes]" rows="2"
                      placeholder="Any special requirements, dietary restrictions, or notes about this guest"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"></textarea>
        </div>
    `;

    container.appendChild(guestEntry);
    guestCounter++;

    // Show remove button for all guests if there's more than one
    updateRemoveButtons();

    // Smooth scroll to the new guest entry
    guestEntry.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function removeGuest(button) {
    const guestEntry = button.closest('.guest-entry');
    const container = document.getElementById('guests_container');

    // Add fade out animation
    guestEntry.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
    guestEntry.style.opacity = '0';
    guestEntry.style.transform = 'translateX(-20px)';

    setTimeout(() => {
        container.removeChild(guestEntry);
        updateRemoveButtons();
        renumberGuests();
    }, 300);
}

function updateRemoveButtons() {
    const guestEntries = document.querySelectorAll('.guest-entry');
    guestEntries.forEach((entry, index) => {
        const removeButton = entry.querySelector('button[onclick*="removeGuest"]');
        if (guestEntries.length > 1) {
            removeButton.style.display = 'block';
        } else {
            removeButton.style.display = 'none';
        }
    });
}

function renumberGuests() {
    const guestEntries = document.querySelectorAll('.guest-entry');
    guestEntries.forEach((entry, index) => {
        const header = entry.querySelector('h4');
        header.innerHTML = `<i class="fas fa-user mr-2 text-primary"></i>Guest #${index + 1}`;

        // Update input names to maintain proper indexing
        const inputs = entry.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            const name = input.getAttribute('name');
            if (name && name.includes('guests[')) {
                const newName = name.replace(/guests\[\d+\]/, `guests[${index}]`);
                input.setAttribute('name', newName);
            }
        });
    });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCoordinatorSearch();
    updateRemoveButtons();
});
</script>
