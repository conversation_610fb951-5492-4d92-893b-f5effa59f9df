<?php
/**
 * AttendanceQrSession Model
 * 
 * Manages QR code attendance sessions
 */
class AttendanceQrSession {
    // Database connection
    private $conn;
    private $table_name = "attendance_qr_sessions";

    // Properties
    public $id;
    public $service_id;
    public $attendance_date;
    public $token;
    public $created_by;
    public $created_at;
    public $expires_at;
    public $status;

    /**
     * Constructor
     *
     * @param object $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create a new QR session
     *
     * @return boolean
     */
    public function create() {
        // Query
        $query = "INSERT INTO " . $this->table_name . " 
                  SET service_id = :service_id, 
                      attendance_date = :attendance_date, 
                      token = :token, 
                      created_by = :created_by, 
                      created_at = :created_at, 
                      expires_at = :expires_at, 
                      status = :status";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->service_id = htmlspecialchars(strip_tags($this->service_id));
        $this->attendance_date = htmlspecialchars(strip_tags($this->attendance_date));
        $this->token = htmlspecialchars(strip_tags($this->token));
        $this->created_by = htmlspecialchars(strip_tags($this->created_by));
        $this->created_at = htmlspecialchars(strip_tags($this->created_at));
        $this->expires_at = htmlspecialchars(strip_tags($this->expires_at));
        $this->status = htmlspecialchars(strip_tags($this->status));

        // Bind parameters
        $stmt->bindParam(":service_id", $this->service_id);
        $stmt->bindParam(":attendance_date", $this->attendance_date);
        $stmt->bindParam(":token", $this->token);
        $stmt->bindParam(":created_by", $this->created_by);
        $stmt->bindParam(":created_at", $this->created_at);
        $stmt->bindParam(":expires_at", $this->expires_at);
        $stmt->bindParam(":status", $this->status);

        // Execute query
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Get QR session by token
     *
     * @param string $token
     * @return boolean
     */
    public function getByToken($token) {
        // Query
        $query = "SELECT * FROM " . $this->table_name . " WHERE token = :token LIMIT 1";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind parameter
        $stmt->bindParam(":token", $token);

        // Execute query
        $stmt->execute();

        // Check if record exists
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            // Set properties
            $this->id = $row['id'];
            $this->service_id = $row['service_id'];
            $this->attendance_date = $row['attendance_date'];
            $this->token = $row['token'];
            $this->created_by = $row['created_by'];
            $this->created_at = $row['created_at'];
            $this->expires_at = $row['expires_at'];
            $this->status = $row['status'];

            return true;
        }

        return false;
    }

    /**
     * Get QR session by ID
     *
     * @param int $id
     * @return boolean
     */
    public function getById($id) {
        // Query
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind parameter
        $stmt->bindParam(":id", $id);

        // Execute query
        $stmt->execute();

        // Check if record exists
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            // Set properties
            $this->id = $row['id'];
            $this->service_id = $row['service_id'];
            $this->attendance_date = $row['attendance_date'];
            $this->token = $row['token'];
            $this->created_by = $row['created_by'];
            $this->created_at = $row['created_at'];
            $this->expires_at = $row['expires_at'];
            $this->status = $row['status'];

            return true;
        }

        return false;
    }

    /**
     * Get active sessions for a user
     *
     * @param int $user_id
     * @return PDOStatement
     */
    public function getActiveSessionsByUser($user_id) {
        // Query
        $query = "SELECT qr.*, s.name as service_name 
                  FROM " . $this->table_name . " qr
                  JOIN services s ON qr.service_id = s.id
                  WHERE qr.created_by = :user_id 
                  AND qr.status = 'active' 
                  AND qr.expires_at > NOW()
                  ORDER BY qr.created_at DESC";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind parameter
        $stmt->bindParam(":user_id", $user_id);

        // Execute query
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get recent sessions
     *
     * @param int $limit
     * @return PDOStatement
     */
    public function getRecentSessions($limit = 10) {
        // Query
        $query = "SELECT qr.*, s.name as service_name, u.username as created_by_name
                  FROM " . $this->table_name . " qr
                  JOIN services s ON qr.service_id = s.id
                  JOIN users u ON qr.created_by = u.id
                  ORDER BY qr.created_at DESC
                  LIMIT :limit";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind parameter
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);

        // Execute query
        $stmt->execute();

        return $stmt;
    }

    /**
     * Update QR session status
     *
     * @return boolean
     */
    public function updateStatus() {
        // Query
        $query = "UPDATE " . $this->table_name . " 
                  SET status = :status 
                  WHERE id = :id";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameters
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":id", $this->id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Close expired sessions
     *
     * @return boolean
     */
    public function closeExpiredSessions() {
        // Query
        $query = "UPDATE " . $this->table_name . "
                  SET status = 'expired'
                  WHERE status = 'active'
                  AND expires_at <= NOW()";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Get attendance records for this QR session
     *
     * @return array
     */
    public function getAttendanceRecords() {
        $query = "SELECT a.*, m.first_name, m.last_name, m.phone_number, m.department,
                         s.name as service_name
                  FROM attendance a
                  JOIN members m ON a.member_id = m.id
                  JOIN services s ON a.service_id = s.id
                  WHERE a.qr_session_id = :qr_session_id
                  ORDER BY a.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':qr_session_id', $this->id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get QR session statistics
     *
     * @return array
     */
    public function getSessionStats() {
        $query = "SELECT
                    COUNT(DISTINCT a.member_id) as total_attendance,
                    COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
                    COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
                    MIN(a.created_at) as first_attendance,
                    MAX(a.created_at) as last_attendance,
                    AVG(TIMESTAMPDIFF(MINUTE, :session_start, a.created_at)) as avg_response_time
                  FROM attendance a
                  WHERE a.qr_session_id = :qr_session_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':qr_session_id', $this->id);
        $stmt->bindParam(':session_start', $this->created_at);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Manually close a QR session
     *
     * @return boolean
     */
    public function closeSession() {
        $query = "UPDATE " . $this->table_name . "
                  SET status = 'closed'
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    /**
     * Extend QR session expiry time
     *
     * @param int $additional_minutes
     * @return boolean
     */
    public function extendSession($additional_minutes) {
        $new_expiry = date('Y-m-d H:i:s', strtotime($this->expires_at . " +{$additional_minutes} minutes"));

        $query = "UPDATE " . $this->table_name . "
                  SET expires_at = :new_expiry
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':new_expiry', $new_expiry);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            $this->expires_at = $new_expiry;
            return true;
        }

        return false;
    }

    /**
     * Delete QR session and related attendance records
     *
     * @return boolean
     */
    public function deleteSession() {
        try {
            // Start transaction
            $this->conn->beginTransaction();

            // First, delete related attendance records
            $delete_attendance_query = "DELETE FROM attendance WHERE qr_session_id = :id";
            $stmt = $this->conn->prepare($delete_attendance_query);
            $stmt->bindParam(':id', $this->id);
            $stmt->execute();

            // Then delete the QR session
            $delete_session_query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($delete_session_query);
            $stmt->bindParam(':id', $this->id);
            $stmt->execute();

            // Commit transaction
            $this->conn->commit();
            return true;

        } catch (Exception $e) {
            // Rollback transaction on error
            $this->conn->rollback();
            error_log("Error deleting QR session: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Archive old QR sessions (older than specified days)
     *
     * @param int $days_old
     * @return boolean
     */
    public function archiveOldSessions($days_old = 90) {
        $cutoff_date = date('Y-m-d', strtotime("-{$days_old} days"));

        $query = "UPDATE " . $this->table_name . "
                  SET status = 'archived'
                  WHERE attendance_date < :cutoff_date
                  AND status IN ('expired', 'closed')";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':cutoff_date', $cutoff_date);

        return $stmt->execute();
    }

    /**
     * Get recent sessions with attendance counts
     *
     * @param int $limit
     * @return array
     */
    public function getRecentSessionsWithStats($limit = 10) {
        $query = "SELECT qr.*, s.name as service_name, u.username as created_by_name,
                         COUNT(DISTINCT a.member_id) as attendance_count,
                         MAX(a.created_at) as last_attendance_at
                  FROM " . $this->table_name . " qr
                  JOIN services s ON qr.service_id = s.id
                  LEFT JOIN users u ON qr.created_by = u.id
                  LEFT JOIN attendance a ON qr.id = a.qr_session_id
                  WHERE qr.status != 'archived'
                  GROUP BY qr.id
                  ORDER BY qr.created_at DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get detailed session analytics including expired sessions
     *
     * @param string $status Filter by status (optional)
     * @param int $limit
     * @return array
     */
    public function getSessionsWithDetailedAnalytics($status = null, $limit = 50) {
        $where_clause = $status ? "WHERE qr.status = :status" : "";

        $query = "SELECT qr.*,
                         s.name as service_name,
                         u.username as created_by_name,
                         qr.attendance_count,
                         qr.last_used_at,
                         TIMESTAMPDIFF(MINUTE, qr.created_at, qr.expires_at) as session_duration_minutes,
                         TIMESTAMPDIFF(MINUTE, qr.created_at, COALESCE(qr.last_used_at, qr.expires_at)) as active_duration_minutes,
                         CASE
                            WHEN qr.status = 'active' THEN 'Active'
                            WHEN qr.status = 'expired' THEN 'Expired'
                            WHEN qr.status = 'closed' THEN 'Manually Closed'
                            WHEN qr.status = 'archived' THEN 'Archived'
                            ELSE 'Unknown'
                         END as status_display,
                         -- Calculate efficiency metrics
                         CASE
                            WHEN qr.attendance_count > 0 THEN
                                ROUND((qr.attendance_count / TIMESTAMPDIFF(MINUTE, qr.created_at, COALESCE(qr.last_used_at, qr.expires_at))) * 60, 2)
                            ELSE 0
                         END as attendance_per_hour
                  FROM " . $this->table_name . " qr
                  JOIN services s ON qr.service_id = s.id
                  LEFT JOIN users u ON qr.created_by = u.id
                  $where_clause
                  ORDER BY qr.created_at DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        if ($status) {
            $stmt->bindParam(":status", $status);
        }
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get comprehensive session details for a specific session
     *
     * @param int $session_id
     * @return array|false
     */
    public function getSessionDetailedAnalytics($session_id) {
        // Get session basic info
        $session_query = "SELECT qr.*,
                                 s.name as service_name,
                                 s.description as service_description,
                                 u.username as created_by_name
                          FROM " . $this->table_name . " qr
                          JOIN services s ON qr.service_id = s.id
                          LEFT JOIN users u ON qr.created_by = u.id
                          WHERE qr.id = :session_id";

        $stmt = $this->conn->prepare($session_query);
        $stmt->bindParam(':session_id', $session_id);
        $stmt->execute();

        $session = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$session) {
            return false;
        }

        // Get attendance details
        $attendance_query = "SELECT a.*,
                                    m.first_name,
                                    m.last_name,
                                    m.phone_number,
                                    m.department,
                                    m.gender,
                                    TIMESTAMPDIFF(MINUTE, :session_start, a.created_at) as minutes_after_start
                             FROM attendance a
                             JOIN members m ON a.member_id = m.id
                             WHERE a.qr_session_id = :session_id
                             ORDER BY a.created_at ASC";

        $stmt = $this->conn->prepare($attendance_query);
        $stmt->bindParam(':session_id', $session_id);
        $stmt->bindParam(':session_start', $session['created_at']);
        $stmt->execute();

        $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate analytics
        $analytics = $this->calculateSessionAnalytics($session, $attendance_records);

        // Get member engagement data for this session
        $member_engagement = $this->getMemberEngagementForSession($session_id, $attendance_records);

        return [
            'session' => $session,
            'attendance_records' => $attendance_records,
            'analytics' => $analytics,
            'member_engagement' => $member_engagement
        ];
    }

    /**
     * Get member engagement analysis for a specific session
     *
     * @param int $session_id
     * @param array $attendance_records
     * @return array
     */
    public function getMemberEngagementForSession($session_id, $attendance_records) {
        try {
            $engagement_data = [];

            foreach ($attendance_records as $record) {
                $member_id = $record['member_id'];

                // Get member's historical engagement
                $member_stats = $this->calculateMemberEngagement($member_id);

                $engagement_data[] = [
                    'member_id' => $member_id,
                    'name' => $record['first_name'] . ' ' . $record['last_name'],
                    'phone' => $record['phone_number'],
                    'department' => $record['department'],
                    'current_session' => [
                        'status' => $record['status'],
                        'response_time' => $record['minutes_after_start'],
                        'marked_at' => $record['created_at']
                    ],
                    'engagement_score' => $member_stats['engagement_score'],
                    'attendance_frequency' => $member_stats['attendance_frequency'],
                    'risk_level' => $member_stats['risk_level'],
                    'last_attendance' => $member_stats['last_attendance'],
                    'total_sessions' => $member_stats['total_sessions'],
                    'streak' => $member_stats['current_streak'],
                    'trend' => $member_stats['trend'],
                    'recommendations' => $member_stats['recommendations']
                ];
            }

            // Sort by engagement score (highest first)
            usort($engagement_data, function($a, $b) {
                return $b['engagement_score'] - $a['engagement_score'];
            });

            return [
                'members' => $engagement_data,
                'summary' => $this->calculateEngagementSummary($engagement_data)
            ];

        } catch (Exception $e) {
            error_log("Error getting member engagement: " . $e->getMessage());
            return ['members' => [], 'summary' => []];
        }
    }

    /**
     * Calculate detailed analytics for a session
     *
     * @param array $session
     * @param array $attendance_records
     * @return array
     */
    private function calculateSessionAnalytics($session, $attendance_records) {
        $total_attendance = count($attendance_records);
        $present_count = 0;
        $late_count = 0;
        $gender_stats = ['male' => 0, 'female' => 0, 'other' => 0];
        $department_stats = [];
        $hourly_distribution = [];
        $response_times = [];

        foreach ($attendance_records as $record) {
            // Status counts
            if ($record['status'] === 'present') $present_count++;
            if ($record['status'] === 'late') $late_count++;

            // Gender distribution
            $gender = strtolower($record['gender'] ?? 'other');
            if (isset($gender_stats[$gender])) {
                $gender_stats[$gender]++;
            } else {
                $gender_stats['other']++;
            }

            // Department distribution
            $dept = $record['department'] ?? 'Unknown';
            $department_stats[$dept] = ($department_stats[$dept] ?? 0) + 1;

            // Hourly distribution
            $hour = date('H', strtotime($record['created_at']));
            $hourly_distribution[$hour] = ($hourly_distribution[$hour] ?? 0) + 1;

            // Response times
            $response_times[] = $record['minutes_after_start'];
        }

        // Calculate session duration and efficiency
        $session_start = strtotime($session['created_at']);
        $session_end = strtotime($session['expires_at']);
        $last_attendance = $attendance_records ? strtotime(end($attendance_records)['created_at']) : $session_start;

        $total_duration_minutes = ($session_end - $session_start) / 60;
        $active_duration_minutes = ($last_attendance - $session_start) / 60;
        $attendance_per_hour = $active_duration_minutes > 0 ? ($total_attendance / $active_duration_minutes) * 60 : 0;

        return [
            'total_attendance' => $total_attendance,
            'present_count' => $present_count,
            'late_count' => $late_count,
            'attendance_rate' => $total_attendance > 0 ? round(($present_count / $total_attendance) * 100, 1) : 0,
            'gender_distribution' => $gender_stats,
            'department_distribution' => $department_stats,
            'hourly_distribution' => $hourly_distribution,
            'session_duration_minutes' => round($total_duration_minutes, 1),
            'active_duration_minutes' => round($active_duration_minutes, 1),
            'attendance_per_hour' => round($attendance_per_hour, 2),
            'avg_response_time_minutes' => $response_times ? round(array_sum($response_times) / count($response_times), 1) : 0,
            'first_attendance_time' => $attendance_records ? $attendance_records[0]['created_at'] : null,
            'last_attendance_time' => $attendance_records ? end($attendance_records)['created_at'] : null,
            'peak_hour' => (!empty($hourly_distribution) && is_array($hourly_distribution)) ? array_keys($hourly_distribution, max($hourly_distribution))[0] : null
        ];
    }

    /**
     * Calculate comprehensive member engagement metrics
     *
     * @param int $member_id
     * @return array
     */
    public function calculateMemberEngagement($member_id) {
        try {
            // Get member's attendance history (last 6 months)
            $history_query = "SELECT
                                a.attendance_date,
                                a.status,
                                a.created_at,
                                a.marked_via,
                                s.name as service_name,
                                DATEDIFF(NOW(), a.attendance_date) as days_ago
                              FROM attendance a
                              JOIN services s ON a.service_id = s.id
                              WHERE a.member_id = :member_id
                              AND a.attendance_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
                              ORDER BY a.attendance_date DESC";

            $stmt = $this->conn->prepare($history_query);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->execute();
            $history = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate engagement metrics
            $total_sessions = count($history);
            $recent_sessions = array_filter($history, function($h) { return $h['days_ago'] <= 30; });
            $qr_sessions = array_filter($history, function($h) { return $h['marked_via'] === 'qr'; });

            // Calculate engagement score (0-100)
            $engagement_score = $this->calculateEngagementScore($history);

            // Determine attendance frequency
            $frequency = $this->determineAttendanceFrequency($history);

            // Calculate risk level
            $risk_level = $this->calculateRiskLevel($history, $engagement_score);

            // Calculate current streak
            $current_streak = $this->calculateAttendanceStreak($history);

            // Determine trend (improving, stable, declining)
            $trend = $this->calculateAttendanceTrend($history);

            // Generate recommendations
            $recommendations = $this->generateMemberRecommendations($history, $engagement_score, $risk_level, $trend);

            return [
                'engagement_score' => $engagement_score,
                'attendance_frequency' => $frequency,
                'risk_level' => $risk_level,
                'last_attendance' => !empty($history) ? $history[0]['attendance_date'] : null,
                'total_sessions' => $total_sessions,
                'recent_sessions' => count($recent_sessions),
                'qr_sessions' => count($qr_sessions),
                'current_streak' => $current_streak,
                'trend' => $trend,
                'recommendations' => $recommendations,
                'history' => array_slice($history, 0, 10) // Last 10 sessions for timeline
            ];

        } catch (Exception $e) {
            error_log("Error calculating member engagement for member $member_id: " . $e->getMessage());
            return [
                'engagement_score' => 0,
                'attendance_frequency' => 'Unknown',
                'risk_level' => 'Unknown',
                'last_attendance' => null,
                'total_sessions' => 0,
                'recent_sessions' => 0,
                'qr_sessions' => 0,
                'current_streak' => 0,
                'trend' => 'Unknown',
                'recommendations' => [],
                'history' => []
            ];
        }
    }

    /**
     * Calculate engagement score (0-100) based on multiple factors
     *
     * @param array $history
     * @return int
     */
    private function calculateEngagementScore($history) {
        if (empty($history)) return 0;

        $score = 0;
        $total_possible = 100;

        // Factor 1: Recent attendance (40 points)
        $recent_count = count(array_filter($history, function($h) { return $h['days_ago'] <= 30; }));
        $score += min(40, $recent_count * 10); // Up to 4 recent sessions = 40 points

        // Factor 2: Consistency (30 points)
        $weeks_with_attendance = [];
        foreach ($history as $record) {
            $week = date('Y-W', strtotime($record['attendance_date']));
            $weeks_with_attendance[$week] = true;
        }
        $consistency_score = min(30, count($weeks_with_attendance) * 2);
        $score += $consistency_score;

        // Factor 3: QR usage (modern engagement) (20 points)
        $qr_usage = count(array_filter($history, function($h) { return $h['marked_via'] === 'qr'; }));
        $qr_percentage = count($history) > 0 ? ($qr_usage / count($history)) * 100 : 0;
        $score += ($qr_percentage / 100) * 20;

        // Factor 4: Recency bonus (10 points)
        $most_recent_days = !empty($history) ? $history[0]['days_ago'] : 999;
        if ($most_recent_days <= 7) $score += 10;
        elseif ($most_recent_days <= 14) $score += 7;
        elseif ($most_recent_days <= 30) $score += 5;

        return min(100, round($score));
    }

    /**
     * Determine attendance frequency category
     *
     * @param array $history
     * @return string
     */
    private function determineAttendanceFrequency($history) {
        $recent_count = count(array_filter($history, function($h) { return $h['days_ago'] <= 30; }));

        if ($recent_count >= 4) return 'Regular'; // 4+ times in last month
        elseif ($recent_count >= 2) return 'Occasional'; // 2-3 times in last month
        elseif ($recent_count >= 1) return 'Infrequent'; // 1 time in last month
        else return 'Absent'; // No attendance in last month
    }

    /**
     * Calculate risk level for member churn
     *
     * @param array $history
     * @param int $engagement_score
     * @return string
     */
    private function calculateRiskLevel($history, $engagement_score) {
        $most_recent_days = !empty($history) ? $history[0]['days_ago'] : 999;

        // High risk conditions
        if ($engagement_score < 30 || $most_recent_days > 60) {
            return 'High';
        }

        // Medium risk conditions
        if ($engagement_score < 60 || $most_recent_days > 30) {
            return 'Medium';
        }

        // Low risk
        return 'Low';
    }

    /**
     * Calculate current attendance streak
     *
     * @param array $history
     * @return int
     */
    private function calculateAttendanceStreak($history) {
        if (empty($history)) return 0;

        $streak = 0;
        $last_date = null;

        foreach ($history as $record) {
            $current_date = strtotime($record['attendance_date']);

            if ($last_date === null) {
                $streak = 1;
                $last_date = $current_date;
                continue;
            }

            // Check if this attendance is within a reasonable timeframe (within 2 weeks of last)
            $days_diff = ($last_date - $current_date) / (24 * 60 * 60);

            if ($days_diff <= 14) { // Within 2 weeks
                $streak++;
                $last_date = $current_date;
            } else {
                break; // Streak broken
            }
        }

        return $streak;
    }

    /**
     * Calculate attendance trend (improving, stable, declining)
     *
     * @param array $history
     * @return string
     */
    private function calculateAttendanceTrend($history) {
        if (count($history) < 4) return 'Insufficient Data';

        // Compare last 2 months vs previous 2 months
        $recent = array_filter($history, function($h) { return $h['days_ago'] <= 60; });
        $older = array_filter($history, function($h) { return $h['days_ago'] > 60 && $h['days_ago'] <= 120; });

        $recent_count = count($recent);
        $older_count = count($older);

        if ($older_count == 0) return 'New Member';

        $change_ratio = $recent_count / $older_count;

        if ($change_ratio > 1.2) return 'Improving';
        elseif ($change_ratio < 0.8) return 'Declining';
        else return 'Stable';
    }

    /**
     * Generate personalized recommendations for member engagement
     *
     * @param array $history
     * @param int $engagement_score
     * @param string $risk_level
     * @param string $trend
     * @return array
     */
    private function generateMemberRecommendations($history, $engagement_score, $risk_level, $trend) {
        $recommendations = [];

        // Risk-based recommendations
        if ($risk_level === 'High') {
            $recommendations[] = [
                'type' => 'urgent',
                'action' => 'Personal Follow-up',
                'description' => 'Schedule a personal call or visit within 48 hours',
                'priority' => 'High'
            ];
            $recommendations[] = [
                'type' => 'engagement',
                'action' => 'Special Invitation',
                'description' => 'Invite to upcoming special events or small group activities',
                'priority' => 'High'
            ];
        } elseif ($risk_level === 'Medium') {
            $recommendations[] = [
                'type' => 'outreach',
                'action' => 'Friendly Check-in',
                'description' => 'Send a caring message or make a friendly phone call',
                'priority' => 'Medium'
            ];
        }

        // Trend-based recommendations
        if ($trend === 'Declining') {
            $recommendations[] = [
                'type' => 'intervention',
                'action' => 'Understand Barriers',
                'description' => 'Reach out to understand what might be preventing attendance',
                'priority' => 'Medium'
            ];
        } elseif ($trend === 'Improving') {
            $recommendations[] = [
                'type' => 'encouragement',
                'action' => 'Positive Reinforcement',
                'description' => 'Acknowledge their increased participation and encourage continued growth',
                'priority' => 'Low'
            ];
        }

        // Engagement-based recommendations
        if ($engagement_score >= 80) {
            $recommendations[] = [
                'type' => 'leadership',
                'action' => 'Leadership Opportunity',
                'description' => 'Consider inviting to take on ministry or leadership roles',
                'priority' => 'Low'
            ];
        }

        // QR usage recommendations
        $qr_usage = count(array_filter($history, function($h) { return $h['marked_via'] === 'qr'; }));
        $total_sessions = count($history);
        if ($total_sessions > 0 && ($qr_usage / $total_sessions) < 0.3) {
            $recommendations[] = [
                'type' => 'technology',
                'action' => 'QR Code Training',
                'description' => 'Offer help with using QR code attendance system',
                'priority' => 'Low'
            ];
        }

        return $recommendations;
    }

    /**
     * Calculate engagement summary for all members in session
     *
     * @param array $engagement_data
     * @return array
     */
    private function calculateEngagementSummary($engagement_data) {
        if (empty($engagement_data)) {
            return [
                'total_members' => 0,
                'avg_engagement_score' => 0,
                'risk_distribution' => ['High' => 0, 'Medium' => 0, 'Low' => 0],
                'frequency_distribution' => ['Regular' => 0, 'Occasional' => 0, 'Infrequent' => 0, 'Absent' => 0],
                'trend_distribution' => ['Improving' => 0, 'Stable' => 0, 'Declining' => 0],
                'high_performers' => [],
                'at_risk_members' => [],
                'action_needed' => 0
            ];
        }

        $total_members = count($engagement_data);
        $total_score = array_sum(array_column($engagement_data, 'engagement_score'));
        $avg_score = $total_score / $total_members;

        // Risk distribution
        $risk_dist = ['High' => 0, 'Medium' => 0, 'Low' => 0];
        foreach ($engagement_data as $member) {
            $risk_dist[$member['risk_level']]++;
        }

        // Frequency distribution
        $freq_dist = ['Regular' => 0, 'Occasional' => 0, 'Infrequent' => 0, 'Absent' => 0];
        foreach ($engagement_data as $member) {
            $freq_dist[$member['attendance_frequency']]++;
        }

        // Trend distribution
        $trend_dist = ['Improving' => 0, 'Stable' => 0, 'Declining' => 0, 'New Member' => 0, 'Insufficient Data' => 0];
        foreach ($engagement_data as $member) {
            if (isset($trend_dist[$member['trend']])) {
                $trend_dist[$member['trend']]++;
            }
        }

        // High performers (top 20% by engagement score)
        $sorted_by_score = $engagement_data;
        usort($sorted_by_score, function($a, $b) { return $b['engagement_score'] - $a['engagement_score']; });
        $high_performers = array_slice($sorted_by_score, 0, max(1, floor($total_members * 0.2)));

        // At-risk members
        $at_risk = array_filter($engagement_data, function($member) {
            return $member['risk_level'] === 'High' || $member['risk_level'] === 'Medium';
        });

        // Members needing action
        $action_needed = count(array_filter($engagement_data, function($member) {
            return !empty($member['recommendations']);
        }));

        return [
            'total_members' => $total_members,
            'avg_engagement_score' => round($avg_score, 1),
            'risk_distribution' => $risk_dist,
            'frequency_distribution' => $freq_dist,
            'trend_distribution' => $trend_dist,
            'high_performers' => array_slice($high_performers, 0, 5), // Top 5
            'at_risk_members' => array_slice($at_risk, 0, 10), // Top 10 at risk
            'action_needed' => $action_needed
        ];
    }
}