<?php
/**
 * Run Member Groups Unique Constraint Migration
 * 
 * This script adds a unique constraint to the member_groups table
 * to prevent duplicate member-group relationships.
 */

// Include database configuration
require_once dirname(__DIR__, 2) . '/config/database.php';

try {
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Running Member Groups Migration</h2>";
    
    // Read and execute the migration SQL
    $migrationFile = __DIR__ . '/add_member_groups_unique_constraint.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    foreach ($statements as $statement) {
        if (empty($statement)) continue;
        
        try {
            $conn->exec($statement);
            echo "<p style='color: green;'>✓ Executed: " . substr($statement, 0, 50) . "...</p>";
        } catch (PDOException $e) {
            // Check if it's a "constraint already exists" error
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: orange;'>⚠ Constraint already exists: " . substr($statement, 0, 50) . "...</p>";
            } else {
                echo "<p style='color: red;'>✗ Error executing: " . substr($statement, 0, 50) . "...</p>";
                echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h3 style='color: green;'>Migration Completed Successfully!</h3>";
    echo "<p>The member_groups table now has a unique constraint to prevent duplicate member-group relationships.</p>";
    
    // Verify the constraint was added
    $stmt = $conn->query("SHOW INDEX FROM member_groups WHERE Key_name = 'unique_member_group'");
    $constraint = $stmt->fetch();
    
    if ($constraint) {
        echo "<p style='color: green;'>✓ Unique constraint 'unique_member_group' is active</p>";
    } else {
        echo "<p style='color: red;'>✗ Unique constraint was not created</p>";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Migration Failed!</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Member Groups Migration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        p { margin: 5px 0; }
    </style>
</head>
<body>
    <h1>Member Groups Migration Tool</h1>
    <p><a href="<?php echo dirname($_SERVER['PHP_SELF'], 2); ?>/dashboard">← Back to Dashboard</a></p>
</body>
</html>
