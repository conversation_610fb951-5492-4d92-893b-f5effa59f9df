<!-- Dynamic Category Dashboard -->
<div class="fade-in">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800"><?php echo htmlspecialchars($category->label); ?> Dashboard</h1>
            <p class="text-gray-600 text-sm mt-1"><?php echo htmlspecialchars($category->description ?: 'Track and manage ' . strtolower($category->label) . ' payments'); ?></p>
        </div>
        <div class="flex space-x-3">
            <?php
            // Determine the correct tab based on category type
            $tabParam = '';
            if (isset($category->category_type)) {
                if ($category->category_type === 'member_payments') {
                    $tabParam = '?tab=member-payments';
                } elseif ($category->category_type === 'general_income') {
                    $tabParam = '?tab=general-income';
                } elseif ($category->category_type === 'expenses') {
                    $tabParam = '?tab=expenses';
                }
            }
            ?>
            <a href="<?php echo BASE_URL; ?>finance/add<?php echo $tabParam; ?>" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2"></i> Record Payment
            </a>
            <div class="relative">
                <button id="exportBtn" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors duration-200 flex items-center">
                    <i class="fas fa-download mr-2"></i> Export
                </button>
                <div id="exportMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                    <a href="<?php echo BASE_URL; ?>finance/category-dashboard/<?php echo $category->slug; ?>/export?format=csv" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-file-csv mr-2"></i> Export as CSV
                    </a>
                    <a href="<?php echo BASE_URL; ?>finance/category-dashboard/<?php echo $category->slug; ?>/export?format=excel" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-file-excel mr-2"></i> Export as Excel
                    </a>
                </div>
            </div>
            <a href="<?php echo BASE_URL; ?>finance" class="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Finance
            </a>
        </div>
    </div>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Amount -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 mr-4">
                    <i class="fas fa-money-bill-wave text-blue-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Collected</p>
                    <p class="text-2xl font-bold text-gray-900">GH₵ <?php echo number_format($dashboardData['overview']['total_amount'], 2); ?></p>
                </div>
            </div>
        </div>

        <!-- Total Payments -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 mr-4">
                    <i class="fas fa-receipt text-green-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Payments</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($dashboardData['overview']['total_payments']); ?></p>
                </div>
            </div>
        </div>

        <!-- This Month -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 mr-4">
                    <i class="fas fa-calendar-month text-purple-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600">This Month</p>
                    <p class="text-2xl font-bold text-gray-900">GH₵ <?php echo number_format($dashboardData['overview']['month_amount'], 2); ?></p>
                    <?php if ($dashboardData['overview']['growth_percentage'] != 0): ?>
                        <p class="text-xs <?php echo $dashboardData['overview']['growth_percentage'] > 0 ? 'text-green-600' : 'text-red-600'; ?>">
                            <i class="fas fa-arrow-<?php echo $dashboardData['overview']['growth_percentage'] > 0 ? 'up' : 'down'; ?> mr-1"></i>
                            <?php echo abs($dashboardData['overview']['growth_percentage']); ?>% from last month
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Contributors (for member payments) -->
        <?php if ($category->requires_member): ?>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 mr-4">
                    <i class="fas fa-users text-orange-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600">Contributors</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($dashboardData['overview']['unique_contributors']); ?></p>
                </div>
            </div>
        </div>
        <?php else: ?>
        <!-- Average Payment -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 mr-4">
                    <i class="fas fa-chart-line text-orange-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600">Average Payment</p>
                    <p class="text-2xl font-bold text-gray-900">
                        GH₵ <?php echo $dashboardData['overview']['total_payments'] > 0 ? number_format($dashboardData['overview']['total_amount'] / $dashboardData['overview']['total_payments'], 2) : '0.00'; ?>
                    </p>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Charts and Tables Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Monthly Trends Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Monthly Trends (Last 12 Months)</h3>
            <div class="h-64">
                <canvas id="monthlyTrendsChart"></canvas>
            </div>
        </div>

        <!-- Top Contributors (for member payments) -->
        <?php if ($category->requires_member && !empty($dashboardData['topContributors'])): ?>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Top Contributors</h3>
            <div class="space-y-3">
                <?php foreach (array_slice($dashboardData['topContributors'], 0, 5) as $index => $contributor): ?>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                            <?php echo $index + 1; ?>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900"><?php echo htmlspecialchars($contributor->member_name); ?></p>
                            <p class="text-sm text-gray-500"><?php echo $contributor->payment_count; ?> payments</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-bold text-gray-900">GH₵ <?php echo number_format($contributor->total_amount, 2); ?></p>
                        <p class="text-xs text-gray-500">Last: <?php echo date('M j', strtotime($contributor->last_payment_date)); ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php else: ?>
        <!-- Recent Payments -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Payments</h3>
            <div class="space-y-3">
                <?php foreach (array_slice($dashboardData['recentPayments'], 0, 5) as $payment): ?>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                        <p class="font-medium text-gray-900">GH₵ <?php echo number_format($payment->amount, 2); ?></p>
                        <p class="text-sm text-gray-500"><?php echo date('M j, Y', strtotime($payment->transaction_date)); ?></p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600"><?php echo ucfirst($payment->payment_method); ?></p>
                        <?php if ($payment->member_name !== 'Anonymous'): ?>
                            <p class="text-xs text-gray-500"><?php echo htmlspecialchars($payment->member_name); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Yearly Comparison -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Yearly Comparison</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <p class="text-sm font-medium text-gray-600"><?php echo $dashboardData['yearlyComparison']['current_year']['year']; ?> (Current Year)</p>
                <p class="text-2xl font-bold text-blue-600">GH₵ <?php echo number_format($dashboardData['yearlyComparison']['current_year']['total_amount'], 2); ?></p>
                <p class="text-sm text-gray-500"><?php echo number_format($dashboardData['yearlyComparison']['current_year']['payment_count']); ?> payments</p>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <p class="text-sm font-medium text-gray-600"><?php echo $dashboardData['yearlyComparison']['last_year']['year']; ?> (Last Year)</p>
                <p class="text-2xl font-bold text-gray-600">GH₵ <?php echo number_format($dashboardData['yearlyComparison']['last_year']['total_amount'], 2); ?></p>
                <p class="text-sm text-gray-500"><?php echo number_format($dashboardData['yearlyComparison']['last_year']['payment_count']); ?> payments</p>
            </div>
        </div>
    </div>

    <!-- Member Payment History (for member payments) -->
    <?php if ($category->requires_member): ?>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Member Payment History</h3>
            <a href="<?php echo BASE_URL; ?>finance/member-payment-history/<?php echo $category->slug; ?>" class="text-primary hover:text-primary-dark text-sm font-medium">
                View All History →
            </a>
        </div>
        
        <!-- Search -->
        <div class="mb-4">
            <input type="text" id="memberSearch" placeholder="Search by member name or amount..." 
                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
        </div>
        
        <!-- Payment History Table -->
        <div id="memberHistoryContainer">
            <div class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-2"></i>
                <p class="text-gray-500">Loading payment history...</p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Trends Chart
    const monthlyData = <?php echo json_encode($dashboardData['monthlyTrends']); ?>;
    const ctx = document.getElementById('monthlyTrendsChart').getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: monthlyData.map(item => {
                const date = new Date(item.month + '-01');
                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            }),
            datasets: [{
                label: 'Amount (GH₵)',
                data: monthlyData.map(item => parseFloat(item.total_amount)),
                borderColor: '#3B82F6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'GH₵ ' + value.toLocaleString();
                        }
                    }
                }
            },
            elements: {
                point: {
                    radius: 4,
                    hoverRadius: 6
                }
            }
        }
    });

    // Export menu toggle
    const exportBtn = document.getElementById('exportBtn');
    const exportMenu = document.getElementById('exportMenu');

    if (exportBtn && exportMenu) {
        exportBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            exportMenu.classList.toggle('hidden');
        });

        document.addEventListener('click', function() {
            exportMenu.classList.add('hidden');
        });
    }

    <?php if ($category->requires_member): ?>
    // Member payment history
    let currentPage = 1;
    const memberSearch = document.getElementById('memberSearch');
    const historyContainer = document.getElementById('memberHistoryContainer');

    function loadMemberHistory(page = 1, search = '') {
        const url = `<?php echo BASE_URL; ?>finance/category-dashboard/<?php echo $category->slug; ?>/member-history?page=${page}&search=${encodeURIComponent(search)}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                renderMemberHistory(data);
            })
            .catch(error => {
                console.error('Error loading member history:', error);
                historyContainer.innerHTML = '<div class="text-center py-8 text-red-500">Error loading payment history</div>';
            });
    }

    function renderMemberHistory(data) {
        let html = '';

        if (data.records && data.records.length > 0) {
            html += '<div class="overflow-x-auto">';
            html += '<table class="min-w-full divide-y divide-gray-200">';
            html += '<thead class="bg-gray-50">';
            html += '<tr>';
            html += '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>';
            html += '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>';
            html += '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>';
            html += '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody class="bg-white divide-y divide-gray-200">';

            data.records.forEach(record => {
                html += '<tr>';
                html += `<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${record.member_name}</td>`;
                html += `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">GH₵ ${parseFloat(record.amount).toLocaleString()}</td>`;
                html += `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(record.transaction_date).toLocaleDateString()}</td>`;
                html += `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${record.payment_method}</td>`;
                html += '</tr>';
            });

            html += '</tbody>';
            html += '</table>';
            html += '</div>';

            // Pagination
            if (data.pagination.total_pages > 1) {
                html += '<div class="flex justify-between items-center mt-4">';
                html += `<p class="text-sm text-gray-700">Showing ${data.records.length} of ${data.pagination.total_records} results</p>`;
                html += '<div class="flex space-x-2">';

                for (let i = 1; i <= data.pagination.total_pages; i++) {
                    const activeClass = i === data.pagination.current_page ? 'bg-primary text-white' : 'bg-white text-gray-700 hover:bg-gray-50';
                    html += `<button onclick="loadMemberHistory(${i}, memberSearch.value)" class="px-3 py-2 border border-gray-300 rounded-md ${activeClass}">${i}</button>`;
                }

                html += '</div>';
                html += '</div>';
            }
        } else {
            html = '<div class="text-center py-8 text-gray-500">No payment history found</div>';
        }

        historyContainer.innerHTML = html;
    }

    // Search functionality
    let searchTimeout;
    memberSearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            loadMemberHistory(1, this.value);
        }, 500);
    });

    // Load initial data
    loadMemberHistory();
    <?php endif; ?>
});
</script>
