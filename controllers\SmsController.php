<?php
/**
 * SMS Controller
 */

require_once 'models/Sms.php';
require_once 'models/Member.php';
require_once 'models/Setting.php';
require_once 'services/SmsService.php';
require_once 'utils/validation.php';
require_once 'utils/sms_helper.php';
require_once 'helpers/functions.php';

class SmsController {
    private $database;
    private $sms;
    private $member;
    private $smsService;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->sms = new Sms($this->database->getConnection());
        $this->member = new Member($this->database->getConnection());
        $this->smsService = new SmsService($this->database);
    }

    /**
     * Display SMS list and creation form
     *
     * @return void
     */
    public function index() {
        // Check if we're coming from a send operation
        $from_send = isset($_GET['from_send']) ? true : false;

        // Get pagination parameters
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = 20; // Show 20 messages per page
        $offset = ($page - 1) * $limit;

        // Get SMS messages with pagination for better performance
        if ($from_send) {
            // Force a direct query to bypass any caching but with pagination
            $query = "SELECT s.*, u.username as sent_by_name
                      FROM sms_messages s
                      LEFT JOIN users u ON s.sent_by = u.id
                      ORDER BY s.sent_date DESC
                      LIMIT :limit OFFSET :offset";
            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log("FRESH QUERY: Loaded " . count($messages) . " messages from page {$page}");
        } else {
            // Normal operation with pagination
            $stmt = $this->sms->getAllPaginated($limit, $offset);
            $messages = $stmt->fetchAll();
        }

        // Get total count for pagination
        $total_messages = $this->sms->getTotalCount();
        $total_pages = ceil($total_messages / $limit);

        // Get member count with phone numbers (for stats only)
        $member_count = $this->member->getCountWithPhoneNumbers();

        // Get SMS credit balance with caching
        $setting = new Setting($this->database->getConnection());
        $settings = $setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';

        // Check if we have a cached balance and it's less than 15 minutes old
        $cached_balance = isset($_SESSION['sms_balance']) ? $_SESSION['sms_balance'] : null;
        $cached_time = isset($_SESSION['sms_balance_time']) ? $_SESSION['sms_balance_time'] : 0;
        $cache_lifetime = 15 * 60; // 15 minutes in seconds

        if ($cached_balance !== null && (time() - $cached_time < $cache_lifetime)) {
            // Use cached balance
            $sms_balance = $cached_balance;
        } else {
            // Get fresh balance with timeout protection
            try {
                $balance_result = check_sms_balance($api_key);

                if ($balance_result['status']) {
                    $sms_balance = $balance_result['balance'];
                    // Cache the successful result
                    $_SESSION['sms_balance'] = $sms_balance;
                    $_SESSION['sms_balance_time'] = time();
                } else {
                    // API failed, use cached balance if available, otherwise default to 0
                    $sms_balance = $cached_balance !== null ? $cached_balance : 0;
                    error_log("SMS Balance API failed: " . $balance_result['message']);
                }
            } catch (Exception $e) {
                // Exception occurred, use cached balance if available, otherwise default to 0
                $sms_balance = $cached_balance !== null ? $cached_balance : 0;
                error_log("SMS Balance Exception: " . $e->getMessage());
            }
        }

        // Set page title and active page
        $page_title = getPageTitle('SMS Broadcast');
        $active_page = 'sms';

        // Start output buffering
        ob_start();

        // Load view with pagination data
        require_once 'views/sms/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display SMS form
     *
     * @return void
     */
    public function create() {
        // Get member count with phone numbers (for stats only)
        $member_count = $this->member->getCountWithPhoneNumbers();

        // Get SMS credit balance with caching
        $setting = new Setting($this->database->getConnection());
        $settings = $setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';

        // Check if we have a cached balance and it's less than 15 minutes old
        $cached_balance = isset($_SESSION['sms_balance']) ? $_SESSION['sms_balance'] : null;
        $cached_time = isset($_SESSION['sms_balance_time']) ? $_SESSION['sms_balance_time'] : 0;
        $cache_lifetime = 15 * 60; // 15 minutes in seconds

        if ($cached_balance !== null && (time() - $cached_time < $cache_lifetime)) {
            // Use cached balance
            $sms_balance = $cached_balance;
        } else {
            // Get fresh balance with timeout protection
            try {
                $balance_result = check_sms_balance($api_key);

                if ($balance_result['status']) {
                    $sms_balance = $balance_result['balance'];
                    // Cache the successful result
                    $_SESSION['sms_balance'] = $sms_balance;
                    $_SESSION['sms_balance_time'] = time();
                } else {
                    // API failed, use cached balance if available, otherwise default to 0
                    $sms_balance = $cached_balance !== null ? $cached_balance : 0;
                    error_log("SMS Balance API failed: " . $balance_result['message']);
                }
            } catch (Exception $e) {
                // Exception occurred, use cached balance if available, otherwise default to 0
                $sms_balance = $cached_balance !== null ? $cached_balance : 0;
                error_log("SMS Balance Exception: " . $e->getMessage());
            }
        }

        // Initialize all variables
        $specific_phone = null;
        $member_data = null;
        $bulk_recipients = [];
        $default_message = '';
        $preselected_mode = 'none'; // none, single, bulk

        // Get URL parameters
        $phone_param = isset($_GET['phone']) ? sanitize($_GET['phone']) : null;
        $recipient_param = isset($_GET['recipient']) ? sanitize($_GET['recipient']) : null;
        $recipients_param = isset($_GET['recipients']) ? sanitize($_GET['recipients']) : null;
        $template_param = isset($_GET['template']) ? sanitize($_GET['template']) : null;

        // Handle single phone number (visitor follow-up)
        if ($phone_param) {
            $memberObj = $this->member->getByPhoneNumber($phone_param);
            if ($memberObj) {
                $member_data = [
                    'id' => $memberObj->id,
                    'first_name' => $memberObj->first_name,
                    'last_name' => $memberObj->last_name,
                    'phone_number' => $memberObj->phone_number,
                    'email' => $memberObj->email
                ];
                $specific_phone = $memberObj->phone_number;
                $preselected_mode = 'single';
            }
        }
        // Handle single recipient ID
        elseif ($recipient_param) {
            if ($this->member->getById($recipient_param)) {
                $member_data = [
                    'id' => $this->member->id,
                    'first_name' => $this->member->first_name,
                    'last_name' => $this->member->last_name,
                    'phone_number' => $this->member->phone_number,
                    'email' => $this->member->email
                ];
                $specific_phone = $this->member->phone_number;
                $preselected_mode = 'single';
            }
        }
        // Handle multiple recipients (bulk SMS from birthday page)
        elseif ($recipients_param) {
            $recipient_ids = array_filter(array_map('trim', explode(',', $recipients_param)));

            foreach ($recipient_ids as $id) {
                if (is_numeric($id) && $this->member->getById($id)) {
                    $bulk_recipients[] = [
                        'id' => $this->member->id,
                        'first_name' => $this->member->first_name,
                        'last_name' => $this->member->last_name,
                        'phone_number' => $this->member->phone_number,
                        'email' => $this->member->email
                    ];
                }
            }

            if (!empty($bulk_recipients)) {
                $preselected_mode = 'bulk';
            }
        }

        // Get church name for dynamic content
        $church_name = 'ICGC Emmanuel Temple'; // Default fallback
        if ($this->setting !== null) {
            try {
                $church_name = $this->setting->getValue('church_name') ?? 'ICGC Emmanuel Temple';
            } catch (Exception $e) {
                error_log("Error getting church name: " . $e->getMessage());
            }
        }

        // Set default message based on template
        if ($template_param === 'birthday') {
            $default_message = "Dear {first_name}, Happy Birthday! 🎉 May God bless you with many more years of joy, peace, and prosperity. We celebrate you today! - " . $church_name;
        }

        // Set page title and active page
        $page_title = 'Create SMS - ' . $church_name;
        $active_page = 'sms';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/sms/create_fixed.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display SMS messages history
     *
     * @return void
     */
    public function messages() {
        // Get pagination parameters
        $page = isset($_GET['page']) ? (int)sanitize($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? (int)sanitize($_GET['limit']) : 20;

        // Ensure valid pagination values
        $page = max(1, $page); // Minimum page is 1
        $limit = min(100, max(10, $limit)); // Limit between 10 and 100

        // Calculate offset
        $offset = ($page - 1) * $limit;

        // Get paginated SMS messages
        $stmt = $this->sms->getAllPaginated($limit, $offset);
        $messages = $stmt->fetchAll();

        // Get total count for pagination
        $totalCount = $this->sms->getTotalCount();
        $totalPages = ceil($totalCount / $limit);

        // Get SMS credit balance with caching
        $setting = new Setting($this->database->getConnection());
        $settings = $setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';

        // Check if we have a cached balance and it's less than 15 minutes old
        $cached_balance = isset($_SESSION['sms_balance']) ? $_SESSION['sms_balance'] : null;
        $cached_time = isset($_SESSION['sms_balance_time']) ? $_SESSION['sms_balance_time'] : 0;
        $cache_lifetime = 15 * 60; // 15 minutes in seconds

        if ($cached_balance && (time() - $cached_time < $cache_lifetime)) {
            // Use cached balance
            $sms_balance = $cached_balance;
        } else {
            // Get fresh balance
            $balance_result = check_sms_balance($api_key);
            $sms_balance = $balance_result['status'] ? $balance_result['balance'] : 0;

            // Cache the balance
            $_SESSION['sms_balance'] = $sms_balance;
            $_SESSION['sms_balance_time'] = time();
        }

        // Set page title and active page
        $page_title = getPageTitle('SMS Messages');
        $active_page = 'sms';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/sms/messages.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Filter SMS messages
     *
     * @return void
     */
    public function filter() {
        $status = isset($_GET['status']) ? sanitize($_GET['status']) : null;
        $start_date = isset($_GET['start_date']) ? sanitize($_GET['start_date']) : null;
        $end_date = isset($_GET['end_date']) ? sanitize($_GET['end_date']) : null;
        $search = isset($_GET['search']) ? sanitize($_GET['search']) : null;

        // Get pagination parameters
        $page = isset($_GET['page']) ? (int)sanitize($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? (int)sanitize($_GET['limit']) : 20;

        // Ensure valid pagination values
        $page = max(1, $page); // Minimum page is 1
        $limit = min(100, max(10, $limit)); // Limit between 10 and 100

        // Calculate offset
        $offset = ($page - 1) * $limit;

        // Get total count for pagination (do this first for all cases)
        $totalCount = $this->sms->getTotalCount();
        $totalPages = ceil($totalCount / $limit);

        // Get filtered messages
        if ($status) {
            $stmt = $this->sms->getByStatus($status);
            $messages = $stmt->fetchAll();
            // For filtered results, we need to adjust the total count
            $totalCount = count($messages);
            $totalPages = ceil($totalCount / $limit);
            // Apply pagination manually
            $messages = array_slice($messages, $offset, $limit);
        } elseif ($start_date && $end_date) {
            $stmt = $this->sms->getByDateRange($start_date, $end_date);
            $messages = $stmt->fetchAll();
            // For filtered results, we need to adjust the total count
            $totalCount = count($messages);
            $totalPages = ceil($totalCount / $limit);
            // Apply pagination manually
            $messages = array_slice($messages, $offset, $limit);
        } else {
            // Use paginated query for unfiltered results
            $stmt = $this->sms->getAllPaginated($limit, $offset);
            $messages = $stmt->fetchAll();
        }

        // Filter by search term if provided
        if ($search) {
            $messages = array_filter($messages, function($message) use ($search) {
                return stripos($message['message'], $search) !== false;
            });

            // When filtering by search, we need to recalculate pagination
            $totalCount = count($messages);
            $totalPages = ceil($totalCount / $limit);

            // Apply pagination manually after filtering
            $messages = array_slice($messages, $offset, $limit);
        }

        // Get SMS credit balance with caching
        $setting = new Setting($this->database->getConnection());
        $settings = $setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';

        // Check if we have a cached balance and it's less than 15 minutes old
        $cached_balance = isset($_SESSION['sms_balance']) ? $_SESSION['sms_balance'] : null;
        $cached_time = isset($_SESSION['sms_balance_time']) ? $_SESSION['sms_balance_time'] : 0;
        $cache_lifetime = 15 * 60; // 15 minutes in seconds

        if ($cached_balance && (time() - $cached_time < $cache_lifetime)) {
            // Use cached balance
            $sms_balance = $cached_balance;
        } else {
            // Get fresh balance
            $balance_result = check_sms_balance($api_key);
            $sms_balance = $balance_result['status'] ? $balance_result['balance'] : 0;

            // Cache the balance
            $_SESSION['sms_balance'] = $sms_balance;
            $_SESSION['sms_balance_time'] = time();
        }

        // Set page title and active page
        $page_title = getPageTitle('SMS Messages');
        $active_page = 'sms';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/sms/messages.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Send SMS using SmsService (Simplified with Service Layer)
     *
     * @return void
     */
    public function send() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // All business logic is now in the service. The controller just orchestrates.
                $result = $this->smsService->sendBulkSms(
                    $_POST['message'],
                    $_POST,
                    $_SESSION['user_id'] ?? 1 // Fallback user ID
                );

                if ($result['success']) {
                    $messageType = isset($result['type']) ? $result['type'] : 'success';
                    set_flash_message($result['message'], $messageType);
                    redirect('sms?from_send=1');
                } else {
                    set_flash_message($result['message'], 'danger');
                    $_SESSION['form_data'] = $_POST;
                    redirect('sms/create');
                }
            } catch (Exception $e) {
                error_log("SMS Send Exception: " . $e->getMessage());
                set_flash_message('An error occurred while sending SMS: ' . $e->getMessage(), 'danger');
                $_SESSION['form_data'] = $_POST;
                redirect('sms/create');
            }
        } else {
            // Not a POST request, redirect to SMS form
            redirect('sms/create');
        }
    }



    /**
     * View SMS message details
     *
     * @return void
     */
    public function view() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Invalid SMS message ID', 'danger');
            redirect('sms/messages');
            return;
        }

        // Get SMS message by ID
        $id = $_GET['id'];
        $message = $this->sms->getById($id);

        if (!$message) {
            set_flash_message('SMS message not found', 'danger');
            redirect('sms/messages');
            return;
        }

        // Get sender information if available
        if (!empty($message['sent_by'])) {
            require_once 'models/User.php';
            $user = new User($this->database->getConnection());
            $sender = $user->getById($message['sent_by']);
            $message['sender'] = $sender;
        }

        // Parse recipients
        $recipients = explode(',', $message['recipients']);
        $recipient_details = [];

        foreach ($recipients as $recipient) {
            // Check if recipient is a member ID or phone number
            if (is_numeric($recipient) && strlen($recipient) >= 9) {
                // It's a phone number
                $recipient_details[] = [
                    'type' => 'phone',
                    'value' => $recipient,
                    'display' => $recipient
                ];
            } else {
                // Try to get member details
                $memberObj = new Member($this->database->getConnection());
                if ($memberObj->getById($recipient)) {
                    $recipient_details[] = [
                        'type' => 'member',
                        'value' => $recipient,
                        'display' => $memberObj->first_name . ' ' . $memberObj->last_name,
                        'phone' => $memberObj->phone_number,
                        'member' => [
                            'id' => $memberObj->id,
                            'first_name' => $memberObj->first_name,
                            'last_name' => $memberObj->last_name,
                            'phone_number' => $memberObj->phone_number,
                            'email' => $memberObj->email
                        ]
                    ];
                } else {
                    // Unknown recipient
                    $recipient_details[] = [
                        'type' => 'unknown',
                        'value' => $recipient,
                        'display' => $recipient
                    ];
                }
            }
        }

        // Set page title and active page
        $page_title = getPageTitle('SMS Details');
        $active_page = 'sms';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/sms/view.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Delete SMS message
     *
     * @return void
     */
    public function delete() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Invalid SMS message ID', 'danger');
            redirect('sms/messages');
            return;
        }

        // Get SMS message by ID
        $id = $_GET['id'];
        $message = $this->sms->getById($id);

        if (!$message) {
            set_flash_message('SMS message not found', 'danger');
            redirect('sms/messages');
            return;
        }

        // Delete the SMS message
        if ($this->sms->delete($id)) {
            set_flash_message('SMS message deleted successfully', 'success');
        } else {
            set_flash_message('Failed to delete SMS message', 'danger');
        }

        // Redirect to SMS messages page
        redirect('sms/messages');
    }
}
