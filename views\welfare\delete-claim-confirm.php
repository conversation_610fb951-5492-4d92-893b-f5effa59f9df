<?php
$page_title = "Delete Claim Confirmation";
$active_page = "welfare";
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Delete Claim Confirmation</h1>
            <p class="text-gray-600 mt-1">Are you sure you want to delete this claim?</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>welfare"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Welfare
            </a>
        </div>
    </div>

    <!-- Confirmation Card -->
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200 bg-red-50">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-lg font-semibold text-red-900">Confirm Claim Deletion</h2>
                        <p class="text-sm text-red-700">This action cannot be undone.</p>
                    </div>
                </div>
            </div>
            
            <div class="p-6">
                <!-- Claim Details -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Claim Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Member Name</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo htmlspecialchars($claim['first_name'] . ' ' . $claim['last_name']); ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Claim Amount</label>
                            <p class="mt-1 text-sm text-gray-900 font-semibold">
                                ₵<?php echo number_format($claim['claim_amount'], 2); ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Claim Date</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo date('F d, Y', strtotime($claim['claim_date'])); ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <p class="mt-1">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    <?php
                                    switch($claim['status']) {
                                        case 'disbursed': echo 'bg-green-100 text-green-800'; break;
                                        case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                        case 'approved': echo 'bg-blue-100 text-blue-800'; break;
                                        case 'rejected': echo 'bg-red-100 text-red-800'; break;
                                        default: echo 'bg-gray-100 text-gray-800';
                                    }
                                    ?>">
                                    <?php echo ucfirst($claim['status']); ?>
                                </span>
                            </p>
                        </div>
                        <?php if (!empty($claim['claim_reason'])): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Claim Reason</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo htmlspecialchars($claim['claim_reason']); ?>
                            </p>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($claim['notes'])): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Notes</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo htmlspecialchars($claim['notes']); ?>
                            </p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Warning Message -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>Deleting this claim will:</p>
                                <ul class="list-disc list-inside mt-1">
                                    <li>Permanently remove the claim record</li>
                                    <li>Update the member's claim history</li>
                                    <li>Affect welfare statistics and reports</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3">
                    <a href="<?php echo BASE_URL; ?>welfare"
                       class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                        Cancel
                    </a>
                    <form method="POST" class="inline">
                        <input type="hidden" name="claim_id" value="<?php echo $claim['id']; ?>">
                        <button type="submit"
                                class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                                onclick="return confirm('Are you absolutely sure you want to delete this claim? This action cannot be undone.')">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Claim
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
