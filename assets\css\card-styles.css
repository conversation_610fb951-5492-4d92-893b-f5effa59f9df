/* 
 * IC<PERSON> Card Styles
 * This CSS applies the SMS card styles to all cards in the application
 */

/* Base card styles */
.card {
    @apply rounded-lg p-4 shadow-sm;
}

/* Primary card style (green) */
.card-primary {
    @apply bg-green-50 border-l-4 border-green-500;
}
.card-primary .card-icon {
    @apply rounded-full bg-green-100 p-3 mr-4;
}
.card-primary .card-icon i {
    @apply text-green-500 text-xl;
}
.card-primary .card-title {
    @apply text-sm text-green-500 font-medium;
}
.card-primary .card-value {
    @apply text-2xl font-bold text-gray-800;
}

/* Blue card style */
.card-blue {
    @apply bg-blue-50 border-l-4 border-blue-500;
}
.card-blue .card-icon {
    @apply rounded-full bg-blue-100 p-3 mr-4;
}
.card-blue .card-icon i {
    @apply text-blue-500 text-xl;
}
.card-blue .card-title {
    @apply text-sm text-blue-500 font-medium;
}
.card-blue .card-value {
    @apply text-2xl font-bold text-gray-800;
}

/* Yellow card style */
.card-yellow {
    @apply bg-yellow-50 border-l-4 border-yellow-500;
}
.card-yellow .card-icon {
    @apply rounded-full bg-yellow-100 p-3 mr-4;
}
.card-yellow .card-icon i {
    @apply text-yellow-500 text-xl;
}
.card-yellow .card-title {
    @apply text-sm text-yellow-500 font-medium;
}
.card-yellow .card-value {
    @apply text-2xl font-bold text-gray-800;
}

/* Orange card style */
.card-orange {
    @apply bg-orange-50 border-l-4 border-orange-500;
}
.card-orange .card-icon {
    @apply rounded-full bg-orange-100 p-3 mr-4;
}
.card-orange .card-icon i {
    @apply text-orange-500 text-xl;
}
.card-orange .card-title {
    @apply text-sm text-orange-500 font-medium;
}
.card-orange .card-value {
    @apply text-2xl font-bold text-gray-800;
}

/* Red card style */
.card-red {
    @apply bg-red-50 border-l-4 border-red-500;
}
.card-red .card-icon {
    @apply rounded-full bg-red-100 p-3 mr-4;
}
.card-red .card-icon i {
    @apply text-red-500 text-xl;
}
.card-red .card-title {
    @apply text-sm text-red-500 font-medium;
}
.card-red .card-value {
    @apply text-2xl font-bold text-gray-800;
}

/* Purple card style */
.card-purple {
    @apply bg-purple-50 border-l-4 border-purple-500;
}
.card-purple .card-icon {
    @apply rounded-full bg-purple-100 p-3 mr-4;
}
.card-purple .card-icon i {
    @apply text-purple-500 text-xl;
}
.card-purple .card-title {
    @apply text-sm text-purple-500 font-medium;
}
.card-purple .card-value {
    @apply text-2xl font-bold text-gray-800;
}

/* Indigo card style */
.card-indigo {
    @apply bg-indigo-50 border-l-4 border-indigo-500;
}
.card-indigo .card-icon {
    @apply rounded-full bg-indigo-100 p-3 mr-4;
}
.card-indigo .card-icon i {
    @apply text-indigo-500 text-xl;
}
.card-indigo .card-title {
    @apply text-sm text-indigo-500 font-medium;
}
.card-indigo .card-value {
    @apply text-2xl font-bold text-gray-800;
}

/* Apply SMS card styles to existing dashboard cards */
.dashboard-card {
    @apply rounded-lg p-4 shadow-sm border-l-4;
}

/* Convert existing dashboard cards to use SMS card styles */
.bg-white.rounded-lg.shadow-md.p-6.border-t-4.border-primary {
    @apply bg-green-50 border-t-0 border-l-4 border-green-500;
}
.bg-white.rounded-lg.shadow-md.p-6.border-t-4.border-blue-500 {
    @apply bg-blue-50 border-t-0 border-l-4 border-blue-500;
}
.bg-white.rounded-lg.shadow-md.p-6.border-t-4.border-green-500 {
    @apply bg-green-50 border-t-0 border-l-4 border-green-500;
}
.bg-white.rounded-lg.shadow-md.p-6.border-t-4.border-yellow-500 {
    @apply bg-yellow-50 border-t-0 border-l-4 border-yellow-500;
}
.bg-white.rounded-lg.shadow-md.p-6.border-t-4.border-red-500 {
    @apply bg-red-50 border-t-0 border-l-4 border-red-500;
}
.bg-white.rounded-lg.shadow-md.p-6.border-t-4.border-purple-500 {
    @apply bg-purple-50 border-t-0 border-l-4 border-purple-500;
}

/* Convert icon backgrounds */
.p-3.rounded-full.bg-primary-light.text-white {
    @apply bg-green-100 text-green-500;
}
.p-3.rounded-full.bg-blue-500.text-white {
    @apply bg-blue-100 text-blue-500;
}
.p-3.rounded-full.bg-green-500.text-white {
    @apply bg-green-100 text-green-500;
}
.p-3.rounded-full.bg-yellow-500.text-white {
    @apply bg-yellow-100 text-yellow-500;
}
.p-3.rounded-full.bg-red-500.text-white {
    @apply bg-red-100 text-red-500;
}
.p-3.rounded-full.bg-purple-500.text-white {
    @apply bg-purple-100 text-purple-500;
}

/* Standard card styles for all other cards */
.card, .dashboard-card, .stats-card {
    @apply rounded-lg shadow-sm;
}

/* Apply colored borders to all cards */
.card:not([class*="border-l-"]), 
.dashboard-card:not([class*="border-l-"]), 
.stats-card:not([class*="border-l-"]) {
    @apply border-l-4 border-green-500 bg-green-50;
}

/* Table cards */
.card-table {
    @apply rounded-lg shadow-sm overflow-hidden;
}
.card-table .card-header {
    @apply bg-green-50 p-4 border-b border-gray-200;
}
.card-table .card-title {
    @apply text-lg font-semibold text-gray-800;
}
.card-table .card-body {
    @apply p-0;
}

/* Form cards */
.card-form {
    @apply rounded-lg shadow-sm overflow-hidden bg-white border-l-4 border-green-500;
}
.card-form .card-header {
    @apply bg-green-50 p-4 border-b border-gray-200;
}
.card-form .card-title {
    @apply text-lg font-semibold text-gray-800;
}
.card-form .card-body {
    @apply p-6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card, .dashboard-card, .stats-card {
        @apply p-4;
    }
    .card-value {
        @apply text-xl;
    }
}
