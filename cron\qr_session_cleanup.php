<?php
/**
 * QR Session Cleanup Cron Job
 * 
 * This script should be run daily to:
 * 1. Mark expired sessions as 'expired'
 * 2. Archive old sessions (older than 90 days)
 * 3. Clean up orphaned analytics data
 * 
 * Add to crontab: 0 2 * * * /usr/bin/php /path/to/icgc/cron/qr_session_cleanup.php
 */

// Include necessary files
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/models/AttendanceQrSession.php';

// Log function
function log_message($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
    
    // Also log to file
    $log_file = dirname(__DIR__) . '/logs/qr_cleanup.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

try {
    log_message("Starting QR session cleanup...");
    
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    $qr_session = new AttendanceQrSession($conn);
    
    // 1. Close expired sessions
    log_message("Step 1: Closing expired sessions...");
    
    $expired_query = "UPDATE attendance_qr_sessions 
                      SET status = 'expired' 
                      WHERE status = 'active' 
                      AND expires_at <= NOW()";
    
    $stmt = $conn->prepare($expired_query);
    $stmt->execute();
    $expired_count = $stmt->rowCount();
    
    log_message("Marked $expired_count sessions as expired");
    
    // 2. Archive old sessions (older than 90 days)
    log_message("Step 2: Archiving old sessions...");
    
    $archive_date = date('Y-m-d', strtotime('-90 days'));
    $archive_query = "UPDATE attendance_qr_sessions 
                      SET status = 'archived' 
                      WHERE attendance_date < :archive_date 
                      AND status IN ('expired', 'closed')";
    
    $stmt = $conn->prepare($archive_query);
    $stmt->bindParam(':archive_date', $archive_date);
    $stmt->execute();
    $archived_count = $stmt->rowCount();
    
    log_message("Archived $archived_count old sessions (older than $archive_date)");
    
    // 3. Update attendance counts for recent sessions
    log_message("Step 3: Updating attendance counts...");
    
    $update_counts_query = "UPDATE attendance_qr_sessions qs
                           SET attendance_count = (
                               SELECT COUNT(DISTINCT a.member_id)
                               FROM attendance a
                               WHERE a.qr_session_id = qs.id
                           ),
                           last_used_at = (
                               SELECT MAX(a.created_at)
                               FROM attendance a
                               WHERE a.qr_session_id = qs.id
                           )
                           WHERE qs.status != 'archived'";
    
    $stmt = $conn->prepare($update_counts_query);
    $stmt->execute();
    $updated_count = $stmt->rowCount();
    
    log_message("Updated attendance counts for $updated_count sessions");
    
    // 4. Clean up orphaned analytics data
    log_message("Step 4: Cleaning up orphaned analytics...");
    
    $cleanup_analytics_query = "DELETE qa FROM qr_session_analytics qa
                               LEFT JOIN attendance_qr_sessions qs ON qa.qr_session_id = qs.id
                               WHERE qs.id IS NULL";
    
    $stmt = $conn->prepare($cleanup_analytics_query);
    $stmt->execute();
    $cleaned_analytics = $stmt->rowCount();
    
    log_message("Cleaned up $cleaned_analytics orphaned analytics records");
    
    // 5. Generate cleanup summary
    log_message("Step 5: Generating cleanup summary...");
    
    // Get current session statistics
    $stats_query = "SELECT 
                        status,
                        COUNT(*) as count
                    FROM attendance_qr_sessions 
                    GROUP BY status";
    
    $stmt = $conn->prepare($stats_query);
    $stmt->execute();
    $status_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Current session status summary:");
    foreach ($status_counts as $stat) {
        log_message("  - {$stat['status']}: {$stat['count']} sessions");
    }
    
    // Get sessions that need attention
    $attention_query = "SELECT COUNT(*) as count
                       FROM attendance_qr_sessions 
                       WHERE status = 'active' 
                       AND expires_at <= DATE_ADD(NOW(), INTERVAL 1 HOUR)";
    
    $stmt = $conn->prepare($attention_query);
    $stmt->execute();
    $expiring_soon = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($expiring_soon > 0) {
        log_message("WARNING: $expiring_soon sessions will expire within the next hour");
    }
    
    // 6. Data Growth Management
    log_message("Step 6: Managing data growth...");

    // Check database size and implement growth management strategies
    $data_stats = checkDataGrowth($conn);
    log_message("Database stats: " . json_encode($data_stats));

    // Auto-archive based on data volume
    if ($data_stats['total_sessions'] > 1000) {
        log_message("High session count detected, implementing aggressive archiving...");

        // Archive sessions older than 60 days instead of 90
        $aggressive_archive_date = date('Y-m-d', strtotime('-60 days'));
        $aggressive_archive_query = "UPDATE attendance_qr_sessions
                                    SET status = 'archived'
                                    WHERE attendance_date < :archive_date
                                    AND status IN ('expired', 'closed')";

        $stmt = $conn->prepare($aggressive_archive_query);
        $stmt->bindParam(':archive_date', $aggressive_archive_date);
        $stmt->execute();
        $aggressive_archived = $stmt->rowCount();

        log_message("Aggressively archived $aggressive_archived additional sessions");
    }

    // Optional: Delete very old archived sessions (older than 2 years)
    $delete_old = false; // Set to true if you want to permanently delete old sessions

    if ($delete_old || $data_stats['total_sessions'] > 5000) {
        log_message("Step 6b: Deleting very old archived sessions...");

        $delete_date = date('Y-m-d', strtotime('-2 years'));

        // Create backup before deletion if needed
        if ($data_stats['total_sessions'] > 5000) {
            log_message("Creating backup of old data before deletion...");
            createDataBackup($conn, $delete_date);
        }

        // First, delete related attendance records
        $delete_attendance_query = "DELETE a FROM attendance a
                                   JOIN attendance_qr_sessions qs ON a.qr_session_id = qs.id
                                   WHERE qs.attendance_date < :delete_date
                                   AND qs.status = 'archived'";

        $stmt = $conn->prepare($delete_attendance_query);
        $stmt->bindParam(':delete_date', $delete_date);
        $stmt->execute();
        $deleted_attendance = $stmt->rowCount();

        // Then delete the sessions
        $delete_sessions_query = "DELETE FROM attendance_qr_sessions
                                 WHERE attendance_date < :delete_date
                                 AND status = 'archived'";

        $stmt = $conn->prepare($delete_sessions_query);
        $stmt->bindParam(':delete_date', $delete_date);
        $stmt->execute();
        $deleted_sessions = $stmt->rowCount();

        log_message("Permanently deleted $deleted_sessions archived sessions and $deleted_attendance attendance records (older than $delete_date)");
    }
    
    log_message("QR session cleanup completed successfully");
    
    // Summary
    $summary = [
        'expired_sessions' => $expired_count,
        'archived_sessions' => $archived_count,
        'updated_counts' => $updated_count,
        'cleaned_analytics' => $cleaned_analytics,
        'expiring_soon' => $expiring_soon
    ];
    
    log_message("Cleanup summary: " . json_encode($summary));
    
} catch (Exception $e) {
    log_message("ERROR: QR session cleanup failed - " . $e->getMessage());
    log_message("Stack trace: " . $e->getTraceAsString());
    exit(1);
}

/**
 * Check data growth and return statistics
 */
function checkDataGrowth($conn) {
    try {
        $stats_query = "SELECT
                          COUNT(*) as total_sessions,
                          SUM(attendance_count) as total_attendance_records,
                          COUNT(CASE WHEN status = 'archived' THEN 1 END) as archived_sessions,
                          COUNT(CASE WHEN attendance_date >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_sessions,
                          AVG(attendance_count) as avg_attendance_per_session,
                          MAX(attendance_date) as latest_session_date,
                          MIN(attendance_date) as oldest_session_date
                        FROM attendance_qr_sessions";

        $stmt = $conn->prepare($stats_query);
        $stmt->execute();
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        // Calculate data age in days
        $oldest_date = strtotime($stats['oldest_session_date']);
        $data_age_days = $oldest_date ? (time() - $oldest_date) / (24 * 60 * 60) : 0;
        $stats['data_age_days'] = round($data_age_days);

        // Estimate storage usage
        $estimated_storage_mb = ($stats['total_sessions'] * 0.5) + ($stats['total_attendance_records'] * 0.1);
        $stats['estimated_storage_mb'] = round($estimated_storage_mb, 2);

        return $stats;

    } catch (Exception $e) {
        log_message("Error checking data growth: " . $e->getMessage());
        return [
            'total_sessions' => 0,
            'total_attendance_records' => 0,
            'archived_sessions' => 0,
            'recent_sessions' => 0,
            'avg_attendance_per_session' => 0,
            'data_age_days' => 0,
            'estimated_storage_mb' => 0
        ];
    }
}

/**
 * Create backup of old data before deletion
 */
function createDataBackup($conn, $cutoff_date) {
    try {
        $backup_dir = dirname(__DIR__) . '/backups/qr_data';
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }

        $backup_file = $backup_dir . '/qr_backup_' . date('Y-m-d_H-i-s') . '.sql';

        // Export old sessions and attendance data
        $export_query = "SELECT
                           qs.*,
                           s.name as service_name,
                           GROUP_CONCAT(
                               CONCAT(m.first_name, ' ', m.last_name, '|', a.status, '|', a.created_at)
                               SEPARATOR ';'
                           ) as attendance_data
                         FROM attendance_qr_sessions qs
                         LEFT JOIN services s ON qs.service_id = s.id
                         LEFT JOIN attendance a ON qs.id = a.qr_session_id
                         LEFT JOIN members m ON a.member_id = m.id
                         WHERE qs.attendance_date < :cutoff_date
                         AND qs.status = 'archived'
                         GROUP BY qs.id";

        $stmt = $conn->prepare($export_query);
        $stmt->bindParam(':cutoff_date', $cutoff_date);
        $stmt->execute();
        $backup_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Write backup file
        $backup_content = "-- QR Session Backup created on " . date('Y-m-d H:i:s') . "\n";
        $backup_content .= "-- Sessions older than $cutoff_date\n\n";
        $backup_content .= json_encode($backup_data, JSON_PRETTY_PRINT);

        file_put_contents($backup_file, $backup_content);
        log_message("Backup created: $backup_file (" . count($backup_data) . " sessions)");

        return $backup_file;

    } catch (Exception $e) {
        log_message("Error creating backup: " . $e->getMessage());
        return false;
    }
}

log_message("QR session cleanup script finished");
?>
