<?php
/**
 * Query Builder Utility
 * 
 * Provides a fluent interface for building SQL queries safely
 */

class QueryBuilder {
    protected $pdo;
    protected $table;
    protected $select = ['*'];
    protected $where = [];
    protected $orderBy = [];
    protected $groupBy = [];
    protected $joins = [];
    protected $limit = null;
    protected $offset = null;
    protected $params = [];
    protected $distinct = false;

    /**
     * Constructor
     * 
     * @param PDO $pdo PDO database connection
     */
    public function __construct($pdo = null) {
        $this->pdo = $pdo ?: db_connect();
    }

    /**
     * Set the table to query
     * 
     * @param string $table Table name
     * @return QueryBuilder
     */
    public function table($table) {
        $this->table = $table;
        return $this;
    }

    /**
     * Set the columns to select
     * 
     * @param array|string $columns Columns to select
     * @return QueryBuilder
     */
    public function select($columns = ['*']) {
        $this->select = is_array($columns) ? $columns : func_get_args();
        return $this;
    }

    /**
     * Make the query use DISTINCT
     * 
     * @return QueryBuilder
     */
    public function distinct() {
        $this->distinct = true;
        return $this;
    }

    /**
     * Add a WHERE clause
     * 
     * @param string $column Column name
     * @param string $operator Comparison operator
     * @param mixed $value Value to compare against
     * @param string $logic Logic operator (AND/OR)
     * @return QueryBuilder
     */
    public function where($column, $operator = '=', $value = null, $logic = 'AND') {
        // Handle case where operator is actually the value
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }
        
        $this->where[] = [
            'column' => $column,
            'operator' => $operator,
            'value' => $value,
            'logic' => $logic
        ];
        
        $this->params[] = $value;
        
        return $this;
    }

    /**
     * Add an OR WHERE clause
     * 
     * @param string $column Column name
     * @param string $operator Comparison operator
     * @param mixed $value Value to compare against
     * @return QueryBuilder
     */
    public function orWhere($column, $operator = '=', $value = null) {
        // Handle case where operator is actually the value
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }
        
        return $this->where($column, $operator, $value, 'OR');
    }

    /**
     * Add a WHERE IN clause
     * 
     * @param string $column Column name
     * @param array $values Values to check against
     * @param string $logic Logic operator (AND/OR)
     * @return QueryBuilder
     */
    public function whereIn($column, array $values, $logic = 'AND') {
        $placeholders = rtrim(str_repeat('?,', count($values)), ',');
        
        $this->where[] = [
            'column' => $column,
            'operator' => 'IN',
            'value' => "($placeholders)",
            'logic' => $logic,
            'raw' => true
        ];
        
        foreach ($values as $value) {
            $this->params[] = $value;
        }
        
        return $this;
    }

    /**
     * Add an OR WHERE IN clause
     * 
     * @param string $column Column name
     * @param array $values Values to check against
     * @return QueryBuilder
     */
    public function orWhereIn($column, array $values) {
        return $this->whereIn($column, $values, 'OR');
    }

    /**
     * Add a JOIN clause
     * 
     * @param string $table Table to join
     * @param string $first First column
     * @param string $operator Comparison operator
     * @param string $second Second column
     * @param string $type Join type
     * @return QueryBuilder
     */
    public function join($table, $first, $operator, $second, $type = 'INNER') {
        $this->joins[] = [
            'table' => $table,
            'first' => $first,
            'operator' => $operator,
            'second' => $second,
            'type' => $type
        ];
        
        return $this;
    }

    /**
     * Add a LEFT JOIN clause
     * 
     * @param string $table Table to join
     * @param string $first First column
     * @param string $operator Comparison operator
     * @param string $second Second column
     * @return QueryBuilder
     */
    public function leftJoin($table, $first, $operator, $second) {
        return $this->join($table, $first, $operator, $second, 'LEFT');
    }

    /**
     * Add a RIGHT JOIN clause
     * 
     * @param string $table Table to join
     * @param string $first First column
     * @param string $operator Comparison operator
     * @param string $second Second column
     * @return QueryBuilder
     */
    public function rightJoin($table, $first, $operator, $second) {
        return $this->join($table, $first, $operator, $second, 'RIGHT');
    }

    /**
     * Add an ORDER BY clause
     * 
     * @param string $column Column to order by
     * @param string $direction Sort direction (ASC/DESC)
     * @return QueryBuilder
     */
    public function orderBy($column, $direction = 'ASC') {
        $this->orderBy[] = [
            'column' => $column,
            'direction' => strtoupper($direction)
        ];
        
        return $this;
    }

    /**
     * Add a GROUP BY clause
     * 
     * @param string $column Column to group by
     * @return QueryBuilder
     */
    public function groupBy($column) {
        $this->groupBy[] = $column;
        
        return $this;
    }

    /**
     * Set the LIMIT clause
     * 
     * @param int $limit Maximum number of rows to return
     * @return QueryBuilder
     */
    public function limit($limit) {
        $this->limit = (int) $limit;
        
        return $this;
    }

    /**
     * Set the OFFSET clause
     * 
     * @param int $offset Number of rows to skip
     * @return QueryBuilder
     */
    public function offset($offset) {
        $this->offset = (int) $offset;
        
        return $this;
    }

    /**
     * Set both LIMIT and OFFSET for pagination
     * 
     * @param int $page Page number
     * @param int $perPage Items per page
     * @return QueryBuilder
     */
    public function paginate($page, $perPage = 20) {
        $page = max(1, (int) $page);
        $this->limit = (int) $perPage;
        $this->offset = ($page - 1) * $perPage;
        
        return $this;
    }

    /**
     * Build the SQL query string
     * 
     * @return string SQL query
     */
    public function buildQuery() {
        if (empty($this->table)) {
            throw new Exception('No table specified for the query');
        }
        
        $sql = $this->distinct ? 'SELECT DISTINCT ' : 'SELECT ';
        $sql .= implode(', ', $this->select);
        $sql .= ' FROM ' . $this->table;
        
        // Add joins
        if (!empty($this->joins)) {
            foreach ($this->joins as $join) {
                $sql .= ' ' . $join['type'] . ' JOIN ' . $join['table'];
                $sql .= ' ON ' . $join['first'] . ' ' . $join['operator'] . ' ' . $join['second'];
            }
        }
        
        // Add where clauses
        if (!empty($this->where)) {
            $sql .= ' WHERE ';
            $first = true;
            
            foreach ($this->where as $where) {
                if (!$first) {
                    $sql .= ' ' . $where['logic'] . ' ';
                }
                
                if (isset($where['raw']) && $where['raw']) {
                    $sql .= $where['column'] . ' ' . $where['operator'] . ' ' . $where['value'];
                } else {
                    $sql .= $where['column'] . ' ' . $where['operator'] . ' ?';
                }
                
                $first = false;
            }
        }
        
        // Add group by
        if (!empty($this->groupBy)) {
            $sql .= ' GROUP BY ' . implode(', ', $this->groupBy);
        }
        
        // Add order by
        if (!empty($this->orderBy)) {
            $sql .= ' ORDER BY ';
            $firstOrder = true;
            
            foreach ($this->orderBy as $order) {
                if (!$firstOrder) {
                    $sql .= ', ';
                }
                
                $sql .= $order['column'] . ' ' . $order['direction'];
                $firstOrder = false;
            }
        }
        
        // Add limit and offset
        if ($this->limit !== null) {
            $sql .= ' LIMIT ' . $this->limit;
            
            if ($this->offset !== null) {
                $sql .= ' OFFSET ' . $this->offset;
            }
        }
        
        return $sql;
    }

    /**
     * Execute the query and get results
     * 
     * @return array Query results
     */
    public function get() {
        $sql = $this->buildQuery();
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($this->params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Execute the query and get the first result
     * 
     * @return array|null First result row or null if none
     */
    public function first() {
        $this->limit(1);
        $results = $this->get();
        
        return !empty($results) ? $results[0] : null;
    }

    /**
     * Count the number of rows that would be returned
     * 
     * @param string $column Column to count (default *)
     * @return int Number of rows
     */
    public function count($column = '*') {
        $this->select = ["COUNT($column) as count"];
        $result = $this->first();
        
        return (int) $result['count'];
    }

    /**
     * Insert a new row
     * 
     * @param array $data Data to insert
     * @return int|bool Last insert ID or false on failure
     */
    public function insert(array $data) {
        $columns = array_keys($data);
        $placeholders = rtrim(str_repeat('?,', count($columns)), ',');
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES ($placeholders)";
        $stmt = $this->pdo->prepare($sql);
        $result = $stmt->execute(array_values($data));
        
        return $result ? $this->pdo->lastInsertId() : false;
    }

    /**
     * Update rows
     * 
     * @param array $data Data to update
     * @return int Number of affected rows
     */
    public function update(array $data) {
        $sets = [];
        $values = [];
        
        foreach ($data as $column => $value) {
            $sets[] = "$column = ?";
            $values[] = $value;
        }
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $sets);
        
        // Add where clauses
        if (!empty($this->where)) {
            $sql .= ' WHERE ';
            $first = true;
            
            foreach ($this->where as $where) {
                if (!$first) {
                    $sql .= ' ' . $where['logic'] . ' ';
                }
                
                if (isset($where['raw']) && $where['raw']) {
                    $sql .= $where['column'] . ' ' . $where['operator'] . ' ' . $where['value'];
                } else {
                    $sql .= $where['column'] . ' ' . $where['operator'] . ' ?';
                    $values[] = $where['value'];
                }
                
                $first = false;
            }
        }
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($values);
        
        return $stmt->rowCount();
    }

    /**
     * Delete rows
     * 
     * @return int Number of affected rows
     */
    public function delete() {
        $sql = "DELETE FROM {$this->table}";
        $values = [];
        
        // Add where clauses
        if (!empty($this->where)) {
            $sql .= ' WHERE ';
            $first = true;
            
            foreach ($this->where as $where) {
                if (!$first) {
                    $sql .= ' ' . $where['logic'] . ' ';
                }
                
                if (isset($where['raw']) && $where['raw']) {
                    $sql .= $where['column'] . ' ' . $where['operator'] . ' ' . $where['value'];
                } else {
                    $sql .= $where['column'] . ' ' . $where['operator'] . ' ?';
                    $values[] = $where['value'];
                }
                
                $first = false;
            }
        }
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($values);
        
        return $stmt->rowCount();
    }
}

/**
 * Create a new query builder instance
 * 
 * @param string $table Table name
 * @return QueryBuilder
 */
function query($table = null) {
    $builder = new QueryBuilder();
    
    if ($table !== null) {
        $builder->table($table);
    }
    
    return $builder;
} 