<?php
// This file contains chart components for the dashboard

/**
 * Renders an attendance chart
 */
function renderAttendanceChart() {
    ?>
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-primary-dark">Attendance Trends</h2>
            <div class="flex space-x-2">
                <select id="attendance-period" class="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary">
                    <option value="weekly">Weekly</option>
                    <option value="monthly" selected>Monthly</option>
                    <option value="yearly">Yearly</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="attendance-chart"></canvas>
        </div>
    </div>
    <?php
}

/**
 * Renders a financial chart
 */
function renderFinancialChart() {
    ?>
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-primary-dark">Financial Overview</h2>
            <div class="flex space-x-2">
                <select id="finance-period" class="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary">
                    <option value="weekly">Weekly</option>
                    <option value="monthly" selected>Monthly</option>
                    <option value="yearly">Yearly</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="offerings-chart"></canvas>
        </div>
    </div>
    <?php
}

/**
 * Renders a membership growth chart
 */
function renderMembershipChart() {
    ?>
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-primary-dark">Membership Growth</h2>
            <span class="text-xs text-white bg-primary px-2 py-1 rounded-full">2023</span>
        </div>
        <div class="h-64">
            <canvas id="membership-chart"></canvas>
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const membershipCtx = document.getElementById('membership-chart');
                if (membershipCtx) {
                    new Chart(membershipCtx, {
                        type: 'line',
                        data: {
                            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                            datasets: [{
                                label: 'New Members',
                                data: [5, 8, 12, 7, 10, 15, 9, 11, 13, 8, 6, 10],
                                backgroundColor: 'rgba(116, 227, 154, 0.2)',
                                borderColor: '#3BD16F',
                                borderWidth: 2,
                                tension: 0.3
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            }
                        }
                    });
                }
            });
        </script>
    </div>
    <?php
}

/**
 * Renders a department distribution chart
 */
function renderDepartmentChart() {
    ?>
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-primary-dark">Department Distribution</h2>
            <span class="text-xs text-white bg-primary-light px-2 py-1 rounded-full">Active Members</span>
        </div>
        <div class="h-64">
            <canvas id="department-chart"></canvas>
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const departmentCtx = document.getElementById('department-chart');
                if (departmentCtx) {
                    new Chart(departmentCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Choir', 'Media', 'Ushering', 'Children', 'Protocol', 'Welfare', 'Others'],
                            datasets: [{
                                data: [45, 25, 30, 20, 15, 25, 90],
                                backgroundColor: [
                                    '#3BD16F', // primary
                                    '#74E39A', // primary-light
                                    '#2BA356', // primary-dark
                                    '#74E39A', // secondary
                                    '#A9EDBE', // secondary-light
                                    '#4BC77A', // secondary-dark
                                    '#E8F8EE'  // light green 50
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'right',
                                    labels: {
                                        boxWidth: 12,
                                        font: {
                                            size: 10
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            });
        </script>
    </div>
    <?php
}
?>
