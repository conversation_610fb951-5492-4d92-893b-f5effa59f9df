<?php
/**
 * Application Bootstrap File
 *
 * This file initializes the application, loads configurations,
 * sets up error handling, and includes necessary files.
 */

// Start output buffering to prevent header issues
ob_start();

// Start session FIRST before any output
if (session_status() === PHP_SESSION_NONE) {
    // Set secure session parameters BEFORE starting session
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
    ini_set('session.cookie_samesite', 'Lax');
    ini_set('session.gc_maxlifetime', 3600);
    session_name('ICGC_SESSION');
    session_start();
}

// Define the base directory
define('BASE_DIR', __DIR__);

// Define the base URL - Fixed to prevent duplication
define('BASE_URL', '/icgc/');

// Debug mode (set to false for production)
define('DEBUG', false);

// Load environment configuration
require_once BASE_DIR . '/config/environment.php';
EnvironmentConfig::load();

// Load error handler first (disabled due to conflicts)
// require_once BASE_DIR . '/utils/error_handler.php';

// Load error handler
if (file_exists(BASE_DIR . '/handlers/ErrorHandler.php')) {
    require_once BASE_DIR . '/handlers/ErrorHandler.php';
}

// Load core functions and utilities
require_once BASE_DIR . '/utils/functions.php';
require_once BASE_DIR . '/helpers/functions.php';
require_once BASE_DIR . '/helpers/time_helpers.php';
require_once BASE_DIR . '/utils/helpers.php';
require_once BASE_DIR . '/utils/cache.php';
require_once BASE_DIR . '/utils/render.php';
require_once BASE_DIR . '/utils/db_helpers.php';

// Load environment variables from .env file if it exists
if (file_exists(BASE_DIR . '/.env')) {
    $env_lines = file(BASE_DIR . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($env_lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
        }
    }
}

// Load configuration
require_once BASE_DIR . '/config/database.php';

// Set timezone
date_default_timezone_set('UTC');

/**
 * Get database connection
 *
 * @return PDO Database connection
 */
function db_connect() {
    static $db = null;
    
    if ($db === null) {
        $database = new Database();
        $db = $database->getConnection();
    }
    
    return $db;
}

// Connect to database
$db = db_connect();

// Session regeneration (session already started at top of file)
if (!isset($_SESSION['created'])) {
    $_SESSION['created'] = time();
} else if (time() - $_SESSION['created'] > 1800) {
    // Regenerate session ID every 30 minutes
    session_regenerate_id(true);
    $_SESSION['created'] = time();
}

// Autoload classes
spl_autoload_register(function ($class_name) {
    // Convert class name to file path
    $class_name = str_replace('\\', '/', $class_name);
    
    // Search in different directories
    $directories = [
        BASE_DIR . '/models/',
        BASE_DIR . '/controllers/',
        BASE_DIR . '/helpers/',
        BASE_DIR . '/services/',
        BASE_DIR . '/utils/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $class_name . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Load utilities
require_once BASE_DIR . '/utils/logger.php';
require_once BASE_DIR . '/utils/sanitizer.php';
require_once BASE_DIR . '/utils/csrf.php';

// Initialize comprehensive error handling
if (file_exists(BASE_DIR . '/handlers/ErrorHandler.php')) {
    require_once BASE_DIR . '/handlers/ErrorHandler.php';
    ErrorHandler::initialize();
}

// Initialize logging with context
$logger = AppLogger::getInstance();
$logger->setContext([
    'session_id' => session_id(),
    'user_id' => $_SESSION['user_id'] ?? null,
    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
]);

// Set default headers (only if headers haven't been sent)
if (!headers_sent()) {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: SAMEORIGIN');
    header('X-XSS-Protection: 1; mode=block');
}

// Disable error reporting for production
if (APP_ENV === 'production') {
    error_reporting(0);
    ini_set('display_errors', 0);
} else {
    // Enable error reporting for development
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
}

// Check if user is logged in
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * Check if user has a specific role
 * 
 * @param string|array $roles
 * @return bool
 */
function has_role($roles) {
    if (!is_logged_in()) {
        return false;
    }
    
    if (!is_array($roles)) {
        $roles = [$roles];
    }
    
    return in_array($_SESSION['user_role'] ?? '', $roles);
}

/**
 * Check if user has permission
 *
 * @param string $permission The permission to check
 * @return bool Whether the user has the permission
 */
function has_permission($permission) {
    if (!isset($_SESSION['user_role'])) {
        return false;
    }
    
    // Admin has all permissions
    if ($_SESSION['user_role'] === 'admin') {
        return true;
    }
    
    // Define role-based permissions
    $permissions = [
        'admin' => ['*'],
        'staff' => ['view_members', 'add_members', 'edit_members', 'view_attendance', 'mark_attendance', 'view_finance', 'add_transaction'],
        'manager' => ['view_members', 'add_members', 'edit_members', 'view_attendance', 'mark_attendance', 'view_finance', 'add_transaction'],
        'user' => ['view_members', 'view_attendance', 'mark_attendance']
    ];
    
    // Check if user role has the permission
    if (isset($permissions[$_SESSION['user_role']])) {
        return in_array($permission, $permissions[$_SESSION['user_role']]) || in_array('*', $permissions[$_SESSION['user_role']]);
    }
    
    return false;
}

// Redirect if not logged in
function require_login($redirect_to = '/auth/login.php') {
    if (!is_logged_in()) {
        header('Location: ' . BASE_URL . ltrim($redirect_to, '/'));
        exit;
    }
}

// Check if user is blocked
function check_user_status() {
    if (isset($_SESSION['user_id']) && isset($_SESSION['user_status']) && $_SESSION['user_status'] === 'blocked') {
        // Clear session
        session_unset();
        session_destroy();

        // Start a new session for the flash message
        session_start();

        // Set error message
        set_flash_message('Your account has been blocked. Please contact the administrator.', 'danger');

        // Redirect to login
        header('Location: ' . BASE_URL . 'login');
        exit;
    }
}

// Removed tenant access check - no longer needed for single tenant

/**
 * Check if user is authenticated and redirect to login if not
 * This function is used in public/index.php to protect routes
 */
function check_auth() {
    if (!isset($_SESSION['user_id'])) {
        // Redirect to login page
        header('Location: ' . BASE_URL . 'login');
        exit;
    }
}

// Check if user has permission to access a resource
function check_permission($required_role = null) {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        return false;
    }

    // Super admin has access to everything
    if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'super_admin') {
        return true;
    }

    // Check role if specified
    if ($required_role) {
        $user_role = $_SESSION['user_role'] ?? 'staff';
        $role_hierarchy = ['staff' => 1, 'admin' => 2];

        $user_level = $role_hierarchy[$user_role] ?? 0;
        $required_level = $role_hierarchy[$required_role] ?? 0;

        if ($user_level < $required_level) {
            return false;
        }
    }

    return true;
}

// Removed tenant-specific helper functions - no longer needed for single tenant

// Removed redundant autoload function - using spl_autoload_register above

// Function to load controller
function load_controller($controller_name) {
    $controller_path = BASE_DIR . '/controllers/' . $controller_name . '.php';
    
    if (file_exists($controller_path)) {
        require_once $controller_path;
        return new $controller_name();
    }
    
    return false;
}
?>
