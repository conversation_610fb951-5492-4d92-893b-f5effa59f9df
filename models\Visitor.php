<?php
/**
 * Visitor Model
 * 
 * This model handles all database operations related to visitors
 */

class Visitor {
    private $conn;
    private $table = 'visitors';

    // Visitor properties
    public $id;
    public $first_name;
    public $last_name;
    public $email;
    public $phone_number;
    public $address;
    public $first_visit_date;
    public $how_did_they_hear;
    public $visitor_status;
    public $notes;
    public $created_at;
    public $updated_at;

    // Error handling
    public $error;

    /**
     * Constructor
     * 
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all visitors
     *
     * @param string $orderBy Column to order by
     * @param string $order Order direction (ASC or DESC)
     * @param string $status Filter by visitor status
     * @return PDOStatement
     */
    public function getAll($orderBy = 'first_visit_date', $order = 'DESC', $status = null) {
        $query = "SELECT * FROM " . $this->table;

        // Add status filter if provided
        if ($status) {
            $query .= " WHERE visitor_status = :status";
        }

        $query .= " ORDER BY " . $orderBy . " " . $order;

        $stmt = $this->conn->prepare($query);

        if ($status) {
            $stmt->bindParam(':status', $status);
        }

        $stmt->execute();
        return $stmt;
    }

    /**
     * Get visitors with pagination and filters
     *
     * @param int $limit Number of visitors per page
     * @param int $offset Starting position
     * @param string $search Search term for name, email, or phone
     * @param string $status Filter by visitor status
     * @param string $orderBy Column to order by
     * @param string $order Order direction (ASC or DESC)
     * @return array Array with 'visitors' and 'total' keys
     */
    public function getWithPagination($limit = 25, $offset = 0, $search = '', $status = null, $orderBy = 'first_visit_date', $order = 'DESC') {
        $where_conditions = [];
        $params = [];

        // Add search filter
        if (!empty($search)) {
            $where_conditions[] = "(first_name LIKE :search OR last_name LIKE :search OR email LIKE :search OR phone_number LIKE :search)";
            $params[':search'] = '%' . $search . '%';
        }

        // Add status filter
        if ($status && $status !== 'all') {
            $where_conditions[] = "visitor_status = :status";
            $params[':status'] = $status;
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM " . $this->table . " " . $where_clause;
        $count_stmt = $this->conn->prepare($count_query);
        $count_stmt->execute($params);
        $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Get visitors with pagination
        $query = "SELECT * FROM " . $this->table . " " . $where_clause . " ORDER BY " . $orderBy . " " . $order . " LIMIT :limit OFFSET :offset";
        $stmt = $this->conn->prepare($query);

        // Bind search and status parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        // Bind pagination parameters
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        $stmt->execute();
        $visitors = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'visitors' => $visitors,
            'total' => $total
        ];
    }

    /**
     * Get visitor by ID
     * 
     * @param int $id Visitor ID
     * @return bool
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($row) {
            $this->id = $row['id'];
            $this->first_name = $row['first_name'];
            $this->last_name = $row['last_name'];
            $this->email = $row['email'];
            $this->phone_number = $row['phone_number'];
            $this->address = $row['address'];
            $this->first_visit_date = $row['first_visit_date'];
            $this->how_did_they_hear = $row['how_did_they_hear'];
            $this->visitor_status = $row['visitor_status'];
            $this->notes = $row['notes'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            
            return true;
        }
        
        return false;
    }

    /**
     * Validate visitor data before create/update operations
     *
     * @return bool True if validation passes, false otherwise
     */
    public function validate() {
        $this->error = null;

        // Required field validation
        if (empty($this->first_name)) {
            $this->error = 'First name is required.';
            return false;
        }

        if (empty($this->last_name)) {
            $this->error = 'Last name is required.';
            return false;
        }

        if (empty($this->phone_number)) {
            $this->error = 'Phone number is required.';
            return false;
        }

        if (empty($this->first_visit_date)) {
            $this->error = 'First visit date is required.';
            return false;
        }

        if (empty($this->how_did_they_hear)) {
            $this->error = 'How did they hear about us is required.';
            return false;
        }

        // Name format validation
        if (!preg_match('/^[a-zA-Z\s\'-]+$/', $this->first_name)) {
            $this->error = 'First name contains invalid characters.';
            return false;
        }

        if (!preg_match('/^[a-zA-Z\s\'-]+$/', $this->last_name)) {
            $this->error = 'Last name contains invalid characters.';
            return false;
        }

        // Email validation (if provided)
        if (!empty($this->email) && !filter_var($this->email, FILTER_VALIDATE_EMAIL)) {
            $this->error = 'Invalid email format.';
            return false;
        }

        // Phone number format validation
        if (!preg_match('/^[0-9\-\(\)\/\+\s]*$/', $this->phone_number)) {
            $this->error = 'Phone number contains invalid characters.';
            return false;
        }

        // Check for duplicate phone number (only for new visitors)
        if (empty($this->id)) {
            $existing_visitor = $this->existsByPhone($this->phone_number);
            if ($existing_visitor) {
                $this->error = 'A visitor with this phone number already exists.';
                return false;
            }
        }

        // Date validation
        if (!empty($this->first_visit_date)) {
            $date = DateTime::createFromFormat('Y-m-d', $this->first_visit_date);
            if (!$date || $date->format('Y-m-d') !== $this->first_visit_date) {
                $this->error = 'Invalid first visit date format.';
                return false;
            }

            // Check if date is not too far in the future (allow up to 1 year)
            $max_future_date = new DateTime('+1 year');
            if ($date > $max_future_date) {
                $this->error = 'First visit date cannot be more than 1 year in the future.';
                return false;
            }
        }

        // Visitor status validation (if provided)
        if (!empty($this->visitor_status)) {
            $valid_statuses = ['new', 'in_follow_up', 'converted', 'inactive'];
            if (!in_array(strtolower($this->visitor_status), $valid_statuses)) {
                $this->error = 'Invalid visitor status.';
                return false;
            }
        }

        return true;
    }

    /**
     * Create new visitor with validation
     *
     * @return bool
     */
    public function create() {
        // Validate data before creating
        if (!$this->validate()) {
            return false;
        }
        $query = "INSERT INTO " . $this->table . " 
                  (first_name, last_name, email, phone_number, address, first_visit_date, 
                   how_did_they_hear, visitor_status, notes, created_at, updated_at) 
                  VALUES 
                  (:first_name, :last_name, :email, :phone_number, :address, :first_visit_date, 
                   :how_did_they_hear, :visitor_status, :notes, :created_at, :updated_at)";
        
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->phone_number = htmlspecialchars(strip_tags($this->phone_number));
        $this->address = htmlspecialchars(strip_tags($this->address));
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        
        // Set timestamps
        $this->created_at = date('Y-m-d H:i:s');
        $this->updated_at = date('Y-m-d H:i:s');
        
        // Default status if not set
        if (!$this->visitor_status) {
            $this->visitor_status = 'new';
        }
        
        // Bind parameters
        $stmt->bindParam(':first_name', $this->first_name);
        $stmt->bindParam(':last_name', $this->last_name);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':phone_number', $this->phone_number);
        $stmt->bindParam(':address', $this->address);
        $stmt->bindParam(':first_visit_date', $this->first_visit_date);
        $stmt->bindParam(':how_did_they_hear', $this->how_did_they_hear);
        $stmt->bindParam(':visitor_status', $this->visitor_status);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);
        
        // Execute query
        try {
            if ($stmt->execute()) {
                $this->id = $this->conn->lastInsertId();
                return true;
            } else {
                $this->error = 'Failed to create visitor record.';
                return false;
            }
        } catch (PDOException $e) {
            // Check for duplicate phone number error
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'phone_number') !== false) {
                $this->error = 'A visitor with this phone number already exists.';
            } else {
                $this->error = 'Database error occurred while creating visitor.';
                error_log("Visitor creation failed: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Update visitor with validation
     *
     * @return bool
     */
    public function update() {
        // Validate data before updating
        if (!$this->validate()) {
            return false;
        }
        $query = "UPDATE " . $this->table . " 
                  SET 
                  first_name = :first_name, 
                  last_name = :last_name, 
                  email = :email, 
                  phone_number = :phone_number, 
                  address = :address, 
                  first_visit_date = :first_visit_date, 
                  how_did_they_hear = :how_did_they_hear, 
                  visitor_status = :visitor_status, 
                  notes = :notes, 
                  updated_at = :updated_at 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->phone_number = htmlspecialchars(strip_tags($this->phone_number));
        $this->address = htmlspecialchars(strip_tags($this->address));
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        
        // Set updated timestamp
        $this->updated_at = date('Y-m-d H:i:s');
        
        // Bind parameters
        $stmt->bindParam(':first_name', $this->first_name);
        $stmt->bindParam(':last_name', $this->last_name);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':phone_number', $this->phone_number);
        $stmt->bindParam(':address', $this->address);
        $stmt->bindParam(':first_visit_date', $this->first_visit_date);
        $stmt->bindParam(':how_did_they_hear', $this->how_did_they_hear);
        $stmt->bindParam(':visitor_status', $this->visitor_status);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);
        
        // Execute query
        try {
            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Failed to update visitor record.';
                return false;
            }
        } catch (PDOException $e) {
            // Check for duplicate phone number error
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'phone_number') !== false) {
                $this->error = 'A visitor with this phone number already exists.';
            } else {
                $this->error = 'Database error occurred while updating visitor.';
                error_log("Visitor update failed: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Delete visitor
     * 
     * @param int $id Visitor ID
     * @return bool
     */
    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute()) {
            return true;
        }
        
        return false;
    }

    /**
     * Search visitors
     * 
     * @param string $keyword Search keyword
     * @return PDOStatement
     */
    public function search($keyword) {
        $query = "SELECT * FROM " . $this->table . " 
                  WHERE first_name LIKE :keyword 
                  OR last_name LIKE :keyword 
                  OR email LIKE :keyword 
                  OR phone_number LIKE :keyword 
                  ORDER BY first_visit_date DESC";
        
        $stmt = $this->conn->prepare($query);
        
        $keyword = '%' . $keyword . '%';
        $stmt->bindParam(':keyword', $keyword);
        
        $stmt->execute();
        return $stmt;
    }

    /**
     * Update visitor status
     * 
     * @param int $id Visitor ID
     * @param string $status New status
     * @return bool
     */
    public function updateStatus($id, $status) {
        $query = "UPDATE " . $this->table . " 
                  SET visitor_status = :status, updated_at = :updated_at 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        $updated_at = date('Y-m-d H:i:s');
        
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':updated_at', $updated_at);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute()) {
            return true;
        }
        
        return false;
    }

    /**
     * Get visitor statistics
     * 
     * @return array
     */
    public function getStatistics() {
        $stats = [];
        
        // Total visitors
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // New visitors
        $query = "SELECT COUNT(*) as new FROM " . $this->table . " WHERE visitor_status = 'new'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['new'] = $stmt->fetch(PDO::FETCH_ASSOC)['new'];
        
        // In follow-up
        $query = "SELECT COUNT(*) as in_follow_up FROM " . $this->table . " WHERE visitor_status = 'in_follow_up'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['in_follow_up'] = $stmt->fetch(PDO::FETCH_ASSOC)['in_follow_up'];
        
        // Converted
        $query = "SELECT COUNT(*) as converted FROM " . $this->table . " WHERE visitor_status = 'converted'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['converted'] = $stmt->fetch(PDO::FETCH_ASSOC)['converted'];
        
        // Inactive
        $query = "SELECT COUNT(*) as inactive FROM " . $this->table . " WHERE visitor_status = 'inactive'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['inactive'] = $stmt->fetch(PDO::FETCH_ASSOC)['inactive'];
        
        // Recent visitors (last 30 days)
        $query = "SELECT COUNT(*) as recent FROM " . $this->table . " 
                  WHERE first_visit_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['recent'] = $stmt->fetch(PDO::FETCH_ASSOC)['recent'];
        
        return $stats;
    }

    /**
     * Check if visitor exists by phone number
     * 
     * @param string $phone_number Phone number
     * @return bool|int Returns visitor ID if exists, false otherwise
     */
    public function existsByPhone($phone_number) {
        $query = "SELECT id FROM " . $this->table . " WHERE phone_number = :phone_number";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':phone_number', $phone_number);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['id'];
        }
        
        return false;
    }
}
