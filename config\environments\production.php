<?php
/**
 * Production Environment Configuration
 * 
 * Configuration overrides for production environment.
 * 
 * @package Config
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

return [
    'app' => [
        'debug' => false,
        'name' => 'ICGC Finance System'
    ],

    'database' => [
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4",
            PDO::ATTR_PERSISTENT => true // Use persistent connections in production
        ]
    ],

    'security' => [
        'session_lifetime' => 3600, // 1 hour
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
    ],

    'logging' => [
        'level' => 'warning', // Only log warnings and errors in production
        'file' => 'logs/production.log',
        'max_files' => 90, // Keep 90 days of logs
        'audit_enabled' => true
    ],

    'cache' => [
        'enabled' => true,
        'driver' => 'file',
        'ttl' => 3600,
        'prefix' => 'icgc_finance_prod_'
    ],

    'email' => [
        'enabled' => true,
        'driver' => 'smtp'
    ],

    'finance' => [
        'dashboard_cache_ttl' => 300, // 5 minutes
        'backup_enabled' => true,
        'backup_frequency' => 'daily'
    ],

    'features' => [
        'audit_logging' => true,
        'advanced_reporting' => true,
        'email_notifications' => true,
        'api_access' => false, // Disable API in production unless needed
        'mobile_app' => false,
        'multi_currency' => false,
        'budget_management' => false,
        'automated_backups' => true,
        'two_factor_auth' => false,
        'ldap_integration' => false
    ]
];
