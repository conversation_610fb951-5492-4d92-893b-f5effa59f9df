<?php
/**
 * Update Database Schema
 *
 * This script updates the database schema with any new tables or modifications
 */

// Database connection parameters
require_once '../config/database.php';

// Create database connection
$database = new Database();
$conn = $database->getConnection();

// Check connection
if (!$conn) {
    die("Connection failed: Unable to connect to database");
}

echo "<h1>Updating Database Schema</h1>";

// Array of SQL statements to execute
$sql_statements = [
    // Create SMS table if not exists
    "CREATE TABLE IF NOT EXISTS sms_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        message TEXT NOT NULL,
        recipients TEXT NOT NULL,
        sent_date DATETIME NOT NULL,
        status ENUM('pending', 'sent', 'failed') NOT NULL DEFAULT 'pending',
        sent_by INT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (sent_by) REFERENCES users(id) ON DELETE SET NULL
    )",

    // Create Events table if not exists
    "CREATE TABLE IF NOT EXISTS events (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(100) NOT NULL,
        description TEXT,
        start_date DATETIME NOT NULL,
        end_date DATETIME NOT NULL,
        location VARCHAR(100),
        created_by INT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    )",

    // Create Equipment table if not exists
    "CREATE TABLE IF NOT EXISTS equipment (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        category VARCHAR(50) NOT NULL,
        description TEXT,
        purchase_date DATE,
        purchase_price DECIMAL(10, 2),
        `condition` ENUM('excellent', 'good', 'fair', 'poor') NOT NULL DEFAULT 'good',
        location VARCHAR(100),
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL
    )",

    // Create Equipment maintenance table if not exists
    "CREATE TABLE IF NOT EXISTS equipment_maintenance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        equipment_id INT NOT NULL,
        maintenance_date DATE NOT NULL,
        description TEXT NOT NULL,
        cost DECIMAL(10, 2),
        performed_by VARCHAR(100),
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (equipment_id) REFERENCES equipment(id) ON DELETE CASCADE
    )",

    // Create Settings table if not exists
    "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(50) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL
    )",

    // Insert default settings if not exists
    "INSERT IGNORE INTO settings (setting_key, setting_value, created_at, updated_at)
    VALUES
    ('church_name', 'ICGC Emmanuel Temple', NOW(), NOW()),
    ('church_address', '123 Church Street, Accra, Ghana', NOW(), NOW()),
    ('church_phone', '+233 123 456 789', NOW(), NOW()),
    ('church_email', '<EMAIL>', NOW(), NOW()),
    ('sms_api_key', '', NOW(), NOW()),
    ('sms_sender_id', 'ICGC', NOW(), NOW())"
];

// Execute each SQL statement
foreach ($sql_statements as $sql) {
    try {
        $conn->exec($sql);
        echo "<p style='color: green;'>✓ SQL statement executed successfully</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Error executing SQL: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>Database Update Complete</h2>";
echo "<p>The database schema has been updated with the latest tables and settings.</p>";
echo "<p><a href='../index.php'>Return to Dashboard</a></p>";
?>
