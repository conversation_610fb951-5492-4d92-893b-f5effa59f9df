/**
 * Phone Number Utilities for International Support
 * 
 * Provides client-side phone number validation, formatting, and normalization
 * to complement the server-side PhoneNumberUtils class.
 */

class PhoneNumberUtils {

    /**
     * Cached default country from server
     */
    static defaultCountry = null;

    /**
     * Country codes and patterns for validation
     */
    static countryPatterns = {
        // Africa
        'DZ': { code: '+213', pattern: /^0?([5-7]\d{8})$/, format: '+213 XXX XXX XXX' },
        'AO': { code: '+244', pattern: /^0?([9]\d{8})$/, format: '+244 XXX XXX XXX' },
        'BJ': { code: '+229', pattern: /^0?([2-9]\d{7})$/, format: '+229 XX XXX XXX' },
        'BW': { code: '+267', pattern: /^0?([7]\d{7})$/, format: '+267 XX XXX XXX' },
        'BF': { code: '+226', pattern: /^0?([7]\d{7})$/, format: '+226 XX XXX XXX' },
        'BI': { code: '+257', pattern: /^0?([7-9]\d{7})$/, format: '+257 XX XXX XXX' },
        'CM': { code: '+237', pattern: /^0?([6-7]\d{8})$/, format: '+237 XXX XXX XXX' },
        'CV': { code: '+238', pattern: /^0?([5-9]\d{6})$/, format: '+238 XXX XXXX' },
        'CF': { code: '+236', pattern: /^0?([7]\d{7})$/, format: '+236 XX XXX XXX' },
        'TD': { code: '+235', pattern: /^0?([6-9]\d{7})$/, format: '+235 XX XXX XXX' },
        'KM': { code: '+269', pattern: /^0?([3-7]\d{6})$/, format: '+269 XXX XXXX' },
        'CG': { code: '+242', pattern: /^0?([0-9]\d{7})$/, format: '+242 XX XXX XXX' },
        'CD': { code: '+243', pattern: /^0?([8-9]\d{8})$/, format: '+243 XXX XXX XXX' },
        'CI': { code: '+225', pattern: /^0?([0-9]\d{7})$/, format: '+225 XX XXX XXX' },
        'DJ': { code: '+253', pattern: /^0?([7]\d{7})$/, format: '+253 XX XXX XXX' },
        'EG': { code: '+20', pattern: /^0?([1]\d{8,9})$/, format: '+20 XXX XXX XXXX' },
        'GQ': { code: '+240', pattern: /^0?([2-9]\d{8})$/, format: '+240 XXX XXX XXX' },
        'ER': { code: '+291', pattern: /^0?([7]\d{6})$/, format: '+291 XXX XXXX' },
        'ET': { code: '+251', pattern: /^0?([9]\d{8})$/, format: '+251 XXX XXX XXX' },
        'GA': { code: '+241', pattern: /^0?([0-9]\d{7})$/, format: '+241 XX XXX XXX' },
        'GM': { code: '+220', pattern: /^0?([2-9]\d{6})$/, format: '+220 XXX XXXX' },
        'GH': { code: '+233', pattern: /^0?([2-5]\d{8})$/, format: '+233 XX XXX XXXX' },
        'GN': { code: '+224', pattern: /^0?([6-7]\d{7})$/, format: '+224 XX XXX XXX' },
        'GW': { code: '+245', pattern: /^0?([5-9]\d{6})$/, format: '+245 XXX XXXX' },
        'KE': { code: '+254', pattern: /^0?([7]\d{8})$/, format: '+254 XXX XXX XXX' },
        'LS': { code: '+266', pattern: /^0?([5-6]\d{7})$/, format: '+266 XX XXX XXX' },
        'LR': { code: '+231', pattern: /^0?([4-9]\d{7})$/, format: '+231 XX XXX XXX' },
        'LY': { code: '+218', pattern: /^0?([9]\d{8})$/, format: '+218 XXX XXX XXX' },
        'MG': { code: '+261', pattern: /^0?([3]\d{8})$/, format: '+261 XX XXX XXXX' },
        'MW': { code: '+265', pattern: /^0?([1-9]\d{7})$/, format: '+265 XX XXX XXX' },
        'ML': { code: '+223', pattern: /^0?([6-9]\d{7})$/, format: '+223 XX XXX XXX' },
        'MR': { code: '+222', pattern: /^0?([2-4]\d{7})$/, format: '+222 XX XXX XXX' },
        'MU': { code: '+230', pattern: /^0?([5]\d{7})$/, format: '+230 XXXX XXXX' },
        'MA': { code: '+212', pattern: /^0?([5-7]\d{8})$/, format: '+212 XXX XXX XXX' },
        'MZ': { code: '+258', pattern: /^0?([8]\d{8})$/, format: '+258 XX XXX XXXX' },
        'NA': { code: '+264', pattern: /^0?([6-8]\d{7})$/, format: '+264 XX XXX XXX' },
        'NE': { code: '+227', pattern: /^0?([9]\d{7})$/, format: '+227 XX XXX XXX' },
        'NG': { code: '+234', pattern: /^0?([7-9]\d{9})$/, format: '+234 XXX XXX XXXX' },
        'RW': { code: '+250', pattern: /^0?([7]\d{8})$/, format: '+250 XXX XXX XXX' },
        'ST': { code: '+239', pattern: /^0?([9]\d{6})$/, format: '+239 XXX XXXX' },
        'SN': { code: '+221', pattern: /^0?([7]\d{8})$/, format: '+221 XX XXX XXXX' },
        'SC': { code: '+248', pattern: /^0?([2]\d{6})$/, format: '+248 XXX XXXX' },
        'SL': { code: '+232', pattern: /^0?([2-9]\d{7})$/, format: '+232 XX XXX XXX' },
        'SO': { code: '+252', pattern: /^0?([6-9]\d{8})$/, format: '+252 XX XXX XXXX' },
        'ZA': { code: '+27', pattern: /^0?([6-8]\d{8})$/, format: '+27 XX XXX XXXX' },
        'SS': { code: '+211', pattern: /^0?([9]\d{8})$/, format: '+211 XXX XXX XXX' },
        'SD': { code: '+249', pattern: /^0?([9]\d{8})$/, format: '+249 XXX XXX XXX' },
        'SZ': { code: '+268', pattern: /^0?([7-8]\d{7})$/, format: '+268 XX XXX XXX' },
        'TZ': { code: '+255', pattern: /^0?([6-7]\d{8})$/, format: '+255 XXX XXX XXX' },
        'TG': { code: '+228', pattern: /^0?([9]\d{7})$/, format: '+228 XX XXX XXX' },
        'TN': { code: '+216', pattern: /^0?([2-9]\d{7})$/, format: '+216 XX XXX XXX' },
        'UG': { code: '+256', pattern: /^0?([7]\d{8})$/, format: '+256 XXX XXX XXX' },
        'ZM': { code: '+260', pattern: /^0?([9]\d{8})$/, format: '+260 XXX XXX XXX' },
        'ZW': { code: '+263', pattern: /^0?([7]\d{8})$/, format: '+263 XX XXX XXXX' },

        // Asia
        'AF': { code: '+93', pattern: /^0?([7]\d{8})$/, format: '+93 XXX XXX XXX' },
        'AM': { code: '+374', pattern: /^0?([9]\d{7})$/, format: '+374 XX XXX XXX' },
        'AZ': { code: '+994', pattern: /^0?([5-7]\d{8})$/, format: '+994 XX XXX XXXX' },
        'BH': { code: '+973', pattern: /^0?([3-9]\d{7})$/, format: '+973 XXXX XXXX' },
        'BD': { code: '+880', pattern: /^0?([1]\d{9})$/, format: '+880 XXXX XXXXXX' },
        'BT': { code: '+975', pattern: /^0?([1-8]\d{7})$/, format: '+975 XX XXX XXX' },
        'BN': { code: '+673', pattern: /^0?([2-8]\d{6})$/, format: '+673 XXX XXXX' },
        'KH': { code: '+855', pattern: /^0?([1-9]\d{7,8})$/, format: '+855 XX XXX XXXX' },
        'CN': { code: '+86', pattern: /^0?([1]\d{10})$/, format: '+86 XXX XXXX XXXX' },
        'CY': { code: '+357', pattern: /^0?([9]\d{7})$/, format: '+357 XX XXX XXX' },
        'GE': { code: '+995', pattern: /^0?([5-9]\d{8})$/, format: '+995 XXX XXX XXX' },
        'IN': { code: '+91', pattern: /^0?([6-9]\d{9})$/, format: '+91 XXXXX XXXXX' },
        'ID': { code: '+62', pattern: /^0?([8]\d{8,11})$/, format: '+62 XXX XXX XXXX' },
        'IR': { code: '+98', pattern: /^0?([9]\d{9})$/, format: '+98 XXX XXX XXXX' },
        'IQ': { code: '+964', pattern: /^0?([7]\d{9})$/, format: '+964 XXX XXX XXXX' },
        'IL': { code: '+972', pattern: /^0?([5]\d{8})$/, format: '+972 XX XXX XXXX' },
        'JP': { code: '+81', pattern: /^0?([7-9]\d{9})$/, format: '+81 XX XXXX XXXX' },
        'JO': { code: '+962', pattern: /^0?([7]\d{8})$/, format: '+962 X XXXX XXXX' },
        'KZ': { code: '+7', pattern: /^0?([7]\d{9})$/, format: '+7 XXX XXX XXXX' },
        'KW': { code: '+965', pattern: /^0?([5-9]\d{7})$/, format: '+965 XXXX XXXX' },
        'KG': { code: '+996', pattern: /^0?([5-9]\d{8})$/, format: '+996 XXX XXX XXX' },
        'LA': { code: '+856', pattern: /^0?([2]\d{8})$/, format: '+856 XX XXX XXXX' },
        'LB': { code: '+961', pattern: /^0?([3-9]\d{7})$/, format: '+961 XX XXX XXX' },
        'MY': { code: '+60', pattern: /^0?([1]\d{8,9})$/, format: '+60 XX XXX XXXX' },
        'MV': { code: '+960', pattern: /^0?([7-9]\d{6})$/, format: '+960 XXX XXXX' },
        'MN': { code: '+976', pattern: /^0?([8-9]\d{7})$/, format: '+976 XXXX XXXX' },
        'MM': { code: '+95', pattern: /^0?([9]\d{8,9})$/, format: '+95 XXX XXX XXXX' },
        'NP': { code: '+977', pattern: /^0?([9]\d{9})$/, format: '+977 XXX XXX XXXX' },
        'KP': { code: '+850', pattern: /^0?([1-9]\d{7,8})$/, format: '+850 XXX XXX XXX' },
        'KR': { code: '+82', pattern: /^0?([1]\d{8,9})$/, format: '+82 XX XXXX XXXX' },
        'OM': { code: '+968', pattern: /^0?([7-9]\d{7})$/, format: '+968 XXXX XXXX' },
        'PK': { code: '+92', pattern: /^0?([3]\d{9})$/, format: '+92 XXX XXX XXXX' },
        'PS': { code: '+970', pattern: /^0?([5]\d{8})$/, format: '+970 XXX XXX XXX' },
        'PH': { code: '+63', pattern: /^0?([9]\d{9})$/, format: '+63 XXX XXX XXXX' },
        'QA': { code: '+974', pattern: /^0?([3-7]\d{7})$/, format: '+974 XXXX XXXX' },
        'SA': { code: '+966', pattern: /^0?([5]\d{8})$/, format: '+966 XX XXX XXXX' },
        'SG': { code: '+65', pattern: /^0?([8-9]\d{7})$/, format: '+65 XXXX XXXX' },
        'LK': { code: '+94', pattern: /^0?([7]\d{8})$/, format: '+94 XX XXX XXXX' },
        'SY': { code: '+963', pattern: /^0?([9]\d{8})$/, format: '+963 XXX XXX XXX' },
        'TW': { code: '+886', pattern: /^0?([9]\d{8})$/, format: '+886 XXX XXX XXX' },
        'TJ': { code: '+992', pattern: /^0?([9]\d{8})$/, format: '+992 XX XXX XXXX' },
        'TH': { code: '+66', pattern: /^0?([6-9]\d{8})$/, format: '+66 XX XXX XXXX' },
        'TL': { code: '+670', pattern: /^0?([7]\d{7})$/, format: '+670 XXX XXXX' },
        'TR': { code: '+90', pattern: /^0?([5]\d{9})$/, format: '+90 XXX XXX XXXX' },
        'TM': { code: '+993', pattern: /^0?([6]\d{7})$/, format: '+993 XX XXX XXX' },
        'AE': { code: '+971', pattern: /^0?([5]\d{8})$/, format: '+971 XX XXX XXXX' },
        'UZ': { code: '+998', pattern: /^0?([9]\d{8})$/, format: '+998 XX XXX XXXX' },
        'VN': { code: '+84', pattern: /^0?([3-9]\d{8})$/, format: '+84 XXX XXX XXX' },
        'YE': { code: '+967', pattern: /^0?([7]\d{8})$/, format: '+967 XXX XXX XXX' },

        // Europe
        'AL': { code: '+355', pattern: /^0?([6-7]\d{8})$/, format: '+355 XXX XXX XXX' },
        'AD': { code: '+376', pattern: /^0?([3-6]\d{5})$/, format: '+376 XXX XXX' },
        'AT': { code: '+43', pattern: /^0?([6-7]\d{8,13})$/, format: '+43 XXX XXX XXXX' },
        'BY': { code: '+375', pattern: /^0?([2-4]\d{8})$/, format: '+375 XX XXX XXXX' },
        'BE': { code: '+32', pattern: /^0?([4]\d{8})$/, format: '+32 XXX XX XX XX' },
        'BA': { code: '+387', pattern: /^0?([6]\d{8})$/, format: '+387 XX XXX XXX' },
        'BG': { code: '+359', pattern: /^0?([8-9]\d{8})$/, format: '+359 XXX XXX XXX' },
        'HR': { code: '+385', pattern: /^0?([9]\d{8})$/, format: '+385 XX XXX XXXX' },
        'CZ': { code: '+420', pattern: /^0?([6-7]\d{8})$/, format: '+420 XXX XXX XXX' },
        'DK': { code: '+45', pattern: /^0?([2-9]\d{7})$/, format: '+45 XX XX XX XX' },
        'EE': { code: '+372', pattern: /^0?([5]\d{7})$/, format: '+372 XXXX XXXX' },
        'FI': { code: '+358', pattern: /^0?([4-5]\d{8})$/, format: '+358 XX XXX XXXX' },
        'FR': { code: '+33', pattern: /^0?([6-7]\d{8})$/, format: '+33 X XX XX XX XX' },
        'DE': { code: '+49', pattern: /^0?([1]\d{9,11})$/, format: '+49 XXX XXX XXXX' },
        'GR': { code: '+30', pattern: /^0?([6]\d{9})$/, format: '+30 XXX XXX XXXX' },
        'HU': { code: '+36', pattern: /^0?([2-7]\d{8})$/, format: '+36 XX XXX XXXX' },
        'IS': { code: '+354', pattern: /^0?([6-8]\d{6})$/, format: '+354 XXX XXXX' },
        'IE': { code: '+353', pattern: /^0?([8]\d{8})$/, format: '+353 XX XXX XXXX' },
        'IT': { code: '+39', pattern: /^0?([3]\d{8,9})$/, format: '+39 XXX XXX XXXX' },
        'LV': { code: '+371', pattern: /^0?([2]\d{7})$/, format: '+371 XX XXX XXX' },
        'LI': { code: '+423', pattern: /^0?([6-7]\d{6})$/, format: '+423 XXX XXXX' },
        'LT': { code: '+370', pattern: /^0?([6]\d{7})$/, format: '+370 XXX XXXXX' },
        'LU': { code: '+352', pattern: /^0?([6]\d{8})$/, format: '+352 XXX XXX XXX' },
        'MK': { code: '+389', pattern: /^0?([7]\d{7})$/, format: '+389 XX XXX XXX' },
        'MT': { code: '+356', pattern: /^0?([7-9]\d{7})$/, format: '+356 XXXX XXXX' },
        'MD': { code: '+373', pattern: /^0?([6-7]\d{7})$/, format: '+373 XX XXX XXX' },
        'MC': { code: '+377', pattern: /^0?([6]\d{7})$/, format: '+377 XX XX XX XX' },
        'ME': { code: '+382', pattern: /^0?([6]\d{7})$/, format: '+382 XX XXX XXX' },
        'NL': { code: '+31', pattern: /^0?([6]\d{8})$/, format: '+31 X XX XX XX XX' },
        'NO': { code: '+47', pattern: /^0?([4-9]\d{7})$/, format: '+47 XXX XX XXX' },
        'PL': { code: '+48', pattern: /^0?([5-9]\d{8})$/, format: '+48 XXX XXX XXX' },
        'PT': { code: '+351', pattern: /^0?([9]\d{8})$/, format: '+351 XXX XXX XXX' },
        'RO': { code: '+40', pattern: /^0?([7]\d{8})$/, format: '+40 XXX XXX XXX' },
        'RU': { code: '+7', pattern: /^0?([9]\d{9})$/, format: '+7 XXX XXX XXXX' },
        'SM': { code: '+378', pattern: /^0?([6]\d{9})$/, format: '+378 XXXX XXXXXX' },
        'RS': { code: '+381', pattern: /^0?([6]\d{8})$/, format: '+381 XX XXX XXXX' },
        'SK': { code: '+421', pattern: /^0?([9]\d{8})$/, format: '+421 XXX XXX XXX' },
        'SI': { code: '+386', pattern: /^0?([3-7]\d{7})$/, format: '+386 XX XXX XXX' },
        'ES': { code: '+34', pattern: /^0?([6-7]\d{8})$/, format: '+34 XXX XXX XXX' },
        'SE': { code: '+46', pattern: /^0?([7]\d{8})$/, format: '+46 XX XXX XXXX' },
        'CH': { code: '+41', pattern: /^0?([7]\d{8})$/, format: '+41 XX XXX XXXX' },
        'UA': { code: '+380', pattern: /^0?([5-9]\d{8})$/, format: '+380 XX XXX XXXX' },
        'GB': { code: '+44', pattern: /^0?([7-8]\d{9}|[1-6]\d{8,9})$/, format: '+44 XXXX XXXXXX' },
        'VA': { code: '+39', pattern: /^0?([3]\d{8,9})$/, format: '+39 XXX XXX XXXX' },

        // North America
        'US': { code: '+1', pattern: /^1?([2-9]\d{9})$/, format: '+1 (XXX) XXX-XXXX' },
        'CA': { code: '+1', pattern: /^1?([2-9]\d{9})$/, format: '+1 (XXX) XXX-XXXX' },
        'MX': { code: '+52', pattern: /^0?([1]\d{9})$/, format: '+52 XX XXXX XXXX' },

        // Central America & Caribbean
        'BZ': { code: '+501', pattern: /^0?([6]\d{6})$/, format: '+501 XXX XXXX' },
        'CR': { code: '+506', pattern: /^0?([6-8]\d{7})$/, format: '+506 XXXX XXXX' },
        'SV': { code: '+503', pattern: /^0?([6-7]\d{7})$/, format: '+503 XXXX XXXX' },
        'GT': { code: '+502', pattern: /^0?([4-5]\d{7})$/, format: '+502 XXXX XXXX' },
        'HN': { code: '+504', pattern: /^0?([3-9]\d{7})$/, format: '+504 XXXX XXXX' },
        'NI': { code: '+505', pattern: /^0?([8]\d{7})$/, format: '+505 XXXX XXXX' },
        'PA': { code: '+507', pattern: /^0?([6]\d{7})$/, format: '+507 XXXX XXXX' },
        'AG': { code: '+1268', pattern: /^0?([4-7]\d{6})$/, format: '+1268 XXX XXXX' },
        'BS': { code: '+1242', pattern: /^0?([3-8]\d{6})$/, format: '+1242 XXX XXXX' },
        'BB': { code: '+1246', pattern: /^0?([2-8]\d{6})$/, format: '+1246 XXX XXXX' },
        'CU': { code: '+53', pattern: /^0?([5]\d{7})$/, format: '+53 X XXX XXXX' },
        'DM': { code: '+1767', pattern: /^0?([2-8]\d{6})$/, format: '+1767 XXX XXXX' },
        'DO': { code: '+1', pattern: /^1?([8]\d{9})$/, format: '+1 XXX XXX XXXX' },
        'GD': { code: '+1473', pattern: /^0?([4]\d{6})$/, format: '+1473 XXX XXXX' },
        'HT': { code: '+509', pattern: /^0?([3-4]\d{7})$/, format: '+509 XX XX XXXX' },
        'JM': { code: '+1876', pattern: /^0?([2-9]\d{6})$/, format: '+1876 XXX XXXX' },
        'KN': { code: '+1869', pattern: /^0?([4-7]\d{6})$/, format: '+1869 XXX XXXX' },
        'LC': { code: '+1758', pattern: /^0?([4-8]\d{6})$/, format: '+1758 XXX XXXX' },
        'VC': { code: '+1784', pattern: /^0?([4-5]\d{6})$/, format: '+1784 XXX XXXX' },
        'TT': { code: '+1868', pattern: /^0?([2-8]\d{6})$/, format: '+1868 XXX XXXX' },

        // South America
        'AR': { code: '+54', pattern: /^0?([9]\d{9})$/, format: '+54 XXX XXX XXXX' },
        'BO': { code: '+591', pattern: /^0?([6-7]\d{7})$/, format: '+591 X XXX XXXX' },
        'BR': { code: '+55', pattern: /^0?([1]\d{10})$/, format: '+55 XX XXXXX XXXX' },
        'CL': { code: '+56', pattern: /^0?([9]\d{8})$/, format: '+56 X XXXX XXXX' },
        'CO': { code: '+57', pattern: /^0?([3]\d{9})$/, format: '+57 XXX XXX XXXX' },
        'EC': { code: '+593', pattern: /^0?([9]\d{8})$/, format: '+593 XX XXX XXXX' },
        'FK': { code: '+500', pattern: /^0?([5]\d{4})$/, format: '+500 XXXXX' },
        'GF': { code: '+594', pattern: /^0?([6]\d{8})$/, format: '+594 XXX XXX XXX' },
        'GY': { code: '+592', pattern: /^0?([6]\d{6})$/, format: '+592 XXX XXXX' },
        'PY': { code: '+595', pattern: /^0?([9]\d{8})$/, format: '+595 XXX XXX XXX' },
        'PE': { code: '+51', pattern: /^0?([9]\d{8})$/, format: '+51 XXX XXX XXX' },
        'SR': { code: '+597', pattern: /^0?([6-8]\d{6})$/, format: '+597 XXX XXXX' },
        'UY': { code: '+598', pattern: /^0?([9]\d{7})$/, format: '+598 XXXX XXXX' },
        'VE': { code: '+58', pattern: /^0?([4]\d{9})$/, format: '+58 XXX XXX XXXX' },

        // Oceania
        'AU': { code: '+61', pattern: /^0?([4]\d{8})$/, format: '+61 XXX XXX XXX' },
        'FJ': { code: '+679', pattern: /^0?([7-9]\d{6})$/, format: '+679 XXX XXXX' },
        'KI': { code: '+686', pattern: /^0?([7-8]\d{4})$/, format: '+686 XXXXX' },
        'MH': { code: '+692', pattern: /^0?([2-6]\d{6})$/, format: '+692 XXX XXXX' },
        'FM': { code: '+691', pattern: /^0?([3-9]\d{6})$/, format: '+691 XXX XXXX' },
        'NR': { code: '+674', pattern: /^0?([5-7]\d{6})$/, format: '+674 XXX XXXX' },
        'NZ': { code: '+64', pattern: /^0?([2]\d{7,9})$/, format: '+64 XX XXX XXXX' },
        'PW': { code: '+680', pattern: /^0?([7]\d{6})$/, format: '+680 XXX XXXX' },
        'PG': { code: '+675', pattern: /^0?([7]\d{7})$/, format: '+675 XXX XXXX' },
        'WS': { code: '+685', pattern: /^0?([7]\d{6})$/, format: '+685 XXX XXXX' },
        'SB': { code: '+677', pattern: /^0?([7-9]\d{6})$/, format: '+677 XXX XXXX' },
        'TO': { code: '+676', pattern: /^0?([7-8]\d{6})$/, format: '+676 XXX XXXX' },
        'TV': { code: '+688', pattern: /^0?([9]\d{4})$/, format: '+688 XXXXX' },
        'VU': { code: '+678', pattern: /^0?([5-7]\d{6})$/, format: '+678 XXX XXXX' }
    };

    /**
     * Get default country from server configuration
     *
     * @returns {string} Default country code
     */
    static getDefaultCountry() {
        if (this.defaultCountry) {
            return this.defaultCountry;
        }

        // Try to get from server via AJAX
        try {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', BASE_URL + 'api/get-default-country.php', false); // Synchronous for simplicity
            xhr.send();

            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                this.defaultCountry = response.country || 'GH';
            } else {
                this.defaultCountry = 'GH'; // Fallback
            }
        } catch (error) {
            console.warn('Could not fetch default country from server, using system fallback');
            // Try to get from a global variable set by the server
            this.defaultCountry = window.DEFAULT_PHONE_COUNTRY || 'GH';
        }

        return this.defaultCountry;
    }

    /**
     * Normalize phone number (client-side version)
     *
     * @param {string} phone - Raw phone number input
     * @param {string} defaultCountry - Default country code (ISO 2-letter)
     * @returns {string} Normalized phone number
     */
    static normalize(phone, defaultCountry = null) {
        // Get default country from server-side configuration if not provided
        if (!defaultCountry) {
            defaultCountry = this.getDefaultCountry();
        }
        if (!phone) return '';
        
        // Remove all non-numeric characters except +
        const clean = phone.replace(/[^\d+]/g, '');
        
        // If already in international format with +
        if (clean.startsWith('+')) {
            return clean;
        }
        
        // Try to detect country from number pattern
        const detectedCountry = this.detectCountry(clean);
        if (detectedCountry) {
            defaultCountry = detectedCountry;
        }
        
        // Get country info
        const countryInfo = this.countryPatterns[defaultCountry] || this.countryPatterns['GH'];
        
        // Handle local format (starting with 0)
        if (clean.startsWith('0')) {
            const localNumber = clean.substring(1);
            return countryInfo.code + localNumber;
        }
        
        // Handle local format without leading 0
        if (clean.length <= 12) { // Reasonable phone number length
            return countryInfo.code + clean;
        }
        
        // If number is too long, assume it already has country code without +
        return '+' + clean;
    }
    
    /**
     * Detect country from phone number pattern
     * 
     * @param {string} cleanNumber - Clean numeric phone number
     * @returns {string|null} Country code if detected
     */
    static detectCountry(cleanNumber) {
        // First check if number starts with country code (most reliable)
        for (const [country, info] of Object.entries(this.countryPatterns)) {
            const codeDigits = info.code.substring(1); // Remove +
            if (cleanNumber.startsWith(codeDigits)) {
                return country;
            }
        }
        
        // Use intelligent scoring system for pattern matching
        const countryScores = {};

        for (const [country, info] of Object.entries(this.countryPatterns)) {
            if (info.pattern.test(cleanNumber)) {
                let score = 0;

                // Length matching score (basic validation)
                if (cleanNumber.length >= 7 && cleanNumber.length <= 15) {
                    score += 10;
                }

                // Specific country pattern bonuses

                // UK mobile numbers starting with 07 get highest priority
                if (country === 'GB' && /^0?7\d{9}$/.test(cleanNumber)) {
                    score += 20;
                }

                // Ghana numbers starting with 02-05 get priority
                if (country === 'GH' && /^0?[2-5]\d{8}$/.test(cleanNumber)) {
                    score += 15;
                }

                // Togo numbers starting with 9 get priority
                if (country === 'TG' && /^0?9\d{7}$/.test(cleanNumber)) {
                    score += 18;
                }

                // US/Canada numbers get priority for 10-digit numbers
                if ((country === 'US' || country === 'CA') && cleanNumber.length === 10) {
                    score += 12;
                }

                // Nigeria mobile numbers get priority
                if (country === 'NG' && /^0?[7-9]\d{9}$/.test(cleanNumber)) {
                    score += 16;
                }

                // Kenya mobile numbers get priority
                if (country === 'KE' && /^0?7\d{8}$/.test(cleanNumber)) {
                    score += 16;
                }

                // South Africa mobile numbers get priority
                if (country === 'ZA' && /^0?[6-8]\d{8}$/.test(cleanNumber)) {
                    score += 16;
                }

                // European mobile patterns
                if (country === 'DE' && /^0?1\d{9,11}$/.test(cleanNumber)) {
                    score += 14;
                }

                if (country === 'FR' && /^0?[6-7]\d{8}$/.test(cleanNumber)) {
                    score += 14;
                }

                // Asian mobile patterns
                if (country === 'IN' && /^0?[6-9]\d{9}$/.test(cleanNumber)) {
                    score += 14;
                }

                if (country === 'CN' && /^0?1\d{10}$/.test(cleanNumber)) {
                    score += 14;
                }

                // Australian mobile pattern
                if (country === 'AU' && /^0?4\d{8}$/.test(cleanNumber)) {
                    score += 14;
                }

                countryScores[country] = score;
            }
        }

        // Return the country with the highest score
        if (Object.keys(countryScores).length > 0) {
            return Object.keys(countryScores).reduce((a, b) =>
                countryScores[a] > countryScores[b] ? a : b
            );
        }

        return null;
    }
    
    /**
     * Format phone number for display
     * 
     * @param {string} normalizedPhone - Normalized phone number
     * @param {string} format - Display format ('international', 'national')
     * @returns {string} Formatted phone number
     */
    static formatForDisplay(normalizedPhone, format = 'international') {
        if (!normalizedPhone || !normalizedPhone.startsWith('+')) {
            return normalizedPhone;
        }
        
        switch (format) {
            case 'international':
                return this.formatInternational(normalizedPhone);
            case 'national':
                return this.formatNational(normalizedPhone);
            default:
                return normalizedPhone;
        }
    }
    
    /**
     * Format for international display
     * 
     * @param {string} normalizedPhone
     * @returns {string}
     */
    static formatInternational(normalizedPhone) {
        // Ghana: +233 24 412 3456
        if (normalizedPhone.startsWith('+233')) {
            const number = normalizedPhone.substring(4);
            if (number.length >= 9) {
                return `+233 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
            }
        }
        
        // US/Canada: +****************
        if (normalizedPhone.startsWith('+1')) {
            const number = normalizedPhone.substring(2);
            if (number.length >= 10) {
                return `+1 (${number.substring(0, 3)}) ${number.substring(3, 6)}-${number.substring(6)}`;
            }
        }
        
        // UK: +44 7123 456789
        if (normalizedPhone.startsWith('+44')) {
            const number = normalizedPhone.substring(3);
            if (number.length >= 10) {
                return `+44 ${number.substring(0, 4)} ${number.substring(4)}`;
            }
        }
        
        // Default: +XXX XXX XXX XXXX
        const match = normalizedPhone.match(/^(\+\d{1,4})(\d+)$/);
        if (match) {
            const [, code, number] = match;
            const formatted = number.replace(/(\d{3})/g, '$1 ').trim();
            return `${code} ${formatted}`;
        }
        
        return normalizedPhone;
    }
    
    /**
     * Format for national display
     * 
     * @param {string} normalizedPhone
     * @returns {string}
     */
    static formatNational(normalizedPhone) {
        // Convert back to national format
        for (const [country, info] of Object.entries(this.countryPatterns)) {
            if (normalizedPhone.startsWith(info.code)) {
                const localNumber = normalizedPhone.substring(info.code.length);
                return '0' + localNumber;
            }
        }
        
        return normalizedPhone;
    }
    
    /**
     * Validate phone number
     * 
     * @param {string} phone - Phone number to validate
     * @returns {object} Validation result with status and message
     */
    static validate(phone) {
        if (!phone || phone.trim() === '') {
            return { valid: false, message: 'Phone number is required' };
        }
        
        const normalized = this.normalize(phone);
        
        // Basic length check
        if (normalized.length < 8 || normalized.length > 17) {
            return { valid: false, message: 'Phone number length is invalid' };
        }
        
        // Must start with + after normalization
        if (!normalized.startsWith('+')) {
            return { valid: false, message: 'Unable to normalize phone number' };
        }
        
        return { valid: true, message: 'Valid phone number', normalized: normalized };
    }
    
    /**
     * Get validation feedback for UI
     * 
     * @param {string} phone - Phone number to validate
     * @returns {object} UI feedback object
     */
    static getValidationFeedback(phone) {
        const validation = this.validate(phone);
        
        if (!phone || phone.trim() === '') {
            return {
                status: 'neutral',
                message: '',
                icon: 'fas fa-phone',
                color: 'gray'
            };
        }
        
        if (validation.valid) {
            const formatted = this.formatForDisplay(validation.normalized);
            return {
                status: 'valid',
                message: `Will be saved as: ${formatted}`,
                icon: 'fas fa-check-circle',
                color: 'green'
            };
        } else {
            return {
                status: 'invalid',
                message: validation.message,
                icon: 'fas fa-exclamation-circle',
                color: 'red'
            };
        }
    }
    
    /**
     * Setup real-time validation for phone input fields
     * 
     * @param {string} inputId - ID of the phone input field
     * @param {string} feedbackId - ID of the feedback element (optional)
     */
    static setupRealTimeValidation(inputId, feedbackId = null) {
        const input = document.getElementById(inputId);
        if (!input) return;
        
        // Create feedback element if not provided
        let feedbackElement = null;
        if (feedbackId) {
            feedbackElement = document.getElementById(feedbackId);
        } else {
            feedbackElement = document.createElement('div');
            feedbackElement.className = 'text-xs mt-1 flex items-center';
            input.parentNode.appendChild(feedbackElement);
        }
        
        // Validation function
        const validateAndShowFeedback = () => {
            const feedback = this.getValidationFeedback(input.value);
            
            if (feedbackElement) {
                feedbackElement.innerHTML = `
                    <i class="${feedback.icon} text-${feedback.color}-500 mr-1"></i>
                    <span class="text-${feedback.color}-600">${feedback.message}</span>
                `;
                
                // Update input border color
                input.classList.remove('border-red-300', 'border-green-300', 'border-gray-300');
                if (feedback.status === 'valid') {
                    input.classList.add('border-green-300');
                } else if (feedback.status === 'invalid') {
                    input.classList.add('border-red-300');
                } else {
                    input.classList.add('border-gray-300');
                }
            }
        };
        
        // Add event listeners
        input.addEventListener('input', validateAndShowFeedback);
        input.addEventListener('blur', validateAndShowFeedback);
        
        // Initial validation if field has value
        if (input.value) {
            validateAndShowFeedback();
        }
    }
    
    /**
     * Check if two phone numbers are equivalent
     * 
     * @param {string} phone1
     * @param {string} phone2
     * @param {string} defaultCountry
     * @returns {boolean}
     */
    static areEqual(phone1, phone2, defaultCountry = 'GH') {
        const normalized1 = this.normalize(phone1, defaultCountry);
        const normalized2 = this.normalize(phone2, defaultCountry);
        
        return normalized1 === normalized2;
    }
}

// Auto-setup validation for common phone input fields when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Setup validation for main phone number field
    PhoneNumberUtils.setupRealTimeValidation('phone_number');
    
    // Setup validation for emergency contact phone field
    PhoneNumberUtils.setupRealTimeValidation('emergency_contact_phone');
    
    // Setup validation for any other phone fields
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        if (input.id !== 'phone_number' && input.id !== 'emergency_contact_phone') {
            PhoneNumberUtils.setupRealTimeValidation(input.id);
        }
    });
});

// Make PhoneNumberUtils available globally
window.PhoneNumberUtils = PhoneNumberUtils;
