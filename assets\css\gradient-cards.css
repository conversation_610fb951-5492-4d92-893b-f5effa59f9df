/*
 * ICGC Emmanuel Temple Gradient Cards
 * Beautiful gradient cards for the dashboard
 */

/* Base card styles */
.gradient-card {
    border-radius: 0.75rem 2rem 0.75rem 0.75rem;
    padding: 1.75rem;
    box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.15), 0 8px 16px -8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border-top: 1px solid rgba(255, 255, 255, 0.4);
    border-left: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    clip-path: polygon(0 0, 100% 0, 100% 85%, 85% 100%, 0 100%);
}

.gradient-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 30px -8px rgba(0, 0, 0, 0.2), 0 15px 20px -10px rgba(0, 0, 0, 0.1);
}

.gradient-card .card-icon {
    width: 4.5rem;
    height: 4.5rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.25rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    position: relative;
    z-index: 1;
    transform: rotate(-5deg);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.gradient-card .card-icon i {
    font-size: 2rem;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transform: rotate(5deg);
}

.gradient-card .card-title {
    font-size: 0.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.075em;
    position: relative;
    display: inline-block;
    padding-bottom: 0.5rem;
}

.gradient-card .card-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    height: 2px;
    width: 2rem;
    background-color: rgba(255, 255, 255, 0.5);
}

.gradient-card .card-value {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.75rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    line-height: 1;
}

.gradient-card .card-subtitle {
    font-size: 0.875rem;
    opacity: 0.9;
    font-weight: 500;
    position: absolute;
    bottom: 1.25rem;
    right: 1.75rem;
}

/* Blue Card (Total Members) */
.gradient-card.card-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    border-left: 4px solid #60a5fa !important;
}

.gradient-card.card-blue .card-icon {
    background-color: #60a5fa !important;
    box-shadow: 0 0 15px rgba(96, 165, 250, 0.5) !important;
    animation: pulse-blue 2s infinite;
}

@keyframes pulse-blue {
    0% {
        box-shadow: 0 0 0 0 rgba(96, 165, 250, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(96, 165, 250, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(96, 165, 250, 0);
    }
}

.gradient-card.card-blue .card-title,
.gradient-card.card-blue .card-value,
.gradient-card.card-blue .card-subtitle,
.gradient-card.card-blue .card-subtitle a {
    color: white !important;
}

/* Purple Card (Birthdays This Month) */
.gradient-card.card-purple {
    background: linear-gradient(135deg, #a855f7 0%, #7e22ce 100%) !important;
    border-left: 4px solid #c084fc !important;
}

.gradient-card.card-purple .card-icon {
    background-color: #c084fc !important;
    box-shadow: 0 0 15px rgba(192, 132, 252, 0.5) !important;
    animation: pulse-purple 2s infinite;
}

@keyframes pulse-purple {
    0% {
        box-shadow: 0 0 0 0 rgba(192, 132, 252, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(192, 132, 252, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(192, 132, 252, 0);
    }
}

.gradient-card.card-purple .card-title,
.gradient-card.card-purple .card-value,
.gradient-card.card-purple .card-subtitle,
.gradient-card.card-purple .card-subtitle a {
    color: white !important;
}

/* Green Card (Financial Balance) */
.gradient-card.card-green {
    background: linear-gradient(135deg, #22c55e 0%, #15803d 100%) !important;
    border-left: 4px solid #4ade80 !important;
}

.gradient-card.card-green .card-icon {
    background-color: #4ade80 !important;
    box-shadow: 0 0 15px rgba(74, 222, 128, 0.5) !important;
    animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(74, 222, 128, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(74, 222, 128, 0);
    }
}

.gradient-card.card-green .card-title,
.gradient-card.card-green .card-value,
.gradient-card.card-green .card-subtitle,
.gradient-card.card-green .card-subtitle a {
    color: white !important;
}

/* Yellow Card (New Members) */
.gradient-card.card-yellow {
    background: linear-gradient(135deg, #eab308 0%, #a16207 100%) !important;
    border-left: 4px solid #facc15 !important;
}

.gradient-card.card-yellow .card-icon {
    background-color: #facc15 !important;
    box-shadow: 0 0 15px rgba(250, 204, 21, 0.5) !important;
    animation: pulse-yellow 2s infinite;
}

@keyframes pulse-yellow {
    0% {
        box-shadow: 0 0 0 0 rgba(250, 204, 21, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(250, 204, 21, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(250, 204, 21, 0);
    }
}

.gradient-card.card-yellow .card-title,
.gradient-card.card-yellow .card-value,
.gradient-card.card-yellow .card-subtitle,
.gradient-card.card-yellow .card-subtitle a {
    color: white !important;
}

/* Orange Card (Platform Health) */
.gradient-card.card-orange {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
    border-left: 4px solid #fbbf24 !important;
}

.gradient-card.card-orange .card-icon {
    background-color: #fbbf24 !important;
    box-shadow: 0 0 15px rgba(251, 191, 36, 0.5) !important;
    animation: pulse-orange 2s infinite;
}

@keyframes pulse-orange {
    0% {
        box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(251, 191, 36, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(251, 191, 36, 0);
    }
}

.gradient-card.card-orange .card-title,
.gradient-card.card-orange .card-value,
.gradient-card.card-orange .card-subtitle,
.gradient-card.card-orange .card-subtitle a {
    color: white !important;
}

/* Red Card (Overdue Payments) */
.gradient-card.card-red {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
    border-left: 4px solid #ef4444 !important;
}

.gradient-card.card-red .card-icon {
    background-color: #ef4444 !important;
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.5) !important;
    animation: pulse-red 2s infinite;
}

@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

.gradient-card.card-red .card-title,
.gradient-card.card-red .card-value,
.gradient-card.card-red .card-subtitle,
.gradient-card.card-red .card-subtitle a {
    color: white !important;
}

/* Card shine effect */
.card-shine {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: translateX(-100%);
    pointer-events: none;
    z-index: 2;
    opacity: 0;
    transition: opacity 0.3s ease;
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.gradient-card:hover .card-shine {
    opacity: 1;
    animation: shine 1.5s ease-in-out;
}

/* Card background pattern */
.gradient-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 5%, transparent 15%),
        radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 10%, transparent 20%),
        radial-gradient(circle at 15% 85%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 10%, transparent 20%),
        radial-gradient(circle at 90% 90%, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 5%, transparent 15%),
        repeating-linear-gradient(45deg, rgba(255, 255, 255, 0.03) 0px, rgba(255, 255, 255, 0.03) 1px, transparent 1px, transparent 10px);
    opacity: 0.6;
    z-index: 0;
}

/* Card decorative corner */
.gradient-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.2);
    clip-path: polygon(100% 0, 0 0, 100% 100%);
    z-index: 0;
}

/* Hover effect for links */
.gradient-card .card-subtitle a {
    position: relative;
    z-index: 3;
    transition: all 0.2s ease;
}

.gradient-card .card-subtitle a:hover {
    text-decoration: none;
    opacity: 0.9;
}

.gradient-card .card-subtitle a:hover i {
    transform: translateX(3px);
}

.gradient-card .card-subtitle a i {
    transition: transform 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .gradient-card {
        padding: 1.25rem;
    }

    .gradient-card .card-value {
        font-size: 1.5rem;
    }
}

/* Tailwind CSS compatibility */
.bg-gradient-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.bg-gradient-purple {
    background: linear-gradient(135deg, #a855f7 0%, #7e22ce 100%);
}

.bg-gradient-green {
    background: linear-gradient(135deg, #22c55e 0%, #15803d 100%);
}

.bg-gradient-yellow {
    background: linear-gradient(135deg, #eab308 0%, #a16207 100%);
}

.border-blue-200 {
    border-color: #60a5fa;
}

.border-purple-200 {
    border-color: #c084fc;
}

.border-green-200 {
    border-color: #4ade80;
}

.border-yellow-200 {
    border-color: #facc15;
}

.bg-blue-500 {
    background-color: #60a5fa;
}

.bg-purple-500 {
    background-color: #c084fc;
}

.bg-green-500 {
    background-color: #4ade80;
}

.bg-yellow-500 {
    background-color: #facc15;
}

.text-blue-700 {
    color: white;
}

.text-purple-700 {
    color: white;
}

.text-green-700 {
    color: white;
}

.text-yellow-700 {
    color: white;
}
