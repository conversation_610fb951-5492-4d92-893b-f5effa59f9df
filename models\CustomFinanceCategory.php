<?php
/**
 * Custom Finance Category Model
 * Handles CRUD operations for custom finance categories
 */

class CustomFinanceCategory {
    // Database connection and table name
    private $conn;
    private $table_name = "custom_finance_categories";

    // Object properties
    public $id;
    public $name;
    public $slug;
    public $label;
    public $icon;
    public $description;
    public $category_type;
    public $requires_member;
    public $is_active;
    public $sort_order;
    public $created_by;
    public $created_at;
    public $updated_at;

    // Error handling
    public $error;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all custom categories
     *
     * @param bool $activeOnly
     * @return array
     */
    public function getAll($activeOnly = true) {
        $whereClause = $activeOnly ? "WHERE is_active = 1" : "";
        
        $query = "SELECT * FROM " . $this->table_name . " 
                  $whereClause 
                  ORDER BY category_type, sort_order, label";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }

    /**
     * Get categories by type
     *
     * @param string $categoryType
     * @param bool $activeOnly
     * @return array
     */
    public function getByType($categoryType, $activeOnly = true) {
        $whereClause = "WHERE category_type = :category_type";
        if ($activeOnly) {
            $whereClause .= " AND is_active = 1";
        }
        
        $query = "SELECT * FROM " . $this->table_name . " 
                  $whereClause 
                  ORDER BY sort_order, label";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':category_type', $categoryType);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }

    /**
     * Get category by ID
     *
     * @param int $id
     * @return object|false
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_OBJ);
    }



    /**
     * Create new custom category with validation
     *
     * @return bool
     */
    public function create() {
        // CRITICAL: Validate data before creating
        if (!$this->validate()) {
            // The 'error' property is set by the validate() method
            return false;
        }
        // Generate slug from name
        $this->slug = $this->generateSlug($this->name);

        $query = "INSERT INTO " . $this->table_name . "
                  (name, slug, label, icon, description, category_type, requires_member, sort_order, created_by)
                  VALUES
                  (:name, :slug, :label, :icon, :description, :category_type, :requires_member, :sort_order, :created_by)";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->label = htmlspecialchars(strip_tags($this->label));
        $this->icon = htmlspecialchars(strip_tags($this->icon));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->category_type = htmlspecialchars(strip_tags($this->category_type));
        $this->requires_member = $this->requires_member ? 1 : 0;
        $this->sort_order = $this->sort_order ?: 0;

        // Bind data
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':slug', $this->slug);
        $stmt->bindParam(':label', $this->label);
        $stmt->bindParam(':icon', $this->icon);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':category_type', $this->category_type);
        $stmt->bindParam(':requires_member', $this->requires_member);
        $stmt->bindParam(':sort_order', $this->sort_order);
        $stmt->bindParam(':created_by', $this->created_by);

        try {
            if ($stmt->execute()) {
                $this->id = $this->conn->lastInsertId();
                return true;
            } else {
                $this->error = 'Failed to create category record.';
                return false;
            }
        } catch (PDOException $e) {
            $this->error = 'Database error occurred while creating category.';
            error_log("CustomFinanceCategory creation failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update custom category with validation
     *
     * @return bool
     */
    public function update() {
        // CRITICAL: Validate data before updating
        if (!$this->validate()) {
            // The 'error' property is set by the validate() method
            return false;
        }
        $query = "UPDATE " . $this->table_name . "
                  SET name = :name,
                      label = :label,
                      icon = :icon,
                      description = :description,
                      category_type = :category_type,
                      requires_member = :requires_member,
                      sort_order = :sort_order,
                      is_active = :is_active,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->label = htmlspecialchars(strip_tags($this->label));
        $this->icon = htmlspecialchars(strip_tags($this->icon));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->category_type = htmlspecialchars(strip_tags($this->category_type));
        $this->requires_member = $this->requires_member ? 1 : 0;
        $this->is_active = $this->is_active ? 1 : 0;
        $this->sort_order = $this->sort_order ?: 0;

        // Bind data
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':label', $this->label);
        $stmt->bindParam(':icon', $this->icon);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':category_type', $this->category_type);
        $stmt->bindParam(':requires_member', $this->requires_member);
        $stmt->bindParam(':sort_order', $this->sort_order);
        $stmt->bindParam(':is_active', $this->is_active);

        try {
            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Failed to update category record.';
                return false;
            }
        } catch (PDOException $e) {
            $this->error = 'Database error occurred while updating category.';
            error_log("CustomFinanceCategory update failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete custom category (hard delete with safety checks)
     *
     * @return bool
     */
    public function delete() {
        try {
            // Clear any previous errors
            $this->error = null;

            // First get the category name
            $categoryQuery = "SELECT name FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($categoryQuery);
            $stmt->bindParam(':id', $this->id);
            $stmt->execute();
            $categoryResult = $stmt->fetch(PDO::FETCH_OBJ);

            if (!$categoryResult) {
                $this->error = 'Finance category not found.';
                error_log("Category not found for deletion: ID " . $this->id);
                return false;
            }

            // Check if this category has any associated transactions
            $transactionCheck = "SELECT COUNT(*) as count FROM finances WHERE category = :category_name";
            $stmt = $this->conn->prepare($transactionCheck);
            $stmt->bindParam(':category_name', $categoryResult->name);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_OBJ);

            if ($result && $result->count > 0) {
                // Category has transactions, do soft delete instead
                $query = "UPDATE " . $this->table_name . "
                          SET is_active = 0,
                              updated_at = CURRENT_TIMESTAMP
                          WHERE id = :id";

                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':id', $this->id);

                $success = $stmt->execute();
                if ($success) {
                    $this->error = 'Category has ' . $result->count . ' transaction(s). Category deactivated instead of deleted to preserve financial data.';
                    error_log("Soft delete successful for category ID: " . $this->id . " (had " . $result->count . " transactions)");
                } else {
                    $this->error = 'Error deactivating category. Please try again.';
                    error_log("Soft delete failed for category ID: " . $this->id . " - " . implode(", ", $stmt->errorInfo()));
                }
                return $success;
            } else {
                // No transactions, safe to hard delete
                $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':id', $this->id);

                $success = $stmt->execute();
                if ($success) {
                    // No error message needed for successful deletion
                    error_log("Hard delete successful for category ID: " . $this->id . " (no transactions)");
                } else {
                    $this->error = 'Error deleting category. Please try again.';
                    error_log("Hard delete failed for category ID: " . $this->id . " - " . implode(", ", $stmt->errorInfo()));
                }
                return $success;
            }

        } catch (Exception $e) {
            $this->error = 'Database error: ' . $e->getMessage();
            error_log("Error deleting custom finance category: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if category name exists
     *
     * @param string $name
     * @param int $excludeId
     * @return bool
     */
    public function nameExists($name, $excludeId = null) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE name = :name";
        
        if ($excludeId) {
            $query .= " AND id != :exclude_id";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':name', $name);
        
        if ($excludeId) {
            $stmt->bindParam(':exclude_id', $excludeId);
        }

        $stmt->execute();
        return $stmt->rowCount() > 0;
    }

    /**
     * Get next sort order for category type
     *
     * @param string $categoryType
     * @return int
     */
    public function getNextSortOrder($categoryType) {
        $query = "SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order 
                  FROM " . $this->table_name . " 
                  WHERE category_type = :category_type";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':category_type', $categoryType);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_OBJ);
        return $result ? $result->next_order : 1;
    }

    /**
     * Validate category data before create/update operations (Model-level validation)
     *
     * @return bool True if validation passes, false otherwise
     */
    public function validate() {
        $this->error = null;
        $errors = [];

        // Required fields validation
        if (empty($this->name) || trim($this->name) === '') {
            $errors[] = 'Category name is required.';
        } elseif (strlen($this->name) > 100) {
            $errors[] = 'Category name must be 100 characters or less.';
        }

        if (empty($this->label) || trim($this->label) === '') {
            $errors[] = 'Category label is required.';
        } elseif (strlen($this->label) > 150) {
            $errors[] = 'Category label must be 150 characters or less.';
        }

        if (empty($this->category_type)) {
            $errors[] = 'Category type is required.';
        } elseif (!in_array($this->category_type, ['member_payments', 'general_income', 'expenses'])) {
            $errors[] = 'Invalid category type. Must be one of: member_payments, general_income, expenses';
        }

        // Description length validation (if provided)
        if (!empty($this->description) && strlen($this->description) > 500) {
            $errors[] = 'Description cannot exceed 500 characters.';
        }

        // Sort order validation (if provided)
        if (!empty($this->sort_order) && (!is_numeric($this->sort_order) || $this->sort_order < 0)) {
            $errors[] = 'Sort order must be a non-negative number.';
        }

        // Check for duplicate name
        if (!empty($this->name)) {
            $excludeId = !empty($this->id) ? $this->id : null;
            if ($this->nameExists(trim($this->name), $excludeId)) {
                $errors[] = 'A category with this name already exists.';
            }
        }

        if (!empty($errors)) {
            $this->error = implode(' ', $errors);
            return false;
        }

        return true;
    }

    /**
     * Get categories formatted for select options
     *
     * @param string $categoryType
     * @return array
     */
    public function getSelectOptions($categoryType) {
        $categories = $this->getByType($categoryType);
        $options = [];

        foreach ($categories as $category) {
            $options[$category->name] = [
                'label' => $category->label,
                'requires_member' => (bool)$category->requires_member,
                'description' => $category->description,
                'icon' => $category->icon
            ];
        }

        return $options;
    }

    /**
     * Generate URL-friendly slug from name
     *
     * @param string $name
     * @return string
     */
    private function generateSlug($name) {
        // Convert to lowercase and replace spaces/underscores with hyphens
        $slug = strtolower(trim($name));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');

        // Ensure slug is unique
        $originalSlug = $slug;
        $counter = 1;

        do {
            $checkQuery = "SELECT id FROM " . $this->table_name . " WHERE slug = ?";
            $checkStmt = $this->conn->prepare($checkQuery);
            $checkStmt->execute([$slug]);

            if ($checkStmt->rowCount() > 0) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            } else {
                break;
            }
        } while (true);

        return $slug;
    }
}
