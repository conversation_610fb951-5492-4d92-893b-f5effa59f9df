<?php
/**
 * QR Sessions Archive Page
 * Displays all QR sessions with filtering and analytics
 */

// Helper function for status badges
function getStatusBadge($status) {
    $badges = [
        'active' => 'bg-green-100 text-green-800',
        'expired' => 'bg-yellow-100 text-yellow-800',
        'closed' => 'bg-blue-100 text-blue-800',
        'archived' => 'bg-gray-100 text-gray-800'
    ];
    $class = $badges[$status] ?? 'bg-gray-100 text-gray-800';
    return "<span class='px-2 py-1 text-xs font-medium rounded-full $class'>" . ucfirst($status) . "</span>";
}

// Get current filter
$current_status = isset($_GET['status']) ? $_GET['status'] : '';
$current_limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
?>

<div class="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-primary px-6 py-4">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-xl font-semibold text-white">QR Sessions Archive</h1>
                    <p class="text-primary-light text-sm">Historical QR attendance sessions and analytics</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance/qr" class="text-white bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded-md text-sm flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i> Back to QR Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-qrcode text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Sessions</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($summary_stats['total_sessions']); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-users text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total QR Attendance</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($summary_stats['total_qr_attendance']); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg per Session</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($summary_stats['avg_attendance_per_session'], 1); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-calendar text-orange-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Last 30 Days</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($summary_stats['sessions_last_30_days']); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Distribution -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Session Status Distribution</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600"><?php echo $summary_stats['active_sessions']; ?></div>
                <div class="text-sm text-gray-600">Active</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600"><?php echo $summary_stats['expired_sessions']; ?></div>
                <div class="text-sm text-gray-600">Expired</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600"><?php echo $summary_stats['closed_sessions']; ?></div>
                <div class="text-sm text-gray-600">Closed</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-600"><?php echo $summary_stats['archived_sessions']; ?></div>
                <div class="text-sm text-gray-600">Archived</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
        <form method="GET" action="<?php echo BASE_URL; ?>attendance/qr-sessions-archive" class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-48">
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" id="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary">
                    <option value="">All Statuses</option>
                    <option value="active" <?php echo $current_status === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="expired" <?php echo $current_status === 'expired' ? 'selected' : ''; ?>>Expired</option>
                    <option value="closed" <?php echo $current_status === 'closed' ? 'selected' : ''; ?>>Closed</option>
                    <option value="archived" <?php echo $current_status === 'archived' ? 'selected' : ''; ?>>Archived</option>
                </select>
            </div>
            <div class="flex-1 min-w-32">
                <label for="limit" class="block text-sm font-medium text-gray-700 mb-1">Limit</label>
                <select name="limit" id="limit" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary">
                    <option value="25" <?php echo $current_limit === 25 ? 'selected' : ''; ?>>25</option>
                    <option value="50" <?php echo $current_limit === 50 ? 'selected' : ''; ?>>50</option>
                    <option value="100" <?php echo $current_limit === 100 ? 'selected' : ''; ?>>100</option>
                    <option value="200" <?php echo $current_limit === 200 ? 'selected' : ''; ?>>200</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary">
                    <i class="fas fa-filter mr-2"></i> Apply Filters
                </button>
            </div>
            <?php if ($current_status || $current_limit !== 50): ?>
            <div class="flex items-end">
                <a href="<?php echo BASE_URL; ?>attendance/qr-sessions-archive" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    <i class="fas fa-times mr-2"></i> Clear
                </a>
            </div>
            <?php endif; ?>
        </form>
    </div>

    <!-- Sessions Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                QR Sessions 
                <?php if ($current_status): ?>
                    <span class="text-sm font-normal text-gray-600">(<?php echo ucfirst($current_status); ?> only)</span>
                <?php endif; ?>
                <span class="text-sm font-normal text-gray-600">(<?php echo count($sessions); ?> sessions)</span>
            </h3>
        </div>
        
        <?php if (empty($sessions)): ?>
            <div class="p-6 text-center text-gray-500">
                <i class="fas fa-archive text-4xl mb-4"></i>
                <p>No QR sessions found matching your criteria.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service & Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Efficiency</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($sessions as $session): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($session['service_name']); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo format_date($session['attendance_date']); ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php echo getStatusBadge($session['status']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo $session['attendance_count'] ?? 0; ?> members
                                    </div>
                                    <?php if ($session['last_used_at']): ?>
                                        <div class="text-sm text-gray-500">
                                            Last: <?php echo date('g:i A', strtotime($session['last_used_at'])); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <?php echo $session['session_duration_minutes']; ?> min
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        Active: <?php echo $session['active_duration_minutes']; ?> min
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo $session['attendance_per_hour']; ?>/hr
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($session['created_by_name'] ?? 'Unknown'); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="<?php echo BASE_URL; ?>attendance/qr-session-details?session_id=<?php echo $session['id']; ?>" 
                                           class="text-primary hover:text-primary-dark">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>attendance/qr-session-export?session_id=<?php echo $session['id']; ?>" 
                                           class="text-green-600 hover:text-green-800">
                                            <i class="fas fa-download"></i> Export
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>
