<?php
/**
 * Edit Child View
 */

// Extract child information (controller ensures this data exists)
$child = $child_info['child'] ?? null;
$parents = $child_info['parents'] ?? []; // Church member parents
$guardians = $child_info['guardians'] ?? []; // Non-member guardians
$all_guardians = $child_info['all_guardians'] ?? []; // Combined list
$medical_info = $child_info['medical_info'] ?? null;

// Get primary guardian from all sources (prioritize guardians, then parents)
$primary_parent = [];

// First, look for primary guardian in guardian_contacts
foreach ($guardians as $guardian) {
    if ($guardian['is_primary']) {
        $primary_parent = $guardian;
        break;
    }
}

// If no primary guardian found, look for primary parent in family_relationships
if (empty($primary_parent)) {
    foreach ($parents as $parent) {
        if ($parent['is_primary']) {
            $primary_parent = $parent;
            break;
        }
    }
}

// If still no primary found, use the first available guardian or parent
if (empty($primary_parent)) {
    if (!empty($guardians)) {
        $primary_parent = $guardians[0];
    } elseif (!empty($parents)) {
        $primary_parent = $parents[0];
    }
}

// Calculate age
$age = 0;
if ($child && $child['date_of_birth']) {
    $birthDate = new DateTime($child['date_of_birth']);
    $today = new DateTime();
    $age = $today->diff($birthDate)->y;
}
?>

<div class="max-w-4xl mx-auto px-4 py-8">
    <!-- Success/Error Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span><?php echo htmlspecialchars($_SESSION['success_message']); ?></span>
            </div>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span><?php echo htmlspecialchars($_SESSION['error_message']); ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>

    <!-- Page Header -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-edit text-xl text-blue-600"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">
                        Edit Child Information
                    </h1>
                    <p class="text-gray-600">Update details for <?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?></p>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo BASE_URL; ?>children-ministry/view-child?id=<?php echo $child['id']; ?>"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Details
                </a>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="bg-blue-50 px-6 py-4 border-b border-blue-200">
            <h3 class="text-lg font-semibold text-blue-900">Child Information</h3>
        </div>
        
        <form method="POST" action="<?php echo BASE_URL; ?>children-ministry/update-child" class="p-6">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="id" value="<?php echo htmlspecialchars($child['id']); ?>">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- First Name -->
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                        First Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="first_name" 
                           name="first_name" 
                           value="<?php echo htmlspecialchars($child['first_name']); ?>"
                           required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Last Name -->
                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Last Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="last_name" 
                           name="last_name" 
                           value="<?php echo htmlspecialchars($child['last_name']); ?>"
                           required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Date of Birth -->
                <div>
                    <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">
                        Date of Birth <span class="text-red-500">*</span>
                    </label>
                    <input type="date"
                           id="date_of_birth"
                           name="date_of_birth"
                           value="<?php echo htmlspecialchars($child['date_of_birth']); ?>"
                           max="<?php echo date('Y-m-d', strtotime('-1 year')); ?>"
                           required
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <div id="age-validation-message" class="hidden mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                            <span class="text-red-700 text-sm">
                                <strong>Age Requirement:</strong> Children must be 17 years old or younger to register for children's ministry programs.
                            </span>
                        </div>
                    </div>
                    <div id="age-display" class="hidden mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span class="text-blue-700 text-sm" id="age-text"></span>
                        </div>
                    </div>
                </div>

                <!-- Gender -->
                <div>
                    <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">
                        Gender <span class="text-red-500">*</span>
                    </label>
                    <select id="gender"
                            name="gender"
                            required
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select Gender</option>
                        <option value="male" <?php echo $child['gender'] === 'male' ? 'selected' : ''; ?>>Male</option>
                        <option value="female" <?php echo $child['gender'] === 'female' ? 'selected' : ''; ?>>Female</option>
                    </select>
                </div>

                <!-- School -->
                <div>
                    <label for="school" class="block text-sm font-medium text-gray-700 mb-2">
                        School
                    </label>
                    <input type="text"
                           id="school"
                           name="school"
                           value="<?php echo htmlspecialchars($child['school'] ?? ''); ?>"
                           placeholder="Enter school name"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Primary Guardian Information Section -->
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div class="bg-green-50 px-6 py-4 border-b border-green-200">
                <h3 class="text-lg font-semibold text-green-900 flex items-center">
                    <i class="fas fa-user-shield mr-2"></i>
                    Primary Parent/Guardian Information
                </h3>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-2">
                        <label for="guardian_first_name" class="block text-sm font-medium text-gray-900">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="guardian_first_name" name="guardian_first_name" required
                               value="<?php echo htmlspecialchars($primary_parent['first_name'] ?? ''); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="space-y-2">
                        <label for="guardian_last_name" class="block text-sm font-medium text-gray-900">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="guardian_last_name" name="guardian_last_name" required
                               value="<?php echo htmlspecialchars($primary_parent['last_name'] ?? ''); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="space-y-2">
                        <label for="guardian_phone" class="block text-sm font-medium text-gray-900">
                            Phone Number <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" id="guardian_phone" name="guardian_phone" required
                               value="<?php echo htmlspecialchars($primary_parent['phone_number'] ?? ''); ?>"
                               placeholder="(*************"
                               class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="space-y-2">
                        <label for="guardian_email" class="block text-sm font-medium text-gray-900">
                            Email Address
                        </label>
                        <input type="email" id="guardian_email" name="guardian_email"
                               value="<?php echo htmlspecialchars($primary_parent['email'] ?? ''); ?>"
                               placeholder="<EMAIL>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="space-y-2">
                        <label for="guardian_relationship" class="block text-sm font-medium text-gray-900">
                            Relationship to Child <span class="text-red-500">*</span>
                        </label>
                        <select id="guardian_relationship" name="guardian_relationship" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Relationship</option>
                            <?php
                            // Handle different field names for relationship type
                            $relationship = $primary_parent['relationship_type'] ?? $primary_parent['relationship_to_child'] ?? '';
                            ?>
                            <option value="parent" <?php echo $relationship === 'parent' ? 'selected' : ''; ?>>Parent</option>
                            <option value="guardian" <?php echo $relationship === 'guardian' ? 'selected' : ''; ?>>Legal Guardian</option>
                            <option value="grandparent" <?php echo $relationship === 'grandparent' ? 'selected' : ''; ?>>Grandparent</option>
                            <option value="other" <?php echo $relationship === 'other' ? 'selected' : ''; ?>>Other</option>
                        </select>
                    </div>

                    <div class="space-y-2">
                        <label for="guardian_occupation" class="block text-sm font-medium text-gray-900">
                            Occupation
                        </label>
                        <input type="text" id="guardian_occupation" name="guardian_occupation"
                               value="<?php echo htmlspecialchars($primary_parent['occupation'] ?? ''); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="md:col-span-2 space-y-2">
                        <label for="guardian_address" class="block text-sm font-medium text-gray-900">
                            Home Address
                        </label>
                        <textarea id="guardian_address" name="guardian_address" rows="3"
                                  placeholder="Street address, city, state, zip code"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"><?php
                                  // Handle different field names for address
                                  echo htmlspecialchars($primary_parent['address'] ?? $primary_parent['location'] ?? '');
                                  ?></textarea>
                    </div>
                </div>

            </div>
        </div>

        <!-- Submit Section -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
                <div class="text-sm text-gray-600">
                    <p><span class="text-red-500">*</span> Required fields</p>
                    <p>All information will be kept confidential and secure.</p>
                </div>

                <div class="flex space-x-4">
                    <a href="<?php echo BASE_URL; ?>children-ministry/view-child?id=<?php echo $child['id']; ?>"
                       class="px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                        Cancel
                    </a>
                    <button type="submit"
                            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        Update Child & Guardian
                    </button>
                </div>
            </div>
        </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Age calculation and validation
    const dobInput = document.getElementById('date_of_birth');
    const ageValidationMessage = document.getElementById('age-validation-message');
    const ageDisplay = document.getElementById('age-display');
    const ageText = document.getElementById('age-text');

    function validateAge() {
        const dateValue = dobInput.value;

        // Hide all messages first
        ageValidationMessage.classList.add('hidden');
        ageDisplay.classList.add('hidden');
        dobInput.classList.remove('border-red-500');

        // Only validate if we have a complete date
        if (!dateValue) {
            return true;
        }

        const birthDate = new Date(dateValue);
        const today = new Date();

        // Check if the date is valid
        if (isNaN(birthDate.getTime())) {
            return true; // Let browser handle invalid date format
        }

        // Calculate age
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }

        // Show age information
        if (age >= 0 && age <= 17) {
            ageText.textContent = `Child's age: ${age} years old - Eligible for children's ministry`;
            ageDisplay.classList.remove('hidden');
            return true;
        } else if (age > 17) {
            ageValidationMessage.classList.remove('hidden');
            dobInput.classList.add('border-red-500');
            return false;
        } else {
            // Future date
            ageValidationMessage.classList.remove('hidden');
            ageValidationMessage.querySelector('span').innerHTML = '<strong>Invalid Date:</strong> Please enter a valid birth date.';
            dobInput.classList.add('border-red-500');
            return false;
        }
    }

    // Validate on change (when user finishes selecting date)
    dobInput.addEventListener('change', validateAge);

    // Also validate on blur (when user leaves the field)
    dobInput.addEventListener('blur', validateAge);

    // Initial validation on page load
    validateAge();

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        // Check age validation first
        if (!validateAge()) {
            e.preventDefault();
            dobInput.focus();
            return false;
        }
    });
});
</script>
