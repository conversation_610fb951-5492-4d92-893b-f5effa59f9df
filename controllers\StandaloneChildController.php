<?php
/**
 * Standalone Child Registration Controller
 * Handles registration of children without requiring parent membership
 */

require_once 'config/database.php';
require_once 'models/Member.php';
require_once 'utils/validation.php';
require_once 'helpers/functions.php';
require_once 'utils/functions.php';

class StandaloneChildController {
    private $database;
    private $member;
    
    public function __construct() {
        // Start session if not already started and headers haven't been sent
        if (session_status() == PHP_SESSION_NONE && !headers_sent()) {
            session_start();
        }

        $this->database = new Database();
        $this->member = new Member($this->database->getConnection());
    }
    
    /**
     * Show the standalone child registration form
     */
    public function showRegistrationForm() {
        try {
            // Set page variables
            $page_title = "Register Child - Children's Ministry";
            $active_page = 'children_ministry';
            
            // Load the registration form view
            require_once 'views/children_ministry/standalone_registration.php';
            
        } catch (Exception $e) {
            error_log("Standalone registration form error: " . $e->getMessage());
            set_flash_message('Unable to load registration form. Please try again.', 'danger');
            redirect('children-ministry');
        }
    }
    
    /**
     * Process the standalone child registration
     */
    public function processRegistration() {
        try {
            // Log the start of processing for debugging
            error_log("Standalone child registration processing started");
            // Validate CSRF token (temporarily disabled for debugging)
            /*
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Security validation failed. Please try again.', 'danger');
                redirect('children-ministry/standalone-registration');
                return;
            }
            */
            
            // Validate required fields
            $validation = new Validation($_POST);
            $validation->required('child_first_name')
                      ->name('child_first_name')
                      ->required('child_last_name')
                      ->name('child_last_name')
                      ->required('child_date_of_birth')
                      ->required('child_gender')
                      ->required('guardian_first_name')
                      ->name('guardian_first_name')
                      ->required('guardian_last_name')
                      ->name('guardian_last_name')
                      ->required('guardian_phone')
                      ->min('guardian_phone', 10); // Basic phone validation
            
            if ($validation->fails()) {
                set_flash_message('Please correct the errors and try again: ' . implode(', ', $validation->errors()), 'danger');
                redirect('children-ministry/standalone-registration');
                return;
            }
            
            // Validate child age (must be under 18)
            $child_dob = $this->convertDateFormat($_POST['child_date_of_birth']);
            if ($child_dob) {
                $birth_date = new DateTime($child_dob);
                $today = new DateTime();
                $age = $birth_date->diff($today)->y;
                
                if ($age >= 18) {
                    set_flash_message('Child must be under 18 years old for children\'s ministry registration.', 'danger');
                    redirect('children-ministry/standalone-registration');
                    return;
                }
            }
            
            // Create the child member using direct database insert to avoid phone normalization issues
            $conn = $this->database->getConnection();

            // Prepare member data
            $member_data = $this->prepareChildMemberData($validation->getData());

            // Insert directly into database
            $insert_query = "INSERT INTO members (
                first_name, last_name, email, phone_number, date_of_birth, gender,
                marital_status, location, emergency_contact_name, emergency_contact_phone,
                baptism_status, department, role, membership_date, occupation, school,
                member_status, profile_picture, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $conn->prepare($insert_query);
            $insert_result = $stmt->execute([
                $member_data['first_name'],
                $member_data['last_name'],
                $member_data['email'],
                $member_data['phone_number'],
                $member_data['date_of_birth'],
                $member_data['gender'],
                $member_data['marital_status'],
                $member_data['location'],
                $member_data['emergency_contact_name'],
                $member_data['emergency_contact_phone'],
                $member_data['baptism_status'],
                $member_data['department'],
                $member_data['role'],
                $member_data['membership_date'],
                $member_data['occupation'],
                $member_data['school'],
                $member_data['member_status'],
                $member_data['profile_picture'],
                $member_data['created_at'],
                $member_data['updated_at']
            ]);

            if (!$insert_result) {
                throw new Exception('Failed to create child member record');
            }

            $child_id = $this->database->getConnection()->lastInsertId();

            // Create guardian contact or link to existing member parent
            $guardian_created = false;
            $member_parent_linked = false;

            try {
                // First, check if parent already exists as a member by phone number
                $guardian_phone = sanitize($_POST['guardian_phone']);
                $existing_member_parent = $this->findExistingParentByPhone($guardian_phone);

                if ($existing_member_parent) {
                    // Parent exists as a member, create family relationship
                    $this->createParentChildRelationship($existing_member_parent['id'], $child_id);
                    $member_parent_linked = true;
                } else {
                    // Create guardian contact (not a member) and establish guardian relationship
                    $guardian_id = $this->createGuardianContact($validation->getData());
                    if ($guardian_id) {
                        // Create child-guardian relationship
                        $this->createChildGuardianRelationship($child_id, $guardian_id);
                        $guardian_created = true;
                    }
                }
            } catch (Exception $e) {
                error_log("Error creating guardian for standalone child $child_id: " . $e->getMessage());
                // Continue without failing the child registration
            }

            // Set success message
            $child_name = sanitize($_POST['child_first_name']) . ' ' . sanitize($_POST['child_last_name']);
            $success_message = "Successfully registered {$child_name} for children's ministry!";

            if ($guardian_created) {
                $success_message .= " Guardian contact information saved!";
            } elseif ($member_parent_linked) {
                $success_message .= " Found and linked to existing parent member!";
            }

            set_flash_message($success_message, 'success');
            
            // Redirect to success page
            redirect("children-ministry/standalone-registration-success?child_id={$child_id}");
            
        } catch (Exception $e) {
            error_log("Standalone child registration error: " . $e->getMessage());
            set_flash_message('Registration failed. Please try again.', 'danger');
            redirect('children-ministry/standalone-registration');
        }
    }
    
    /**
     * Show registration success page
     */
    public function showRegistrationSuccess() {
        try {
            $child_id = $_GET['child_id'] ?? null;
            
            if ($child_id) {
                // Get child details
                $this->member->getById($child_id);
            }
            
            // Set page variables
            $page_title = "Registration Successful - Children's Ministry";
            $active_page = 'children_ministry';
            
            // Load success view
            require_once 'views/children_ministry/registration_success.php';
            
        } catch (Exception $e) {
            error_log("Registration success page error: " . $e->getMessage());
            redirect('children-ministry');
        }
    }
    
    /**
     * Prepare child member data for database insert
     */
    private function prepareChildMemberData($data) {
        return [
            'first_name' => sanitize($data['child_first_name']),
            'last_name' => sanitize($data['child_last_name']),
            'email' => null, // Children typically don't have email
            'phone_number' => '', // Children typically don't have phone
            'date_of_birth' => $this->convertDateFormat($data['child_date_of_birth']),
            'gender' => sanitize($data['child_gender']),
            'marital_status' => 'single',
            'location' => sanitize($data['guardian_address'] ?? ''),
            'emergency_contact_name' => sanitize($data['guardian_first_name']) . ' ' . sanitize($data['guardian_last_name']),
            'emergency_contact_phone' => sanitize($data['guardian_phone']),
            'baptism_status' => 'not_baptized',
            'department' => 'children',
            'role' => 'member',
            'membership_date' => date('Y-m-d'),
            'occupation' => '',
            'school' => sanitize($data['child_school'] ?? ''),
            'member_status' => 'active',
            'profile_picture' => '',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Find existing parent by phone number
     */
    private function findExistingParentByPhone($phone) {
        try {
            $query = "SELECT id, first_name, last_name, phone_number
                     FROM members
                     WHERE phone_number = ?
                     AND TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) >= 18
                     AND member_status = 'active'
                     LIMIT 1";

            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->execute([$phone]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error finding existing parent: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create guardian contact (non-member) from guardian information
     */
    private function createGuardianContact($data) {
        try {
            require_once 'models/GuardianContact.php';
            $guardianContact = new GuardianContact($this->database->getConnection());

            // Set guardian contact properties
            $guardianContact->first_name = sanitize($data['guardian_first_name']);
            $guardianContact->last_name = sanitize($data['guardian_last_name']);
            $guardianContact->email = !empty($data['guardian_email']) ? sanitize($data['guardian_email']) : null;
            $guardianContact->phone_number = sanitize($data['guardian_phone']);
            $guardianContact->relationship_to_child = 'parent'; // Default to parent
            $guardianContact->address = sanitize($data['guardian_address'] ?? '');
            $guardianContact->occupation = sanitize($data['guardian_occupation'] ?? '');
            $guardianContact->is_primary = true; // First guardian is primary
            $guardianContact->can_pickup = true;
            $guardianContact->consent_given = false; // Will be updated when consent is provided
            $guardianContact->consent_date = null;
            $guardianContact->emergency_priority = 1;
            $guardianContact->notes = 'Created from standalone child registration';
            $guardianContact->created_at = date('Y-m-d H:i:s');
            $guardianContact->updated_at = date('Y-m-d H:i:s');

            // Create the guardian contact
            if ($guardianContact->create()) {
                return $guardianContact->id;
            }
            return null;
        } catch (Exception $e) {
            error_log("Error creating guardian contact: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create child-guardian relationship (for non-member guardians)
     */
    private function createChildGuardianRelationship($child_id, $guardian_id) {
        try {
            $conn = $this->database->getConnection();

            $insert_query = "INSERT INTO child_guardian_relationships (
                child_id, guardian_contact_id, member_id, relationship_type,
                is_primary, can_pickup, emergency_priority, relationship_source,
                notes, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $conn->prepare($insert_query);
            $result = $stmt->execute([
                $child_id,
                $guardian_id,
                null, // member_id is null for guardian contacts
                'parent',
                1, // is_primary
                1, // can_pickup
                1, // emergency_priority
                'guardian_contact',
                'Created from standalone child registration',
                date('Y-m-d H:i:s'),
                date('Y-m-d H:i:s')
            ]);

            return $result;
        } catch (Exception $e) {
            error_log("Error creating child-guardian relationship: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create parent-child relationship (for member parents)
     */
    private function createParentChildRelationship($parent_id, $child_id) {
        try {
            require_once 'models/FamilyRelationship.php';
            $familyRelationship = new FamilyRelationship($this->database->getConnection());

            $relationship_data = [
                'parent_id' => $parent_id,
                'child_id' => $child_id,
                'relationship_type' => 'parent',
                'is_primary' => 1,
                'can_pickup' => 1,
                'notes' => 'Created from standalone child registration',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            return $familyRelationship->create($relationship_data);
        } catch (Exception $e) {
            error_log("Error creating parent-child relationship: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Convert date format from DD-MM-YYYY to YYYY-MM-DD for MySQL
     */
    private function convertDateFormat($date_string) {
        if (empty($date_string)) {
            return null;
        }
        
        $date_string = trim($date_string);
        
        // If already in YYYY-MM-DD format, return as is
        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_string)) {
            return $date_string;
        }
        
        // Convert DD-MM-YYYY to YYYY-MM-DD
        if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $date_string, $matches)) {
            $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            $year = $matches[3];
            
            if (checkdate($month, $day, $year)) {
                return "$year-$month-$day";
            }
        }
        
        return null;
    }
}
?>
