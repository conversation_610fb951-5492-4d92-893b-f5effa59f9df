<?php
/**
 * ICGC Emmanuel Temple Church Management System
 *
 * Main entry point for the application
 */

// Include bootstrap file for common setup tasks
require_once dirname(__DIR__) . '/bootstrap.php';

// Simple routing
$request = $_SERVER['REQUEST_URI'];

// Handle both Apache (/icgc/) and PHP built-in server scenarios
if (strpos($request, '/icgc/') === 0) {
    // Apache with /icgc/ base path
    $request = str_replace('/icgc/', '', $request);
} else {
    // PHP built-in server or direct access
    $request = ltrim($request, '/');
}

// Remove query parameters from the route
$request = explode('?', $request)[0];

// Default route
if (empty($request)) {
    $request = 'dashboard';
}

// Authentication is now handled by the Router based on route metadata

// Load the new routing system
require_once dirname(__DIR__) . '/utils/Router.php';
$routes = require_once dirname(__DIR__) . '/routes.php';

// Create router instance and dispatch request
$router = new Router($routes, $request, $_SERVER['REQUEST_METHOD']);

if ($router->dispatch()) {
    // Route was found and handled successfully
    exit;
}

// If no route was found, show 404 page
if (defined('BASE_DIR') && file_exists(BASE_DIR . '/views/404.php')) {
    require BASE_DIR . '/views/404.php';
} else {
    // Simple 404 fallback
    http_response_code(404);
    echo '<h1>404 - Page Not Found</h1>';
    echo '<p>The requested page could not be found.</p>';
    echo '<a href="' . (defined('BASE_URL') ? BASE_URL : '/icgc/') . '">Go to Dashboard</a>';
}
