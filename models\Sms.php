<?php
/**
 * SMS Model
 */

class Sms {
    // Database connection and table name
    private $conn;
    private $table_name = "sms_messages";

    // Object properties
    public $id;
    public $message;
    public $recipients;
    public $sent_date;
    public $status;
    public $sent_by;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all SMS messages with user details
     *
     * @return PDOStatement
     */
    public function getAll() {
        $query = "SELECT s.*, u.username as sent_by_name
                  FROM " . $this->table_name . " s
                  LEFT JOIN users u ON s.sent_by = u.id
                  ORDER BY s.sent_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get SMS by ID
     *
     * @param int $id
     * @return array|false Returns the SMS data as an array or false if not found
     */
    public function getById($id) {
        $query = "SELECT s.*, u.username as sent_by_name
                  FROM " . $this->table_name . " s
                  LEFT JOIN users u ON s.sent_by = u.id
                  WHERE s.id = :id
                  LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            // Set object properties
            $this->id = $row['id'];
            $this->message = $row['message'];
            $this->recipients = $row['recipients'];
            $this->sent_date = $row['sent_date'];
            $this->status = $row['status'];
            $this->sent_by = $row['sent_by'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];

            // Return the row data
            return $row;
        }

        return false;
    }

    /**
     * Create SMS record
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (message, recipients, sent_date, status, sent_by, created_at, updated_at)
                  VALUES
                  (:message, :recipients, :sent_date, :status, :sent_by, :created_at, :updated_at)";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->message = htmlspecialchars(strip_tags($this->message));
        $this->recipients = htmlspecialchars(strip_tags($this->recipients));
        $this->sent_date = htmlspecialchars(strip_tags($this->sent_date));

        // Ensure status is set to 'sent' by default if not specified
        if (empty($this->status)) {
            $this->status = 'sent';
        } else {
            $this->status = htmlspecialchars(strip_tags($this->status));
        }

        // Log the status being set
        error_log("Creating SMS record with status: {$this->status}");

        $this->created_at = htmlspecialchars(strip_tags($this->created_at));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':message', $this->message);
        $stmt->bindParam(':recipients', $this->recipients);
        $stmt->bindParam(':sent_date', $this->sent_date);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':sent_by', $this->sent_by);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Update SMS record
     *
     * @return bool
     */
    public function update() {
        // Set updated_at if not already set
        if (empty($this->updated_at)) {
            $this->updated_at = date('Y-m-d H:i:s');
        }

        // Log the update attempt
        error_log("Updating SMS record ID: {$this->id}, Status: {$this->status}, Updated At: {$this->updated_at}");

        $query = "UPDATE " . $this->table_name . "
                  SET message = :message,
                      recipients = :recipients,
                      sent_date = :sent_date,
                      status = :status,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->id = htmlspecialchars(strip_tags($this->id));
        $this->message = htmlspecialchars(strip_tags($this->message));
        $this->recipients = htmlspecialchars(strip_tags($this->recipients));
        $this->sent_date = htmlspecialchars(strip_tags($this->sent_date));
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':message', $this->message);
        $stmt->bindParam(':recipients', $this->recipients);
        $stmt->bindParam(':sent_date', $this->sent_date);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        try {
            if ($stmt->execute()) {
                error_log("SMS record updated successfully. ID: {$this->id}, Status: {$this->status}");
                return true;
            } else {
                $error = $stmt->errorInfo();
                error_log("Failed to update SMS record. ID: {$this->id}, Error: " . json_encode($error));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Exception updating SMS record. ID: {$this->id}, Error: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Delete SMS record
     *
     * @param int|null $id Optional ID to delete (uses object ID if not provided)
     * @return bool
     */
    public function delete($id = null) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Use provided ID or object ID
        $delete_id = $id ?? $this->id;

        // Sanitize input
        $delete_id = htmlspecialchars(strip_tags($delete_id));

        // Bind parameter
        $stmt->bindParam(':id', $delete_id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Get SMS by status
     *
     * @param string $status
     * @return PDOStatement
     */
    public function getByStatus($status) {
        $query = "SELECT s.*, u.username as sent_by_name
                  FROM " . $this->table_name . " s
                  LEFT JOIN users u ON s.sent_by = u.id
                  WHERE s.status = :status
                  ORDER BY s.sent_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get SMS by date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return PDOStatement
     */
    public function getByDateRange($start_date, $end_date) {
        $query = "SELECT s.*, u.username as sent_by_name
                  FROM " . $this->table_name . " s
                  LEFT JOIN users u ON s.sent_by = u.id
                  WHERE s.sent_date BETWEEN :start_date AND :end_date
                  ORDER BY s.sent_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Update SMS status only
     *
     * This is a simplified method to update just the status field
     * which can be more reliable than updating all fields at once
     *
     * @param string $status New status value ('pending', 'sent', 'failed', 'partial')
     * @return bool Success or failure
     */
    public function updateStatus($status) {
        // Validate status
        $valid_statuses = ['pending', 'sent', 'failed', 'partial'];
        if (!in_array($status, $valid_statuses)) {
            error_log("Invalid SMS status: {$status}");
            return false;
        }

        // Set the status property
        $this->status = $status;
        $this->updated_at = date('Y-m-d H:i:s');

        // Log the update attempt
        error_log("Updating SMS status only - ID: {$this->id}, New Status: {$status}");

        // Verify that the ID is set
        if (empty($this->id)) {
            error_log("Cannot update SMS status: ID is not set");
            return false;
        }

        // Prepare the query to update only status and updated_at
        $query = "UPDATE " . $this->table_name . "
                  SET status = :status,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->id = htmlspecialchars(strip_tags($this->id));
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        try {
            if ($stmt->execute()) {
                error_log("SMS status updated successfully. ID: {$this->id}, Status: {$this->status}");

                // Verify the update was successful by checking the database
                $verify_query = "SELECT status FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
                $verify_stmt = $this->conn->prepare($verify_query);
                $verify_stmt->bindParam(':id', $this->id);
                $verify_stmt->execute();

                if ($verify_stmt->rowCount() > 0) {
                    $row = $verify_stmt->fetch(PDO::FETCH_ASSOC);
                    if ($row['status'] === $status) {
                        error_log("Verified SMS status update: ID: {$this->id}, Status: {$status}");
                        return true;
                    } else {
                        error_log("SMS status verification failed. Expected: {$status}, Actual: {$row['status']}");

                        // Try one more direct update as a last resort
                        $final_query = "UPDATE " . $this->table_name . " SET status = :status WHERE id = :id";
                        $final_stmt = $this->conn->prepare($final_query);
                        $final_stmt->bindParam(':id', $this->id);
                        $final_stmt->bindParam(':status', $status);
                        $final_result = $final_stmt->execute();
                        error_log("Final direct status update attempt: " . ($final_result ? 'Success' : 'Failed'));
                        return $final_result;
                    }
                }

                return true;
            } else {
                $error = $stmt->errorInfo();
                error_log("Failed to update SMS status. ID: {$this->id}, Error: " . json_encode($error));
                return false;
            }
        } catch (PDOException $e) {
            error_log("Exception updating SMS status. ID: {$this->id}, Error: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Get all SMS messages with user details with pagination
     *
     * @param int $limit Number of records to return
     * @param int $offset Offset for pagination
     * @return PDOStatement
     */
    public function getAllPaginated($limit = 20, $offset = 0) {
        $query = "SELECT s.*, u.username as sent_by_name
                  FROM " . $this->table_name . " s
                  LEFT JOIN users u ON s.sent_by = u.id
                  ORDER BY s.sent_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get total count of SMS messages
     *
     * @return int
     */
    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'] ?? 0;
    }
}
