<?php
// Define application root directory
define('APP_ROOT', dirname(dirname(__DIR__)));

// Database configuration
require_once APP_ROOT . '/config/database.php';

try {
    // Create database connection
    $conn = new PDO("mysql:host=localhost;dbname=icgc_db", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if status column already exists
    $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'status'");
    $column_exists = $stmt->rowCount() > 0;
    
    if (!$column_exists) {
        // Add status column to users table
        $sql = "ALTER TABLE users ADD COLUMN status ENUM('active', 'blocked') NOT NULL DEFAULT 'active' AFTER role";
        $conn->exec($sql);
        
        echo "Status column added to users table successfully!";
    } else {
        echo "Status column already exists in users table.";
    }
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
