<?php
/**
 * Enhanced Children Listing View - Optimized and Refactored
 */

// Ensure we have the required data
if (!isset($children) || !isset($age_groups)) {
    redirect('children-ministry');
    exit;
}

// Configuration
$items_per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 25;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$total_children = count($children);
$total_pages = ceil($total_children / $items_per_page);
$offset = ($current_page - 1) * $items_per_page;
$paginated_children = array_slice($children, $offset, $items_per_page);
$view_mode = isset($_GET['view']) ? $_GET['view'] : 'table';

// Helper Functions
function renderProfilePicture($child, $size = 'h-12 w-12', $iconSize = 'text-lg') {
    if ($child['profile_picture']) {
        return '<img class="' . $size . ' rounded-full object-cover border-2 border-gray-200" src="' . BASE_URL . htmlspecialchars($child['profile_picture']) . '" alt="Profile">';
    } else {
        return '<div class="' . $size . ' rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                    <i class="fas fa-child text-white ' . $iconSize . '"></i>
                </div>';
    }
}

function renderGenderIcon($gender) {
    $icon = $gender === 'male' ? 'mars text-blue-500' : 'venus text-pink-500';
    return '<i class="fas fa-' . $icon . ' mr-1"></i>';
}

function renderChildName($child) {
    return htmlspecialchars($child['first_name'] . ' ' . $child['last_name']);
}

function renderAgeGroup($child) {
    if ($child['age_group_name']) {
        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">' .
               htmlspecialchars($child['age_group_name']) . '</span>';
    } else {
        return '<span class="text-gray-400 text-sm">Not assigned</span>';
    }
}

function renderDepartment($child) {
    $department = str_replace('_', ' ', htmlspecialchars($child['department'] ?? 'None'));
    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 capitalize">' .
           $department . '</span>';
}

function renderActionButtons($child, $type = 'table') {
    $childId = $child['id'];
    $childName = htmlspecialchars($child['first_name'] . ' ' . $child['last_name']);
    $baseUrl = BASE_URL;

    $buttons = [
        'view' => [
            'url' => "{$baseUrl}children-ministry/view-child?id={$childId}",
            'icon' => 'fas fa-eye',
            'title' => 'View Details',
            'color' => 'blue'
        ],
        'edit' => [
            'url' => "{$baseUrl}members/edit?id={$childId}",
            'icon' => 'fas fa-edit',
            'title' => 'Edit',
            'color' => 'green'
        ],
        'medical' => [
            'url' => "{$baseUrl}children-ministry/medical-info?child_id={$childId}",
            'icon' => 'fas fa-heartbeat',
            'title' => 'Medical Info',
            'color' => 'purple'
        ],
        'delete' => [
            'onclick' => "showDeleteModal({$childId}, '{$childName}')",
            'icon' => 'fas fa-trash',
            'title' => 'Delete Child',
            'color' => 'red'
        ]
    ];

    if ($type === 'table') {
        $html = '<div class="flex items-center justify-end space-x-2">';
        foreach ($buttons as $key => $button) {
            if (isset($button['url'])) {
                $html .= '<a href="' . $button['url'] . '" class="text-' . $button['color'] . '-600 hover:text-' . $button['color'] . '-900 transition-colors duration-150" title="' . $button['title'] . '">
                            <i class="' . $button['icon'] . '"></i>
                          </a>';
            } else {
                $html .= '<button onclick="' . $button['onclick'] . '" class="text-' . $button['color'] . '-600 hover:text-' . $button['color'] . '-900 transition-colors duration-150" title="' . $button['title'] . '">
                            <i class="' . $button['icon'] . '"></i>
                          </button>';
            }
        }
        $html .= '</div>';
    } else {
        // Grid view buttons
        $html = '<div class="flex space-x-2">';
        $gridButtons = ['view', 'medical', 'delete'];
        foreach ($gridButtons as $key) {
            $button = $buttons[$key];
            if (isset($button['url'])) {
                $html .= '<a href="' . $button['url'] . '" class="flex-1 bg-' . $button['color'] . '-600 hover:bg-' . $button['color'] . '-700 text-white text-center py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200">
                            <i class="' . $button['icon'] . ' mr-1"></i> ' . ucfirst($key) . '
                          </a>';
            } else {
                $html .= '<button onclick="' . $button['onclick'] . '" class="flex-1 bg-' . $button['color'] . '-600 hover:bg-' . $button['color'] . '-700 text-white text-center py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200">
                            <i class="' . $button['icon'] . ' mr-1"></i> Delete
                          </button>';
            }
        }
        $html .= '</div>';
    }

    return $html;
}
?>

<div class="page-content-centered py-6 fade-in">
    <!-- Enhanced Header Section -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-6 border border-gray-100">
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white relative overflow-hidden">
            <div class="absolute right-0 top-0 opacity-10">
                <i class="fas fa-users text-8xl transform -rotate-12 translate-x-6 -translate-y-4"></i>
            </div>
            <div class="relative z-10">
                <h1 class="text-3xl font-bold mb-2">Children Directory</h1>
                <p class="text-blue-100 max-w-2xl">Comprehensive management system for all children in the ministry with advanced search, filtering, and data visualization.</p>
            </div>
        </div>

        <!-- Action Bar -->
        <div class="p-4 bg-gray-50 border-b border-gray-200">
            <div class="flex flex-col lg:flex-row justify-between items-center gap-4">
                <!-- Left Actions -->
                <div class="flex flex-wrap gap-3">
                    <a href="<?php echo BASE_URL; ?>children-ministry" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-200 text-sm font-medium">
                        <i class="fas fa-arrow-left mr-2"></i> Dashboard
                    </a>
                    <a href="<?php echo BASE_URL; ?>children-ministry/standalone-registration" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-200 text-sm font-medium">
                        <i class="fas fa-user-plus mr-2"></i> Register Child
                    </a>
                    <button onclick="exportData()" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-200 text-sm font-medium">
                        <i class="fas fa-download mr-2"></i> Export
                    </button>
                    <a href="<?php echo BASE_URL; ?>children-ministry/fix-children-data"
                       onclick="return confirm('This will fix missing department and emergency contact information for all children. Continue?')"
                       class="bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-200 text-sm font-medium">
                        <i class="fas fa-tools mr-2"></i> Fix Data
                    </a>
                </div>

                <!-- View Toggle -->
                <div class="flex items-center gap-2 bg-white rounded-lg p-1 border border-gray-300">
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['view' => 'table'])); ?>"
                       class="px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 <?php echo $view_mode === 'table' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-100'; ?>">
                        <i class="fas fa-table mr-1"></i> Table
                    </a>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['view' => 'grid'])); ?>"
                       class="px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 <?php echo $view_mode === 'grid' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-100'; ?>">
                        <i class="fas fa-th mr-1"></i> Grid
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Statistics Overview -->
    <?php if (!empty($children)): ?>
        <div class="mb-4 bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200 px-6 py-4">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-chart-pie text-blue-600 mr-2"></i>
                    Statistics Overview
                </h3>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-2 md:grid-cols-6 gap-3">
                    <!-- Total Children -->
                    <div class="text-center p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg text-white shadow-md">
                        <p class="text-2xl font-bold"><?php echo number_format($total_children); ?></p>
                        <p class="text-xs text-blue-100">Total</p>
                    </div>

                    <!-- Age Groups -->
                    <div class="text-center p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-lg text-white shadow-md">
                        <p class="text-2xl font-bold">
                            <?php echo count(array_filter($children, function($c) { return $c['age'] <= 5; })); ?>
                        </p>
                        <p class="text-xs text-green-100">Ages 0-5</p>
                    </div>
                    <div class="text-center p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg text-white shadow-md">
                        <p class="text-2xl font-bold">
                            <?php echo count(array_filter($children, function($c) { return $c['age'] >= 6 && $c['age'] <= 12; })); ?>
                        </p>
                        <p class="text-xs text-purple-100">Ages 6-12</p>
                    </div>
                    <div class="text-center p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white shadow-md">
                        <p class="text-2xl font-bold">
                            <?php echo count(array_filter($children, function($c) { return $c['age'] >= 13; })); ?>
                        </p>
                        <p class="text-xs text-orange-100">Ages 13+</p>
                    </div>

                    <!-- Gender Distribution - Compact -->
                    <div class="text-center p-3 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg text-white shadow-md">
                        <p class="text-2xl font-bold">
                            <?php echo count(array_filter($children, function($c) { return $c['gender'] === 'male'; })); ?>
                        </p>
                        <p class="text-xs text-cyan-100">
                            <i class="fas fa-mars mr-1"></i>Male
                        </p>
                    </div>
                    <div class="text-center p-3 bg-gradient-to-br from-pink-500 to-pink-600 rounded-lg text-white shadow-md">
                        <p class="text-2xl font-bold">
                            <?php echo count(array_filter($children, function($c) { return $c['gender'] === 'female'; })); ?>
                        </p>
                        <p class="text-xs text-pink-100">
                            <i class="fas fa-venus mr-1"></i>Female
                        </p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Search and Filter Panel -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-4 border border-gray-100">
        <div class="bg-gray-50 border-b border-gray-200 px-6 py-3">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-filter text-blue-600 mr-2"></i> Search & Filters
            </h2>
        </div>
        <div class="p-4">
            <form method="GET" action="<?php echo BASE_URL; ?>children-ministry/children" id="searchForm">
                <!-- Preserve view mode -->
                <input type="hidden" name="view" value="<?php echo htmlspecialchars($view_mode); ?>">

                <!-- Main Filter Row -->
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3">
                    <!-- Search -->
                    <div class="lg:col-span-2">
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Children</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-blue-600"></i>
                            </div>
                            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search ?? ''); ?>"
                                   placeholder="Search by name (2+ characters)..."
                                   class="w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                        </div>
                    </div>

                    <!-- Age Group Filter -->
                    <div>
                        <label for="age_group" class="block text-sm font-medium text-gray-700 mb-2">Age Group</label>
                        <select id="age_group" name="age_group" class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                            <option value="">All Ages</option>
                            <?php foreach ($age_groups as $group): ?>
                                <option value="<?php echo $group['id']; ?>" <?php echo (isset($age_group) && $age_group == $group['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($group['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Gender Filter -->
                    <div>
                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                        <select id="gender" name="gender" class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All</option>
                            <option value="male" <?php echo (isset($_GET['gender']) && $_GET['gender'] == 'male') ? 'selected' : ''; ?>>Male</option>
                            <option value="female" <?php echo (isset($_GET['gender']) && $_GET['gender'] == 'female') ? 'selected' : ''; ?>>Female</option>
                        </select>
                    </div>

                    <!-- Items per page -->
                    <div>
                        <label for="per_page" class="block text-sm font-medium text-gray-700 mb-2">Per Page</label>
                        <select id="per_page" name="per_page" class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                            <option value="25" <?php echo $items_per_page == 25 ? 'selected' : ''; ?>>25</option>
                            <option value="50" <?php echo $items_per_page == 50 ? 'selected' : ''; ?>>50</option>
                            <option value="100" <?php echo $items_per_page == 100 ? 'selected' : ''; ?>>100</option>
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between items-center mt-4">
                    <div class="text-sm text-gray-600">
                        <span id="filterStatus">Filters auto-apply when changed</span>
                    </div>
                    <div class="flex gap-2">
                        <a href="<?php echo BASE_URL; ?>children-ministry/children?view=<?php echo $view_mode; ?>"
                           class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-all duration-200 font-medium text-sm">
                            <i class="fas fa-times mr-2"></i> Clear All
                        </a>
                        <button type="submit"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center transition-all duration-200 font-medium text-sm">
                            <i class="fas fa-search mr-2"></i> Apply Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>



    <!-- Results Summary and Pagination Info -->
    <div class="mb-4">
        <div class="bg-white rounded-lg shadow-md border border-gray-200 p-3">
            <div class="flex flex-col md:flex-row justify-between items-center gap-4">
                <div class="flex items-center gap-4">
                    <?php
                    $hasFilters = !empty($search) || !empty($age_group) || !empty($_GET['gender']);
                    if ($hasFilters): ?>
                        <div class="flex items-center gap-2">
                            <div class="text-gray-600">
                                <i class="fas fa-filter mr-1"></i>
                                Filtered Results
                            </div>
                            <div class="flex gap-1">
                                <?php if (!empty($search)): ?>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                                        Search: "<?php echo htmlspecialchars($search); ?>"
                                    </span>
                                <?php endif; ?>
                                <?php if (!empty($age_group)): ?>
                                    <?php
                                    $selected_group = array_filter($age_groups, function($g) use ($age_group) {
                                        return $g['id'] == $age_group;
                                    });
                                    $group_name = !empty($selected_group) ? reset($selected_group)['name'] : 'Unknown';
                                    ?>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                                        Age: <?php echo htmlspecialchars($group_name); ?>
                                    </span>
                                <?php endif; ?>
                                <?php if (!empty($_GET['gender'])): ?>
                                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium">
                                        Gender: <?php echo ucfirst(htmlspecialchars($_GET['gender'])); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-gray-600">
                            <i class="fas fa-list mr-1"></i>
                            All Children
                        </div>
                    <?php endif; ?>
                </div>

                <?php if ($total_pages > 1): ?>
                    <div class="text-sm text-gray-600">
                        Showing <?php echo number_format($offset + 1); ?>-<?php echo number_format(min($offset + $items_per_page, $total_children)); ?>
                        of <?php echo number_format($total_children); ?> children
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <?php if (empty($paginated_children)): ?>
        <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
            <div class="p-12 text-center">
                <div class="text-gray-400 mb-6">
                    <i class="fas fa-search text-8xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-700 mb-3">No Children Found</h3>
                <p class="text-gray-500 mb-6 max-w-md mx-auto">
                    <?php if (!empty($search) || !empty($age_group)): ?>
                        No children match your search criteria. Try adjusting your search terms or clearing the filters.
                    <?php else: ?>
                        No children are currently registered in the system. Start by adding your first child.
                    <?php endif; ?>
                </p>
                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    <a href="<?php echo BASE_URL; ?>members/new-registration" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center transition-all duration-200 font-medium">
                        <i class="fas fa-plus mr-2"></i> Add New Child
                    </a>
                    <?php if (!empty($search) || !empty($age_group)): ?>
                        <a href="<?php echo BASE_URL; ?>children-ministry/children?view=<?php echo $view_mode; ?>" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg inline-flex items-center transition-all duration-200 font-medium">
                            <i class="fas fa-times mr-2"></i> Clear Filters
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php else: ?>

        <?php if ($view_mode === 'table'): ?>
            <!-- Table View -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2">Child</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age & Gender</th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age Group</th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emergency Contact</th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($paginated_children as $child): ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <input type="checkbox" class="child-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" value="<?php echo $child['id']; ?>">
                                            <div class="ml-4 flex items-center">
                                                <div class="flex-shrink-0">
                                                    <?php echo renderProfilePicture($child); ?>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        <?php echo renderChildName($child); ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">ID: <?php echo $child['id']; ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 font-medium"><?php echo $child['age']; ?> years old</div>
                                        <div class="text-sm text-gray-500 capitalize">
                                            <?php echo renderGenderIcon($child['gender']); ?>
                                            <?php echo htmlspecialchars($child['gender']); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php echo renderAgeGroup($child); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php echo renderDepartment($child); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($child['emergency_contact_name'] ?? 'Not set'); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($child['emergency_contact_phone'] ?? ''); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-circle text-green-400 mr-1" style="font-size: 6px;"></i>
                                            Active
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <?php echo renderActionButtons($child, 'table'); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php else: ?>
            <!-- Grid View -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <?php foreach ($paginated_children as $child): ?>
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:scale-105 group">
                        <!-- Child Photo -->
                        <div class="relative">
                            <?php if ($child['profile_picture']): ?>
                                <img src="<?php echo BASE_URL . htmlspecialchars($child['profile_picture']); ?>" alt="Profile" class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center group-hover:from-blue-500 group-hover:to-purple-600 transition-all duration-300">
                                    <i class="fas fa-child text-6xl text-white opacity-80"></i>
                                </div>
                            <?php endif; ?>

                            <!-- Age Badge -->
                            <div class="absolute top-3 right-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">
                                Age <?php echo $child['age']; ?>
                            </div>

                            <!-- Age Group Badge -->
                            <?php if ($child['age_group_name']): ?>
                                <div class="absolute top-3 left-3 bg-white bg-opacity-90 text-gray-800 px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                    <?php echo htmlspecialchars($child['age_group_name']); ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Child Info -->
                        <div class="p-6">
                            <h3 class="text-lg font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-200">
                                <?php echo renderChildName($child); ?>
                            </h3>

                            <div class="space-y-3 text-sm text-gray-600 mb-4">
                                <div class="flex items-center">
                                    <i class="fas fa-birthday-cake w-5 mr-3 text-blue-600"></i>
                                    <span><?php echo date('M j, Y', strtotime($child['date_of_birth'])); ?></span>
                                </div>
                                <div class="flex items-center">
                                    <?php echo str_replace('mr-1', 'w-5 mr-3', renderGenderIcon($child['gender'])); ?>
                                    <span class="capitalize"><?php echo htmlspecialchars($child['gender']); ?></span>
                                </div>
                                <?php if ($child['department']): ?>
                                    <div class="flex items-center">
                                        <i class="fas fa-users w-5 mr-3 text-green-600"></i>
                                        <span class="capitalize"><?php echo str_replace('_', ' ', htmlspecialchars($child['department'])); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Action Buttons -->
                            <?php echo renderActionButtons($child, 'grid'); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <div class="mt-8 bg-white rounded-lg shadow-md border border-gray-200 p-4">
            <div class="flex flex-col md:flex-row justify-between items-center gap-4">
                <div class="text-sm text-gray-600">
                    Page <?php echo $current_page; ?> of <?php echo $total_pages; ?>
                </div>

                <nav class="flex items-center space-x-2">
                    <!-- Previous Page -->
                    <?php if ($current_page > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page - 1])); ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-150">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Page Numbers -->
                    <?php
                    $start_page = max(1, $current_page - 2);
                    $end_page = min($total_pages, $current_page + 2);

                    if ($start_page > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-150">1</a>
                        <?php if ($start_page > 2): ?>
                            <span class="px-3 py-2 text-sm font-medium text-gray-500">...</span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                           class="px-3 py-2 text-sm font-medium <?php echo $i === $current_page ? 'text-white bg-blue-600 border-blue-600' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50'; ?> border rounded-md transition-colors duration-150">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($end_page < $total_pages): ?>
                        <?php if ($end_page < $total_pages - 1): ?>
                            <span class="px-3 py-2 text-sm font-medium text-gray-500">...</span>
                        <?php endif; ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-150"><?php echo $total_pages; ?></a>
                    <?php endif; ?>

                    <!-- Next Page -->
                    <?php if ($current_page < $total_pages): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page + 1])); ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-150">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </nav>
            </div>
        </div>
    <?php endif; ?>



    <!-- Bulk Actions Panel (Hidden by default) -->
    <div id="bulkActionsPanel" class="hidden fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-xl border border-gray-200 p-4 z-50">
        <div class="flex items-center gap-4">
            <span class="text-sm font-medium text-gray-700">
                <span id="selectedCount">0</span> children selected
            </span>
            <div class="flex gap-2">
                <button onclick="bulkAction('export')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors duration-150">
                    <i class="fas fa-download mr-1"></i> Export
                </button>
                <button onclick="bulkAction('print')" class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors duration-150">
                    <i class="fas fa-print mr-1"></i> Print
                </button>
                <button onclick="clearSelection()" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors duration-150">
                    <i class="fas fa-times mr-1"></i> Clear
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on filter changes
    const autoSubmitElements = ['age_group', 'per_page', 'gender'];
    autoSubmitElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', function() {
                // Build URL with current parameters
                const currentUrl = new URL(window.location);
                const params = new URLSearchParams(currentUrl.search);

                // Update the changed parameter
                if (this.value === '') {
                    params.delete(this.name);
                } else {
                    params.set(this.name, this.value);
                }

                // Reset to page 1 when filters change
                params.set('page', '1');

                // Add loading state
                this.style.opacity = '0.6';
                this.disabled = true;

                // Update status
                const statusElement = document.getElementById('filterStatus');
                if (statusElement) {
                    statusElement.textContent = 'Applying filters...';
                }

                // Navigate to new URL
                const newUrl = currentUrl.pathname + '?' + params.toString();
                window.location.href = newUrl;
            });
        }
    });

    // Optimized Selection Management
    const selectionManager = {
        elements: {
            selectAll: document.getElementById('selectAll'),
            checkboxes: document.querySelectorAll('.child-checkbox'),
            bulkPanel: document.getElementById('bulkActionsPanel'),
            selectedCount: document.getElementById('selectedCount')
        },

        init() {
            this.bindEvents();
        },

        bindEvents() {
            if (this.elements.selectAll) {
                this.elements.selectAll.addEventListener('change', () => this.toggleAll());
            }

            this.elements.checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => this.updateState());
            });
        },

        toggleAll() {
            const isChecked = this.elements.selectAll.checked;
            this.elements.checkboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            this.updateBulkActions();
        },

        updateState() {
            this.updateBulkActions();
            this.updateSelectAllState();
        },

        updateSelectAllState() {
            if (!this.elements.selectAll) return;

            const checkedCount = this.getCheckedCount();
            const totalCount = this.elements.checkboxes.length;

            this.elements.selectAll.checked = checkedCount === totalCount;
            this.elements.selectAll.indeterminate = checkedCount > 0 && checkedCount < totalCount;
        },

        updateBulkActions() {
            const checkedCount = this.getCheckedCount();

            if (this.elements.selectedCount) {
                this.elements.selectedCount.textContent = checkedCount;
            }

            if (this.elements.bulkPanel) {
                this.elements.bulkPanel.classList.toggle('hidden', checkedCount === 0);
            }
        },

        getCheckedCount() {
            return document.querySelectorAll('.child-checkbox:checked').length;
        },

        getSelectedIds() {
            return Array.from(document.querySelectorAll('.child-checkbox:checked')).map(cb => cb.value);
        },

        clearAll() {
            this.elements.checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            if (this.elements.selectAll) {
                this.elements.selectAll.checked = false;
                this.elements.selectAll.indeterminate = false;
            }
            this.updateBulkActions();
        }
    };

    // Initialize selection manager
    selectionManager.init();

    // Optimized Export/Action Functions
    const actionManager = {
        baseUrl: '<?php echo BASE_URL; ?>children-ministry/children',

        bulkAction(action) {
            const selectedIds = selectionManager.getSelectedIds();

            if (selectedIds.length === 0) {
                this.showNotification('Please select at least one child.', 'warning');
                return;
            }

            switch (action) {
                case 'export': this.exportChildren(selectedIds); break;
                case 'print': this.printChildren(selectedIds); break;
                default: console.warn('Unknown bulk action:', action);
            }
        },

        exportData() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'csv');
            window.location.href = this.baseUrl + '?' + params.toString();
        },

        exportChildren(selectedIds) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'csv');
            params.set('selected_ids', selectedIds.join(','));
            window.location.href = this.baseUrl + '?' + params.toString();
        },

        printChildren(selectedIds) {
            const params = new URLSearchParams();
            params.set('print', '1');
            params.set('selected_ids', selectedIds.join(','));
            window.open(this.baseUrl + '?' + params.toString(), '_blank');
        },

        showNotification(message, type = 'info') {
            // Simple notification system
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'warning' ? 'bg-yellow-100 border border-yellow-400 text-yellow-700' :
                type === 'error' ? 'bg-red-100 border border-red-400 text-red-700' :
                'bg-blue-100 border border-blue-400 text-blue-700'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.remove(), 3000);
        }
    };

    // Global functions for backward compatibility
    window.bulkAction = (action) => actionManager.bulkAction(action);
    window.exportData = () => actionManager.exportData();
    window.clearSelection = () => selectionManager.clearAll();

    // Optimized Search Manager
    const searchManager = {
        input: document.getElementById('search'),
        timeout: null,

        init() {
            if (!this.input) return;

            this.bindEvents();
            this.setupVisualFeedback();
        },

        bindEvents() {
            // Realtime search with debouncing
            this.input.addEventListener('input', (e) => this.handleInput(e));

            // Form submission
            const form = this.input.form;
            if (form) {
                form.addEventListener('submit', (e) => this.handleSubmit(e));
            }

            // Clear button functionality
            this.setupClearButton();
        },

        handleInput(e) {
            clearTimeout(this.timeout);
            const searchValue = e.target.value.trim();

            this.timeout = setTimeout(() => {
                if (searchValue.length >= 2 || searchValue.length === 0) {
                    this.performSearch(searchValue);
                }
            }, 300);

            this.updateSearchIcon(searchValue.length > 0);
        },

        handleSubmit(e) {
            e.preventDefault();
            this.performSearch(this.input.value.trim());
        },

        performSearch(searchValue) {
            const url = new URL(window.location);
            const params = new URLSearchParams(url.search);

            if (searchValue === '') {
                params.delete('search');
            } else {
                params.set('search', searchValue);
            }

            params.set('page', '1'); // Reset pagination
            window.location.href = url.pathname + '?' + params.toString();
        },

        setupClearButton() {
            const clearButton = document.querySelector('a[href*="children-ministry/children"]');
            if (clearButton && (clearButton.textContent.includes('Clear') || clearButton.querySelector('.fa-times'))) {
                clearButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.input.value = '';

                    const url = new URL(window.location);
                    const viewMode = new URLSearchParams(window.location.search).get('view') || 'table';
                    window.location.href = url.pathname + '?view=' + viewMode;
                });
            }
        },

        setupVisualFeedback() {
            this.input.addEventListener('focus', () => {
                this.input.parentElement.classList.add('ring-2', 'ring-blue-500');
            });

            this.input.addEventListener('blur', () => {
                this.input.parentElement.classList.remove('ring-2', 'ring-blue-500');
            });
        },

        updateSearchIcon(isSearching) {
            const searchIcon = this.input.parentElement.querySelector('.fa-search');
            if (searchIcon && isSearching) {
                searchIcon.className = 'fas fa-spinner fa-spin text-blue-600';
                setTimeout(() => {
                    searchIcon.className = 'fas fa-search text-blue-600';
                }, 1000);
            }
        }
    };

    // Initialize search manager
    searchManager.init();

    // Optimized Keyboard Shortcuts
    const keyboardManager = {
        init() {
            document.addEventListener('keydown', (e) => this.handleKeydown(e));
        },

        handleKeydown(e) {
            // Ignore if user is typing in input fields
            if (e.target.matches('input, textarea, select')) return;

            switch (e.key) {
                case 'a':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.selectAll();
                    }
                    break;
                case 'Escape':
                    selectionManager.clearAll();
                    break;
                case '/':
                    e.preventDefault();
                    this.focusSearch();
                    break;
            }
        },

        selectAll() {
            if (selectionManager.elements.selectAll) {
                selectionManager.elements.selectAll.checked = true;
                selectionManager.toggleAll();
            }
        },

        focusSearch() {
            if (searchManager.input) {
                searchManager.input.focus();
            }
        }
    };

    // Initialize keyboard manager
    keyboardManager.init();

    // Optimized Table Scroll Manager
    const scrollManager = {
        container: document.querySelector('.overflow-x-auto'),

        init() {
            if (!this.container) return;

            this.bindEvents();
            this.updateIndicators();
        },

        bindEvents() {
            this.container.addEventListener('scroll', () => this.updateIndicators());
            window.addEventListener('resize', () => this.updateIndicators());
        },

        updateIndicators() {
            const { scrollLeft, scrollWidth, clientWidth } = this.container;

            this.container.classList.toggle('scrolled-left', scrollLeft > 0);
            this.container.classList.toggle('can-scroll-right', scrollLeft < scrollWidth - clientWidth - 1);
        }
    };

    // Initialize scroll manager
    scrollManager.init();
});
</script>

<style>
/* Optimized Custom Styles */
.overflow-x-auto {
    position: relative;
}

.overflow-x-auto.scrolled-left::before,
.overflow-x-auto.can-scroll-right::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 1;
}

.overflow-x-auto.scrolled-left::before {
    left: 0;
    background: linear-gradient(to right, rgba(255,255,255,0.8), transparent);
}

.overflow-x-auto.can-scroll-right::after {
    right: 0;
    background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
}

/* Utility classes */
.transition-all { transition: all 0.2s ease-in-out; }
.loading { opacity: 0.6; pointer-events: none; }

/* Responsive design */
@media print {
    .no-print { display: none !important; }
    .print-only { display: block !important; }
    body { font-size: 12px; }
    table { font-size: 11px; }
}

@media (max-width: 768px) {
    .container { padding: 0 1rem; }
    .overflow-x-auto { -webkit-overflow-scrolling: touch; }
}
</style>

<!-- Delete Child Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Delete Child</h3>
                    <button onclick="closeDeleteModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="mb-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-900">Are you sure?</h4>
                            <p class="text-sm text-gray-600">This action cannot be undone.</p>
                        </div>
                    </div>

                    <p class="text-gray-700 mb-4">
                        You are about to delete <strong id="childName"></strong>.
                        Please choose how you want to proceed:
                    </p>

                    <div class="space-y-3">
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="deleteType" value="soft" checked class="mr-3">
                            <div>
                                <div class="font-medium text-gray-900">Deactivate (Recommended)</div>
                                <div class="text-sm text-gray-600">Mark as inactive but keep all data and relationships</div>
                            </div>
                        </label>

                        <label class="flex items-center p-3 border border-red-200 rounded-lg cursor-pointer hover:bg-red-50">
                            <input type="radio" name="deleteType" value="hard" class="mr-3">
                            <div>
                                <div class="font-medium text-red-900">Permanent Delete</div>
                                <div class="text-sm text-red-600">Completely remove child and all related data</div>
                            </div>
                        </label>
                    </div>

                    <div id="hardDeleteWarning" class="hidden mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-red-500 mt-0.5 mr-2"></i>
                            <div class="text-sm text-red-700">
                                <strong>Warning:</strong> This will permanently delete:
                                <ul class="mt-1 ml-4 list-disc">
                                    <li>Child's personal information</li>
                                    <li>Family relationships</li>
                                    <li>Guardian contacts (if no other children)</li>
                                    <li>Medical information</li>
                                    <li>Attendance history</li>
                                    <li>All authorizations</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button onclick="closeDeleteModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                        Cancel
                    </button>
                    <button onclick="confirmDelete()"
                            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors">
                        <span id="deleteButtonText">Deactivate Child</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentChildId = null;
let currentChildName = null;

function showDeleteModal(childId, childName) {
    currentChildId = childId;
    currentChildName = childName;
    document.getElementById('childName').textContent = childName;
    document.getElementById('deleteModal').classList.remove('hidden');

    // Reset to soft delete
    document.querySelector('input[name="deleteType"][value="soft"]').checked = true;
    updateDeleteButton();
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    currentChildId = null;
    currentChildName = null;
}

// Update delete button text and warning based on selected option
document.querySelectorAll('input[name="deleteType"]').forEach(radio => {
    radio.addEventListener('change', updateDeleteButton);
});

function updateDeleteButton() {
    const deleteType = document.querySelector('input[name="deleteType"]:checked')?.value;
    const deleteButton = document.getElementById('deleteButtonText');
    const hardDeleteWarning = document.getElementById('hardDeleteWarning');

    // Check if elements exist before updating
    if (!deleteType || !deleteButton || !hardDeleteWarning) {
        return;
    }

    if (deleteType === 'soft') {
        deleteButton.textContent = 'Deactivate Child';
        hardDeleteWarning.classList.add('hidden');
    } else {
        deleteButton.textContent = 'Permanently Delete';
        hardDeleteWarning.classList.remove('hidden');
    }
}

async function confirmDelete(retryCount = 0) {
    if (!currentChildId) return;

    const deleteType = document.querySelector('input[name="deleteType"]:checked').value;
    const deleteButton = document.querySelector('#deleteModal button[onclick="confirmDelete()"]');
    const originalButtonContent = deleteButton.innerHTML;

    // Show loading state
    deleteButton.disabled = true;
    if (retryCount > 0) {
        deleteButton.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>Retrying... (${retryCount}/3)`;
    } else {
        deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
    }

    try {
        const formData = new FormData();
        formData.append('child_id', currentChildId);
        formData.append('delete_type', deleteType);

        console.log('Sending delete request for child:', currentChildId, 'type:', deleteType, 'retry:', retryCount);

        const response = await fetch('<?php echo BASE_URL; ?>children-ministry/delete-child', {
            method: 'POST',
            body: formData
        });

        console.log('Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseText = await response.text();
        console.log('Raw response:', responseText);

        const result = JSON.parse(responseText);

        if (result.success) {
            // Show success message
            showNotification(result.message, 'success');

            // Close modal
            closeDeleteModal();

            // Reload page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            // Check if it's a temporary database busy error
            if ((result.message.includes('busy') || result.message.includes('timeout') || result.message.includes('Lock wait')) && retryCount < 3) {
                // Wait a bit and retry
                setTimeout(() => {
                    confirmDelete(retryCount + 1);
                }, 2000 * (retryCount + 1)); // Exponential backoff
                return;
            }

            showNotification(result.message, 'error');
            // Reset button on error
            deleteButton.disabled = false;
            deleteButton.innerHTML = originalButtonContent;
        }
    } catch (error) {
        console.error('Error deleting child:', error);

        // Check if it's a network/timeout error and retry
        if ((error.message.includes('timeout') || error.message.includes('network')) && retryCount < 3) {
            setTimeout(() => {
                confirmDelete(retryCount + 1);
            }, 2000 * (retryCount + 1));
            return;
        }

        showNotification('An error occurred while deleting the child. Please try again.', 'error');
        // Reset button on error
        deleteButton.disabled = false;
        deleteButton.innerHTML = originalButtonContent;
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' :
        'bg-red-100 border border-red-400 text-red-700'
    }`;

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>
