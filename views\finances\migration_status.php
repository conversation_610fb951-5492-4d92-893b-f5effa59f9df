<!-- Finance Migration Status Page -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-green-50 to-blue-50">
    <div class="container mx-auto px-4 py-8">
        
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Finance Schema Migration</h1>
                    <p class="text-gray-600">Check and manage database schema updates</p>
                </div>
                <div class="bg-gradient-to-r from-blue-100 to-green-100 p-4 rounded-lg">
                    <i class="fas fa-database text-2xl text-blue-600"></i>
                </div>
            </div>
        </div>

        <!-- Migration Status -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Current Schema Status</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Current Columns -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-700 mb-3">Existing Columns</h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <?php if (isset($existing_columns) && !empty($existing_columns)): ?>
                            <ul class="space-y-2">
                                <?php foreach ($existing_columns as $column): ?>
                                    <li class="flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                        <span class="font-mono text-sm"><?= htmlspecialchars($column) ?></span>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-gray-500">No column information available</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Required Columns -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-700 mb-3">Enhanced Features Status</h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <ul class="space-y-2">
                            <?php 
                            $required_columns = ['transaction_type', 'subcategory', 'reference_number', 'payment_method'];
                            foreach ($required_columns as $column): 
                                $exists = isset($existing_columns) && in_array($column, $existing_columns);
                            ?>
                                <li class="flex items-center">
                                    <?php if ($exists): ?>
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                        <span class="text-green-700 font-mono text-sm"><?= $column ?></span>
                                        <span class="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Ready</span>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-red-500 mr-2"></i>
                                        <span class="text-red-700 font-mono text-sm"><?= $column ?></span>
                                        <span class="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Missing</span>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Migration Status Summary -->
            <div class="mt-6 p-4 rounded-lg <?= isset($migration_complete) && $migration_complete ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200' ?>">
                <?php if (isset($migration_complete) && $migration_complete): ?>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-green-800 font-semibold">Migration Complete!</h4>
                            <p class="text-green-700 text-sm">All enhanced features are available and ready to use.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-yellow-800 font-semibold">Migration Required</h4>
                            <p class="text-yellow-700 text-sm">Enhanced features require database schema updates.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Actions</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                
                <!-- Run Migration -->
                <?php if (!isset($migration_complete) || !$migration_complete): ?>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-center">
                        <i class="fas fa-play-circle text-blue-600 text-3xl mb-3"></i>
                        <h3 class="font-semibold text-blue-800 mb-2">Run Migration</h3>
                        <p class="text-blue-700 text-sm mb-4">Apply database schema updates</p>
                        <form method="POST" action="<?= BASE_URL ?>finance/run-migration">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-database mr-2"></i>Run Migration
                            </button>
                        </form>
                    </div>
                </div>
                <?php endif; ?>

                <!-- View Reports -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-center">
                        <i class="fas fa-chart-bar text-green-600 text-3xl mb-3"></i>
                        <h3 class="font-semibold text-green-800 mb-2">Enhanced Reports</h3>
                        <p class="text-green-700 text-sm mb-4">View financial analytics</p>
                        <a href="<?= BASE_URL ?>finance/enhanced-reports" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors inline-block">
                            <i class="fas fa-chart-line mr-2"></i>View Reports
                        </a>
                    </div>
                </div>

                <!-- Add Transaction -->
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-center">
                        <i class="fas fa-plus-circle text-purple-600 text-3xl mb-3"></i>
                        <h3 class="font-semibold text-purple-800 mb-2">Add Transaction</h3>
                        <p class="text-purple-700 text-sm mb-4">Record new transaction</p>
                        <a href="<?= BASE_URL ?>finance/add" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg transition-colors inline-block">
                            <i class="fas fa-plus mr-2"></i>Add Transaction
                        </a>
                    </div>
                </div>

            </div>
        </div>

        <!-- Sample Data -->
        <?php if (isset($sample_data) && !empty($sample_data)): ?>
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h2 class="text-xl font-bold text-gray-800">Sample Data (Recent 5 Records)</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <?php foreach (array_keys($sample_data[0]) as $header): ?>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <?= htmlspecialchars($header) ?>
                                </th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($sample_data as $row): ?>
                            <tr class="hover:bg-gray-50">
                                <?php foreach ($row as $value): ?>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?= htmlspecialchars($value ?? 'NULL') ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>

        <!-- Back to Finance -->
        <div class="mt-8 text-center">
            <a href="<?= BASE_URL ?>finance" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition-colors inline-block">
                <i class="fas fa-arrow-left mr-2"></i>Back to Finance
            </a>
        </div>

    </div>
</div>

<style>
/* Enhanced styles for migration page */
.hover\:bg-gray-50:hover {
    background-color: #f9fafb;
}

/* Animation for action cards */
.bg-blue-50, .bg-green-50, .bg-purple-50 {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.bg-blue-50:hover, .bg-green-50:hover, .bg-purple-50:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Button animations */
button, a.inline-block {
    transition: all 0.2s ease;
}

button:hover, a.inline-block:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive table */
@media (max-width: 768px) {
    .overflow-x-auto {
        -webkit-overflow-scrolling: touch;
    }
}
</style>
