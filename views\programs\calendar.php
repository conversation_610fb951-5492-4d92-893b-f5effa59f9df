<?php
/**
 * Programs Calendar View - Church Program & Activities Planner
 */

$current_month_name = date('F', strtotime($start_date));
$current_year = date('Y', strtotime($start_date));
$days_in_month = date('t', strtotime($start_date));
$first_day_of_month = date('w', strtotime($start_date));

// Header configuration
$header_title = 'Program Calendar';
$header_subtitle = $current_month_name . ' ' . $current_year . ' - Visual overview of all church programs';
$header_icon = 'fas fa-calendar-alt';
$header_width = 'container mx-auto';

// Custom navigation for calendar page
$navigation_buttons = [
    'create' => [
        'url' => BASE_URL . 'programs/create',
        'text' => 'New Program',
        'icon' => 'fas fa-plus',
        'style' => 'bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white'
    ],
    'list' => [
        'url' => BASE_URL . 'programs/list',
        'text' => 'List View',
        'icon' => 'fas fa-list',
        'style' => 'bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-gray-800'
    ],
    'dashboard' => [
        'url' => BASE_URL . 'programs/dashboard',
        'text' => 'Dashboard',
        'icon' => 'fas fa-chart-pie',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ],
    'timeline' => [
        'url' => BASE_URL . 'programs/timeline?year=' . $current_year,
        'text' => 'Timeline',
        'icon' => 'fas fa-timeline',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ]
];

// Include shared header
include 'components/header.php';
?>

    <!-- Calendar Navigation -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
            <div class="flex flex-col md:flex-row justify-between items-center gap-4">
                <!-- Month Navigation -->
                <div class="flex items-center space-x-4">
                    <?php
                    $prev_month = date('n', strtotime($start_date . ' -1 month'));
                    $prev_year = date('Y', strtotime($start_date . ' -1 month'));
                    $next_month = date('n', strtotime($start_date . ' +1 month'));
                    $next_year = date('Y', strtotime($start_date . ' +1 month'));
                    ?>
                    <a href="<?php echo BASE_URL; ?>programs/calendar?month=<?php echo $prev_month; ?>&year=<?php echo $prev_year; ?>"
                       class="inline-flex items-center justify-center w-10 h-10 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all duration-200 shadow-sm">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <h2 class="text-2xl font-bold text-gray-800"><?php echo $current_month_name . ' ' . $current_year; ?></h2>
                    <a href="<?php echo BASE_URL; ?>programs/calendar?month=<?php echo $next_month; ?>&year=<?php echo $next_year; ?>"
                       class="inline-flex items-center justify-center w-10 h-10 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all duration-200 shadow-sm">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>

                <!-- Quick Actions -->
                <div class="flex items-center space-x-3">
                    <!-- Month Selector -->
                    <div class="relative">
                        <select onchange="window.location.href='<?php echo BASE_URL; ?>programs/calendar?month=' + this.value + '&year=<?php echo $current_year; ?>'"
                                class="px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm font-medium">
                            <?php for ($m = 1; $m <= 12; $m++): ?>
                                <option value="<?php echo $m; ?>" <?php echo ($m == date('n', strtotime($start_date))) ? 'selected' : ''; ?>>
                                    <?php echo date('F', mktime(0, 0, 0, $m, 1)); ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>

                    <!-- Print Button -->
                    <button onclick="window.print()" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-all duration-200">
                        <i class="fas fa-print mr-2"></i> Print
                    </button>
                </div>
            </div>
        </div>

        <!-- Calendar Grid -->
        <div class="p-6">
            <div class="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200">
                <!-- Calendar Header -->
                <div class="grid grid-cols-7 bg-gradient-to-r from-gray-100 to-gray-50">
                    <div class="p-4 text-center font-semibold text-gray-700 border-r border-gray-200">Sunday</div>
                    <div class="p-4 text-center font-semibold text-gray-700 border-r border-gray-200">Monday</div>
                    <div class="p-4 text-center font-semibold text-gray-700 border-r border-gray-200">Tuesday</div>
                    <div class="p-4 text-center font-semibold text-gray-700 border-r border-gray-200">Wednesday</div>
                    <div class="p-4 text-center font-semibold text-gray-700 border-r border-gray-200">Thursday</div>
                    <div class="p-4 text-center font-semibold text-gray-700 border-r border-gray-200">Friday</div>
                    <div class="p-4 text-center font-semibold text-gray-700">Saturday</div>
                </div>

                <!-- Calendar Body -->
                <div class="grid grid-cols-7">
                    <?php
                    // Add empty cells for days before the first day of the month
                    for ($i = 0; $i < $first_day_of_month; $i++) {
                        echo '<div class="h-32 p-2 border-r border-b border-gray-200 bg-gray-50"></div>';
                    }

                    // Add days of the month
                    for ($day = 1; $day <= $days_in_month; $day++) {
                        $is_today = ($day == date('j') && date('n', strtotime($start_date)) == date('n') && date('Y', strtotime($start_date)) == date('Y'));
                        $has_programs = isset($calendar_data[$day]);
                        $day_programs = $calendar_data[$day] ?? [];

                        $day_classes = 'h-32 p-2 border-r border-b border-gray-200 relative hover:bg-gray-50 transition-colors duration-200';
                        if ($is_today) {
                            $day_classes .= ' bg-blue-50 border-blue-200';
                        }

                        echo '<div class="' . $day_classes . '">';

                        // Day number
                        $day_number_classes = 'inline-flex items-center justify-center w-8 h-8 text-sm font-semibold rounded-full';
                        if ($is_today) {
                            $day_number_classes .= ' bg-primary text-white';
                        } else {
                            $day_number_classes .= ' text-gray-700';
                        }
                        echo '<div class="' . $day_number_classes . '">' . $day . '</div>';

                        // Programs for this day
                        if ($has_programs) {
                            echo '<div class="mt-1 space-y-1">';
                            $program_count = 0;
                            foreach ($day_programs as $program) {
                                if ($program_count >= 2) {
                                    $remaining = count($day_programs) - 2;
                                    echo '<div class="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded cursor-pointer hover:bg-gray-300 transition-colors duration-200" title="Click to see all programs">+' . $remaining . ' more</div>';
                                    break;
                                }

                                $program_color = $program['color_code'] ?? '#3F7D58';
                                echo '<div class="text-xs px-2 py-1 rounded text-white font-medium truncate cursor-pointer hover:opacity-80 transition-opacity duration-200"
                                           style="background-color: ' . $program_color . '"
                                           title="' . htmlspecialchars($program['title']) . '">';
                                echo '<a href="' . BASE_URL . 'programs/show?id=' . $program['id'] . '" class="text-white hover:text-white">';
                                echo htmlspecialchars(substr($program['title'], 0, 15));
                                if (strlen($program['title']) > 15) echo '...';
                                echo '</a>';
                                echo '</div>';
                                $program_count++;
                            }
                            echo '</div>';
                        }

                        echo '</div>';
                    }

                    // Fill remaining cells
                    $total_cells = $first_day_of_month + $days_in_month;
                    $remaining_cells = 42 - $total_cells; // 6 rows × 7 days = 42 cells
                    for ($i = 0; $i < $remaining_cells; $i++) {
                        echo '<div class="h-32 p-2 border-r border-b border-gray-200 bg-gray-50"></div>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Legend -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-tags mr-3 text-primary"></i>
                Program Categories
            </h2>
            <p class="text-gray-600 text-sm mt-2">Color-coded categories for easy identification</p>
        </div>
        <div class="p-6">
            <?php if (!empty($categories)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <?php foreach ($categories as $category): ?>
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                            <div class="w-4 h-4 rounded-full shadow-sm" style="background-color: <?php echo $category['color_code']; ?>"></div>
                            <span class="text-sm font-medium text-gray-700"><?php echo htmlspecialchars($category['name']); ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-tags text-3xl text-gray-300 mb-3"></i>
                    <p class="text-gray-500">No categories available</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Add click handler for "more" items to show details
document.addEventListener('DOMContentLoaded', function() {
    // Handle "more" program items
    document.querySelectorAll('[title="Click to see all programs"]').forEach(function(item) {
        item.addEventListener('click', function() {
            alert('Feature coming soon: Click to see all programs for this day');
        });
    });

    // Add print styles
    const printStyles = `
        @media print {
            .no-print { display: none !important; }
            .container { max-width: none !important; margin: 0 !important; }
            .shadow-md { box-shadow: none !important; }
        }
    `;

    const styleSheet = document.createElement('style');
    styleSheet.textContent = printStyles;
    document.head.appendChild(styleSheet);
});
</script>
