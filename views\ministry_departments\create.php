<?php
/**
 * Ministry Departments Create View
 * Form to create a new ministry department
 */
?>

<div class="max-w-4xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Create Ministry Department</h1>
            <p class="text-gray-600 mt-1">Add a new ministry department to organize church activities</p>
        </div>
        <a href="<?php echo BASE_URL; ?>ministry-departments" 
           class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Departments
        </a>
    </div>

    <!-- Create Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form method="POST" action="<?php echo BASE_URL; ?>ministry-departments" class="space-y-6">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">
            
            <!-- Department Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Department Name <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                       placeholder="Enter department name">
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                          placeholder="Enter department description"></textarea>
            </div>

            <!-- Head of Department -->
            <div>
                <label for="head_of_department" class="block text-sm font-medium text-gray-700 mb-2">
                    Head of Department
                </label>
                <select id="head_of_department" 
                        name="head_of_department"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                    <option value="">Select a member (optional)</option>
                    <?php if (!empty($members)): ?>
                        <?php foreach ($members as $member): ?>
                            <option value="<?php echo htmlspecialchars($member['id']); ?>">
                                <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <!-- Status -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Status
                </label>
                <div class="flex items-center space-x-4">
                    <label class="flex items-center">
                        <input type="radio" 
                               name="is_active" 
                               value="1" 
                               checked
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                        <span class="ml-2 text-sm text-gray-700">Active</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" 
                               name="is_active" 
                               value="0"
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                        <span class="ml-2 text-sm text-gray-700">Inactive</span>
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="<?php echo BASE_URL; ?>ministry-departments" 
                   class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-emerald-500 hover:bg-emerald-600 text-white px-6 py-2 rounded-md font-medium transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Create Department
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('Department name is required.');
        document.getElementById('name').focus();
        return false;
    }
    
    if (name.length < 2) {
        e.preventDefault();
        alert('Department name must be at least 2 characters long.');
        document.getElementById('name').focus();
        return false;
    }
});

// Auto-focus on name field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('name').focus();
});
</script>
