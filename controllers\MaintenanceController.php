<?php
/**
 * Maintenance Controller
 * 
 * Handles database maintenance, archiving, and statistics operations
 */
class MaintenanceController {
    private $dbStats;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Create DatabaseStats model
        require_once 'models/DatabaseStats.php';
        $this->dbStats = new DatabaseStats();
    }
    
    /**
     * Display database statistics
     * 
     * @return void
     */
    public function refreshStats() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to access database statistics.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        // Get database statistics
        $db_stats = $this->dbStats->getDatabaseStats();
        
        // Store in session for display
        $_SESSION['db_stats'] = $db_stats;
        
        // Set flash message
        $_SESSION['flash_message'] = 'Database statistics refreshed successfully.';
        $_SESSION['flash_type'] = 'success';
        
        // Redirect back to settings page
        header('Location: ' . BASE_URL . 'settings#database/system-maintenance');
        exit;
    }
    
    /**
     * Optimize database
     * 
     * @return void
     */
    public function optimizeDatabase() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to optimize the database.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        try {
            // Optimize database tables
            $result = $this->dbStats->optimizeTables();
            
            // Set flash message
            $_SESSION['flash_message'] = $result['message'];
            $_SESSION['flash_type'] = $result['status'];
            
            // Store optimization details in session
            $_SESSION['optimization_details'] = $result['details'];
            
            // Refresh database statistics
            $_SESSION['db_stats'] = $this->dbStats->getDatabaseStats();
            
        } catch (Exception $e) {
            // Set error message
            $_SESSION['flash_message'] = 'Error optimizing database: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
        }
        
        // Redirect back to settings page
        header('Location: ' . BASE_URL . 'settings#database/system-maintenance');
        exit;
    }
    
    /**
     * Display archiving options
     * 
     * @return void
     */
    public function archiving() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to access archiving options.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        // Get archivable data types
        $dataTypes = $this->dbStats->getArchivableDataTypes();
        
        // Set page title and active page
        $page_title = 'Data Archiving - ICGC Emmanuel Temple';
        $active_page = 'settings';
        
        // Include the view
        require_once 'views/settings/archiving.php';
    }
    
    /**
     * Process archiving request
     * 
     * @return void
     */
    public function processArchiving() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to archive data.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: ' . BASE_URL . 'settings/archiving');
            exit;
        }
        
        // Get form data
        $dataType = isset($_POST['data_type']) ? $_POST['data_type'] : '';
        $cutoffDate = isset($_POST['cutoff_date']) ? $_POST['cutoff_date'] : '';
        $confirm = isset($_POST['confirm']) ? true : false;
        
        // Validate form data
        if (empty($dataType) || empty($cutoffDate) || !$confirm) {
            $_SESSION['flash_message'] = 'Please fill in all required fields and confirm the archiving operation.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings/archiving');
            exit;
        }
        
        // Process archiving based on data type
        switch ($dataType) {
            case 'finances':
                $this->archiveFinances($cutoffDate);
                break;
            case 'attendance':
                $this->archiveAttendance($cutoffDate);
                break;
            case 'sms_messages':
                $this->archiveSmsMessages($cutoffDate);
                break;
            case 'visitors':
                $this->archiveVisitors($cutoffDate);
                break;
            case 'equipment_maintenance':
                $this->archiveEquipmentMaintenance($cutoffDate);
                break;
            default:
                $_SESSION['flash_message'] = 'Invalid data type selected.';
                $_SESSION['flash_type'] = 'danger';
                header('Location: ' . BASE_URL . 'settings/archiving');
                exit;
        }
        
        // Redirect back to archiving page
        header('Location: ' . BASE_URL . 'settings/archiving');
        exit;
    }
    
    /**
     * Archive finances
     * 
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveFinances($cutoffDate) {
        try {
            // Create Finance model
            require_once 'models/Finance.php';
            $finance = new Finance();
            
            // Archive old records
            $result = $finance->archiveOldRecords($cutoffDate);
            
            // Set flash message
            $_SESSION['flash_message'] = $result['message'];
            $_SESSION['flash_type'] = $result['status'];
            
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error archiving finances: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
        }
    }
    
    /**
     * Archive attendance records
     * 
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveAttendance($cutoffDate) {
        try {
            // Create Attendance model
            require_once 'models/Attendance.php';
            $attendance = new Attendance();
            
            // Check if archive table exists, create if not
            $this->createArchiveTable('attendance', 'attendance_archive');
            
            // Archive old records
            $startTime = microtime(true);
            
            // Begin transaction
            $this->dbStats->conn->beginTransaction();
            
            // Move old records to archive table
            $moveQuery = "INSERT INTO attendance_archive
                SELECT *, NOW() as archived_at FROM attendance
                WHERE attendance_date < :cutoff_date";
            $moveStmt = $this->dbStats->conn->prepare($moveQuery);
            $moveStmt->bindParam(':cutoff_date', $cutoffDate);
            $moveStmt->execute();
            $archivedCount = $moveStmt->rowCount();
            
            // Delete archived records from main table
            $deleteQuery = "DELETE FROM attendance WHERE attendance_date < :cutoff_date";
            $deleteStmt = $this->dbStats->conn->prepare($deleteQuery);
            $deleteStmt->bindParam(':cutoff_date', $cutoffDate);
            $deleteStmt->execute();
            
            // Commit transaction
            $this->dbStats->conn->commit();
            
            // Calculate duration
            $duration = microtime(true) - $startTime;
            
            // Log the maintenance operation
            $this->dbStats->logMaintenanceOperation(
                'archiving',
                "Archived attendance records before {$cutoffDate}",
                'success',
                'attendance,attendance_archive',
                $archivedCount,
                $duration,
                0
            );
            
            // Set flash message
            $_SESSION['flash_message'] = "{$archivedCount} attendance records successfully archived.";
            $_SESSION['flash_type'] = 'success';
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->dbStats->conn->inTransaction()) {
                $this->dbStats->conn->rollBack();
            }
            
            $_SESSION['flash_message'] = 'Error archiving attendance records: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            
            // Log the failed operation
            $this->dbStats->logMaintenanceOperation(
                'archiving',
                'Error archiving attendance records: ' . $e->getMessage(),
                'error',
                'attendance',
                0,
                0,
                0
            );
        }
    }
    
    /**
     * Archive SMS messages
     * 
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveSmsMessages($cutoffDate) {
        try {
            // Create SMS model
            require_once 'models/Sms.php';
            $sms = new Sms();
            
            // Check if archive table exists, create if not
            $this->createArchiveTable('sms_messages', 'sms_messages_archive');
            
            // Archive old records
            $startTime = microtime(true);
            
            // Begin transaction
            $this->dbStats->conn->beginTransaction();
            
            // Move old records to archive table
            $moveQuery = "INSERT INTO sms_messages_archive
                SELECT *, NOW() as archived_at FROM sms_messages
                WHERE sent_at < :cutoff_date";
            $moveStmt = $this->dbStats->conn->prepare($moveQuery);
            $moveStmt->bindParam(':cutoff_date', $cutoffDate);
            $moveStmt->execute();
            $archivedCount = $moveStmt->rowCount();
            
            // Delete archived records from main table
            $deleteQuery = "DELETE FROM sms_messages WHERE sent_at < :cutoff_date";
            $deleteStmt = $this->dbStats->conn->prepare($deleteQuery);
            $deleteStmt->bindParam(':cutoff_date', $cutoffDate);
            $deleteStmt->execute();
            
            // Commit transaction
            $this->dbStats->conn->commit();
            
            // Calculate duration
            $duration = microtime(true) - $startTime;
            
            // Log the maintenance operation
            $this->dbStats->logMaintenanceOperation(
                'archiving',
                "Archived SMS messages before {$cutoffDate}",
                'success',
                'sms_messages,sms_messages_archive',
                $archivedCount,
                $duration,
                0
            );
            
            // Set flash message
            $_SESSION['flash_message'] = "{$archivedCount} SMS messages successfully archived.";
            $_SESSION['flash_type'] = 'success';
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->dbStats->conn->inTransaction()) {
                $this->dbStats->conn->rollBack();
            }
            
            $_SESSION['flash_message'] = 'Error archiving SMS messages: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            
            // Log the failed operation
            $this->dbStats->logMaintenanceOperation(
                'archiving',
                'Error archiving SMS messages: ' . $e->getMessage(),
                'error',
                'sms_messages',
                0,
                0,
                0
            );
        }
    }
    
    /**
     * Archive visitor records
     * 
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveVisitors($cutoffDate) {
        try {
            // Create Visitor model
            require_once 'models/Visitor.php';
            $visitor = new Visitor();
            
            // Check if archive table exists, create if not
            $this->createArchiveTable('visitors', 'visitors_archive');
            
            // Archive old records
            $startTime = microtime(true);
            
            // Begin transaction
            $this->dbStats->conn->beginTransaction();
            
            // Move old records to archive table
            $moveQuery = "INSERT INTO visitors_archive
                SELECT *, NOW() as archived_at FROM visitors
                WHERE visit_date < :cutoff_date";
            $moveStmt = $this->dbStats->conn->prepare($moveQuery);
            $moveStmt->bindParam(':cutoff_date', $cutoffDate);
            $moveStmt->execute();
            $archivedCount = $moveStmt->rowCount();
            
            // Delete archived records from main table
            $deleteQuery = "DELETE FROM visitors WHERE visit_date < :cutoff_date";
            $deleteStmt = $this->dbStats->conn->prepare($deleteQuery);
            $deleteStmt->bindParam(':cutoff_date', $cutoffDate);
            $deleteStmt->execute();
            
            // Commit transaction
            $this->dbStats->conn->commit();
            
            // Calculate duration
            $duration = microtime(true) - $startTime;
            
            // Log the maintenance operation
            $this->dbStats->logMaintenanceOperation(
                'archiving',
                "Archived visitor records before {$cutoffDate}",
                'success',
                'visitors,visitors_archive',
                $archivedCount,
                $duration,
                0
            );
            
            // Set flash message
            $_SESSION['flash_message'] = "{$archivedCount} visitor records successfully archived.";
            $_SESSION['flash_type'] = 'success';
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->dbStats->conn->inTransaction()) {
                $this->dbStats->conn->rollBack();
            }
            
            $_SESSION['flash_message'] = 'Error archiving visitor records: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            
            // Log the failed operation
            $this->dbStats->logMaintenanceOperation(
                'archiving',
                'Error archiving visitor records: ' . $e->getMessage(),
                'error',
                'visitors',
                0,
                0,
                0
            );
        }
    }
    
    /**
     * Archive equipment maintenance records
     * 
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveEquipmentMaintenance($cutoffDate) {
        try {
            // Create Equipment model
            require_once 'models/Equipment.php';
            $equipment = new Equipment();
            
            // Check if archive table exists, create if not
            $this->createArchiveTable('equipment_maintenance', 'equipment_maintenance_archive');
            
            // Archive old records
            $startTime = microtime(true);
            
            // Begin transaction
            $this->dbStats->conn->beginTransaction();
            
            // Move old records to archive table
            $moveQuery = "INSERT INTO equipment_maintenance_archive
                SELECT *, NOW() as archived_at FROM equipment_maintenance
                WHERE maintenance_date < :cutoff_date";
            $moveStmt = $this->dbStats->conn->prepare($moveQuery);
            $moveStmt->bindParam(':cutoff_date', $cutoffDate);
            $moveStmt->execute();
            $archivedCount = $moveStmt->rowCount();
            
            // Delete archived records from main table
            $deleteQuery = "DELETE FROM equipment_maintenance WHERE maintenance_date < :cutoff_date";
            $deleteStmt = $this->dbStats->conn->prepare($deleteQuery);
            $deleteStmt->bindParam(':cutoff_date', $cutoffDate);
            $deleteStmt->execute();
            
            // Commit transaction
            $this->dbStats->conn->commit();
            
            // Calculate duration
            $duration = microtime(true) - $startTime;
            
            // Log the maintenance operation
            $this->dbStats->logMaintenanceOperation(
                'archiving',
                "Archived equipment maintenance records before {$cutoffDate}",
                'success',
                'equipment_maintenance,equipment_maintenance_archive',
                $archivedCount,
                $duration,
                0
            );
            
            // Set flash message
            $_SESSION['flash_message'] = "{$archivedCount} equipment maintenance records successfully archived.";
            $_SESSION['flash_type'] = 'success';
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->dbStats->conn->inTransaction()) {
                $this->dbStats->conn->rollBack();
            }
            
            $_SESSION['flash_message'] = 'Error archiving equipment maintenance records: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            
            // Log the failed operation
            $this->dbStats->logMaintenanceOperation(
                'archiving',
                'Error archiving equipment maintenance records: ' . $e->getMessage(),
                'error',
                'equipment_maintenance',
                0,
                0,
                0
            );
        }
    }
    
    /**
     * Create archive table if it doesn't exist
     * 
     * @param string $sourceTable Source table name
     * @param string $archiveTable Archive table name
     * @return bool Success
     */
    private function createArchiveTable($sourceTable, $archiveTable) {
        try {
            // Check if archive table exists
            $checkQuery = "SHOW TABLES LIKE '{$archiveTable}'";
            $checkStmt = $this->dbStats->conn->query($checkQuery);
            
            if ($checkStmt && $checkStmt->rowCount() === 0) {
                // Get source table structure
                $structureQuery = "SHOW CREATE TABLE {$sourceTable}";
                $structureStmt = $this->dbStats->conn->query($structureQuery);
                $structureRow = $structureStmt->fetch(PDO::FETCH_ASSOC);
                
                if (isset($structureRow['Create Table'])) {
                    // Modify create table statement for archive table
                    $createTableSql = $structureRow['Create Table'];
                    $createTableSql = str_replace("CREATE TABLE `{$sourceTable}`", "CREATE TABLE `{$archiveTable}`", $createTableSql);
                    
                    // Add archived_at column if it doesn't exist in the statement
                    if (strpos($createTableSql, 'archived_at') === false) {
                        $createTableSql = str_replace('PRIMARY KEY', 'archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, PRIMARY KEY', $createTableSql);
                    }
                    
                    // Create archive table
                    $this->dbStats->conn->exec($createTableSql);
                    
                    return true;
                }
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Error creating archive table: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Display maintenance log
     * 
     * @return void
     */
    public function maintenanceLog() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to access the maintenance log.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        // Get page and limit parameters
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
        
        // Calculate offset
        $offset = ($page - 1) * $limit;
        
        // Get maintenance log entries
        $logEntries = $this->dbStats->getMaintenanceLog($limit, $offset);
        $totalCount = $this->dbStats->getMaintenanceLogCount();
        
        // Calculate total pages
        $totalPages = ceil($totalCount / $limit);
        
        // Set page title and active page
        $page_title = 'Maintenance Log - ICGC Emmanuel Temple';
        $active_page = 'settings';
        
        // Include the view
        require_once 'views/settings/maintenance_log.php';
    }
    
    /**
     * Display maintenance scheduling page
     * 
     * @return void
     */
    public function maintenanceSchedule() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to access maintenance scheduling.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        // Set page title and active page
        $page_title = 'Maintenance Scheduling - ICGC Emmanuel Temple';
        $active_page = 'settings';
        
        // Include the view
        require_once 'views/settings/maintenance_schedule.php';
    }
    
    /**
     * Process maintenance schedule
     * 
     * @return void
     */
    public function processSchedule() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to schedule maintenance.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: ' . BASE_URL . 'settings/maintenance-schedule');
            exit;
        }
        
        // Get form data
        $scheduleType = isset($_POST['schedule_type']) ? $_POST['schedule_type'] : '';
        $scheduleDay = isset($_POST['schedule_day']) ? (int)$_POST['schedule_day'] : 1;
        $scheduleTime = isset($_POST['schedule_time']) ? $_POST['schedule_time'] : '00:00';
        $maintenanceTypes = isset($_POST['maintenance_types']) ? $_POST['maintenance_types'] : [];
        
        // Validate form data
        if (empty($scheduleType) || empty($scheduleTime) || empty($maintenanceTypes)) {
            $_SESSION['flash_message'] = 'Please fill in all required fields.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings/maintenance-schedule');
            exit;
        }
        
        // Save schedule settings
        $settings = [
            'schedule_type' => $scheduleType,
            'schedule_day' => $scheduleDay,
            'schedule_time' => $scheduleTime,
            'maintenance_types' => $maintenanceTypes,
            'last_updated' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user_id']
        ];
        
        // Save settings to database
        $this->saveMaintenanceSettings($settings);
        
        // Set flash message
        $_SESSION['flash_message'] = 'Maintenance schedule saved successfully.';
        $_SESSION['flash_type'] = 'success';
        
        // Redirect back to maintenance schedule page
        header('Location: ' . BASE_URL . 'settings/maintenance-schedule');
        exit;
    }
    
    /**
     * Save maintenance settings
     * 
     * @param array $settings Settings to save
     * @return bool Success
     */
    private function saveMaintenanceSettings($settings) {
        try {
            // Convert settings to JSON
            $settingsJson = json_encode($settings);
            
            // Check if settings already exist
            $checkQuery = "SELECT id FROM settings WHERE setting_key = 'maintenance_schedule'";
            $checkStmt = $this->dbStats->conn->query($checkQuery);
            
            if ($checkStmt && $checkStmt->rowCount() > 0) {
                // Update existing settings
                $updateQuery = "UPDATE settings SET setting_value = :value WHERE setting_key = 'maintenance_schedule'";
                $updateStmt = $this->dbStats->conn->prepare($updateQuery);
                $updateStmt->bindParam(':value', $settingsJson);
                return $updateStmt->execute();
            } else {
                // Insert new settings
                $insertQuery = "INSERT INTO settings (setting_key, setting_value) VALUES ('maintenance_schedule', :value)";
                $insertStmt = $this->dbStats->conn->prepare($insertQuery);
                $insertStmt->bindParam(':value', $settingsJson);
                return $insertStmt->execute();
            }
        } catch (PDOException $e) {
            error_log("Error saving maintenance settings: " . $e->getMessage());
            return false;
        }
    }
}
?>
