<?php
/**
 * Visitor Follow-Up Model
 *
 * This model handles all database operations related to visitor follow-ups
 */

class VisitorFollowUp {
    private $conn;
    private $table = 'visitor_follow_ups';

    // Follow-up properties
    public $id;
    public $visitor_id;
    public $follow_up_date;
    public $follow_up_type;
    public $notes;
    public $conducted_by;
    public $created_at;
    public $updated_at;

    /**
     * Constructor
     *
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all follow-ups
     *
     * @param string $orderBy Column to order by
     * @param string $order Order direction (ASC or DESC)
     * @return PDOStatement
     */
    public function getAll($orderBy = 'follow_up_date', $order = 'DESC') {
        $query = "SELECT f.*, v.first_name, v.last_name, v.phone_number, u.username
                  FROM " . $this->table . " f
                  LEFT JOIN visitors v ON f.visitor_id = v.id
                  LEFT JOIN users u ON f.conducted_by = u.id
                  ORDER BY " . $orderBy . " " . $order;

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get follow-ups by visitor ID
     *
     * @param int $visitor_id Visitor ID
     * @return PDOStatement
     */
    public function getByVisitorId($visitor_id) {
        $query = "SELECT f.*, u.username
                  FROM " . $this->table . " f
                  LEFT JOIN users u ON f.conducted_by = u.id
                  WHERE f.visitor_id = :visitor_id
                  ORDER BY f.follow_up_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':visitor_id', $visitor_id);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get follow-up by ID
     *
     * @param int $id Follow-up ID
     * @return bool
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            $this->id = $row['id'];
            $this->visitor_id = $row['visitor_id'];
            $this->follow_up_date = $row['follow_up_date'];
            $this->follow_up_type = $row['follow_up_type'];
            $this->notes = $row['notes'];
            $this->conducted_by = $row['conducted_by'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];

            return true;
        }

        return false;
    }

    /**
     * Create new follow-up
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table . "
                  (visitor_id, follow_up_date, follow_up_type, notes, conducted_by, created_at, updated_at)
                  VALUES
                  (:visitor_id, :follow_up_date, :follow_up_type, :notes, :conducted_by, :created_at, :updated_at)";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Set timestamps
        $this->created_at = date('Y-m-d H:i:s');
        $this->updated_at = date('Y-m-d H:i:s');

        // Bind parameters
        $stmt->bindParam(':visitor_id', $this->visitor_id);
        $stmt->bindParam(':follow_up_date', $this->follow_up_date);
        $stmt->bindParam(':follow_up_type', $this->follow_up_type);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':conducted_by', $this->conducted_by);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Update follow-up
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table . "
                  SET
                  visitor_id = :visitor_id,
                  follow_up_date = :follow_up_date,
                  follow_up_type = :follow_up_type,
                  notes = :notes,
                  conducted_by = :conducted_by,
                  updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Set updated timestamp
        $this->updated_at = date('Y-m-d H:i:s');

        // Bind parameters
        $stmt->bindParam(':visitor_id', $this->visitor_id);
        $stmt->bindParam(':follow_up_date', $this->follow_up_date);
        $stmt->bindParam(':follow_up_type', $this->follow_up_type);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':conducted_by', $this->conducted_by);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Delete follow-up
     *
     * @param int $id Follow-up ID
     * @return bool
     */
    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Get upcoming follow-ups
     *
     * @param int $days Number of days to look ahead
     * @return PDOStatement
     */
    public function getUpcoming($days = 7) {
        $query = "SELECT f.*, v.first_name, v.last_name, v.phone_number, u.username
                  FROM " . $this->table . " f
                  LEFT JOIN visitors v ON f.visitor_id = v.id
                  LEFT JOIN users u ON f.conducted_by = u.id
                  WHERE f.follow_up_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :days DAY)
                  AND v.visitor_status != 'converted'
                  ORDER BY f.follow_up_date ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':days', $days, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get follow-up statistics
     *
     * @return array
     */
    public function getStatistics() {
        $stats = [];

        // Total follow-ups
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Follow-ups by type
        $query = "SELECT follow_up_type, COUNT(*) as count
                  FROM " . $this->table . "
                  GROUP BY follow_up_type";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['by_type'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Upcoming follow-ups
        $query = "SELECT COUNT(*) as upcoming
                  FROM " . $this->table . "
                  WHERE follow_up_date >= CURDATE()";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['upcoming'] = $stmt->fetch(PDO::FETCH_ASSOC)['upcoming'];

        // Recent follow-ups (last 30 days)
        $query = "SELECT COUNT(*) as recent
                  FROM " . $this->table . "
                  WHERE follow_up_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['recent'] = $stmt->fetch(PDO::FETCH_ASSOC)['recent'];

        return $stats;
    }
}
