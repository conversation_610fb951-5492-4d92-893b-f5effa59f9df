<?php
/**
 * Export Utilities
 * Functions for exporting data to various formats
 */

/**
 * Export data to PDF
 *
 * @param array $data The data to export
 * @param array $columns The column headers
 * @param string $title The report title
 * @param string $filename The filename without extension
 * @return void
 */
function export_to_pdf($data, $columns, $title, $filename) {
    // Require TCPDF library
    require_once 'vendor/tcpdf/tcpdf.php';
    
    // Create new PDF document
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    
    // Set document information
    $pdf->SetCreator('ICGC Emmanuel Temple');
    $pdf->SetAuthor('ICGC Emmanuel Temple');
    $pdf->SetTitle($title);
    $pdf->SetSubject($title);
    
    // Set default header data
    $pdf->SetHeaderData('', 0, $title, 'Generated on: ' . date('Y-m-d H:i:s'));
    
    // Set header and footer fonts
    $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
    $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
    
    // Set default monospaced font
    $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
    
    // Set margins
    $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
    $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
    $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
    
    // Set auto page breaks
    $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
    
    // Set image scale factor
    $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
    
    // Add a page
    $pdf->AddPage();
    
    // Set font
    $pdf->SetFont('helvetica', '', 10);
    
    // Create the table header
    $html = '<table border="1" cellpadding="5">';
    $html .= '<tr style="background-color:#3F7D58;color:white;">';
    foreach ($columns as $column) {
        $html .= '<th>' . $column . '</th>';
    }
    $html .= '</tr>';
    
    // Add data rows
    foreach ($data as $row) {
        $html .= '<tr>';
        foreach ($row as $key => $value) {
            // Skip keys that aren't in the columns array
            if (!in_array(ucfirst($key), $columns) && !in_array($key, array_keys(array_flip($columns)))) {
                continue;
            }
            $html .= '<td>' . $value . '</td>';
        }
        $html .= '</tr>';
    }
    $html .= '</table>';
    
    // Output the HTML content
    $pdf->writeHTML($html, true, false, true, false, '');
    
    // Close and output PDF document
    $pdf->Output($filename . '.pdf', 'D');
    exit;
}

/**
 * Export data to Excel
 *
 * @param array $data The data to export
 * @param array $columns The column headers
 * @param string $title The report title
 * @param string $filename The filename without extension
 * @return void
 */
function export_to_excel($data, $columns, $title, $filename) {
    // Require PhpSpreadsheet library
    require_once 'vendor/autoload.php';
    
    // Create new Spreadsheet object
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    
    // Set document properties
    $spreadsheet->getProperties()
        ->setCreator('ICGC Emmanuel Temple')
        ->setLastModifiedBy('ICGC Emmanuel Temple')
        ->setTitle($title)
        ->setSubject($title)
        ->setDescription($title)
        ->setKeywords($title)
        ->setCategory('Report');
    
    // Add header row
    $sheet = $spreadsheet->getActiveSheet();
    $col = 1;
    foreach ($columns as $column) {
        $sheet->setCellValueByColumnAndRow($col++, 1, $column);
    }
    
    // Style the header row
    $headerStyle = [
        'font' => [
            'bold' => true,
            'color' => ['rgb' => 'FFFFFF'],
        ],
        'fill' => [
            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
            'startColor' => ['rgb' => '3F7D58'],
        ],
    ];
    $sheet->getStyle('A1:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($columns)) . '1')->applyFromArray($headerStyle);
    
    // Add data rows
    $row = 2;
    foreach ($data as $dataRow) {
        $col = 1;
        foreach ($dataRow as $key => $value) {
            // Skip keys that aren't in the columns array
            if (!in_array(ucfirst($key), $columns) && !in_array($key, array_keys(array_flip($columns)))) {
                continue;
            }
            $sheet->setCellValueByColumnAndRow($col++, $row, $value);
        }
        $row++;
    }
    
    // Auto-size columns
    foreach (range(1, count($columns)) as $col) {
        $sheet->getColumnDimensionByColumn($col)->setAutoSize(true);
    }
    
    // Create writer
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    
    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    // Save file to output
    $writer->save('php://output');
    exit;
}

/**
 * Export data to CSV
 *
 * @param array $data The data to export
 * @param array $columns The column headers
 * @param string $filename The filename without extension
 * @return void
 */
function export_to_csv($data, $columns, $filename) {
    // Set headers for download
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment;filename="' . $filename . '.csv"');
    
    // Open output stream
    $output = fopen('php://output', 'w');
    
    // Add column headers
    fputcsv($output, $columns);
    
    // Add data rows
    foreach ($data as $row) {
        $csvRow = [];
        foreach ($row as $key => $value) {
            // Skip keys that aren't in the columns array
            if (!in_array(ucfirst($key), $columns) && !in_array($key, array_keys(array_flip($columns)))) {
                continue;
            }
            $csvRow[] = $value;
        }
        fputcsv($output, $csvRow);
    }
    
    // Close output stream
    fclose($output);
    exit;
}
