<?php
/**
 * Family Relationship Model
 * Manages parent-child relationships and guardian information
 */

class FamilyRelationship {
    // Database connection and table name
    private $conn;
    private $table_name = "family_relationships";

    // Object properties
    public $id;
    public $parent_id;
    public $child_id;
    public $relationship_type;
    public $is_primary;
    public $can_pickup;
    public $notes;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create family relationship
     *
     * @param array $data Optional array of relationship data
     * @return bool
     */
    public function create($data = null) {
        // If data array is provided, set object properties
        if (is_array($data)) {
            $this->parent_id = $data['parent_id'] ?? $this->parent_id;
            $this->child_id = $data['child_id'] ?? $this->child_id;
            $this->relationship_type = $data['relationship_type'] ?? $this->relationship_type ?? 'parent';
            $this->is_primary = $data['is_primary'] ?? $this->is_primary ?? 1;
            $this->can_pickup = $data['can_pickup'] ?? $this->can_pickup ?? 1;
            $this->notes = $data['notes'] ?? $this->notes ?? '';
            $this->created_at = $data['created_at'] ?? date('Y-m-d H:i:s');
            $this->updated_at = $data['updated_at'] ?? date('Y-m-d H:i:s');
        }

        // Validate required fields
        if (empty($this->parent_id) || empty($this->child_id)) {
            error_log("FamilyRelationship create failed: Missing parent_id or child_id");
            return false;
        }

        // Enhanced duplicate relationship prevention
        if ($this->relationshipExists($this->parent_id, $this->child_id, $this->relationship_type)) {
            error_log("FamilyRelationship create failed: Relationship already exists - Parent ID: {$this->parent_id}, Child ID: {$this->child_id}, Type: {$this->relationship_type}");
            return false; // Changed from true to false to properly indicate duplicate
        }

        // Additional validation: Check for self-relationships
        if ($this->parent_id == $this->child_id) {
            error_log("FamilyRelationship create failed: Self-relationship not allowed - Member ID: {$this->parent_id}");
            return false;
        }

        // Additional validation: Check for age consistency (optional warning)
        if ($this->isAgeInconsistent($this->parent_id, $this->child_id)) {
            error_log("FamilyRelationship create warning: Potential age inconsistency - Parent ID: {$this->parent_id}, Child ID: {$this->child_id}");
            // Continue with creation but log the warning
        }

        $query = "INSERT INTO " . $this->table_name . "
                  SET parent_id = :parent_id,
                      child_id = :child_id,
                      relationship_type = :relationship_type,
                      is_primary = :is_primary,
                      can_pickup = :can_pickup,
                      notes = :notes,
                      created_at = :created_at,
                      updated_at = :updated_at";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->parent_id = htmlspecialchars(strip_tags($this->parent_id));
        $this->child_id = htmlspecialchars(strip_tags($this->child_id));
        $this->relationship_type = htmlspecialchars(strip_tags($this->relationship_type));
        $this->is_primary = $this->is_primary ? 1 : 0;
        $this->can_pickup = $this->can_pickup ? 1 : 0;
        $this->notes = htmlspecialchars(strip_tags($this->notes ?? ''));

        // Bind data
        $stmt->bindParam(':parent_id', $this->parent_id);
        $stmt->bindParam(':child_id', $this->child_id);
        $stmt->bindParam(':relationship_type', $this->relationship_type);
        $stmt->bindParam(':is_primary', $this->is_primary);
        $stmt->bindParam(':can_pickup', $this->can_pickup);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        try {
            if ($stmt->execute()) {
                error_log("FamilyRelationship created successfully: Parent ID {$this->parent_id} -> Child ID {$this->child_id} ({$this->relationship_type})");
                return true;
            }

            // Enhanced error logging for debugging
            $error = $stmt->errorInfo();
            error_log("FamilyRelationship create failed: " . $error[2] . " - Parent ID: {$this->parent_id}, Child ID: {$this->child_id}, Type: {$this->relationship_type}");

            // Check for specific constraint violations
            if (strpos($error[2], 'Duplicate entry') !== false) {
                error_log("FamilyRelationship create failed: Duplicate relationship detected by database constraint");
            } elseif (strpos($error[2], 'foreign key constraint') !== false) {
                error_log("FamilyRelationship create failed: Invalid parent or child ID (member does not exist)");
            }

            return false;
        } catch (Exception $e) {
            error_log("FamilyRelationship create exception: " . $e->getMessage() . " - Parent ID: {$this->parent_id}, Child ID: {$this->child_id}, Type: {$this->relationship_type}");
            return false;
        }
    }

    /**
     * Get children for a parent
     *
     * @param int $parent_id
     * @return PDOStatement
     */
    public function getChildrenByParent($parent_id) {
        $query = "SELECT fr.*,
                         m.first_name, m.last_name, m.date_of_birth, m.gender,
                         m.profile_picture, m.member_status,
                         CASE
                             WHEN m.date_of_birth IS NULL OR m.date_of_birth = '0000-00-00' OR m.date_of_birth = '1970-01-01'
                             THEN NULL
                             ELSE TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE())
                         END as age
                  FROM " . $this->table_name . " fr
                  JOIN members m ON fr.child_id = m.id
                  WHERE fr.parent_id = :parent_id
                  ORDER BY CASE
                              WHEN m.date_of_birth IS NULL OR m.date_of_birth = '0000-00-00' OR m.date_of_birth = '1970-01-01'
                              THEN 1
                              ELSE 0
                           END,
                           m.date_of_birth DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':parent_id', $parent_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get parents/guardians for a child
     *
     * @param int $child_id
     * @return PDOStatement
     */
    public function getParentsByChild($child_id) {
        $query = "SELECT fr.*, 
                         m.first_name, m.last_name, m.phone_number, m.email,
                         m.profile_picture, m.member_status
                  FROM " . $this->table_name . " fr
                  JOIN members m ON fr.parent_id = m.id
                  WHERE fr.child_id = :child_id
                  ORDER BY fr.is_primary DESC, fr.relationship_type";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get siblings for a child (other children with the same parents)
     *
     * @param int $child_id
     * @return PDOStatement
     */
    public function getSiblingsByChild($child_id) {
        $query = "SELECT DISTINCT s.*,
                         m.first_name, m.last_name, m.date_of_birth, m.gender,
                         m.profile_picture, m.member_status,
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
                  FROM " . $this->table_name . " fr1
                  JOIN " . $this->table_name . " s ON fr1.parent_id = s.parent_id
                  JOIN members m ON s.child_id = m.id
                  WHERE fr1.child_id = :child_id
                  AND s.child_id != :child_id
                  ORDER BY m.date_of_birth DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();

        return $stmt;
    }

    // Auto-detection methods removed for stability
    // Use manual parent-child assignment instead

    /**
     * Get all family relationships
     *
     * @return PDOStatement
     */
    public function getAll() {
        $query = "SELECT fr.*,
                         p.first_name as parent_first_name, p.last_name as parent_last_name,
                         c.first_name as child_first_name, c.last_name as child_last_name,
                         TIMESTAMPDIFF(YEAR, c.date_of_birth, CURDATE()) as child_age,
                         p.created_at as parent_created_at
                  FROM " . $this->table_name . " fr
                  JOIN members p ON fr.parent_id = p.id
                  JOIN members c ON fr.child_id = c.id
                  ORDER BY p.created_at DESC, p.last_name, p.first_name, c.date_of_birth";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Check if relationship exists
     *
     * @param int $parent_id
     * @param int $child_id
     * @param string $relationship_type
     * @return bool
     */
    public function relationshipExists($parent_id, $child_id, $relationship_type) {
        $query = "SELECT id FROM " . $this->table_name . "
                  WHERE parent_id = :parent_id 
                  AND child_id = :child_id 
                  AND relationship_type = :relationship_type";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':parent_id', $parent_id);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->bindParam(':relationship_type', $relationship_type);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    /**
     * Get authorized pickup persons for a child
     *
     * @param int $child_id
     * @return PDOStatement
     */
    public function getAuthorizedPickupPersons($child_id) {
        $query = "SELECT fr.*, 
                         m.first_name, m.last_name, m.phone_number, m.profile_picture
                  FROM " . $this->table_name . " fr
                  JOIN members m ON fr.parent_id = m.id
                  WHERE fr.child_id = :child_id 
                  AND fr.can_pickup = 1
                  AND m.member_status = 'active'
                  ORDER BY fr.is_primary DESC, fr.relationship_type";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Update relationship
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET relationship_type = :relationship_type,
                      is_primary = :is_primary,
                      can_pickup = :can_pickup,
                      notes = :notes,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->relationship_type = htmlspecialchars(strip_tags($this->relationship_type));
        $this->is_primary = $this->is_primary ? 1 : 0;
        $this->can_pickup = $this->can_pickup ? 1 : 0;
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Bind data
        $stmt->bindParam(':relationship_type', $this->relationship_type);
        $stmt->bindParam(':is_primary', $this->is_primary);
        $stmt->bindParam(':can_pickup', $this->can_pickup);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Delete relationship
     *
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Delete relationships by child ID
     *
     * @param int $child_id
     * @return bool
     */
    public function deleteByChildId($child_id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE child_id = :child_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);

        return $stmt->execute();
    }

    /**
     * Get family statistics
     *
     * @return array
     */
    public function getFamilyStats() {
        $query = "SELECT 
                    COUNT(DISTINCT parent_id) as total_parents,
                    COUNT(DISTINCT child_id) as total_children,
                    COUNT(*) as total_relationships,
                    COUNT(CASE WHEN relationship_type = 'parent' THEN 1 END) as parent_relationships,
                    COUNT(CASE WHEN relationship_type = 'guardian' THEN 1 END) as guardian_relationships,
                    COUNT(CASE WHEN relationship_type = 'emergency_contact' THEN 1 END) as emergency_contacts
                  FROM " . $this->table_name;

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get relationship by ID
     *
     * @param int $id
     * @return array|false
     */
    public function getById($id) {
        $query = "SELECT fr.*,
                         p.first_name as parent_first_name, p.last_name as parent_last_name,
                         c.first_name as child_first_name, c.last_name as child_last_name,
                         TIMESTAMPDIFF(YEAR, c.date_of_birth, CURDATE()) as child_age
                  FROM " . $this->table_name . " fr
                  JOIN members p ON fr.parent_id = p.id
                  JOIN members c ON fr.child_id = c.id
                  WHERE fr.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Check if relationship exists excluding a specific ID (for updates)
     *
     * @param int $parent_id
     * @param int $child_id
     * @param string $relationship_type
     * @param int $exclude_id
     * @return bool
     */
    public function relationshipExistsExcluding($parent_id, $child_id, $relationship_type, $exclude_id) {
        $query = "SELECT id FROM " . $this->table_name . "
                  WHERE parent_id = :parent_id
                  AND child_id = :child_id
                  AND relationship_type = :relationship_type
                  AND id != :exclude_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':parent_id', $parent_id);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->bindParam(':relationship_type', $relationship_type);
        $stmt->bindParam(':exclude_id', $exclude_id);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    /**
     * Check for age inconsistency between parent and child
     *
     * @param int $parent_id
     * @param int $child_id
     * @return bool True if there's an age inconsistency
     */
    private function isAgeInconsistent($parent_id, $child_id) {
        try {
            $query = "SELECT
                        TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) as parent_age,
                        TIMESTAMPDIFF(YEAR, c.date_of_birth, CURDATE()) as child_age
                      FROM members p, members c
                      WHERE p.id = :parent_id
                      AND c.id = :child_id
                      AND p.date_of_birth IS NOT NULL
                      AND c.date_of_birth IS NOT NULL";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':parent_id', $parent_id);
            $stmt->bindParam(':child_id', $child_id);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                // Consider it inconsistent if parent is younger than child or age difference is less than 10 years
                return ($result['parent_age'] <= $result['child_age']) ||
                       (($result['parent_age'] - $result['child_age']) < 10);
            }

            return false; // No age data available, assume consistent

        } catch (Exception $e) {
            error_log("Age consistency check failed: " . $e->getMessage());
            return false; // Assume consistent on error
        }
    }

    /**
     * Update relationship with parent and child IDs
     *
     * @return bool
     */
    public function updateComplete() {
        $query = "UPDATE " . $this->table_name . "
                  SET parent_id = :parent_id,
                      child_id = :child_id,
                      relationship_type = :relationship_type,
                      is_primary = :is_primary,
                      can_pickup = :can_pickup,
                      notes = :notes,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->parent_id = htmlspecialchars(strip_tags($this->parent_id));
        $this->child_id = htmlspecialchars(strip_tags($this->child_id));
        $this->relationship_type = htmlspecialchars(strip_tags($this->relationship_type));
        $this->is_primary = $this->is_primary ? 1 : 0;
        $this->can_pickup = $this->can_pickup ? 1 : 0;
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Bind data
        $stmt->bindParam(':parent_id', $this->parent_id);
        $stmt->bindParam(':child_id', $this->child_id);
        $stmt->bindParam(':relationship_type', $this->relationship_type);
        $stmt->bindParam(':is_primary', $this->is_primary);
        $stmt->bindParam(':can_pickup', $this->can_pickup);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }
}
