<?php
/**
 * Program Model for Church Program & Activities Planner
 */

class Program {
    // Database connection and table name
    private $conn;
    private $table_name = "church_programs";

    // Object properties
    public $id;
    public $title;
    public $description;
    public $category_id;
    public $department_id;
    public $coordinator_id;
    public $start_date;
    public $end_date;
    public $start_time;
    public $end_time;
    public $location;
    public $budget_allocated;
    public $budget_spent;
    public $expected_attendance;
    public $actual_attendance;
    public $status;
    public $priority;
    public $is_recurring;
    public $recurrence_pattern;
    public $requires_registration;
    public $max_participants;
    public $notes;
    public $created_by;
    public $approved_by;
    public $approved_at;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db = null) {
        $this->conn = $db;

        // If no database connection is provided, get one from the Database class
        if ($this->conn === null) {
            require_once 'config/database.php';
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }

    /**
     * Get all programs with optional filters
     *
     * @param array $filters
     * @return PDOStatement
     */
    public function getAll($filters = []) {
        $query = "SELECT p.*, 
                         pc.name as category_name, pc.color_code, pc.icon,
                         md.name as department_name,
                         CONCAT(m.first_name, ' ', m.last_name) as coordinator_name,
                         CONCAT(u.username) as created_by_name
                  FROM " . $this->table_name . " p
                  LEFT JOIN program_categories pc ON p.category_id = pc.id
                  LEFT JOIN ministry_departments md ON p.department_id = md.id
                  LEFT JOIN members m ON p.coordinator_id = m.id
                  LEFT JOIN users u ON p.created_by = u.id";

        $conditions = [];
        $params = [];

        // Apply filters
        if (!empty($filters['status'])) {
            $conditions[] = "p.status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($filters['category_id'])) {
            $conditions[] = "p.category_id = :category_id";
            $params[':category_id'] = $filters['category_id'];
        }

        if (!empty($filters['department_id'])) {
            $conditions[] = "p.department_id = :department_id";
            $params[':department_id'] = $filters['department_id'];
        }

        if (!empty($filters['month'])) {
            $conditions[] = "MONTH(p.start_date) = :month";
            $params[':month'] = $filters['month'];
        }

        if (!empty($filters['year'])) {
            $conditions[] = "YEAR(p.start_date) = :year";
            $params[':year'] = $filters['year'];
        }

        if (!empty($filters['search'])) {
            $conditions[] = "(p.title LIKE :search OR p.description LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $query .= " ORDER BY p.start_date ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);

        return $stmt;
    }

    /**
     * Get program by ID
     *
     * @param int $id
     * @return array|false
     */
    public function getById($id) {
        $query = "SELECT p.*, 
                         pc.name as category_name, pc.color_code, pc.icon,
                         md.name as department_name,
                         CONCAT(m.first_name, ' ', m.last_name) as coordinator_name,
                         m.email as coordinator_email, m.phone_number as coordinator_phone,
                         CONCAT(u.username) as created_by_name
                  FROM " . $this->table_name . " p
                  LEFT JOIN program_categories pc ON p.category_id = pc.id
                  LEFT JOIN ministry_departments md ON p.department_id = md.id
                  LEFT JOIN members m ON p.coordinator_id = m.id
                  LEFT JOIN users u ON p.created_by = u.id
                  WHERE p.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create new program
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (title, description, category_id, department_id, coordinator_id,
                   start_date, end_date, start_time, end_time, location,
                   budget_allocated, expected_attendance, status, priority,
                   is_recurring, recurrence_pattern, requires_registration,
                   max_participants, notes, created_by)
                  VALUES
                  (:title, :description, :category_id, :department_id, :coordinator_id,
                   :start_date, :end_date, :start_time, :end_time, :location,
                   :budget_allocated, :expected_attendance, :status, :priority,
                   :is_recurring, :recurrence_pattern, :requires_registration,
                   :max_participants, :notes, :created_by)";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->location = htmlspecialchars(strip_tags($this->location));
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Bind parameters
        $stmt->bindParam(':title', $this->title);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':category_id', $this->category_id);
        $stmt->bindParam(':department_id', $this->department_id);
        $stmt->bindParam(':coordinator_id', $this->coordinator_id);
        $stmt->bindParam(':start_date', $this->start_date);
        $stmt->bindParam(':end_date', $this->end_date);
        $stmt->bindParam(':start_time', $this->start_time);
        $stmt->bindParam(':end_time', $this->end_time);
        $stmt->bindParam(':location', $this->location);
        $stmt->bindParam(':budget_allocated', $this->budget_allocated);
        $stmt->bindParam(':expected_attendance', $this->expected_attendance);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':priority', $this->priority);
        $stmt->bindParam(':is_recurring', $this->is_recurring);
        $stmt->bindParam(':recurrence_pattern', $this->recurrence_pattern);
        $stmt->bindParam(':requires_registration', $this->requires_registration);
        $stmt->bindParam(':max_participants', $this->max_participants);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':created_by', $this->created_by);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Update program
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET title = :title,
                      description = :description,
                      category_id = :category_id,
                      department_id = :department_id,
                      coordinator_id = :coordinator_id,
                      start_date = :start_date,
                      end_date = :end_date,
                      start_time = :start_time,
                      end_time = :end_time,
                      location = :location,
                      budget_allocated = :budget_allocated,
                      expected_attendance = :expected_attendance,
                      status = :status,
                      priority = :priority,
                      is_recurring = :is_recurring,
                      recurrence_pattern = :recurrence_pattern,
                      requires_registration = :requires_registration,
                      max_participants = :max_participants,
                      notes = :notes,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->location = htmlspecialchars(strip_tags($this->location));
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':title', $this->title);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':category_id', $this->category_id);
        $stmt->bindParam(':department_id', $this->department_id);
        $stmt->bindParam(':coordinator_id', $this->coordinator_id);
        $stmt->bindParam(':start_date', $this->start_date);
        $stmt->bindParam(':end_date', $this->end_date);
        $stmt->bindParam(':start_time', $this->start_time);
        $stmt->bindParam(':end_time', $this->end_time);
        $stmt->bindParam(':location', $this->location);
        $stmt->bindParam(':budget_allocated', $this->budget_allocated);
        $stmt->bindParam(':expected_attendance', $this->expected_attendance);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':priority', $this->priority);
        $stmt->bindParam(':is_recurring', $this->is_recurring);
        $stmt->bindParam(':recurrence_pattern', $this->recurrence_pattern);
        $stmt->bindParam(':requires_registration', $this->requires_registration);
        $stmt->bindParam(':max_participants', $this->max_participants);
        $stmt->bindParam(':notes', $this->notes);

        return $stmt->execute();
    }

    /**
     * Delete program
     *
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        return $stmt->execute();
    }

    /**
     * Update program status
     *
     * @param string $new_status
     * @param int $changed_by
     * @param string $reason
     * @return bool
     */
    public function updateStatus($new_status, $changed_by, $reason = '') {
        // Get current status
        $current_program = $this->getById($this->id);
        $old_status = $current_program['status'];

        // Update program status
        $query = "UPDATE " . $this->table_name . "
                  SET status = :status, updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $new_status);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            // Log status change in history
            $history_query = "INSERT INTO program_status_history
                             (program_id, old_status, new_status, changed_by, change_reason)
                             VALUES (:program_id, :old_status, :new_status, :changed_by, :reason)";
            $history_stmt = $this->conn->prepare($history_query);
            $history_stmt->bindParam(':program_id', $this->id);
            $history_stmt->bindParam(':old_status', $old_status);
            $history_stmt->bindParam(':new_status', $new_status);
            $history_stmt->bindParam(':changed_by', $changed_by);
            $history_stmt->bindParam(':reason', $reason);
            $history_stmt->execute();

            return true;
        }

        return false;
    }

    /**
     * Get programs by date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return PDOStatement
     */
    public function getByDateRange($start_date, $end_date) {
        $query = "SELECT p.*,
                         pc.name as category_name, pc.color_code, pc.icon,
                         md.name as department_name,
                         CONCAT(m.first_name, ' ', m.last_name) as coordinator_name
                  FROM " . $this->table_name . " p
                  LEFT JOIN program_categories pc ON p.category_id = pc.id
                  LEFT JOIN ministry_departments md ON p.department_id = md.id
                  LEFT JOIN members m ON p.coordinator_id = m.id
                  WHERE p.start_date >= :start_date AND p.start_date <= :end_date
                  ORDER BY p.start_date ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get upcoming programs
     *
     * @param int $limit
     * @return PDOStatement
     */
    public function getUpcoming($limit = 10) {
        $query = "SELECT p.*,
                         pc.name as category_name, pc.color_code, pc.icon,
                         md.name as department_name,
                         CONCAT(m.first_name, ' ', m.last_name) as coordinator_name
                  FROM " . $this->table_name . " p
                  LEFT JOIN program_categories pc ON p.category_id = pc.id
                  LEFT JOIN ministry_departments md ON p.department_id = md.id
                  LEFT JOIN members m ON p.coordinator_id = m.id
                  WHERE p.start_date >= CURDATE() AND p.status != 'cancelled'
                  ORDER BY p.start_date ASC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get programs by coordinator
     *
     * @param int $coordinator_id
     * @return PDOStatement
     */
    public function getByCoordinator($coordinator_id) {
        $query = "SELECT p.*,
                         pc.name as category_name, pc.color_code, pc.icon,
                         md.name as department_name
                  FROM " . $this->table_name . " p
                  LEFT JOIN program_categories pc ON p.category_id = pc.id
                  LEFT JOIN ministry_departments md ON p.department_id = md.id
                  WHERE p.coordinator_id = :coordinator_id
                  ORDER BY p.start_date ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':coordinator_id', $coordinator_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get program statistics
     *
     * @return array
     */
    public function getStatistics() {
        $stats = [];

        // Total programs
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Programs by status
        $query = "SELECT status, COUNT(*) as count FROM " . $this->table_name . " GROUP BY status";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $status_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($status_counts as $status) {
            $stats['by_status'][$status['status']] = $status['count'];
        }

        // This year's programs
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE YEAR(start_date) = YEAR(CURDATE())";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['this_year'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // This month's programs
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . "
                  WHERE YEAR(start_date) = YEAR(CURDATE()) AND MONTH(start_date) = MONTH(CURDATE())";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['this_month'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        return $stats;
    }

    /**
     * Validate program data
     *
     * @return array
     */
    public function validate() {
        $errors = [];

        if (empty($this->title)) {
            $errors[] = "Program title is required";
        }

        if (empty($this->category_id)) {
            $errors[] = "Program category is required";
        }

        if (empty($this->department_id)) {
            $errors[] = "Ministry department is required";
        }

        if (empty($this->start_date)) {
            $errors[] = "Start date is required";
        }

        if (empty($this->end_date)) {
            $errors[] = "End date is required";
        }

        if (!empty($this->start_date) && !empty($this->end_date)) {
            if (strtotime($this->start_date) > strtotime($this->end_date)) {
                $errors[] = "End date must be after start date";
            }
        }

        if (!empty($this->budget_allocated) && $this->budget_allocated < 0) {
            $errors[] = "Budget allocated cannot be negative";
        }

        if (!empty($this->expected_attendance) && $this->expected_attendance < 0) {
            $errors[] = "Expected attendance cannot be negative";
        }

        if (!empty($this->max_participants) && $this->max_participants < 0) {
            $errors[] = "Maximum participants cannot be negative";
        }

        return $errors;
    }
}
