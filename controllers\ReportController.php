<?php
/**
 * Report Controller
 * Handles all report generation and display functionality
 */

require_once 'models/Member.php';
require_once 'models/Attendance.php';
require_once 'models/Finance.php';
require_once 'models/Visitor.php';
require_once 'models/Group.php';
require_once 'utils/validation.php';
require_once 'utils/export.php';

class ReportController {
    private $database;
    private $member;
    private $attendance;
    private $finance;
    private $visitor;
    private $group;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->member = new Member($this->database->getConnection());
        $this->attendance = new Attendance($this->database->getConnection());
        $this->finance = new Finance($this->database->getConnection());
        $this->visitor = new Visitor($this->database->getConnection());
        $this->group = new Group($this->database->getConnection());
    }

    /**
     * Display reports dashboard with statistics
     *
     * @return void
     */
    public function index() {
        // Set page title and active page
        $page_title = 'Reports Dashboard - ICGC Emmanuel Temple';
        $active_page = 'reports';

        // Get dashboard statistics
        $stats = $this->getDashboardStats();

        // Get chart data
        $attendance_chart_data = $this->getAttendanceChartData();
        $finance_chart_data = $this->getFinanceChartData();

        // Get departments for filter dropdown
        $departments = $this->getDepartments();

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/reports/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Get dashboard statistics
     *
     * @return array
     */
    private function getDashboardStats() {
        // Get total members
        $total_members = $this->member->getCount();

        // Get last Sunday attendance
        $last_sunday = date('Y-m-d', strtotime('last Sunday'));
        $last_sunday_attendance = $this->attendance->getCountByDate($last_sunday);

        // Get monthly income
        $first_day_of_month = date('Y-m-01');
        $last_day_of_month = date('Y-m-t');
        $monthly_income = $this->finance->getTotalIncomeByDateRange($first_day_of_month, $last_day_of_month);

        // Get new members in last 30 days
        $thirty_days_ago = date('Y-m-d', strtotime('-30 days'));
        $today = date('Y-m-d');
        $new_members = $this->member->getCountByDateRange($thirty_days_ago, $today);

        return [
            'total_members' => $total_members,
            'last_sunday_attendance' => $last_sunday_attendance,
            'monthly_income' => $monthly_income,
            'new_members' => $new_members
        ];
    }

    /**
     * Get attendance chart data
     *
     * @return array
     */
    private function getAttendanceChartData() {
        // Get attendance data for the past 12 months
        $months = [];
        $sunday_data = [];
        $tuesday_data = [];

        for ($i = 11; $i >= 0; $i--) {
            $month = date('M', strtotime("-$i months"));
            $year_month = date('Y-m', strtotime("-$i months"));

            $months[] = $month;

            // Get Sunday service attendance
            $sunday_attendance = $this->attendance->getAverageByMonthAndService($year_month, 1);
            $sunday_data[] = $sunday_attendance;

            // Get Tuesday Bible study attendance
            $tuesday_attendance = $this->attendance->getAverageByMonthAndService($year_month, 2);
            $tuesday_data[] = $tuesday_attendance;
        }

        return [
            'labels' => $months,
            'sunday_data' => $sunday_data,
            'tuesday_data' => $tuesday_data
        ];
    }

    /**
     * Get finance chart data
     *
     * @return array
     */
    private function getFinanceChartData() {
        // Get finance data for the past 12 months
        $months = [];
        $income_data = [];
        $expense_data = [];

        for ($i = 11; $i >= 0; $i--) {
            $month = date('M', strtotime("-$i months"));
            $year_month = date('Y-m', strtotime("-$i months"));
            $first_day = date('Y-m-01', strtotime("-$i months"));
            $last_day = date('Y-m-t', strtotime("-$i months"));

            $months[] = $month;

            // Get income for the month
            $income = $this->finance->getTotalIncomeByDateRange($first_day, $last_day);
            $income_data[] = $income;

            // Get expenses for the month
            $expense = $this->finance->getTotalExpensesByDateRange($first_day, $last_day);
            $expense_data[] = $expense;
        }

        return [
            'labels' => $months,
            'income_data' => $income_data,
            'expense_data' => $expense_data
        ];
    }

    /**
     * Generate report based on user selection
     *
     * @return void
     */
    public function generate() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Get report parameters
            $report_type = sanitize($_POST['report_type'] ?? '');
            $start_date = sanitize($_POST['start_date'] ?? '');
            $end_date = sanitize($_POST['end_date'] ?? '');
            $export_format = sanitize($_POST['export_format'] ?? '');

            // Get additional filters based on report type
            $department = sanitize($_POST['department'] ?? '');
            $service_id = sanitize($_POST['service_id'] ?? '');
            $category = sanitize($_POST['category'] ?? '');
            $group_id = sanitize($_POST['group_id'] ?? '');

            // Validate dates
            if (empty($start_date) || empty($end_date)) {
                set_flash_message('Start date and end date are required', 'danger');
                redirect('reports');
                exit;
            }

            // Generate report based on type
            $report_data = [];
            $report_title = '';
            $report_columns = [];

            switch ($report_type) {
                case 'member':
                    $report_title = 'Member Report';
                    $report_columns = ['ID', 'Name', 'Phone', 'Email', 'Department', 'Registration Date'];

                    // Get members by date range (registration date) with optional department filter
                    if (!empty($department)) {
                        $stmt = $this->member->getByDateRangeAndDepartment($start_date, $end_date, $department);
                        $report_title .= ' - ' . ucfirst($department) . ' Department';
                    } else {
                        $stmt = $this->member->getByDateRange($start_date, $end_date);
                    }
                    $report_data = $stmt->fetchAll();
                    break;

                case 'attendance':
                    $report_title = 'Attendance Report';
                    $report_columns = ['Date', 'Service', 'Total Present', 'Male', 'Female', 'Children'];

                    // Get attendance by date range with optional service filter
                    if (!empty($service_id)) {
                        $stmt = $this->attendance->getByDateRangeAndService($start_date, $end_date, $service_id);
                        $service_name = $this->attendance->getServiceNameById($service_id);
                        $report_title .= ' - ' . $service_name;
                    } else {
                        $stmt = $this->attendance->getByDateRange($start_date, $end_date);
                    }
                    $report_data = $stmt->fetchAll();
                    break;

                case 'finance':
                    $report_title = 'Finance Report';
                    $report_columns = ['Date', 'Category', 'Amount', 'Description', 'Type'];

                    // Get finances by date range with optional category filter
                    if (!empty($category)) {
                        $stmt = $this->finance->getByDateRangeAndCategory($start_date, $end_date, $category);
                        $report_title .= ' - ' . ucfirst($category);
                    } else {
                        $stmt = $this->finance->getByDateRange($start_date, $end_date);
                    }
                    $report_data = $stmt->fetchAll();

                    // Calculate totals
                    $total_income = 0;
                    $total_expenses = 0;
                    foreach ($report_data as $item) {
                        if ($item['type'] == 'income') {
                            $total_income += $item['amount'];
                        } else {
                            $total_expenses += $item['amount'];
                        }
                    }

                    // Add summary data
                    $report_summary = [
                        'total_income' => $total_income,
                        'total_expenses' => $total_expenses,
                        'net' => $total_income - $total_expenses
                    ];
                    break;

                case 'visitor':
                    $report_title = 'Visitor Report';
                    $report_columns = ['Name', 'Phone', 'Email', 'Visit Date', 'Follow-up Status'];

                    // Get visitors by date range
                    $stmt = $this->visitor->getByDateRange($start_date, $end_date);
                    $report_data = $stmt->fetchAll();
                    break;

                case 'group':
                    $report_title = 'Group Report';
                    $report_columns = ['Group Name', 'Leader', 'Members Count', 'Description'];

                    // Get groups with optional group_id filter
                    if (!empty($group_id)) {
                        $stmt = $this->group->getById($group_id);
                        $group_data = $stmt->fetch();
                        $report_title .= ' - ' . $group_data['name'];

                        // Get members of this group
                        $stmt = $this->group->getMembersByGroupId($group_id);
                        $report_data = $stmt->fetchAll();
                        $report_columns = ['Member ID', 'Name', 'Phone', 'Email', 'Role in Group'];
                    } else {
                        $stmt = $this->group->getAll();
                        $report_data = $stmt->fetchAll();
                    }
                    break;

                default:
                    set_flash_message('Invalid report type', 'danger');
                    redirect('reports');
                    exit;
            }

            // Handle export if requested
            if (!empty($export_format)) {
                $filename = strtolower(str_replace(' ', '_', $report_title)) . '_' . date('Y-m-d');

                switch ($export_format) {
                    case 'pdf':
                        export_to_pdf($report_data, $report_columns, $report_title, $filename);
                        exit;

                    case 'excel':
                        export_to_excel($report_data, $report_columns, $report_title, $filename);
                        exit;

                    case 'csv':
                        export_to_csv($report_data, $report_columns, $filename);
                        exit;
                }
            }

            // Set report data for view
            $report = [
                'type' => $report_type,
                'title' => $report_title,
                'start_date' => $start_date,
                'end_date' => $end_date,
                'columns' => $report_columns,
                'data' => $report_data,
                'summary' => $report_summary ?? null
            ];

            // Set page title and active page
            $page_title = $report_title . ' - ICGC Emmanuel Temple';
            $active_page = 'reports';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/reports/view.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';
        } else {
            // Not a POST request, redirect to reports page
            redirect('reports');
        }
    }

    /**
     * Display new members report
     *
     * @return void
     */
    public function newMembersReport() {
        // Get period from query string (default to month)
        $period = sanitize($_GET['period'] ?? 'month');

        // Set date range based on period
        $end_date = date('Y-m-d'); // Today

        switch ($period) {
            case 'week':
                $start_date = date('Y-m-d', strtotime('-1 week'));
                $period_text = 'This Week';
                break;
            case 'month':
                $start_date = date('Y-m-d', strtotime('-1 month'));
                $period_text = 'This Month';
                break;
            case 'quarter':
                $start_date = date('Y-m-d', strtotime('-3 months'));
                $period_text = 'This Quarter';
                break;
            case 'year':
                $start_date = date('Y-m-d', strtotime('-1 year'));
                $period_text = 'This Year';
                break;
            default:
                $start_date = date('Y-m-d', strtotime('-1 month'));
                $period_text = 'This Month';
        }

        // Get new members for the selected period
        $stmt = $this->member->getByDateRange($start_date, $end_date);
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Prepare report data
        $report = [
            'type' => 'member',
            'title' => 'New Members Report - ' . $period_text,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'columns' => ['ID', 'Name', 'Phone', 'Email', 'Department', 'Registration Date'],
            'data' => $members,
            'period' => $period,
            'period_text' => $period_text
        ];

        // Set page title and active page
        $page_title = 'New Members Report - ' . $period_text . ' - ICGC Emmanuel Temple';
        $active_page = 'reports';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/reports/members/new.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display members birthdays report
     *
     * @return void
     */
    public function birthdaysReport() {
        // Get period from query string (default to month)
        $period = sanitize($_GET['period'] ?? 'month');

        // Set period text and get appropriate birthdays
        switch ($period) {
            case 'today':
                $period_text = 'Today';
                $members = $this->member->getBirthdaysToday();
                break;
            case 'week':
                $period_text = 'This Week';
                $members = $this->member->getBirthdaysThisWeek();
                break;
            case 'month':
                $period_text = 'This Month';
                $members = $this->member->getBirthdaysThisMonth();
                break;
            case 'all':
                $period_text = 'All';
                $members = $this->member->getAllBirthdays();
                break;
            default:
                $period_text = 'This Month';
                $members = $this->member->getBirthdaysThisMonth();
        }

        // Prepare report data
        $report = [
            'type' => 'birthdays',
            'title' => 'Members Birthdays Report - ' . $period_text,
            'columns' => ['ID', 'Name', 'Phone', 'Email', 'Department', 'Birthday', 'Age'],
            'data' => $members,
            'period' => $period,
            'period_text' => $period_text
        ];

        // Set page title and active page
        $page_title = 'Members Birthdays Report - ' . $period_text . ' - ICGC Emmanuel Temple';
        $active_page = 'reports';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/reports/members/birthdays.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display members by department report
     *
     * @return void
     */
    public function departmentReport() {
        // Get all departments with member counts
        $departments = $this->member->getCountByDepartment();

        // Get members grouped by department
        $members_by_department = [];
        foreach ($departments as $dept) {
            $department_name = $dept['department'];
            $members_by_department[$department_name] = $this->member->getByDepartment($department_name);
        }

        // Prepare report data
        $report = [
            'type' => 'department',
            'title' => 'Members by Department Report',
            'departments' => $departments,
            'members_by_department' => $members_by_department
        ];

        // Set page title and active page
        $page_title = 'Members by Department Report - ICGC Emmanuel Temple';
        $active_page = 'reports';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/reports/members/department.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display members by status report
     *
     * @return void
     */
    public function statusReport() {
        // Get members by status
        $active_members = $this->member->getByStatus('active');
        $inactive_members = $this->member->getByStatus('inactive');
        $deceased_members = $this->member->getByStatus('deceased');
        $relocated_members = $this->member->getByStatus('relocated');

        // Get counts
        $active_count = count($active_members);
        $inactive_count = count($inactive_members);
        $deceased_count = count($deceased_members);
        $relocated_count = count($relocated_members);
        $total_count = $active_count + $inactive_count + $deceased_count + $relocated_count;

        // Prepare report data
        $report = [
            'type' => 'status',
            'title' => 'Members by Status Report',
            'active_members' => $active_members,
            'inactive_members' => $inactive_members,
            'deceased_members' => $deceased_members,
            'relocated_members' => $relocated_members,
            'counts' => [
                'active' => $active_count,
                'inactive' => $inactive_count,
                'deceased' => $deceased_count,
                'relocated' => $relocated_count,
                'total' => $total_count
            ]
        ];

        // Set page title and active page
        $page_title = 'Members by Status Report - ICGC Emmanuel Temple';
        $active_page = 'reports';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/reports/members/status.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Get departments for filter dropdown
     *
     * @return array
     */
    private function getDepartments() {
        try {
            $database = new Database();
            $conn = $database->getConnection();

            $stmt = $conn->prepare("SELECT name, display_name FROM departments WHERE is_active = 1 ORDER BY sort_order, display_name");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting departments: " . $e->getMessage());
            return [];
        }
    }
}
