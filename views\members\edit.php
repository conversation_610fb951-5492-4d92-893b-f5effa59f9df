<?php
/**
 * Edit Member Form
 */

// Set page title
$page_title = 'Edit Member - ICGC Emmanuel Temple';
$active_page = 'members';

// Ensure required variables are defined
if (!isset($member)) {
    throw new Exception('Member data not available');
}
if (!isset($database)) {
    throw new Exception('Database connection not available');
}
if (!isset($children_data)) {
    $children_data = [];
}

// Check if this is an "assign parent" action
$is_assign_parent = isset($_GET['assign_parent']) && $_GET['assign_parent'] == '1';

// If assigning parent, prepare child data and create parent member with guardian info
if ($is_assign_parent) {
    // Store the child member data
    $child_member = $member;

    // Ensure child_member is valid
    if (!$child_member || !$child_member->id) {
        throw new Exception('Invalid child member data for parent assignment');
    }

    // Create a new member object for the parent form
    if (!class_exists('Member')) {
        require_once 'models/Member.php';
    }
    $member = new Member($database->getConnection());

    // Initialize parent form with completely empty fields for user to fill
    $member->id = null;

    // ALWAYS clear all child data from parent form to prevent confusion
    $member->first_name = '';
    $member->last_name = '';
    $member->phone_number = '';
    $member->email = '';
    $member->date_of_birth = '';
    $member->gender = '';
    $member->marital_status = '';
    $member->occupation = '';
    $member->education_level = '';
    $member->member_status = 'active';

    // Check if emergency contact is different from child's name for optional pre-population
    $child_full_name = trim(($child_member->first_name ?? '') . ' ' . ($child_member->last_name ?? ''));
    $emergency_contact_name = trim($child_member->emergency_contact_name ?? '');

    // Only pre-populate if emergency contact is clearly different from child's name AND user wants it
    if (!empty($emergency_contact_name) && $emergency_contact_name !== $child_full_name && strlen($emergency_contact_name) > 3) {
        // Parse emergency contact name (usually "FirstName LastName") - but leave empty for now to avoid confusion
        // User can manually enter parent information
        // $emergency_name_parts = explode(' ', $emergency_contact_name, 2);
        // $member->first_name = $emergency_name_parts[0] ?? '';
        // $member->last_name = $emergency_name_parts[1] ?? '';
        // $member->phone_number = $child_member->emergency_contact_phone ?? '';
    }

    // Leave location empty for user to fill
    $member->location = '';

    // Set other fields as empty for user to fill
    $member->email = '';
    $member->date_of_birth = '';
    $member->gender = '';
    $member->marital_status = '';

    // For emergency contact, leave empty for user to fill (parent should not have child as emergency contact)
    $member->emergency_contact_name = '';
    $member->emergency_contact_phone = '';

    $member->baptism_status = '';
    $member->department = '';
    $member->role = 'member';
    $member->membership_date = date('Y-m-d');
    $member->occupation = '';
    $member->school = '';
    $member->member_status = 'active';
    $member->profile_picture = '';

    // Prepare children data with the child information
    $children_data = [[
        'id' => $child_member->id,
        'first_name' => $child_member->first_name,
        'last_name' => $child_member->last_name,
        'date_of_birth' => $child_member->date_of_birth,
        'gender' => $child_member->gender,
        'school' => $child_member->school ?? '',
        'department' => $child_member->department ?? 'children',
        'age' => $child_member->age ?? 0
    ]];

    $page_title = 'Assign Parent - ICGC Emmanuel Temple';
}

// Start output buffering
ob_start();
?>

<div class="container mx-auto px-4 fade-in">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 flex items-center">
            <span class="bg-primary-light bg-opacity-20 text-primary p-2 rounded-full mr-3">
                <i class="fas fa-<?php echo $is_assign_parent ? 'user-plus' : 'user-edit'; ?>"></i>
            </span>
            <?php if ($is_assign_parent): ?>
                Assign Parent to <?php echo htmlspecialchars($child_member->first_name . ' ' . $child_member->last_name); ?>
            <?php elseif (isset($_GET['child_focus'])): ?>
                Edit Family Information
            <?php else: ?>
                Edit Member
            <?php endif; ?>
        </h1>
        <div class="flex space-x-3">
            <?php if ($is_assign_parent): ?>
                <a href="<?php echo url('members/view?id=' . $child_member->id); ?>" class="flex items-center px-4 py-2 bg-primary bg-opacity-10 text-primary rounded-lg hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105 shadow-sm">
                    <i class="fas fa-eye mr-2"></i> View Child
                </a>
            <?php else: ?>
                <a href="<?php echo url('members/view?id=' . $member->id); ?>" class="flex items-center px-4 py-2 bg-primary bg-opacity-10 text-primary rounded-lg hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105 shadow-sm">
                    <i class="fas fa-eye mr-2"></i> View Member
                </a>
            <?php endif; ?>
            <a href="<?php echo url('members'); ?>" class="flex items-center px-4 py-2 bg-primary bg-opacity-10 text-primary rounded-lg hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105 shadow-sm">
                <i class="fas fa-arrow-left mr-2"></i> Back to Members
            </a>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-lg p-6 max-w-4xl mx-auto border-t-4 border-primary relative overflow-hidden">
        <!-- Decorative elements -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-primary bg-opacity-5 rounded-full -mt-20 -mr-20"></div>
        <div class="absolute bottom-0 left-0 w-40 h-40 bg-primary bg-opacity-5 rounded-full -mb-20 -ml-20"></div>
        
        <div class="mb-6 pb-3 border-b border-gray-200 relative z-10">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-light text-white mr-4 shadow-md">
                        <i class="fas fa-user-edit text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-700">Edit Member Information</h3>
                        <p class="text-sm text-gray-500 mt-1">Update the member details below</p>
                    </div>
                </div>

                <!-- Data Quality Indicator -->
                <div class="flex items-center space-x-4">
                    <div id="data-quality-indicator" class="flex items-center bg-gray-50 rounded-lg px-3 py-2">
                        <div class="flex items-center mr-3">
                            <div id="quality-icon" class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                            <span id="quality-text" class="text-sm font-medium text-gray-600">Data Quality</span>
                        </div>
                        <div id="completeness-score" class="text-sm font-bold text-gray-800">0%</div>
                    </div>
                    <div id="quality-suggestions" class="hidden">
                        <button type="button" onclick="showQualitySuggestions()" class="text-blue-600 hover:text-blue-800 text-sm">
                            <i class="fas fa-lightbulb mr-1"></i>Improve
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <form action="<?php echo $is_assign_parent ? url('members/create-parent-for-child') : url('members/' . $member->id); ?>" method="POST" enctype="multipart/form-data" class="space-y-8 relative z-10" onsubmit="return <?php echo $is_assign_parent ? 'handleCreateParent(event)' : 'showValidationSummary()'; ?>">
            <?php if ($is_assign_parent): ?>
                <!-- Hidden field to indicate this is a parent assignment -->
                <input type="hidden" name="assign_parent_for_child" value="<?php echo $child_member->id; ?>">
            <?php else: ?>
                <!-- Hidden ID field for regular member updates -->
                <input type="hidden" name="id" value="<?php echo $member->id; ?>">
            <?php endif; ?>
            <?php echo csrf_field(); ?>
            <?php if (!$is_assign_parent): ?>
                <input type="hidden" name="_method" value="PUT">
            <?php endif; ?>
            
            <!-- Personal Information -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <span class="bg-primary-light bg-opacity-20 text-primary p-2 rounded-lg mr-3">
                        <i class="fas fa-user-circle"></i>
                    </span>
                    <?php echo $is_assign_parent ? 'Parent Information' : 'Personal Information'; ?>
                </h2>
                <?php if ($is_assign_parent): ?>
                    <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span class="text-blue-700 text-sm">
                                <strong>Pre-filled Information:</strong> Name, phone number, and address have been pre-filled from the guardian information provided during child registration. Please complete the remaining required fields.
                            </span>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name <span class="text-red-500">*</span></label>
                        <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($member->first_name); ?>" required
                               oninput="validateName(this); suggestNameCorrections(this)"
                               onblur="formatName(this)"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        <div id="first_name_error" class="text-red-500 text-xs mt-1 hidden"></div>
                    </div>
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name <span class="text-red-500">*</span></label>
                        <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($member->last_name); ?>" required
                               oninput="validateName(this); suggestNameCorrections(this)"
                               onblur="formatName(this)"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        <div id="last_name_error" class="text-red-500 text-xs mt-1 hidden"></div>
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($member->email ?? ''); ?>"
                                   oninput="validateEmail(this)"
                                   onblur="suggestEmailCorrections(this)"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                        <div id="email_error" class="text-red-500 text-xs mt-1 hidden"></div>
                        <div id="email_suggestion" class="text-blue-600 text-xs mt-1 hidden"></div>
                    </div>
                    <div>
                        <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-1">Phone Number <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-phone text-gray-400"></i>
                            </div>
                            <input type="tel" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($member->phone_number ?? ''); ?>" required
                                   placeholder="e.g., 0244123456, +233244123456"
                                   oninput="formatPhoneNumber(this); validatePhoneLength(this); checkDuplicatePhone(this)"
                                   onblur="validatePhoneNumber(this)"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                        <div id="phone_number_error" class="text-red-500 text-xs mt-1 hidden"></div>
                        <div id="phone_number_warning" class="text-yellow-600 text-xs mt-1 hidden"></div>
                        <div id="phone_number_suggestion" class="text-blue-600 text-xs mt-1 hidden"></div>
                        <div class="text-xs text-gray-500 mt-1 flex items-center">
                            <i class="fas fa-globe text-primary mr-1"></i>
                            Enter in any format - we'll standardize it automatically for global compatibility
                        </div>
                    </div>
                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-alt text-gray-400"></i>
                            </div>
                            <input type="text" id="date_of_birth" name="date_of_birth"
                                   value="<?php
                                   // Convert YYYY-MM-DD to DD-MM-YYYY format for display
                                   if (!empty($member->date_of_birth)) {
                                       $date = DateTime::createFromFormat('Y-m-d', $member->date_of_birth);
                                       echo $date ? $date->format('d-m-Y') : '';
                                   }
                                   ?>"
                                   placeholder="DD-MM-YYYY (e.g., 15-03-1990)"
                                   oninput="formatDateInput(this)"
                                   onchange="validateDateOfBirth(this)"
                                   maxlength="10"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                        <div id="date_of_birth_error" class="text-red-500 text-xs mt-1 hidden"></div>
                        <div id="date_of_birth_warning" class="text-yellow-600 text-xs mt-1 hidden"></div>
                        <div id="date_of_birth_suggestion" class="text-blue-600 text-xs mt-1 hidden"></div>
                    </div>
                    <div>
                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">Gender <span class="text-red-500">*</span></label>
                        <select id="gender" name="gender" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="">Select Gender</option>
                            <option value="male" <?php echo $member->gender === 'male' ? 'selected' : ''; ?>>Male</option>
                            <option value="female" <?php echo $member->gender === 'female' ? 'selected' : ''; ?>>Female</option>
                        </select>
                    </div>
                    <div>
                        <label for="marital_status" class="block text-sm font-medium text-gray-700 mb-1">Marital Status</label>
                        <select id="marital_status" name="marital_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="">Select Marital Status</option>
                            <option value="single" <?php echo $member->marital_status === 'single' ? 'selected' : ''; ?>>Single</option>
                            <option value="married" <?php echo $member->marital_status === 'married' ? 'selected' : ''; ?>>Married</option>
                            <option value="divorced" <?php echo $member->marital_status === 'divorced' ? 'selected' : ''; ?>>Divorced</option>
                            <option value="widowed" <?php echo $member->marital_status === 'widowed' ? 'selected' : ''; ?>>Widowed</option>
                        </select>
                    </div>
                    <div>
                        <label for="occupation" class="block text-sm font-medium text-gray-700 mb-1">Occupation</label>
                        <input type="text" id="occupation" name="occupation" value="<?php echo $member->occupation; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                    </div>
                    <div>
                        <label for="school" class="block text-sm font-medium text-gray-700 mb-1">School/Institution</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-graduation-cap text-gray-400"></i>
                            </div>
                            <input type="text" id="school" name="school" value="<?php echo htmlspecialchars($member->school ?? ''); ?>" placeholder="Enter school or institution name" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>

                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location/Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-map-marker-alt text-gray-400"></i>
                            </div>
                            <input type="text" id="location" name="location" value="<?php echo $member->location; ?>" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>
                    <div class="md:col-span-3 bg-gray-50 p-5 rounded-lg border border-gray-200">
                        <label for="profile_picture" class="block text-sm font-medium text-gray-700 mb-3 flex items-center">
                            <i class="fas fa-camera text-primary mr-2"></i> Profile Picture
                        </label>
                        <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
                            <div class="w-32 h-32 border-2 border-dashed border-primary-light rounded-full flex items-center justify-center bg-white shadow-sm overflow-hidden">
                                <?php if (!empty($member->profile_picture) && file_exists($member->profile_picture)): ?>
                                    <img id="profile_preview" src="<?php echo url($member->profile_picture); ?>" alt="Profile Preview" class="max-w-full max-h-full">
                                <?php else: ?>
                                    <img id="profile_preview" src="<?php echo url('assets/images/default-avatar.png'); ?>" alt="Profile Preview" class="max-w-full max-h-full hidden">
                                    <span id="profile_placeholder" class="text-gray-400"><i class="fas fa-user text-5xl"></i></span>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1">
                                <div class="relative bg-white rounded-md shadow-sm border border-gray-300 px-3 py-2">
                                    <input type="file" id="profile_picture" name="profile_picture" accept="image/*" class="w-full focus:outline-none focus:ring-primary focus:border-primary">
                                </div>
                                <p class="mt-2 text-sm text-gray-500 flex items-center">
                                    <i class="fas fa-info-circle mr-1"></i> Accepted formats: JPG, PNG or GIF (Max. 2MB)
                                </p>
                                <?php if (!empty($member->profile_picture)): ?>
                                    <p class="mt-1 text-xs text-gray-500">Leave empty to keep current image</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emergency Contact Information -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <span class="bg-red-100 text-red-500 p-2 rounded-lg mr-3">
                        <i class="fas fa-phone-alt"></i>
                    </span>
                    Emergency Contact Information
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div>
                        <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Name</label>
                        <input type="text" id="emergency_contact_name" name="emergency_contact_name" value="<?php echo $member->emergency_contact_name; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                    </div>
                    <div>
                        <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Phone</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-phone-alt text-gray-400"></i>
                            </div>
                            <input type="tel" id="emergency_contact_phone" name="emergency_contact_phone" value="<?php echo $member->emergency_contact_phone; ?>" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Church Information -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <span class="bg-secondary-light bg-opacity-20 text-secondary p-2 rounded-lg mr-3">
                        <i class="fas fa-church"></i>
                    </span>
                    Church Information
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
                    <div>
                        <label for="baptism_status" class="block text-sm font-medium text-gray-700 mb-1">Baptism Status</label>
                        <select id="baptism_status" name="baptism_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="not_baptized" <?php echo $member->baptism_status === 'not_baptized' ? 'selected' : ''; ?>>Not Baptized</option>
                            <option value="baptized" <?php echo $member->baptism_status === 'baptized' ? 'selected' : ''; ?>>Baptized</option>
                            <option value="scheduled" <?php echo $member->baptism_status === 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                        </select>
                    </div>
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                        <div class="flex gap-2">
                            <select id="department" name="department" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white" data-current-value="<?php echo htmlspecialchars($member->department); ?>">
                                <option value="">Loading departments...</option>
                            </select>
                            <button type="button" onclick="openDepartmentManager()" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg border border-gray-300 transition-colors duration-200 flex items-center" title="Manage Departments">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <div class="flex gap-2">
                            <select id="role" name="role" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white" data-current-value="<?php echo htmlspecialchars($member->role); ?>">
                                <option value="">Loading roles...</option>
                            </select>
                            <button type="button" onclick="openRoleManager()" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg border border-gray-300 transition-colors duration-200 flex items-center" title="Manage Roles">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label for="membership_date" class="block text-sm font-medium text-gray-700 mb-1">Membership Date</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-check text-gray-400"></i>
                            </div>
                            <input type="date" id="membership_date" name="membership_date" value="<?php echo $member->membership_date; ?>" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>
                    <div>
                        <label for="member_status" class="block text-sm font-medium text-gray-700 mb-1">Member Status <span class="text-red-500">*</span></label>
                        <select id="member_status" name="member_status" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="active" <?php echo $member->member_status === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $member->member_status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="transferred" <?php echo $member->member_status === 'transferred' ? 'selected' : ''; ?>>Transferred</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Simplified editing - only children registration needed -->



            <!-- Children Registration Section -->
            <div id="children-registration-section">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <span class="bg-green-100 text-green-600 p-2 rounded-lg mr-3">
                        <i class="fas fa-child"></i>
                    </span>
                    Register Children
                    <span class="ml-2 text-sm font-normal text-gray-500">(Optional - Add children under 18)</span>
                </h2>

                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <?php if (isset($_GET['child_focus'])): ?>
                    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle mr-2"></i>
                            <span>You were redirected here to edit family information. The highlighted child below is the one you originally wanted to edit.</span>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-medium text-gray-800 flex items-center">
                            <i class="fas fa-users text-green-600 mr-2"></i>
                            Children Information
                        </h4>
                        <button type="button" onclick="addChild()" class="bg-gradient-to-r from-green-500 to-green-600 text-white px-3 py-2 rounded-lg text-sm font-medium hover:shadow-md transition-all duration-200">
                            <i class="fas fa-plus mr-1"></i>Add Child
                        </button>
                    </div>

                    <div id="children-container" class="space-y-4">
                        <!-- Display existing children as editable forms -->
                        <?php


                        // Load children data directly in the view if not available (only for normal edit mode)
                        if (!$is_assign_parent && (!isset($children_data) || empty($children_data))) {
                            $children_data = [];

                            // Load children data as fallback
                            if (isset($member) && $member->id) {
                                if (!class_exists('FamilyRelationship')) {
                                    require_once 'models/FamilyRelationship.php';
                                }
                                $familyRelationship = new FamilyRelationship($database->getConnection());

                                // Get relationships where this member is the parent
                                $parent_relationships = $familyRelationship->getChildrenByParent($member->id)->fetchAll(PDO::FETCH_ASSOC);

                                // Load complete children data
                                if (!empty($parent_relationships) && is_array($parent_relationships)) {
                                    foreach ($parent_relationships as $relationship) {
                                        if (!isset($relationship['child_id'])) continue;
                                        $child_member = new Member($database->getConnection());
                                        if ($child_member->getById($relationship['child_id'])) {
                                            $children_data[] = [
                                                'id' => $child_member->id,
                                                'first_name' => $child_member->first_name,
                                                'last_name' => $child_member->last_name,
                                                'date_of_birth' => $child_member->date_of_birth,
                                                'gender' => $child_member->gender,
                                                'school' => $child_member->school ?? '',
                                                'department' => $child_member->department ?? 'children',
                                                'age' => $child_member->age ?? 0
                                            ];
                                        }
                                    }
                                }
                            }
                        }

                        $child_count = 0;
                        $focus_child_id = isset($_GET['child_focus']) ? $_GET['child_focus'] : null;



                        if (!empty($children_data) && is_array($children_data)) {
                            foreach ($children_data as $child) {
                                if (!is_array($child) || !isset($child['id'])) continue;
                                $child_count++;
                                $is_focused = ($focus_child_id && $child['id'] == $focus_child_id);
                                $bg_class = $is_focused ? 'bg-yellow-100 border-yellow-400' : 'bg-green-50 border-green-200';
                                echo '<div class="child-entry ' . $bg_class . ' p-4 rounded-lg border" data-index="' . $child_count . '" data-existing-id="' . $child['id'] . '">';
                                echo '<div class="flex justify-between items-center mb-3">';
                                echo '<h4 class="font-medium text-green-800">Existing Child ' . $child_count . '</h4>';
                                echo '<div class="flex gap-2">';
                                echo '<a href="' . url('members/view?id=' . $child['id']) . '" class="text-blue-600 hover:text-blue-800 text-sm font-medium">';
                                echo '<i class="fas fa-eye mr-1"></i>View Profile';
                                echo '</a>';
                                echo '<button type="button" onclick="deleteChildEntry(' . $child_count . ')" class="text-red-500 hover:text-red-700 transition-colors">';
                                echo '<i class="fas fa-trash"></i>';
                                echo '</button>';
                                echo '</div>';
                                echo '</div>';

                                echo '<div class="grid grid-cols-1 md:grid-cols-3 gap-4">';
                                echo '<div>';
                                echo '<label class="block text-sm font-medium text-gray-700 mb-1">First Name <span class="text-red-500">*</span></label>';
                                echo '<input type="text" name="existing_children[' . $child['id'] . '][first_name]" value="' . htmlspecialchars($child['first_name']) . '" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">';
                                echo '</div>';
                                echo '<div>';
                                echo '<label class="block text-sm font-medium text-gray-700 mb-1">Last Name <span class="text-red-500">*</span></label>';
                                echo '<input type="text" name="existing_children[' . $child['id'] . '][last_name]" value="' . htmlspecialchars($child['last_name']) . '" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">';
                                echo '</div>';
                                echo '<div>';
                                echo '<label class="block text-sm font-medium text-gray-700 mb-1">Date of Birth <span class="text-red-500">*</span></label>';
                                echo '<input type="date" name="existing_children[' . $child['id'] . '][date_of_birth]" value="' . htmlspecialchars($child['date_of_birth']) . '" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">';
                                echo '</div>';
                                echo '<div>';
                                echo '<label class="block text-sm font-medium text-gray-700 mb-1">Gender <span class="text-red-500">*</span></label>';
                                echo '<select name="existing_children[' . $child['id'] . '][gender]" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors bg-white">';
                                echo '<option value="">Select Gender</option>';
                                echo '<option value="male"' . ($child['gender'] === 'male' ? ' selected' : '') . '>Male</option>';
                                echo '<option value="female"' . ($child['gender'] === 'female' ? ' selected' : '') . '>Female</option>';
                                echo '</select>';
                                echo '</div>';
                                echo '<div>';
                                echo '<label class="block text-sm font-medium text-gray-700 mb-1">School</label>';
                                echo '<input type="text" name="existing_children[' . $child['id'] . '][school]" value="' . htmlspecialchars($child['school']) . '" placeholder="Enter school name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">';
                                echo '</div>';
                                echo '<div>';
                                echo '<label class="block text-sm font-medium text-gray-700 mb-1">Department</label>';
                                echo '<select name="existing_children[' . $child['id'] . '][department]" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors bg-white">';
                                echo '<option value="children"' . ($child['department'] === 'children' ? ' selected' : '') . '>Children</option>';
                                echo '<option value="new_breed"' . ($child['department'] === 'new_breed' ? ' selected' : '') . '>New Breed</option>';
                                echo '<option value="none"' . ($child['department'] === 'none' ? ' selected' : '') . '>None</option>';
                                echo '</select>';
                                echo '</div>';
                                echo '</div>';

                                echo '<input type="hidden" name="existing_children[' . $child['id'] . '][id]" value="' . $child['id'] . '">';
                                echo '</div>';
                            }
                        }
                        ?>
                    </div>

                    <div id="no-children-msg" class="text-center text-gray-500 py-4" <?php echo ($child_count > 0) ? 'style="display: none;"' : ''; ?>>
                        <i class="fas fa-child text-2xl mb-2"></i>
                        <p class="text-sm">No children found. Click "Add Child" to register children under 18.</p>
                    </div>
                </div>
            </div>

            <!-- Manual Parent Assignment Section (for children) - TEMPORARILY DISABLED -->
            <?php
            // Show parent assignment section only for children (under 18)
            $member_age = 0;
            if (!empty($member->date_of_birth)) {
                try {
                    $birth_date = new DateTime($member->date_of_birth);
                    $today = new DateTime();
                    $member_age = $birth_date->diff($today)->y;
                } catch (Exception $e) {
                    // If date parsing fails, assume adult age
                    $member_age = 25;
                }
            }

            if (false && $member_age < 18): // DISABLED FOR DEBUGGING ?>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="fas fa-users mr-2"></i>Parent Assignment
                </h3>
                <p class="text-sm text-blue-600 mb-4">
                    Assign a parent member to this child. Only adult members (18+) are shown.
                </p>

                <?php
                try {
                    // Get current parent if exists
                    $current_parent = null;
                    $parent_relationships = $familyRelationship->getParentsByChild($member->id)->fetchAll(PDO::FETCH_ASSOC);
                    if (!empty($parent_relationships)) {
                        $current_parent = $parent_relationships[0];
                    }

                    // Get all adult members for parent selection
                    $adult_query = "SELECT id, first_name, last_name, phone_number
                                   FROM members
                                   WHERE TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) >= 18
                                   AND member_status = 'active'
                                   AND id != ?
                                   ORDER BY first_name, last_name";
                    $adult_stmt = $database->getConnection()->prepare($adult_query);
                    $adult_stmt->execute([$member->id]);
                    $adult_members = $adult_stmt->fetchAll(PDO::FETCH_ASSOC);
                } catch (Exception $e) {
                    error_log("Parent assignment error for member {$member->id}: " . $e->getMessage());
                    echo '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">';
                    echo '<p><strong>Error loading parent assignment:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '</div>';
                    $current_parent = null;
                    $adult_members = [];
                }
                ?>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Select Parent</label>
                        <select name="parent_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">No Parent Assigned</option>
                            <?php foreach ($adult_members as $adult): ?>
                                <option value="<?php echo $adult['id']; ?>"
                                        <?php echo ($current_parent && $current_parent['parent_id'] == $adult['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($adult['first_name'] . ' ' . $adult['last_name']); ?>
                                    <?php if (!empty($adult['phone_number'])): ?>
                                        (<?php echo htmlspecialchars($adult['phone_number']); ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <?php if ($current_parent): ?>
                    <div class="bg-green-100 p-3 rounded-lg">
                        <p class="text-sm text-green-800">
                            <strong>Current Parent:</strong><br>
                            <?php echo htmlspecialchars($current_parent['parent_first_name'] . ' ' . $current_parent['parent_last_name']); ?>
                        </p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <div class="flex justify-end space-x-4 pt-6 mt-6 border-t border-gray-200">
                <?php if ($is_assign_parent): ?>
                    <a href="<?php echo url('members/view?id=' . $child_member->id); ?>" class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200 flex items-center">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                    <button type="submit" class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200 flex items-center">
                        <i class="fas fa-user-plus mr-2"></i>
                        Create Parent
                    </button>
                <?php else: ?>
                    <a href="<?php echo url('members/view?id=' . $member->id); ?>" class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200 flex items-center">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                    <button type="submit" class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200 flex items-center">
                        <i class="fas fa-save mr-2"></i>
                        Update Member
                    </button>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript for profile picture preview -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const profileInput = document.getElementById('profile_picture');
        const profilePreview = document.getElementById('profile_preview');
        const profilePlaceholder = document.getElementById('profile_placeholder');

        profileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    profilePreview.src = e.target.result;
                    profilePreview.classList.remove('hidden');
                    if (profilePlaceholder) {
                        profilePlaceholder.classList.add('hidden');
                    }
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    });

    // Children Registration Functions
    let childCount = <?php echo isset($child_count) ? $child_count : (isset($children_data) ? count($children_data) : 0); ?>;

    // Simplified editing - only children management needed

    function addChild() {
        childCount++;
        const childHtml = createChildFormHTML(childCount);
        addFormEntry('children-container', 'no-children-msg', childHtml);
    }

    function deleteChildEntry(index) {
        const childElement = document.querySelector(`.child-entry[data-index="${index}"]`);

        if (childElement) {
            const existingId = childElement.getAttribute('data-existing-id');

            if (existingId) {
                // For existing children, add a hidden field to mark for deletion
                const confirmMessage = 'Are you sure you want to remove this child?\n\nThis will permanently delete their record and all associated data including:\n• Personal information\n• Attendance records\n• Parent-child connections\n\nThis action cannot be undone.';
                if (confirm(confirmMessage)) {
                    try {
                        // Add hidden field to mark for deletion
                        const deleteInput = document.createElement('input');
                        deleteInput.type = 'hidden';
                        deleteInput.name = `delete_children[${existingId}]`;
                        deleteInput.value = '1';

                        const form = document.querySelector('form');
                        if (form) {
                            form.appendChild(deleteInput);
                        } else {
                            alert('Error: Could not find form to submit deletion.');
                            return;
                        }

                        // Remove the element from view with animation
                        childElement.style.transition = 'all 0.3s ease-out';
                        childElement.style.opacity = '0';
                        childElement.style.transform = 'translateX(-100%)';

                        setTimeout(() => {
                            childElement.remove();
                        }, 300);

                        // Show success message
                        const successMsg = document.createElement('div');
                        successMsg.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 animate-pulse';
                        successMsg.innerHTML = '<div class="flex items-center"><i class="fas fa-exclamation-triangle mr-2"></i><strong>Child marked for deletion.</strong> Save the form to permanently delete this child.</div>';

                        const container = document.getElementById('children-container');
                        if (container) {
                            container.insertBefore(successMsg, container.firstChild);

                            // Remove the message after 8 seconds
                            setTimeout(() => {
                                if (successMsg.parentNode) {
                                    successMsg.style.transition = 'all 0.3s ease-out';
                                    successMsg.style.opacity = '0';
                                    setTimeout(() => successMsg.remove(), 300);
                                }
                            }, 8000);
                        }

                    } catch (error) {
                        alert('An error occurred while marking the child for deletion. Please try again.');
                    }
                }
            } else {
                // For new children, just remove the element
                if (confirm('Remove this child from the form?')) {
                    // Animate removal
                    childElement.style.transition = 'all 0.3s ease-out';
                    childElement.style.opacity = '0';
                    childElement.style.transform = 'translateX(-100%)';

                    setTimeout(() => {
                        childElement.remove();
                    }, 300);

                    // Show message for new children
                    const infoMsg = document.createElement('div');
                    infoMsg.className = 'bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4';
                    infoMsg.innerHTML = '<div class="flex items-center"><i class="fas fa-info-circle mr-2"></i><strong>Child removed.</strong> This was a new child that had not been saved yet.</div>';

                    const container = document.getElementById('children-container');
                    if (container) {
                        container.insertBefore(infoMsg, container.firstChild);

                        // Remove the message after 4 seconds
                        setTimeout(() => {
                            if (infoMsg.parentNode) {
                                infoMsg.style.transition = 'all 0.3s ease-out';
                                infoMsg.style.opacity = '0';
                                setTimeout(() => infoMsg.remove(), 300);
                            }
                        }, 4000);
                    }
                }
            }

            // Check if container is empty
            const container = document.getElementById('children-container');
            const noChildrenMsg = document.getElementById('no-children-msg');

            if (container.children.length === 0) {
                noChildrenMsg.classList.remove('hidden');
                noChildrenMsg.style.display = 'block';
            }
        }
    }

    function createChildFormHTML(count) {
        return `
            <div class="child-entry bg-green-50 p-4 rounded-lg border border-green-200" data-index="${count}">
                <div class="flex justify-between items-center mb-3">
                    <h4 class="font-medium text-green-800">Child ${count}</h4>
                    <button type="button" onclick="deleteChildEntry(${count})" class="text-red-500 hover:text-red-700 transition-colors">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">First Name <span class="text-red-500">*</span></label>
                        <input type="text" name="children[${count}][first_name]" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Last Name <span class="text-red-500">*</span></label>
                        <input type="text" name="children[${count}][last_name]" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date of Birth <span class="text-red-500">*</span></label>
                        <input type="date" name="children[${count}][date_of_birth]" required onchange="validateChildAge(${count})" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Gender <span class="text-red-500">*</span></label>
                        <select name="children[${count}][gender]" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors bg-white">
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">School</label>
                        <input type="text" name="children[${count}][school]" placeholder="Enter school name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                        <select name="children[${count}][department]" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors bg-white">
                            <option value="children">Children</option>
                            <option value="new_breed">New Breed</option>
                            <option value="none">None</option>
                        </select>
                    </div>
                </div>

                <!-- Age Warning Container -->
                <div id="age-warning-${count}" class="mt-3" style="display: none;">
                    <div class="bg-orange-100 border border-orange-400 text-orange-700 px-4 py-3 rounded">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span><strong>Note:</strong> This person is 18 or older. Consider registering them as a separate adult member instead.</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function validateChildAge(childIndex) {
        const dobInput = document.querySelector(`input[name="children[${childIndex}][date_of_birth]"]`);
        const warningDiv = document.getElementById(`age-warning-${childIndex}`);

        if (!dobInput.value) {
            warningDiv.style.display = 'none';
            return;
        }

        const age = calculateAge(dobInput.value);

        if (age >= 18) {
            warningDiv.style.display = 'block';
        } else {
            warningDiv.style.display = 'none';
        }
    }

    function calculateAge(birthDate) {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }

        return age;
    }

    // Simplified form management - only children forms needed

    // Utility Functions for Form Management
    function addFormEntry(containerId, noItemsMsgId, html) {
        const container = document.getElementById(containerId);
        const noItemsMsg = document.getElementById(noItemsMsgId);

        container.insertAdjacentHTML('beforeend', html);
        noItemsMsg.classList.add('hidden');
    }

    function removeFormEntry(selector, containerId, noItemsMsgId) {
        const element = document.querySelector(selector);
        if (element) {
            element.remove();

            const container = document.getElementById(containerId);
            const noItemsMsg = document.getElementById(noItemsMsgId);

            if (container.children.length === 0) {
                noItemsMsg.classList.remove('hidden');
            }
        }
    }

    // Define BASE_URL for JavaScript
    window.BASE_URL = '<?php echo url(""); ?>';

    // Load departments and roles when page loads
    loadDepartments();
    loadRoles();

    // Load departments from API
    async function loadDepartments() {
        try {
            const response = await fetch('<?php echo url("api/departments.php"); ?>');
            const data = await response.json();

            const departmentSelect = document.getElementById('department');
            const currentValue = departmentSelect.getAttribute('data-current-value');
            departmentSelect.innerHTML = '<option value="">Select Department</option>';

            if (data.success && data.data) {
                data.data.forEach(dept => {
                    const option = document.createElement('option');
                    option.value = dept.name;
                    option.textContent = dept.display_name;
                    if (dept.name === currentValue) {
                        option.selected = true;
                    }
                    departmentSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading departments:', error);
        }
    }

    // Load roles from API
    async function loadRoles() {
        try {
            const response = await fetch('<?php echo url("api/roles.php"); ?>');
            const data = await response.json();

            const roleSelect = document.getElementById('role');
            const currentValue = roleSelect.getAttribute('data-current-value');
            roleSelect.innerHTML = '<option value="">Select Role</option>';

            if (data.success && data.data) {
                data.data.forEach(role => {
                    const option = document.createElement('option');
                    option.value = role.name;
                    option.textContent = role.display_name;
                    if (role.name === currentValue) {
                        option.selected = true;
                    }
                    roleSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading roles:', error);
        }
    }

    // Open department manager modal
    function openDepartmentManager() {
        document.getElementById('departmentModal').classList.remove('hidden');
        loadDepartmentList();
    }

    // Open role manager modal
    function openRoleManager() {
        document.getElementById('roleModal').classList.remove('hidden');
        loadRoleList();
    }

    // Close modals
    function closeDepartmentModal() {
        document.getElementById('departmentModal').classList.add('hidden');
    }

    function closeRoleModal() {
        document.getElementById('roleModal').classList.add('hidden');
    }

    // Load department list for management
    async function loadDepartmentList() {
        try {
            const response = await fetch('<?php echo url("api/departments.php"); ?>');
            const data = await response.json();

            const tbody = document.getElementById('departmentTableBody');
            tbody.innerHTML = '';

            if (data.success && data.data) {
                data.data.forEach(dept => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-4 py-2 border-b">${dept.display_name}</td>
                        <td class="px-4 py-2 border-b text-sm text-gray-600">${dept.description || 'No description'}</td>
                        <td class="px-4 py-2 border-b">
                            <button onclick="editDepartment(${dept.id}, '${dept.display_name}', '${dept.description || ''}')" class="text-blue-600 hover:text-blue-800 mr-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${dept.name !== 'none' ? `<button onclick="deleteDepartment(${dept.id})" class="text-red-600 hover:text-red-800"><i class="fas fa-trash"></i></button>` : ''}
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        } catch (error) {
            console.error('Error loading department list:', error);
        }
    }

    // Load role list for management
    async function loadRoleList() {
        try {
            const response = await fetch('<?php echo url("api/roles.php"); ?>');
            const data = await response.json();

            const tbody = document.getElementById('roleTableBody');
            tbody.innerHTML = '';

            if (data.success && data.data) {
                data.data.forEach(role => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-4 py-2 border-b">${role.display_name}</td>
                        <td class="px-4 py-2 border-b text-sm text-gray-600">${role.description || 'No description'}</td>
                        <td class="px-4 py-2 border-b">
                            <button onclick="editRole(${role.id}, '${role.display_name}', '${role.description || ''}')" class="text-blue-600 hover:text-blue-800 mr-2">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${role.name !== 'member' ? `<button onclick="deleteRole(${role.id})" class="text-red-600 hover:text-red-800"><i class="fas fa-trash"></i></button>` : ''}
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        } catch (error) {
            console.error('Error loading role list:', error);
        }
    }
</script>

<!-- Include department and role management functionality -->
<script src="<?php echo url('assets/js/department-role-management.js'); ?>"></script>

<!-- Department Management Modal -->
<div id="departmentModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Manage Departments</h3>
                <button onclick="closeDepartmentModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Add New Department Form -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-md font-medium mb-3">Add New Department</h4>
                <form id="addDepartmentForm" onsubmit="addDepartment(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                            <input type="text" id="newDeptDisplayName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <input type="text" id="newDeptDescription" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark">
                            <i class="fas fa-plus mr-2"></i>Add Department
                        </button>
                    </div>
                </form>
            </div>

            <!-- Departments List -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="px-4 py-2 text-left">Name</th>
                            <th class="px-4 py-2 text-left">Description</th>
                            <th class="px-4 py-2 text-left">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="departmentTableBody">
                        <!-- Departments will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Role Management Modal -->
<div id="roleModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Manage Roles</h3>
                <button onclick="closeRoleModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Add New Role Form -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-md font-medium mb-3">Add New Role</h4>
                <form id="addRoleForm" onsubmit="addRole(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                            <input type="text" id="newRoleDisplayName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <input type="text" id="newRoleDescription" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark">
                            <i class="fas fa-plus mr-2"></i>Add Role
                        </button>
                    </div>
                </form>
            </div>

            <!-- Roles List -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="px-4 py-2 text-left">Name</th>
                            <th class="px-4 py-2 text-left">Description</th>
                            <th class="px-4 py-2 text-left">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="roleTableBody">
                        <!-- Roles will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Define BASE_URL for JavaScript
const BASE_URL = '<?php echo url(""); ?>';

// Name validation functions
function validateName(input) {
    const value = input.value;
    const errorDiv = document.getElementById(input.id + '_error');
    let isValid = true;
    let errorMessage = '';

    // Clear previous error
    if (errorDiv) {
        errorDiv.classList.add('hidden');
        input.classList.remove('border-red-500');
        input.classList.add('border-gray-300');
    }

    if (!value.trim()) {
        return true; // Empty is okay for validation
    }

    // Check for numbers
    if (/\d/.test(value)) {
        isValid = false;
        errorMessage = 'Names should not contain numbers';
    }
    // Check for special characters (except spaces, hyphens, apostrophes)
    else if (!/^[a-zA-Z\s\-']+$/.test(value)) {
        isValid = false;
        errorMessage = 'Names should only contain letters, spaces, hyphens, and apostrophes';
    }
    // Check length
    else if (value.length < 2) {
        isValid = false;
        errorMessage = 'Name must be at least 2 characters long';
    }
    else if (value.length > 50) {
        isValid = false;
        errorMessage = 'Name must be less than 50 characters';
    }

    if (!isValid && errorDiv) {
        errorDiv.textContent = errorMessage;
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        input.classList.remove('border-gray-300');
    }

    return isValid;
}

function formatName(input) {
    let value = input.value;

    // Remove extra spaces and format properly
    value = value.replace(/\s+/g, ' ').trim();

    // Capitalize first letter of each word
    value = value.split(' ').map(word => {
        if (word.length > 0) {
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
        return word;
    }).join(' ');

    input.value = value;

    // Revalidate after formatting
    validateName(input);
}

function suggestNameCorrections(input) {
    // This function can be enhanced to provide name suggestions
    // For now, it just validates
    validateName(input);
}

// Email validation functions
let emailSuggestion = '';

function validateEmail(input) {
    const value = input.value.trim();
    const errorDiv = document.getElementById('email_error');
    let isValid = true;
    let errorMessage = '';

    // Clear previous error
    if (errorDiv) {
        errorDiv.classList.add('hidden');
        input.classList.remove('border-red-500');
        input.classList.add('border-gray-300');
    }

    if (!value) {
        return true; // Empty is okay for optional field
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address';
    }

    if (!isValid && errorDiv) {
        errorDiv.textContent = errorMessage;
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        input.classList.remove('border-gray-300');
    }

    return isValid;
}

function suggestEmailCorrections(input) {
    // Basic email domain suggestions
    const value = input.value.trim().toLowerCase();
    const suggestionDiv = document.getElementById('email_suggestion');

    if (!suggestionDiv) return;

    const commonDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
    const emailParts = value.split('@');

    if (emailParts.length === 2) {
        const domain = emailParts[1];
        const suggestion = commonDomains.find(d => d.startsWith(domain.charAt(0)) && d !== domain);

        if (suggestion && domain.length > 2) {
            suggestionDiv.innerHTML = `Did you mean <span class="font-semibold cursor-pointer text-blue-700" onclick="acceptEmailSuggestion('${emailParts[0]}@${suggestion}')">${emailParts[0]}@${suggestion}</span>?`;
            suggestionDiv.classList.remove('hidden');
            emailSuggestion = `${emailParts[0]}@${suggestion}`;
        } else {
            suggestionDiv.classList.add('hidden');
        }
    }
}

function acceptEmailSuggestion(suggestion) {
    const emailInput = document.getElementById('email');
    const suggestionDiv = document.getElementById('email_suggestion');

    if (emailInput && suggestionDiv) {
        emailInput.value = suggestion;
        suggestionDiv.classList.add('hidden');
        validateEmail(emailInput);
    }
}

// Date of Birth validation functions
function validateDateOfBirth(input) {
    const value = input.value;
    const errorDiv = document.getElementById('date_of_birth_error');
    const warningDiv = document.getElementById('date_of_birth_warning');
    const suggestionDiv = document.getElementById('date_of_birth_suggestion');

    // Clear all messages
    if (errorDiv) errorDiv.classList.add('hidden');
    if (warningDiv) warningDiv.classList.add('hidden');
    if (suggestionDiv) suggestionDiv.classList.add('hidden');
    input.classList.remove('border-red-500', 'border-yellow-500');
    input.classList.add('border-gray-300');

    if (!value.trim()) {
        return true; // Empty is okay
    }

    // Check format DD-MM-YYYY
    const dateRegex = /^(\d{1,2})-(\d{1,2})-(\d{4})$/;
    const match = value.match(dateRegex);

    if (!match) {
        if (errorDiv) {
            errorDiv.textContent = 'Please use DD-MM-YYYY format (e.g., 15-03-1990)';
            errorDiv.classList.remove('hidden');
        }
        input.classList.add('border-red-500');
        return false;
    }

    const day = parseInt(match[1]);
    const month = parseInt(match[2]);
    const year = parseInt(match[3]);

    // Basic range checks
    if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > new Date().getFullYear()) {
        if (errorDiv) {
            errorDiv.textContent = 'Please enter a valid date';
            errorDiv.classList.remove('hidden');
        }
        input.classList.add('border-red-500');
        return false;
    }

    // Create date object (month is 0-indexed in JavaScript)
    const birthDate = new Date(year, month - 1, day);
    const today = new Date();

    // Check if the date is valid (handles invalid dates like 31-02-2020)
    if (birthDate.getDate() !== day || birthDate.getMonth() !== (month - 1) || birthDate.getFullYear() !== year) {
        if (errorDiv) {
            errorDiv.textContent = 'Please enter a valid date (e.g., 29-02 only exists in leap years)';
            errorDiv.classList.remove('hidden');
        }
        input.classList.add('border-red-500');
        return false;
    }

    const age = calculateAgeFromDDMMYYYY(day, month, year);

    // Check for future dates
    if (birthDate > today) {
        if (errorDiv) {
            errorDiv.textContent = 'Date of birth cannot be in the future';
            errorDiv.classList.remove('hidden');
        }
        input.classList.add('border-red-500');
        return false;
    }

    return true;
}

function calculateAgeFromDDMMYYYY(day, month, year) {
    const today = new Date();
    const birthDate = new Date(year, month - 1, day); // month is 0-indexed
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
}

function formatDateInput(input) {
    let value = input.value.replace(/\D/g, ''); // Remove non-digits

    if (value.length >= 2) {
        value = value.substring(0, 2) + '-' + value.substring(2);
    }
    if (value.length >= 5) {
        value = value.substring(0, 5) + '-' + value.substring(5, 9);
    }

    input.value = value;
}

// Phone number validation functions
function formatPhoneNumber(input) {
    // Basic formatting - can be enhanced
    let value = input.value.replace(/\D/g, '');
    input.value = value;
}

function validatePhoneLength(input) {
    const value = input.value.replace(/\D/g, '');
    const errorDiv = document.getElementById('phone_number_error');

    if (errorDiv) {
        errorDiv.classList.add('hidden');
        input.classList.remove('border-red-500');
    }

    if (value.length > 0 && value.length < 7) {
        if (errorDiv) {
            errorDiv.textContent = 'Phone number is too short';
            errorDiv.classList.remove('hidden');
        }
        input.classList.add('border-red-500');
        return false;
    }

    return true;
}

function validatePhoneNumber(input) {
    const value = input.value.trim();
    const errorDiv = document.getElementById('phone_number_error');

    if (errorDiv) {
        errorDiv.classList.add('hidden');
        input.classList.remove('border-red-500');
    }

    if (!value) {
        if (errorDiv) {
            errorDiv.textContent = 'Phone number is required';
            errorDiv.classList.remove('hidden');
        }
        input.classList.add('border-red-500');
        return false;
    }

    return true;
}

function checkDuplicatePhone(input) {
    // This can be enhanced to check for duplicates via AJAX
    // For now, just validate format
    validatePhoneLength(input);
}

// Validation summary before submission
function showValidationSummary() {
    const errors = [];

    // Check required fields
    const requiredFields = document.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            const label = field.closest('div').querySelector('label')?.textContent || field.name;
            errors.push(`${label} is required`);
        }
    });

    // Check for validation errors
    const errorDivs = document.querySelectorAll('[id$="_error"]:not(.hidden)');
    errorDivs.forEach(div => {
        if (div.textContent.trim()) {
            errors.push(div.textContent);
        }
    });

    if (errors.length > 0) {
        showValidationModal(errors, []);
        return false;
    }

    return true;
}

function showValidationModal(errors, warnings) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

    let content = `
        <div class="bg-white rounded-lg p-6 max-w-lg mx-4 max-h-96 overflow-y-auto">
            <div class="flex items-center mb-4">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl mr-3"></i>
                <h3 class="text-lg font-semibold">Form Validation Summary</h3>
            </div>
    `;

    if (errors.length > 0) {
        content += `
            <div class="mb-4">
                <h4 class="font-semibold text-red-600 mb-2">Please fix these errors:</h4>
                <ul class="list-disc list-inside text-red-600 space-y-1">
                    ${errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    content += `
            <div class="flex justify-end">
                <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    OK
                </button>
            </div>
        </div>
    `;

    modal.innerHTML = content;
    document.body.appendChild(modal);
}

// Data Quality Calculation
function calculateDataQuality() {
    const requiredFields = [
        'first_name', 'last_name', 'phone_number', 'gender', 'member_status'
    ];

    const optionalFields = [
        'email', 'date_of_birth', 'marital_status', 'location', 'occupation',
        'school', 'emergency_contact_name', 'emergency_contact_phone',
        'baptism_status', 'department', 'role', 'membership_date'
    ];

    let requiredCompleted = 0;
    let optionalCompleted = 0;
    let qualityIssues = [];

    // Check required fields
    requiredFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field && field.value.trim()) {
            requiredCompleted++;
        }
    });

    // Check optional fields
    optionalFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field && field.value.trim()) {
            optionalCompleted++;
        }
    });

    // Calculate completeness percentage
    const totalFields = requiredFields.length + optionalFields.length;
    const completedFields = requiredCompleted + optionalCompleted;
    const completenessPercentage = Math.round((completedFields / totalFields) * 100);

    // Determine quality level and issues
    let qualityLevel = 'Poor';
    let qualityColor = 'red';

    if (completenessPercentage >= 90) {
        qualityLevel = 'Excellent';
        qualityColor = 'green';
    } else if (completenessPercentage >= 75) {
        qualityLevel = 'Good';
        qualityColor = 'blue';
    } else if (completenessPercentage >= 50) {
        qualityLevel = 'Fair';
        qualityColor = 'yellow';
    }

    // Update the indicator
    updateDataQualityIndicator(completenessPercentage, qualityColor, qualityLevel, qualityIssues);

    return {
        completeness: completenessPercentage,
        level: qualityLevel,
        issues: qualityIssues,
        requiredComplete: requiredCompleted === requiredFields.length
    };
}

function updateDataQualityIndicator(percentage, color, text, issues) {
    const qualityIcon = document.getElementById('quality-icon');
    const qualityText = document.getElementById('quality-text');
    const completenessScore = document.getElementById('completeness-score');
    const qualitySuggestions = document.getElementById('quality-suggestions');

    if (qualityIcon && qualityText && completenessScore) {
        // Update icon color
        qualityIcon.className = `w-3 h-3 rounded-full mr-2 bg-${color}-400`;

        // Update text
        qualityText.textContent = text;
        completenessScore.textContent = percentage + '%';

        // Show/hide suggestions
        if (qualitySuggestions) {
            if (percentage < 90) {
                qualitySuggestions.classList.remove('hidden');
            } else {
                qualitySuggestions.classList.add('hidden');
            }
        }
    }
}

// Initialize data quality on page load
document.addEventListener('DOMContentLoaded', function() {
    calculateDataQuality();

    // Recalculate on form changes
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('input', calculateDataQuality);
        form.addEventListener('change', calculateDataQuality);
    }
});

function showQualitySuggestions() {
    alert('Data quality suggestions:\n• Add missing optional fields\n• Verify contact information\n• Complete profile details');
}

// Handle create parent form submission with AJAX
function handleCreateParent(event) {
    event.preventDefault();

    const form = event.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;

    // Show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Parent...';

    // Create FormData object
    const formData = new FormData(form);

    // Submit via AJAX
    fetch(form.action, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => {
        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return response.json();
        } else {
            // If not JSON, it might be an error page or redirect
            throw new Error('Server returned non-JSON response. Please check for errors.');
        }
    })
    .then(data => {
        if (data.success) {
            // Show success message
            showNotification('success', data.message);

            // Redirect after a short delay
            setTimeout(() => {
                window.location.href = '<?php echo url("members/view?id="); ?>' + data.parent_id;
            }, 1500);
        } else {
            // Show error message
            showNotification('error', data.message);

            // Debug information for authentication issues
            if (data.debug) {
                console.log('Debug info:', data.debug);
            }

            // If it's a duplicate member, offer to assign existing member
            if (data.existing_member) {
                const assign = confirm(data.message + '\n\nWould you like to assign the existing member as the parent instead?');
                if (assign) {
                    // TODO: Implement assign existing member functionality
                    showNotification('info', 'Assign existing member functionality coming soon...');
                }
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('error', 'An unexpected error occurred. Please try again.');
    })
    .finally(() => {
        // Restore button state
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    });

    return false;
}

// Show notification function
function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-md ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                'fa-info-circle'
            } mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}
</script>

<?php
// Get the contents of the output buffer
$content = ob_get_clean();

// Include the layout template
include 'views/layouts/main.php';
?>
