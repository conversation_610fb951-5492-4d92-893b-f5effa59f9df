<?php

/**
 * Simple Custom Finance Category Controller
 * 
 * Handles CRUD operations for custom finance categories using simple, proven architecture
 * This is a permanent solution that eliminates complex service layer dependencies
 */

require_once 'config/database.php';
require_once 'models/CustomFinanceCategory.php';
require_once 'helpers/functions.php';
require_once 'controllers/BaseRestfulController.php';

class SimpleCustomFinanceCategoryController extends BaseRestfulController {
    private $db;
    private $customCategory;

    public function __construct() {
        try {
            // Get database connection
            $database = new Database();
            $this->db = $database->getConnection();

            // Initialize the CustomFinanceCategory model
            $this->customCategory = new CustomFinanceCategory($this->db);

        } catch (Exception $e) {
            error_log("SimpleCustomFinanceCategoryController initialization failed: " . $e->getMessage());
            set_flash_message("System initialization failed. Please try again.", 'danger');
            redirect('finance');
        }
    }

    /**
     * Check if user is logged in
     */
    private function checkAuth(): void {
        if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
            set_flash_message('Please log in to access this page', 'danger');
            redirect('login');
        }
    }

    /**
     * Validate request method
     */
    private function validateRequestMethod(string $expectedMethod): void {
        if ($_SERVER['REQUEST_METHOD'] !== $expectedMethod) {
            set_flash_message('Invalid request method', 'danger');
            redirect('finance/categories');
        }
    }

    /**
     * Sanitize input data
     */
    private function sanitizeData(array $data): array {
        $sanitized = [];
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = sanitize($value);
            } else {
                $sanitized[$key] = $value;
            }
        }
        return $sanitized;
    }

    // Validation logic moved to CustomFinanceCategory model

    /**
     * Generate slug from name
     */
    private function generateSlug(string $name): string {
        return strtolower(str_replace([' ', '-'], '_', trim($name)));
    }

    /**
     * Generate dashboard route
     */
    private function generateDashboardRoute(string $name, string $type): string {
        $slug = $this->generateSlug($name);

        // Map category types to dashboard types correctly
        $routeType = 'income'; // Default to income

        if ($type === 'expenses') {
            $routeType = 'expense';
        } elseif ($type === 'member_payments') {
            $routeType = 'income'; // Member payments are income
        } elseif ($type === 'general_income') {
            $routeType = 'income'; // General income is income
        }

        return "finance/dashboard/category?category={$slug}&type={$routeType}";
    }

    /**
     * Display categories index page
     */
    public function index(): void {
        try {
            $this->checkAuth();

            // Get all categories including inactive ones for management interface
            $categories = $this->customCategory->getAll(false); // false = include inactive

            // Group categories by type
            $categoriesByType = [
                'member_payments' => [],
                'general_income' => [],
                'expenses' => []
            ];

            foreach ($categories as $category) {
                if (isset($categoriesByType[$category->category_type])) {
                    $categoriesByType[$category->category_type][] = $category;
                }
            }

            // Set up view variables for layout
            $page_title = getPageTitle('Finance Categories');
            $pageTitle = 'Finance Categories'; // Keep both for compatibility
            $active_page = 'finance'; // Set active page for navigation
            $memberPaymentCategories = $categoriesByType['member_payments'];
            $generalIncomeCategories = $categoriesByType['general_income'];
            $expenseCategories = $categoriesByType['expenses'];

            // Start output buffering to capture the view content
            ob_start();

            // Include the view
            include 'views/finance_categories/index.php';

            // Get the contents and store in $content variable for layout
            $content = ob_get_clean();

            // Include the main layout which will echo $content
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("Categories index failed: " . $e->getMessage());
            set_flash_message('An error occurred while loading categories', 'danger');
            redirect('finance');
        }
    }

    /**
     * Store new category
     */
    public function store(): void {
        try {
            $this->checkAuth();
            $this->validateRequestMethod('POST');

            // Get and sanitize input data
            $rawData = [
                'name' => $_POST['name'] ?? '',
                'label' => $_POST['label'] ?? '',
                'description' => $_POST['description'] ?? '',
                'category_type' => $_POST['category_type'] ?? '',
                'icon' => $_POST['icon'] ?? 'fas fa-coins',
                'requires_member' => isset($_POST['requires_member']) ? 1 : 0
            ];

            $data = $this->sanitizeData($rawData);

            // Model will handle all validation
            // Set properties
            $this->customCategory->name = $data['name'];
            $this->customCategory->label = $data['label'];
            $this->customCategory->description = $data['description'];
            $this->customCategory->category_type = $data['category_type'];
            $this->customCategory->icon = $data['icon'];
            $this->customCategory->requires_member = $data['requires_member'];
            $this->customCategory->is_active = 1;
            $this->customCategory->is_core = 0;
            $this->customCategory->slug = $this->generateSlug($data['name']);
            $this->customCategory->dashboard_route = $this->generateDashboardRoute($data['name'], $data['category_type']);
            $this->customCategory->created_by = $_SESSION['user_id'] ?? 1;

            // Create category (model will handle validation)
            if ($this->customCategory->create()) {
                set_flash_message('Category created successfully', 'success');
            } else {
                $errorMessage = $this->customCategory->error ?? 'Failed to create category.';
                set_flash_message($errorMessage, 'danger');
            }

        } catch (Exception $e) {
            error_log("Category creation failed: " . $e->getMessage());
            set_flash_message('An error occurred while creating the category', 'danger');
        }

        redirect('finance/categories');
    }

    /**
     * Update existing category
     */
    public function update($id = null): void {
        try {
            $this->checkAuth();

            // Support both RESTful (PUT) and legacy (POST) methods
            if (!$this->checkHttpMethod(['POST', 'PUT'])) {
                redirect('finances/categories');
                return;
            }

            $category_id = $this->getId($id, 'id', 'id');
            if (!$category_id) {
                set_flash_message('Invalid category ID', 'danger');
                redirect('finances/categories');
                return;
            }

            // Get existing category
            if (!$this->customCategory->getById($id)) {
                set_flash_message('Category not found', 'danger');
                redirect('finance/categories');
                return;
            }

            // Get and sanitize input data
            $rawData = [
                'id' => $id,
                'name' => $_POST['name'] ?? '',
                'label' => $_POST['label'] ?? '',
                'description' => $_POST['description'] ?? '',
                'category_type' => $_POST['category_type'] ?? '',
                'icon' => $_POST['icon'] ?? 'fas fa-coins',
                'requires_member' => isset($_POST['requires_member']) ? 1 : 0
            ];

            $data = $this->sanitizeData($rawData);

            // Model will handle all validation
            // Update properties
            $this->customCategory->name = $data['name'];
            $this->customCategory->label = $data['label'];
            $this->customCategory->description = $data['description'];
            $this->customCategory->category_type = $data['category_type'];
            $this->customCategory->icon = $data['icon'];
            $this->customCategory->requires_member = $data['requires_member'];
            $this->customCategory->slug = $this->generateSlug($data['name']);
            $this->customCategory->dashboard_route = $this->generateDashboardRoute($data['name'], $data['category_type']);

            // Update category (model will handle validation)
            if ($this->customCategory->update()) {
                set_flash_message('Category updated successfully', 'success');
            } else {
                $errorMessage = $this->customCategory->error ?? 'Failed to update category.';
                set_flash_message($errorMessage, 'danger');
            }

        } catch (Exception $e) {
            error_log("Category update failed: " . $e->getMessage());
            set_flash_message('An error occurred while updating the category', 'danger');
        }

        redirect('finance/categories');
    }

    /**
     * Delete category
     */
    public function delete(): void {
        try {
            $this->checkAuth();

            // Use base class method to get ID from multiple sources (supports both legacy and RESTful)
            $id = $this->getId(null, 'id', 'id');
            if (!$id) {
                $this->handleResponse(false, 'Invalid category ID', 'finances/categories');
                return;
            }

            // Get category data
            $categoryData = $this->customCategory->getById($id);
            if (!$categoryData) {
                set_flash_message('Category not found', 'danger');
                redirect('finance/categories');
                return;
            }

            // Set the ID in the model for deletion
            $this->customCategory->id = $id;
            $this->customCategory->name = $categoryData->name;

            // The model handles transaction checking and soft/hard delete logic
            // So we don't need to duplicate that logic here
            if ($this->customCategory->delete()) {
                // Check if it was a soft delete (category still exists but inactive)
                $checkQuery = "SELECT is_active FROM custom_finance_categories WHERE id = ?";
                $stmt = $this->db->prepare($checkQuery);
                $stmt->execute([$id]);
                $result = $stmt->fetch(PDO::FETCH_OBJ);

                if ($result && $result->is_active == 0) {
                    set_flash_message("Category '{$categoryData->name}' has been deactivated (it has associated transactions)", 'warning');
                } else {
                    set_flash_message("Category '{$categoryData->name}' deleted successfully", 'success');
                }
            } else {
                set_flash_message('Failed to delete category', 'danger');
            }

        } catch (Exception $e) {
            error_log("Category deletion failed: " . $e->getMessage());
            set_flash_message('An error occurred while deleting the category', 'danger');
        }

        redirect('finance/categories');
    }

    /**
     * Get category data (AJAX)
     */
    public function getCategory(): void {
        try {
            $this->checkAuth();

            $id = intval($_GET['id'] ?? 0);
            if ($id <= 0) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid category ID']);
                return;
            }

            if ($this->customCategory->getById($id)) {
                echo json_encode([
                    'id' => $this->customCategory->id,
                    'name' => $this->customCategory->name,
                    'label' => $this->customCategory->label,
                    'description' => $this->customCategory->description,
                    'category_type' => $this->customCategory->category_type,
                    'icon' => $this->customCategory->icon,
                    'requires_member' => $this->customCategory->requires_member
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Category not found']);
            }

        } catch (Exception $e) {
            error_log("Get category failed: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Internal server error']);
        }
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(): void {
        try {
            $this->checkAuth();

            $id = intval($_POST['id'] ?? 0);
            if ($id <= 0) {
                echo json_encode(['success' => false, 'message' => 'Invalid category ID']);
                return;
            }

            // Get category data
            $categoryData = $this->customCategory->getById($id);
            if (!$categoryData) {
                echo json_encode(['success' => false, 'message' => 'Category not found']);
                return;
            }

            // Populate all model properties from the database data
            $this->customCategory->id = $categoryData->id;
            $this->customCategory->name = $categoryData->name;
            $this->customCategory->label = $categoryData->label;
            $this->customCategory->icon = $categoryData->icon;
            $this->customCategory->description = $categoryData->description;
            $this->customCategory->category_type = $categoryData->category_type;
            $this->customCategory->requires_member = $categoryData->requires_member;
            $this->customCategory->sort_order = $categoryData->sort_order;
            $this->customCategory->slug = $categoryData->slug;
            $this->customCategory->dashboard_route = $categoryData->dashboard_route;

            // Toggle status
            $newStatus = $categoryData->is_active ? 0 : 1;
            $this->customCategory->is_active = $newStatus;

            if ($this->customCategory->update()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Category status updated successfully',
                    'new_status' => $newStatus
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update category status']);
            }

        } catch (Exception $e) {
            error_log("Toggle status failed: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'An error occurred']);
        }
    }

    /**
     * Show create form (RESTful)
     */
    public function create(): void {
        try {
            $this->checkAuth();

            // For now, redirect to index with create modal trigger
            redirect('finances/categories?action=create');
        } catch (Exception $e) {
            error_log("Error in create method: " . $e->getMessage());
            set_flash_message('An error occurred while loading the create form', 'danger');
            redirect('finances/categories');
        }
    }

    /**
     * Show single category (RESTful)
     */
    public function show($id = null): void {
        try {
            $this->checkAuth();

            $category_id = $this->getId($id, 'id', 'id');
            if (!$category_id) {
                $this->handleResponse(false, 'Invalid category ID', 'finances/categories');
                return;
            }

            $category = $this->customCategory->getById($category_id);
            if (!$category) {
                $this->handleResponse(false, 'Category not found', 'finances/categories');
                return;
            }

            // For AJAX requests, return JSON
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'data' => $category]);
                exit;
            }

            // For regular requests, redirect to index with view modal
            redirect('finances/categories?action=view&id=' . $category_id);
        } catch (Exception $e) {
            error_log("Error in show method: " . $e->getMessage());
            $this->handleResponse(false, 'An error occurred while loading the category', 'finances/categories');
        }
    }

    /**
     * Show edit form (RESTful)
     */
    public function edit($id = null): void {
        try {
            $this->checkAuth();

            $category_id = $this->getId($id, 'id', 'id');
            if (!$category_id) {
                $this->handleResponse(false, 'Invalid category ID', 'finances/categories');
                return;
            }

            // For now, redirect to index with edit modal trigger
            redirect('finances/categories?action=edit&id=' . $category_id);
        } catch (Exception $e) {
            error_log("Error in edit method: " . $e->getMessage());
            set_flash_message('An error occurred while loading the edit form', 'danger');
            redirect('finances/categories');
        }
    }
}
?>
