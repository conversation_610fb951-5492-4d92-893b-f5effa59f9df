# ICGC Church Management System - .htaccess Configuration
# Enhances security and performance

# Enable URL rewriting
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Set application base
    RewriteBase /icgc/
    
    # Redirect to HTTPS if not already using it
    # Uncomment when SS<PERSON> is configured
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Prevent direct access to sensitive directories
    RewriteRule ^(config|database|logs|vendor|cache)(/|$) - [F,L]
    
    # Handle 404 errors
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [QSA,L]
</IfModule>

# Set security headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header set X-Frame-Options "SAMEORIGIN"
    
    # XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Prevent MIME type sniffing
    Header set X-Content-Type-Options "nosniff"
    
    # Referrer policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (CSP) - Basic protection
    # Customize as needed for your external resources
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://ajax.googleapis.com https://cdnjs.cloudflare.com 'unsafe-inline'; style-src 'self' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data: https://chart.googleapis.com https://api.qrserver.com https://qr-server.com; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com data:; connect-src 'self';"
</IfModule>

# PHP settings
<IfModule mod_php8.c>
    # PHP error reporting
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
    
    # PHP security settings
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
    php_flag register_globals Off
    php_flag expose_php Off
    
    # Session security
    php_flag session.cookie_httponly On
    php_flag session.use_only_cookies On
    php_value session.cookie_samesite "Lax"
    
    # Set max upload file size
    php_value upload_max_filesize 8M
    php_value post_max_size 10M
</IfModule>

# Caching settings
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 month"
    ExpiresByType application/font-woff2 "access plus 1 month"
    ExpiresByType application/font-sfnt "access plus 1 month"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 month"
</IfModule>

# Enable Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Disable directory listing
Options -Indexes

# Protect .htaccess file
<Files .htaccess>
    Order Allow,Deny
    Deny from all
</Files>

# Protect sensitive files
<FilesMatch "^(\.env|composer\.json|composer\.lock)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# For now, we'll include the public/index.php file directly
# This maintains compatibility while we transition to the new structure
# Later, we can change this to a redirect:
# RewriteRule ^$ public/ [L]
# RewriteRule (.*) public/$1 [L]

# Explicitly route /ai and its subdirectories to index.php
RewriteRule ^ai(/.*)?$ index.php [QSA,L]

# Prevent direct access to sensitive files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# TEMPORARILY COMMENTED OUT - This was blocking controller access
# <FilesMatch "^(config|models|controllers|views|utils|database|scripts)">
#     Order allow,deny
#     Deny from all
# </FilesMatch>

# PHP settings
php_flag display_errors off
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300
