<?php
/**
 * Run Guardian Contacts Migration
 * This script creates the missing guardian_contacts table and related tables
 */

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Running Guardian Contacts Migration</h2>";
    
    // Read the SQL migration file
    $sql_file = __DIR__ . '/add_standalone_child_registration.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("Migration file not found: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // Split SQL statements by semicolon and execute each one
    $statements = explode(';', $sql_content);
    
    $executed = 0;
    $errors = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // Skip empty statements and comments
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $conn->exec($statement);
            $executed++;
            echo "<p style='color: green;'>✓ Executed statement " . substr($statement, 0, 50) . "...</p>";
        } catch (PDOException $e) {
            $errors++;
            echo "<p style='color: red;'>✗ Error in statement: " . $e->getMessage() . "</p>";
            echo "<p style='color: gray;'>Statement: " . substr($statement, 0, 100) . "...</p>";
        }
    }
    
    echo "<h3>Migration Summary</h3>";
    echo "<p>Executed: $executed statements</p>";
    echo "<p>Errors: $errors statements</p>";
    
    if ($errors === 0) {
        echo "<p style='color: green; font-weight: bold;'>✅ Migration completed successfully!</p>";
        
        // Verify the guardian_contacts table was created
        $check_query = "SHOW TABLES LIKE 'guardian_contacts'";
        $result = $conn->query($check_query);
        
        if ($result && $result->rowCount() > 0) {
            echo "<p style='color: green;'>✓ guardian_contacts table verified</p>";
        } else {
            echo "<p style='color: red;'>✗ guardian_contacts table not found</p>";
        }
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️ Migration completed with some errors</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>Migration failed: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
