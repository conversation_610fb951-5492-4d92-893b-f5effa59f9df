<?php
/**
 * SMS Helper Functions
 *
 * This file contains helper functions for sending SMS using the Arkesel API
 */

/**
 * Send SMS using Arkesel API
 *
 * @param string|array $recipients Phone number(s) to send SMS to
 * @param string $message Message content
 * @param string $sender_id Sender ID (from settings)
 * @param string $api_key API key (from settings)
 * @return array Response with status and details
 */
function send_sms($recipients, $message, $sender_id = null, $api_key = null) {
    // Get settings if not provided
    if (empty($api_key) || empty($sender_id)) {
        $db = new Database();
        $setting = new Setting($db->getConnection());
        $settings = $setting->getAllAsArray();

        $api_key = $api_key ?? $settings['sms_api_key'] ?? '';
        $sender_id = $sender_id ?? $settings['sms_sender_id'] ?? 'ICGC';
    }

    // Validate API key
    if (empty($api_key)) {
        return [
            'status' => false,
            'message' => 'SMS API key is not configured. Please set it in the settings.',
            'details' => null
        ];
    }

    // Format recipients
    if (is_array($recipients)) {
        // If multiple recipients, process each one
        $results = [];
        $success_count = 0;
        $failed_count = 0;

        foreach ($recipients as $recipient) {
            $result = send_single_sms($recipient, $message, $sender_id, $api_key);
            $results[] = $result;

            if ($result['status']) {
                $success_count++;
            } else {
                $failed_count++;
            }
        }

        return [
            'status' => $success_count > 0,
            'message' => "SMS sent to {$success_count} recipients. Failed: {$failed_count}",
            'details' => $results
        ];
    } else {
        // Single recipient
        return send_single_sms($recipients, $message, $sender_id, $api_key);
    }
}

/**
 * Send SMS to a single recipient
 *
 * @param string $recipient Phone number to send SMS to
 * @param string $message Message content
 * @param string $sender_id Sender ID
 * @param string $api_key API key
 * @return array Response with status and details
 */
function send_single_sms($recipient, $message, $sender_id, $api_key) {
    // Format phone number (ensure it has country code)
    $recipient = format_phone_number($recipient);

    // Log the SMS attempt
    error_log("Attempting to send SMS to {$recipient} with sender ID {$sender_id}");

    // Arkesel API uses GET method for sending SMS
    // Using file_get_contents method which is more reliable for this API
    $request_url = "https://sms.arkesel.com/sms/api?action=send-sms&api_key={$api_key}&to={$recipient}&from={$sender_id}&sms=" . urlencode($message);

    // Log the request URL (without API key for security)
    $log_url = str_replace($api_key, 'API_KEY_HIDDEN', $request_url);
    error_log("Sending SMS using URL: {$log_url}");

    try {
        // Set stream context to capture response headers
        $context = stream_context_create([
            'http' => [
                'ignore_errors' => true, // Don't throw exceptions on HTTP errors
                'method' => 'GET',
                'timeout' => 30,
                'header' => "Content-Type: application/x-www-form-urlencoded\r\n"
            ]
        ]);

        // Make the request
        $response = @file_get_contents($request_url, false, $context);

        // Get response headers
        $response_headers = $http_response_header ?? [];
        $http_code = 0;

        // Extract HTTP status code
        foreach ($response_headers as $header) {
            if (preg_match('/^HTTP\/\d\.\d\s+(\d+)\s+/', $header, $matches)) {
                $http_code = (int)$matches[1];
                break;
            }
        }

        // Log the response
        error_log("SMS API Response - HTTP Code: {$http_code}, Response length: " . strlen($response));

        // Process response
        $response_data = json_decode($response, true);

        // First check HTTP code - 200 usually means success regardless of response content
        if ($http_code == 200) {
            error_log("SMS likely sent successfully based on HTTP 200 status code");

            return [
                'status' => true,
                'message' => 'SMS sent successfully (HTTP 200)',
                'details' => [
                    'raw_response' => $response,
                    'http_code' => $http_code,
                    'request_url' => $log_url,
                    'method' => 'GET'
                ]
            ];
        }

        // Check if response is valid JSON
        if (json_last_error() !== JSON_ERROR_NONE) {
            // Not JSON, check if it contains success message
            if (strpos($response, 'success') !== false ||
                strpos($response, '1000') !== false ||
                empty(trim($response))) { // Empty response might be success in some cases

                // Log the successful response
                error_log("SMS sent successfully with non-JSON response. Response: " . substr($response, 0, 100));

                return [
                    'status' => true,
                    'message' => 'SMS sent successfully',
                    'details' => [
                        'raw_response' => $response,
                        'http_code' => $http_code,
                        'request_url' => $log_url,
                        'method' => 'GET'
                    ]
                ];
            } else {
                // Log the failed response
                error_log("SMS failed with non-JSON response. HTTP Code: {$http_code}, Response: " . substr($response, 0, 100));

                // If we got a 405 error (Method Not Allowed), provide specific guidance
                if ($http_code == 405) {
                    return [
                        'status' => false,
                        'message' => 'SMS API returned Method Not Allowed (405). The API endpoint may have changed. Please check your API configuration.',
                        'details' => [
                            'raw_response' => $response,
                            'http_code' => $http_code,
                            'request_url' => $log_url,
                            'method' => 'GET'
                        ]
                    ];
                }

                return [
                    'status' => false,
                    'message' => "Failed to send SMS: HTTP error {$http_code}",
                    'details' => [
                        'raw_response' => $response,
                        'http_code' => $http_code,
                        'request_url' => $log_url,
                        'method' => 'GET'
                    ]
                ];
            }
        }

        // Process JSON response
        if (isset($response_data['code']) && $response_data['code'] === '1000') {
            return [
                'status' => true,
                'message' => 'SMS sent successfully',
                'details' => $response_data
            ];
        } else if (isset($response_data['code']) && $response_data['code'] === '103') {
            // Invalid phone number, try alternative format
            $alternative_recipient = try_alternative_format($recipient);
            if ($alternative_recipient !== $recipient) {
                return send_single_sms($alternative_recipient, $message, $sender_id, $api_key);
            }

            return [
                'status' => false,
                'message' => 'Failed to send SMS: Invalid phone number format',
                'details' => $response_data
            ];
        } else {
            return [
                'status' => false,
                'message' => 'Failed to send SMS: ' . ($response_data['message'] ?? 'Unknown error'),
                'details' => $response_data
            ];
        }
    } catch (Exception $e) {
        error_log("Exception when sending SMS: " . $e->getMessage());

        return [
            'status' => false,
            'message' => 'Failed to send SMS: ' . $e->getMessage(),
            'details' => [
                'exception' => $e->getMessage(),
                'request_url' => $log_url,
                'method' => 'GET'
            ]
        ];
    }
}

/**
 * Try alternative phone number format
 *
 * @param string $phone_number Phone number to format
 * @return string Formatted phone number
 */
function try_alternative_format($phone_number) {
    // Try different formats
    if (substr($phone_number, 0, 3) === '233') {
        // Try without country code
        return '0' . substr($phone_number, 3);
    } else if (substr($phone_number, 0, 1) === '0') {
        // Try with country code
        return '233' . substr($phone_number, 1);
    } else if (strlen($phone_number) === 9) {
        // Try with leading zero
        return '0' . $phone_number;
    }

    // Return original if no alternative
    return $phone_number;
}

/**
 * Try alternative API endpoint for sending SMS
 * This is a fallback function in case the primary method fails
 *
 * @param string $recipient Phone number to send SMS to
 * @param string $message Message content
 * @param string $sender_id Sender ID
 * @param string $api_key API key
 * @return array Response with status and details
 */
function try_alternative_endpoint($recipient, $message, $sender_id, $api_key) {
    // Log the attempt to use alternative endpoint
    error_log("Attempting to send SMS using alternative endpoint for {$recipient}");

    // Try the V2 API endpoint if available
    $request_url = "https://api.arkesel.com/sms/v2/send?action=send-sms&api_key={$api_key}&to={$recipient}&from={$sender_id}&sms=" . urlencode($message);

    // Log the request URL (without API key for security)
    $log_url = str_replace($api_key, 'API_KEY_HIDDEN', $request_url);
    error_log("Sending SMS using alternative URL: {$log_url}");

    try {
        // Set stream context to capture response headers
        $context = stream_context_create([
            'http' => [
                'ignore_errors' => true, // Don't throw exceptions on HTTP errors
                'method' => 'GET',
                'timeout' => 30,
                'header' => "Content-Type: application/x-www-form-urlencoded\r\n"
            ]
        ]);

        // Make the request
        $response = @file_get_contents($request_url, false, $context);

        // Get response headers
        $response_headers = $http_response_header ?? [];
        $http_code = 0;

        // Extract HTTP status code
        foreach ($response_headers as $header) {
            if (preg_match('/^HTTP\/\d\.\d\s+(\d+)\s+/', $header, $matches)) {
                $http_code = (int)$matches[1];
                break;
            }
        }

        // Log the response
        error_log("Alternative SMS API Response - HTTP Code: {$http_code}, Response length: " . strlen($response));

        // Process response
        $response_data = json_decode($response, true);

        // First check HTTP code - 200 usually means success regardless of response content
        if ($http_code == 200) {
            error_log("SMS likely sent successfully using alternative endpoint based on HTTP 200 status code");

            return [
                'status' => true,
                'message' => 'SMS sent successfully using alternative endpoint (HTTP 200)',
                'details' => [
                    'raw_response' => $response,
                    'http_code' => $http_code,
                    'request_url' => $log_url,
                    'method' => 'GET (Alternative)'
                ]
            ];
        }

        // If not successful, return error
        return [
            'status' => false,
            'message' => 'Failed to send SMS using alternative endpoint',
            'details' => [
                'raw_response' => $response,
                'http_code' => $http_code,
                'request_url' => $log_url,
                'method' => 'GET (Alternative)'
            ]
        ];
    } catch (Exception $e) {
        error_log("Exception when sending SMS using alternative endpoint: " . $e->getMessage());

        return [
            'status' => false,
            'message' => 'Failed to send SMS using alternative endpoint: ' . $e->getMessage(),
            'details' => [
                'exception' => $e->getMessage(),
                'request_url' => $log_url,
                'method' => 'GET (Alternative)'
            ]
        ];
    }
}

/**
 * Format phone number to ensure it has country code
 *
 * @param string $phone_number Phone number to format
 * @return string Formatted phone number
 */
function format_phone_number($phone_number) {
    // Use PhoneNumberUtils for proper normalization
    require_once __DIR__ . '/PhoneNumberUtils.php';

    try {
        return PhoneNumberUtils::normalize($phone_number);
    } catch (Exception $e) {
        // Fallback to basic formatting if PhoneNumberUtils fails
        error_log('SMS Helper: PhoneNumberUtils failed, using fallback: ' . $e->getMessage());

        // Remove any non-numeric characters except the plus sign at the beginning
        if (substr($phone_number, 0, 1) === '+') {
            return $phone_number; // Already formatted
        }

        // Remove any remaining non-numeric characters
        $clean_number = preg_replace('/[^0-9]/', '', $phone_number);

        // If empty, return original
        if (empty($clean_number)) {
            return $phone_number;
        }

        // Basic fallback: assume it needs a + if it doesn't have one
        if (strlen($clean_number) > 10) {
            return '+' . $clean_number;
        }

        return $phone_number; // Return as-is if we can't determine format
    }
}

/**
 * Replace placeholders in message with member data
 *
 * @param string $message Message with placeholders
 * @param array $member_data Member data for replacement
 * @return string Message with placeholders replaced
 */
function replace_sms_placeholders($message, $member_data) {
    $placeholders = [
        '{first_name}' => $member_data['first_name'] ?? '',
        '{last_name}' => $member_data['last_name'] ?? '',
        '{full_name}' => ($member_data['first_name'] ?? '') . ' ' . ($member_data['last_name'] ?? ''),
        '{phone}' => $member_data['phone_number'] ?? '',
        '{email}' => $member_data['email'] ?? '',
        '{id}' => $member_data['id'] ?? '',
    ];

    return str_replace(array_keys($placeholders), array_values($placeholders), $message);
}

/**
 * Check SMS credit balance using Arkesel API
 *
 * @param string $api_key API key (from settings)
 * @return array Response with status, balance and details
 */
function check_sms_balance($api_key = null) {
    // Get settings if not provided
    if (empty($api_key)) {
        $db = new Database();
        $setting = new Setting($db->getConnection());
        $settings = $setting->getAllAsArray();

        $api_key = $settings['sms_api_key'] ?? '';
    }

    // Validate API key
    if (empty($api_key)) {
        return [
            'status' => false,
            'message' => 'SMS API key is not configured. Please set it in the settings.',
            'balance' => 0,
            'details' => null
        ];
    }

    // Use the exact URL format provided
    $request_url = "https://sms.arkesel.com/sms/api?action=check-balance&api_key={$api_key}&response=json";

    // Initialize cURL session
    $ch = curl_init();

    // Set cURL options with timeouts to prevent hanging
    curl_setopt($ch, CURLOPT_URL, $request_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For development only, consider enabling in production
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Maximum 10 seconds total timeout
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // Maximum 5 seconds to connect
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // Don't follow redirects
    curl_setopt($ch, CURLOPT_MAXREDIRS, 0); // No redirects

    // Execute cURL request
    $response = curl_exec($ch);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);

    // Close cURL session
    curl_close($ch);

    // Check for errors
    if ($error) {
        // Log the error for debugging
        error_log("SMS Balance Check Error: " . $error);

        return [
            'status' => false,
            'message' => 'Failed to check balance: Connection timeout or network error',
            'balance' => 0,
            'details' => [
                'curl_error' => $error,
                'http_code' => $info['http_code'] ?? 0,
                'request_url' => $request_url,
                'total_time' => $info['total_time'] ?? 0
            ]
        ];
    }

    // Check for timeout (if response took too long)
    if (isset($info['total_time']) && $info['total_time'] >= 10) {
        error_log("SMS Balance Check Timeout: " . $info['total_time'] . " seconds");

        return [
            'status' => false,
            'message' => 'Failed to check balance: Request timeout',
            'balance' => 0,
            'details' => [
                'timeout' => true,
                'total_time' => $info['total_time'],
                'http_code' => $info['http_code'] ?? 0,
                'request_url' => $request_url
            ]
        ];
    }

    // Process response
    $response_data = json_decode($response, true);

    // Check if response is valid JSON
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'status' => false,
            'message' => 'Failed to check balance: Invalid response',
            'balance' => 0,
            'details' => [
                'raw_response' => $response,
                'http_code' => $info['http_code'],
                'request_url' => $request_url
            ]
        ];
    }

    // Process JSON response
    if (isset($response_data['balance'])) {
        return [
            'status' => true,
            'message' => 'Balance retrieved successfully',
            'balance' => $response_data['balance'],
            'details' => $response_data
        ];
    } else {
        return [
            'status' => false,
            'message' => 'Failed to retrieve balance: ' . ($response_data['message'] ?? 'Unknown error'),
            'balance' => 0,
            'details' => $response_data
        ];
    }
}
?>
