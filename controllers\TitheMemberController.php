<?php
/**
 * Tithe Member Tracking Controller
 * Handles dedicated member tithe tracking and management
 */

require_once 'config/database.php';

class TitheMemberController {
    private $database;

    public function __construct() {
        $this->database = new Database();
    }

    /**
     * Main member tracking page
     */
    public function index() {
        try {
            // Get filter parameters
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 20; // Members per page
            $search = trim($_GET['search'] ?? '');
            $sortBy = $_GET['sort'] ?? 'total_amount_desc';
            $filterActive = $_GET['active'] ?? 'all';
            $dateRange = $_GET['date_range'] ?? 'all_time';
            $amountRange = $_GET['amount_range'] ?? 'all';

            // Get members with pagination and filters
            $membersData = $this->getMembersWithFilters($page, $limit, $search, $sortBy, $filterActive, $dateRange, $amountRange);
            
            // Get summary statistics
            $stats = $this->getTitheStatistics($search, $filterActive, $dateRange, $amountRange);

            // Set page data
            $pageTitle = 'Member Tithe Tracking';
            $active_page = 'finance'; // For navigation highlighting
            $members = $membersData['members'];
            $totalMembers = $membersData['total'];
            $totalPages = ceil($totalMembers / $limit);
            $currentPage = $page;

            // Start output buffering
            ob_start();
            include 'views/finances/tithe/members.php';
            $content = ob_get_clean();

            // Include layout
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading member tracking: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finance/dashboard/tithe');
        }
    }

    /**
     * Get members with advanced filtering and pagination
     */
    private function getMembersWithFilters($page, $limit, $search, $sortBy, $filterActive, $dateRange, $amountRange) {
        $conn = $this->database->getConnection();
        $offset = ($page - 1) * $limit;

        // Check schema type
        $hasNewSchema = $this->checkNewSchema($conn);
        $whereClause = $hasNewSchema ? "f.transaction_type = 'income' AND f.income_category = 'tithe'" : "f.category = 'tithe'";

        // Build date filter
        $dateFilter = $this->buildDateFilter($dateRange);
        
        // Build amount filter for HAVING clause
        $amountFilter = $this->buildAmountFilter($amountRange);

        // Build search filter
        $searchFilter = '';
        $searchParams = [];
        if (!empty($search)) {
            $searchFilter = "AND (CONCAT(m.first_name, ' ', m.last_name) LIKE :search 
                           OR m.phone_number LIKE :search 
                           OR m.email LIKE :search)";
            $searchParams[':search'] = "%$search%";
        }

        // Build activity filter
        $activityFilter = '';
        if ($filterActive === 'active') {
            $activityFilter = "AND f.transaction_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH)";
        } elseif ($filterActive === 'inactive') {
            $activityFilter = "AND f.transaction_date < DATE_SUB(NOW(), INTERVAL 3 MONTH)";
        }

        // Build sort clause
        $sortClause = $this->buildSortClause($sortBy);

        // Main query
        $query = "SELECT 
                    m.id,
                    m.first_name,
                    m.last_name,
                    m.phone_number,
                    m.email,
                    m.profile_picture,
                    COUNT(f.id) as total_payments,
                    SUM(f.amount) as total_amount,
                    AVG(f.amount) as average_amount,
                    MAX(f.transaction_date) as last_payment_date,
                    MIN(f.transaction_date) as first_payment_date,
                    DATEDIFF(NOW(), MAX(f.transaction_date)) as days_since_last_payment
                  FROM members m
                  INNER JOIN finances f ON m.id = f.member_id 
                  WHERE $whereClause $dateFilter $activityFilter $searchFilter
                  GROUP BY m.id
                  $amountFilter
                  $sortClause
                  LIMIT :limit OFFSET :offset";

        $stmt = $conn->prepare($query);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        
        // Bind search parameters
        foreach ($searchParams as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get total count for pagination
        $countQuery = "SELECT COUNT(DISTINCT m.id) as total
                       FROM members m
                       INNER JOIN finances f ON m.id = f.member_id 
                       WHERE $whereClause $dateFilter $activityFilter $searchFilter";

        $countStmt = $conn->prepare($countQuery);
        foreach ($searchParams as $key => $value) {
            $countStmt->bindValue($key, $value);
        }
        $countStmt->execute();
        $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

        return [
            'members' => $members,
            'total' => $total
        ];
    }

    /**
     * Get tithe statistics for the current filters
     */
    private function getTitheStatistics($search, $filterActive, $dateRange, $amountRange) {
        $conn = $this->database->getConnection();
        
        // Check schema type
        $hasNewSchema = $this->checkNewSchema($conn);
        $whereClause = $hasNewSchema ? "f.transaction_type = 'income' AND f.income_category = 'tithe'" : "f.category = 'tithe'";

        // Build filters (same as main query)
        $dateFilter = $this->buildDateFilter($dateRange);
        
        $searchFilter = '';
        $searchParams = [];
        if (!empty($search)) {
            $searchFilter = "AND (CONCAT(m.first_name, ' ', m.last_name) LIKE :search 
                           OR m.phone_number LIKE :search 
                           OR m.email LIKE :search)";
            $searchParams[':search'] = "%$search%";
        }

        $activityFilter = '';
        if ($filterActive === 'active') {
            $activityFilter = "AND f.transaction_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH)";
        } elseif ($filterActive === 'inactive') {
            $activityFilter = "AND f.transaction_date < DATE_SUB(NOW(), INTERVAL 3 MONTH)";
        }

        $query = "SELECT 
                    COUNT(DISTINCT m.id) as total_members,
                    COUNT(f.id) as total_payments,
                    SUM(f.amount) as total_amount,
                    AVG(f.amount) as average_payment,
                    MAX(f.amount) as highest_payment,
                    MIN(f.amount) as lowest_payment
                  FROM members m
                  INNER JOIN finances f ON m.id = f.member_id 
                  WHERE $whereClause $dateFilter $activityFilter $searchFilter";

        $stmt = $conn->prepare($query);
        foreach ($searchParams as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get member payment history via AJAX
     */
    public function getMemberHistory() {
        try {
            $memberId = $_GET['member_id'] ?? '';
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 10; // Payments per page

            if (empty($memberId) || !is_numeric($memberId)) {
                echo json_encode(['success' => false, 'message' => 'Invalid member ID']);
                return;
            }

            $conn = $this->database->getConnection();
            $offset = ($page - 1) * $limit;

            // Check schema type
            $hasNewSchema = $this->checkNewSchema($conn);
            $whereClause = $hasNewSchema ? "f.transaction_type = 'income' AND f.income_category = 'tithe'" : "f.category = 'tithe'";

            // Get payments with pagination
            $query = "SELECT 
                        f.id,
                        f.amount,
                        f.description,
                        f.transaction_date,
                        f.created_at,
                        DATE_FORMAT(f.transaction_date, '%M %d, %Y') as formatted_date,
                        DATE_FORMAT(f.transaction_date, '%h:%i %p') as formatted_time
                      FROM finances f
                      WHERE $whereClause AND f.member_id = :member_id
                      ORDER BY f.transaction_date DESC, f.created_at DESC
                      LIMIT :limit OFFSET :offset";

            $stmt = $conn->prepare($query);
            $stmt->bindValue(':member_id', $memberId, PDO::PARAM_INT);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();

            $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get total count
            $countQuery = "SELECT COUNT(*) as total FROM finances f WHERE $whereClause AND f.member_id = :member_id";
            $countStmt = $conn->prepare($countQuery);
            $countStmt->bindValue(':member_id', $memberId, PDO::PARAM_INT);
            $countStmt->execute();
            $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            echo json_encode([
                'success' => true,
                'payments' => $payments,
                'total' => $total,
                'page' => $page,
                'totalPages' => ceil($total / $limit),
                'hasMore' => ($page * $limit) < $total
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false, 
                'message' => 'Error loading payment history: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export members data
     */
    public function export() {
        try {
            // Get all members (no pagination for export)
            $search = trim($_GET['search'] ?? '');
            $sortBy = $_GET['sort'] ?? 'total_amount_desc';
            $filterActive = $_GET['active'] ?? 'all';
            $dateRange = $_GET['date_range'] ?? 'all_time';
            $amountRange = $_GET['amount_range'] ?? 'all';

            $membersData = $this->getMembersWithFilters(1, 10000, $search, $sortBy, $filterActive, $dateRange, $amountRange);
            $members = $membersData['members'];

            // Set headers for CSV download
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="tithe_members_' . date('Y-m-d') . '.csv"');

            $output = fopen('php://output', 'w');

            // CSV headers
            fputcsv($output, [
                'Name', 'Phone', 'Email', 'Total Payments', 'Total Amount (GH₵)', 
                'Average Amount (GH₵)', 'First Payment', 'Last Payment', 'Days Since Last Payment'
            ]);

            // CSV data
            foreach ($members as $member) {
                fputcsv($output, [
                    $member['first_name'] . ' ' . $member['last_name'],
                    $member['phone_number'],
                    $member['email'],
                    $member['total_payments'],
                    number_format($member['total_amount'], 2),
                    number_format($member['average_amount'], 2),
                    $member['first_payment_date'],
                    $member['last_payment_date'],
                    $member['days_since_last_payment']
                ]);
            }

            fclose($output);

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error exporting data: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finance/tithe/members');
        }
    }

    /**
     * Helper methods
     */
    private function checkNewSchema($conn) {
        $checkNewSchema = "SHOW COLUMNS FROM finances WHERE Field IN ('transaction_type', 'income_category')";
        $stmt = $conn->prepare($checkNewSchema);
        $stmt->execute();
        return count($stmt->fetchAll(PDO::FETCH_ASSOC)) >= 2;
    }

    private function buildDateFilter($dateRange) {
        switch ($dateRange) {
            case 'this_month':
                return "AND DATE_FORMAT(f.transaction_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')";
            case 'last_3_months':
                return "AND f.transaction_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH)";
            case 'this_year':
                return "AND YEAR(f.transaction_date) = YEAR(NOW())";
            case 'last_year':
                return "AND YEAR(f.transaction_date) = YEAR(NOW()) - 1";
            default:
                return '';
        }
    }

    private function buildAmountFilter($amountRange) {
        switch ($amountRange) {
            case 'under_100':
                return "HAVING total_amount < 100";
            case '100_500':
                return "HAVING total_amount BETWEEN 100 AND 500";
            case '500_1000':
                return "HAVING total_amount BETWEEN 500 AND 1000";
            case 'over_1000':
                return "HAVING total_amount > 1000";
            default:
                return '';
        }
    }

    private function buildSortClause($sortBy) {
        switch ($sortBy) {
            case 'name_asc':
                return "ORDER BY m.first_name ASC, m.last_name ASC";
            case 'name_desc':
                return "ORDER BY m.first_name DESC, m.last_name DESC";
            case 'total_amount_asc':
                return "ORDER BY total_amount ASC";
            case 'total_amount_desc':
                return "ORDER BY total_amount DESC";
            case 'payments_asc':
                return "ORDER BY total_payments ASC";
            case 'payments_desc':
                return "ORDER BY total_payments DESC";
            case 'recent':
                return "ORDER BY last_payment_date DESC";
            case 'oldest':
                return "ORDER BY first_payment_date ASC";
            default:
                return "ORDER BY total_amount DESC";
        }
    }
}
