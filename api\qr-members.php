<?php
/**
 * QR Members Search API
 * Simplified API for QR attendance member search
 */

// Include bootstrap to ensure proper initialization
require_once dirname(__DIR__) . '/bootstrap.php';

// Set headers for JSON response
header('Content-Type: application/json');

// CRITICAL SECURITY FIX: Restrict CORS to application domain only
// The wildcard '*' is dangerous and allows any website to access this API
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false ||
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
    // If origin is not allowed, we don't set the CORS header
    // This will cause the browser to block the request
}

header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Credentials: true');

try {
    // Initialize database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get search parameter
    $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    
    if (empty($search) || strlen($search) < 2) {
        echo json_encode([
            'success' => false,
            'message' => 'Search term must be at least 2 characters',
            'members' => []
        ]);
        exit;
    }
    
    // Search for members
    $query = "SELECT id, first_name, last_name, email, phone_number, profile_picture
              FROM members
              WHERE (first_name LIKE :search
                     OR last_name LIKE :search
                     OR phone_number LIKE :search
                     OR email LIKE :search)
              AND member_status = 'active'
              ORDER BY first_name, last_name
              LIMIT 20";
    
    $stmt = $conn->prepare($query);
    $searchParam = "%{$search}%";
    $stmt->bindParam(':search', $searchParam);
    $stmt->execute();
    
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format response for QR scan page
    echo json_encode([
        'success' => true,
        'members' => $members,
        'count' => count($members)
    ]);
    
} catch (Exception $e) {
    error_log("QR Members API Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Search failed. Please try again.',
        'members' => []
    ]);
}
?>
