<?php
require_once 'models/WelfareModel.php';
require_once 'models/Member.php';
require_once 'helpers/functions.php';
require_once 'controllers/BaseRestfulController.php';

class WelfareController extends BaseRestfulController {
    private $welfareModel;
    private $memberModel;

    public function __construct() {
        $this->welfareModel = new WelfareModel();

        // Initialize database connection for Member model
        $database = new Database();
        $this->memberModel = new Member($database->getConnection());
    }

    public function index() {
        try {
            // Get welfare statistics
            $stats = $this->welfareModel->getWelfareStats();

            // Recent payments section removed - now using monthly overview instead

            // Get welfare categories
            $categories = $this->welfareModel->getActiveCategories();

            // Get monthly welfare data for chart
            $monthlyData = $this->welfareModel->getMonthlyWelfareData();

            // Get top recipients
            $topRecipients = $this->welfareModel->getTopRecipients(5);

            // Get monthly welfare data
            $monthlySettings = $this->welfareModel->getMonthlySettings();
            $membersWhoPaid = $this->welfareModel->getMembersWhoPayedThisMonth(20); // Show more members on homepage
            $membersWhoClaimed = $this->welfareModel->getMembersWhoClaimedThisMonth(20); // Show more members on homepage

            // Get total counts for "View All" links
            $totalPaidCount = $this->welfareModel->getMembersWhoPayedThisMonthCount();
            $totalClaimedCount = $this->welfareModel->getMembersWhoClaimedThisMonthCount();

            // Get welfare fund balance
            $welfareFunds = $this->welfareModel->getAvailableWelfareFunds();

        } catch (Exception $e) {
            error_log("Error in WelfareController::index: " . $e->getMessage());
            $_SESSION['error'] = "Error loading welfare dashboard: " . $e->getMessage();
            header('Location: ' . BASE_URL . 'dashboard');
            exit;
        }

        // Start output buffering
        ob_start();

        // Include view content
        include 'views/welfare/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    public function paymentsThisMonth() {
        try {
            $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
            $limit = 50; // Show more items per page

            $result = $this->welfareModel->getMembersWhoPayedThisMonthPaginated($page, $limit);
            $payments = $result['payments'];
            $pagination = [
                'current_page' => $result['current_page'],
                'total_pages' => $result['total_pages'],
                'total_count' => $result['total_count'],
                'per_page' => $result['per_page'],
                'has_next' => $result['has_next'],
                'has_prev' => $result['has_prev']
            ];

        } catch (Exception $e) {
            error_log("Error in WelfareController::paymentsThisMonth: " . $e->getMessage());
            $_SESSION['error'] = "Error loading payments: " . $e->getMessage();
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        // Start output buffering
        ob_start();

        // Include view content
        include 'views/welfare/payments-this-month.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    public function claimsThisMonth() {
        try {
            $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
            $limit = 50; // Show more items per page

            $result = $this->welfareModel->getMembersWhoClaimedThisMonthPaginated($page, $limit);
            $claims = $result['claims'];
            $pagination = [
                'current_page' => $result['current_page'],
                'total_pages' => $result['total_pages'],
                'total_count' => $result['total_count'],
                'per_page' => $result['per_page'],
                'has_next' => $result['has_next'],
                'has_prev' => $result['has_prev']
            ];

        } catch (Exception $e) {
            error_log("Error in WelfareController::claimsThisMonth: " . $e->getMessage());
            $_SESSION['error'] = "Error loading claims: " . $e->getMessage();
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        // Start output buffering
        ob_start();

        // Include view content
        include 'views/welfare/claims-this-month.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Show create welfare claim form (RESTful)
     *
     * @return void
     */
    public function create() {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        $active_page = 'welfare';
        $page_title = 'Add Welfare Payment';

        // Get all members for the dropdown
        try {
            $stmt = $this->memberModel->getAll();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $categories = $this->welfareModel->getActiveCategories();
        } catch (Exception $e) {
            error_log("Error in WelfareController::create: " . $e->getMessage());
            set_flash_message("Error loading add welfare form: " . $e->getMessage(), 'danger');
            redirect('welfare');
            return;
        }

        // Start output buffering
        ob_start();

        // Include view content - use existing add.php file
        include 'views/welfare/add.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Legacy add method (for backward compatibility)
     */
    public function add() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Check if member has already paid this month
                $hasPaidThisMonth = $this->welfareModel->hasMemberPaidThisMonth($_POST['member_id']);

                if ($hasPaidThisMonth) {
                    $_SESSION['error'] = "This member has already paid monthly welfare dues this month.";
                    header('Location: ' . BASE_URL . 'welfare/add');
                    exit;
                }

                // Get monthly welfare category ID
                $monthlyWelfareCategoryId = $this->getMonthlyWelfareCategoryId();

                $data = [
                    'member_id' => $_POST['member_id'],
                    'category_id' => $monthlyWelfareCategoryId,
                    'amount' => $_POST['amount'],
                    'reason' => 'Monthly welfare dues payment for ' . date('F Y'),
                    'payment_date' => $_POST['payment_date'],
                    'payment_method' => $_POST['payment_method'],
                    'reference_number' => $_POST['reference_number'] ?? null,
                    'notes' => $_POST['notes'] ?? null,
                    'approved_by' => $_SESSION['user_id'],
                    'payment_month' => date('n'), // Current month (1-12)
                    'payment_year' => date('Y'),  // Current year
                    'welfare_amount_due' => $_POST['amount'],
                    'payment_status' => 'full'
                ];

                $paymentId = $this->welfareModel->addMonthlyWelfarePayment($data);
                
                if ($paymentId) {
                    $_SESSION['success'] = "Welfare payment recorded successfully!";
                    header('Location: ' . BASE_URL . 'welfare');
                } else {
                    $_SESSION['error'] = "Failed to record welfare payment.";
                    header('Location: ' . BASE_URL . 'welfare/add');
                }
            } catch (Exception $e) {
                error_log("Error adding welfare payment: " . $e->getMessage());
                $_SESSION['error'] = "Error recording welfare payment: " . $e->getMessage();
                header('Location: ' . BASE_URL . 'welfare/add');
            }
            exit;
        }

        // GET request - show add form
        try {
            $stmt = $this->memberModel->getAll();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $categories = $this->welfareModel->getActiveCategories();

        } catch (Exception $e) {
            error_log("Error in WelfareController::add: " . $e->getMessage());
            $_SESSION['error'] = "Error loading add welfare form: " . $e->getMessage();
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        // Start output buffering
        ob_start();

        // Include view content
        include 'views/welfare/add.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store welfare payment (RESTful)
     *
     * @return void
     */
    public function store() {
        // Support both RESTful (POST) and legacy methods
        if (!$this->checkHttpMethod(['POST'])) {
            redirect('welfare');
            return;
        }

        if (!$this->validateCsrf('welfare')) {
            return;
        }

        try {
            // Check if member has already paid this month
            $hasPaidThisMonth = $this->welfareModel->hasMemberPaidThisMonth($_POST['member_id']);

            if ($hasPaidThisMonth) {
                $this->handleResponse(false, 'This member has already paid welfare for this month.', 'welfare/create');
                return;
            }

            // Add welfare payment
            $result = $this->welfareModel->addWelfarePayment(
                $_POST['member_id'],
                floatval($_POST['amount']),
                $_POST['payment_method'] ?? 'cash',
                $_POST['notes'] ?? ''
            );

            if ($result) {
                $this->handleResponse(true, 'Welfare payment added successfully!', 'welfare');
            } else {
                $this->handleResponse(false, 'Failed to add welfare payment. Please try again.', 'welfare/create');
            }
        } catch (Exception $e) {
            error_log("Error adding welfare payment: " . $e->getMessage());
            $this->handleResponse(false, 'An error occurred while adding the payment.', 'welfare/create');
        }
    }

    /**
     * Show single welfare record (RESTful)
     *
     * @param int|null $id Welfare ID from route parameter
     * @return void
     */
    public function show($id = null) {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        $welfare_id = $this->getId($id, 'id', 'id');
        if (!$welfare_id) {
            $this->handleResponse(false, 'Invalid welfare ID', 'welfare');
            return;
        }

        // Get welfare record (could be payment or claim)
        $welfare = $this->welfareModel->getById($welfare_id);
        if (!$welfare) {
            $this->handleResponse(false, 'Welfare record not found', 'welfare');
            return;
        }

        // For AJAX requests, return JSON
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'data' => $welfare]);
            exit;
        }

        $active_page = 'welfare';
        $page_title = 'View Welfare Record';

        require_once 'views/welfare/show.php';
    }

    /**
     * Show edit form (RESTful)
     *
     * @param int|null $id Welfare ID from route parameter
     * @return void
     */
    public function edit($id = null) {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        $welfare_id = $this->getId($id, 'id', 'id');
        if (!$welfare_id) {
            $this->handleResponse(false, 'Invalid welfare ID', 'welfare');
            return;
        }

        // Get welfare record
        $welfare = $this->welfareModel->getById($welfare_id);
        if (!$welfare) {
            $this->handleResponse(false, 'Welfare record not found', 'welfare');
            return;
        }

        $active_page = 'welfare';
        $page_title = 'Edit Welfare Record';

        // Get members for dropdown
        $stmt = $this->memberModel->getAll();
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

        require_once 'views/welfare/edit.php';
    }

    /**
     * Update welfare record (RESTful)
     *
     * @param int|null $id Welfare ID from route parameter
     * @return void
     */
    public function update($id = null) {
        // Support both RESTful (PUT) and legacy (POST) methods
        if (!$this->checkHttpMethod(['POST', 'PUT'])) {
            redirect('welfare');
            return;
        }

        if (!$this->validateCsrf('welfare')) {
            return;
        }

        // Get ID from multiple sources
        $welfare_id = $this->getId($id, 'id', 'id');
        if (!$welfare_id) {
            $this->handleResponse(false, 'Welfare ID is required', 'welfare');
            return;
        }

        // Update logic here (delegate to existing methods)
        // For now, redirect to appropriate edit method
        if (isset($_POST['claim_amount'])) {
            return $this->editClaim($welfare_id);
        } else {
            return $this->editPayment($welfare_id);
        }
    }

    /**
     * Delete welfare record (RESTful)
     *
     * @param int|null $id Welfare ID from route parameter
     * @return void
     */
    public function delete($id = null) {
        // Support both RESTful (DELETE) and legacy (POST) methods
        if (!$this->checkHttpMethod(['POST', 'DELETE'])) {
            redirect('welfare');
            return;
        }

        if (!$this->validateCsrf('welfare')) {
            return;
        }

        // Get ID from multiple sources
        $welfare_id = $this->getId($id, 'id', 'id');
        if (!$welfare_id) {
            $this->handleResponse(false, 'Welfare ID is required', 'welfare');
            return;
        }

        // Delete logic here (delegate to existing methods)
        // Determine if it's a payment or claim and call appropriate method
        $welfare = $this->welfareModel->getById($welfare_id);
        if ($welfare) {
            if (isset($welfare['claim_amount'])) {
                return $this->deleteClaim($welfare_id);
            } else {
                return $this->deletePayment($welfare_id);
            }
        }

        $this->handleResponse(false, 'Welfare record not found', 'welfare');
    }

    /**
     * Show claim form (for /welfare/claim GET requests)
     */
    public function claimForm() {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        $active_page = 'welfare';
        $page_title = 'Claim Welfare';

        try {
            // Get all active members for the dropdown
            $stmt = $this->memberModel->getAll();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Filter only active members
            $members = array_filter($members, function($member) {
                return $member['member_status'] === 'active';
            });

            // Get welfare categories
            $categories = $this->welfareModel->getActiveCategories();

            // Get welfare funds (required by the view)
            $welfareFunds = $this->welfareModel->getAvailableWelfareFunds();
        } catch (Exception $e) {
            error_log("Error in WelfareController::claimForm: " . $e->getMessage());
            set_flash_message("Error loading claim form: " . $e->getMessage(), 'danger');
            redirect('welfare');
            return;
        }

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/welfare/claim.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Legacy claim method (for backward compatibility)
     */
    public function claim() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Prepare data for model (model will handle all validation)
                $data = [
                    'member_id' => $_POST['member_id'],
                    'claim_month' => date('n'), // Current month (1-12)
                    'claim_year' => date('Y'),  // Current year
                    'claim_amount' => floatval($_POST['claim_amount']),
                    'claim_reason' => $_POST['claim_reason'],
                    'claim_date' => $_POST['claim_date'],
                    'approved_by' => $_SESSION['user_id'],
                    'status' => 'disbursed', // Auto-approve and disburse
                    'notes' => $_POST['notes'] ?? null
                ];

                // Let model handle all business logic and validation
                $claimId = $this->welfareModel->addWelfareClaim($data);

                if ($claimId) {
                    $newBalance = $this->welfareModel->getAvailableWelfareFunds();
                    $successMessage = "Welfare claim of ₵" . number_format($data['claim_amount'], 2) . " recorded successfully! " .
                                      "Remaining welfare fund balance: ₵" . number_format($newBalance['available_balance'], 2);
                    set_flash_message($successMessage, 'success');
                    redirect('welfare');
                } else {
                    // Get detailed error message from model
                    $errorMessage = $this->welfareModel->error ?? 'Failed to record welfare claim.';
                    set_flash_message($errorMessage, 'danger');
                    redirect('welfare/claim');
                }
            } catch (Exception $e) {
                error_log("Error adding welfare claim: " . $e->getMessage());
                set_flash_message("Error recording welfare claim: " . $e->getMessage(), 'danger');
                redirect('welfare/claim');
            }
            exit;
        }

        // GET request - show claim form
        try {
            $stmt = $this->memberModel->getAll();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get available welfare funds
            $welfareFunds = $this->welfareModel->getAvailableWelfareFunds();

        } catch (Exception $e) {
            error_log("Error in WelfareController::claim: " . $e->getMessage());
            $_SESSION['error'] = "Error loading welfare claim form: " . $e->getMessage();
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        // Start output buffering
        ob_start();

        // Include view content
        include 'views/welfare/claim.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    public function history($memberId = null) {



        if ($memberId) {
            // INDIVIDUAL MEMBER HISTORY - COMPLETELY REWRITTEN

            // Get member data with direct query
            $member = null;
            try {
                $sql = "SELECT * FROM members WHERE id = " . intval($memberId);
                $result = $this->welfareModel->getConnection()->query($sql);
                if ($result) {
                    $member = $result->fetch(PDO::FETCH_ASSOC);
                }
            } catch (Exception $e) {
                error_log("Error getting member: " . $e->getMessage());
            }

            if (!$member) {
                $_SESSION['error'] = "Member not found.";
                header('Location: ' . BASE_URL . 'welfare/history');
                exit;
            }

            // Get member payments/claims with new method
            $payments = [];
            try {
                $payments = $this->welfareModel->getMemberPayments($memberId);
            } catch (Exception $e) {
                error_log("Error getting member payments: " . $e->getMessage());
                $payments = [];
            }

            // Get member stats
            $member_stats = [
                'total_paid' => 0,
                'total_claimed' => 0,
                'payment_count' => 0,
                'claim_count' => 0,
                'last_activity' => null
            ];

            try {
                $member_stats = $this->welfareModel->getMemberStats($memberId);
            } catch (Exception $e) {
                error_log("Error getting member stats: " . $e->getMessage());
            }

            // Debug output if requested
            if (isset($_GET['debug'])) {
                echo "<h2>FINAL DEBUG - Member ID: $memberId</h2>";
                echo "<p>Member found: " . ($member ? 'YES' : 'NO') . "</p>";
                echo "<p>Payments count: " . count($payments) . "</p>";
                echo "<p>Member stats payment_count: " . $member_stats['payment_count'] . "</p>";
                echo "<h3>Payments Array:</h3><pre>";
                print_r($payments);
                echo "</pre>";
                exit;
            }

            // Load individual member view
            ob_start();
            include 'views/welfare/member_history.php';
            $content = ob_get_clean();

        } else {
            // ALL MEMBERS HISTORY WITH PAGINATION AND FILTERING

            // Set page variables for layout
            $page_title = getPageTitle("Member Welfare History");
            $active_page = "welfare";

            // Get pagination and filter parameters
            $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
            $limit = isset($_GET['limit']) ? max(10, min(1000, intval($_GET['limit']))) : 50;
            $search = isset($_GET['search']) ? trim($_GET['search']) : '';
            $filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';

            // Get paginated results
            $welfare_data = [];
            try {
                $welfare_data = $this->welfareModel->getMembersWithWelfare($page, $limit, $search, $filter);
                $members_with_welfare = $welfare_data['members'] ?? [];
                $pagination = [
                    'current_page' => $welfare_data['current_page'] ?? 1,
                    'total_pages' => $welfare_data['total_pages'] ?? 1,
                    'per_page' => $welfare_data['per_page'] ?? 20,
                    'total_count' => $welfare_data['total_count'] ?? 0,
                    'has_next' => $welfare_data['has_next'] ?? false,
                    'has_prev' => $welfare_data['has_prev'] ?? false
                ];
            } catch (Exception $e) {
                error_log("Error getting members with welfare: " . $e->getMessage());
                $members_with_welfare = [];
                $pagination = [
                    'current_page' => 1,
                    'total_pages' => 1,
                    'per_page' => 20,
                    'total_count' => 0,
                    'has_next' => false,
                    'has_prev' => false
                ];
            }

            // Get summary statistics for current filter
            $summary_stats = [];
            try {
                $summary_stats = $this->welfareModel->getWelfareSummaryStats($search, $filter);
            } catch (Exception $e) {
                error_log("Error getting summary stats: " . $e->getMessage());
                $summary_stats = [
                    'total_members' => 0,
                    'total_payments' => 0,
                    'total_paid_amount' => 0,
                    'total_claims' => 0,
                    'total_claimed_amount' => 0
                ];
            }

            // Current filter values for form
            $current_filters = [
                'search' => $search,
                'filter' => $filter,
                'limit' => $limit
            ];

            // Load main history view
            ob_start();
            include 'views/welfare/history.php';
            $content = ob_get_clean();
        }



        // Include the layout template
        include 'views/layouts/main.php';
    }

    // AJAX endpoint for real-time member search - COMPLETELY REWRITTEN
    public function search() {
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, must-revalidate');

        try {
            // Get and validate search parameters
            $search = isset($_GET['q']) ? trim($_GET['q']) : '';
            $filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';
            $limit = isset($_GET['limit']) ? min(50, max(5, intval($_GET['limit']))) : 10;



            // Validate search query length
            if (strlen($search) < 2) {
                echo json_encode([
                    'success' => true,
                    'members' => [],
                    'total_found' => 0,
                    'showing' => 0,
                    'message' => 'Type at least 2 characters to search'
                ]);
                exit;
            }

            // Get search results using the model
            $results = $this->welfareModel->searchMembersWithWelfare($search, $filter, $limit);

            if (!$results || !isset($results['members'])) {
                echo json_encode([
                    'success' => false,
                    'error' => 'Search failed. Please try again.'
                ]);
                exit;
            }

            $members = $results['members'];

            // Format results for JSON response
            $formattedMembers = [];
            foreach ($members as $member) {
                $formattedMembers[] = [
                    'id' => intval($member['id']),
                    'name' => trim($member['first_name'] . ' ' . $member['last_name']),
                    'phone' => $member['phone_number'] ?? '',
                    'payment_count' => intval($member['payment_count'] ?? 0),
                    'total_paid' => number_format(floatval($member['total_paid'] ?? 0), 2),
                    'claim_count' => intval($member['claim_count'] ?? 0),
                    'total_claimed' => number_format(floatval($member['total_claimed'] ?? 0), 2),
                    'last_activity' => $member['last_activity_date'] ?? '',
                    'url' => BASE_URL . 'welfare/history/' . $member['id']
                ];
            }

            echo json_encode([
                'success' => true,
                'members' => $formattedMembers,
                'total_found' => intval($results['total_count'] ?? 0),
                'showing' => count($formattedMembers),
                'search_term' => $search,
                'filter' => $filter
            ]);

        } catch (Exception $e) {
            error_log("Error in welfare search: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'error' => 'Search failed. Please try again.',
                'debug' => $e->getMessage()
            ]);
        }
        exit;
    }

    public function categories() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $action = $_POST['action'];
                
                if ($action === 'add') {
                    $data = [
                        'name' => $_POST['name'],
                        'description' => $_POST['description']
                    ];
                    
                    $this->welfareModel->addCategory($data);
                    $_SESSION['success'] = "Welfare category added successfully!";
                } elseif ($action === 'update') {
                    $data = [
                        'id' => $_POST['id'],
                        'name' => $_POST['name'],
                        'description' => $_POST['description'],
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];
                    
                    $this->welfareModel->updateCategory($data);
                    $_SESSION['success'] = "Welfare category updated successfully!";
                } elseif ($action === 'delete') {
                    $this->welfareModel->deleteCategory($_POST['id']);
                    $_SESSION['success'] = "Welfare category deleted successfully!";
                }
                
                header('Location: ' . BASE_URL . 'welfare/categories');
            } catch (Exception $e) {
                error_log("Error managing welfare categories: " . $e->getMessage());
                $_SESSION['error'] = "Error managing welfare categories: " . $e->getMessage();
                header('Location: ' . BASE_URL . 'welfare/categories');
            }
            exit;
        }

        // GET request - show categories
        try {
            $categories = $this->welfareModel->getAllCategories();
        } catch (Exception $e) {
            error_log("Error in WelfareController::categories: " . $e->getMessage());
            $_SESSION['error'] = "Error loading welfare categories: " . $e->getMessage();
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        // Start output buffering
        ob_start();

        // Include view content
        include 'views/welfare/categories.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    public function reports() {
        try {
            $year = $_GET['year'] ?? date('Y');

            // Get welfare reports data
            $yearlyStats = $this->welfareModel->getYearlyStats($year);
            $monthlyTrends = $this->welfareModel->getMonthlyTrends($year);
            $topRecipients = $this->welfareModel->getTopRecipients(10, $year);

            // Make variables available for the view
            $yearly_stats = $yearlyStats;
            $monthly_trends = $monthlyTrends;
            $top_recipients = $topRecipients;
            $selected_year = $year;

        } catch (Exception $e) {
            error_log("Error in WelfareController::reports: " . $e->getMessage());
            $_SESSION['error'] = "Error loading welfare reports: " . $e->getMessage();
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        // Set page variables for layout
        $page_title = getPageTitle("Welfare Reports");
        $active_page = "welfare";

        // Start output buffering
        ob_start();

        // Include view content
        include 'views/welfare/reports.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    public function updatePayment() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $data = [
                    'id' => $_POST['id'],
                    'status' => $_POST['status'],
                    'notes' => $_POST['notes'] ?? null
                ];

                $this->welfareModel->updatePaymentStatus($data);
                $_SESSION['success'] = "Payment status updated successfully!";
                
                header('Location: ' . BASE_URL . 'welfare');
            } catch (Exception $e) {
                error_log("Error updating payment status: " . $e->getMessage());
                $_SESSION['error'] = "Error updating payment status: " . $e->getMessage();
                header('Location: ' . BASE_URL . 'welfare');
            }
            exit;
        }
    }

    public function deletePayment($paymentId = null) {
        // Handle both URL parameter and POST data
        if ($paymentId === null && $_SERVER['REQUEST_METHOD'] === 'POST') {
            $paymentId = $_POST['payment_id'] ?? null;
        }

        if (!$paymentId) {
            $_SESSION['error'] = "Payment ID is required.";
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $result = $this->welfareModel->deletePayment($paymentId);
                if ($result) {
                    $_SESSION['success'] = "Welfare payment deleted successfully!";
                } else {
                    $_SESSION['error'] = "Failed to delete welfare payment.";
                }
                header('Location: ' . BASE_URL . 'welfare');
            } catch (Exception $e) {
                error_log("Error deleting welfare payment: " . $e->getMessage());
                $_SESSION['error'] = "Error deleting welfare payment: " . $e->getMessage();
                header('Location: ' . BASE_URL . 'welfare');
            }
            exit;
        } else {
            // GET request - show confirmation page
            try {
                $payment = $this->welfareModel->getPaymentById($paymentId);
                if (!$payment) {
                    $_SESSION['error'] = "Payment not found.";
                    header('Location: ' . BASE_URL . 'welfare');
                    exit;
                }

                // Start output buffering
                ob_start();

                // Include confirmation view
                include 'views/welfare/delete-payment-confirm.php';

                // Get the contents of the output buffer
                $content = ob_get_clean();

                // Include the layout template
                include 'views/layouts/main.php';

            } catch (Exception $e) {
                error_log("Error loading payment for deletion: " . $e->getMessage());
                $_SESSION['error'] = "Error loading payment: " . $e->getMessage();
                header('Location: ' . BASE_URL . 'welfare');
                exit;
            }
        }
    }

    public function deleteClaim($claimId = null) {
        // Handle both URL parameter and POST data
        if ($claimId === null && $_SERVER['REQUEST_METHOD'] === 'POST') {
            $claimId = $_POST['claim_id'] ?? null;
        }

        if (!$claimId) {
            $_SESSION['error'] = "Claim ID is required.";
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $result = $this->welfareModel->deleteClaim($claimId);
                if ($result) {
                    $_SESSION['success'] = "Welfare claim deleted successfully!";
                } else {
                    $_SESSION['error'] = "Failed to delete welfare claim.";
                }
                header('Location: ' . BASE_URL . 'welfare');
            } catch (Exception $e) {
                error_log("Error deleting welfare claim: " . $e->getMessage());
                $_SESSION['error'] = "Error deleting welfare claim: " . $e->getMessage();
                header('Location: ' . BASE_URL . 'welfare');
            }
            exit;
        } else {
            // GET request - show confirmation page
            try {
                $claim = $this->welfareModel->getClaimById($claimId);
                if (!$claim) {
                    $_SESSION['error'] = "Claim not found.";
                    header('Location: ' . BASE_URL . 'welfare');
                    exit;
                }

                // Start output buffering
                ob_start();

                // Include confirmation view
                include 'views/welfare/delete-claim-confirm.php';

                // Get the contents of the output buffer
                $content = ob_get_clean();

                // Include the layout template
                include 'views/layouts/main.php';

            } catch (Exception $e) {
                error_log("Error loading claim for deletion: " . $e->getMessage());
                $_SESSION['error'] = "Error loading claim: " . $e->getMessage();
                header('Location: ' . BASE_URL . 'welfare');
                exit;
            }
        }
    }

    public function editPayment($paymentId = null) {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $data = [
                    'member_id' => $_POST['member_id'],
                    'amount' => $_POST['amount'],
                    'payment_date' => $_POST['payment_date'],
                    'payment_method' => $_POST['payment_method'],
                    'notes' => $_POST['notes'] ?? ''
                ];

                $this->welfareModel->updatePayment($_POST['payment_id'], $data);
                $_SESSION['success'] = "Welfare payment updated successfully!";

                header('Location: ' . BASE_URL . 'welfare');
            } catch (Exception $e) {
                error_log("Error updating welfare payment: " . $e->getMessage());
                $_SESSION['error'] = "Error updating welfare payment: " . $e->getMessage();
                header('Location: ' . BASE_URL . 'welfare/edit-payment/' . $_POST['payment_id']);
            }
            exit;
        }

        // GET request - show edit form
        try {
            if (!$paymentId) {
                $_SESSION['error'] = "Payment ID is required.";
                header('Location: ' . BASE_URL . 'welfare');
                exit;
            }

            $payment = $this->welfareModel->getPaymentById($paymentId);
            if (!$payment) {
                $_SESSION['error'] = "Welfare payment not found with ID: $paymentId. Please create a welfare payment first or use a valid payment ID.";
                header('Location: ' . BASE_URL . 'welfare');
                exit;
            }

            $stmt = $this->memberModel->getAll();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error in WelfareController::editPayment: " . $e->getMessage());
            $_SESSION['error'] = "Error loading payment edit form: " . $e->getMessage();
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        // Set page variables for layout
        $page_title = getPageTitle("Edit Welfare Payment");
        $active_page = "welfare";

        // Start output buffering
        ob_start();

        // Include view content
        include 'views/welfare/edit_payment.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    public function editClaim($claimId = null) {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $data = [
                    'member_id' => $_POST['member_id'],
                    'claim_amount' => $_POST['claim_amount'],
                    'claim_reason' => $_POST['claim_reason'],
                    'claim_date' => $_POST['claim_date'],
                    'status' => $_POST['status'],
                    'notes' => $_POST['notes'] ?? ''
                ];

                $this->welfareModel->updateClaim($_POST['claim_id'], $data);
                $_SESSION['success'] = "Welfare claim updated successfully!";

                header('Location: ' . BASE_URL . 'welfare');
            } catch (Exception $e) {
                error_log("Error updating welfare claim: " . $e->getMessage());
                $_SESSION['error'] = "Error updating welfare claim: " . $e->getMessage();
                header('Location: ' . BASE_URL . 'welfare/edit-claim/' . $_POST['claim_id']);
            }
            exit;
        }

        // GET request - show edit form
        try {
            if (!$claimId) {
                $_SESSION['error'] = "Claim ID is required.";
                header('Location: ' . BASE_URL . 'welfare');
                exit;
            }

            $claim = $this->welfareModel->getClaimById($claimId);
            if (!$claim) {
                $_SESSION['error'] = "Welfare claim not found with ID: $claimId. Please create a welfare claim first or use a valid claim ID.";
                header('Location: ' . BASE_URL . 'welfare');
                exit;
            }

            $stmt = $this->memberModel->getAll();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error in WelfareController::editClaim: " . $e->getMessage());
            $_SESSION['error'] = "Error loading claim edit form: " . $e->getMessage();
            header('Location: ' . BASE_URL . 'welfare');
            exit;
        }

        // Set page variables for layout
        $page_title = getPageTitle("Edit Welfare Claim");
        $active_page = "welfare";

        // Start output buffering
        ob_start();

        // Include view content
        include 'views/welfare/edit_claim.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    // Helper method to get member data
    private function getMemberData($memberId) {
        try {
            $query = "SELECT * FROM members WHERE id = :id LIMIT 1";
            $stmt = $this->welfareModel->getConnection()->prepare($query);
            $stmt->bindParam(':id', $memberId);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting member data: " . $e->getMessage());
            return false;
        }
    }

    // Helper method to get monthly welfare category ID
    private function getMonthlyWelfareCategoryId() {
        try {
            $query = "SELECT id FROM welfare_categories WHERE name = 'Monthly Welfare Dues' LIMIT 1";
            $stmt = $this->welfareModel->getConnection()->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                return $result['id'];
            }

            // If category doesn't exist, create it
            $insertQuery = "INSERT INTO welfare_categories (name, description) VALUES ('Monthly Welfare Dues', 'Regular monthly welfare contributions from members')";
            $insertStmt = $this->welfareModel->getConnection()->prepare($insertQuery);
            $insertStmt->execute();
            return $this->welfareModel->getConnection()->lastInsertId();

        } catch (Exception $e) {
            error_log("Error getting monthly welfare category ID: " . $e->getMessage());
            return 1; // Default to first category
        }
    }

    /**
     * Show welfare history for a specific member
     *
     * @param int $memberId Member ID from URL parameter
     */
    public function memberHistory($memberId = null) {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        // Get member ID from URL parameter
        if ($memberId === null) {
            // Extract from URL if not passed as parameter
            $uri = $_SERVER['REQUEST_URI'];
            if (preg_match('/welfare\/history\/(\d+)/', $uri, $matches)) {
                $memberId = (int)$matches[1];
            } else {
                set_flash_message('Invalid member ID', 'danger');
                redirect('welfare/history');
                return;
            }
        }

        $active_page = 'welfare';
        $page_title = 'Member Welfare History';

        try {
            // Get member details
            $memberExists = $this->memberModel->getById($memberId);
            if (!$memberExists) {
                set_flash_message('Member not found', 'danger');
                redirect('welfare/history');
                return;
            }

            // Get member data from the model (getById populates the model properties)
            $member = [
                'id' => $this->memberModel->id,
                'first_name' => $this->memberModel->first_name,
                'last_name' => $this->memberModel->last_name,
                'phone_number' => $this->memberModel->phone_number,
                'email' => $this->memberModel->email,
                'member_status' => $this->memberModel->member_status
            ];

            // Get member's welfare history (both payments and claims)
            $payments = $this->welfareModel->getMemberWelfareHistory($memberId);

            // Get member's welfare statistics
            $member_stats = $this->welfareModel->getMemberWelfareStats($memberId);

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/welfare/member_history.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("Error in WelfareController::memberHistory: " . $e->getMessage());
            set_flash_message("Error loading member welfare history: " . $e->getMessage(), 'danger');
            redirect('welfare/history');
            return;
        }
    }
}
