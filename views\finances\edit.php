<div class="fade-in mx-auto" style="width: 85%; max-width: 1200px;">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Edit Financial Transaction</h1>
            <p class="text-gray-600 mt-1">Update transaction details for ICGC Emmanuel Temple</p>
            <div class="mt-2 text-sm text-gray-500">
                <span class="font-medium">Transaction ID:</span> #<?php echo $this->finance->id; ?>
            </div>
        </div>
        <a href="<?php echo BASE_URL . $categoryDashboardUrl; ?>" class="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 font-medium">
            ← Back to Dashboard
        </a>
    </div>

    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-50 border-l-4 border-red-400 text-red-800 p-5 rounded-r-lg mb-8 shadow-sm">
            <div class="flex items-center mb-3">
                <h4 class="font-semibold text-red-900">Please fix the following errors:</h4>
            </div>
            <ul class="list-disc ml-6 space-y-1">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li class="text-red-700"><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
        <!-- Enhanced Tab Navigation -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-1 rounded-t-xl">
            <nav class="flex space-x-1">
                <button type="button" id="tab-member-payments" class="tab-button flex-1 py-4 px-6 text-sm font-semibold rounded-lg text-gray-600 hover:text-green-700 hover:bg-green-50 transition-all duration-200" data-tab="member-payments">
                    Member Payments
                </button>
                <button type="button" id="tab-general-income" class="tab-button flex-1 py-4 px-6 text-sm font-medium rounded-lg text-gray-600 hover:text-green-700 hover:bg-green-50 transition-all duration-200" data-tab="general-income">
                    General Income
                </button>
                <button type="button" id="tab-expenses" class="tab-button flex-1 py-4 px-6 text-sm font-medium rounded-lg text-gray-600 hover:text-red-700 hover:bg-red-50 transition-all duration-200" data-tab="expenses">
                    Expenses
                </button>
            </nav>
        </div>

        <!-- Form Content Area -->
        <div class="p-8">

            <form action="<?php echo BASE_URL; ?>finance/update" method="POST" id="financeForm">
                <input type="hidden" name="id" value="<?php echo $this->finance->id; ?>">

                <!-- Dynamic transaction type field -->
                <input type="hidden" name="transaction_type" id="transaction_type" value="<?php echo ($this->finance->category == 'expense') ? 'expense' : 'income'; ?>">

                <!-- Global member_id field - accessible from all tabs -->
                <input type="hidden" name="member_id" id="global-member-id" value="<?php echo $this->finance->member_id ?? ''; ?>">

                <?php
                // Load categories from database
                require_once 'models/CustomFinanceCategory.php';
                $database = new Database();
                $customCategory = new CustomFinanceCategory($database->getConnection());

                $memberPaymentCategories = $customCategory->getByType('member_payments');
                $generalIncomeCategories = $customCategory->getByType('general_income');
                $expenseCategories = $customCategory->getByType('expenses');

                // Determine which tab should be active based on current category
                $currentCategory = $this->finance->category;
                $activeTab = 'member-payments'; // default

                // Check which category type the current transaction belongs to
                foreach ($memberPaymentCategories as $cat) {
                    if ($cat->name === $currentCategory) {
                        $activeTab = 'member-payments';
                        break;
                    }
                }
                foreach ($generalIncomeCategories as $cat) {
                    if ($cat->name === $currentCategory) {
                        $activeTab = 'general-income';
                        break;
                    }
                }
                foreach ($expenseCategories as $cat) {
                    if ($cat->name === $currentCategory) {
                        $activeTab = 'expenses';
                        break;
                    }
                }
                ?>

                <!-- Member Payments Tab -->
                <div id="member-payments-tab" class="tab-content <?php echo $activeTab === 'member-payments' ? '' : 'hidden'; ?>">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Payment Type Selection -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-3">Payment Type</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                <?php foreach ($memberPaymentCategories as $category): ?>
                                <label class="payment-type-option cursor-pointer">
                                    <input type="radio" name="category" value="<?php echo $category->name; ?>"
                                           class="sr-only payment-type-radio"
                                           <?php echo ($this->finance->category == $category->name) ? 'checked' : ''; ?>>
                                    <div class="payment-type-card border-2 border-gray-200 rounded-lg p-4 hover:border-green-300 transition-all duration-200 <?php echo ($this->finance->category == $category->name) ? 'border-green-500 bg-green-50' : ''; ?>">
                                        <div class="text-center">
                                            <div class="text-lg font-medium text-gray-900"><?php echo $category->label; ?></div>
                                        </div>
                                    </div>
                                </label>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Amount -->
                        <div>
                            <label for="member-amount" class="block text-sm font-medium text-gray-700 mb-2">Amount (GH₵)</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 font-medium">₵</span>
                                </div>
                                <input type="number" id="member-amount" name="amount" step="0.01" min="0.01"
                                       value="<?php echo $this->finance->amount; ?>"
                                       class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                                       placeholder="0.00" required>
                            </div>
                        </div>

                        <!-- Transaction Date -->
                        <div>
                            <label for="member-date" class="block text-sm font-medium text-gray-700 mb-2">Transaction Date</label>
                            <input type="date" id="member-date" name="transaction_date"
                                   value="<?php echo $this->finance->transaction_date; ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                                   required>
                        </div>
                    </div>

                        <!-- Member Selection -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Member (Optional for most payments, Required for Tithe)</label>
                            <div class="relative">
                                <input type="text" id="member-search" placeholder="Search member by name..."
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200">

                                <!-- Hidden select with real member data for JavaScript -->
                                <select id="member-data-source" class="hidden">
                                    <option value="">Select member</option>
                                    <?php
                                    require_once 'models/Member.php';
                                    $db = new Database();
                                    $conn = $db->getConnection();
                                    $memberModel = new Member($conn);
                                    $members = $memberModel->getByStatus('active');

                                    foreach ($members as $member_item) :
                                        $memberName = htmlspecialchars($member_item->first_name . ' ' . $member_item->last_name);
                                    ?>
                                    <option value="<?php echo $member_item->id; ?>"
                                            data-name="<?php echo $memberName; ?>"
                                            data-phone="<?php echo htmlspecialchars($member_item->phone_number ?? ''); ?>"
                                            data-email="<?php echo htmlspecialchars($member_item->email ?? ''); ?>">
                                        <?php echo $memberName; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>

                                <div id="member-search-results" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                                    <div id="search-results-list">
                                        <!-- Results populated by JavaScript -->
                                    </div>
                                </div>
                            </div>

                            <div id="selected-member-info" class="<?php echo !empty($this->finance->member_id) ? '' : 'hidden'; ?> mt-3">
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div id="member-details" class="flex items-center">
                                            <?php if (!empty($this->finance->member_id)): ?>
                                            <?php
                                            $currentMember = new Member($conn);
                                            $currentMember->getById($this->finance->member_id);
                                            ?>
                                            <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mr-3 text-white font-bold text-lg">
                                                <?php echo substr($currentMember->first_name, 0, 1); ?>
                                            </div>
                                            <div class="flex-1">
                                                <div class="font-medium text-gray-900"><?php echo $currentMember->first_name . ' ' . $currentMember->last_name; ?></div>
                                                <div class="text-sm text-gray-600"><?php echo $currentMember->phone_number; ?></div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <button type="button" id="change-member" class="text-green-600 hover:text-green-700 text-sm font-medium">
                                            Change
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    <!-- Description -->
                    <div class="mt-6">
                        <label for="member-description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="member-description" name="description" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                                  placeholder="Optional transaction description"><?php echo $this->finance->description; ?></textarea>
                    </div>

                    <!-- Payment Method -->
                    <div class="mt-6">
                        <label for="member-payment-method" class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                        <select id="member-payment-method" name="payment_method"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200">
                            <option value="cash" <?php echo ($this->finance->payment_method == 'cash') ? 'selected' : ''; ?>>Cash</option>
                            <option value="mobile_money" <?php echo ($this->finance->payment_method == 'mobile_money') ? 'selected' : ''; ?>>Mobile Money</option>
                            <option value="bank_transfer" <?php echo ($this->finance->payment_method == 'bank_transfer') ? 'selected' : ''; ?>>Bank Transfer</option>
                            <option value="cheque" <?php echo ($this->finance->payment_method == 'cheque') ? 'selected' : ''; ?>>Cheque</option>
                            <option value="card" <?php echo ($this->finance->payment_method == 'card') ? 'selected' : ''; ?>>Card</option>
                        </select>
                    </div>
                </div>

                <!-- General Income Tab -->
                <div id="general-income-tab" class="tab-content <?php echo $activeTab === 'general-income' ? '' : 'hidden'; ?>">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Income Type Selection -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-3">Income Type</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                <?php foreach ($generalIncomeCategories as $category): ?>
                                <label class="payment-type-option cursor-pointer">
                                    <input type="radio" name="category" value="<?php echo $category->name; ?>"
                                           class="sr-only payment-type-radio"
                                           <?php echo ($this->finance->category == $category->name) ? 'checked' : ''; ?>>
                                    <div class="payment-type-card border-2 border-gray-200 rounded-lg p-4 hover:border-green-300 transition-all duration-200 <?php echo ($this->finance->category == $category->name) ? 'border-green-500 bg-green-50' : ''; ?>">
                                        <div class="text-center">
                                            <div class="text-lg font-medium text-gray-900"><?php echo $category->label; ?></div>
                                        </div>
                                    </div>
                                </label>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Amount -->
                        <div>
                            <label for="income-amount" class="block text-sm font-medium text-gray-700 mb-2">Amount (GH₵)</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 font-medium">₵</span>
                                </div>
                                <input type="number" id="income-amount" name="amount" step="0.01" min="0.01"
                                       value="<?php echo $this->finance->amount; ?>"
                                       class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                                       placeholder="0.00" required>
                            </div>
                        </div>

                        <!-- Transaction Date -->
                        <div>
                            <label for="income-date" class="block text-sm font-medium text-gray-700 mb-2">Transaction Date</label>
                            <input type="date" id="income-date" name="transaction_date"
                                   value="<?php echo $this->finance->transaction_date; ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                                   required>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mt-6">
                        <label for="income-description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="income-description" name="description" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                                  placeholder="Optional transaction description"><?php echo $this->finance->description; ?></textarea>
                    </div>

                    <!-- Payment Method -->
                    <div class="mt-6">
                        <label for="income-payment-method" class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                        <select id="income-payment-method" name="payment_method"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200">
                            <option value="cash" <?php echo ($this->finance->payment_method == 'cash') ? 'selected' : ''; ?>>Cash</option>
                            <option value="mobile_money" <?php echo ($this->finance->payment_method == 'mobile_money') ? 'selected' : ''; ?>>Mobile Money</option>
                            <option value="bank_transfer" <?php echo ($this->finance->payment_method == 'bank_transfer') ? 'selected' : ''; ?>>Bank Transfer</option>
                            <option value="cheque" <?php echo ($this->finance->payment_method == 'cheque') ? 'selected' : ''; ?>>Cheque</option>
                            <option value="card" <?php echo ($this->finance->payment_method == 'card') ? 'selected' : ''; ?>>Card</option>
                        </select>
                    </div>
                </div>

                <!-- Expenses Tab -->
                <div id="expenses-tab" class="tab-content <?php echo $activeTab === 'expenses' ? '' : 'hidden'; ?>">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Expense Type Selection -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-3">Expense Type</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                <?php foreach ($expenseCategories as $category): ?>
                                <label class="payment-type-option cursor-pointer">
                                    <input type="radio" name="category" value="<?php echo $category->name; ?>"
                                           class="sr-only payment-type-radio"
                                           <?php echo ($this->finance->category == $category->name) ? 'checked' : ''; ?>>
                                    <div class="payment-type-card border-2 border-gray-200 rounded-lg p-4 hover:border-red-300 transition-all duration-200 <?php echo ($this->finance->category == $category->name) ? 'border-red-500 bg-red-50' : ''; ?>">
                                        <div class="text-center">
                                            <div class="text-lg font-medium text-gray-900"><?php echo $category->label; ?></div>
                                        </div>
                                    </div>
                                </label>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Amount -->
                        <div>
                            <label for="expense-amount" class="block text-sm font-medium text-gray-700 mb-2">Amount (GH₵)</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 font-medium">₵</span>
                                </div>
                                <input type="number" id="expense-amount" name="amount" step="0.01" min="0.01"
                                       value="<?php echo $this->finance->amount; ?>"
                                       class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                                       placeholder="0.00" required>
                            </div>
                        </div>

                        <!-- Transaction Date -->
                        <div>
                            <label for="expense-date" class="block text-sm font-medium text-gray-700 mb-2">Transaction Date</label>
                            <input type="date" id="expense-date" name="transaction_date"
                                   value="<?php echo $this->finance->transaction_date; ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                                   required>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mt-6">
                        <label for="expense-description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="expense-description" name="description" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                                  placeholder="Optional transaction description"><?php echo $this->finance->description; ?></textarea>
                    </div>

                    <!-- Payment Method -->
                    <div class="mt-6">
                        <label for="expense-payment-method" class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                        <select id="expense-payment-method" name="payment_method"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200">
                            <option value="cash" <?php echo ($this->finance->payment_method == 'cash') ? 'selected' : ''; ?>>Cash</option>
                            <option value="mobile_money" <?php echo ($this->finance->payment_method == 'mobile_money') ? 'selected' : ''; ?>>Mobile Money</option>
                            <option value="bank_transfer" <?php echo ($this->finance->payment_method == 'bank_transfer') ? 'selected' : ''; ?>>Bank Transfer</option>
                            <option value="cheque" <?php echo ($this->finance->payment_method == 'cheque') ? 'selected' : ''; ?>>Cheque</option>
                            <option value="card" <?php echo ($this->finance->payment_method == 'card') ? 'selected' : ''; ?>>Card</option>
                        </select>
                    </div>
                </div>

                <!-- Form Footer with Submit Button -->
                <div class="mt-8 pt-6 border-t border-gray-100 flex justify-between items-center">
                    <a href="<?php echo BASE_URL . $categoryDashboardUrl; ?>" class="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 font-medium">
                        ← Cancel
                    </a>
                    <button type="submit" class="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl">
                        Update Transaction
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Quick Tips -->
<div class="mt-8 bg-green-50 rounded-xl p-6 border border-green-100 shadow-sm">
    <h3 class="text-lg font-semibold text-green-800 mb-2 flex items-center">
        💡 Quick Tips for Editing Transactions
    </h3>
    <ul class="text-green-700 space-y-2">
        <li class="flex items-start">
            <span class="text-green-500 mt-1 mr-2">✓</span>
            <span>Verify the category matches the transaction type before updating.</span>
        </li>
        <li class="flex items-start">
            <span class="text-green-500 mt-1 mr-2">✓</span>
            <span>Double-check the amount and date for accuracy.</span>
        </li>
        <li class="flex items-start">
            <span class="text-green-500 mt-1 mr-2">✓</span>
            <span>Update the description to reflect any changes in purpose or details.</span>
        </li>
    </ul>
</div>

<style>
    .fade-in {
        animation: fadeIn 0.6s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .tab-button {
        transition: all 0.3s ease;
        border: none;
        outline: none;
    }

    .tab-button.active {
        background: white;
        color: #059669;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid #d1fae5;
    }

    .tab-content {
        transition: all 0.3s ease;
    }

    .tab-content.hidden {
        display: none;
    }

    .payment-type-option input:checked + .payment-type-card {
        border-color: #059669;
        background-color: #ecfdf5;
        box-shadow: 0 4px 6px rgba(5, 150, 105, 0.1);
    }

    .payment-type-card {
        transition: all 0.2s ease;
    }

    .payment-type-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    const transactionTypeInput = document.getElementById('transaction_type');

    // Member search functionality
    const memberSearch = document.getElementById('member-search');
    const memberSearchResults = document.getElementById('member-search-results');
    const searchResultsList = document.getElementById('search-results-list');
    const selectedMemberInfo = document.getElementById('selected-member-info');
    const selectedMemberDetails = document.getElementById('member-details');
    const changeMemberBtn = document.getElementById('change-member');
    const selectedMemberIdInput = document.getElementById('global-member-id');
    const memberDataSource = document.getElementById('member-data-source');

    // Set initial active tab based on PHP variable
    const activeTab = '<?php echo $activeTab; ?>';
    showTab(activeTab);

    // Load real member data from PHP
    let allMembers = [];
    if (memberDataSource) {
        const options = memberDataSource.querySelectorAll('option');
        allMembers = Array.from(options)
            .filter(option => option.value !== '')
            .map(option => ({
                id: option.value,
                name: option.dataset.name || option.textContent.trim(),
                phone: option.dataset.phone || '',
                email: option.dataset.email || ''
            }));
    }

    // Set initial member search value if member is already selected
    if (selectedMemberIdInput && selectedMemberIdInput.value) {
        const selectedMember = allMembers.find(member => member.id === selectedMemberIdInput.value);
        if (selectedMember && memberSearch) {
            memberSearch.value = selectedMember.name;
        }
    }

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            showTab(tabName);
        });
    });

    function showTab(tabName) {
        // Update tab buttons
        tabButtons.forEach(btn => {
            btn.classList.remove('active', 'bg-white', 'text-green-700', 'shadow-sm', 'border', 'border-green-200');
            btn.classList.add('text-gray-600', 'hover:text-green-700', 'hover:bg-green-50');
        });

        const activeButton = document.getElementById(`tab-${tabName}`);
        if (activeButton) {
            activeButton.classList.remove('text-gray-600', 'hover:text-green-700', 'hover:bg-green-50');
            activeButton.classList.add('active', 'bg-white', 'text-green-700', 'shadow-sm', 'border', 'border-green-200');
        }

        // Update tab contents
        tabContents.forEach(content => {
            content.classList.add('hidden');
        });

        const activeContent = document.getElementById(`${tabName}-tab`);
        if (activeContent) {
            activeContent.classList.remove('hidden');
        }

        // Update transaction type
        if (tabName === 'expenses') {
            transactionTypeInput.value = 'expense';
        } else {
            transactionTypeInput.value = 'income';
        }

        // Update tab button colors for expenses
        if (tabName === 'expenses') {
            activeButton.classList.remove('text-green-700', 'border-green-200');
            activeButton.classList.add('text-red-700', 'border-red-200');
        }
    }

    // Member search functionality
    if (memberSearch) {
        memberSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();

            if (searchTerm.length === 0) {
                hideSearchResults();
                return;
            }

            const filteredMembers = allMembers.filter(member =>
                member.name.toLowerCase().includes(searchTerm) ||
                member.phone.includes(searchTerm)
            );

            displaySearchResults(filteredMembers);
        });

        memberSearch.addEventListener('focus', function() {
            if (this.value.trim().length > 0) {
                const searchTerm = this.value.toLowerCase().trim();
                const filteredMembers = allMembers.filter(member =>
                    member.name.toLowerCase().includes(searchTerm) ||
                    member.phone.includes(searchTerm)
                );
                displaySearchResults(filteredMembers);
            }
        });

        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!memberSearch.contains(e.target) && !memberSearchResults.contains(e.target)) {
                hideSearchResults();
            }
        });
    }

    function displaySearchResults(members) {
        if (!searchResultsList) return;

        searchResultsList.innerHTML = '';

        if (members.length === 0) {
            searchResultsList.innerHTML = '<div class="p-3 text-gray-500 text-center">No members found</div>';
        } else {
            members.slice(0, 10).forEach(member => {
                const div = document.createElement('div');
                div.className = 'p-3 hover:bg-green-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150';
                div.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mr-3 text-white font-bold text-sm">
                                ${member.name.charAt(0).toUpperCase()}
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">${member.name}</div>
                                <div class="text-sm text-gray-600">${member.phone}</div>
                            </div>
                        </div>
                        <div class="text-green-600 text-xs font-medium">
                            Select
                        </div>
                    </div>
                `;
                div.addEventListener('click', () => selectMember(member));
                searchResultsList.appendChild(div);
            });
        }

        showSearchResults();
    }

    function selectMember(member) {
        memberSearch.value = member.name;
        selectedMemberIdInput.value = member.id;

        selectedMemberDetails.innerHTML = `
            <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mr-3 text-white font-bold text-lg">
                    ${member.name.charAt(0).toUpperCase()}
                </div>
                <div class="flex-1">
                    <div class="font-medium text-gray-900">${member.name}</div>
                    <div class="text-sm text-gray-600">${member.phone}</div>
                </div>
            </div>
        `;

        hideSearchResults();
        selectedMemberInfo.classList.remove('hidden');
    }

    function showSearchResults() {
        if (memberSearchResults) {
            memberSearchResults.classList.remove('hidden');
        }
    }

    function hideSearchResults() {
        if (memberSearchResults) {
            memberSearchResults.classList.add('hidden');
        }
    }

    if (changeMemberBtn) {
        changeMemberBtn.addEventListener('click', function() {
            selectedMemberInfo.classList.add('hidden');
            selectedMemberIdInput.value = '';
            memberSearch.value = '';
            memberSearch.focus();
        });
    }

    // Payment type selection
    const paymentTypeRadios = document.querySelectorAll('.payment-type-radio');
    paymentTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove active state from all cards
            document.querySelectorAll('.payment-type-card').forEach(card => {
                card.classList.remove('border-green-500', 'bg-green-50', 'border-red-500', 'bg-red-50');
                card.classList.add('border-gray-200');
            });

            // Add active state to selected card
            if (this.checked) {
                const card = this.nextElementSibling;
                const isExpense = this.closest('#expenses-tab');

                if (isExpense) {
                    card.classList.remove('border-gray-200');
                    card.classList.add('border-red-500', 'bg-red-50');
                } else {
                    card.classList.remove('border-gray-200');
                    card.classList.add('border-green-500', 'bg-green-50');
                }
            }
        });
    });

    // Form validation
    const form = document.getElementById('financeForm');
    form.addEventListener('submit', function(e) {
        const categorySelected = document.querySelector('input[name="category"]:checked');
        const amount = document.querySelector('input[name="amount"]').value;
        const date = document.querySelector('input[name="transaction_date"]').value;

        if (!categorySelected) {
            e.preventDefault();
            alert('Please select a payment/income/expense type.');
            return;
        }

        if (!amount || parseFloat(amount) <= 0) {
            e.preventDefault();
            alert('Please enter a valid amount.');
            return;
        }

        if (!date) {
            e.preventDefault();
            alert('Please select a transaction date.');
            return;
        }
    });

    // Sync form fields across tabs
    const amountInputs = document.querySelectorAll('input[name="amount"]');
    const dateInputs = document.querySelectorAll('input[name="transaction_date"]');
    const descriptionInputs = document.querySelectorAll('textarea[name="description"]');
    const paymentMethodSelects = document.querySelectorAll('select[name="payment_method"]');

    // Sync amount fields
    amountInputs.forEach(input => {
        input.addEventListener('input', function() {
            amountInputs.forEach(otherInput => {
                if (otherInput !== this) {
                    otherInput.value = this.value;
                }
            });
        });
    });

    // Sync date fields
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            dateInputs.forEach(otherInput => {
                if (otherInput !== this) {
                    otherInput.value = this.value;
                }
            });
        });
    });

    // Sync description fields
    descriptionInputs.forEach(input => {
        input.addEventListener('input', function() {
            descriptionInputs.forEach(otherInput => {
                if (otherInput !== this) {
                    otherInput.value = this.value;
                }
            });
        });
    });

    // Sync payment method fields
    paymentMethodSelects.forEach(select => {
        select.addEventListener('change', function() {
            paymentMethodSelects.forEach(otherSelect => {
                if (otherSelect !== this) {
                    otherSelect.value = this.value;
                }
            });
        });
    });
});
</script>
