<div class="container mx-auto px-4 py-6 max-w-6xl">
    <!-- Enhanced Hero Banner -->
    <div class="bg-gradient-to-r from-primary to-primary-light rounded-xl shadow-lg p-6 mb-8 text-white overflow-hidden relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
                <defs>
                    <pattern id="pattern" width="40" height="40" patternUnits="userSpaceOnUse">
                        <circle cx="20" cy="20" r="2" fill="#fff"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#pattern)"/>
            </svg>
        </div>

        <div class="flex flex-col md:flex-row justify-between items-start md:items-center relative z-10">
            <div class="mb-6 md:mb-0">
                <div class="flex items-center mb-3">
                    <div class="bg-white p-3 rounded-full shadow-md mr-4 flex items-center justify-center">
                        <i class="fas fa-hand-holding-usd text-primary text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold mb-1">Tithe Tracking</h1>
                        <p class="opacity-90">Track and manage member tithes</p>
                    </div>
                </div>

                <!-- Tithe Stats -->
                <?php
                $total_amount = 0;
                foreach ($tithes as $tithe) {
                    $total_amount += $tithe['amount'];
                }

                // Calculate percentage of members who tithe
                $tithe_percentage = 0;
                if (count($members) > 0) {
                    $tithe_percentage = (count($tithe_members) / count($members)) * 100;
                }
                ?>
                <div class="mt-4 flex flex-wrap gap-4">
                    <div class="bg-white/20 py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <span><?php echo date('F Y'); ?></span>
                    </div>
                    <div class="bg-white/20 py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-money-bill-wave mr-2"></i>
                        <span>Total Tithes: <span class="font-semibold">GH₵ <?php echo number_format($total_amount, 2); ?></span></span>
                    </div>
                    <div class="bg-white/20 py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-percentage mr-2"></i>
                        <span>Member Participation: <span class="font-semibold"><?php echo number_format($tithe_percentage, 1); ?>%</span></span>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap gap-3">
                <a href="<?php echo BASE_URL; ?>finance/add" class="bg-white text-primary hover:bg-gray-100 py-3 px-6 rounded-lg flex items-center text-sm font-medium transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
                    <i class="fas fa-plus-circle mr-2"></i> Add Tithe
                </a>
                <a href="<?php echo BASE_URL; ?>finance" class="bg-white/20 hover:bg-white/30 text-white py-2.5 px-5 rounded-lg flex items-center text-sm font-medium transition-all duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Finance
                </a>
            </div>
        </div>
    </div>

    <!-- Tithe Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Tithes Card -->
        <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden relative group">
            <div class="absolute top-0 right-0 w-24 h-24 bg-green-50 rounded-bl-full opacity-50 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="flex items-center relative z-10">
                <div class="p-3 rounded-full bg-gradient-to-br from-green-400 to-green-500 text-white mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-money-bill-wave text-lg"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-1">Total Tithes Collected</p>
                    <?php
                    $total_amount = 0;
                    foreach ($tithes as $tithe) {
                        $total_amount += $tithe['amount'];
                    }
                    ?>
                    <h3 class="text-2xl font-bold text-gray-800" id="total-tithes">GH₵ <?php echo number_format($total_amount, 2); ?></h3>
                </div>
            </div>
        </div>

        <!-- Total Members Card -->
        <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden relative group">
            <div class="absolute top-0 right-0 w-24 h-24 bg-blue-50 rounded-bl-full opacity-50 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="flex items-center relative z-10">
                <div class="p-3 rounded-full bg-gradient-to-br from-blue-400 to-blue-500 text-white mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-users text-lg"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-1">Members Paying Tithes</p>
                    <h3 class="text-2xl font-bold text-gray-800" id="total-members"><?php echo count($tithe_members); ?></h3>
                </div>
            </div>
        </div>

        <!-- Average Tithe Card -->
        <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden relative group">
            <div class="absolute top-0 right-0 w-24 h-24 bg-purple-50 rounded-bl-full opacity-50 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="flex items-center relative z-10">
                <div class="p-3 rounded-full bg-gradient-to-br from-purple-400 to-purple-500 text-white mr-4 shadow-md group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chart-line text-lg"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-1">Average Tithe Amount</p>
                    <?php
                    $avg_amount = count($tithes) > 0 ? $total_amount / count($tithes) : 0;
                    ?>
                    <h3 class="text-2xl font-bold text-gray-800" id="avg-tithe">GH₵ <?php echo number_format($avg_amount, 2); ?></h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Members Tithe Table -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 mb-8">
        <div class="p-6 border-b border-gray-100">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="p-2.5 rounded-full bg-gradient-to-r from-indigo-500 to-indigo-600 text-white mr-3 shadow-md">
                        <i class="fas fa-users text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">Members Tithe Records</h3>
                        <p class="text-sm text-gray-500 mt-1">View and manage tithe records for all members</p>
                    </div>
                </div>
                <div class="relative w-full md:w-64">
                    <input type="text" id="member-search" placeholder="Search members..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <div class="absolute left-3 top-2.5 text-gray-400">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Tithes</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Tithe Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="members-table-body">
                    <?php if (empty($tithe_members)) : ?>
                        <tr>
                            <td colspan="5" class="px-6 py-10 text-center">
                                <div class="flex flex-col items-center justify-center text-gray-500 py-6">
                                    <p class="text-lg font-bold text-gray-700 mb-2">No Tithe Records Found</p>
                                    <p class="text-gray-500 max-w-md mb-5 text-sm">There are no tithe records yet. You can add a new tithe record using the "Add Tithe" button.</p>
                                    <a href="<?php echo BASE_URL; ?>finance/add" class="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-white py-2 px-4 rounded-lg flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 text-sm">
                                        <i class="fas fa-plus-circle mr-2 text-sm"></i> Add Your First Tithe Record
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php $rowIndex = 0; ?>
                        <?php foreach ($tithe_members as $member) : ?>
                            <?php $rowIndex++; ?>
                            <tr class="<?php echo $rowIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'; ?> hover:bg-blue-50 transition-all duration-200 group">
                                <!-- Member Column -->
                                <td class="px-6 py-3 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <?php if (!empty($member['profile_picture']) && file_exists($member['profile_picture'])): ?>
                                            <div class="flex-shrink-0 h-8 w-8 rounded-full overflow-hidden border border-gray-200 shadow-sm">
                                                <img src="<?php echo BASE_URL . $member['profile_picture']; ?>" alt="<?php echo $member['name']; ?>" class="w-full h-full object-cover">
                                            </div>
                                        <?php else: ?>
                                            <div class="flex-shrink-0 h-8 w-8 bg-gradient-to-r from-primary to-primary-light rounded-full flex items-center justify-center text-white shadow-sm">
                                                <?php
                                                $nameParts = explode(' ', $member['name']);
                                                $initials = '';
                                                if (count($nameParts) >= 2) {
                                                    $initials = substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1);
                                                } else {
                                                    $initials = substr($member['name'], 0, 2);
                                                }
                                                echo strtoupper($initials);
                                                ?>
                                            </div>
                                        <?php endif; ?>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900"><?php echo $member['name']; ?></div>
                                            <?php if (!empty($member['phone_number'])): ?>
                                                <div class="text-xs text-gray-500"><?php echo $member['phone_number']; ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>

                                <!-- Total Tithes Column -->
                                <td class="px-6 py-3 whitespace-nowrap">
                                    <div class="text-sm font-semibold text-gray-900">GH₵ <?php echo number_format($member['total_tithes'], 2); ?></div>
                                </td>

                                <!-- Last Tithe Date Column -->
                                <td class="px-6 py-3 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo format_date($member['last_tithe_date']); ?></div>
                                </td>

                                <!-- Status Column -->
                                <td class="px-6 py-3 whitespace-nowrap">
                                    <?php
                                    $last_tithe_date = strtotime($member['last_tithe_date']);
                                    $one_month_ago = strtotime('-1 month');
                                    $status = '';
                                    $status_class = '';

                                    if ($last_tithe_date >= $one_month_ago) {
                                        $status = 'Active';
                                        $status_class = 'bg-green-100 text-green-800';
                                    } else if ($last_tithe_date >= strtotime('-3 months')) {
                                        $status = 'Irregular';
                                        $status_class = 'bg-yellow-100 text-yellow-800';
                                    } else {
                                        $status = 'Inactive';
                                        $status_class = 'bg-red-100 text-red-800';
                                    }
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_class; ?>">
                                        <?php echo $status; ?>
                                    </span>
                                </td>

                                <!-- Actions Column -->
                                <td class="px-6 py-3 whitespace-nowrap text-center">
                                    <div class="flex items-center justify-center space-x-2">
                                        <a href="<?php echo BASE_URL; ?>finance/memberTithes?id=<?php echo $member['id']; ?>" class="bg-indigo-50 hover:bg-indigo-100 text-indigo-600 p-2 rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow" title="View Tithe History">
                                            <i class="fas fa-history text-sm"></i>
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>finance/add" class="bg-green-50 hover:bg-green-100 text-green-600 p-2 rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow" title="Add Tithe">
                                            <i class="fas fa-plus text-sm"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('member-search');
        const tableRows = document.querySelectorAll('#members-table-body tr');

        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();

            tableRows.forEach(row => {
                const memberName = row.querySelector('td:first-child').textContent.toLowerCase();

                if (memberName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Add animation to summary cards
        const animateValue = (element, start, end, duration) => {
            let startTimestamp = null;
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                const value = Math.floor(progress * (end - start) + start);

                if (element.id === 'total-tithes' || element.id === 'avg-tithe') {
                    element.innerHTML = 'GH₵ ' + value.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                } else {
                    element.innerHTML = value;
                }

                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            window.requestAnimationFrame(step);
        };

        // Get the elements to animate
        const totalTithesElement = document.getElementById('total-tithes');
        const totalMembersElement = document.getElementById('total-members');
        const avgTitheElement = document.getElementById('avg-tithe');

        // Extract the numeric values
        const totalTithesValue = parseFloat(totalTithesElement.textContent.replace('GH₵ ', '').replace(',', ''));
        const totalMembersValue = parseInt(totalMembersElement.textContent);
        const avgTitheValue = parseFloat(avgTitheElement.textContent.replace('GH₵ ', '').replace(',', ''));

        // Animate the values
        animateValue(totalTithesElement, 0, totalTithesValue, 1500);
        animateValue(totalMembersElement, 0, totalMembersValue, 1500);
        animateValue(avgTitheElement, 0, avgTitheValue, 1500);
    });
</script>
