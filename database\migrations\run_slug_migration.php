<?php
/**
 * Run slug migration for finance categories
 */

require_once dirname(__DIR__, 2) . '/bootstrap.php';

echo "<h2>Adding Slug Column to Finance Categories</h2>\n";

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<h3>Step 1: Adding slug column...</h3>\n";
    
    // Check if slug column already exists
    $checkColumn = "SHOW COLUMNS FROM custom_finance_categories LIKE 'slug'";
    $stmt = $conn->prepare($checkColumn);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "<p>⏭️ Slug column already exists</p>\n";
    } else {
        // Add slug column
        $addColumn = "ALTER TABLE custom_finance_categories ADD COLUMN slug VARCHAR(100) UNIQUE AFTER name";
        $conn->exec($addColumn);
        echo "<p>✅ Added slug column</p>\n";
    }
    
    echo "<h3>Step 2: Generating slugs for existing categories...</h3>\n";
    
    // Get all categories without slugs
    $getCategories = "SELECT id, name FROM custom_finance_categories WHERE slug IS NULL OR slug = ''";
    $stmt = $conn->prepare($getCategories);
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    foreach ($categories as $category) {
        // Generate slug from name
        $slug = strtolower(trim($category->name));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // Ensure slug is unique
        $originalSlug = $slug;
        $counter = 1;
        
        do {
            $checkSlug = "SELECT id FROM custom_finance_categories WHERE slug = ? AND id != ?";
            $checkStmt = $conn->prepare($checkSlug);
            $checkStmt->execute([$slug, $category->id]);
            
            if ($checkStmt->rowCount() > 0) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            } else {
                break;
            }
        } while (true);
        
        // Update category with slug
        $updateSlug = "UPDATE custom_finance_categories SET slug = ? WHERE id = ?";
        $updateStmt = $conn->prepare($updateSlug);
        $updateStmt->execute([$slug, $category->id]);
        
        echo "<p>✅ Generated slug '{$slug}' for category '{$category->name}'</p>\n";
    }
    
    echo "<h3>Step 3: Adding index and constraints...</h3>\n";
    
    try {
        // Add index for better performance
        $addIndex = "CREATE INDEX idx_slug ON custom_finance_categories(slug)";
        $conn->exec($addIndex);
        echo "<p>✅ Added slug index</p>\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p>⏭️ Slug index already exists</p>\n";
        } else {
            throw $e;
        }
    }
    
    // Make slug NOT NULL after generating values
    try {
        $makeNotNull = "ALTER TABLE custom_finance_categories MODIFY COLUMN slug VARCHAR(100) NOT NULL UNIQUE";
        $conn->exec($makeNotNull);
        echo "<p>✅ Made slug column NOT NULL and UNIQUE</p>\n";
    } catch (PDOException $e) {
        echo "<p>⚠️ Note: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<h3>✅ Slug migration completed successfully!</h3>\n";
    echo "<p>All finance categories now have URL-friendly slugs for dynamic dashboards.</p>\n";
    
    // Show generated slugs
    echo "<h3>Generated Slugs:</h3>\n";
    $showSlugs = "SELECT name, slug, category_type FROM custom_finance_categories ORDER BY category_type, name";
    $stmt = $conn->prepare($showSlugs);
    $stmt->execute();
    $allCategories = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th style='padding: 8px;'>Category Name</th><th style='padding: 8px;'>Slug</th><th style='padding: 8px;'>Type</th><th style='padding: 8px;'>Dashboard URL</th></tr>\n";
    
    foreach ($allCategories as $cat) {
        $dashboardUrl = BASE_URL . "finance/category-dashboard/" . $cat->slug;
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$cat->name}</td>";
        echo "<td style='padding: 8px;'>{$cat->slug}</td>";
        echo "<td style='padding: 8px;'>{$cat->category_type}</td>";
        echo "<td style='padding: 8px;'><a href='{$dashboardUrl}' target='_blank'>{$dashboardUrl}</a></td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
} catch (Exception $e) {
    echo "<h3>❌ Migration failed!</h3>\n";
    echo "<p>Error: " . $e->getMessage() . "</p>\n";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>\n";
}
?>
