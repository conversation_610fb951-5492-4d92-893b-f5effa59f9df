<div class="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-primary px-6 py-4">
            <h1 class="text-xl font-semibold text-white">QR Code Attendance System</h1>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                    <h2 class="text-lg font-semibold mb-4 text-gray-800">Generate New QR Code</h2>
                    <p class="text-sm text-gray-600 mb-4">Create a QR code for a service that members can scan to mark their attendance.</p>
                    
                    <form action="<?php echo BASE_URL; ?>attendance/qr-generate" method="POST" class="space-y-4">
                        <!-- Service Selection -->
                        <div>
                            <label for="service_id" class="block text-sm font-medium text-gray-700 mb-1">Service</label>
                            <select id="service_id" name="service_id" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" required>
                                <option value="">Select a service</option>
                                <?php foreach ($services as $service): ?>
                                    <option value="<?php echo $service['id']; ?>"><?php echo $service['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- Date Selection -->
                        <div>
                            <label for="attendance_date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                            <input type="date" id="attendance_date" name="attendance_date" 
                                   value="<?php echo date('Y-m-d'); ?>" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" required>
                        </div>
                        
                        <!-- Duration Selection -->
                        <div>
                            <label for="duration" class="block text-sm font-medium text-gray-700 mb-1">QR Code Validity (minutes)</label>
                            <select id="duration" name="duration" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" required>
                                <option value="30">30 minutes</option>
                                <option value="60" selected>1 hour</option>
                                <option value="120">2 hours</option>
                                <option value="180">3 hours</option>
                                <option value="240">4 hours</option>
                                <option value="300">5 hours</option>
                            </select>
                        </div>
                        
                        <div class="pt-2">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <i class="fas fa-qrcode mr-2"></i> Generate QR Code
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="bg-blue-50 rounded-xl shadow-md p-6 border border-blue-100">
                    <h2 class="text-lg font-semibold mb-4 text-gray-800">How It Works</h2>
                    
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">1</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">Generate a QR code for a specific service and date.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">2</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">Display the QR code on a screen or print it out.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">3</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">Members scan the QR code with their smartphones.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">4</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">They search for their name and mark themselves present.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">5</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">Attendance is automatically recorded in the system.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 text-center">
                        <i class="fas fa-info-circle text-blue-500 mr-1"></i>
                        <span class="text-sm text-blue-700">QR codes are valid for the duration you select.</span>
                    </div>
                </div>
            </div>
            
            <!-- Recent QR Sessions -->
            <div class="mt-8">
                <h2 class="text-lg font-semibold mb-4 text-gray-800">Recent QR Sessions</h2>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php if (empty($recent_sessions)): ?>
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                                        No recent QR sessions found.
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recent_sessions as $session): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo htmlspecialchars($session['service_name']); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo format_date($session['attendance_date']); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo time_ago($session['created_at']); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php
                                            $expires_at = strtotime($session['expires_at']);
                                            $now = time();
                                            if ($expires_at > $now) {
                                                $time_left = $expires_at - $now;
                                                $minutes_left = floor($time_left / 60);
                                                echo 'In ' . $minutes_left . ' min';
                                            } else {
                                                echo 'Expired';
                                            }
                                            ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($session['status'] === 'active' && strtotime($session['expires_at']) > time()): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                            <?php elseif ($session['status'] === 'closed'): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Closed</span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Expired</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <span class="font-medium"><?php echo $session['attendance_count'] ?? 0; ?></span>
                                            <span class="text-gray-500">members</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div class="flex space-x-2">
                                                <?php if ($session['status'] === 'active' && strtotime($session['expires_at']) > time()): ?>
                                                    <a href="<?php echo BASE_URL; ?>attendance/qr-display?token=<?php echo $session['token']; ?>"
                                                       class="text-blue-600 hover:text-blue-900" title="View QR Code">
                                                        <i class="fas fa-qrcode"></i>
                                                    </a>
                                                    <button onclick="extendSession(<?php echo $session['id']; ?>)"
                                                            class="text-green-600 hover:text-green-900" title="Extend Session">
                                                        <i class="fas fa-clock"></i>
                                                    </button>
                                                    <button onclick="closeSession(<?php echo $session['id']; ?>)"
                                                            class="text-red-600 hover:text-red-900" title="Close Session">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button onclick="viewSessionStats(<?php echo $session['id']; ?>)"
                                                        class="text-purple-600 hover:text-purple-900" title="View Statistics">
                                                    <i class="fas fa-chart-bar"></i>
                                                </button>
                                                <a href="<?php echo BASE_URL; ?>attendance/qr-session-export?session_id=<?php echo $session['id']; ?>"
                                                   class="text-gray-600 hover:text-gray-900" title="Export Data">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Session Management Modal -->
<div id="sessionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" id="modalTitle">Session Management</h3>
            <div id="modalContent">
                <!-- Dynamic content will be loaded here -->
            </div>
            <div class="flex justify-end mt-4">
                <button onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Session Management Functions
function extendSession(sessionId) {
    const minutes = prompt('How many minutes to extend the session?', '30');
    if (minutes && !isNaN(minutes)) {
        fetch('<?php echo BASE_URL; ?>attendance/qr-session-manage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=extend&session_id=${sessionId}&additional_minutes=${minutes}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while extending the session');
        });
    }
}

function closeSession(sessionId) {
    if (confirm('Are you sure you want to close this QR session? This action cannot be undone.')) {
        fetch('<?php echo BASE_URL; ?>attendance/qr-session-manage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=close&session_id=${sessionId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while closing the session');
        });
    }
}

function viewSessionStats(sessionId) {
    fetch('<?php echo BASE_URL; ?>attendance/qr-session-manage', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get_stats&session_id=${sessionId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSessionStatsModal(data.stats, data.attendance_records);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while loading session statistics');
    });
}

function showSessionStatsModal(stats, attendanceRecords) {
    const modalTitle = document.getElementById('modalTitle');
    const modalContent = document.getElementById('modalContent');

    modalTitle.textContent = 'Session Statistics';

    let content = `
        <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-blue-50 p-3 rounded">
                    <div class="text-2xl font-bold text-blue-600">${stats.total_attendance || 0}</div>
                    <div class="text-sm text-gray-600">Total Attendance</div>
                </div>
                <div class="bg-green-50 p-3 rounded">
                    <div class="text-2xl font-bold text-green-600">${stats.present_count || 0}</div>
                    <div class="text-sm text-gray-600">Present</div>
                </div>
            </div>

            <div class="mt-4">
                <h4 class="font-medium text-gray-900 mb-2">Recent Attendance</h4>
                <div class="max-h-40 overflow-y-auto">
    `;

    if (attendanceRecords && attendanceRecords.length > 0) {
        attendanceRecords.slice(0, 10).forEach(record => {
            content += `
                <div class="flex justify-between items-center py-1 border-b border-gray-100">
                    <span class="text-sm">${record.first_name} ${record.last_name}</span>
                    <span class="text-xs text-gray-500">${new Date(record.created_at).toLocaleTimeString()}</span>
                </div>
            `;
        });
    } else {
        content += '<div class="text-sm text-gray-500">No attendance records found</div>';
    }

    content += `
                </div>
            </div>
        </div>
    `;

    modalContent.innerHTML = content;
    document.getElementById('sessionModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('sessionModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('sessionModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
