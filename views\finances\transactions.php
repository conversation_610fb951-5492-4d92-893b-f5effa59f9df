<?php
// Make sure we have the database connection for the category dropdown
require_once 'config/database.php';
?>
<div class="container mx-auto px-4 py-6 max-w-7xl">
    <!-- Custom styles for transactions page -->
    <style>
        /* Filter section styles */
        .filter-group {
            margin-bottom: 0.75rem;
        }

        .filter-label {
            display: flex;
            align-items: center;
            font-size: 0.75rem;
            font-weight: 500;
            color: #4B5563;
            margin-bottom: 0.375rem;
        }

        .filter-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 1.25rem;
            height: 1.25rem;
            border-radius: 0.25rem;
            margin-right: 0.375rem;
            font-size: 0.625rem;
        }

        .filter-input {
            width: 100%;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border: 1px solid #E5E7EB;
            border-radius: 0.375rem;
            background-color: #F9FAFB;
            transition: all 0.2s;
        }

        .filter-input:focus {
            outline: none;
            border-color: #3BD16F;
            box-shadow: 0 0 0 2px rgba(59, 209, 111, 0.1);
            background-color: white;
        }

        .filter-clear {
            position: absolute;
            right: 0.5rem;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9CA3AF;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .filter-input:not(:placeholder-shown) ~ .filter-clear,
        select.filter-input:not([value=""]) ~ .filter-clear {
            opacity: 1;
        }

        /* Table styles */
        .transactions-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .transactions-table th {
            background-color: #F9FAFB;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            font-size: 0.75rem;
        }

        .transactions-table tr:hover td {
            background-color: #F9FAFB;
        }

        /* Subtle background patterns */
        .bg-pattern-dots {
            background-image: radial-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px);
            background-size: 20px 20px;
        }

        /* Filter tags */
        .filter-tag {
            display: flex;
            align-items: center;
            background-color: #F3F4F6;
            border-radius: 9999px;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            color: #4B5563;
            margin-right: 0.25rem;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .filter-tag span {
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .filter-tag-close {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.375rem;
            width: 1rem;
            height: 1rem;
            border-radius: 9999px;
            background-color: #E5E7EB;
            color: #6B7280;
            cursor: pointer;
            flex-shrink: 0;
        }

        .filter-tag-close:hover {
            background-color: #D1D5DB;
            color: #4B5563;
        }

        /* Filter pulse animation */
        @keyframes filterPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        .filter-pulse {
            animation: filterPulse 0.8s ease-in-out;
        }

        /* Animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease forwards;
        }

        /* Collapsed filter styles */
        .filter-collapsed {
            max-height: 0;
            padding-top: 0;
            padding-bottom: 0;
            margin-top: 0;
            margin-bottom: 0;
            opacity: 0;
        }

        .filter-expanded {
            max-height: 1000px;
            opacity: 1;
        }
    </style>
    <!-- Enhanced Hero Banner -->
    <div class="bg-gradient-to-r from-primary to-primary-light rounded-xl shadow-lg p-6 mb-8 text-white overflow-hidden relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
                <defs>
                    <pattern id="pattern" width="40" height="40" patternUnits="userSpaceOnUse">
                        <circle cx="20" cy="20" r="2" fill="#fff"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#pattern)"/>
            </svg>
        </div>

        <div class="flex flex-col md:flex-row justify-between items-start md:items-center relative z-10">
            <div class="mb-6 md:mb-0">
                <div class="flex items-center mb-3">
                    <div class="bg-white p-3 rounded-full shadow-md mr-4 flex items-center justify-center">
                        <i class="fas fa-list-alt text-primary text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold mb-1">Transaction Records</h1>
                        <p class="opacity-90">View and manage all financial transactions</p>
                    </div>
                </div>

                <!-- Transaction Stats -->
                <div class="mt-4 flex flex-wrap gap-4">
                    <div class="bg-white/20 py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <span><?php echo date('F Y'); ?></span>
                    </div>
                    <div class="bg-white/20 py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-receipt mr-2"></i>
                        <span>Total Records: <span class="font-semibold"><?php echo count($transactions); ?></span></span>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap gap-3">
                <a href="<?php echo BASE_URL; ?>finance/add" class="bg-white text-primary hover:bg-gray-100 py-3 px-6 rounded-lg flex items-center text-sm font-medium transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1">
                    <i class="fas fa-plus-circle mr-2"></i> Add Transaction
                </a>
                <a href="<?php echo BASE_URL; ?>finance" class="bg-white/20 hover:bg-white/30 text-white py-2.5 px-5 rounded-lg flex items-center text-sm font-medium transition-all duration-300">
                    <i class="fas fa-chart-pie mr-2"></i> Finance Dashboard
                </a>
                <?php if (isset($hasArchivedRecords) && $hasArchivedRecords): ?>
                <a href="<?php echo BASE_URL; ?>finance/archived" class="bg-white/20 hover:bg-white/30 text-white py-2.5 px-5 rounded-lg flex items-center text-sm font-medium transition-all duration-300">
                    <i class="fas fa-archive mr-2"></i> View Archived
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Simplified Filter Bar (copied from SMS messages page) -->
    <div class="bg-white rounded-lg shadow-md p-5 mb-6 border border-gray-200">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <div class="bg-primary-light bg-opacity-20 p-2 rounded-full mr-3">
                    <i class="fas fa-filter text-primary"></i>
                </div>
                <h3 class="text-base font-medium text-gray-800">Quick Filter</h3>
            </div>
            <button id="toggle-filters" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 py-1.5 px-3 rounded-full flex items-center transition-colors duration-200 focus:outline-none">
                <span id="toggle-text">Show Options</span>
                <i class="fas fa-chevron-down ml-1.5 text-xs"></i>
            </button>
        </div>

        <!-- Always visible search bar -->
        <div class="relative mt-3 mb-4">
            <input type="text" id="table-search" class="pl-10 w-full rounded-full border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 text-sm py-3 px-4 hover:border-primary transition-colors duration-200" placeholder="Search transactions...">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-500"></i>
            </div>
            <button id="reset-search" class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200 focus:outline-none">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Expandable advanced filters -->
        <div id="filters-container" class="hidden mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <div class="relative">
                        <select id="type-filter" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 text-sm pl-9 pr-9 py-2.5 appearance-none hover:border-primary transition-colors duration-200">
                            <option value="">All Types</option>
                            <option value="income">Income</option>
                            <option value="expense">Expense</option>
                        </select>
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-exchange-alt text-gray-500 text-xs"></i>
                        </div>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-500 text-xs"></i>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="relative">
                        <select id="category-filter" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 text-sm pl-9 pr-9 py-2.5 appearance-none hover:border-primary transition-colors duration-200">
                            <option value="">All Categories</option>
                            <option value="tithe">Tithe</option>
                            <option value="offering">Offering</option>
                            <option value="project_offering">Project Offering</option>
                            <option value="donation">Donation</option>
                            <option value="seed">Seed</option>
                            <option value="pladge">Pledge</option>
                            <option value="welfare">Welfare</option>
                            <option value="others">Others</option>
                            <option value="expense">Expense</option>
                            <?php
                            // Get any additional categories from the database that might not be in our hardcoded list
                            $db = new Database();
                            $conn = $db->getConnection();
                            $stmt = $conn->query('SELECT DISTINCT category FROM finances WHERE category NOT IN ("tithe", "offering", "project_offering", "donation", "seed", "pladge", "welfare", "others", "expense") ORDER BY category');
                            $additionalCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);

                            // Add any additional categories found
                            foreach ($additionalCategories as $cat) {
                                $displayCat = ucfirst(str_replace('_', ' ', $cat));
                                echo "<option value=\"$cat\">$displayCat</option>";
                            }
                            ?>
                        </select>
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-tag text-gray-500 text-xs"></i>
                        </div>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-500 text-xs"></i>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <div class="relative flex-1">
                        <input type="date" id="start-date" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 text-sm pl-9 py-2.5 hover:border-primary transition-colors duration-200" placeholder="From">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-500 text-xs"></i>
                        </div>
                    </div>
                    <div class="relative flex-1">
                        <input type="date" id="end-date" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 text-sm pl-9 py-2.5 hover:border-primary transition-colors duration-200" placeholder="To">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-500 text-xs"></i>
                        </div>
                    </div>
                </div>
                <div class="md:col-span-3 flex justify-end">
                    <button id="reset-filters" class="bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm py-2.5 px-4 rounded-md mr-3 transition-colors duration-200 flex items-center border border-gray-200 shadow-sm">
                        <i class="fas fa-undo-alt mr-2 text-xs"></i> Reset
                    </button>
                    <button id="apply-filters" class="bg-primary hover:bg-primary-dark text-white text-sm py-2.5 px-4 rounded-md transition-colors duration-200 flex items-center shadow-sm">
                        <i class="fas fa-filter mr-2 text-xs"></i> Apply
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Finances Table -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-all duration-300 animate-fadeIn" style="animation-delay: 0.2s;">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
            <div>
                <h3 class="text-base font-bold text-gray-800">Transaction Records</h3>
                <p class="text-xs text-gray-600">Showing <span class="font-medium"><?php echo count($finances); ?></span> of <span class="font-medium"><?php echo $totalCount; ?></span> transactions</p>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 transactions-table">
                <thead>
                    <tr>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                        <th scope="col" class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($finances)) : ?>
                        <tr>
                            <td colspan="6" class="px-4 py-8 text-center">
                                <div class="flex flex-col items-center justify-center text-gray-500 py-4">
                                    <p class="text-base font-bold text-gray-700 mb-1">No Financial Records Found</p>
                                    <p class="text-gray-500 max-w-md mb-4 text-sm">No transactions found. Add a new transaction or adjust your filters.</p>
                                    <a href="<?php echo BASE_URL; ?>finance/add" class="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-white py-2 px-4 rounded-lg flex items-center justify-center shadow-sm hover:shadow transition-all duration-300 text-sm">
                                        <i class="fas fa-plus-circle mr-2"></i> Add Transaction
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php $rowIndex = 0; ?>
                        <?php foreach ($finances as $finance) : ?>
                            <?php $rowIndex++; ?>
                            <tr class="<?php echo $rowIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'; ?> hover:bg-blue-50 transition-all duration-200 group">
                                <!-- Date Column -->
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-semibold text-gray-900"><?php echo format_date($finance['transaction_date']); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($finance['created_at'])); ?></div>
                                    </div>
                                </td>

                                <!-- Category Column -->
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <?php
                                    $categoryClass = '';
                                    $categoryIcon = '';
                                    $categoryBgClass = '';
                                    $categoryBorderClass = '';

                                    if ($finance['category'] === 'expense') {
                                        $categoryClass = 'text-red-600';
                                        $categoryIcon = 'fa-arrow-circle-down';
                                        $categoryBgClass = 'bg-red-50';
                                        $categoryBorderClass = 'border-red-100';
                                    } else {
                                        $categoryClass = 'text-green-600';
                                        $categoryIcon = 'fa-arrow-circle-up';
                                        $categoryBgClass = 'bg-green-50';
                                        $categoryBorderClass = 'border-green-100';
                                    }
                                    ?>
                                    <div class="flex items-center">
                                        <div class="<?php echo $categoryBgClass; ?> p-2 rounded-full mr-3">
                                            <i class="fas <?php echo $categoryIcon; ?> <?php echo $categoryClass; ?>"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php
                                                // Format the category for display
                                                $displayCategory = str_replace('_', ' ', $finance['category']);
                                                $displayCategory = ucwords($displayCategory);
                                                echo $displayCategory;
                                                ?>
                                            </div>
                                            <div class="text-xs <?php echo $categoryClass; ?>">
                                                <?php echo $finance['category'] === 'expense' ? 'Expense' : 'Income'; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>

                                <!-- Amount Column -->
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="text-sm font-bold <?php echo $finance['category'] === 'expense' ? 'text-red-600' : 'text-green-600'; ?>">
                                        GH₵ <?php echo number_format($finance['amount'], 2); ?>
                                    </div>
                                </td>

                                <!-- Description Column -->
                                <td class="px-4 py-3">
                                    <div class="text-sm text-gray-900 max-w-xs truncate" title="<?php echo htmlspecialchars($finance['description']); ?>">
                                        <?php echo htmlspecialchars($finance['description']); ?>
                                    </div>
                                </td>

                                <!-- Member Column -->
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <?php if (!empty($finance['member_id']) && !empty($finance['member_name'])) : ?>
                                        <a href="<?php echo BASE_URL; ?>members/view?id=<?php echo $finance['member_id']; ?>" class="text-sm text-blue-600 hover:text-blue-800 hover:underline">
                                            <?php echo htmlspecialchars($finance['member_name']); ?>
                                        </a>
                                    <?php else : ?>
                                        <span class="text-sm text-gray-500">N/A</span>
                                    <?php endif; ?>
                                </td>

                                <!-- Actions Column -->
                                <td class="px-4 py-3 whitespace-nowrap text-center">
                                    <div class="flex items-center justify-center space-x-2">
                                        <a href="<?php echo BASE_URL; ?>finance/edit?id=<?php echo $finance['id']; ?>" class="bg-indigo-50 hover:bg-indigo-100 text-indigo-600 p-1.5 rounded-md transition-all duration-200 hover:scale-110 text-xs" title="Edit Transaction">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $finance['id']; ?>)" class="bg-red-50 hover:bg-red-100 text-red-600 p-1.5 rounded-md transition-all duration-200 hover:scale-110 text-xs" title="Delete Transaction">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <!-- Pagination Controls -->
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium"><?php echo count($finances) > 0 ? ($page - 1) * $limit + 1 : 0; ?></span> to <span class="font-medium"><?php echo min($page * $limit, $totalCount); ?></span> of <span class="font-medium"><?php echo $totalCount; ?></span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <!-- Previous Page Link -->
                            <?php if ($page > 1): ?>
                                <a href="<?php echo BASE_URL; ?>finance/transactions?page=<?php echo $page - 1; ?><?php echo isset($_GET['limit']) ? '&limit=' . htmlspecialchars($_GET['limit']) : ''; ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php else: ?>
                                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $startPage + 4);
                            if ($endPage - $startPage < 4 && $totalPages > 5) {
                                $startPage = max(1, $endPage - 4);
                            }

                            // Preserve any limit parameter
                            $limitParam = isset($_GET['limit']) ? '&limit=' . htmlspecialchars($_GET['limit']) : '';

                            for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="relative inline-flex items-center px-4 py-2 border border-primary bg-primary-light text-sm font-medium text-white">
                                        <?php echo $i; ?>
                                    </span>
                                <?php else: ?>
                                    <a href="<?php echo BASE_URL; ?>finance/transactions?page=<?php echo $i; ?><?php echo $limitParam; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <!-- Next Page Link -->
                            <?php if ($page < $totalPages): ?>
                                <a href="<?php echo BASE_URL; ?>finance/transactions?page=<?php echo $page + 1; ?><?php echo $limitParam; ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php else: ?>
                                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($_SESSION['user_role']) && ($_SESSION['user_role'] === 'admin' || $_SESSION['user_role'] === 'super_admin')): ?>
    <!-- Admin-only content could go here -->
    <?php endif; ?>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get all filter elements
        const searchInput = document.getElementById('table-search');
        const resetSearchBtn = document.getElementById('reset-search');
        const typeFilter = document.getElementById('type-filter');
        const categoryFilter = document.getElementById('category-filter');
        const startDateFilter = document.getElementById('start-date');
        const endDateFilter = document.getElementById('end-date');
        const resetFiltersButton = document.getElementById('reset-filters');
        const applyFiltersButton = document.getElementById('apply-filters');
        const toggleFiltersButton = document.getElementById('toggle-filters');
        const filtersContainer = document.getElementById('filters-container');
        const toggleText = document.getElementById('toggle-text');

        // Store original table data for filtering
        const tableRows = document.querySelectorAll('tbody tr');
        const originalTableData = [];

        // Extract and store data from each row for faster filtering
        tableRows.forEach(row => {
            // Skip the "no records" row if it exists
            if (row.cells.length === 1 && row.cells[0].colSpan > 1) {
                return;
            }

            // Extract text content from each row for searching
            const categoryCell = row.querySelector('td:nth-child(2)');
            const dateCell = row.querySelector('td:nth-child(1)');

            let typeText = '';
            let categoryText = '';

            if (categoryCell) {
                const typeElement = categoryCell.querySelector('.text-xs');
                const categoryElement = categoryCell.querySelector('.text-sm.font-medium');

                if (typeElement) {
                    // Get the type (income/expense)
                    typeText = typeElement.textContent.toLowerCase().trim();
                    // Make sure we get 'income' or 'expense' to match the filter values
                    if (typeText === 'income' || typeText === 'expense') {
                        // Already correct format
                    } else {
                        // If it's not explicitly 'expense', it's an income type
                        typeText = typeText === 'expense' ? 'expense' : 'income';
                    }
                }

                if (categoryElement) {
                    // Get the category (tithe, offering, etc.)
                    categoryText = categoryElement.textContent.toLowerCase().trim();
                }
            }

            const rowData = {
                element: row,
                fullText: row.textContent.toLowerCase(),
                type: typeText,
                category: categoryText,
                date: dateCell && dateCell.querySelector('.text-sm') ?
                      dateCell.querySelector('.text-sm').textContent : ''
            };

            originalTableData.push(rowData);
        });

        // Toggle advanced filters
        toggleFiltersButton.addEventListener('click', function() {
            const icon = this.querySelector('i');

            if (filtersContainer.classList.contains('hidden')) {
                filtersContainer.classList.remove('hidden');
                toggleText.textContent = 'Hide Options';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            } else {
                filtersContainer.classList.add('hidden');
                toggleText.textContent = 'Show Options';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
        });

        // Function to filter table rows based on search text
        function filterTable(searchText) {
            const lowerSearchText = searchText.toLowerCase();
            let visibleCount = 0;

            originalTableData.forEach(rowData => {
                const text = rowData.fullText;
                if (text.includes(lowerSearchText)) {
                    rowData.element.style.display = '';
                    visibleCount++;
                } else {
                    rowData.element.style.display = 'none';
                }
            });

            // Show/hide reset button
            if (searchText) {
                resetSearchBtn.style.display = 'flex';
            } else {
                resetSearchBtn.style.display = 'none';
            }

            // Update the count of visible transactions
            updateVisibleCount(visibleCount);
        }

        // Function to update the visible count display
        function updateVisibleCount(visibleCount) {
            const countElement = document.querySelector('.text-xs.text-gray-600 span.font-medium');
            if (countElement) {
                countElement.textContent = visibleCount;
                // Add pulse animation
                countElement.classList.add('filter-pulse');
                setTimeout(() => {
                    countElement.classList.remove('filter-pulse');
                }, 1000);
            }

            // Update pagination info when filtering
            const paginationInfoElement = document.querySelector('.px-4.py-3.bg-gray-50 .text-sm.text-gray-700');
            if (paginationInfoElement && visibleCount !== originalTableData.length) {
                paginationInfoElement.innerHTML = `Showing <span class="font-medium">${visibleCount}</span> filtered results`;
            }
        }

        // Initialize reset button visibility
        resetSearchBtn.style.display = searchInput.value ? 'flex' : 'none';

        // Add event listeners for real-time search
        searchInput.addEventListener('input', function() {
            filterTable(this.value);
        });

        // Reset search
        resetSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            filterTable('');
            searchInput.focus();
        });

        // Apply advanced filters
        applyFiltersButton.addEventListener('click', function() {
            const type = typeFilter.value;
            const category = categoryFilter.value;
            const startDate = startDateFilter.value;
            const endDate = endDateFilter.value;
            const searchText = searchInput.value;

            let visibleCount = 0;

            originalTableData.forEach(rowData => {
                let showRow = true;

                // Search filter
                if (searchText && !rowData.fullText.includes(searchText.toLowerCase())) {
                    showRow = false;
                }

                // Type filter
                if (type && rowData.type !== type) {
                    showRow = false;
                }

                // Category filter
                if (category && rowData.category.toLowerCase() !== category.toLowerCase()) {
                    showRow = false;
                }

                // Date range filter
                if (startDate || endDate) {
                    const rowDate = new Date(rowData.date);

                    if (startDate) {
                        const filterStartDate = new Date(startDate);
                        if (rowDate < filterStartDate) {
                            showRow = false;
                        }
                    }

                    if (endDate) {
                        const filterEndDate = new Date(endDate);
                        filterEndDate.setHours(23, 59, 59); // Set to end of day
                        if (rowDate > filterEndDate) {
                            showRow = false;
                        }
                    }
                }

                // Show or hide the row
                rowData.element.style.display = showRow ? '' : 'none';

                // Count visible rows
                if (showRow) {
                    visibleCount++;
                }
            });

            // Update the count display
            updateVisibleCount(visibleCount);

            // Highlight the table if filters are active
            const tableContainer = document.querySelector('.bg-white.rounded-xl.shadow-md.overflow-hidden');
            if (tableContainer) {
                if (type || category || startDate || endDate) {
                    tableContainer.classList.add('border-primary');
                } else {
                    tableContainer.classList.remove('border-primary');
                }
            }
        });

        // Reset all filters
        resetFiltersButton.addEventListener('click', function() {
            typeFilter.value = '';
            categoryFilter.value = '';
            startDateFilter.value = '';
            endDateFilter.value = '';
            searchInput.value = '';

            // Show all rows
            originalTableData.forEach(rowData => {
                rowData.element.style.display = '';
            });

            // Update the count display
            updateVisibleCount(originalTableData.length);

            // Reset pagination info
            const paginationInfoElement = document.querySelector('.px-4.py-3.bg-gray-50 .text-sm.text-gray-700');
            if (paginationInfoElement) {
                const currentPage = <?php echo $page; ?>;
                const limit = <?php echo $limit; ?>;
                const totalCount = <?php echo $totalCount; ?>;
                const start = (currentPage - 1) * limit + 1;
                const end = Math.min(currentPage * limit, totalCount);
                paginationInfoElement.innerHTML = `Showing <span class="font-medium">${start}</span> to <span class="font-medium">${end}</span> of <span class="font-medium">${totalCount}</span> results`;
            }

            // Remove highlight from table
            const tableContainer = document.querySelector('.bg-white.rounded-xl.shadow-md.overflow-hidden');
            if (tableContainer) {
                tableContainer.classList.remove('border-primary');
            }

            // Hide reset search button
            resetSearchBtn.style.display = 'none';
        });

        // Initialize table filtering with any existing search text
        filterTable(searchInput.value);
    });

    // Confirm delete function
    function confirmDelete(id) {
        if (confirm('Are you sure you want to delete this transaction? This action cannot be undone.')) {
            let deleteUrl = '<?php echo BASE_URL; ?>finance/delete?id=' + id;

            // Add category parameter if available for proper redirect
            <?php if (isset($filters['category']) && !empty($filters['category'])): ?>
            deleteUrl += '&category=<?php echo urlencode($filters['category']); ?>';
            <?php endif; ?>

            window.location.href = deleteUrl;
        }
    }
</script>

