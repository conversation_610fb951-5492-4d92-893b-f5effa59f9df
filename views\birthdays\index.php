<?php
// No model imports needed

// Start output buffering
ob_start();
?>

<div class="container mx-auto fade-in">
    <!-- Header Section -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6 border border-gray-100">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-birthday-cake text-lg text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold">Birthdays in <?php echo date('F'); ?></h1>
                </div>
            </div>
            <p class="opacity-90 text-sm">View and celebrate all church members with birthdays this month.</p>
        </div>

        <div class="p-4 bg-white">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Quick Stats -->
                <div class="mb-4 md:mb-0">
                    <div class="bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg p-4 flex items-center w-48">
                        <div class="rounded-lg bg-white bg-opacity-30 p-3 mr-3 text-white">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-white"><?php echo count($birthdays_this_month); ?></div>
                            <div class="text-sm text-white text-opacity-90">Birthdays</div>
                        </div>
                    </div>
                </div>
                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-2">
                    <button onclick="exportBirthdays()" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-colors">
                        <i class="fas fa-download mr-2"></i> Export
                    </button>
                    <button onclick="sendBulkWishes()" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-colors">
                        <i class="fas fa-sms mr-2"></i> Bulk SMS
                    </button>
                    <a href="<?php echo BASE_URL; ?>dashboard" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg flex items-center text-sm transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i> Back
                    </a>
                </div>
            </div>
        </div>
    </div>



    <!-- View Toggle and Today's Birthdays -->
    <div class="mb-8">
        <!-- View Toggle -->
        <div class="flex justify-between items-center mb-6">
            <div class="flex space-x-2">
                <button onclick="switchView('grid')" id="grid-view-btn" class="px-4 py-2 bg-primary text-white rounded-lg transition-all duration-200 hover:bg-primary-dark">
                    <i class="fas fa-th-large mr-2"></i>Grid View
                </button>
                <button onclick="switchView('calendar')" id="calendar-view-btn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg transition-all duration-200 hover:bg-gray-300">
                    <i class="fas fa-calendar mr-2"></i>Calendar View
                </button>
                <button onclick="switchView('timeline')" id="timeline-view-btn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg transition-all duration-200 hover:bg-gray-300">
                    <i class="fas fa-list mr-2"></i>Timeline View
                </button>
            </div>

            <!-- Quick Stats -->
            <div class="flex space-x-4 text-sm">
                <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                    <i class="fas fa-calendar-day mr-1"></i>
                    Today: <?php echo count($birthdays_today ?? []); ?>
                </div>
                <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                    <i class="fas fa-calendar-week mr-1"></i>
                    This Week: <?php echo $birthday_stats['this_week'] ?? 0; ?>
                </div>
                <div class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full">
                    <i class="fas fa-calendar-alt mr-1"></i>
                    This Month: <?php echo $birthday_stats['total'] ?? count($birthdays_this_month); ?>
                </div>
            </div>
        </div>

        <!-- Today's Birthdays Highlight -->
        <?php if (!empty($birthdays_today)): ?>
        <div class="bg-gradient-to-r from-yellow-400 to-orange-400 text-white p-6 rounded-xl mb-6 shadow-lg">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-white bg-opacity-20 p-3 rounded-full mr-4">
                        <i class="fas fa-star text-2xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold">🎉 Today's Birthday Celebrants!</h3>
                        <p class="opacity-90"><?php echo count($birthdays_today); ?> member<?php echo count($birthdays_today) > 1 ? 's' : ''; ?> celebrating today</p>
                    </div>
                </div>
                <button onclick="sendTodayWishes()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-all duration-200">
                    <i class="fas fa-sms mr-2"></i>Send All Wishes
                </button>
            </div>
            <div class="mt-4 flex flex-wrap gap-2">
                <?php foreach ($birthdays_today as $birthday): ?>
                    <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                        <?php echo $birthday['first_name'] . ' ' . $birthday['last_name']; ?>
                    </span>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Calendar View (Hidden by default) -->
    <div id="calendar-view" class="hidden mb-8">
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-calendar-alt mr-2 text-primary"></i>
                <?php echo $current_month . ' ' . $current_year; ?> Birthday Calendar
            </h3>
            <div class="grid grid-cols-7 gap-2">
                <!-- Calendar Header -->
                <div class="text-center font-semibold text-gray-600 p-2">Sun</div>
                <div class="text-center font-semibold text-gray-600 p-2">Mon</div>
                <div class="text-center font-semibold text-gray-600 p-2">Tue</div>
                <div class="text-center font-semibold text-gray-600 p-2">Wed</div>
                <div class="text-center font-semibold text-gray-600 p-2">Thu</div>
                <div class="text-center font-semibold text-gray-600 p-2">Fri</div>
                <div class="text-center font-semibold text-gray-600 p-2">Sat</div>

                <!-- Calendar Days -->
                <?php
                $first_day = date('w', strtotime($current_year . '-' . date('n') . '-01'));
                $days_in_month = date('t');

                // Empty cells for days before month starts
                for ($i = 0; $i < $first_day; $i++) {
                    echo '<div class="p-2"></div>';
                }

                // Days of the month
                for ($day = 1; $day <= $days_in_month; $day++) {
                    $has_birthday = isset($calendar_data[$day]);
                    $is_today = ($day == date('j') && date('n') == date('n'));

                    echo '<div class="p-2 border rounded-lg ' .
                         ($has_birthday ? 'bg-purple-100 border-purple-300' : 'border-gray-200') .
                         ($is_today ? ' ring-2 ring-blue-400' : '') . '">';
                    echo '<div class="text-center font-medium">' . $day . '</div>';

                    if ($has_birthday) {
                        echo '<div class="text-xs text-purple-600 mt-1 space-y-1">';
                        foreach ($calendar_data[$day] as $birthday) {
                            echo '<div class="truncate">';
                            echo '<a href="' . BASE_URL . 'members/view?id=' . $birthday['id'] . '" ';
                            echo 'class="hover:text-purple-800 hover:underline transition-colors duration-200 cursor-pointer" ';
                            echo 'title="' . htmlspecialchars($birthday['first_name'] . ' ' . $birthday['last_name']) . ' - Click to view profile">';
                            echo htmlspecialchars($birthday['first_name']);
                            echo '</a>';
                            echo '</div>';
                        }
                        echo '</div>';
                    }
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>

    <!-- Timeline View (Hidden by default) -->
    <div id="timeline-view" class="hidden mb-8">
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-xl font-semibold mb-6 flex items-center">
                <i class="fas fa-list mr-2 text-primary"></i>
                Birthday Timeline - <?php echo $current_month; ?>
            </h3>
            <div class="space-y-4">
                <?php if (!empty($timeline_data)): ?>
                    <?php foreach ($timeline_data as $date => $birthdays): ?>
                        <div class="border-l-4 border-primary pl-4">
                            <div class="flex items-center mb-2">
                                <div class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                    <?php echo date('M d', strtotime($date)); ?>
                                </div>
                                <span class="ml-3 text-gray-600"><?php echo count($birthdays); ?> birthday<?php echo count($birthdays) > 1 ? 's' : ''; ?></span>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                <?php foreach ($birthdays as $birthday): ?>
                                    <div class="bg-gray-50 p-3 rounded-lg flex items-center">
                                        <div class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center mr-3">
                                            <?php echo substr($birthday['first_name'], 0, 1) . substr($birthday['last_name'], 0, 1); ?>
                                        </div>
                                        <div>
                                            <div class="font-medium"><?php echo $birthday['first_name'] . ' ' . $birthday['last_name']; ?></div>
                                            <div class="text-sm text-gray-600">Age: <?php echo $birthday['age'] ?? 'N/A'; ?></div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-calendar-times text-4xl mb-4"></i>
                        <p>No birthdays found for timeline view</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Birthdays Grid -->
    <div id="grid-view" class="mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <?php if (empty($birthdays_this_month)): ?>
            <div class="col-span-full bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <div class="w-16 h-16 mx-auto bg-gray-500 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-birthday-cake text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">No Birthdays This Month</h3>
                <p class="text-gray-600 max-w-md mx-auto">There are no members with birthdays in <?php echo date('F'); ?>. Check back next month for upcoming celebrations!</p>
            </div>
        <?php else: ?>
            <?php foreach ($birthdays_this_month as $birthday): ?>
                <?php
                // Calculate days until birthday
                $birthday_this_year = date('Y') . '-' . date('m-d', strtotime($birthday['date_of_birth']));
                $days_until = ceil((strtotime($birthday_this_year) - time()) / (60 * 60 * 24));
                if ($days_until < 0) {
                    $days_until = ceil((strtotime(date('Y') + 1 . '-' . date('m-d', strtotime($birthday['date_of_birth']))) - time()) / (60 * 60 * 24));
                }

                // Determine card styling based on urgency - simplified
                $card_style = '';
                $badge_style = '';
                $badge_text = '';

                if ($days_until == 0) {
                    $card_style = 'bg-white border-orange-300';
                    $badge_style = 'bg-orange-500 text-white';
                    $badge_text = 'TODAY';
                } elseif ($days_until <= 7) {
                    $card_style = 'bg-white border-blue-200';
                    $badge_style = 'bg-blue-500 text-white';
                    $badge_text = $days_until . ' day' . ($days_until > 1 ? 's' : '');
                } else {
                    $card_style = 'bg-white border-gray-200';
                    $badge_style = 'bg-gray-500 text-white';
                    $badge_text = $days_until . ' days';
                }
                ?>

                <div class="birthday-card <?php echo $card_style; ?> rounded-lg shadow-sm border hover:shadow-md overflow-hidden">
                    <!-- Birthday Badge -->
                    <div class="relative">
                        <div class="absolute top-3 right-3 z-10">
                            <span class="<?php echo $badge_style; ?> px-2 py-1 rounded text-xs font-medium">
                                <?php echo $badge_text; ?>
                            </span>
                        </div>

                        <!-- Birthday Date -->
                        <div class="bg-gray-50 px-4 py-3 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-birthday-cake text-white text-xs"></i>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-800"><?php echo date('F j', strtotime($birthday['date_of_birth'])); ?></div>
                                    <div class="text-xs text-gray-600"><?php echo date('l', strtotime($birthday_this_year)); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Member Information -->
                    <div class="p-5">
                        <!-- Profile Section -->
                        <div class="flex items-center mb-4">
                            <?php if (!empty($birthday['profile_picture']) && file_exists($birthday['profile_picture'])): ?>
                                <img class="h-12 w-12 rounded-full object-cover border-2 border-gray-200"
                                     src="<?php echo BASE_URL . $birthday['profile_picture']; ?>"
                                     alt="<?php echo $birthday['first_name'] . ' ' . $birthday['last_name']; ?>">
                            <?php else: ?>
                                <div class="h-12 w-12 rounded-full bg-gray-500 flex items-center justify-center border-2 border-gray-200">
                                    <span class="text-white font-semibold text-sm"><?php echo substr($birthday['first_name'], 0, 1) . substr($birthday['last_name'], 0, 1); ?></span>
                                </div>
                            <?php endif; ?>
                            <div class="ml-3 flex-1">
                                <h3 class="font-semibold text-gray-900"><?php echo $birthday['first_name'] . ' ' . $birthday['last_name']; ?></h3>
                                <p class="text-sm text-gray-600"><?php echo ucfirst($birthday['department'] != 'none' ? str_replace('_', ' ', $birthday['department']) : 'Member'); ?></p>
                            </div>
                        </div>

                        <!-- Age and Contact Info -->
                        <div class="bg-gray-50 rounded p-3 mb-3">
                            <div class="flex justify-between items-center text-sm">
                                <div>
                                    <span class="text-gray-600">Turning</span>
                                    <span class="font-semibold text-gray-900 ml-1"><?php echo $birthday['age'] ?? 'N/A'; ?></span>
                                </div>
                                <a href="tel:<?php echo $birthday['phone_number']; ?>"
                                   class="text-gray-600 hover:text-gray-800 transition-colors">
                                    <?php echo $birthday['phone_number']; ?>
                                </a>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-2">
                            <a href="<?php echo BASE_URL; ?>birthdays/sms?recipient=<?php echo $birthday['id']; ?>"
                               class="flex-1 bg-gray-600 hover:bg-gray-700 text-white text-center py-2 px-3 rounded text-sm transition-colors flex items-center justify-center">
                                <i class="fas fa-sms mr-2"></i>
                                Send Wishes
                            </a>
                            <button onclick="markAsContacted(<?php echo $birthday['id']; ?>)"
                                    class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-3 rounded text-sm transition-colors"
                                    title="Mark as contacted">
                                <i class="fas fa-check"></i>
                            </button>
                            <a href="<?php echo BASE_URL; ?>members/view?id=<?php echo $birthday['id']; ?>"
                               class="bg-gray-400 hover:bg-gray-500 text-white py-2 px-3 rounded text-sm transition-colors"
                               title="View profile">
                                <i class="fas fa-user"></i>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
        </div>
    </div>
</div>

<script>
    // CSS class constants for better maintainability
    const CSS_CLASSES = {
        BUTTON_INACTIVE: 'px-4 py-2 bg-gray-200 text-gray-700 rounded-lg transition-colors hover:bg-gray-300',
        BUTTON_ACTIVE: 'px-4 py-2 bg-primary text-white rounded-lg transition-colors hover:bg-primary-dark'
    };

    // View switching functionality
    function switchView(viewType) {
        // Hide all views
        const views = ['grid-view', 'calendar-view', 'timeline-view'];
        const buttons = ['grid-view-btn', 'calendar-view-btn', 'timeline-view-btn'];

        views.forEach(view => {
            document.getElementById(view).classList.add('hidden');
        });

        // Reset all button styles
        buttons.forEach(button => {
            document.getElementById(button).className = CSS_CLASSES.BUTTON_INACTIVE;
        });

        // Show selected view and update button
        const viewElement = document.getElementById(viewType + '-view');
        const buttonElement = document.getElementById(viewType + '-view-btn');

        if (viewElement && buttonElement) {
            viewElement.classList.remove('hidden');
            buttonElement.className = CSS_CLASSES.BUTTON_ACTIVE;
        }
    }

    // Birthday data for export (extracted from PHP)
    const EXPORT_DATA = [
        <?php if (!empty($birthdays_this_month)): ?>
        <?php foreach ($birthdays_this_month as $birthday): ?>
        {
            name: "<?php echo addslashes($birthday['first_name'] . ' ' . $birthday['last_name']); ?>",
            birthday: "<?php echo date('F d', strtotime($birthday['date_of_birth'])); ?>",
            age: "<?php echo $birthday['age'] ?? 'N/A'; ?>",
            department: "<?php echo addslashes(ucfirst($birthday['department'] != 'none' ? str_replace('_', ' ', $birthday['department']) : 'No Department')); ?>",
            phone: "<?php echo $birthday['phone_number']; ?>"
        },
        <?php endforeach; ?>
        <?php endif; ?>
    ];

    // Export birthdays functionality
    function exportBirthdays() {
        try {
            if (EXPORT_DATA.length === 0) {
                showNotification('No birthday data to export', 'warning');
                return;
            }

            // Create CSV content
            const headers = ['Name', 'Birthday', 'Age', 'Department', 'Phone'];
            let csvContent = headers.join(',') + '\n';

            EXPORT_DATA.forEach(row => {
                const values = [row.name, row.birthday, row.age, row.department, row.phone];
                csvContent += values.map(value => `"${value}"`).join(',') + '\n';
            });

            // Create and download file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');

            link.href = url;
            link.download = `birthdays_<?php echo date('F_Y'); ?>.csv`;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            showNotification('Birthday list exported successfully!', 'success');

        } catch (error) {
            console.error('Export error:', error);
            showNotification('Failed to export birthday list', 'error');
        }
    }

    // Member data for JavaScript (extracted to avoid duplication)
    const BIRTHDAY_DATA = {
        allMemberIds: [
            <?php if (!empty($birthdays_this_month)): ?>
            <?php foreach ($birthdays_this_month as $birthday): ?>
            <?php echo $birthday['id']; ?>,
            <?php endforeach; ?>
            <?php endif; ?>
        ],
        todayMemberIds: [
            <?php if (!empty($birthdays_today)): ?>
            <?php foreach ($birthdays_today as $birthday): ?>
            <?php echo $birthday['id']; ?>,
            <?php endforeach; ?>
            <?php endif; ?>
        ],
        baseUrl: '<?php echo BASE_URL; ?>'
    };

    // Send bulk wishes functionality
    function sendBulkWishes() {
        const memberIds = BIRTHDAY_DATA.allMemberIds;

        if (memberIds.length === 0) {
            showNotification('No members to send wishes to', 'warning');
            return;
        }

        if (confirm(`Send birthday wishes to all ${memberIds.length} members?`)) {
            window.location.href = `${BIRTHDAY_DATA.baseUrl}birthdays/sms?recipients=${memberIds.join(',')}`;
        }
    }

    // Send today's wishes functionality
    function sendTodayWishes() {
        const todayIds = BIRTHDAY_DATA.todayMemberIds;

        if (todayIds.length === 0) {
            showNotification('No birthdays today', 'warning');
            return;
        }

        const pluralText = todayIds.length > 1 ? 's' : '';
        if (confirm(`Send birthday wishes to today's ${todayIds.length} celebrant${pluralText}?`)) {
            window.location.href = `${BIRTHDAY_DATA.baseUrl}birthdays/sms?recipients=${todayIds.join(',')}`;
        }
    }

    // Mark as contacted functionality
    function markAsContacted(memberId) {
        try {
            if (!memberId) {
                throw new Error('Member ID is required');
            }

            const button = event.target.closest('button');
            if (!button) {
                throw new Error('Button element not found');
            }

            // Update button appearance
            button.innerHTML = '<i class="fas fa-check-circle"></i>';
            button.className = 'bg-green-500 text-white py-2 px-3 rounded text-sm';
            button.disabled = true;
            button.title = 'Already contacted';

            showNotification('Marked as contacted!', 'success');

            // TODO: Implement AJAX call to update database
            // fetch(`${BIRTHDAY_DATA.baseUrl}api/mark-contacted`, { ... })

        } catch (error) {
            console.error('Error marking as contacted:', error);
            showNotification('Failed to mark as contacted', 'error');
        }
    }

    // Notification system configuration
    const NOTIFICATION_CONFIG = {
        types: {
            success: { bg: 'bg-green-500', icon: 'check' },
            error: { bg: 'bg-red-500', icon: 'times' },
            warning: { bg: 'bg-yellow-500', icon: 'exclamation-triangle' },
            info: { bg: 'bg-blue-500', icon: 'info' }
        },
        duration: 3000,
        animationDelay: 100
    };

    // Improved notification function
    function showNotification(message, type = 'info') {
        try {
            if (!message) {
                console.warn('Notification message is required');
                return;
            }

            const config = NOTIFICATION_CONFIG.types[type] || NOTIFICATION_CONFIG.types.info;
            const notification = document.createElement('div');

            notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transition-all duration-300 transform translate-x-full ${config.bg}`;
            notification.setAttribute('role', 'alert');
            notification.setAttribute('aria-live', 'polite');

            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${config.icon} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, NOTIFICATION_CONFIG.animationDelay);

            // Auto remove
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }
            }, NOTIFICATION_CONFIG.duration);

        } catch (error) {
            console.error('Notification error:', error);
        }
    }
</script>

<style>
    /* Custom animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-up {
        animation: fadeInUp 0.6s ease forwards;
        opacity: 0;
    }

    /* Enhanced Birthday Card Styles */
    .birthday-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .birthday-card:hover {
        transform: translateY(-8px) scale(1.02);
    }
    .birthday-card .profile-image {
        transition: transform 0.3s ease;
    }
    .birthday-card:hover .profile-image {
        transform: scale(1.1);
    }
    .birthday-badge {
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }
    .backdrop-blur-sm {
        backdrop-filter: blur(4px);
    }
    .border-3 {
        border-width: 3px;
    }

    /* Hover scale for cards */
    .hover\:scale-102:hover {
        transform: scale(1.02);
    }

    /* Print styles */
    @media print {
        body {
            background-color: white;
        }
        .container {
            max-width: 100%;
            padding: 0;
        }
        header, footer, .bg-gradient-to-r, a[onclick="window.print()"] {
            display: none;
        }
        .grid {
            display: block;
        }
        .grid > div {
            margin-bottom: 20px;
            page-break-inside: avoid;
            break-inside: avoid;
        }
    }
</style>
