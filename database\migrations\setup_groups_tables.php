<?php
// Define application root directory
define('APP_ROOT', dirname(dirname(__DIR__)));

/**
 * Setup Groups Tables
 * This script creates the necessary database tables for the group management feature
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load database configuration
require_once APP_ROOT . '/config/database.php';

// Create database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>Setting Up Group Management Tables</h1>";

// SQL statements to create tables
$sql = [
    // Groups Table
    "CREATE TABLE IF NOT EXISTS `groups` (
        `group_id` INT PRIMARY KEY AUTO_INCREMENT,
        `group_name` VARCHAR(100) NOT NULL,
        `group_description` TEXT,
        `group_type_id` INT,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        `status` ENUM('active', 'inactive') DEFAULT 'active',
        `parent_group_id` INT DEFAULT NULL
    )",

    // Group Types Table
    "CREATE TABLE IF NOT EXISTS `group_types` (
        `group_type_id` INT PRIMARY KEY AUTO_INCREMENT,
        `type_name` VARCHAR(50) NOT NULL,
        `type_description` TEXT
    )",

    // Member-Group Relationship Table
    "CREATE TABLE IF NOT EXISTS `member_groups` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `member_id` INT NOT NULL,
        `group_id` INT NOT NULL,
        `role_in_group` ENUM('leader', 'member', 'secretary', 'treasurer') DEFAULT 'member',
        `joined_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `status` ENUM('active', 'inactive') DEFAULT 'active',
        FOREIGN KEY (`member_id`) REFERENCES `members`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`group_id`) REFERENCES `groups`(`group_id`) ON DELETE CASCADE
    )"
];

// Execute SQL statements
foreach ($sql as $query) {
    try {
        $stmt = $conn->prepare($query);
        $stmt->execute();
        echo "<p style='color: green;'>✓ Successfully executed: " . substr($query, 0, 50) . "...</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Error executing query: " . $e->getMessage() . "</p>";
        echo "<pre>" . $query . "</pre>";
    }
}

// Insert default group types
$groupTypes = [
    ['Ministry', 'Church ministries such as worship, children, youth, etc.'],
    ['Committee', 'Administrative committees for church operations'],
    ['Bible Study', 'Groups focused on Bible study and spiritual growth'],
    ['Choir', 'Music and worship teams'],
    ['Outreach', 'Evangelism and community outreach groups'],
    ['Fellowship', 'Social and fellowship groups'],
    ['Department', 'Operational departments within the church']
];

echo "<h2>Adding Default Group Types</h2>";

// Check if group types already exist
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM group_types");
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result['count'] > 0) {
    echo "<p style='color: blue;'>ℹ Group types already exist. Skipping insertion.</p>";
} else {
    // Insert group types
    $insertSql = "INSERT INTO `group_types` (`type_name`, `type_description`) VALUES (?, ?)";
    $stmt = $conn->prepare($insertSql);
    
    foreach ($groupTypes as $type) {
        try {
            $stmt->execute($type);
            echo "<p style='color: green;'>✓ Added group type: " . $type[0] . "</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Error adding group type: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<h2>Setup Complete</h2>";
echo "<p>The group management tables have been set up successfully.</p>";
echo "<p><a href='/icgc/groups' class='bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded'>Go to Groups Management</a></p>";
?>
