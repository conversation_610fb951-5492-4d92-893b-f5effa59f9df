<?php
/**
 * Custom Finance Category Controller
 *
 * Handles CRUD operations for custom finance categories.
 * Uses service layer for business logic and proper error handling.
 *
 * @package Controllers
 * <AUTHOR> Finance System
 * @version 2.0.0
 */

require_once 'services/CategoryService.php';
require_once 'services/SecurityService.php';
require_once 'exceptions/CategoryException.php';
require_once 'exceptions/ValidationException.php';
require_once 'exceptions/SecurityException.php';
require_once 'config/Config.php';

class CustomFinanceCategoryController {
    /**
     * @var CategoryService Category service instance
     */
    private $categoryService;

    /**
     * @var SecurityService Security service instance
     */
    private $securityService;

    /**
     * @var PDO Database connection
     */
    private $db;

    /**
     * CustomFinanceCategoryController constructor
     */
    public function __construct() {
        try {
            // Get database connection first
            $database = new Database();
            $this->db = $database->getConnection();

            // Try to load configuration (optional)
            try {
                if (class_exists('Config')) {
                    Config::load();
                }
            } catch (Exception $configError) {
                error_log("Config loading failed: " . $configError->getMessage());
                // Continue without config
            }

            // Initialize services with error handling
            try {
                if (class_exists('CategoryService')) {
                    $this->categoryService = new CategoryService($this->db);
                }
            } catch (Exception $serviceError) {
                error_log("CategoryService initialization failed: " . $serviceError->getMessage());
                // Continue without service
            }

            try {
                if (class_exists('SecurityService')) {
                    $this->securityService = new SecurityService();
                }
            } catch (Exception $securityError) {
                error_log("SecurityService initialization failed: " . $securityError->getMessage());
                // Continue without security service
            }

            // Initialize the CustomFinanceCategory model (required for CRUD operations)
            try {
                require_once 'models/CustomFinanceCategory.php';
                $this->customCategory = new CustomFinanceCategory($this->db);
            } catch (Exception $modelError) {
                error_log("CustomFinanceCategory model initialization failed: " . $modelError->getMessage());
                // This is critical, so we need to handle it
                set_flash_message("Category management is temporarily unavailable. Please try again.", 'danger');
                redirect('finance');
            }

        } catch (Exception $e) {
            error_log("CustomFinanceCategoryController initialization failed: " . $e->getMessage());
            // Don't call handleError here as it might cause more issues
            set_flash_message("System initialization failed. Please try again.", 'danger');
            redirect('finance');
        }
    }

    /**
     * Display custom categories management page
     *
     * @return void
     */
    public function index(): void {
        try {
            // Check authentication and authorization
            $this->checkAdminAccess();

            // Set page title and active page
            $page_title = 'Manage Finance Categories - ICGC Emmanuel Temple';
            $active_page = 'finance';

            // Get all custom categories (including inactive ones for management)
            if (isset($this->categoryService)) {
                $memberPaymentCategories = $this->categoryService->getCategoriesByType('member_payments', false);
                $generalIncomeCategories = $this->categoryService->getCategoriesByType('general_income', false);
                $expenseCategories = $this->categoryService->getCategoriesByType('expenses', false);
            } else {
                // Fallback to direct database queries if service is not available
                require_once 'models/CustomFinanceCategory.php';
                $customCategory = new CustomFinanceCategory($this->db);
                $memberPaymentCategories = $customCategory->getByType('member_payments', false);
                $generalIncomeCategories = $customCategory->getByType('general_income', false);
                $expenseCategories = $customCategory->getByType('expenses', false);
            }

            // Get available icons
            require_once 'config/finance_categories.php';
            $availableIcons = FinanceCategoriesConfig::getAvailableIcons();

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/finance_categories/index.php';

        } catch (SecurityException $e) {
            $this->handleSecurityError($e);
        } catch (Exception $e) {
            error_log("CustomFinanceCategoryController::index failed: " . $e->getMessage());
            $this->handleError("Failed to load categories page", $e);
        }

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store new custom category
     *
     * @return void
     */
    public function store(): void {
        try {
            // Validate request method
            $this->validateRequestMethod('POST');

            // Check authentication and authorization
            $this->checkAdminAccess();

            // Get and sanitize input data
            $data = $this->getCreateCategoryData();

            // Validate that name is not empty
            if (empty($data['name']) || trim($data['name']) === '') {
                throw new Exception('Category name is required and cannot be empty');
            }

            // Create category using service or fallback to model
            if (isset($this->categoryService)) {
                $categoryId = $this->categoryService->createCategory($data);
            } else {
                // Check if customCategory is available
                if (!isset($this->customCategory) || $this->customCategory === null) {
                    throw new Exception('CustomFinanceCategory model is not initialized');
                }

                // Fallback to direct model usage
                $this->customCategory->name = $data['name'];
                $this->customCategory->label = $data['label'];
                $this->customCategory->description = $data['description'];
                $this->customCategory->category_type = $data['category_type'];
                $this->customCategory->icon = $data['icon'];
                $this->customCategory->requires_member = $data['requires_member'];
                $this->customCategory->is_active = 1;
                $this->customCategory->is_core = 0;
                $this->customCategory->slug = $this->generateSlug($data['name']);
                $this->customCategory->dashboard_route = $this->generateDashboardRoute($data['name'], $data['category_type']);
                $this->customCategory->created_by = $_SESSION['user_id'] ?? 1; // Set created_by to current user or default to 1

                if ($this->customCategory->create()) {
                    $categoryId = $this->customCategory->id;
                } else {
                    throw new Exception('Failed to create category - create() method returned false');
                }
            }

            // Set success message
            set_flash_message('Custom category created successfully!', 'success');

        } catch (SecurityException $e) {
            $this->handleSecurityError($e);
        } catch (ValidationException $e) {
            $_SESSION['errors'] = $e->getValidationErrors();
            $_SESSION['form_data'] = $_POST;
            set_flash_message($e->getUserMessage(), 'danger');
        } catch (CategoryException $e) {
            set_flash_message($e->getUserMessage(), 'danger');
        } catch (Exception $e) {
            error_log("CustomFinanceCategoryController::store failed: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            set_flash_message('An error occurred while creating the category', 'danger');
        }

        redirect('finance/categories');
    }

    /**
     * Update custom category
     */
    public function update() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('finance/categories');
            return;
        }

        // Check if user is admin
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
            set_flash_message('Access denied. Admin privileges required.', 'danger');
            redirect('dashboard');
            return;
        }

        try {
            $id = sanitize($_POST['id']);
            
            // Validate input
            $errors = $this->customCategory->validate($_POST);

            if (!empty($errors)) {
                $_SESSION['errors'] = $errors;
                $_SESSION['form_data'] = $_POST;
                redirect('finance/categories');
                return;
            }

            // Set category properties
            $this->customCategory->id = $id;
            $this->customCategory->name = sanitize($_POST['name']);
            $this->customCategory->label = sanitize($_POST['label']);
            $this->customCategory->icon = sanitize($_POST['icon']);
            $this->customCategory->description = sanitize($_POST['description']);
            $this->customCategory->category_type = sanitize($_POST['category_type']);
            $this->customCategory->requires_member = isset($_POST['requires_member']) ? 1 : 0;
            $this->customCategory->sort_order = sanitize($_POST['sort_order']);
            $this->customCategory->is_active = isset($_POST['is_active']) ? 1 : 0;

            // Update category
            if ($this->customCategory->update()) {
                set_flash_message('Custom category updated successfully!', 'success');
            } else {
                throw new Exception('Failed to update custom category');
            }

        } catch (Exception $e) {
            set_flash_message('Error: ' . $e->getMessage(), 'danger');
        }

        redirect('finance/categories');
    }

    /**
     * Delete custom category
     */
    public function delete() {
        // Check if user is admin
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
            set_flash_message('Access denied. Admin privileges required.', 'danger');
            redirect('dashboard');
            return;
        }

        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Category ID is required', 'danger');
            redirect('finance/categories');
            return;
        }

        try {
            $id = sanitize($_GET['id']);
            $this->customCategory->id = $id;

            // Check if category exists
            $category = $this->customCategory->getById($id);
            if (!$category) {
                set_flash_message('Category not found', 'danger');
                redirect('finance/categories');
                return;
            }

            // Delete the category (model will handle data integrity checks)
            $deleteResult = $this->customCategory->delete();

            if ($deleteResult) {
                // Check if there's an error message (indicates soft delete due to transactions)
                if ($this->customCategory->error) {
                    set_flash_message($this->customCategory->error, 'warning');
                } else {
                    set_flash_message('Custom category deleted successfully!', 'success');
                }
            } else {
                // Get error message from model
                $errorMessage = $this->customCategory->error ?? 'Failed to delete custom category. Please try again.';
                set_flash_message($errorMessage, 'danger');
            }

        } catch (Exception $e) {
            set_flash_message('Error: ' . $e->getMessage(), 'danger');
        }

        redirect('finance/categories');
    }

    /**
     * Get category data for AJAX requests
     */
    public function getCategory() {
        // Check if user is admin
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        if (!isset($_GET['id']) || empty($_GET['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Category ID is required']);
            return;
        }

        try {
            $id = sanitize($_GET['id']);
            $category = $this->customCategory->getById($id);

            if (!$category) {
                http_response_code(404);
                echo json_encode(['error' => 'Category not found']);
                return;
            }

            header('Content-Type: application/json');
            echo json_encode($category);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    /**
     * Toggle category status (active/inactive)
     */
    public function toggleStatus() {
        // Check if user is admin
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $id = sanitize($input['id']);

            $category = $this->customCategory->getById($id);
            if (!$category) {
                http_response_code(404);
                echo json_encode(['error' => 'Category not found']);
                return;
            }

            // Toggle status
            $this->customCategory->id = $id;
            $this->customCategory->is_active = $category->is_active ? 0 : 1;
            
            // Update only the status
            $query = "UPDATE custom_finance_categories SET is_active = :is_active, updated_at = CURRENT_TIMESTAMP WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':is_active', $this->customCategory->is_active);
            $stmt->bindParam(':id', $this->customCategory->id);

            if ($stmt->execute()) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'is_active' => (bool)$this->customCategory->is_active,
                    'message' => 'Category status updated successfully'
                ]);
            } else {
                throw new Exception('Failed to update category status');
            }

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    /**
     * Check admin access
     *
     * @return void
     * @throws SecurityException If access denied
     */
    private function checkAdminAccess(): void {
        // For now, allow any logged-in user to access categories
        // TODO: Implement proper role-based access control
        if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
            throw new SecurityException('Access denied. Please log in to continue.');
        }

        // Temporarily comment out admin-only restriction
        // if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        //     throw new SecurityException('Access denied. Admin privileges required.');
        // }
    }

    /**
     * Validate request method
     *
     * @param string $expectedMethod Expected HTTP method
     * @return void
     * @throws SecurityException If method is invalid
     */
    private function validateRequestMethod(string $expectedMethod): void {
        if ($_SERVER['REQUEST_METHOD'] !== $expectedMethod) {
            throw new SecurityException("Invalid request method. Expected {$expectedMethod}.");
        }
    }

    /**
     * Validate CSRF token
     *
     * @return void
     * @throws SecurityException If CSRF token is invalid
     */
    private function validateCSRFToken(): void {
        $token = $_POST['csrf_token'] ?? '';
        if (!$this->securityService->validateCSRFToken($token)) {
            throw new SecurityException('Invalid CSRF token.');
        }
    }

    /**
     * Get and sanitize category creation data
     *
     * @return array Sanitized category data
     */
    private function getCreateCategoryData(): array {
        $rawData = [
            'name' => $_POST['name'] ?? '',
            'label' => $_POST['label'] ?? '',
            'description' => $_POST['description'] ?? '',
            'category_type' => $_POST['category_type'] ?? '',
            'icon' => $_POST['icon'] ?? '',
            'requires_member' => isset($_POST['requires_member'])
        ];

        // Use security service if available, otherwise sanitize manually
        if (isset($this->securityService)) {
            return $this->securityService->sanitizeCategoryData($rawData);
        } else {
            // Fallback manual sanitization
            return [
                'name' => sanitize($rawData['name']),
                'label' => sanitize($rawData['label']),
                'description' => sanitize($rawData['description']),
                'category_type' => sanitize($rawData['category_type']),
                'icon' => sanitize($rawData['icon']),
                'requires_member' => (bool)$rawData['requires_member']
            ];
        }
    }

    /**
     * Send JSON response
     *
     * @param array $data Response data
     * @param int $statusCode HTTP status code
     * @return void
     */
    private function sendJsonResponse(array $data, int $statusCode = 200): void {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Handle security errors
     *
     * @param SecurityException $e Security exception
     * @return void
     */
    private function handleSecurityError(SecurityException $e): void {
        // Log security event (only if security service is available)
        if (isset($this->securityService)) {
            try {
                $this->securityService->logSecurityEvent($e->getMessage(), $e->getContext());
            } catch (Exception $logError) {
                error_log("Security logging failed: " . $logError->getMessage());
            }
        }

        // Check if it's an AJAX request
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->sendJsonResponse([
                'success' => false,
                'message' => $e->getUserMessage()
            ], 403);
        } else {
            set_flash_message($e->getUserMessage(), 'danger');
            // Redirect to finance page instead of dashboard to avoid potential loops
            redirect('finance');
        }
    }

    /**
     * Handle general errors
     *
     * @param string $message Error message
     * @param Exception $e Exception
     * @return void
     */
    private function handleError(string $message, Exception $e): void {
        // Check if it's an AJAX request
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->sendJsonResponse([
                'success' => false,
                'message' => $message
            ], 500);
        } else {
            set_flash_message($message, 'danger');
            redirect('finance/categories');
        }
    }

    /**
     * Generate slug from category name
     *
     * @param string $name Category name
     * @return string Generated slug
     */
    private function generateSlug(string $name): string {
        return strtolower(str_replace(' ', '_', trim($name)));
    }

    /**
     * Generate dashboard route for category
     *
     * @param string $name Category name
     * @param string $type Category type
     * @return string Dashboard route
     */
    private function generateDashboardRoute(string $name, string $type): string {
        $slug = $this->generateSlug($name);
        $routeType = ($type === 'expenses') ? 'expense' : 'income';
        return "finance/dashboard/category?category={$slug}&type={$routeType}";
    }
}
