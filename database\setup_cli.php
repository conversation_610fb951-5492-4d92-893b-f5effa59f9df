<?php
/**
 * Command Line Interface for Database Setup
 * ICGC Emmanuel Temple Church Management System
 * 
 * Usage: php database/setup_cli.php [options]
 * 
 * Options:
 *   --host=hostname     Database host (default: localhost)
 *   --name=dbname       Database name (default: icgc_db)
 *   --user=username     Database username (default: root)
 *   --pass=password     Database password (default: empty)
 *   --help              Display this help message
 */

// Parse command line arguments
$options = getopt('', ['host::', 'name::', 'user::', 'pass::', 'help']);

// Show help if requested
if (isset($options['help'])) {
    echo "ICGC Emmanuel Temple Database Setup\n";
    echo "===================================\n\n";
    echo "Usage: php database/setup_cli.php [options]\n\n";
    echo "Options:\n";
    echo "  --host=hostname     Database host (default: localhost)\n";
    echo "  --name=dbname       Database name (default: icgc_db)\n";
    echo "  --user=username     Database username (default: root)\n";
    echo "  --pass=password     Database password (default: empty)\n";
    echo "  --help              Display this help message\n";
    exit(0);
}

// Set default values
$_POST = [
    'db_host' => 'localhost',
    'db_name' => 'icgc_db',
    'db_username' => 'root',
    'db_password' => ''
];

// Override with command line options if provided
if (isset($options['host'])) $_POST['db_host'] = $options['host'];
if (isset($options['name'])) $_POST['db_name'] = $options['name'];
if (isset($options['user'])) $_POST['db_username'] = $options['user'];
if (isset($options['pass'])) $_POST['db_password'] = $options['pass'];

// Display banner
echo "\n";
echo "╔═══════════════════════════════════════════════════════════╗\n";
echo "║                                                           ║\n";
echo "║             ICGC EMMANUEL TEMPLE DATABASE SETUP           ║\n";
echo "║                                                           ║\n";
echo "╚═══════════════════════════════════════════════════════════╝\n\n";

echo "Setting up database with the following configuration:\n";
echo "Host: " . $_POST['db_host'] . "\n";
echo "Database: " . $_POST['db_name'] . "\n";
echo "Username: " . $_POST['db_username'] . "\n";
echo "Password: " . (empty($_POST['db_password']) ? "(empty)" : "(provided)") . "\n\n";

echo "Starting database setup...\n\n";

// Define custom output functions for CLI
function cli_success($message) {
    echo "\033[32m✓ $message\033[0m\n";
}

function cli_error($message) {
    echo "\033[31m✗ $message\033[0m\n";
}

// Capture output from create_db.php
ob_start();
include __DIR__ . '/create_db.php';
ob_end_clean();

// Display results
if (isset($results)) {
    foreach ($results['messages'] as $message) {
        // Remove HTML tags
        $message = strip_tags($message);
        cli_success($message);
    }
    
    foreach ($results['errors'] as $error) {
        cli_error($error);
    }
    
    echo "\n";
    if ($results['success']) {
        echo "Database setup completed successfully!\n";
        echo "You can now access the application at http://localhost/icgc/\n";
        echo "Default admin credentials: username 'admin', password 'admin123'\n";
    } else {
        echo "Database setup encountered errors. Please fix the issues and try again.\n";
        exit(1);
    }
} else {
    cli_error("An unknown error occurred during database setup.");
    exit(1);
}

exit(0);
