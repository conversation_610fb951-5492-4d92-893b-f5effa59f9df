<?php
/**
 * QR Session Analytics Dashboard
 */

// Start session and check authentication
session_start();

// Include necessary files
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/models/AttendanceQrSession.php';
require_once dirname(__DIR__) . '/helpers/functions.php';

// Simple auth check (you can enhance this)
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // For testing
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get QR session statistics
    $stats_query = "SELECT 
                        COUNT(*) as total_sessions,
                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_sessions,
                        SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_sessions,
                        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_sessions,
                        SUM(CASE WHEN status = 'archived' THEN 1 ELSE 0 END) as archived_sessions,
                        SUM(attendance_count) as total_qr_attendance,
                        AVG(attendance_count) as avg_attendance_per_session
                    FROM attendance_qr_sessions";
    
    $stmt = $conn->prepare($stats_query);
    $stmt->execute();
    $overall_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get recent session activity
    $recent_activity_query = "SELECT 
                                qs.*,
                                s.name as service_name,
                                u.username as created_by_name,
                                qs.attendance_count
                              FROM attendance_qr_sessions qs
                              LEFT JOIN services s ON qs.service_id = s.id
                              LEFT JOIN users u ON qs.created_by = u.id
                              WHERE qs.status != 'archived'
                              ORDER BY qs.created_at DESC
                              LIMIT 10";
    
    $stmt = $conn->prepare($recent_activity_query);
    $stmt->execute();
    $recent_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get sessions expiring soon
    $expiring_query = "SELECT 
                          qs.*,
                          s.name as service_name,
                          TIMESTAMPDIFF(MINUTE, NOW(), qs.expires_at) as minutes_left
                       FROM attendance_qr_sessions qs
                       LEFT JOIN services s ON qs.service_id = s.id
                       WHERE qs.status = 'active'
                       AND qs.expires_at <= DATE_ADD(NOW(), INTERVAL 2 HOUR)
                       ORDER BY qs.expires_at ASC";
    
    $stmt = $conn->prepare($expiring_query);
    $stmt->execute();
    $expiring_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get QR vs Manual attendance comparison
    $attendance_comparison_query = "SELECT 
                                      marked_via,
                                      COUNT(*) as count,
                                      DATE(created_at) as date
                                    FROM attendance 
                                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                                    GROUP BY marked_via, DATE(created_at)
                                    ORDER BY date DESC";
    
    $stmt = $conn->prepare($attendance_comparison_query);
    $stmt->execute();
    $attendance_trends = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
}

// Helper functions
if (!function_exists('format_date')) {
    function format_date($date) {
        return date('M j, Y', strtotime($date));
    }
}

if (!function_exists('time_ago')) {
    function time_ago($datetime) {
        $time = time() - strtotime($datetime);
        if ($time < 60) return 'Just now';
        if ($time < 3600) return floor($time/60) . ' min ago';
        if ($time < 86400) return floor($time/3600) . ' hr ago';
        return floor($time/86400) . ' days ago';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Session Analytics - ICGC</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">QR Session Analytics Dashboard</h1>
            <p class="text-gray-600 mt-2">Monitor and analyze QR attendance system performance</p>
        </div>
        
        <?php if (isset($error_message)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                Error: <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Overall Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-qrcode text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800"><?php echo $overall_stats['total_sessions'] ?? 0; ?></h3>
                        <p class="text-gray-600">Total Sessions</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-play-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800"><?php echo $overall_stats['active_sessions'] ?? 0; ?></h3>
                        <p class="text-gray-600">Active Sessions</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800"><?php echo $overall_stats['total_qr_attendance'] ?? 0; ?></h3>
                        <p class="text-gray-600">QR Attendance</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800"><?php echo number_format($overall_stats['avg_attendance_per_session'] ?? 0, 1); ?></h3>
                        <p class="text-gray-600">Avg per Session</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Alerts Section -->
        <?php if (!empty($expiring_sessions)): ?>
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            <strong>Warning:</strong> <?php echo count($expiring_sessions); ?> session(s) expiring soon
                        </p>
                        <div class="mt-2">
                            <?php foreach ($expiring_sessions as $session): ?>
                                <div class="text-xs text-yellow-600">
                                    • <?php echo htmlspecialchars($session['service_name']); ?> 
                                    (expires in <?php echo max(0, $session['minutes_left']); ?> minutes)
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Sessions -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800">Recent QR Sessions</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <?php if (empty($recent_sessions)): ?>
                            <p class="text-gray-500 text-center py-4">No recent sessions found</p>
                        <?php else: ?>
                            <?php foreach (array_slice($recent_sessions, 0, 5) as $session): ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <h4 class="font-medium text-gray-800"><?php echo htmlspecialchars($session['service_name'] ?? 'Unknown'); ?></h4>
                                        <p class="text-sm text-gray-600"><?php echo format_date($session['attendance_date']); ?></p>
                                        <p class="text-xs text-gray-500"><?php echo time_ago($session['created_at']); ?></p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-semibold text-blue-600"><?php echo $session['attendance_count'] ?? 0; ?></div>
                                        <div class="text-xs text-gray-500">attendees</div>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            <?php 
                                            if ($session['status'] === 'active' && strtotime($session['expires_at']) > time()) {
                                                echo 'bg-green-100 text-green-800';
                                            } elseif ($session['status'] === 'closed') {
                                                echo 'bg-gray-100 text-gray-800';
                                            } else {
                                                echo 'bg-red-100 text-red-800';
                                            }
                                            ?>">
                                            <?php echo ucfirst($session['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Session Status Distribution -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800">Session Status Distribution</h2>
                </div>
                <div class="p-6">
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Quick Actions</h2>
            <div class="flex flex-wrap gap-4">
                <button onclick="runCleanup()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    <i class="fas fa-broom mr-2"></i>Run Cleanup
                </button>
                <a href="../attendance/qr" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 inline-block">
                    <i class="fas fa-qrcode mr-2"></i>Manage QR Sessions
                </a>
                <button onclick="exportData()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                    <i class="fas fa-download mr-2"></i>Export Analytics
                </button>
            </div>
        </div>
    </div>

    <script>
        // Create status distribution chart
        const ctx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Active', 'Expired', 'Closed', 'Archived'],
                datasets: [{
                    data: [
                        <?php echo $overall_stats['active_sessions'] ?? 0; ?>,
                        <?php echo $overall_stats['expired_sessions'] ?? 0; ?>,
                        <?php echo $overall_stats['closed_sessions'] ?? 0; ?>,
                        <?php echo $overall_stats['archived_sessions'] ?? 0; ?>
                    ],
                    backgroundColor: [
                        '#10B981', // Green for active
                        '#EF4444', // Red for expired
                        '#6B7280', // Gray for closed
                        '#8B5CF6'  // Purple for archived
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        function runCleanup() {
            if (confirm('Run QR session cleanup? This will mark expired sessions and update counts.')) {
                // You can implement an AJAX call to run the cleanup script
                alert('Cleanup functionality would be implemented here');
            }
        }

        function exportData() {
            // You can implement data export functionality
            alert('Export functionality would be implemented here');
        }

        // Auto-refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
