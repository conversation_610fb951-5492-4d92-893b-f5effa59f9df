# ICGC Church Management System - Environment Configuration
# Copy this file to .env and update with your actual values

# Application Settings
APP_ENV=production
APP_DEBUG=false
APP_NAME="ICGC Emmanuel Temple"
APP_URL=https://your-domain.com

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=icgc_db
DB_USER=your_db_user
DB_PASS=your_secure_password

# Security Settings
SESSION_LIFETIME=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# Email Configuration (Optional)
MAIL_ENABLED=false
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="ICGC Emmanuel Temple"

# File Upload Settings
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Logging Settings
LOG_LEVEL=warning
LOG_FILE=logs/app.log
AUDIT_ENABLED=true

# Cache Settings
CACHE_ENABLED=false
CACHE_TTL=3600

# Backup Settings
BACKUP_ENABLED=true
BACKUP_FREQUENCY=daily
