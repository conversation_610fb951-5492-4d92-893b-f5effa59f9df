<?php
/**
 * Centralized Attendance Routing Service
 * 
 * This service provides a single, consistent way to route attendance records
 * to the appropriate table (attendance vs children_checkin_log) based on
 * member age and system configuration.
 * 
 * SOLVES: Architecture problem where attendance records were going to wrong tables
 */

class AttendanceRouter {
    private $conn;
    private $childrenMinistry;
    private $max_child_age;
    
    // Cache for member classifications to avoid repeated database calls
    private $member_cache = [];
    
    public function __construct($database_connection) {
        if (!$database_connection) {
            throw new Exception("Database connection is required");
        }

        $this->conn = $database_connection;

        try {
            // Initialize children ministry for age threshold
            require_once 'models/ChildrenMinistry.php';
            $this->childrenMinistry = new ChildrenMinistry($this->conn);
            $this->max_child_age = $this->childrenMinistry->getMaxChildAge();
        } catch (Exception $e) {
            // Fallback to default age if children ministry fails
            $this->max_child_age = 17;
            error_log("AttendanceRouter: Failed to get max_child_age, using default 17: " . $e->getMessage());
        }
    }
    
    /**
     * Route attendance record to appropriate table
     * 
     * @param array $member Member data with id, date_of_birth
     * @param array $attendance_data Service, date, status, etc.
     * @param string $source Source of attendance (qr_individual, qr_family, manual, etc.)
     * @return array Result with success status and details
     */
    public function routeAttendance($member, $attendance_data, $source = 'unknown') {
        try {
            // Determine member classification
            $classification = $this->classifyMember($member);
            
            // Route based on classification
            if ($classification['is_child']) {
                return $this->routeToChildrenMinistry($member, $attendance_data, $source, $classification);
            } else {
                return $this->routeToMainAttendance($member, $attendance_data, $source, $classification);
            }
            
        } catch (Exception $e) {
            // Fallback to main attendance on any error
            error_log("AttendanceRouter Error: " . $e->getMessage() . " - Falling back to main attendance");
            return $this->routeToMainAttendance($member, $attendance_data, $source . '_fallback');
        }
    }
    
    /**
     * Classify member as child or adult with detailed information
     * 
     * @param array $member Member data
     * @return array Classification details
     */
    public function classifyMember($member) {
        $member_id = $member['id'];
        
        // Check cache first
        if (isset($this->member_cache[$member_id])) {
            return $this->member_cache[$member_id];
        }
        
        $classification = [
            'member_id' => $member_id,
            'age' => null,
            'is_child' => false,
            'route_to' => 'attendance',
            'reason' => 'default_adult',
            'max_child_age' => $this->max_child_age
        ];
        
        // Calculate age if date of birth is available
        if (!empty($member['date_of_birth'])) {
            try {
                $birthDate = new DateTime($member['date_of_birth']);
                $today = new DateTime();
                $age = $today->diff($birthDate)->y;
                
                $classification['age'] = $age;
                
                if ($age <= $this->max_child_age) {
                    $classification['is_child'] = true;
                    $classification['route_to'] = 'children_checkin_log';
                    $classification['reason'] = "age_{$age}_under_threshold_{$this->max_child_age}";
                } else {
                    $classification['reason'] = "age_{$age}_over_threshold_{$this->max_child_age}";
                }
                
            } catch (Exception $e) {
                $classification['reason'] = 'invalid_date_of_birth';
                error_log("Age calculation failed for member {$member_id}: " . $e->getMessage());
            }
        } else {
            $classification['reason'] = 'no_date_of_birth';
        }
        
        // Cache the result
        $this->member_cache[$member_id] = $classification;
        
        return $classification;
    }
    
    /**
     * Route attendance to children ministry system
     */
    private function routeToChildrenMinistry($member, $attendance_data, $source, $classification) {
        try {
            require_once 'models/ChildrenCheckinLog.php';
            $checkinLog = new ChildrenCheckinLog($this->conn);
            
            // Check for existing record
            $existing = $checkinLog->getByChildAndDate(
                $member['id'],
                $attendance_data['attendance_date'],
                $attendance_data['service_id']
            );
            
            if ($existing) {
                // Update existing record
                $checkinLog->id = $existing['id'];
                $checkinLog->child_id = $member['id'];
                $checkinLog->service_id = $attendance_data['service_id'];
                $checkinLog->checked_in_by = $attendance_data['checked_in_by'] ?? $member['id'];
                $checkinLog->attendance_date = $attendance_data['attendance_date'];
                $checkinLog->notes = $this->generateNotes($source, $classification);
                $checkinLog->updated_at = date('Y-m-d H:i:s');
                
                $result = $checkinLog->update();
                $action = 'updated';
            } else {
                // Create new record
                $checkinLog->child_id = $member['id'];
                $checkinLog->service_id = $attendance_data['service_id'];
                $checkinLog->checked_in_by = $attendance_data['checked_in_by'] ?? $member['id'];
                $checkinLog->check_in_time = date('Y-m-d H:i:s');
                $checkinLog->attendance_date = $attendance_data['attendance_date'];
                $checkinLog->notes = $this->generateNotes($source, $classification);
                $checkinLog->security_code = sprintf("%04d", rand(1000, 9999));
                $checkinLog->created_at = date('Y-m-d H:i:s');
                $checkinLog->updated_at = date('Y-m-d H:i:s');
                
                $result = $checkinLog->create();
                $action = 'created';
            }
            
            if ($result) {
                return [
                    'success' => true,
                    'table' => 'children_checkin_log',
                    'action' => $action,
                    'member_id' => $member['id'],
                    'classification' => $classification,
                    'security_code' => $checkinLog->security_code ?? null,
                    'message' => 'Child attendance recorded successfully'
                ];
            } else {
                throw new Exception('Failed to save to children_checkin_log');
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Children ministry routing failed: ' . $e->getMessage(),
                'fallback_attempted' => false
            ];
        }
    }
    
    /**
     * Route attendance to main attendance system
     */
    private function routeToMainAttendance($member, $attendance_data, $source, $classification = null) {
        try {
            require_once 'models/Attendance.php';
            $attendance = new Attendance($this->conn);
            
            // Check for existing record
            $existing = $attendance->getByMemberDateService(
                $member['id'],
                $attendance_data['attendance_date'],
                $attendance_data['service_id']
            );
            
            if ($existing) {
                // Update existing record
                $attendance->id = $existing['id'];
                $attendance->member_id = $member['id'];
                $attendance->service_id = $attendance_data['service_id'];
                $attendance->attendance_date = $attendance_data['attendance_date'];
                $attendance->status = $attendance_data['status'] ?? 'present';
                $attendance->marked_via = $source;
                $attendance->qr_session_id = $attendance_data['qr_session_id'] ?? null;
                $attendance->updated_at = date('Y-m-d H:i:s');
                
                $result = $attendance->update();
                $action = 'updated';
            } else {
                // Create new record
                $attendance->member_id = $member['id'];
                $attendance->service_id = $attendance_data['service_id'];
                $attendance->attendance_date = $attendance_data['attendance_date'];
                $attendance->status = $attendance_data['status'] ?? 'present';
                $attendance->marked_via = $source;
                $attendance->qr_session_id = $attendance_data['qr_session_id'] ?? null;
                $attendance->created_at = date('Y-m-d H:i:s');
                $attendance->updated_at = date('Y-m-d H:i:s');
                
                $result = $attendance->create();
                $action = 'created';
            }
            
            if ($result) {
                return [
                    'success' => true,
                    'table' => 'attendance',
                    'action' => $action,
                    'member_id' => $member['id'],
                    'classification' => $classification,
                    'message' => 'Attendance recorded successfully'
                ];
            } else {
                throw new Exception('Failed to save to attendance table');
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Main attendance routing failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate descriptive notes for attendance record
     */
    private function generateNotes($source, $classification) {
        $notes = [];
        
        // Add source information
        switch ($source) {
            case 'qr_individual':
                $notes[] = 'QR Individual Attendance';
                break;
            case 'qr_family':
                $notes[] = 'QR Family Attendance';
                break;
            case 'manual':
                $notes[] = 'Manual Attendance Entry';
                break;
            default:
                $notes[] = ucfirst(str_replace('_', ' ', $source));
        }
        
        // Add classification reason
        if ($classification) {
            $notes[] = "Routed: {$classification['reason']}";
            if ($classification['age'] !== null) {
                $notes[] = "Age: {$classification['age']}";
            }
        }
        
        return implode(' | ', $notes);
    }
    
    /**
     * Get routing statistics for debugging
     */
    public function getRoutingStats() {
        return [
            'max_child_age' => $this->max_child_age,
            'cache_size' => count($this->member_cache),
            'cached_members' => array_keys($this->member_cache)
        ];
    }
    
    /**
     * Clear member classification cache
     */
    public function clearCache() {
        $this->member_cache = [];
    }
}
