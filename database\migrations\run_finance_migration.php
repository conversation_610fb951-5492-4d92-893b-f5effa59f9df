<?php
/**
 * Run Finance Schema Migration
 * This script applies the improved finance schema changes
 */

require_once '../../config/database.php';

try {
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Finance Schema Migration</h2>\n";
    echo "<p>Starting migration process...</p>\n";
    
    // Step 1: Check current schema
    echo "<h3>Step 1: Checking current schema...</h3>\n";
    
    $checkColumns = "SHOW COLUMNS FROM finances";
    $stmt = $conn->prepare($checkColumns);
    $stmt->execute();
    $currentColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $existingColumns = array_column($currentColumns, 'Field');
    echo "<p>Current columns: " . implode(', ', $existingColumns) . "</p>\n";
    
    // Step 2: Add new columns if they don't exist
    echo "<h3>Step 2: Adding new columns...</h3>\n";
    
    $newColumns = [
        'transaction_type' => "ADD COLUMN transaction_type ENUM('income', 'expense') NOT NULL DEFAULT 'income' AFTER category",
        'subcategory' => "ADD COLUMN subcategory VARCHAR(50) NULL AFTER transaction_type",
        'reference_number' => "ADD COLUMN reference_number VARCHAR(50) NULL AFTER description",
        'payment_method' => "ADD COLUMN payment_method ENUM('cash', 'bank_transfer', 'mobile_money', 'cheque', 'other') DEFAULT 'cash' AFTER reference_number"
    ];
    
    foreach ($newColumns as $column => $sql) {
        if (!in_array($column, $existingColumns)) {
            try {
                $alterSql = "ALTER TABLE finances " . $sql;
                $conn->exec($alterSql);
                echo "<p>✅ Added column: $column</p>\n";
            } catch (PDOException $e) {
                echo "<p>❌ Error adding column $column: " . $e->getMessage() . "</p>\n";
            }
        } else {
            echo "<p>⏭️ Column $column already exists</p>\n";
        }
    }
    
    // Step 3: Update existing records
    echo "<h3>Step 3: Updating existing records...</h3>\n";
    
    try {
        // Set transaction_type for existing records
        $updateExpenses = "UPDATE finances SET transaction_type = 'expense' WHERE category = 'expense'";
        $conn->exec($updateExpenses);
        echo "<p>✅ Updated expense records</p>\n";
        
        $updateIncome = "UPDATE finances SET transaction_type = 'income' WHERE category != 'expense'";
        $conn->exec($updateIncome);
        echo "<p>✅ Updated income records</p>\n";
        
        // Generate reference numbers for existing records
        $updateRef = "UPDATE finances SET reference_number = CONCAT(
            CASE WHEN transaction_type = 'income' THEN 'INC' ELSE 'EXP' END,
            DATE_FORMAT(transaction_date, '%Y%m%d'),
            LPAD(id, 4, '0')
        ) WHERE reference_number IS NULL";
        $conn->exec($updateRef);
        echo "<p>✅ Generated reference numbers</p>\n";
        
    } catch (PDOException $e) {
        echo "<p>❌ Error updating records: " . $e->getMessage() . "</p>\n";
    }
    
    // Step 4: Expand category ENUM
    echo "<h3>Step 4: Expanding category options...</h3>\n";
    
    try {
        $expandEnum = "ALTER TABLE finances 
                      MODIFY COLUMN category ENUM(
                          'tithe', 'offering', 'project_offering', 'donation', 'seed', 'pledge', 'pastors_appreciation', 
                          'welfare', 'children_service_offering', 'others',
                          'utilities', 'rent', 'salaries', 'maintenance', 'equipment', 'supplies', 
                          'events', 'missions', 'charity', 'other_expenses'
                      ) NOT NULL";
        $conn->exec($expandEnum);
        echo "<p>✅ Expanded category options</p>\n";
    } catch (PDOException $e) {
        echo "<p>❌ Error expanding categories: " . $e->getMessage() . "</p>\n";
    }
    
    // Step 5: Add indexes
    echo "<h3>Step 5: Adding performance indexes...</h3>\n";
    
    $indexes = [
        'idx_transaction_type' => 'CREATE INDEX idx_transaction_type ON finances(transaction_type)',
        'idx_subcategory' => 'CREATE INDEX idx_subcategory ON finances(subcategory)',
        'idx_reference_number' => 'CREATE INDEX idx_reference_number ON finances(reference_number)',
        'idx_payment_method' => 'CREATE INDEX idx_payment_method ON finances(payment_method)'
    ];
    
    foreach ($indexes as $indexName => $sql) {
        try {
            $conn->exec($sql);
            echo "<p>✅ Added index: $indexName</p>\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p>⏭️ Index $indexName already exists</p>\n";
            } else {
                echo "<p>❌ Error adding index $indexName: " . $e->getMessage() . "</p>\n";
            }
        }
    }
    
    // Step 6: Add constraints
    echo "<h3>Step 6: Adding data constraints...</h3>\n";
    
    try {
        // Check if constraints already exist before adding
        $checkConstraints = "SELECT CONSTRAINT_NAME FROM information_schema.TABLE_CONSTRAINTS 
                           WHERE TABLE_NAME = 'finances' AND TABLE_SCHEMA = DATABASE()";
        $stmt = $conn->prepare($checkConstraints);
        $stmt->execute();
        $existingConstraints = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'CONSTRAINT_NAME');
        
        if (!in_array('chk_amount_positive', $existingConstraints)) {
            $conn->exec("ALTER TABLE finances ADD CONSTRAINT chk_amount_positive CHECK (amount > 0)");
            echo "<p>✅ Added positive amount constraint</p>\n";
        } else {
            echo "<p>⏭️ Amount constraint already exists</p>\n";
        }
        
        if (!in_array('chk_future_date', $existingConstraints)) {
            $conn->exec("ALTER TABLE finances ADD CONSTRAINT chk_future_date CHECK (transaction_date <= CURDATE())");
            echo "<p>✅ Added future date constraint</p>\n";
        } else {
            echo "<p>⏭️ Date constraint already exists</p>\n";
        }
        
    } catch (PDOException $e) {
        echo "<p>❌ Error adding constraints: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<h3>✅ Migration completed successfully!</h3>\n";
    echo "<p>The finance schema has been updated with enhanced features.</p>\n";
    
} catch (Exception $e) {
    echo "<h3>❌ Migration failed!</h3>\n";
    echo "<p>Error: " . $e->getMessage() . "</p>\n";
}
?>
