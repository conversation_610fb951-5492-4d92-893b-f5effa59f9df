<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($group->group_name ?? 'Group'); ?> Members - ICGC</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">

<div class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center gap-4 mb-4">
                <a href="<?php echo url('groups'); ?>" class="text-green-600 hover:text-green-800 transition-colors p-2 rounded-lg hover:bg-green-50">
                    <i class="fas fa-arrow-left text-xl"></i>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-gray-800"><?php echo htmlspecialchars($group->group_name ?? 'Unknown Group'); ?></h1>
                    <div class="flex items-center gap-4 text-sm text-gray-600 mt-2">
                        <span class="flex items-center gap-1 bg-green-50 px-2 py-1 rounded-full">
                            <i class="fas fa-users text-green-600"></i>
                            <?php echo $memberCount ?? 0; ?> Members
                        </span>
                        <span class="flex items-center gap-1 bg-purple-50 px-2 py-1 rounded-full">
                            <i class="fas fa-tag text-purple-600"></i>
                            <?php echo htmlspecialchars($group->type_name ?? 'Group'); ?>
                        </span>
                        <span class="flex items-center gap-1 bg-gray-50 px-2 py-1 rounded-full">
                            <i class="fas fa-circle text-green-500 text-xs"></i>
                            <?php echo ucfirst($group->status ?? 'active'); ?>
                        </span>
                    </div>
                </div>
            </div>
            
            <?php if ($group->group_description): ?>
                <div class="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border-l-4 border-green-500">
                    <p class="text-gray-700"><?php echo htmlspecialchars($group->group_description); ?></p>
                </div>
            <?php endif; ?>
            
            <!-- Action Buttons -->
            <div class="flex gap-3 mt-4">
                <a href="<?php echo url('groups/add-members/' . ($group->group_id ?? 1)); ?>" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center">
                    <i class="fas fa-user-plus mr-2"></i>Add Members
                </a>
                <a href="<?php echo url('groups/edit/' . ($group->group_id ?? 1)); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center">
                    <i class="fas fa-edit mr-2"></i>Edit Group
                </a>
            </div>
        </div>

        <!-- Members List -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 class="text-xl font-semibold text-gray-800">Group Members</h2>
            </div>
            
            <div class="p-6">
                <?php if (!empty($members) && is_array($members)): ?>
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <?php foreach ($members as $member): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center gap-3">
                                    <div class="flex-shrink-0">
                                        <?php if (!empty($member->profile_picture)): ?>
                                            <img src="<?php echo url('uploads/members/' . $member->profile_picture); ?>" 
                                                 alt="<?php echo htmlspecialchars($member->first_name ?? ''); ?>" 
                                                 class="h-12 w-12 rounded-full object-cover border-2 border-green-200">
                                        <?php else: ?>
                                            <div class="h-12 w-12 rounded-full bg-green-500 flex items-center justify-center text-white font-bold text-lg border-2 border-green-200">
                                                <?php echo strtoupper(substr($member->first_name ?? 'U', 0, 1)); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-sm font-medium text-gray-900 truncate">
                                            <?php echo htmlspecialchars(($member->first_name ?? '') . ' ' . ($member->last_name ?? '')); ?>
                                        </h3>
                                        <p class="text-sm text-gray-500 truncate">
                                            <?php echo htmlspecialchars($member->email ?? 'No email'); ?>
                                        </p>
                                        <?php if (!empty($member->role_in_group)): ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                                                <?php echo ucfirst($member->role_in_group); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="mt-3 flex justify-end">
                                    <a href="<?php echo url('members/view/' . ($member->id ?? 1)); ?>" 
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-users text-6xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Members Found</h3>
                        <p class="text-gray-500 mb-6">This group doesn't have any members yet.</p>
                        <a href="<?php echo url('groups/add-members/' . ($group->group_id ?? 1)); ?>" 
                           class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-all duration-200 inline-flex items-center">
                            <i class="fas fa-user-plus mr-2"></i>Add First Member
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Debug Information (remove in production) -->
        <?php if (defined('DEBUG') && DEBUG): ?>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
                <h3 class="text-lg font-medium text-yellow-800 mb-2">Debug Information</h3>
                <div class="text-sm text-yellow-700">
                    <p><strong>Group ID:</strong> <?php echo $group->group_id ?? 'N/A'; ?></p>
                    <p><strong>Group Name:</strong> <?php echo htmlspecialchars($group->group_name ?? 'N/A'); ?></p>
                    <p><strong>Member Count:</strong> <?php echo $memberCount ?? 'N/A'; ?></p>
                    <p><strong>Members Array:</strong> <?php echo is_array($members) ? count($members) . ' items' : 'Not an array'; ?></p>
                </div>
            </div>
        <?php endif; ?>

    </div>
</div>

</body>
</html>
