<?php
/**
 * Service Model
 */

class Service {
    // Database connection and table name
    private $conn;
    private $table_name = "services";

    // Object properties
    public $id;
    public $name;
    public $description;
    public $day_of_week;
    public $time;
    public $status;
    public $created_at;
    public $updated_at;

    // Error handling
    public $error;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all services
     *
     * @param bool $includeArchived Whether to include archived services
     * @return PDOStatement
     */
    public function getAll($includeArchived = false) {
        $query = "SELECT * FROM " . $this->table_name;

        // Check if status column exists before using it
        if (!$includeArchived && $this->hasStatusColumn()) {
            $query .= " WHERE (status IS NULL OR status = 'active')";
        }

        $query .= " ORDER BY day_of_week, time";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Check if status column exists in the table
     *
     * @return bool
     */
    private function hasStatusColumn() {
        try {
            $query = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'status'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get service by ID
     *
     * @param int $id
     * @return bool
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            $this->id = $row['id'];
            $this->name = $row['name'];
            $this->description = $row['description'];
            $this->day_of_week = $row['day_of_week'];
            $this->time = $row['time'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];

            return true;
        }

        return false;
    }

    /**
     * Read one service record
     *
     * @return array|false
     */
    public function readOne() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Validate service data before create/update operations
     *
     * @return bool True if validation passes, false otherwise
     */
    public function validate() {
        $this->error = null;

        // Required field validation
        if (empty($this->name)) {
            $this->error = 'Service name is required.';
            return false;
        }

        if (empty($this->day_of_week)) {
            $this->error = 'Day of week is required.';
            return false;
        }

        if (empty($this->time)) {
            $this->error = 'Time is required.';
            return false;
        }

        // Service name validation
        if (strlen($this->name) > 100) {
            $this->error = 'Service name cannot exceed 100 characters.';
            return false;
        }

        // Day of week validation
        $valid_days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        if (!in_array(strtolower($this->day_of_week), $valid_days)) {
            $this->error = 'Invalid day of week selected.';
            return false;
        }

        // Time format validation
        if (!empty($this->time)) {
            // Check if time is in valid format (HH:MM or HH:MM:SS)
            if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/', $this->time)) {
                $this->error = 'Invalid time format. Use HH:MM format.';
                return false;
            }
        }

        // Description length validation (if provided)
        if (!empty($this->description) && strlen($this->description) > 500) {
            $this->error = 'Description cannot exceed 500 characters.';
            return false;
        }

        // Status validation (if provided)
        if (!empty($this->status)) {
            $valid_statuses = ['active', 'inactive', 'cancelled'];
            if (!in_array(strtolower($this->status), $valid_statuses)) {
                $this->error = 'Invalid service status.';
                return false;
            }
        }

        return true;
    }

    /**
     * Create service with validation
     *
     * @return bool
     */
    public function create() {
        // CRITICAL: Validate data before creating
        if (!$this->validate()) {
            // The 'error' property is set by the validate() method
            return false;
        }
        $query = "INSERT INTO " . $this->table_name . "
                  (name, description, day_of_week, time, created_at, updated_at)
                  VALUES
                  (:name, :description, :day_of_week, :time, :created_at, :updated_at)";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->day_of_week = htmlspecialchars(strip_tags($this->day_of_week));
        $this->time = htmlspecialchars(strip_tags($this->time));
        $this->created_at = htmlspecialchars(strip_tags($this->created_at));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':day_of_week', $this->day_of_week);
        $stmt->bindParam(':time', $this->time);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        try {
            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Failed to create service record.';
                return false;
            }
        } catch (PDOException $e) {
            $this->error = 'Database error occurred while creating service.';
            error_log("Service creation failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update service with validation
     *
     * @return bool
     */
    public function update() {
        // CRITICAL: Validate data before updating
        if (!$this->validate()) {
            // The 'error' property is set by the validate() method
            return false;
        }
        $query = "UPDATE " . $this->table_name . "
                  SET name = :name,
                      description = :description,
                      day_of_week = :day_of_week,
                      time = :time,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->id = htmlspecialchars(strip_tags($this->id));
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->day_of_week = htmlspecialchars(strip_tags($this->day_of_week));
        $this->time = htmlspecialchars(strip_tags($this->time));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':day_of_week', $this->day_of_week);
        $stmt->bindParam(':time', $this->time);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        try {
            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Failed to update service record.';
                return false;
            }
        } catch (PDOException $e) {
            $this->error = 'Database error occurred while updating service.';
            error_log("Service update failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete service
     *
     * @param int $id Service ID
     * @return bool
     */
    public function delete($id) {
        try {
            // Clear any previous errors
            $this->error = null;

            // Sanitize input
            $id = htmlspecialchars(strip_tags($id));

            // Check if service is used in attendance records
            if ($this->isUsedInAttendance($id)) {
                $this->error = 'Cannot delete service. It is being used in attendance records.';
                return false;
            }

            $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);

            // Bind parameters
            $stmt->bindParam(':id', $id);

            // Execute query
            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Error deleting service. Please try again.';
                return false;
            }
        } catch (Exception $e) {
            $this->error = 'Database error: ' . $e->getMessage();
            return false;
        }
    }



    /**
     * Archive service (soft delete)
     *
     * @param int $id Service ID
     * @return bool
     */
    public function archive($id) {
        // Check if status column exists
        if (!$this->hasStatusColumn()) {
            // If no status column, fall back to regular delete
            return $this->delete($id);
        }

        $query = "UPDATE " . $this->table_name . "
                  SET status = 'archived', updated_at = NOW()
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $id = htmlspecialchars(strip_tags($id));

        // Bind parameters
        $stmt->bindParam(':id', $id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Restore archived service
     *
     * @param int $id Service ID
     * @return bool
     */
    public function restore($id) {
        $query = "UPDATE " . $this->table_name . "
                  SET status = 'active', updated_at = NOW()
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $id = htmlspecialchars(strip_tags($id));

        // Bind parameters
        $stmt->bindParam(':id', $id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Check if service is used in attendance records
     *
     * @param int $id Service ID
     * @return bool
     */
    public function isUsedInAttendance($id) {
        $query = "SELECT COUNT(*) as count FROM attendance a
                  WHERE a.service_id = :service_id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $id = htmlspecialchars(strip_tags($id));

        // Bind parameters
        $stmt->bindParam(':service_id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['count'] > 0;
    }

    /**
     * Get services by day of week
     *
     * @param string $day_of_week
     * @return PDOStatement
     */
    public function getByDayOfWeek($day_of_week) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE day_of_week = :day_of_week ORDER BY time";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':day_of_week', $day_of_week);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get recent services with attendance data
     *
     * @param int $limit Number of recent services to retrieve
     * @return array Array of service data
     */
    public function getRecentWithAttendance($limit = 5) {
        // Get distinct service_id and date combinations from attendance table
        $query = "SELECT DISTINCT a.service_id, a.attendance_date as date, s.*
                  FROM attendance a
                  JOIN " . $this->table_name . " s ON a.service_id = s.id
                  GROUP BY a.service_id, a.attendance_date
                  ORDER BY a.attendance_date DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
