<?php
// Prevent direct access
if (!defined('BASE_URL')) {
    exit('Direct access not permitted');
}
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
            <a href="<?php echo BASE_URL; ?>welfare" class="text-gray-600 hover:text-gray-800 mr-4">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Welfare Reports</h1>
                <p class="text-gray-600 mt-1">Comprehensive welfare assistance analytics</p>
            </div>
        </div>
        <div class="flex space-x-3">
            <select id="yearFilter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                    <option value="<?php echo $y; ?>" <?php echo $y == $selected_year ? 'selected' : ''; ?>><?php echo $y; ?></option>
                <?php endfor; ?>
            </select>
        </div>
    </div>

    <!-- Yearly Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-emerald-100">
                    <i class="fas fa-money-bill-wave text-emerald-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Disbursed</p>
                    <p class="text-2xl font-bold text-gray-900">₵<?php echo number_format($yearly_stats['total_amount'] ?? 0, 2); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="fas fa-list text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Payments</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($yearly_stats['total_payments'] ?? 0); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <i class="fas fa-users text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Recipients</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($yearly_stats['unique_recipients'] ?? 0); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-amber-100">
                    <i class="fas fa-calculator text-amber-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Average Payment</p>
                    <p class="text-2xl font-bold text-gray-900">₵<?php echo number_format($yearly_stats['average_payment'] ?? 0, 2); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Recipients -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Top Recipients (<?php echo $selected_year; ?>)</h2>
        </div>
        <div class="p-6">
            <?php if (empty($top_recipients)): ?>
                <div class="text-center py-8">
                    <i class="fas fa-users text-gray-300 text-4xl mb-4"></i>
                    <p class="text-gray-500">No recipient data available for <?php echo $selected_year; ?></p>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php foreach ($top_recipients as $index => $recipient): ?>
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center text-sm font-medium text-emerald-600">
                                    <?php echo $index + 1; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-medium text-gray-900 text-sm">
                                        <?php echo htmlspecialchars($recipient['first_name'] . ' ' . $recipient['last_name']); ?>
                                    </p>
                                    <p class="text-xs text-gray-600"><?php echo $recipient['payment_count']; ?> payments</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900 text-sm">₵<?php echo number_format($recipient['total_received'], 2); ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Monthly Trends -->
    <?php if (!empty($monthly_trends)): ?>
    <div class="mt-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Monthly Trends (<?php echo $selected_year; ?>)</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <?php foreach ($monthly_trends as $month): ?>
                        <div class="bg-gray-50 rounded-lg p-4 text-center">
                            <h3 class="font-medium text-gray-900"><?php echo $month['month_name']; ?></h3>
                            <p class="text-2xl font-bold text-emerald-600 mt-2">₵<?php echo number_format($month['total_amount'], 0); ?></p>
                            <p class="text-sm text-gray-600"><?php echo $month['payment_count']; ?> payments</p>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
document.getElementById('yearFilter').addEventListener('change', function() {
    const year = this.value;
    window.location.href = `<?php echo BASE_URL; ?>welfare/reports?year=${year}`;
});
</script>

<?php include 'views/layouts/footer.php'; ?>
