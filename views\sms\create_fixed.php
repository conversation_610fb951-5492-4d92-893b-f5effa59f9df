<?php
// This is a fixed version of the SMS create page
// It includes the proper HTML structure with the layout
?>
<div class="container mx-auto max-w-5xl">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div class="mb-4 md:mb-0">
                <h1 class="text-2xl font-bold text-gray-800 mb-3">Send SMS</h1>
                <div class="flex items-center bg-gradient-to-r from-amber-50 to-amber-100 px-4 py-3 rounded-lg border border-amber-200">
                    <div class="rounded-full bg-amber-200 p-2 mr-3">
                        <i class="fas fa-coins text-amber-600 text-lg"></i>
                    </div>
                    <div>
                        <span class="text-sm text-amber-700 font-medium">Credit Balance</span>
                        <div class="text-lg font-bold <?php echo $sms_balance > 0 ? 'text-green-600' : 'text-red-600'; ?>">
                            <?php echo number_format($sms_balance); ?> SMS
                            <?php if ($sms_balance <= 100): ?>
                                <span class="ml-2 px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">Low Balance</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <a href="<?php echo BASE_URL; ?>sms" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-5 rounded-md flex items-center mt-2 md:mt-0 transition-colors duration-200 border border-gray-200 shadow-sm hover:shadow">
                <i class="fas fa-arrow-left mr-2"></i> Back to SMS Dashboard
            </a>
        </div>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-50 border border-red-200 rounded-lg text-red-700 p-5 mb-6 shadow-sm" role="alert">
            <div class="flex items-center mb-3">
                <div class="rounded-full bg-red-100 p-2 mr-3">
                    <i class="fas fa-exclamation-circle text-red-500"></i>
                </div>
                <p class="font-bold text-red-800">Please fix the following errors:</p>
            </div>
            <ul class="list-disc ml-10 space-y-1">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Debug Information -->
    <?php if (isset($_SESSION['debug_info']) && !empty($_SESSION['debug_info'])) : ?>
        <div class="bg-blue-50 border border-blue-200 rounded-lg text-blue-700 p-5 mb-6 shadow-sm" role="alert">
            <div class="flex items-center mb-3">
                <div class="rounded-full bg-blue-100 p-2 mr-3">
                    <i class="fas fa-info-circle text-blue-500"></i>
                </div>
                <p class="font-bold text-blue-800">Debug Information:</p>
            </div>
            <pre class="mt-2 text-xs overflow-auto max-h-40 bg-white p-3 rounded border border-blue-100"><?php echo json_encode($_SESSION['debug_info'], JSON_PRETTY_PRINT); ?></pre>
        </div>
        <?php unset($_SESSION['debug_info']); ?>
    <?php endif; ?>





    <!-- SMS Form -->
    <form action="<?php echo BASE_URL; ?>sms/send" method="POST" id="sms-form" onsubmit="return validateSmsForm();">
        <?php echo csrf_field(); ?>
        <!-- Two-column layout -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Left Column: Recipient Selection -->
            <div>
                <div class="flex items-center mb-3">
                    <div class="rounded-full bg-blue-100 p-1.5 mr-2">
                        <i class="fas fa-users text-blue-600"></i>
                    </div>
                    <h2 class="text-lg font-semibold text-gray-800">Select Recipients</h2>
                </div>
                <div class="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-md" style="min-height: 450px;">
                <!-- Tabs - Enhanced design -->
                <div class="flex border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
                    <button type="button" class="tab-button px-6 py-4 text-sm font-medium border-b-2 border-primary text-primary flex-1 active hover:bg-white transition-colors duration-150" data-tab="all-members">
                        <i class="fas fa-users mr-2"></i> All Members
                        <span id="all-members-count" class="ml-2 px-2 py-0.5 bg-gray-100 text-gray-700 text-xs rounded-full">0 selected</span>
                    </button>
                    <button type="button" class="tab-button px-6 py-4 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 flex-1 hover:bg-white transition-colors duration-150" data-tab="by-group">
                        <i class="fas fa-layer-group mr-2"></i> By Group
                        <span id="group-count" class="ml-2 px-2 py-0.5 bg-gray-100 text-gray-700 text-xs rounded-full">0 selected</span>
                    </button>
                    <button type="button" class="tab-button px-6 py-4 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 flex-1 hover:bg-white transition-colors duration-150" data-tab="individual-members">
                        <i class="fas fa-user-check mr-2"></i> Individual Members
                        <span id="individual-count" class="ml-2 px-2 py-0.5 bg-gray-100 text-gray-700 text-xs rounded-full">0 selected</span>
                    </button>
                </div>

                <!-- Tab Content -->
                <div class="p-5">
                    <!-- All Members Tab -->
                    <div class="tab-content" id="all-members-tab">
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-5 border border-blue-200 mb-4">
                            <div class="flex items-center mb-3">
                                <input type="checkbox" id="send-to-all" name="send_to_all" value="1" class="h-5 w-5 text-primary focus:ring-primary border-gray-300 rounded">
                                <label for="send-to-all" class="ml-3 block text-sm font-medium text-blue-800">
                                    Send to all members with valid phone numbers
                                </label>
                            </div>
                            <div class="ml-8 text-sm text-blue-700">
                                <p>Your message will be sent to all <strong id="total-members-count" class="font-bold"><?php echo $member_count; ?></strong> members with valid phone numbers.</p>
                                <!-- Hidden input will be enabled/disabled via JavaScript -->
                                <input type="hidden" name="send_to_all" value="all" id="send-to-all-value" disabled>
                            </div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <div class="flex items-center">
                                <div class="rounded-full bg-yellow-100 p-2 mr-3">
                                    <i class="fas fa-info-circle text-yellow-600"></i>
                                </div>
                                <p class="text-sm text-gray-600">When sending to all members, make sure your message is relevant to everyone.</p>
                            </div>
                        </div>
                    </div>

                    <!-- By Group Tab -->
                    <div class="tab-content hidden" id="by-group-tab">
                        <div class="flex justify-between items-center mb-2 text-sm">
                            <div class="flex space-x-2">
                                <button type="button" id="select-all-groups" class="px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded">
                                    <i class="fas fa-check-square mr-1"></i> Select All
                                </button>
                                <button type="button" id="deselect-all-groups" class="px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded">
                                    <i class="fas fa-square mr-1"></i> Deselect All
                                </button>
                            </div>
                            <div id="group-selected-count" class="text-gray-600">0 groups selected</div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-medium text-gray-700 mb-2">By Department</p>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="department_choir" name="recipient_groups[]" value="department_choir" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="department_choir" class="ml-2 block text-sm text-gray-700">Choir</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="department_ushering" name="recipient_groups[]" value="department_ushering" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="department_ushering" class="ml-2 block text-sm text-gray-700">Ushering</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="department_protocol" name="recipient_groups[]" value="department_protocol" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="department_protocol" class="ml-2 block text-sm text-gray-700">Protocol</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="department_media" name="recipient_groups[]" value="department_media" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="department_media" class="ml-2 block text-sm text-gray-700">Media</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="department_children" name="recipient_groups[]" value="department_children" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="department_children" class="ml-2 block text-sm text-gray-700">Children's Ministry</label>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700 mb-2">By Role</p>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="role_pastor" name="recipient_groups[]" value="role_pastor" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="role_pastor" class="ml-2 block text-sm text-gray-700">Pastors</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="role_elder" name="recipient_groups[]" value="role_elder" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="role_elder" class="ml-2 block text-sm text-gray-700">Elders</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="role_deacon" name="recipient_groups[]" value="role_deacon" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="role_deacon" class="ml-2 block text-sm text-gray-700">Deacons</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="role_deaconess" name="recipient_groups[]" value="role_deaconess" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="role_deaconess" class="ml-2 block text-sm text-gray-700">Deaconesses</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="role_member" name="recipient_groups[]" value="role_member" class="group-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                        <label for="role_member" class="ml-2 block text-sm text-gray-700">Regular Members</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Individual Members Tab -->
                    <div class="tab-content hidden" id="individual-members-tab">
                        <div class="mb-4">
                            <label for="search-members" class="block text-sm font-medium text-gray-700 mb-1">Search Members</label>
                            <input type="text" id="search-members" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Search by name or phone number...">
                        </div>

                        <div class="flex justify-between items-center mb-2 text-sm">
                            <div class="flex space-x-2">
                                <button type="button" id="select-all" class="px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded">
                                    <i class="fas fa-check-square mr-1"></i> Select All
                                </button>
                                <button type="button" id="deselect-all" class="px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded">
                                    <i class="fas fa-square mr-1"></i> Deselect All
                                </button>
                            </div>
                            <div id="filtered-count" class="text-gray-600">0 members found</div>
                        </div>

                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                            <!-- Loading indicator -->
                            <div id="members-loading" class="text-center py-4">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                                <p class="mt-2 text-gray-600">Loading members...</p>
                            </div>

                            <!-- No members message -->
                            <div id="no-members-message" class="text-center py-4 hidden">
                                <p class="text-gray-600">No members found matching your search criteria.</p>
                            </div>

                            <!-- Members container -->
                            <div class="space-y-1 hidden" id="members-container"></div>

                            <!-- Load more button -->
                            <div id="load-more-container" class="text-center mt-4 hidden">
                                <button type="button" id="load-more" class="text-sm bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-4 rounded">
                                    <i class="fas fa-sync-alt mr-2"></i> Load More Members
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- Right Column: Message Content -->
            <div>
                <div class="flex items-center mb-3">
                    <div class="rounded-full bg-green-100 p-1.5 mr-2">
                        <i class="fas fa-comment-alt text-green-600"></i>
                    </div>
                    <h2 class="text-lg font-semibold text-gray-800">Message Content</h2>
                </div>
                <div class="bg-white rounded-lg shadow-md border border-gray-200 p-6 flex flex-col" style="min-height: 450px;">
                    <!-- Message Templates -->
                    <div class="mb-4">
                        <label for="template" class="block text-sm font-medium text-gray-700 mb-2">Message Templates</label>
                        <select id="template" class="w-full px-3 py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-gray-50 hover:bg-white transition-colors duration-150">
                            <option value="">Select a template or write your own</option>
                            <option value="Dear Member, we remind you of our Sunday service tomorrow at 9:00 AM. God bless you.">Sunday Service Reminder</option>
                            <option value="Dear Member, we invite you to our special prayer meeting on Friday at 6:00 PM. Your presence is important.">Prayer Meeting Invitation</option>
                            <option value="Dear Member, this is a reminder about the upcoming church conference. Please confirm your attendance.">Conference Reminder</option>
                        </select>
                    </div>

                    <div class="flex-grow">
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message Content <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <textarea id="message" name="message" rows="10" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-gray-50 hover:bg-white transition-colors duration-150" placeholder="Type your message here..." maxlength="1000"></textarea>
                            <div class="absolute top-2 right-2 bg-gray-100 px-2 py-1 rounded-md text-xs text-gray-500">
                                <i class="fas fa-info-circle mr-1"></i> Use {first_name} to personalize
                            </div>
                        </div>
                        <div class="mt-2 flex justify-between items-center">
                            <span id="character-counter" class="text-sm text-gray-500 font-medium">0 characters</span>
                            <span class="text-sm text-gray-500">Max 1000 characters</span>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="mt-6 flex justify-end">
                        <button type="submit" class="bg-gradient-to-r from-primary to-primary-dark text-white font-medium py-3 px-8 rounded-md flex items-center shadow-md transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1">
                            <i class="fas fa-paper-plane mr-2"></i> Send SMS
                        </button>
                    </div>
                </div>
            </div>
        </div> <!-- End of grid layout -->
    </form>
</div>

<script>
    // Global variables for member loading
    let currentPage = 1;
    let membersPerPage = 100; // Increased from 50 to 100 for better loading
    let hasMoreMembers = false;
    let currentSearch = '';
    let selectedMembers = new Set(); // Track selected members across pagination
    const apiBaseUrl = '<?php echo BASE_URL; ?>api/members_for_sms.php';

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        // Load members without setting default department and role filters
        fetchMembers();

        // Initialize all counts
        updateAllMembersCount();
        updateGroupCount();
        updateSelectedCount();

        // Handle preselected recipients based on mode
        <?php if (isset($preselected_mode) && $preselected_mode !== 'none'): ?>

        // Switch to the individual members tab
        const individualTab = document.querySelector('[data-tab="individual-members"]');
        if (individualTab) {
            individualTab.click();

            <?php if ($preselected_mode === 'single' && isset($member_data)): ?>
            // Handle single recipient
            setTimeout(function() {
                const memberData = <?php echo json_encode($member_data); ?>;
                addBulkRecipient(memberData.phone_number, memberData.first_name + ' ' + memberData.last_name, memberData.id);
            }, 500);

            <?php elseif ($preselected_mode === 'bulk' && isset($bulk_recipients)): ?>
            // Handle multiple recipients
            setTimeout(function() {
                const bulkRecipients = <?php echo json_encode($bulk_recipients); ?>;

                bulkRecipients.forEach(function(recipient) {
                    addBulkRecipient(recipient.phone_number, recipient.first_name + ' ' + recipient.last_name, recipient.id);
                });

                // Update the count display
                updateSelectedCount();
            }, 500);
            <?php endif; ?>
        }

        <?php endif; ?>

        // Set default message if provided
        <?php if (isset($default_message) && !empty($default_message)): ?>
        setTimeout(function() {
            const messageTextarea = document.getElementById('message');
            if (messageTextarea) {
                messageTextarea.value = '<?php echo addslashes($default_message); ?>';
                messageTextarea.dispatchEvent(new Event('input')); // Trigger character count update
            }
        }, 100);
        <?php endif; ?>
    });

    // Member search functionality
    const searchInput = document.getElementById('search-members');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            currentSearch = this.value.toLowerCase();
            resetMembersList();
            fetchMembers();
        });
    }

    // Initialize load more button
    const loadMoreBtn = document.getElementById('load-more');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            fetchMembers(true);
        });
    }

    // Department and role filters have been removed

    // Select all filtered button has been removed

    // Initialize select/deselect all buttons
    const selectAllBtn = document.getElementById('select-all');
    const deselectAllBtn = document.getElementById('deselect-all');

    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            document.querySelectorAll('.member-checkbox').forEach(checkbox => {
                checkbox.checked = true;
                selectedMembers.add(checkbox.id.replace('member-', ''));
            });
            updateSelectedCount();
        });
    }

    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', function() {
            document.querySelectorAll('.member-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                selectedMembers.delete(checkbox.id.replace('member-', ''));
            });
            updateSelectedCount();
        });
    }

    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');

            // Remove active class from all buttons and hide all contents
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'border-primary', 'text-primary');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            // Add active class to clicked button and show corresponding content
            this.classList.add('active', 'border-primary', 'text-primary');
            this.classList.remove('border-transparent', 'text-gray-500');
            document.getElementById(tabId + '-tab').classList.remove('hidden');

            // Update the text color for the active tab
            const badge = this.querySelector('span');
            if (badge) {
                badge.classList.add('text-primary');
                badge.classList.remove('text-gray-700');
            }

            // Handle tab-specific actions
            if (tabId === 'all-members') {
                // When switching to All Members tab, disable the send_to_all input if checkbox is not checked
                const sendToAllValue = document.getElementById('send-to-all-value');
                if (sendToAllValue && allMembersCheckbox) {
                    sendToAllValue.disabled = !allMembersCheckbox.checked;
                }
            } else if (tabId === 'individual-members') {
                // When switching to Individual Members tab, disable the send_to_all input
                const sendToAllValue = document.getElementById('send-to-all-value');
                if (sendToAllValue) {
                    sendToAllValue.disabled = true;
                }
                // Uncheck the send-to-all checkbox
                if (allMembersCheckbox) {
                    allMembersCheckbox.checked = false;
                    updateAllMembersCount();
                }
            } else if (tabId === 'by-group') {
                // When switching to By Group tab, disable the send_to_all input
                const sendToAllValue = document.getElementById('send-to-all-value');
                if (sendToAllValue) {
                    sendToAllValue.disabled = true;
                }
                // Uncheck the send-to-all checkbox
                if (allMembersCheckbox) {
                    allMembersCheckbox.checked = false;
                    updateAllMembersCount();
                }
            }

            // Update all counts to ensure they're displayed correctly
            updateAllMembersCount();
            updateGroupCount();
            updateSelectedCount();
        });
    });

    // Character counter
    const messageTextarea = document.getElementById('message');
    const characterCounter = document.getElementById('character-counter');

    if (messageTextarea && characterCounter) {
        messageTextarea.addEventListener('input', function() {
            const count = this.value.length;
            characterCounter.textContent = count + ' characters';

            // Change color when approaching limit
            if (count > 900) {
                characterCounter.classList.add('text-red-500');
                characterCounter.classList.remove('text-gray-500');
            } else {
                characterCounter.classList.remove('text-red-500');
                characterCounter.classList.add('text-gray-500');
            }
        });

        // Trigger count on page load
        messageTextarea.dispatchEvent(new Event('input'));
    }

    // Template selection
    const templateSelect = document.getElementById('template');
    if (templateSelect && messageTextarea) {
        templateSelect.addEventListener('change', function() {
            if (this.value) {
                messageTextarea.value = this.value;
                messageTextarea.dispatchEvent(new Event('input'));
            }
        });
    }

    // Update selected count
    function updateSelectedCount() {
        const selectedCount = selectedMembers.size;

        // Update filtered count if it exists
        const filteredCount = document.getElementById('filtered-count');
        if (filteredCount) {
            // Get the total number of visible members
            const visibleMembers = document.querySelectorAll('.member-checkbox').length;
            filteredCount.textContent = visibleMembers + ' members found';
        }

        // Update selected count display
        const selectedCountDisplay = document.getElementById('selected-count');
        if (selectedCountDisplay) {
            selectedCountDisplay.textContent = selectedCount;
        }

        // Update tab count badge
        const individualCountBadge = document.getElementById('individual-count');
        if (individualCountBadge) {
            individualCountBadge.textContent = selectedCount + ' selected';
        }

        // Update text color only
        const individualTab = document.querySelector('[data-tab="individual-members"]');
        if (individualCountBadge) {
            if (selectedCount > 0 && individualTab && individualTab.classList.contains('active')) {
                individualCountBadge.classList.add('text-primary');
                individualCountBadge.classList.remove('text-gray-700');
            } else {
                individualCountBadge.classList.remove('text-primary');
                individualCountBadge.classList.add('text-gray-700');
            }
        }
    }

    // Initialize all members count based on checkbox state
    const allMembersCheckbox = document.getElementById('send-to-all');

    if (allMembersCheckbox) {
        // Update count when checkbox changes
        allMembersCheckbox.addEventListener('change', function() {
            updateAllMembersCount();

            // If checking "Send to all", clear other selections
            if (this.checked) {
                // Clear department/role selections
                document.querySelectorAll('input[name="recipient_group"]').forEach(radio => {
                    radio.checked = false;
                });

                // Clear individual member selections
                document.querySelectorAll('.member-checkbox').forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Clear selected members set
                selectedMembers.clear();
                updateSelectedCount();

                // Remove specific recipient if it exists
                const specificRecipient = document.getElementById('specific-recipient');
                if (specificRecipient) {
                    specificRecipient.remove();
                }
            }
        });
    }

    // Initialize counts
    updateAllMembersCount();
    updateSelectedCount();
    updateGroupCount();

    // Handle recipient selection
    const groupCheckboxes = document.querySelectorAll('.group-checkbox');
    let selectedGroups = new Set(); // Track selected groups

    // Initialize select/deselect all groups buttons
    const selectAllGroupsBtn = document.getElementById('select-all-groups');
    const deselectAllGroupsBtn = document.getElementById('deselect-all-groups');

    if (selectAllGroupsBtn) {
        selectAllGroupsBtn.addEventListener('click', function() {
            groupCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                selectedGroups.add(checkbox.id);
            });
            updateGroupCount();

            // Uncheck the "Send to all" checkbox
            if (allMembersCheckbox) {
                allMembersCheckbox.checked = false;
                updateAllMembersCount();
            }
        });
    }

    if (deselectAllGroupsBtn) {
        deselectAllGroupsBtn.addEventListener('click', function() {
            groupCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                selectedGroups.delete(checkbox.id);
            });
            updateGroupCount();
        });
    }

    // When selecting a group checkbox, uncheck the "Send to all" checkbox
    groupCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (allMembersCheckbox && this.checked) {
                allMembersCheckbox.checked = false;
                updateAllMembersCount();
            }

            // Update selected groups tracking
            if (this.checked) {
                selectedGroups.add(this.id);

                // If selecting a group, preserve the specific recipient if it exists
                // but clear other individual selections
                const specificRecipient = document.getElementById('specific-recipient');
                const specificCheckbox = specificRecipient ? specificRecipient.querySelector('input[type="checkbox"]') : null;
                const specificId = specificCheckbox ? specificCheckbox.id.replace('member-', '') : null;

                // Store the specific recipient ID if it exists and is checked
                const keepSpecificRecipient = specificCheckbox && specificCheckbox.checked && specificId;

                // Clear individual member selections except the specific recipient
                document.querySelectorAll('.member-checkbox').forEach(cb => {
                    if (!keepSpecificRecipient || cb.id !== specificCheckbox.id) {
                        cb.checked = false;
                    }
                });

                // Clear selected members set but keep specific recipient if needed
                selectedMembers.clear();
                if (keepSpecificRecipient) {
                    selectedMembers.add(specificId);
                }
                updateSelectedCount();
            } else {
                selectedGroups.delete(this.id);
            }

            updateGroupCount();
        });
    });

    // Function to update the group count display
    function updateGroupCount() {
        const groupCountDisplay = document.getElementById('group-selected-count');
        const groupTabCountDisplay = document.getElementById('group-count');
        const count = selectedGroups.size;

        if (groupCountDisplay) {
            groupCountDisplay.textContent = count + ' groups selected';
        }

        if (groupTabCountDisplay) {
            groupTabCountDisplay.textContent = count + ' selected';

            // Update text color only
            const groupTabButton = document.querySelector('[data-tab="by-group"]');

            if (count > 0 && groupTabButton && groupTabButton.classList.contains('active')) {
                groupTabCountDisplay.classList.add('text-primary');
                groupTabCountDisplay.classList.remove('text-gray-700');
            } else {
                groupTabCountDisplay.classList.remove('text-primary');
                groupTabCountDisplay.classList.add('text-gray-700');
            }
        }
    }

    // Function to update the "All Members" count display
    function updateAllMembersCount() {
        const allMembersCountDisplay = document.getElementById('all-members-count');
        const allMembersTabButton = document.querySelector('[data-tab="all-members"]');

        if (allMembersCountDisplay && allMembersCheckbox) {
            if (allMembersCheckbox.checked) {
                const totalCount = document.getElementById('total-members-count').textContent;
                allMembersCountDisplay.textContent = totalCount + ' selected';

                // Update text color for active state
                if (allMembersTabButton && allMembersTabButton.classList.contains('active')) {
                    allMembersCountDisplay.classList.add('text-primary');
                    allMembersCountDisplay.classList.remove('text-gray-700');
                }
            } else {
                allMembersCountDisplay.textContent = '0 selected';

                // Keep neutral styling
                allMembersCountDisplay.classList.remove('text-primary');
                allMembersCountDisplay.classList.add('text-gray-700');
            }
        }
    }

    // Fetch members from API
    function fetchMembers(loadMore = false) {
        if (!loadMore) {
            // Reset pagination when doing a new search
            currentPage = 1;
            document.getElementById('members-loading').classList.remove('hidden');
            document.getElementById('members-container').classList.add('hidden');
            document.getElementById('no-members-message').classList.add('hidden');
            document.getElementById('load-more-container').classList.add('hidden');
        } else {
            // Show loading indicator in the load more button
            const loadMoreBtn = document.getElementById('load-more');
            loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Loading...';
            loadMoreBtn.disabled = true;
        }

        // Build query parameters
        let queryParams = [];
        queryParams.push(`page=${currentPage}`);
        queryParams.push(`limit=${membersPerPage}`);

        if (currentSearch) {
            queryParams.push(`search=${encodeURIComponent(currentSearch)}`);
        }

        // Build the URL
        const url = `${apiBaseUrl}?${queryParams.join('&')}`;

        // Fetch data
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (!loadMore) {
                    // Clear existing members
                    document.getElementById('members-container').innerHTML = '';
                }

                // Process members
                if (data.data && data.data.length > 0) {
                    renderMembers(data.data);
                    document.getElementById('members-container').classList.remove('hidden');

                    // Check if there are more pages
                    hasMoreMembers = currentPage < data.pages;
                    if (hasMoreMembers) {
                        document.getElementById('load-more-container').classList.remove('hidden');
                        document.getElementById('load-more').innerHTML = '<i class="fas fa-sync-alt mr-2"></i> Load More Members';
                        document.getElementById('load-more').disabled = false;
                        currentPage++;
                    } else {
                        document.getElementById('load-more-container').classList.add('hidden');
                    }

                    // Update total count display
                    if (data.total_with_phones) {
                        document.querySelectorAll('#total-members-count').forEach(el => {
                            el.textContent = data.total_with_phones;
                        });
                    }
                } else if (!loadMore) {
                    // Show no members message
                    document.getElementById('no-members-message').classList.remove('hidden');
                }

                // Hide loading indicator
                document.getElementById('members-loading').classList.add('hidden');
            })
            .catch(error => {
                console.error('Error fetching members:', error);
                document.getElementById('members-loading').classList.add('hidden');
                document.getElementById('load-more').innerHTML = '<i class="fas fa-sync-alt mr-2"></i> Load More Members';
                document.getElementById('load-more').disabled = false;
            });
    }

    // Render members in the container
    function renderMembers(members) {
        const container = document.getElementById('members-container');
        const filteredCount = document.getElementById('filtered-count');

        // Update filtered count
        if (filteredCount) {
            filteredCount.textContent = members.length + ' members found';
        }

        members.forEach(member => {
            const memberItem = document.createElement('div');
            memberItem.className = 'member-item flex items-center p-2 hover:bg-gray-100 rounded-md';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `member-${member.id}`;
            checkbox.name = 'recipients[]';
            checkbox.value = member.phone_number;
            checkbox.className = 'member-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded';
            // Check if this member was previously selected
            if (selectedMembers.has(member.id)) {
                checkbox.checked = true;
            }

            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    selectedMembers.add(member.id);
                } else {
                    selectedMembers.delete(member.id);
                }
                updateSelectedCount();
            });

            const label = document.createElement('label');
            label.htmlFor = `member-${member.id}`;
            label.className = 'ml-3 flex-1 text-sm text-gray-700 flex items-center justify-between';

            // Create a more structured layout for member information
            const nameContainer = document.createElement('div');
            nameContainer.className = 'flex items-center';

            const nameSpan = document.createElement('span');
            nameSpan.className = 'font-medium';
            nameSpan.textContent = `${member.first_name} ${member.last_name}`;

            const phoneSpan = document.createElement('span');
            phoneSpan.className = 'text-xs text-gray-500 ml-2';
            phoneSpan.textContent = member.phone_number;

            nameContainer.appendChild(nameSpan);
            nameContainer.appendChild(phoneSpan);

            // Add department/role info if available
            const infoSpan = document.createElement('span');
            infoSpan.className = 'text-xs text-gray-500';

            if (member.department) {
                infoSpan.textContent = member.department;
                if (member.role) {
                    infoSpan.textContent += ` • ${member.role}`;
                }
            } else if (member.role) {
                infoSpan.textContent = member.role;
            }

            label.appendChild(nameContainer);
            if (member.department || member.role) {
                label.appendChild(infoSpan);
            }

            memberItem.appendChild(checkbox);
            memberItem.appendChild(label);
            container.appendChild(memberItem);
        });
    }

    // Reset members list for new search
    function resetMembersList() {
        document.getElementById('members-container').innerHTML = '';
        document.getElementById('members-container').classList.add('hidden');
        document.getElementById('no-members-message').classList.add('hidden');
        document.getElementById('load-more-container').classList.add('hidden');

        // Reset filtered count
        const filteredCount = document.getElementById('filtered-count');
        if (filteredCount) {
            filteredCount.textContent = '0 members found';
        }
    }

    // Function to add bulk recipients (for birthday SMS)
    function addBulkRecipient(phoneNumber, memberName, memberId) {
        // Check if this recipient already exists
        const existingRecipient = document.getElementById(`member-${memberId}`);
        if (existingRecipient) {
            // If it already exists, just check it and return
            existingRecipient.checked = true;
            selectedMembers.add(memberId.toString());
            return;
        }

        // Make sure the container is visible
        const container = document.getElementById('members-container');
        container.classList.remove('hidden');

        // Hide loading and no members messages
        document.getElementById('members-loading').classList.add('hidden');
        document.getElementById('no-members-message').classList.add('hidden');

        // Create a member item for the recipient
        const memberItem = document.createElement('div');
        memberItem.className = 'member-item flex items-center p-2 hover:bg-gray-100 rounded-md bg-blue-50 border border-blue-200';
        memberItem.id = `bulk-recipient-${memberId}`;

        // Create checkbox
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `member-${memberId}`;
        checkbox.name = 'recipients[]';
        checkbox.value = phoneNumber;
        checkbox.className = 'member-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded';
        checkbox.checked = true; // Pre-select this recipient

        // Add to selected members
        selectedMembers.add(memberId.toString());

        // Add change event listener
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                selectedMembers.add(memberId.toString());
            } else {
                selectedMembers.delete(memberId.toString());
            }
            updateSelectedCount();
        });

        // Create label
        const label = document.createElement('label');
        label.htmlFor = `member-${memberId}`;
        label.className = 'ml-3 flex-1 text-sm text-gray-700 flex items-center justify-between cursor-pointer';

        // Create name container
        const nameContainer = document.createElement('div');
        nameContainer.className = 'flex items-center';

        // Create name span
        const nameSpan = document.createElement('span');
        nameSpan.className = 'font-medium text-blue-800';
        nameSpan.textContent = memberName;

        // Create phone span
        const phoneSpan = document.createElement('span');
        phoneSpan.className = 'text-xs text-blue-600 ml-2';
        phoneSpan.textContent = phoneNumber;

        // Create birthday badge
        const birthdayBadge = document.createElement('span');
        birthdayBadge.className = 'ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full';
        birthdayBadge.innerHTML = '<i class="fas fa-birthday-cake mr-1"></i>Birthday';

        // Assemble the elements
        nameContainer.appendChild(nameSpan);
        nameContainer.appendChild(phoneSpan);
        nameContainer.appendChild(birthdayBadge);

        label.appendChild(nameContainer);
        memberItem.appendChild(checkbox);
        memberItem.appendChild(label);

        // Add to container (at the beginning)
        container.insertBefore(memberItem, container.firstChild);
    }

    // Function to add a specific recipient (for visitor follow-up or member selection)
    function addSpecificRecipient(phoneNumber, memberName, memberId) {
        // Use the bulk recipient function for consistency
        addBulkRecipient(phoneNumber, memberName, memberId);
    }

    // Form validation function
    function validateSmsForm() {
        // Check if we have any recipients selected
        const sendToAllChecked = allMembersCheckbox && allMembersCheckbox.checked;
        const groupsSelected = selectedGroups.size > 0;
        const individualMembersSelected = selectedMembers.size > 0;

        // Make sure the send_to_all hidden input is only enabled when the checkbox is checked
        const sendToAllValue = document.getElementById('send-to-all-value');
        if (sendToAllValue) {
            sendToAllValue.disabled = !sendToAllChecked;
        }

        // Check if any recipients are selected
        if (!sendToAllChecked && !groupsSelected && !individualMembersSelected) {
            alert('Please select at least one recipient for your SMS.');
            return false;
        }

        // Check if message is empty
        const messageContent = document.getElementById('message').value.trim();
        if (!messageContent) {
            alert('Please enter a message to send.');
            return false;
        }

        // Disable the tabs that aren't being used to prevent their inputs from being submitted
        if (sendToAllChecked) {
            // Disable group and individual member inputs
            document.querySelectorAll('input[name="recipient_groups[]"]').forEach(input => input.disabled = true);
            document.querySelectorAll('input[name="recipients[]"]').forEach(input => input.disabled = true);
        } else if (groupsSelected) {
            // Disable individual member inputs
            document.querySelectorAll('input[name="recipients[]"]').forEach(input => input.disabled = true);
        }

        return true;
    }

    // When checking "Send to all", uncheck any group checkboxes
    if (allMembersCheckbox) {
        allMembersCheckbox.addEventListener('change', function() {
            // Update the hidden input value based on checkbox state
            const sendToAllValue = document.getElementById('send-to-all-value');
            if (sendToAllValue) {
                sendToAllValue.disabled = !this.checked;
            }

            if (this.checked) {
                // Clear group selections
                groupCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                selectedGroups.clear();
                updateGroupCount();

                // Clear individual member selections
                document.querySelectorAll('.member-checkbox').forEach(checkbox => {
                    checkbox.checked = false;
                });
                selectedMembers.clear();
                updateSelectedCount();
            }
        });

        // Initialize the hidden input state
        const sendToAllValue = document.getElementById('send-to-all-value');
        if (sendToAllValue) {
            sendToAllValue.disabled = !allMembersCheckbox.checked;
        }
    }
</script>

<?php
// Clear form data
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>
