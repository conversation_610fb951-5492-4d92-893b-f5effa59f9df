<?php
/**
 * CSRF and additional security functions for the application
 */

// Note: Authentication functions are now defined in bootstrap.php
// is_logged_in() and has_role() functions have been moved there

// Load centralized utilities
require_once __DIR__ . '/csrf.php';

/**
 * Generate CSRF token (backward compatibility)
 *
 * @return string
 */
function generate_csrf_token() {
    return CSRFProtection::generateToken();
}

/**
 * Verify CSRF token (backward compatibility)
 *
 * @param string $token
 * @return bool
 */
function verify_csrf_token($token) {
    return CSRFProtection::validateToken($token);
}

/**
 * Get CSRF token field (backward compatibility)
 *
 * @return string HTML input field
 */
function csrf_field() {
    return CSRFProtection::getTokenField();
}

/**
 * Alias for verify_csrf_token for compatibility
 *
 * @param string $token
 * @return bool
 */
function validate_csrf_token($token) {
    return verify_csrf_token($token);
}
