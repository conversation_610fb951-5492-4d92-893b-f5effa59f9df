<div class="container mx-auto">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-1">Equipment Reports</h1>
            <p class="text-gray-600">View analytics and reports for equipment inventory and maintenance</p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mt-4 md:mt-0">
            <a href="<?php echo BASE_URL; ?>equipment" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center justify-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Equipment
            </a>
            <button onclick="printReport()" class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md flex items-center justify-center">
                <i class="fas fa-print mr-2"></i> Print Report
            </button>
        </div>
    </div>

    <div id="report-content">
        <!-- Equipment Summary -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Equipment Summary</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-primary bg-opacity-10 mr-4">
                            <i class="fas fa-tools text-primary text-xl"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Equipment</p>
                            <p class="text-2xl font-bold"><?php echo $equipment_stats['total']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 mr-4">
                            <i class="fas fa-money-bill-wave text-green-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Value</p>
                            <p class="text-2xl font-bold">GH₵ <?php echo number_format($equipment_stats['total_value'], 2); ?></p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 mr-4">
                            <i class="fas fa-wrench text-blue-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Maintenance Records</p>
                            <p class="text-2xl font-bold"><?php echo $maintenance_stats['total_records']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 mr-4">
                            <i class="fas fa-chart-line text-red-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Maintenance Cost</p>
                            <p class="text-2xl font-bold">GH₵ <?php echo number_format($maintenance_stats['total_cost'], 2); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Equipment by Category -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Equipment by Category</h2>
                <div class="h-64">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Equipment by Condition</h2>
                <div class="h-64">
                    <canvas id="conditionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Maintenance Cost Chart -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Maintenance Cost by Month (<?php echo date('Y'); ?>)</h2>
            <div class="h-64">
                <canvas id="maintenanceChart"></canvas>
            </div>
        </div>

        <!-- Equipment Condition Breakdown -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Equipment Condition Breakdown</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Condition</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visual</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Excellent</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $equipment_stats['by_condition']['excellent']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $equipment_stats['total'] > 0 ? round(($equipment_stats['by_condition']['excellent'] / $equipment_stats['total']) * 100, 1) : 0; ?>%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-green-600 h-2.5 rounded-full" style="width: <?php echo $equipment_stats['total'] > 0 ? ($equipment_stats['by_condition']['excellent'] / $equipment_stats['total']) * 100 : 0; ?>%"></div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Good</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $equipment_stats['by_condition']['good']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $equipment_stats['total'] > 0 ? round(($equipment_stats['by_condition']['good'] / $equipment_stats['total']) * 100, 1) : 0; ?>%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: <?php echo $equipment_stats['total'] > 0 ? ($equipment_stats['by_condition']['good'] / $equipment_stats['total']) * 100 : 0; ?>%"></div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Fair</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $equipment_stats['by_condition']['fair']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $equipment_stats['total'] > 0 ? round(($equipment_stats['by_condition']['fair'] / $equipment_stats['total']) * 100, 1) : 0; ?>%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-yellow-500 h-2.5 rounded-full" style="width: <?php echo $equipment_stats['total'] > 0 ? ($equipment_stats['by_condition']['fair'] / $equipment_stats['total']) * 100 : 0; ?>%"></div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Poor</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $equipment_stats['by_condition']['poor']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $equipment_stats['total'] > 0 ? round(($equipment_stats['by_condition']['poor'] / $equipment_stats['total']) * 100, 1) : 0; ?>%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-red-600 h-2.5 rounded-full" style="width: <?php echo $equipment_stats['total'] > 0 ? ($equipment_stats['by_condition']['poor'] / $equipment_stats['total']) * 100 : 0; ?>%"></div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Equipment by Category Table -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Equipment by Category</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visual</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($equipment_stats['by_category'] as $category => $count) : ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo ucfirst($category); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $count; ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $equipment_stats['total'] > 0 ? round(($count / $equipment_stats['total']) * 100, 1) : 0; ?>%
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-primary h-2.5 rounded-full" style="width: <?php echo $equipment_stats['total'] > 0 ? ($count / $equipment_stats['total']) * 100 : 0; ?>%"></div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Category Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        const categoryLabels = <?php echo json_encode(array_map('ucfirst', array_keys($equipment_stats['by_category']))); ?>;
        const categoryData = <?php echo json_encode(array_values($equipment_stats['by_category'])); ?>;
        
        new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: categoryLabels,
                datasets: [{
                    data: categoryData,
                    backgroundColor: [
                        'rgba(59, 209, 111, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)'
                    ],
                    borderColor: [
                        'rgba(59, 209, 111, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });

        // Condition Chart
        const conditionCtx = document.getElementById('conditionChart').getContext('2d');
        const conditionLabels = ['Excellent', 'Good', 'Fair', 'Poor'];
        const conditionData = [
            <?php echo $equipment_stats['by_condition']['excellent']; ?>,
            <?php echo $equipment_stats['by_condition']['good']; ?>,
            <?php echo $equipment_stats['by_condition']['fair']; ?>,
            <?php echo $equipment_stats['by_condition']['poor']; ?>
        ];
        
        new Chart(conditionCtx, {
            type: 'doughnut',
            data: {
                labels: conditionLabels,
                datasets: [{
                    data: conditionData,
                    backgroundColor: [
                        'rgba(72, 187, 120, 0.7)',
                        'rgba(66, 153, 225, 0.7)',
                        'rgba(237, 137, 54, 0.7)',
                        'rgba(229, 62, 62, 0.7)'
                    ],
                    borderColor: [
                        'rgba(72, 187, 120, 1)',
                        'rgba(66, 153, 225, 1)',
                        'rgba(237, 137, 54, 1)',
                        'rgba(229, 62, 62, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });

        // Maintenance Cost Chart
        const maintenanceCtx = document.getElementById('maintenanceChart').getContext('2d');
        const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        const maintenanceData = <?php echo json_encode($chart_data); ?>;
        
        new Chart(maintenanceCtx, {
            type: 'bar',
            data: {
                labels: months,
                datasets: [{
                    label: 'Maintenance Cost (GH₵)',
                    data: maintenanceData,
                    backgroundColor: 'rgba(59, 209, 111, 0.6)',
                    borderColor: 'rgba(59, 209, 111, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'GH₵ ' + value;
                            }
                        }
                    }
                }
            }
        });
    });

    // Print report
    function printReport() {
        const printContents = document.getElementById('report-content').innerHTML;
        const originalContents = document.body.innerHTML;

        document.body.innerHTML = `
            <div style="padding: 20px;">
                <h1 style="text-align: center; margin-bottom: 20px;">Equipment Report</h1>
                <p style="text-align: center; margin-bottom: 30px;">
                    Generated on: <?php echo date('F j, Y, g:i a'); ?><br>
                    ICGC Emmanuel Temple
                </p>
                ${printContents}
            </div>
        `;

        window.print();
        document.body.innerHTML = originalContents;
        
        // Reinitialize charts after printing
        document.dispatchEvent(new Event('DOMContentLoaded'));
    }
</script>
