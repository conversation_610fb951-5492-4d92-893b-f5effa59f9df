<?php
/**
 * Database Statistics Model
 * 
 * Handles database statistics, maintenance, and archiving operations
 */
class DatabaseStats {
    private $conn;
    private $maintenance_log_table = 'maintenance_log';

    /**
     * Constructor
     */
    public function __construct($db = null) {
        if ($db === null) {
            // Create database connection
            require_once 'config/database.php';
            $database = new Database();
            $this->conn = $database->getConnection();
        } else {
            $this->conn = $db;
        }
        
        // Create maintenance log table if it doesn't exist
        $this->createMaintenanceLogTable();
    }

    /**
     * Create maintenance log table if it doesn't exist
     */
    private function createMaintenanceLogTable() {
        $query = "CREATE TABLE IF NOT EXISTS " . $this->maintenance_log_table . " (
            id INT AUTO_INCREMENT PRIMARY KEY,
            operation_type VARCHAR(50) NOT NULL,
            operation_details TEXT,
            status VARCHAR(20) NOT NULL,
            executed_by INT,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            affected_tables TEXT,
            affected_records INT,
            duration_seconds FLOAT,
            size_change_mb FLOAT
        )";
        
        try {
            $this->conn->exec($query);
        } catch (PDOException $e) {
            error_log("Error creating maintenance log table: " . $e->getMessage());
        }
    }

    /**
     * Get database statistics
     * 
     * @return array Database statistics
     */
    public function getDatabaseStats() {
        $stats = [
            'total_size' => '0 MB',
            'total_tables' => 0,
            'last_optimized' => 'Never',
            'tables' => [],
            'recommendations' => []
        ];
        
        try {
            // Get database name
            $dbName = $this->getDatabaseName();
            
            // Get total database size
            $sizeQuery = "SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.TABLES
                WHERE table_schema = :db_name";
            
            $sizeStmt = $this->conn->prepare($sizeQuery);
            $sizeStmt->bindParam(':db_name', $dbName);
            $sizeStmt->execute();
            $sizeRow = $sizeStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($sizeRow) {
                $stats['total_size'] = $sizeRow['size_mb'] . ' MB';
            }
            
            // Get table information
            $tableQuery = "SELECT 
                table_name,
                ROUND((data_length + index_length) / 1024 / 1024, 2) AS size_mb,
                table_rows,
                create_time,
                update_time
                FROM information_schema.TABLES
                WHERE table_schema = :db_name
                ORDER BY (data_length + index_length) DESC";
            
            $tableStmt = $this->conn->prepare($tableQuery);
            $tableStmt->bindParam(':db_name', $dbName);
            $tableStmt->execute();
            
            $tables = [];
            while ($row = $tableStmt->fetch(PDO::FETCH_ASSOC)) {
                $tables[] = $row;
            }
            
            $stats['total_tables'] = count($tables);
            $stats['tables'] = $tables;
            
            // Get last optimization time
            $logQuery = "SELECT executed_at FROM " . $this->maintenance_log_table . "
                WHERE operation_type = 'optimization' AND status = 'success'
                ORDER BY executed_at DESC LIMIT 1";
            
            $logStmt = $this->conn->query($logQuery);
            if ($logStmt && $row = $logStmt->fetch(PDO::FETCH_ASSOC)) {
                $stats['last_optimized'] = date('M d, Y', strtotime($row['executed_at']));
            }
            
            // Generate recommendations
            $stats['recommendations'] = $this->generateRecommendations($stats);
            
        } catch (PDOException $e) {
            error_log("Error getting database stats: " . $e->getMessage());
        }
        
        return $stats;
    }
    
    /**
     * Generate maintenance recommendations based on database statistics
     * 
     * @param array $stats Database statistics
     * @return array Recommendations
     */
    private function generateRecommendations($stats) {
        $recommendations = [];
        
        // Check database size
        $sizeInMB = floatval($stats['total_size']);
        if ($sizeInMB > 100) {
            $recommendations[] = "Your database is quite large ({$stats['total_size']}). Consider archiving old data to improve performance.";
        }
        
        // Check last optimization time
        if ($stats['last_optimized'] === 'Never') {
            $recommendations[] = "Your database has never been optimized. Run database optimization to improve performance.";
        } else {
            $lastOptimizedDate = strtotime($stats['last_optimized']);
            $threeMonthsAgo = strtotime('-3 months');
            
            if ($lastOptimizedDate < $threeMonthsAgo) {
                $recommendations[] = "Your database hasn't been optimized in over 3 months. Consider running optimization.";
            }
        }
        
        // Check for large tables
        $largeTables = [];
        foreach ($stats['tables'] as $table) {
            if (floatval($table['size_mb']) > 10) {
                $largeTables[] = $table['table_name'];
            }
        }
        
        if (count($largeTables) > 0) {
            $tableList = implode(', ', $largeTables);
            $recommendations[] = "The following tables are quite large and may benefit from archiving: {$tableList}";
        }
        
        // Check for archive tables
        $archiveTables = array_filter($stats['tables'], function($table) {
            return strpos($table['table_name'], '_archive') !== false;
        });
        
        if (count($archiveTables) === 0) {
            $recommendations[] = "No archive tables found. Consider setting up archiving for your data.";
        }
        
        return $recommendations;
    }
    
    /**
     * Get database name from connection
     * 
     * @return string Database name
     */
    private function getDatabaseName() {
        try {
            $stmt = $this->conn->query("SELECT DATABASE() as db_name");
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['db_name'];
        } catch (PDOException $e) {
            error_log("Error getting database name: " . $e->getMessage());
            return 'icgc_db'; // Default fallback
        }
    }
    
    /**
     * Optimize database tables
     * 
     * @param array $tables Tables to optimize (empty for all tables)
     * @return array Result with status and message
     */
    public function optimizeTables($tables = []) {
        $startTime = microtime(true);
        $result = [
            'status' => 'success',
            'message' => 'Database optimization completed successfully',
            'details' => []
        ];
        
        try {
            // Get all tables if none specified
            if (empty($tables)) {
                $stmt = $this->conn->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            }
            
            // Get database size before optimization
            $dbName = $this->getDatabaseName();
            $sizeBeforeStmt = $this->conn->prepare("SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.TABLES
                WHERE table_schema = :db_name");
            $sizeBeforeStmt->bindParam(':db_name', $dbName);
            $sizeBeforeStmt->execute();
            $sizeBeforeRow = $sizeBeforeStmt->fetch(PDO::FETCH_ASSOC);
            $sizeBefore = $sizeBeforeRow ? floatval($sizeBeforeRow['size_mb']) : 0;
            
            // Optimize each table
            $affectedTables = [];
            foreach ($tables as $table) {
                try {
                    // Use mysqli for OPTIMIZE TABLE to avoid unbuffered query issues
                    // Get database configuration from centralized environment config
                    $host = EnvironmentConfig::get('database.host', 'localhost');
                    $user = EnvironmentConfig::get('database.username', 'root');
                    $pass = EnvironmentConfig::get('database.password', '');
                    $name = EnvironmentConfig::get('database.name', 'icgc_db');

                    $mysqli = new mysqli($host, $user, $pass, $name);
                    $optimizeResult = $mysqli->query("OPTIMIZE TABLE `{$table}`");
                    
                    if ($optimizeResult) {
                        $row = $optimizeResult->fetch_assoc();
                        $status = $row['Msg_text'];
                        $result['details'][] = "Table {$table}: {$status}";
                        $affectedTables[] = $table;
                    } else {
                        $result['details'][] = "Error optimizing table {$table}: " . $mysqli->error;
                    }
                    
                    $optimizeResult->free();
                    $mysqli->close();
                } catch (Exception $e) {
                    $result['details'][] = "Error optimizing table {$table}: " . $e->getMessage();
                }
            }
            
            // Get database size after optimization
            $sizeAfterStmt = $this->conn->prepare("SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.TABLES
                WHERE table_schema = :db_name");
            $sizeAfterStmt->bindParam(':db_name', $dbName);
            $sizeAfterStmt->execute();
            $sizeAfterRow = $sizeAfterStmt->fetch(PDO::FETCH_ASSOC);
            $sizeAfter = $sizeAfterRow ? floatval($sizeAfterRow['size_mb']) : 0;
            
            // Calculate size change
            $sizeChange = $sizeBefore - $sizeAfter;
            
            // Calculate duration
            $endTime = microtime(true);
            $duration = $endTime - $startTime;
            
            // Log the maintenance operation
            $this->logMaintenanceOperation(
                'optimization',
                'Optimized ' . count($affectedTables) . ' tables',
                'success',
                implode(',', $affectedTables),
                count($affectedTables),
                $duration,
                $sizeChange
            );
            
            // Add size change to result
            if ($sizeChange > 0) {
                $result['message'] .= " (saved " . number_format($sizeChange, 2) . " MB)";
            }
            
        } catch (Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'Error optimizing database: ' . $e->getMessage();
            
            // Log the failed operation
            $this->logMaintenanceOperation(
                'optimization',
                'Error: ' . $e->getMessage(),
                'error',
                '',
                0,
                microtime(true) - $startTime,
                0
            );
        }
        
        return $result;
    }
    
    /**
     * Log a maintenance operation
     * 
     * @param string $operationType Type of operation (optimization, archiving, etc.)
     * @param string $details Operation details
     * @param string $status Operation status (success, error)
     * @param string $affectedTables Comma-separated list of affected tables
     * @param int $affectedRecords Number of affected records
     * @param float $duration Duration in seconds
     * @param float $sizeChange Size change in MB
     * @return bool Success
     */
    public function logMaintenanceOperation($operationType, $details, $status, $affectedTables, $affectedRecords, $duration, $sizeChange) {
        try {
            $query = "INSERT INTO " . $this->maintenance_log_table . "
                (operation_type, operation_details, status, executed_by, affected_tables, affected_records, duration_seconds, size_change_mb)
                VALUES (:operation_type, :operation_details, :status, :executed_by, :affected_tables, :affected_records, :duration_seconds, :size_change_mb)";
            
            $stmt = $this->conn->prepare($query);
            
            $executedBy = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
            
            $stmt->bindParam(':operation_type', $operationType);
            $stmt->bindParam(':operation_details', $details);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':executed_by', $executedBy);
            $stmt->bindParam(':affected_tables', $affectedTables);
            $stmt->bindParam(':affected_records', $affectedRecords);
            $stmt->bindParam(':duration_seconds', $duration);
            $stmt->bindParam(':size_change_mb', $sizeChange);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error logging maintenance operation: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get maintenance log entries
     * 
     * @param int $limit Number of entries to return
     * @param int $offset Offset for pagination
     * @return array Maintenance log entries
     */
    public function getMaintenanceLog($limit = 20, $offset = 0) {
        $entries = [];
        
        try {
            $query = "SELECT m.*, u.username as executed_by_name
                FROM " . $this->maintenance_log_table . " m
                LEFT JOIN users u ON m.executed_by = u.id
                ORDER BY m.executed_at DESC
                LIMIT :limit OFFSET :offset";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $entries[] = $row;
            }
        } catch (PDOException $e) {
            error_log("Error getting maintenance log: " . $e->getMessage());
        }
        
        return $entries;
    }
    
    /**
     * Get total count of maintenance log entries
     * 
     * @return int Total count
     */
    public function getMaintenanceLogCount() {
        try {
            $query = "SELECT COUNT(*) as count FROM " . $this->maintenance_log_table;
            $stmt = $this->conn->query($query);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return (int)$row['count'];
        } catch (PDOException $e) {
            error_log("Error getting maintenance log count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get archivable data types and their counts
     * 
     * @return array Data types and counts
     */
    public function getArchivableDataTypes() {
        $dataTypes = [
            'finances' => [
                'name' => 'Financial Transactions',
                'count' => 0,
                'archivable_count' => 0,
                'has_archive_table' => false,
                'archive_table' => 'finances_archive',
                'oldest_record' => null,
                'newest_record' => null
            ],
            'attendance' => [
                'name' => 'Attendance Records',
                'count' => 0,
                'archivable_count' => 0,
                'has_archive_table' => false,
                'archive_table' => 'attendance_archive',
                'oldest_record' => null,
                'newest_record' => null
            ],
            'sms_messages' => [
                'name' => 'SMS Messages',
                'count' => 0,
                'archivable_count' => 0,
                'has_archive_table' => false,
                'archive_table' => 'sms_messages_archive',
                'oldest_record' => null,
                'newest_record' => null
            ],
            'visitors' => [
                'name' => 'Visitor Records',
                'count' => 0,
                'archivable_count' => 0,
                'has_archive_table' => false,
                'archive_table' => 'visitors_archive',
                'oldest_record' => null,
                'newest_record' => null
            ],
            'equipment_maintenance' => [
                'name' => 'Equipment Maintenance',
                'count' => 0,
                'archivable_count' => 0,
                'has_archive_table' => false,
                'archive_table' => 'equipment_maintenance_archive',
                'oldest_record' => null,
                'newest_record' => null
            ]
        ];
        
        try {
            // Check finances
            $this->getTableStats($dataTypes['finances'], 'finances', 'transaction_date', 'created_at', '-1 year');
            
            // Check attendance
            $this->getTableStats($dataTypes['attendance'], 'attendance', 'attendance_date', 'created_at', '-1 year');
            
            // Check SMS messages
            $this->getTableStats($dataTypes['sms_messages'], 'sms_messages', 'sent_at', 'created_at', '-6 months');
            
            // Check visitors
            $this->getTableStats($dataTypes['visitors'], 'visitors', 'visit_date', 'created_at', '-1 year');
            
            // Check equipment maintenance
            $this->getTableStats($dataTypes['equipment_maintenance'], 'equipment_maintenance', 'maintenance_date', 'created_at', '-2 years');
            
        } catch (PDOException $e) {
            error_log("Error getting archivable data types: " . $e->getMessage());
        }
        
        return $dataTypes;
    }
    
    /**
     * Get table statistics for archiving
     * 
     * @param array &$dataType Data type array to update
     * @param string $tableName Table name
     * @param string $dateField Date field for archiving
     * @param string $createdField Created date field
     * @param string $cutoffPeriod Cutoff period for archiving (e.g., '-1 year')
     */
    private function getTableStats(&$dataType, $tableName, $dateField, $createdField, $cutoffPeriod) {
        // Check if table exists
        $tableExistsQuery = "SHOW TABLES LIKE '{$tableName}'";
        $tableExistsStmt = $this->conn->query($tableExistsQuery);
        
        if ($tableExistsStmt && $tableExistsStmt->rowCount() > 0) {
            // Get total count
            $countQuery = "SELECT COUNT(*) as count FROM {$tableName}";
            $countStmt = $this->conn->query($countQuery);
            $countRow = $countStmt->fetch(PDO::FETCH_ASSOC);
            $dataType['count'] = (int)$countRow['count'];
            
            // Get oldest and newest records
            $oldestQuery = "SELECT MIN({$dateField}) as oldest FROM {$tableName}";
            $oldestStmt = $this->conn->query($oldestQuery);
            $oldestRow = $oldestStmt->fetch(PDO::FETCH_ASSOC);
            $dataType['oldest_record'] = $oldestRow['oldest'];
            
            $newestQuery = "SELECT MAX({$dateField}) as newest FROM {$tableName}";
            $newestStmt = $this->conn->query($newestQuery);
            $newestRow = $newestStmt->fetch(PDO::FETCH_ASSOC);
            $dataType['newest_record'] = $newestRow['newest'];
            
            // Get archivable count
            $cutoffDate = date('Y-m-d', strtotime($cutoffPeriod));
            $archivableQuery = "SELECT COUNT(*) as count FROM {$tableName} WHERE {$dateField} < '{$cutoffDate}'";
            $archivableStmt = $this->conn->query($archivableQuery);
            $archivableRow = $archivableStmt->fetch(PDO::FETCH_ASSOC);
            $dataType['archivable_count'] = (int)$archivableRow['count'];
            
            // Check if archive table exists
            $archiveTableQuery = "SHOW TABLES LIKE '{$dataType['archive_table']}'";
            $archiveTableStmt = $this->conn->query($archiveTableQuery);
            $dataType['has_archive_table'] = ($archiveTableStmt && $archiveTableStmt->rowCount() > 0);
        }
    }
}
?>
