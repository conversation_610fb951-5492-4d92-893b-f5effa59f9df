<?php
/**
 * Ministry Departments Show View
 * Display details of a single ministry department
 */
?>

<div class="max-w-4xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900"><?php echo htmlspecialchars($department['name']); ?></h1>
            <p class="text-gray-600 mt-1">Ministry Department Details</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>ministry-departments/<?php echo $department['id']; ?>/edit" 
               class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-edit mr-2"></i>
                Edit Department
            </a>
            <a href="<?php echo BASE_URL; ?>ministry-departments" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to List
            </a>
        </div>
    </div>

    <!-- Department Details -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <!-- Header -->
        <div class="bg-primary px-6 py-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-building text-white text-xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-white"><?php echo htmlspecialchars($department['name']); ?></h2>
                    <p class="text-primary-light">Department ID: <?php echo htmlspecialchars($department['id']); ?></p>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        Basic Information
                    </h3>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Department Name</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($department['name']); ?></p>
                    </div>

                    <?php if (!empty($department['description'])): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Description</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo nl2br(htmlspecialchars($department['description'])); ?></p>
                        </div>
                    <?php endif; ?>

                    <div>
                        <label class="block text-sm font-medium text-gray-500">Status</label>
                        <div class="mt-1">
                            <?php if (isset($department['is_active']) && $department['is_active']): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Active
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-pause-circle mr-1"></i>
                                    Inactive
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Management Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        Management
                    </h3>

                    <div>
                        <label class="block text-sm font-medium text-gray-500">Head of Department</label>
                        <p class="mt-1 text-sm text-gray-900">
                            <?php if (!empty($department['head_of_department'])): ?>
                                <?php echo htmlspecialchars($department['head_of_department']); ?>
                            <?php else: ?>
                                <span class="text-gray-400 italic">Not assigned</span>
                            <?php endif; ?>
                        </p>
                    </div>

                    <?php if (!empty($department['created_at'])): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Created</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo date('F j, Y \a\t g:i A', strtotime($department['created_at'])); ?>
                            </p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($department['updated_at']) && $department['updated_at'] !== $department['created_at']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Last Updated</label>
                            <p class="mt-1 text-sm text-gray-900">
                                <?php echo date('F j, Y \a\t g:i A', strtotime($department['updated_at'])); ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    Department information and assignments
                </div>
                <div class="flex space-x-3">
                    <a href="<?php echo BASE_URL; ?>ministry-departments/<?php echo $department['id']; ?>/edit" 
                       class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">
                        <i class="fas fa-edit mr-1"></i>Edit Department
                    </a>
                    <button type="button" 
                            class="text-red-600 hover:text-red-800 text-sm font-medium"
                            onclick="confirmDelete(<?php echo $department['id']; ?>, '<?php echo htmlspecialchars($department['name'], ENT_QUOTES); ?>')">
                        <i class="fas fa-trash mr-1"></i>Delete Department
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Confirm Delete</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete the department "<span id="departmentName" class="font-medium"></span>"?
                </p>
                <p class="text-sm text-red-600 mt-2">
                    <strong>Warning:</strong> This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="cancelDelete" 
                        class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-600 transition-colors">
                    Cancel
                </button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" 
                            class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700 transition-colors">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(departmentId, departmentName) {
    document.getElementById('departmentName').textContent = departmentName;
    document.getElementById('deleteForm').action = '<?php echo BASE_URL; ?>ministry-departments/' + departmentId;
    document.getElementById('deleteModal').classList.remove('hidden');
}

// Close modal handlers
document.getElementById('cancelDelete').addEventListener('click', function() {
    document.getElementById('deleteModal').classList.add('hidden');
});

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
    }
});
</script>
