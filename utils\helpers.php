<?php
/**
 * Authentication and User-related Helper Functions
 */

/**
 * Check if a user is blocked
 *
 * @param int $user_id
 * @return bool
 */
function is_user_blocked($user_id) {
    // If status is in session, use that
    if (isset($_SESSION['user_status'])) {
        return $_SESSION['user_status'] === 'blocked';
    }

    // Otherwise, check from database
    require_once 'models/User.php';
    require_once 'config/database.php';

    $database = new Database();
    $user = new User($database->getConnection());

    if ($user->getById($user_id)) {
        // Update status in session
        $_SESSION['user_status'] = $user->status ?? 'active';

        return $user->status === 'blocked';
    }

    return false;
}

// Redirect function removed - using the one from helpers/functions.php instead

/**
 * Display flash message
 *
 * @return string
 */
function flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);

        return '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">
                    ' . $message . '
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>';
    }
    return '';
}

/**
 * Set flash message
 *
 * @param string $message
 * @param string $type
 * @return void
 */
if (!function_exists('set_flash_message')) {
    function set_flash_message($message, $type = 'info') {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }
}

/**
 * Sanitize input data
 *
 * @param string $data
 * @return string
 */
if (!function_exists('sanitize')) {
    function sanitize($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data);
        return $data;
    }
}

/**
 * Generate random string
 *
 * @param int $length
 * @return string
 */
if (!function_exists('generate_random_string')) {
    function generate_random_string($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}

/**
 * Format date
 *
 * @param string $date
 * @param string $format
 * @return string
 */
if (!function_exists('format_date')) {
    function format_date($date, $format = 'd M, Y') {
        return date($format, strtotime($date));
    }
}

/**
 * Get current user
 *
 * @return array|null
 */
function current_user() {
    if (isset($_SESSION['user_id'])) {
        // Return user data from session
        return [
            'id' => $_SESSION['user_id'],
            'name' => $_SESSION['user_name'] ?? 'Admin User',
            'email' => $_SESSION['user_email'] ?? '<EMAIL>',
            'role' => $_SESSION['user_role'] ?? 'admin'
        ];
    }
    return null;
}
