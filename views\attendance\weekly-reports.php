<div class="container mx-auto px-4 max-w-7xl">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-blue-600 to-indigo-700 p-6 text-white">
            <div class="flex flex-row justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Weekly Attendance Reports</h1>
                    <p class="text-sm opacity-90 mt-1">Analyze attendance data for specific time periods</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Attendance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Filter Options</h2>
        </div>
        <div class="p-4">
            <form action="<?php echo BASE_URL; ?>attendance/weekly-reports" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="date" id="start_date" name="start_date" value="<?php echo $start_date; ?>" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2 px-3">
                </div>
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="date" id="end_date" name="end_date" value="<?php echo $end_date; ?>" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2 px-3">
                </div>
                <div>
                    <label for="service_id" class="block text-sm font-medium text-gray-700 mb-1">Service</label>
                    <select id="service_id" name="service_id" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2 px-3">
                        <option value="">All Services</option>
                        <?php foreach ($services as $service) : ?>
                            <option value="<?php echo $service->id; ?>" <?php echo (isset($_GET['service_id']) && $_GET['service_id'] == $service->id) ? 'selected' : ''; ?>>
                                <?php echo $service->name; ?> (<?php echo ucfirst($service->day_of_week); ?> - <?php echo date('h:i A', strtotime($service->time)); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200 w-full justify-center">
                        <i class="fas fa-filter mr-2"></i> Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Date Presets -->
    <div class="flex flex-wrap gap-2 mb-6">
        <a href="<?php echo BASE_URL; ?>attendance/weekly-reports?start_date=<?php echo date('Y-m-d', strtotime('monday this week')); ?>&end_date=<?php echo date('Y-m-d', strtotime('sunday this week')); ?><?php echo isset($_GET['service_id']) ? '&service_id=' . $_GET['service_id'] : ''; ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 py-1.5 px-3 rounded-lg text-xs font-medium transition-all duration-200">
            This Week
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/weekly-reports?start_date=<?php echo date('Y-m-d', strtotime('monday last week')); ?>&end_date=<?php echo date('Y-m-d', strtotime('sunday last week')); ?><?php echo isset($_GET['service_id']) ? '&service_id=' . $_GET['service_id'] : ''; ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 py-1.5 px-3 rounded-lg text-xs font-medium transition-all duration-200">
            Last Week
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/weekly-reports?start_date=<?php echo date('Y-m-01'); ?>&end_date=<?php echo date('Y-m-t'); ?><?php echo isset($_GET['service_id']) ? '&service_id=' . $_GET['service_id'] : ''; ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 py-1.5 px-3 rounded-lg text-xs font-medium transition-all duration-200">
            This Month
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/weekly-reports?start_date=<?php echo date('Y-m-01', strtotime('-1 month')); ?>&end_date=<?php echo date('Y-m-t', strtotime('-1 month')); ?><?php echo isset($_GET['service_id']) ? '&service_id=' . $_GET['service_id'] : ''; ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 py-1.5 px-3 rounded-lg text-xs font-medium transition-all duration-200">
            Last Month
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/weekly-reports?start_date=<?php echo date('Y-01-01'); ?>&end_date=<?php echo date('Y-12-31'); ?><?php echo isset($_GET['service_id']) ? '&service_id=' . $_GET['service_id'] : ''; ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 py-1.5 px-3 rounded-lg text-xs font-medium transition-all duration-200">
            This Year
        </a>
    </div>

    <!-- Attendance Summary -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Attendance Summary</h2>
            <p class="text-sm text-gray-600">
                <?php echo format_date($start_date); ?> to <?php echo format_date($end_date); ?>
                <?php if (isset($_GET['service_id']) && !empty($_GET['service_id'])) : ?>
                    <?php foreach ($services as $service) : ?>
                        <?php if ($service->id == $_GET['service_id']) : ?>
                            (<?php echo $service->name; ?>)
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </p>
        </div>
        <div class="p-6">
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Services -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-church text-xl"></i>
                        </div>
                        <div class="text-3xl font-bold text-blue-700"><?php echo $attendance_summary['total_services']; ?></div>
                    </div>
                    <h3 class="text-sm font-medium text-gray-700">Total Services</h3>
                    <p class="text-xs text-gray-500 mt-1">Number of services in the selected period</p>
                </div>

                <!-- Total Attendance -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-users text-xl"></i>
                        </div>
                        <div class="text-3xl font-bold text-green-700"><?php echo $attendance_summary['total_attendance']; ?></div>
                    </div>
                    <h3 class="text-sm font-medium text-gray-700">Total Attendance</h3>
                    <p class="text-xs text-gray-500 mt-1">Sum of all attendance records</p>
                </div>

                <!-- Average Attendance -->
                <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-100 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                        <div class="text-3xl font-bold text-purple-700"><?php echo $attendance_summary['average_attendance']; ?></div>
                    </div>
                    <h3 class="text-sm font-medium text-gray-700">Average Attendance</h3>
                    <p class="text-xs text-gray-500 mt-1">Average attendance per service</p>
                </div>

                <!-- Attendance Rate -->
                <div class="bg-gradient-to-br from-amber-50 to-orange-50 rounded-xl p-6 border border-amber-100 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 rounded-full bg-amber-100 text-amber-600">
                            <i class="fas fa-percentage text-xl"></i>
                        </div>
                        <div class="text-3xl font-bold text-amber-700"><?php echo $attendance_summary['attendance_rate']; ?>%</div>
                    </div>
                    <h3 class="text-sm font-medium text-gray-700">Attendance Rate</h3>
                    <p class="text-xs text-gray-500 mt-1">Percentage of present and late members</p>
                </div>
            </div>

            <!-- Attendance by Status -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Present Members -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-base font-semibold text-gray-800">Present</h3>
                        <div class="text-2xl font-bold text-green-600"><?php echo $attendance_summary['total_present']; ?></div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div class="bg-green-500 h-2.5 rounded-full" style="width: <?php echo $attendance_summary['total_attendance'] > 0 ? ($attendance_summary['total_present'] / $attendance_summary['total_attendance'] * 100) : 0; ?>%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-2 text-right">
                        <?php echo $attendance_summary['total_attendance'] > 0 ? round($attendance_summary['total_present'] / $attendance_summary['total_attendance'] * 100) : 0; ?>% of total
                    </p>
                </div>

                <!-- Absent Members -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-base font-semibold text-gray-800">Absent</h3>
                        <div class="text-2xl font-bold text-red-600"><?php echo $attendance_summary['total_absent']; ?></div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div class="bg-red-500 h-2.5 rounded-full" style="width: <?php echo $attendance_summary['total_attendance'] > 0 ? ($attendance_summary['total_absent'] / $attendance_summary['total_attendance'] * 100) : 0; ?>%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-2 text-right">
                        <?php echo $attendance_summary['total_attendance'] > 0 ? round($attendance_summary['total_absent'] / $attendance_summary['total_attendance'] * 100) : 0; ?>% of total
                    </p>
                </div>

                <!-- Late Members -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-base font-semibold text-gray-800">Late</h3>
                        <div class="text-2xl font-bold text-yellow-600"><?php echo $attendance_summary['total_late']; ?></div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div class="bg-yellow-500 h-2.5 rounded-full" style="width: <?php echo $attendance_summary['total_attendance'] > 0 ? ($attendance_summary['total_late'] / $attendance_summary['total_attendance'] * 100) : 0; ?>%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-2 text-right">
                        <?php echo $attendance_summary['total_attendance'] > 0 ? round($attendance_summary['total_late'] / $attendance_summary['total_attendance'] * 100) : 0; ?>% of total
                    </p>
                </div>
            </div>

            <!-- Attendance by Day of Week -->
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-8">
                <h3 class="text-base font-semibold text-gray-800 mb-4">Attendance by Day of Week</h3>
                <div class="grid grid-cols-1 md:grid-cols-7 gap-4">
                    <?php
                    $days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                    foreach ($days_of_week as $day) :
                        $day_data = $attendance_summary['attendance_by_day'][$day] ?? ['present' => 0, 'absent' => 0, 'late' => 0, 'total' => 0];
                        $day_total = $day_data['total'];
                        $day_rate = $day_total > 0 ? round(($day_data['present'] + $day_data['late']) / $day_total * 100) : 0;
                    ?>
                        <div class="bg-gray-50 rounded-lg p-3 text-center">
                            <div class="text-sm font-medium text-gray-700 mb-2"><?php echo $day; ?></div>
                            <div class="text-2xl font-bold <?php echo $day_total > 0 ? 'text-blue-600' : 'text-gray-400'; ?>"><?php echo $day_total; ?></div>
                            <?php if ($day_total > 0) : ?>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-2 mb-1">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: <?php echo $day_rate; ?>%"></div>
                                </div>
                                <div class="text-xs text-gray-500"><?php echo $day_rate; ?>% present</div>
                            <?php else : ?>
                                <div class="text-xs text-gray-500 mt-2">No services</div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Attendance by Service -->
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                <h3 class="text-base font-semibold text-gray-800 mb-4">Attendance by Service</h3>
                <?php if (empty($attendance_summary['attendance_by_service'])) : ?>
                    <div class="text-center py-8 text-gray-500">
                        <div class="p-3 rounded-full bg-gray-100 text-gray-400 mx-auto mb-3 w-12 h-12 flex items-center justify-center">
                            <i class="fas fa-church text-xl"></i>
                        </div>
                        <p>No attendance data available for the selected period</p>
                    </div>
                <?php else : ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                    <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-green-500 uppercase tracking-wider">Present</th>
                                    <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-red-500 uppercase tracking-wider">Absent</th>
                                    <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-yellow-500 uppercase tracking-wider">Late</th>
                                    <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($attendance_summary['attendance_by_service'] as $service_name => $service_data) : ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo $service_name; ?></td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center"><?php echo $service_data['total']; ?></td>
                                        <td class="px-4 py-3 whitespace-nowrap text-center">
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                <?php echo $service_data['present']; ?>
                                            </span>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-center">
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                <?php echo $service_data['absent']; ?>
                                            </span>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-center">
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                <?php echo $service_data['late']; ?>
                                            </span>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center">
                                            <?php
                                            $rate = $service_data['total'] > 0 ? round(($service_data['present'] + $service_data['late']) / $service_data['total'] * 100) : 0;
                                            $rate_color = $rate >= 70 ? 'text-green-600' : ($rate >= 50 ? 'text-yellow-600' : 'text-red-600');
                                            ?>
                                            <span class="font-medium <?php echo $rate_color; ?>"><?php echo $rate; ?>%</span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Weekly Attendance Records -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Attendance Records</h2>
        </div>
        <div class="p-4">
            <?php if (empty($weekly_data)) : ?>
                <div class="text-center py-8 text-gray-500">
                    <div class="p-3 rounded-full bg-gray-100 text-gray-400 mx-auto mb-3 w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-calendar-times text-xl"></i>
                    </div>
                    <p>No attendance records found for the selected period</p>
                </div>
            <?php else : ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-green-500 uppercase tracking-wider">Present</th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-red-500 uppercase tracking-wider">Absent</th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-yellow-500 uppercase tracking-wider">Late</th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($weekly_data as $record) : ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo format_date($record['date']); ?>
                                        <div class="text-xs text-gray-500"><?php echo date('l', strtotime($record['date'])); ?></div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $record['service_name']; ?>
                                        <div class="text-xs text-gray-400"><?php echo date('h:i A', strtotime($record['time'])); ?></div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <?php echo $record['present_count'] + $record['absent_count'] + $record['late_count']; ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-center">
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            <?php echo $record['present_count']; ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-center">
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            <?php echo $record['absent_count']; ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-center">
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            <?php echo $record['late_count']; ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <a href="<?php echo BASE_URL; ?>attendance/view-by-date?date=<?php echo $record['date']; ?>&service_id=<?php echo $record['service_id']; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Export Options -->
    <div class="flex justify-end mb-6">
        <div class="flex space-x-2">
            <button type="button" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                <i class="fas fa-file-excel mr-2"></i> Export to Excel
            </button>
            <button type="button" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                <i class="fas fa-file-pdf mr-2"></i> Export to PDF
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add any JavaScript functionality here
    });
</script>
