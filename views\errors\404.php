<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php
    // Get church name from settings for dynamic page title
    require_once __DIR__ . '/../../models/Setting.php';
    require_once __DIR__ . '/../../config/database.php';
    try {
        $database = new Database();
        $setting = new Setting($database->getConnection());
        $layout_settings = $setting->getAllAsArray();
        $church_name = $layout_settings['church_name'] ?? 'ICGC Emmanuel Temple';
    } catch (Exception $e) {
        $church_name = 'ICGC Emmanuel Temple';
    }
    ?>
    <title>Page Not Found - <?php echo htmlspecialchars($church_name); ?></title>
    <link rel="icon" href="<?php echo defined('BASE_URL') ? BASE_URL : '/icgc/'; ?>assets/images/icgc.png" type="image/png">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#3F7D58',
                        'primary-light': '#5a9e76',
                        'primary-dark': '#2c5a3f',
                        'secondary': '#D3E671',
                        'secondary-light': '#e1ef9a',
                        'secondary-dark': '#b8c95c',
                    },
                    backgroundImage: {
                        // Exact gradient from the reference image
                        'sidebar-theme': 'linear-gradient(to bottom, #2D5A3D 0%, #3A6B4A 8%, #477C57 16%, #548D64 24%, #619E71 32%, #6EAF7E 40%, #7BC08B 48%, #88D198 56%, #95E2A5 64%, #A2F3B2 72%, #AFE2BF 80%, #BCE5CC 88%, #C9E8D9 96%, #D6EBE6 100%)',
                    }
                }
            }
        }
    </script>
    <style>
        .error-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8ec 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center error-container">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-lg border-t-4 border-primary">
        <div class="text-center mb-8">
            <div class="flex justify-center mb-6">
                <div class="w-24 h-24 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Page Not Found</h1>
            <p class="text-gray-600 mb-6">We couldn't find the page you were looking for.</p>
            
            <div class="bg-gray-100 p-4 rounded-lg text-left mb-6">
                <p class="text-gray-700 mb-2"><strong>What might have happened?</strong></p>
                <ul class="text-gray-600 text-sm list-disc list-inside space-y-1">
                    <li>The page may have been moved or deleted</li>
                    <li>You might have typed the address incorrectly</li>
                    <li>You may have used an outdated or broken link</li>
                </ul>
            </div>
            
            <div class="flex flex-col space-y-3">
                <a href="<?php echo defined('BASE_URL') ? BASE_URL : '/icgc/'; ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200">
                    Return to Dashboard
                </a>
                <a href="javascript:history.back()" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200">
                    Go Back
                </a>
            </div>
        </div>
        
        <div class="text-center text-gray-500 text-xs">
            <p>If you believe this page should exist, please contact the system administrator.</p>
        </div>
    </div>
</body>
</html> 