<div class="page-content-centered">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-green-600 to-teal-500 p-6 text-white">
            <div class="flex flex-row justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Service Management</h1>
                    <p class="text-sm opacity-90 mt-1">Manage church services for attendance tracking</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Attendance
                    </a>
                    <a href="<?php echo BASE_URL; ?>services/add" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-plus mr-2"></i> Add New Service
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <p class="font-bold">Please fix the following errors:</p>
            <ul class="list-disc ml-5 mt-2">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Success Message -->
    <?php if (isset($_SESSION['success_message'])) : ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
            <p><?php echo $_SESSION['success_message']; ?></p>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <!-- Services List -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-green-50 to-teal-50 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-green-100 text-green-600 mr-3">
                    <i class="fas fa-church text-lg"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-800">Church Services</h2>
                    <p class="text-sm text-gray-600">All available services for attendance tracking</p>
                </div>
            </div>
        </div>

        <div class="p-6">
            <?php if (empty($services)) : ?>
                <div class="text-center py-8 text-gray-500">
                    <div class="p-3 rounded-full bg-gray-100 text-gray-400 mx-auto mb-3 w-16 h-16 flex items-center justify-center">
                        <i class="fas fa-church text-2xl"></i>
                    </div>
                    <p class="text-lg font-medium">No services found</p>
                    <p class="text-sm text-gray-400 mt-1">Add a new service to get started</p>
                    <div class="mt-4">
                        <a href="<?php echo BASE_URL; ?>services/add" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg inline-flex items-center text-sm transition-all duration-200">
                            <i class="fas fa-plus mr-2"></i> Add New Service
                        </a>
                    </div>
                </div>
            <?php else : ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Day</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($services as $service) : ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                                <i class="fas fa-church text-green-600"></i>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><?php echo $service->name; ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            <?php echo ucfirst($service->day_of_week); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('h:i A', strtotime($service->time)); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                                        <?php echo !empty($service->description) ? $service->description : '<span class="text-gray-400 italic">No description</span>'; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <a href="<?php echo url('services/' . $service->id); ?>" class="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-2 py-1 rounded-md transition-colors duration-200 mr-1">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="<?php echo url('services/' . $service->id . '/edit'); ?>" class="text-indigo-600 hover:text-indigo-900 bg-indigo-50 hover:bg-indigo-100 px-2 py-1 rounded-md transition-colors duration-200">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="#" onclick="confirmDelete(<?php echo $service->id; ?>); return false;" class="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 px-2 py-1 rounded-md transition-colors duration-200">
                                                <i class="fas fa-trash-alt"></i> Delete
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 backdrop-blur-sm">
    <div class="relative top-20 mx-auto p-5 border border-gray-200 w-96 shadow-lg rounded-lg bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-3">Delete Service</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete this service? This action cannot be undone.
                </p>
            </div>
            <div class="flex justify-center mt-4 gap-4">
                <button id="cancelDelete" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none">
                    Cancel
                </button>
                <form id="deleteForm" action="<?php echo BASE_URL; ?>services/delete" method="POST" class="inline-block">
                    <input type="hidden" name="id" id="deleteServiceId">
                    <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(id) {
        const modal = document.getElementById('deleteModal');
        const deleteIdInput = document.getElementById('deleteServiceId');

        modal.classList.remove('hidden');
        deleteIdInput.value = id;

        // Close modal when cancel button is clicked
        document.getElementById('cancelDelete').addEventListener('click', function() {
            modal.classList.add('hidden');
        });

        // Close modal when clicking outside the modal
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });
    }
</script>
