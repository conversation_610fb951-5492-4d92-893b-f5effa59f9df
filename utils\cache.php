<?php
/**
 * Cache Utility
 * 
 * Provides caching functionality for frequently accessed data
 */

// Set cache directory path
define('CACHE_DIR', __DIR__ . '/../cache');

// Create cache directory if it doesn't exist
if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

/**
 * Get data from cache
 * 
 * @param string $key Cache key
 * @param int $ttl Time to live in seconds (0 = never expires)
 * @return mixed|null Cached data or null if not found/expired
 */
function cache_get($key) {
    $cache_file = CACHE_DIR . '/' . md5($key) . '.cache';
    
    if (!file_exists($cache_file)) {
        return null;
    }
    
    $cache_data = file_get_contents($cache_file);
    $data = unserialize($cache_data);
    
    // Check if cache has expired
    if ($data['expires'] > 0 && $data['expires'] < time()) {
        unlink($cache_file); // Remove expired cache
        return null;
    }
    
    return $data['content'];
}

/**
 * Store data in cache
 * 
 * @param string $key Cache key
 * @param mixed $data Data to cache
 * @param int $ttl Time to live in seconds (0 = never expires)
 * @return bool True if stored successfully
 */
function cache_set($key, $data, $ttl = 3600) {
    $cache_file = CACHE_DIR . '/' . md5($key) . '.cache';
    
    $expires = ($ttl > 0) ? time() + $ttl : 0;
    
    $cache_data = [
        'content' => $data,
        'expires' => $expires,
        'created' => time()
    ];
    
    return file_put_contents($cache_file, serialize($cache_data)) !== false;
}

/**
 * Remove data from cache
 * 
 * @param string $key Cache key
 * @return bool True if removed successfully or didn't exist
 */
function cache_delete($key) {
    $cache_file = CACHE_DIR . '/' . md5($key) . '.cache';
    
    if (file_exists($cache_file)) {
        return unlink($cache_file);
    }
    
    return true;
}

/**
 * Get data from cache or run callback to get fresh data
 * 
 * @param string $key Cache key
 * @param callable $callback Function to call if cache miss
 * @param int $ttl Time to live in seconds
 * @return mixed Data from cache or callback
 */
function cache_remember($key, $callback, $ttl = 3600) {
    $data = cache_get($key);
    
    if ($data !== null) {
        return $data;
    }
    
    $fresh_data = $callback();
    cache_set($key, $fresh_data, $ttl);
    
    return $fresh_data;
}

/**
 * Clear all cache or by prefix
 * 
 * @param string $prefix Cache key prefix to clear (empty for all)
 * @return int Number of cache files cleared
 */
function cache_clear($prefix = '') {
    $count = 0;
    $files = glob(CACHE_DIR . '/*.cache');
    
    foreach ($files as $file) {
        if (empty($prefix) || strpos(basename($file), md5($prefix)) === 0) {
            if (unlink($file)) {
                $count++;
            }
        }
    }
    
    return $count;
}

/**
 * Get cache statistics
 * 
 * @return array Cache statistics
 */
function cache_stats() {
    $files = glob(CACHE_DIR . '/*.cache');
    $count = count($files);
    $size = 0;
    $expired = 0;
    
    foreach ($files as $file) {
        $size += filesize($file);
        
        // Check if cache has expired
        $cache_data = file_get_contents($file);
        $data = unserialize($cache_data);
        
        if ($data['expires'] > 0 && $data['expires'] < time()) {
            $expired++;
        }
    }
    
    return [
        'count' => $count,
        'size' => $size,
        'size_formatted' => format_size($size),
        'expired' => $expired
    ];
}

/**
 * Format size in bytes to human readable format
 * 
 * @param int $size Size in bytes
 * @return string Formatted size
 */
function format_size($size) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    
    while ($size >= 1024 && $i < count($units) - 1) {
        $size /= 1024;
        $i++;
    }
    
    return round($size, 2) . ' ' . $units[$i];
} 