<?php
/**
 * Birthdays Controller
 */

require_once 'config/database.php';
require_once 'models/Member.php';
require_once 'models/Sms.php';
require_once 'models/Setting.php';
require_once 'utils/validation.php';
require_once 'utils/sms_helper.php';
require_once 'helpers/functions.php';

class BirthdaysController {
    private $database;
    private $member;
    private $setting;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->member = new Member($this->database->getConnection());
        $this->setting = new Setting($this->database->getConnection());
    }

    /**
     * Display birthday SMS page
     *
     * @return void
     */
    public function sms() {
        // Set page title and active page
        $page_title = getPageTitle('Birthday SMS');
        $active_page = 'birthdays';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/birthdays/sms.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Send birthday SMS
     *
     * @return void
     */
    public function sendSms() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate message
            if (empty($_POST['message'])) {
                set_flash_message('Message is required', 'danger');
                redirect('birthdays/sms?' . $_SERVER['QUERY_STRING']);
                exit;
            }

            // Validate recipients
            if (empty($_POST['recipients']) || !is_array($_POST['recipients'])) {
                set_flash_message('No recipients selected', 'danger');
                redirect('birthdays/sms?' . $_SERVER['QUERY_STRING']);
                exit;
            }

            // Get SMS settings
            $setting = new Setting($this->database->getConnection());
            $settings = $setting->getAllAsArray();
            $api_key = $settings['sms_api_key'] ?? '';
            $sender_id = $settings['sms_sender_id'] ?? 'ICGC';

            if (empty($api_key)) {
                set_flash_message('SMS API key not configured. Please go to Settings and configure the SMS API key.', 'danger');
                redirect('settings');
                exit;
            }

            // Process recipients and send SMS
            $message_template = sanitize($_POST['message']);
            $recipients = $_POST['recipients'];
            $recipient_ids = $_POST['recipient_ids'] ?? [];

            $sent_count = 0;
            $failed_count = 0;
            $sent_recipients = [];

            foreach ($recipients as $index => $phone_number) {
                if (empty($phone_number)) continue;

                // Get member data for personalization
                $member_id = $recipient_ids[$index] ?? null;
                $first_name = 'Member';

                if ($member_id && $this->member->getById($member_id)) {
                    $first_name = $this->member->first_name;
                }

                // Personalize message
                $personalized_message = str_replace('{first_name}', $first_name, $message_template);

                // Send SMS
                $sms_result = send_sms($phone_number, $personalized_message, $sender_id, $api_key);

                if ($sms_result['status']) {
                    $sent_count++;
                    $sent_recipients[] = $phone_number;
                } else {
                    $failed_count++;
                }
            }

            // Save to SMS history
            if ($sent_count > 0) {
                $sms = new Sms($this->database->getConnection());
                $sms->message = $message_template;
                $sms->recipients = implode(',', $sent_recipients);
                $sms->sent_by = $_SESSION['user_id'];
                $sms->sent_date = date('Y-m-d H:i:s');
                $sms->status = $failed_count > 0 ? 'partial' : 'sent';
                $sms->created_at = date('Y-m-d H:i:s');
                $sms->updated_at = date('Y-m-d H:i:s');
                $sms->create();
            }

            // Set success/error messages
            if ($sent_count > 0) {
                $message = "Birthday wishes sent successfully to {$sent_count} recipient(s)";
                if ($failed_count > 0) {
                    $message .= ". {$failed_count} message(s) failed to send.";
                }
                set_flash_message($message, 'success');
            } else {
                set_flash_message('Failed to send birthday wishes. Please try again.', 'danger');
            }

            // Redirect back to birthdays page
            redirect('birthdays');
        } else {
            // Invalid request method
            redirect('birthdays');
        }
    }

    /**
     * Display all birthdays for the current month
     *
     * @return void
     */
    public function index() {
        // Get view type from query parameter
        $view_type = $_GET['view'] ?? 'grid';

        // Get birthday data with proper error handling
        $birthdays_this_month = $this->getBirthdayData('month');
        $birthdays_today = $this->getBirthdayData('today');
        $upcoming_birthdays = $this->getBirthdayData('week');

        // Process birthdays for calendar view
        $calendar_data = $this->processCalendarData($birthdays_this_month);

        // Process birthdays for timeline view
        $timeline_data = $this->processTimelineData($birthdays_this_month);

        // Calculate statistics
        $birthday_stats = $this->calculateBirthdayStats($birthdays_this_month);

        // Get current month name
        $current_month = date('F');
        $current_year = date('Y');

        // Set page title and active page
        $page_title = getPageTitle('Birthdays in ' . $current_month);
        $active_page = 'birthdays';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/birthdays/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Process birthdays data for calendar view
     */
    private function processCalendarData($birthdays) {
        $calendar_data = [];
        $current_month = date('n');
        $current_year = date('Y');

        foreach ($birthdays as $birthday) {
            $day = date('j', strtotime($birthday['date_of_birth']));
            if (!isset($calendar_data[$day])) {
                $calendar_data[$day] = [];
            }
            $calendar_data[$day][] = $birthday;
        }

        return $calendar_data;
    }

    /**
     * Process birthdays data for timeline view
     */
    private function processTimelineData($birthdays) {
        $timeline_data = [];

        foreach ($birthdays as $birthday) {
            $date_key = date('Y-m-d', strtotime(date('Y') . '-' . date('m', strtotime($birthday['date_of_birth'])) . '-' . date('d', strtotime($birthday['date_of_birth']))));
            if (!isset($timeline_data[$date_key])) {
                $timeline_data[$date_key] = [];
            }
            $timeline_data[$date_key][] = $birthday;
        }

        ksort($timeline_data);
        return $timeline_data;
    }

    /**
     * Calculate birthday statistics
     */
    private function calculateBirthdayStats($birthdays) {
        $stats = [
            'total' => count($birthdays),
            'today' => 0,
            'this_week' => 0,
            'age_groups' => [
                'under_18' => 0,
                '18_30' => 0,
                '31_50' => 0,
                '51_70' => 0,
                'over_70' => 0
            ],
            'departments' => []
        ];

        $today = date('m-d');
        $week_start = date('m-d');
        $week_end = date('m-d', strtotime('+7 days'));

        foreach ($birthdays as $birthday) {
            $birthday_date = date('m-d', strtotime($birthday['date_of_birth']));
            $age = $birthday['age'] ?? 0;

            // Count today's birthdays
            if ($birthday_date === $today) {
                $stats['today']++;
            }

            // Count this week's birthdays
            if ($birthday_date >= $week_start && $birthday_date <= $week_end) {
                $stats['this_week']++;
            }

            // Age groups
            if ($age < 18) {
                $stats['age_groups']['under_18']++;
            } elseif ($age <= 30) {
                $stats['age_groups']['18_30']++;
            } elseif ($age <= 50) {
                $stats['age_groups']['31_50']++;
            } elseif ($age <= 70) {
                $stats['age_groups']['51_70']++;
            } else {
                $stats['age_groups']['over_70']++;
            }

            // Department stats
            $dept = $birthday['department'] ?? 'none';
            if (!isset($stats['departments'][$dept])) {
                $stats['departments'][$dept] = 0;
            }
            $stats['departments'][$dept]++;
        }

        return $stats;
    }

    /**
     * Get birthday data with error handling
     *
     * @param string $type Type of birthday data (month, today, week)
     * @return array Birthday data or empty array on error
     */
    private function getBirthdayData($type) {
        try {
            switch ($type) {
                case 'month':
                    return $this->member->getBirthdaysThisMonth();
                case 'today':
                    return $this->member->getBirthdaysToday();
                case 'week':
                    return $this->member->getBirthdaysThisWeek();
                default:
                    throw new InvalidArgumentException("Invalid birthday data type: {$type}");
            }
        } catch (Exception $e) {
            error_log("Error fetching birthday data ({$type}): " . $e->getMessage());
            return [];
        }
    }
}
