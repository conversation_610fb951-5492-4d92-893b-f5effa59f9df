<!-- Meeting Attendance View -->
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <a href="<?php echo url('groups/members/' . $meeting->group_id); ?>" class="text-green-600 hover:text-green-800">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <h1 class="text-3xl font-bold text-gray-800">Meeting Attendance</h1>
                    </div>
                    <p class="text-gray-600"><?= htmlspecialchars($meeting->group_name) ?> - <?= htmlspecialchars($meeting->meeting_topic ?: 'Group Meeting') ?></p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">
                        <i class="fas fa-calendar mr-2"></i><?= date('M j, Y', strtotime($meeting->meeting_date)) ?>
                    </p>
                    <p class="text-sm text-gray-600">
                        <i class="fas fa-clock mr-2"></i><?= date('g:i A', strtotime($meeting->meeting_time)) ?>
                    </p>
                    <?php if ($meeting->meeting_location): ?>
                        <p class="text-sm text-gray-600">
                            <i class="fas fa-map-marker-alt mr-2"></i><?= htmlspecialchars($meeting->meeting_location) ?>
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Meeting Details -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Meeting Details</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-700 mb-2">Meeting Information</h3>
                    <div class="space-y-2 text-sm">
                        <p><span class="font-medium">Topic:</span> <?= htmlspecialchars($meeting->meeting_topic ?: 'General Meeting') ?></p>
                        <p><span class="font-medium">Created by:</span> <?= htmlspecialchars($meeting->first_name . ' ' . $meeting->last_name) ?></p>
                        <p><span class="font-medium">Status:</span> 
                            <span class="px-2 py-1 text-xs font-medium rounded-full 
                                <?php 
                                switch($meeting->status) {
                                    case 'completed': echo 'bg-green-100 text-green-800'; break;
                                    case 'scheduled': echo 'bg-blue-100 text-blue-800'; break;
                                    case 'cancelled': echo 'bg-red-100 text-red-800'; break;
                                    default: echo 'bg-gray-100 text-gray-800'; break;
                                }
                                ?>">
                                <?= ucfirst($meeting->status) ?>
                            </span>
                        </p>
                    </div>
                </div>
                <?php if ($meeting->meeting_notes): ?>
                    <div>
                        <h3 class="font-medium text-gray-700 mb-2">Meeting Notes</h3>
                        <p class="text-sm text-gray-600"><?= nl2br(htmlspecialchars($meeting->meeting_notes)) ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Attendance Statistics -->
        <?php if (!empty($attendance)): ?>
            <?php
            $totalMembers = count($attendance);
            $presentCount = 0;
            $absentCount = 0;
            $lateCount = 0;
            $excusedCount = 0;
            
            foreach ($attendance as $record) {
                switch ($record->attendance_status) {
                    case 'present': $presentCount++; break;
                    case 'absent': $absentCount++; break;
                    case 'late': $lateCount++; break;
                    case 'excused': $excusedCount++; break;
                }
            }
            
            $attendanceRate = $totalMembers > 0 ? round(($presentCount + $lateCount) / $totalMembers * 100, 1) : 0;
            ?>
            
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-blue-600"><?= $totalMembers ?></div>
                    <div class="text-sm text-blue-800">Total Members</div>
                </div>
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-green-600"><?= $presentCount ?></div>
                    <div class="text-sm text-green-800">Present</div>
                </div>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-yellow-600"><?= $lateCount ?></div>
                    <div class="text-sm text-yellow-800">Late</div>
                </div>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-red-600"><?= $absentCount ?></div>
                    <div class="text-sm text-red-800">Absent</div>
                </div>
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-purple-600"><?= $attendanceRate ?>%</div>
                    <div class="text-sm text-purple-800">Attendance Rate</div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Attendance Records -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-6">Attendance Records</h2>
            
            <?php if (!empty($attendance)): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="py-3 px-4 border-b text-left">Member</th>
                                <th class="py-3 px-4 border-b text-left">Status</th>
                                <th class="py-3 px-4 border-b text-left">Check-in Time</th>
                                <th class="py-3 px-4 border-b text-left">Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($attendance as $record): ?>
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="py-3 px-4">
                                        <div class="flex items-center">
                                            <?php if (!empty($record->profile_picture)): ?>
                                                <img src="<?php echo BASE_URL; ?>uploads/members/<?= $record->profile_picture ?>" alt="<?= $record->first_name ?>" class="w-10 h-10 rounded-full mr-3">
                                            <?php else: ?>
                                                <div class="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                                                    <span class="text-gray-600 font-semibold"><?= substr($record->first_name, 0, 1) ?></span>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <p class="font-medium"><?= htmlspecialchars($record->first_name . ' ' . $record->last_name) ?></p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full 
                                            <?php 
                                            switch($record->attendance_status) {
                                                case 'present': echo 'bg-green-100 text-green-800'; break;
                                                case 'absent': echo 'bg-red-100 text-red-800'; break;
                                                case 'late': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'excused': echo 'bg-blue-100 text-blue-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800'; break;
                                            }
                                            ?>">
                                            <?= ucfirst($record->attendance_status) ?>
                                        </span>
                                    </td>
                                    <td class="py-3 px-4 text-sm text-gray-600">
                                        <?= $record->check_in_time ? date('g:i A', strtotime($record->check_in_time)) : '-' ?>
                                    </td>
                                    <td class="py-3 px-4 text-sm text-gray-600">
                                        <?= $record->notes ? htmlspecialchars($record->notes) : '-' ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-clipboard-list text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Attendance Records</h3>
                    <p class="text-gray-600">No attendance has been recorded for this meeting yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
