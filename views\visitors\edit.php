<div class="container mx-auto max-w-4xl">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-1">Edit Visitor</h1>
            <p class="text-sm text-gray-600">Update visitor information</p>
        </div>
        <div>
            <a href="<?php echo url('visitors/' . $this->visitor->id); ?>" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Visitor
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['form_errors'])): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
            <div class="font-bold">Please fix the following errors:</div>
            <ul class="list-disc ml-5">
                <?php foreach ($_SESSION['form_errors'] as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['form_errors']); ?>
    <?php endif; ?>

    <div class="bg-white rounded-lg shadow-md p-6">
        <form action="<?php echo url('visitors/' . $visitor->id); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="_method" value="PUT">
            <input type="hidden" name="id" value="<?php echo $this->visitor->id; ?>">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Personal Information -->
                <div class="md:col-span-2">
                    <h2 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b">Personal Information</h2>
                </div>
                
                <!-- First Name -->
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name <span class="text-red-500">*</span></label>
                    <input type="text" name="first_name" id="first_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required value="<?php echo isset($_SESSION['form_data']['first_name']) ? $_SESSION['form_data']['first_name'] : $this->visitor->first_name; ?>">
                </div>
                
                <!-- Last Name -->
                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name <span class="text-red-500">*</span></label>
                    <input type="text" name="last_name" id="last_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required value="<?php echo isset($_SESSION['form_data']['last_name']) ? $_SESSION['form_data']['last_name'] : $this->visitor->last_name; ?>">
                </div>
                
                <!-- Phone Number -->
                <div>
                    <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-1">Phone Number <span class="text-red-500">*</span></label>
                    <input type="tel" name="phone_number" id="phone_number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required value="<?php echo isset($_SESSION['form_data']['phone_number']) ? $_SESSION['form_data']['phone_number'] : $this->visitor->phone_number; ?>">
                </div>
                
                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input type="email" name="email" id="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" value="<?php echo isset($_SESSION['form_data']['email']) ? $_SESSION['form_data']['email'] : $this->visitor->email; ?>">
                </div>
                
                <!-- Address -->
                <div class="md:col-span-2">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                    <input type="text" name="address" id="address" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" value="<?php echo isset($_SESSION['form_data']['address']) ? $_SESSION['form_data']['address'] : $this->visitor->address; ?>">
                </div>
                
                <!-- Visit Information -->
                <div class="md:col-span-2">
                    <h2 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b mt-4">Visit Information</h2>
                </div>
                
                <!-- First Visit Date -->
                <div>
                    <label for="first_visit_date" class="block text-sm font-medium text-gray-700 mb-1">First Visit Date <span class="text-red-500">*</span></label>
                    <input type="date" name="first_visit_date" id="first_visit_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required value="<?php echo isset($_SESSION['form_data']['first_visit_date']) ? $_SESSION['form_data']['first_visit_date'] : $this->visitor->first_visit_date; ?>">
                </div>
                
                <!-- How Did They Hear -->
                <div>
                    <label for="how_did_they_hear" class="block text-sm font-medium text-gray-700 mb-1">How Did They Hear About Us <span class="text-red-500">*</span></label>
                    <select name="how_did_they_hear" id="how_did_they_hear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required>
                        <option value="">-- Select Option --</option>
                        <option value="friend" <?php echo (isset($_SESSION['form_data']['how_did_they_hear']) ? $_SESSION['form_data']['how_did_they_hear'] === 'friend' : $this->visitor->how_did_they_hear === 'friend') ? 'selected' : ''; ?>>Friend</option>
                        <option value="family" <?php echo (isset($_SESSION['form_data']['how_did_they_hear']) ? $_SESSION['form_data']['how_did_they_hear'] === 'family' : $this->visitor->how_did_they_hear === 'family') ? 'selected' : ''; ?>>Family</option>
                        <option value="social_media" <?php echo (isset($_SESSION['form_data']['how_did_they_hear']) ? $_SESSION['form_data']['how_did_they_hear'] === 'social_media' : $this->visitor->how_did_they_hear === 'social_media') ? 'selected' : ''; ?>>Social Media</option>
                        <option value="website" <?php echo (isset($_SESSION['form_data']['how_did_they_hear']) ? $_SESSION['form_data']['how_did_they_hear'] === 'website' : $this->visitor->how_did_they_hear === 'website') ? 'selected' : ''; ?>>Website</option>
                        <option value="event" <?php echo (isset($_SESSION['form_data']['how_did_they_hear']) ? $_SESSION['form_data']['how_did_they_hear'] === 'event' : $this->visitor->how_did_they_hear === 'event') ? 'selected' : ''; ?>>Event</option>
                        <option value="other" <?php echo (isset($_SESSION['form_data']['how_did_they_hear']) ? $_SESSION['form_data']['how_did_they_hear'] === 'other' : $this->visitor->how_did_they_hear === 'other') ? 'selected' : ''; ?>>Other</option>
                    </select>
                </div>
                
                <!-- Visitor Status -->
                <div>
                    <label for="visitor_status" class="block text-sm font-medium text-gray-700 mb-1">Visitor Status <span class="text-red-500">*</span></label>
                    <select name="visitor_status" id="visitor_status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required>
                        <option value="new" <?php echo (isset($_SESSION['form_data']['visitor_status']) ? $_SESSION['form_data']['visitor_status'] === 'new' : $this->visitor->visitor_status === 'new') ? 'selected' : ''; ?>>New</option>
                        <option value="in_follow_up" <?php echo (isset($_SESSION['form_data']['visitor_status']) ? $_SESSION['form_data']['visitor_status'] === 'in_follow_up' : $this->visitor->visitor_status === 'in_follow_up') ? 'selected' : ''; ?>>In Follow-up</option>
                        <option value="converted" <?php echo (isset($_SESSION['form_data']['visitor_status']) ? $_SESSION['form_data']['visitor_status'] === 'converted' : $this->visitor->visitor_status === 'converted') ? 'selected' : ''; ?>>Converted</option>
                        <option value="inactive" <?php echo (isset($_SESSION['form_data']['visitor_status']) ? $_SESSION['form_data']['visitor_status'] === 'inactive' : $this->visitor->visitor_status === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                    </select>
                </div>
                
                <!-- Notes -->
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea name="notes" id="notes" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"><?php echo isset($_SESSION['form_data']['notes']) ? $_SESSION['form_data']['notes'] : $this->visitor->notes; ?></textarea>
                </div>
            </div>
            
            <div class="mt-6 flex justify-end">
                <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-6 rounded-md">
                    Update Visitor
                </button>
            </div>
        </form>
    </div>
</div>

<?php
// Clear form data after displaying
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>
