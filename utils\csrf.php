<?php
/**
 * Centralized CSRF Protection
 * Consolidates all CSRF functionality into a single, consistent implementation
 */

class CSRFProtection {
    private static $tokenName = 'csrf_token';
    
    /**
     * Generate CSRF token
     * 
     * @return string CSRF token
     */
    public static function generateToken(): string {
        if (!isset($_SESSION[self::$tokenName])) {
            $_SESSION[self::$tokenName] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION[self::$tokenName];
    }
    
    /**
     * Validate CSRF token
     * 
     * @param string $token Token to validate
     * @return bool Whether token is valid
     */
    public static function validateToken(string $token): bool {
        if (!isset($_SESSION[self::$tokenName]) || empty($token)) {
            return false;
        }
        
        return hash_equals($_SESSION[self::$tokenName], $token);
    }
    
    /**
     * Get CSRF token field HTML
     * 
     * @return string HTML input field
     */
    public static function getTokenField(): string {
        $token = self::generateToken();
        return '<input type="hidden" name="' . self::$tokenName . '" value="' . htmlspecialchars($token) . '">';
    }
    
    /**
     * Verify CSRF token from POST data
     * 
     * @throws SecurityException If token is invalid
     * @return void
     */
    public static function verifyPostToken(): void {
        $token = $_POST[self::$tokenName] ?? '';
        
        if (!self::validateToken($token)) {
            throw new SecurityException('Invalid CSRF token', ['action' => 'csrf_validation_failed']);
        }
    }
    
    /**
     * Regenerate CSRF token
     * 
     * @return string New CSRF token
     */
    public static function regenerateToken(): string {
        unset($_SESSION[self::$tokenName]);
        return self::generateToken();
    }
}

// Backward compatibility functions are defined in utils/functions.php
?>
