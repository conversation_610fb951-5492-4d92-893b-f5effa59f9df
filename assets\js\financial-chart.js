/**
 * Financial Chart Dropdown Functionality
 * Standalone script for financial overview chart
 */



// Wait for everything to be ready
function initFinancialChart() {
    console.log('🚀 Initializing financial chart...');
    
    // Check if Chart.js is available
    if (typeof Chart === 'undefined') {
        console.log('⏳ Chart.js not available, retrying...');
        setTimeout(initFinancialChart, 500);
        return;
    }
    
    // Get elements
    const canvas = document.getElementById('financial-overview-chart');
    const dropdown = document.getElementById('financial-period');
    
    console.log('Canvas found:', !!canvas);
    console.log('Dropdown found:', !!dropdown);
    
    if (!canvas || !dropdown) {
        console.log('⏳ Elements not ready, retrying...');
        setTimeout(initFinancialChart, 500);
        return;
    }
    
    // Check if canvas already has a chart and destroy it
    try {
        const existingChart = Chart.getChart(canvas);
        if (existingChart) {
            existingChart.destroy();
        }
    } catch (error) {
        // Silent error handling
    }

    let chart = null;

    function updateChart(period) {

        // Show loading
        canvas.style.opacity = '0.5';
        canvas.style.transition = 'opacity 0.3s ease';
        
        fetch(`api/dashboard_data.php?action=financial_overview&period=${period}`)
            .then(response => response.json())
            .then(data => {
                console.log('📈 Data received:', data);
                
                if (data.success) {
                    // Destroy existing chart
                    if (chart) {
                        chart.destroy();
                    }
                    
                    // Create new chart
                    chart = new Chart(canvas, {
                        type: 'bar',
                        data: {
                            labels: data.labels,
                            datasets: [{
                                label: 'Income',
                                data: data.income,
                                backgroundColor: '#10b981',
                                borderColor: '#059669',
                                borderWidth: 1,
                                borderRadius: 6,
                            }, {
                                label: 'Expenses',
                                data: data.expenses,
                                backgroundColor: '#ef4444',
                                borderColor: '#dc2626',
                                borderWidth: 1,
                                borderRadius: 6,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'top',
                                    labels: {
                                        usePointStyle: true,
                                        padding: 15,
                                        font: { size: 12 }
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    titleColor: '#ffffff',
                                    bodyColor: '#ffffff',
                                    callbacks: {
                                        label: function(context) {
                                            return context.dataset.label + ': GH₵' + context.parsed.y.toLocaleString();
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return 'GH₵' + value.toLocaleString();
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 800
                            }
                        }
                    });
                    
                    console.log('✅ Chart created successfully');
                } else {
                    console.error('❌ API Error:', data.error);
                }
                
                // Hide loading
                canvas.style.opacity = '1';
            })
            .catch(error => {
                console.error('❌ Network Error:', error);
                canvas.style.opacity = '1';
            });
    }
    
    // Load initial data
    updateChart('6months');
    
    // Add event listener to dropdown
    dropdown.addEventListener('change', function() {
        console.log('🔄 Dropdown changed to:', this.value);
        console.log('🔄 Available options:', Array.from(this.options).map(opt => opt.value));
        updateChart(this.value);
    });
    
    console.log('✅ Financial chart setup complete');
}

// Initialize chart when DOM is ready - optimized for speed
document.addEventListener('DOMContentLoaded', function() {
    // Check if Chart.js is loaded, if not wait briefly
    if (typeof Chart !== 'undefined') {
        initFinancialChart();
    } else {
        setTimeout(initFinancialChart, 200);
    }
});
