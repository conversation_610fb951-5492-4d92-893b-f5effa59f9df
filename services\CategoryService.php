<?php
/**
 * Category Service
 * 
 * Handles all business logic related to finance categories.
 * Provides a clean interface between controllers and models.
 * 
 * @package Services
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

require_once 'services/ValidationService.php';
require_once 'services/AuditService.php';
require_once 'services/SecurityService.php';
require_once 'models/CustomFinanceCategory.php';
require_once 'exceptions/CategoryException.php';

class CategoryService
{
    /**
     * @var CustomFinanceCategory Category model instance
     */
    private $categoryModel;
    
    /**
     * @var ValidationService Validation service instance
     */
    private $validationService;
    
    /**
     * @var AuditService Audit service instance
     */
    private $auditService;
    
    /**
     * @var SecurityService Security service instance
     */
    private $securityService;
    
    /**
     * @var PDO Database connection
     */
    private $db;

    /**
     * CategoryService constructor
     * 
     * @param PDO $db Database connection
     */
    public function __construct($db)
    {
        $this->db = $db;
        $this->categoryModel = new CustomFinanceCategory($db);
        $this->validationService = new ValidationService();
        $this->auditService = new AuditService($db);
        $this->securityService = new SecurityService();
    }

    /**
     * Create a new finance category
     * 
     * @param array $data Category data
     * @return int Created category ID
     * @throws CategoryException If creation fails
     */
    public function createCategory(array $data): int
    {
        try {
            // Validate input data
            $this->validationService->validateCategoryData($data);
            
            // Check for duplicate names
            if ($this->categoryExists($data['name'], $data['category_type'])) {
                throw new CategoryException("Category with name '{$data['name']}' already exists in {$data['category_type']}");
            }
            
            // Sanitize input data
            $sanitizedData = $this->securityService->sanitizeCategoryData($data);
            
            // Generate slug if not provided
            if (empty($sanitizedData['slug'])) {
                $sanitizedData['slug'] = $this->generateUniqueSlug($sanitizedData['name']);
            }
            
            // Set default values
            $sanitizedData['is_active'] = $sanitizedData['is_active'] ?? true;
            $sanitizedData['is_core'] = false; // New categories are never core
            $sanitizedData['created_at'] = date('Y-m-d H:i:s');
            $sanitizedData['updated_at'] = date('Y-m-d H:i:s');
            
            // Generate dashboard route
            $sanitizedData['dashboard_route'] = $this->generateDashboardRoute($sanitizedData);
            
            // Create category
            $categoryId = $this->categoryModel->create($sanitizedData);
            
            if (!$categoryId) {
                throw new CategoryException('Failed to create category');
            }
            
            // Log the creation
            $this->auditService->logCategoryCreation($categoryId, $sanitizedData);
            
            return $categoryId;
            
        } catch (ValidationException $e) {
            throw new CategoryException('Validation failed: ' . $e->getMessage(), 0, $e);
        } catch (Exception $e) {
            error_log("CategoryService::createCategory failed: " . $e->getMessage());
            throw new CategoryException('Failed to create category: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Update an existing category
     * 
     * @param int $id Category ID
     * @param array $data Updated category data
     * @return bool Success status
     * @throws CategoryException If update fails
     */
    public function updateCategory(int $id, array $data): bool
    {
        try {
            // Get existing category
            $existingCategory = $this->categoryModel->getById($id);
            if (!$existingCategory) {
                throw new CategoryException("Category with ID {$id} not found");
            }
            
            // Validate input data
            $this->validationService->validateCategoryData($data, $id);
            
            // Check for duplicate names (excluding current category)
            if (isset($data['name']) && $this->categoryExists($data['name'], $data['category_type'] ?? $existingCategory->category_type, $id)) {
                throw new CategoryException("Category with name '{$data['name']}' already exists");
            }
            
            // Sanitize input data
            $sanitizedData = $this->securityService->sanitizeCategoryData($data);
            
            // Update slug if name changed
            if (isset($sanitizedData['name']) && $sanitizedData['name'] !== $existingCategory->name) {
                $sanitizedData['slug'] = $this->generateUniqueSlug($sanitizedData['name']);
            }
            
            // Update dashboard route if necessary
            if (isset($sanitizedData['name']) || isset($sanitizedData['category_type'])) {
                $updatedData = array_merge((array)$existingCategory, $sanitizedData);
                $sanitizedData['dashboard_route'] = $this->generateDashboardRoute($updatedData);
            }
            
            // Set updated timestamp
            $sanitizedData['updated_at'] = date('Y-m-d H:i:s');
            
            // Update category
            $success = $this->categoryModel->update($id, $sanitizedData);
            
            if (!$success) {
                throw new CategoryException('Failed to update category');
            }
            
            // Log the update
            $this->auditService->logCategoryUpdate($id, $existingCategory, $sanitizedData);
            
            return true;
            
        } catch (ValidationException $e) {
            throw new CategoryException('Validation failed: ' . $e->getMessage(), 0, $e);
        } catch (Exception $e) {
            error_log("CategoryService::updateCategory failed: " . $e->getMessage());
            throw new CategoryException('Failed to update category: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Delete a category
     * 
     * @param int $id Category ID
     * @return bool Success status
     * @throws CategoryException If deletion fails
     */
    public function deleteCategory(int $id): bool
    {
        try {
            // Get existing category
            $category = $this->categoryModel->getById($id);
            if (!$category) {
                throw new CategoryException("Category with ID {$id} not found");
            }
            
            // Check if category is core (core categories cannot be deleted)
            if ($category->is_core) {
                throw new CategoryException('Core categories cannot be deleted');
            }
            
            // Check if category has transactions
            if ($this->categoryHasTransactions($id)) {
                throw new CategoryException('Cannot delete category with existing transactions. Deactivate instead.');
            }
            
            // Delete category
            $success = $this->categoryModel->delete($id);
            
            if (!$success) {
                throw new CategoryException('Failed to delete category');
            }
            
            // Log the deletion
            $this->auditService->logCategoryDeletion($id, $category);
            
            return true;
            
        } catch (Exception $e) {
            error_log("CategoryService::deleteCategory failed: " . $e->getMessage());
            throw new CategoryException('Failed to delete category: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Toggle category status (active/inactive)
     * 
     * @param int $id Category ID
     * @return bool New status
     * @throws CategoryException If toggle fails
     */
    public function toggleCategoryStatus(int $id): bool
    {
        try {
            $category = $this->categoryModel->getById($id);
            if (!$category) {
                throw new CategoryException("Category with ID {$id} not found");
            }
            
            $newStatus = !$category->is_active;
            
            $success = $this->categoryModel->update($id, [
                'is_active' => $newStatus,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            if (!$success) {
                throw new CategoryException('Failed to toggle category status');
            }
            
            // Log the status change
            $this->auditService->logCategoryStatusChange($id, $category->is_active, $newStatus);
            
            return $newStatus;
            
        } catch (Exception $e) {
            error_log("CategoryService::toggleCategoryStatus failed: " . $e->getMessage());
            throw new CategoryException('Failed to toggle category status: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Get categories by type
     * 
     * @param string $type Category type
     * @param bool $activeOnly Whether to return only active categories
     * @return array Categories
     */
    public function getCategoriesByType(string $type, bool $activeOnly = true): array
    {
        try {
            return $this->categoryModel->getByType($type, $activeOnly);
        } catch (Exception $e) {
            error_log("CategoryService::getCategoriesByType failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get category by ID
     * 
     * @param int $id Category ID
     * @return object|null Category object or null if not found
     */
    public function getCategoryById(int $id): ?object
    {
        try {
            return $this->categoryModel->getById($id);
        } catch (Exception $e) {
            error_log("CategoryService::getCategoryById failed: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if category exists
     * 
     * @param string $name Category name
     * @param string $type Category type
     * @param int|null $excludeId ID to exclude from check
     * @return bool Whether category exists
     */
    private function categoryExists(string $name, string $type, ?int $excludeId = null): bool
    {
        try {
            $query = "SELECT COUNT(*) FROM custom_finance_categories 
                     WHERE name = ? AND category_type = ?";
            $params = [$name, $type];
            
            if ($excludeId) {
                $query .= " AND id != ?";
                $params[] = $excludeId;
            }
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            error_log("CategoryService::categoryExists failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate unique slug for category
     * 
     * @param string $name Category name
     * @return string Unique slug
     */
    private function generateUniqueSlug(string $name): string
    {
        $baseSlug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name)));
        $slug = $baseSlug;
        $counter = 1;
        
        while ($this->slugExists($slug)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Check if slug exists
     * 
     * @param string $slug Slug to check
     * @return bool Whether slug exists
     */
    private function slugExists(string $slug): bool
    {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM custom_finance_categories WHERE slug = ?");
            $stmt->execute([$slug]);
            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            error_log("CategoryService::slugExists failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate dashboard route for category
     * 
     * @param array $categoryData Category data
     * @return string Dashboard route
     */
    private function generateDashboardRoute(array $categoryData): string
    {
        // Special cases for core categories
        if ($categoryData['name'] === 'tithe') {
            return 'finance/dashboard/tithe';
        }
        if ($categoryData['name'] === 'pledge') {
            return 'finance/dashboard/pledge';
        }
        
        // Standard route for custom categories
        $type = ($categoryData['category_type'] === 'expenses') ? 'expense' : 'income';
        return "finance/dashboard/category?category=" . urlencode($categoryData['name']) . "&type={$type}";
    }

    /**
     * Check if category has transactions
     * 
     * @param int $categoryId Category ID
     * @return bool Whether category has transactions
     */
    private function categoryHasTransactions(int $categoryId): bool
    {
        try {
            // Get category name first
            $category = $this->categoryModel->getById($categoryId);
            if (!$category) {
                return false;
            }
            
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM finances WHERE category = ?");
            $stmt->execute([$category->name]);
            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            error_log("CategoryService::categoryHasTransactions failed: " . $e->getMessage());
            return false;
        }
    }
}
