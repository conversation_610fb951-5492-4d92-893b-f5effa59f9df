<?php
/**
 * Service Controller
 */

require_once 'models/Service.php';
require_once 'models/Setting.php';
require_once 'utils/validation.php';

class ServiceController {
    private $database;
    private $service;
    private $setting;

    /**
     * Constructor
     */
    public function __construct() {
        try {
            $this->database = new Database();
            $this->service = new Service($this->database->getConnection());
            $this->setting = new Setting($this->database->getConnection());
        } catch (Exception $e) {
            error_log("ServiceController constructor error: " . $e->getMessage());
            $this->setting = null;
        }
    }

    /**
     * Display services list
     *
     * @return void
     */
    public function index() {
        // Get all services
        $stmt = $this->service->getAll();
        $services = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Set page title and active page
        $page_title = getPageTitle('Services');
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/services/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display service creation form
     *
     * @return void
     */
    public function create() {
        // Set page title and active page
        $page_title = getPageTitle('Add Service');
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/services/create.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store new service
     *
     * @return void
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Model will handle all validation

            // Set service properties
            $this->service->name = sanitize($_POST['name']);
            $this->service->description = sanitize($_POST['description'] ?? '');
            $this->service->day_of_week = sanitize($_POST['day_of_week']);
            $this->service->time = sanitize($_POST['time']);
            $this->service->created_at = date('Y-m-d H:i:s');
            $this->service->updated_at = date('Y-m-d H:i:s');

            // Create service (model will handle all validation)
            if ($this->service->create()) {
                // Set success message
                set_flash_message('Service added successfully', 'success');
                redirect('services');
            } else {
                // Get detailed error message from model
                $errorMessage = $this->service->error ?? 'Failed to add service';
                set_flash_message($errorMessage, 'danger');
                $_SESSION['form_data'] = $_POST;
                redirect('services/add');
            }
        } else {
            // Not a POST request, redirect to service form
            redirect('services/add');
        }
    }

    /**
     * Display service edit form
     *
     * @param int|null $id Service ID from URL parameter
     * @return void
     */
    public function edit($id = null) {
        // Get ID from parameter or fallback to legacy $_GET method
        if ($id === null) {
            if (!isset($_GET['id']) || empty($_GET['id'])) {
                set_flash_message('Service ID is required', 'danger');
                redirect('services');
                exit;
            }
            $id = sanitize($_GET['id']);
        } else {
            $id = sanitize($id);
        }

        // Get service
        $found = $this->service->getById($id);
        if (!$found) {
            set_flash_message('Service not found', 'danger');
            redirect('services');
            exit;
        }

        // Make the service object available to the view
        $service = $this->service;

        // Set page title and active page
        $page_title = getPageTitle('Edit Service');
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/services/edit.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Update service (RESTful)
     *
     * @param int $id Service ID from URL parameter
     * @return void
     */
    public function update($id = null) {
        // Get ID from parameter or POST data (for legacy support)
        if ($id === null) {
            $id = $_POST['id'] ?? $_GET['id'] ?? null;
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'PUT') {
            // Check for required ID
            if (empty($id)) {
                set_flash_message('Service ID is required', 'danger');
                redirect('services');
                exit;
            }

            // Model will handle all other validation

            // Get service
            $id = sanitize($_POST['id']);
            if (!$this->service->getById($id)) {
                set_flash_message('Service not found', 'danger');
                redirect('services');
                exit;
            }

            // Set service properties
            $this->service->id = $id;
            $this->service->name = sanitize($_POST['name']);
            $this->service->description = sanitize($_POST['description'] ?? '');
            $this->service->day_of_week = sanitize($_POST['day_of_week']);
            $this->service->time = sanitize($_POST['time']);
            $this->service->updated_at = date('Y-m-d H:i:s');

            // Update service (model will handle all validation)
            if ($this->service->update()) {
                // Set success message
                set_flash_message('Service updated successfully', 'success');
                redirect('services');
            } else {
                // Get detailed error message from model
                $errorMessage = $this->service->error ?? 'Failed to update service';
                set_flash_message($errorMessage, 'danger');
                $_SESSION['form_data'] = $_POST;
                redirect('services/edit/' . $id);
            }
        } else {
            // Not a POST request, redirect to services list
            redirect('services');
        }
    }

    /**
     * Show service details (RESTful)
     *
     * @param int $id Service ID
     * @return void
     */
    public function show($id = null) {
        // Get ID from parameter or query string
        if ($id === null) {
            $id = $_GET['id'] ?? null;
        }

        if (!$id || !is_numeric($id)) {
            set_flash_message('Service ID is required', 'danger');
            redirect('services');
            return;
        }

        // Get service
        $service = $this->service->getById($id);
        if (!$service) {
            set_flash_message('Service not found', 'danger');
            redirect('services');
            return;
        }

        // Set page title and active page
        $page_title = getPageTitle('Service Details - ' . $service->name);
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/services/show.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Delete service (RESTful)
     *
     * @param int $id Service ID from URL parameter
     * @return void
     */
    public function delete($id = null) {
        // Get ID from parameter or POST data (for legacy support)
        if ($id === null) {
            $id = $_POST['id'] ?? $_GET['id'] ?? null;
        }

        if (!$id || !is_numeric($id)) {
            set_flash_message('Service ID is required', 'danger');
            redirect('services');
            return;
        }

        $id = sanitize($id);

        // Get service
        if (!$this->service->getById($id)) {
            set_flash_message('Service not found', 'danger');
            redirect('services');
            exit;
        }

        // Delete service (model will handle data integrity checks)
        if ($this->service->delete($id)) {
            // Set success message
            set_flash_message('Service deleted successfully', 'success');
        } else {
            // Get error message from model
            $errorMessage = $this->service->error ?? 'Failed to delete service';
            set_flash_message($errorMessage, 'danger');
        }

        redirect('services');
    }
}
