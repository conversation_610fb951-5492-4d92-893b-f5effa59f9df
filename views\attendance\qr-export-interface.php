<?php
/**
 * QR Data Export Interface
 * Advanced export options for QR attendance data
 */
?>

<div class="container max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-gradient-to-r from-green-600 to-blue-600 px-6 py-4">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-xl font-semibold text-white">QR Data Export</h1>
                    <p class="text-green-100 text-sm">Export QR attendance data with advanced filtering options</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance/qr" class="text-white bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded-md text-sm flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i> Back to QR Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <form id="exportForm" action="<?php echo BASE_URL; ?>attendance/qr-advanced-export" method="GET">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Export Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Export Type</label>
                    <select name="type" id="type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary" required>
                        <option value="sessions">QR Sessions</option>
                        <option value="attendance">Attendance Records</option>
                        <option value="analytics">Analytics Summary</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Choose what type of data to export</p>
                </div>

                <!-- Format -->
                <div>
                    <label for="format" class="block text-sm font-medium text-gray-700 mb-2">Format</label>
                    <select name="format" id="format" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary" required>
                        <option value="csv">CSV (Excel Compatible)</option>
                        <option value="json">JSON (Raw Data)</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Choose export format</p>
                </div>

                <!-- Date From -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                    <input type="date" name="date_from" id="date_from" 
                           value="<?php echo date('Y-m-d', strtotime('-30 days')); ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary" required>
                    <p class="text-xs text-gray-500 mt-1">Start date for data export</p>
                </div>

                <!-- Date To -->
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                    <input type="date" name="date_to" id="date_to" 
                           value="<?php echo date('Y-m-d'); ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary" required>
                    <p class="text-xs text-gray-500 mt-1">End date for data export</p>
                </div>

                <!-- Service Filter -->
                <div>
                    <label for="service_id" class="block text-sm font-medium text-gray-700 mb-2">Service (Optional)</label>
                    <select name="service_id" id="service_id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">All Services</option>
                        <?php if (isset($services) && !empty($services)): ?>
                            <?php foreach ($services as $service): ?>
                                <option value="<?php echo $service['id']; ?>"><?php echo htmlspecialchars($service['name']); ?></option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Filter by specific service</p>
                </div>

                <!-- Status Filter (for sessions only) -->
                <div id="statusFilter">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status (Optional)</label>
                    <select name="status" id="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="expired">Expired</option>
                        <option value="closed">Closed</option>
                        <option value="archived">Archived</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Filter by session status</p>
                </div>
            </div>

            <!-- Export Preview -->
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Export Preview</h3>
                <div id="exportPreview" class="text-sm text-gray-600">
                    <p><strong>Type:</strong> <span id="previewType">QR Sessions</span></p>
                    <p><strong>Format:</strong> <span id="previewFormat">CSV</span></p>
                    <p><strong>Date Range:</strong> <span id="previewDateRange">Last 30 days</span></p>
                    <p><strong>Filters:</strong> <span id="previewFilters">None</span></p>
                    <p><strong>Estimated Records:</strong> <span id="previewCount">Loading...</span></p>
                </div>
            </div>

            <!-- Quick Export Options -->
            <div class="mt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Export Options</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button type="button" onclick="setQuickExport('last7days')" class="bg-blue-100 hover:bg-blue-200 text-blue-800 px-4 py-3 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-calendar-week mr-2"></i>
                        Last 7 Days
                    </button>
                    <button type="button" onclick="setQuickExport('last30days')" class="bg-green-100 hover:bg-green-200 text-green-800 px-4 py-3 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-calendar-month mr-2"></i>
                        Last 30 Days
                    </button>
                    <button type="button" onclick="setQuickExport('thismonth')" class="bg-purple-100 hover:bg-purple-200 text-purple-800 px-4 py-3 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-calendar mr-2"></i>
                        This Month
                    </button>
                </div>
            </div>

            <!-- Export Button -->
            <div class="mt-8 flex justify-end space-x-4">
                <button type="button" onclick="previewExport()" class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    <i class="fas fa-eye mr-2"></i> Preview
                </button>
                <button type="submit" class="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary">
                    <i class="fas fa-download mr-2"></i> Export Data
                </button>
            </div>
        </form>
    </div>

    <!-- Export History -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Exports</h3>
        <div class="text-sm text-gray-600">
            <p>Export history will be displayed here in future updates.</p>
            <p class="mt-2">For now, all exports are downloaded immediately.</p>
        </div>
    </div>
</div>

<script>
// Update preview when form changes
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('exportForm');
    const inputs = form.querySelectorAll('select, input');
    
    inputs.forEach(input => {
        input.addEventListener('change', updatePreview);
    });
    
    updatePreview(); // Initial preview
    
    // Show/hide status filter based on export type
    document.getElementById('type').addEventListener('change', function() {
        const statusFilter = document.getElementById('statusFilter');
        if (this.value === 'sessions') {
            statusFilter.style.display = 'block';
        } else {
            statusFilter.style.display = 'none';
        }
    });
});

function updatePreview() {
    const type = document.getElementById('type').value;
    const format = document.getElementById('format').value;
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;
    const serviceId = document.getElementById('service_id').value;
    const status = document.getElementById('status').value;
    
    // Update preview text
    document.getElementById('previewType').textContent = type.charAt(0).toUpperCase() + type.slice(1);
    document.getElementById('previewFormat').textContent = format.toUpperCase();
    document.getElementById('previewDateRange').textContent = dateFrom + ' to ' + dateTo;
    
    // Update filters
    let filters = [];
    if (serviceId) filters.push('Service: ' + document.getElementById('service_id').selectedOptions[0].text);
    if (status && type === 'sessions') filters.push('Status: ' + status);
    document.getElementById('previewFilters').textContent = filters.length > 0 ? filters.join(', ') : 'None';
    
    // Estimate record count (placeholder)
    document.getElementById('previewCount').textContent = 'Calculating...';
    setTimeout(() => {
        document.getElementById('previewCount').textContent = 'Estimated: 50-200 records';
    }, 500);
}

function setQuickExport(period) {
    const dateFrom = document.getElementById('date_from');
    const dateTo = document.getElementById('date_to');
    const today = new Date();
    
    switch (period) {
        case 'last7days':
            dateFrom.value = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            dateTo.value = today.toISOString().split('T')[0];
            break;
        case 'last30days':
            dateFrom.value = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            dateTo.value = today.toISOString().split('T')[0];
            break;
        case 'thismonth':
            dateFrom.value = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
            dateTo.value = today.toISOString().split('T')[0];
            break;
    }
    
    updatePreview();
}

function previewExport() {
    alert('Preview functionality will show a sample of the export data. This feature will be implemented in a future update.');
}
</script>
