<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($group->group_name ?? 'Group'); ?> Members - ICGC</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 via-blue-50 to-green-50">

<div class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        
        <!-- Enhanced Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <div class="flex-1">
                    <div class="flex items-center gap-3 mb-3">
                        <a href="<?php echo url('groups'); ?>" class="text-green-600 hover:text-green-800 transition-colors p-2 rounded-lg hover:bg-green-50">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-800 mb-1"><?php echo htmlspecialchars($group->group_name ?? 'Unknown Group'); ?></h1>
                            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                                <span class="flex items-center gap-1 bg-green-50 px-2 py-1 rounded-full">
                                    <i class="fas fa-users text-green-600"></i>
                                    <?php echo $memberCount ?? 0; ?> Members
                                </span>
                                <?php if (isset($meetingSchedule) && $meetingSchedule): ?>
                                    <span class="flex items-center gap-1 bg-blue-50 px-2 py-1 rounded-full">
                                        <i class="fas fa-calendar text-blue-600"></i>
                                        <?php echo ucfirst($meetingSchedule->meeting_day ?? ''); ?>s at <?php echo date('g:i A', strtotime($meetingSchedule->meeting_time ?? '')); ?>
                                    </span>
                                <?php endif; ?>
                                <span class="flex items-center gap-1 bg-purple-50 px-2 py-1 rounded-full">
                                    <i class="fas fa-tag text-purple-600"></i>
                                    <?php echo htmlspecialchars($group->type_name ?? 'Group'); ?>
                                </span>
                                <span class="flex items-center gap-1 bg-gray-50 px-2 py-1 rounded-full">
                                    <i class="fas fa-circle text-green-500 text-xs"></i>
                                    <?php echo ucfirst($group->status ?? 'active'); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php if (!empty($group->group_description)): ?>
                        <div class="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border-l-4 border-green-500 mt-3">
                            <p class="text-gray-700"><?php echo htmlspecialchars($group->group_description); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="flex gap-3">
                    <a href="<?php echo url('groups/add-members/' . ($group->group_id ?? 1)); ?>" class="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center">
                        <i class="fas fa-user-plus mr-2"></i>Add Members
                    </a>
                    <a href="<?php echo url('groups/edit/' . ($group->group_id ?? 1)); ?>" class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center">
                        <i class="fas fa-edit mr-2"></i>Edit Group
                    </a>
                </div>
            </div>
        </div>

        <?php if (function_exists('flash')): ?>
            <?php flash('group_message'); ?>
        <?php endif; ?>

        <!-- Quick Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <!-- Members Card -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Total Members</p>
                        <h3 class="text-2xl font-bold"><?php echo $memberCount ?? 0; ?></h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Meetings Card -->
            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Total Meetings</p>
                        <h3 class="text-2xl font-bold"><?php echo isset($meetings) && is_array($meetings) ? count($meetings) : 0; ?></h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-calendar-check text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Announcements Card -->
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Announcements</p>
                        <h3 class="text-2xl font-bold"><?php echo isset($announcements) && is_array($announcements) ? count($announcements) : 0; ?></h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-bullhorn text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Attendance Card -->
            <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Avg Attendance</p>
                        <h3 class="text-2xl font-bold"><?php echo isset($attendanceStats['average']) ? round($attendanceStats['average']) : 0; ?>%</h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <button onclick="showTab('members')" class="tab-button border-b-2 border-green-500 py-4 px-1 text-sm font-medium text-green-600" data-tab="members">
                        <i class="fas fa-users mr-2"></i>Members
                    </button>
                    <button onclick="showTab('schedule')" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="schedule">
                        <i class="fas fa-calendar mr-2"></i>Schedule
                    </button>
                    <button onclick="showTab('attendance')" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="attendance">
                        <i class="fas fa-chart-bar mr-2"></i>Attendance
                    </button>
                    <button onclick="showTab('announcements')" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="announcements">
                        <i class="fas fa-bullhorn mr-2"></i>Announcements
                    </button>
                </nav>
            </div>

            <!-- Members Tab Content -->
            <div id="members-tab" class="tab-content active p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-gray-800">Group Members</h2>
                    <div class="flex gap-3">
                        <input type="text" placeholder="Search members..." class="border border-gray-300 rounded-lg px-4 py-2 text-sm">
                        <select class="border border-gray-300 rounded-lg px-4 py-2 text-sm">
                            <option value="">All Roles</option>
                            <option value="leader">Leader</option>
                            <option value="member">Member</option>
                        </select>
                    </div>
                </div>

                <?php 
                // Handle both data structures
                $members = [];
                if (isset($membersData) && is_array($membersData) && isset($membersData['members'])) {
                    $members = $membersData['members'];
                } elseif (isset($members) && is_array($members)) {
                    // Already set
                } else {
                    $members = [];
                }
                ?>

                <?php if (!empty($members)): ?>
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <?php foreach ($members as $member): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center gap-3">
                                    <div class="flex-shrink-0">
                                        <?php if (!empty($member->profile_picture)): ?>
                                            <img src="<?php echo url('uploads/members/' . $member->profile_picture); ?>" 
                                                 alt="<?php echo htmlspecialchars($member->first_name ?? ''); ?>" 
                                                 class="h-12 w-12 rounded-full object-cover border-2 border-green-200">
                                        <?php else: ?>
                                            <div class="h-12 w-12 rounded-full bg-green-500 flex items-center justify-center text-white font-bold text-lg border-2 border-green-200">
                                                <?php echo strtoupper(substr($member->first_name ?? 'U', 0, 1)); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-sm font-medium text-gray-900 truncate">
                                            <?php echo htmlspecialchars(($member->first_name ?? '') . ' ' . ($member->last_name ?? '')); ?>
                                        </h3>
                                        <p class="text-sm text-gray-500 truncate">
                                            <?php echo htmlspecialchars($member->email ?? 'No email'); ?>
                                        </p>
                                        <?php if (!empty($member->role_in_group)): ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                                                <?php echo ucfirst($member->role_in_group); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="mt-3 flex justify-end gap-2">
                                    <a href="<?php echo url('members/view/' . ($member->id ?? 1)); ?>" 
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-users text-6xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Members Found</h3>
                        <p class="text-gray-500 mb-6">This group doesn't have any members yet.</p>
                        <a href="<?php echo url('groups/add-members/' . ($group->group_id ?? 1)); ?>" 
                           class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-all duration-200 inline-flex items-center">
                            <i class="fas fa-user-plus mr-2"></i>Add First Member
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Schedule Tab Content -->
            <div id="schedule-tab" class="tab-content p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-6">Meeting Schedule</h2>
                <?php if (isset($meetingSchedule) && $meetingSchedule): ?>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-blue-800 mb-2">Regular Meeting</h3>
                        <p class="text-blue-700">
                            <i class="fas fa-calendar mr-2"></i>
                            Every <?php echo ucfirst($meetingSchedule->meeting_day ?? ''); ?> at <?php echo date('g:i A', strtotime($meetingSchedule->meeting_time ?? '')); ?>
                        </p>
                        <?php if (!empty($meetingSchedule->meeting_location)): ?>
                            <p class="text-blue-700 mt-2">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                <?php echo htmlspecialchars($meetingSchedule->meeting_location); ?>
                            </p>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-calendar text-6xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Schedule Set</h3>
                        <p class="text-gray-500">This group doesn't have a regular meeting schedule yet.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Attendance Tab Content -->
            <div id="attendance-tab" class="tab-content p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-6">Attendance Tracking</h2>
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <i class="fas fa-chart-bar text-6xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Attendance Features</h3>
                    <p class="text-gray-500">Attendance tracking features will be available here.</p>
                </div>
            </div>

            <!-- Announcements Tab Content -->
            <div id="announcements-tab" class="tab-content p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-6">Group Announcements</h2>
                <?php if (isset($announcements) && is_array($announcements) && !empty($announcements)): ?>
                    <div class="space-y-4">
                        <?php foreach ($announcements as $announcement): ?>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <h3 class="text-lg font-medium text-yellow-800"><?php echo htmlspecialchars($announcement->title ?? ''); ?></h3>
                                <p class="text-yellow-700 mt-2"><?php echo htmlspecialchars($announcement->content ?? ''); ?></p>
                                <p class="text-yellow-600 text-sm mt-2">
                                    <i class="fas fa-clock mr-1"></i>
                                    <?php echo date('M j, Y', strtotime($announcement->created_at ?? '')); ?>
                                </p>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-bullhorn text-6xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Announcements</h3>
                        <p class="text-gray-500">There are no announcements for this group yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-green-500', 'text-green-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab content
    const selectedTab = document.getElementById(tabName + '-tab');
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    
    // Activate selected tab button
    const selectedButton = document.querySelector(`[data-tab="${tabName}"]`);
    if (selectedButton) {
        selectedButton.classList.remove('border-transparent', 'text-gray-500');
        selectedButton.classList.add('border-green-500', 'text-green-600');
    }
}
</script>

</body>
</html>
