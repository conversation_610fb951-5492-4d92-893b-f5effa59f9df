<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-primary">API Keys</h1>
        <a href="<?php echo BASE_URL; ?>api-keys/create" class="bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark transition">
            <i class="fas fa-plus mr-2"></i> Create New API Key
        </a>
    </div>

    <?php if (isset($_SESSION['new_api_key'])): ?>
    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Important</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <p>Your new API key has been created. Please copy it now as it will not be shown again:</p>
                    <div class="mt-2 p-2 bg-gray-100 rounded font-mono text-sm break-all select-all">
                        <?php echo htmlspecialchars($_SESSION['new_api_key']); ?>
                    </div>
                    <button class="mt-2 bg-primary text-white px-3 py-1 rounded text-xs" onclick="copyApiKey()">
                        Copy to Clipboard
                    </button>
                </div>
            </div>
        </div>
    </div>
    <script>
        function copyApiKey() {
            const apiKey = '<?php echo $_SESSION['new_api_key']; ?>';
            navigator.clipboard.writeText(apiKey).then(() => {
                alert('API key copied to clipboard!');
            });
        }
    </script>
    <?php 
        // Clear the session variable
        unset($_SESSION['new_api_key']);
    endif; 
    ?>

    <div class="bg-white rounded-lg shadow overflow-hidden">
        <?php if (empty($keys)): ?>
            <div class="p-6 text-center text-gray-500">
                <p>No API keys found. Click the button above to create your first API key.</p>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Description
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Permissions
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Created
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Last Used
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($keys as $key): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($key['description']); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">
                                    <?php 
                                        $permissionsArray = explode(',', $key['permissions']);
                                        foreach ($permissionsArray as $permission) {
                                            echo '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">' . 
                                                htmlspecialchars($permission) . 
                                                '</span>';
                                        }
                                    ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">
                                    <?php echo date('M d, Y', strtotime($key['created_at'])); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">
                                    <?php echo $key['last_used'] ? date('M d, Y H:i', strtotime($key['last_used'])) : 'Never'; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if ($key['status'] === 'active'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Disabled
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <?php if ($key['status'] === 'active'): ?>
                                    <form action="<?php echo BASE_URL; ?>api-keys/disable/<?php echo $key['id']; ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to disable this API key?')">
                                            Disable
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <span class="text-gray-400">Disabled</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
    
    <div class="mt-8 bg-gray-50 p-6 rounded-lg shadow">
        <h2 class="text-xl font-semibold mb-4">Using API Keys</h2>
        <div class="space-y-4">
            <p>To authenticate API requests, include your API key in the request headers:</p>
            <div class="bg-gray-100 p-4 rounded font-mono text-sm overflow-x-auto">
                <code>X-API-Key: your_api_key_here</code>
            </div>
            <p>Example using cURL:</p>
            <div class="bg-gray-100 p-4 rounded font-mono text-sm overflow-x-auto">
                <code>curl -H "X-API-Key: your_api_key_here" <?php echo rtrim(BASE_URL, '/'); ?>/api/attendance.php</code>
            </div>
            <div class="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            Keep your API keys secure and never share them publicly. If a key is compromised, disable it immediately.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 