<?php
/**
 * Shared Member Form Component
 * Used for both add and edit member forms
 * 
 * Variables expected:
 * - $member (object|null): Member data for edit mode, null for add mode
 * - $form_action (string): Form action URL
 * - $form_title (string): Form title
 * - $submit_text (string): Submit button text
 * - $is_edit (boolean): Whether this is edit mode
 */

$is_edit = isset($member) && $member !== null;
$member_data = $is_edit ? $member : (object)[];
?>

<div class="container mx-auto px-4 fade-in">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 flex items-center">
            <span class="bg-primary-light bg-opacity-20 text-primary p-2 rounded-full mr-3">
                <i class="fas fa-<?php echo $is_edit ? 'user-edit' : 'user-plus'; ?>"></i>
            </span>
            <?php echo $form_title; ?>
        </h1>
        <div class="flex space-x-3">
            <?php if ($is_edit): ?>
                <a href="<?php echo BASE_URL; ?>members/view?id=<?php echo $member_data->id; ?>" class="flex items-center px-4 py-2 bg-indigo-100 text-indigo-700 rounded-lg hover:bg-indigo-200 transition-all duration-300 transform hover:scale-105 shadow-sm">
                    <i class="fas fa-eye mr-2"></i> View Member
                </a>
            <?php endif; ?>
            <a href="<?php echo BASE_URL; ?>members" class="flex items-center px-4 py-2 bg-primary bg-opacity-10 text-primary rounded-lg hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105 shadow-sm">
                <i class="fas fa-arrow-left mr-2"></i> Back to Members
            </a>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-lg p-6 max-w-4xl mx-auto border-t-4 border-primary relative overflow-hidden">
        <!-- Decorative elements -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-primary bg-opacity-5 rounded-full -mt-20 -mr-20"></div>
        <div class="absolute bottom-0 left-0 w-40 h-40 bg-primary bg-opacity-5 rounded-full -mb-20 -ml-20"></div>
        
        <div class="mb-6 pb-3 border-b border-gray-200 relative z-10">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-primary-light text-white mr-4 shadow-md">
                    <i class="fas fa-<?php echo $is_edit ? 'user-edit' : 'user-plus'; ?> text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-700"><?php echo $is_edit ? 'Edit Member Information' : 'Add New Member Information'; ?></h3>
                    <p class="text-sm text-gray-500 mt-1"><?php echo $is_edit ? 'Update the member details below' : 'Please fill in the member details below'; ?></p>
                </div>
            </div>
        </div>
        
        <form action="<?php echo $form_action; ?>" method="POST" enctype="multipart/form-data" class="space-y-8 relative z-10">
            <?php if ($is_edit): ?>
                <!-- Hidden ID field for edit mode -->
                <input type="hidden" name="id" value="<?php echo $member_data->id; ?>">
            <?php endif; ?>
            <?php echo csrf_field(); ?>
            
            <!-- Personal Information -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <span class="bg-primary-light bg-opacity-20 text-primary p-2 rounded-lg mr-3">
                        <i class="fas fa-user-circle"></i>
                    </span>
                    Personal Information
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name <span class="text-red-500">*</span></label>
                        <input type="text" id="first_name" name="first_name" value="<?php echo $is_edit ? $member_data->first_name : ''; ?>" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                    </div>
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name <span class="text-red-500">*</span></label>
                        <input type="text" id="last_name" name="last_name" value="<?php echo $is_edit ? $member_data->last_name : ''; ?>" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" id="email" name="email" value="<?php echo $is_edit ? $member_data->email : ''; ?>" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>
                    <div>
                        <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-1">Phone Number <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-phone text-gray-400"></i>
                            </div>
                            <input type="tel" id="phone_number" name="phone_number" value="<?php echo $is_edit ? $member_data->phone_number : ''; ?>" required class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>
                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-alt text-gray-400"></i>
                            </div>
                            <input type="date" id="date_of_birth" name="date_of_birth" value="<?php echo $is_edit ? $member_data->date_of_birth : ''; ?>" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>
                    <div>
                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">Gender <span class="text-red-500">*</span></label>
                        <select id="gender" name="gender" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="">Select Gender</option>
                            <option value="male" <?php echo ($is_edit && $member_data->gender === 'male') ? 'selected' : ''; ?>>Male</option>
                            <option value="female" <?php echo ($is_edit && $member_data->gender === 'female') ? 'selected' : ''; ?>>Female</option>
                        </select>
                    </div>
                    <div>
                        <label for="marital_status" class="block text-sm font-medium text-gray-700 mb-1">Marital Status</label>
                        <select id="marital_status" name="marital_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="">Select Marital Status</option>
                            <option value="single" <?php echo ($is_edit && $member_data->marital_status === 'single') ? 'selected' : ''; ?>>Single</option>
                            <option value="married" <?php echo ($is_edit && $member_data->marital_status === 'married') ? 'selected' : ''; ?>>Married</option>
                            <option value="divorced" <?php echo ($is_edit && $member_data->marital_status === 'divorced') ? 'selected' : ''; ?>>Divorced</option>
                            <option value="widowed" <?php echo ($is_edit && $member_data->marital_status === 'widowed') ? 'selected' : ''; ?>>Widowed</option>
                        </select>
                    </div>
                    <div>
                        <label for="occupation" class="block text-sm font-medium text-gray-700 mb-1">Occupation</label>
                        <input type="text" id="occupation" name="occupation" value="<?php echo $is_edit ? $member_data->occupation : ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                    </div>
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location/Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-map-marker-alt text-gray-400"></i>
                            </div>
                            <input type="text" id="location" name="location" value="<?php echo $is_edit ? $member_data->location : ''; ?>" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>
                </div>
            </div>
