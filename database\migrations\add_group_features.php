<?php
/**
 * Migration script to add group features:
 * - Meeting Schedule
 * - Group Announcements  
 * - Attendance Tracking
 */

require_once __DIR__ . '/../../config/database.php';

echo "<h1>Adding Group Features - Meeting Schedule, Announcements, Attendance</h1>";

try {
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Creating Group Meeting Schedule Table</h2>";
    
    // Group Meeting Schedule Table
    $sql_meeting_schedule = "CREATE TABLE IF NOT EXISTS `group_meeting_schedule` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `group_id` INT NOT NULL,
        `meeting_day` ENUM('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday') NOT NULL,
        `meeting_time` TIME NOT NULL,
        `meeting_location` VARCHAR(255),
        `frequency` ENUM('weekly', 'bi-weekly', 'monthly') DEFAULT 'weekly',
        `is_active` BOOLEAN DEFAULT TRUE,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (`group_id`) REFERENCES `groups`(`group_id`) ON DELETE CASCADE
    )";
    
    $stmt = $conn->prepare($sql_meeting_schedule);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Group meeting schedule table created successfully</p>";
    
    echo "<h2>Creating Group Announcements Table</h2>";
    
    // Group Announcements Table
    $sql_announcements = "CREATE TABLE IF NOT EXISTS `group_announcements` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `group_id` INT NOT NULL,
        `title` VARCHAR(255) NOT NULL,
        `content` TEXT NOT NULL,
        `announcement_type` ENUM('general', 'urgent', 'prayer_request', 'event') DEFAULT 'general',
        `posted_by` INT NOT NULL,
        `is_active` BOOLEAN DEFAULT TRUE,
        `expires_at` DATETIME NULL,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (`group_id`) REFERENCES `groups`(`group_id`) ON DELETE CASCADE,
        FOREIGN KEY (`posted_by`) REFERENCES `members`(`id`) ON DELETE CASCADE
    )";
    
    $stmt = $conn->prepare($sql_announcements);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Group announcements table created successfully</p>";
    
    echo "<h2>Creating Group Meetings Table</h2>";
    
    // Group Meetings Table (for attendance tracking)
    $sql_meetings = "CREATE TABLE IF NOT EXISTS `group_meetings` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `group_id` INT NOT NULL,
        `meeting_date` DATE NOT NULL,
        `meeting_time` TIME NOT NULL,
        `meeting_location` VARCHAR(255),
        `meeting_topic` VARCHAR(255),
        `meeting_notes` TEXT,
        `created_by` INT NOT NULL,
        `status` ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled',
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (`group_id`) REFERENCES `groups`(`group_id`) ON DELETE CASCADE,
        FOREIGN KEY (`created_by`) REFERENCES `members`(`id`) ON DELETE CASCADE
    )";
    
    $stmt = $conn->prepare($sql_meetings);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Group meetings table created successfully</p>";
    
    echo "<h2>Creating Meeting Attendance Table</h2>";
    
    // Meeting Attendance Table
    $sql_attendance = "CREATE TABLE IF NOT EXISTS `meeting_attendance` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `meeting_id` INT NOT NULL,
        `member_id` INT NOT NULL,
        `attendance_status` ENUM('present', 'absent', 'late', 'excused') DEFAULT 'present',
        `check_in_time` TIMESTAMP NULL,
        `notes` TEXT,
        `recorded_by` INT NOT NULL,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (`meeting_id`) REFERENCES `group_meetings`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`member_id`) REFERENCES `members`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`recorded_by`) REFERENCES `members`(`id`) ON DELETE CASCADE,
        UNIQUE KEY `unique_meeting_member` (`meeting_id`, `member_id`)
    )";
    
    $stmt = $conn->prepare($sql_attendance);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Meeting attendance table created successfully</p>";
    
    echo "<h2>Migration Completed Successfully!</h2>";
    echo "<p style='color: blue;'>All tables for group features have been created:</p>";
    echo "<ul>";
    echo "<li>✓ group_meeting_schedule - For managing meeting days and times</li>";
    echo "<li>✓ group_announcements - For group announcements and notices</li>";
    echo "<li>✓ group_meetings - For tracking individual meetings</li>";
    echo "<li>✓ meeting_attendance - For recording member attendance</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>
