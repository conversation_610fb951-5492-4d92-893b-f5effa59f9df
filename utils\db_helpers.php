<?php
/**
 * Database Helper Functions
 * Provides utility functions for database operations
 */

/**
 * Execute a query with parameters
 * @param PDO $conn Database connection
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @return PDOStatement|false
 */
function executeQuery($conn, $sql, $params = []) {
    try {
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Database query error: " . $e->getMessage());
        return false;
    }
}

/**
 * Fetch all results from a query
 * @param PDO $conn Database connection
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @return array|false
 */
function fetchAll($conn, $sql, $params = []) {
    $stmt = executeQuery($conn, $sql, $params);
    if ($stmt) {
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    return false;
}

/**
 * Fetch a single row from a query
 * @param PDO $conn Database connection
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @return object|false
 */
function fetchOne($conn, $sql, $params = []) {
    $stmt = executeQuery($conn, $sql, $params);
    if ($stmt) {
        return $stmt->fetch(PDO::FETCH_OBJ);
    }
    return false;
}

/**
 * Execute a query and return the last insert ID
 * @param PDO $conn Database connection
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @return int|false
 */
function executeInsert($conn, $sql, $params = []) {
    try {
        $stmt = $conn->prepare($sql);
        if ($stmt->execute($params)) {
            return $conn->lastInsertId();
        }
        return false;
    } catch (PDOException $e) {
        error_log("Database insert error: " . $e->getMessage());
        return false;
    }
}

/**
 * Execute a query and return success/failure
 * @param PDO $conn Database connection
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @return bool
 */
function executeUpdate($conn, $sql, $params = []) {
    try {
        $stmt = $conn->prepare($sql);
        return $stmt->execute($params);
    } catch (PDOException $e) {
        error_log("Database update error: " . $e->getMessage());
        return false;
    }
}
