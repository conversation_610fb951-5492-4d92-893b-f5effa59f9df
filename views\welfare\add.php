<?php
$page_title = "Record Monthly Welfare Dues";
$active_page = "welfare";
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center mb-6">
        <a href="<?php echo BASE_URL; ?>welfare" class="text-gray-600 hover:text-gray-800 mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Record Monthly Welfare Dues</h1>
            <p class="text-gray-600 mt-1">Record a member's monthly welfare dues payment</p>
        </div>
    </div>

    <!-- Add Payment Form -->
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Monthly Dues Payment Details</h2>
                <p class="text-sm text-gray-600 mt-1">Record monthly welfare dues payment for <?php echo date('F Y'); ?></p>
            </div>
            
            <form action="<?php echo BASE_URL; ?>welfare/add" method="POST" class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Member Selection -->
                    <div class="md:col-span-2">
                        <label for="member_search" class="block text-sm font-medium text-gray-700 mb-2">
                            Select Member <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <!-- Hidden input for form submission -->
                            <input type="hidden" name="member_id" id="member_id" required>

                            <!-- Search input -->
                            <input type="text"
                                   id="member_search"
                                   placeholder="Search by name or phone number..."
                                   autocomplete="off"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">

                            <!-- Search results dropdown -->
                            <div id="member_dropdown"
                                 class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                                <div id="member_results" class="py-1">
                                    <!-- Results will be populated here -->
                                </div>
                                <div id="no_results" class="px-3 py-2 text-gray-500 text-sm hidden">
                                    No members found
                                </div>
                            </div>
                        </div>

                        <!-- Selected member display -->
                        <div id="selected_member" class="mt-2 hidden">
                            <div class="flex items-center p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
                                <div class="flex-1">
                                    <p class="font-medium text-emerald-900" id="selected_member_name"></p>
                                    <p class="text-sm text-emerald-700" id="selected_member_phone"></p>
                                </div>
                                <button type="button"
                                        onclick="clearMemberSelection()"
                                        class="text-emerald-600 hover:text-emerald-800">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Dues Info -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Payment Type
                        </label>
                        <div class="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-700">
                            <i class="fas fa-coins text-emerald-600 mr-2"></i>
                            Monthly Welfare Dues - <?php echo date('F Y'); ?>
                        </div>
                    </div>

                    <!-- Amount -->
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                            Amount (₵) <span class="text-red-500">*</span>
                        </label>
                        <input type="number" name="amount" id="amount" step="0.01" min="0" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                               placeholder="0.00">
                    </div>

                    <!-- Payment Date -->
                    <div>
                        <label for="payment_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Payment Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="payment_date" id="payment_date" required
                               value="<?php echo date('Y-m-d'); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-2">
                            Payment Method <span class="text-red-500">*</span>
                        </label>
                        <select name="payment_method" id="payment_method" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                            <option value="">Select method...</option>
                            <option value="cash">Cash</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="mobile_money">Mobile Money</option>
                            <option value="check">Check</option>
                        </select>
                    </div>

                    <!-- Reference Number -->
                    <div class="md:col-span-2">
                        <label for="reference_number" class="block text-sm font-medium text-gray-700 mb-2">
                            Reference Number
                        </label>
                        <input type="text" name="reference_number" id="reference_number"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                               placeholder="Transaction reference, check number, etc.">
                    </div>

                    <!-- Monthly Dues Notice -->
                    <div class="md:col-span-2">
                        <div class="p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-emerald-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-emerald-800">Monthly Welfare Dues</h3>
                                    <div class="mt-2 text-sm text-emerald-700">
                                        <p>Recording monthly welfare dues payment for <?php echo date('F Y'); ?>. Each member can only pay once per month.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Additional Notes
                        </label>
                        <textarea name="notes" id="notes" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                                  placeholder="Any additional notes or comments..."></textarea>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                    <a href="<?php echo BASE_URL; ?>welfare" 
                       class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit"
                            class="px-6 py-2 bg-emerald-500 hover:bg-emerald-600 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-coins mr-2"></i>
                        Record Dues Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Member data for search functionality
const members = <?php echo json_encode($members); ?>;

// Enhanced member search functionality
document.addEventListener('DOMContentLoaded', function() {
    const memberSearch = document.getElementById('member_search');
    const memberDropdown = document.getElementById('member_dropdown');
    const memberResults = document.getElementById('member_results');
    const noResults = document.getElementById('no_results');
    const memberIdInput = document.getElementById('member_id');
    const selectedMemberDiv = document.getElementById('selected_member');

    // Search functionality
    memberSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();

        if (searchTerm.length === 0) {
            memberDropdown.classList.add('hidden');
            return;
        }

        // Filter members based on search term
        const filteredMembers = members.filter(member => {
            const fullName = `${member.first_name} ${member.last_name}`.toLowerCase();
            const phone = member.phone_number ? member.phone_number.toLowerCase() : '';
            return fullName.includes(searchTerm) || phone.includes(searchTerm);
        });

        // Display results
        displaySearchResults(filteredMembers);
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!memberSearch.contains(e.target) && !memberDropdown.contains(e.target)) {
            memberDropdown.classList.add('hidden');
        }
    });

    // Show dropdown when focusing on search input
    memberSearch.addEventListener('focus', function() {
        if (this.value.trim().length > 0) {
            memberDropdown.classList.remove('hidden');
        }
    });

    function displaySearchResults(filteredMembers) {
        memberResults.innerHTML = '';

        if (filteredMembers.length === 0) {
            noResults.classList.remove('hidden');
            memberResults.classList.add('hidden');
        } else {
            noResults.classList.add('hidden');
            memberResults.classList.remove('hidden');

            filteredMembers.forEach(member => {
                const resultItem = document.createElement('div');
                resultItem.className = 'px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0';
                resultItem.innerHTML = `
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-emerald-600 font-medium text-sm">
                                ${member.first_name.charAt(0)}${member.last_name.charAt(0)}
                            </span>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">${member.first_name} ${member.last_name}</p>
                            ${member.phone_number ? `<p class="text-sm text-gray-600">${member.phone_number}</p>` : ''}
                        </div>
                    </div>
                `;

                resultItem.addEventListener('click', function() {
                    selectMember(member);
                });

                memberResults.appendChild(resultItem);
            });
        }

        memberDropdown.classList.remove('hidden');
    }

    function selectMember(member) {
        // Set hidden input value
        memberIdInput.value = member.id;

        // Clear search input
        memberSearch.value = '';

        // Hide dropdown
        memberDropdown.classList.add('hidden');

        // Show selected member
        document.getElementById('selected_member_name').textContent = `${member.first_name} ${member.last_name}`;
        document.getElementById('selected_member_phone').textContent = member.phone_number || 'No phone number';
        selectedMemberDiv.classList.remove('hidden');

        // Hide search input
        memberSearch.style.display = 'none';
    }

    // Auto-calculate and validate amount
    const amountInput = document.getElementById('amount');
    if (amountInput) {
        amountInput.addEventListener('input', function() {
            const value = parseFloat(this.value);
            if (value < 0) {
                this.value = 0;
            }
        });
    }

    // Set max date to today
    const dateInput = document.getElementById('payment_date');
    if (dateInput) {
        dateInput.max = new Date().toISOString().split('T')[0];
    }
});

// Clear member selection function
function clearMemberSelection() {
    document.getElementById('member_id').value = '';
    document.getElementById('member_search').value = '';
    document.getElementById('member_search').style.display = 'block';
    document.getElementById('selected_member').classList.add('hidden');
    document.getElementById('member_dropdown').classList.add('hidden');
    document.getElementById('member_search').focus();
}
</script>


