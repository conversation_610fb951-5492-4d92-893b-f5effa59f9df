<div class="container mx-auto">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white">
            <h1 class="text-3xl font-bold">Add Individual Attendance</h1>
            <p class="mt-2 opacity-90">Record attendance for church members</p>
        </div>
        <div class="p-6 bg-white">
            <div class="flex flex-wrap gap-3 justify-end">
                <a href="<?php echo BASE_URL; ?>attendance" class="bg-gray-500 hover:bg-gray-600 text-white py-2.5 px-5 rounded-md flex items-center justify-center shadow-md transition-all duration-200 font-medium">
                    <i class="fas fa-arrow-left mr-2 text-lg"></i> Back to Attendance
                </a>
                <a href="<?php echo BASE_URL; ?>attendance/bulk" class="bg-green-600 hover:bg-green-700 text-white py-2.5 px-5 rounded-md flex items-center justify-center shadow-md transition-all duration-200 font-medium">
                    <i class="fas fa-users mr-2 text-lg"></i> Bulk Attendance
                </a>
            </div>
        </div>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <p class="font-bold">Please fix the following errors:</p>
            <ul class="list-disc ml-5 mt-2">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Success Message -->
    <?php if (isset($_SESSION['success_message'])) : ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
            <p><?php echo $_SESSION['success_message']; ?></p>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <!-- Attendance Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6 max-w-4xl mx-auto border border-gray-200">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-blue-100 text-blue-600 mr-3">
                    <i class="fas fa-clipboard-check text-lg"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-800">Attendance Details</h2>
                    <p class="text-sm text-gray-600">Record attendance for an individual member</p>
                </div>
            </div>
        </div>

        <div class="p-8">
            <form action="<?php echo BASE_URL; ?>attendance/store" method="POST">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Service -->
                    <div class="bg-white p-6 rounded-lg border-2 border-gray-300 hover:border-primary transition-colors duration-200 shadow-sm">
                        <label for="service_id" class="block text-sm font-medium text-gray-700 mb-3">Service <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <select id="service_id" name="service_id" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-3.5 px-4 appearance-none" required>
                                <option value="">Select Service</option>
                                <?php foreach ($services as $service) : ?>
                                    <option value="<?php echo $service['id']; ?>" <?php echo (isset($_SESSION['form_data']['service_id']) && $_SESSION['form_data']['service_id'] == $service['id']) ? 'selected' : ''; ?>>
                                        <?php echo $service['name']; ?> (<?php echo ucfirst($service['day_of_week']); ?> - <?php echo date('h:i A', strtotime($service['time'])); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="fas fa-church text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Date -->
                    <div class="bg-white p-6 rounded-lg border-2 border-gray-300 hover:border-primary transition-colors duration-200 shadow-sm">
                        <label for="attendance_date" class="block text-sm font-medium text-gray-700 mb-3">Attendance Date <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <input type="date" id="attendance_date" name="attendance_date" value="<?php echo isset($_SESSION['form_data']['attendance_date']) ? $_SESSION['form_data']['attendance_date'] : date('Y-m-d'); ?>" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-3.5 px-4" required>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i class="fas fa-calendar-alt text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>

            <div class="mt-8">
                <!-- Member (Optional) -->
                <div class="bg-white p-6 rounded-lg border-2 border-gray-300 hover:border-primary transition-colors duration-200 shadow-sm">
                    <label for="member_id" class="block text-sm font-medium text-gray-700 mb-3">Member</label>
                    <div class="relative">
                        <select id="member_id" name="member_id" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-3.5 px-4 appearance-none">
                            <option value="">General Attendance (No specific member)</option>
                            <?php foreach ($members as $member) : ?>
                                <option value="<?php echo $member['id']; ?>" <?php echo (isset($_SESSION['form_data']['member_id']) && $_SESSION['form_data']['member_id'] == $member['id']) ? 'selected' : ''; ?>>
                                    <?php echo $member['first_name'] . ' ' . $member['last_name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-3">Leave blank for general attendance count</p>
                </div>
            </div>

            <div class="mt-8">
                <!-- Status -->
                <div class="bg-white p-6 rounded-lg border-2 border-gray-300 hover:border-primary transition-colors duration-200 shadow-sm">
                    <label class="block text-sm font-medium text-gray-700 mb-4">Attendance Status</label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-green-50 border-2 border-green-200 rounded-lg p-5 flex items-center shadow-sm hover:shadow-md hover:border-green-400 transition-all duration-200 cursor-pointer">
                            <input type="radio" id="status_present" name="status" value="present" class="h-5 w-5 text-green-600 focus:ring-green-500 border-gray-300" <?php echo (!isset($_SESSION['form_data']['status']) || $_SESSION['form_data']['status'] == 'present') ? 'checked' : ''; ?>>
                            <label for="status_present" class="ml-3 text-sm text-gray-700 flex items-center cursor-pointer">
                                <i class="fas fa-check-circle text-green-500 mr-2 text-lg"></i> Present
                            </label>
                        </div>
                        <div class="bg-red-50 border-2 border-red-200 rounded-lg p-5 flex items-center shadow-sm hover:shadow-md hover:border-red-400 transition-all duration-200 cursor-pointer">
                            <input type="radio" id="status_absent" name="status" value="absent" class="h-5 w-5 text-red-600 focus:ring-red-500 border-gray-300" <?php echo (isset($_SESSION['form_data']['status']) && $_SESSION['form_data']['status'] == 'absent') ? 'checked' : ''; ?>>
                            <label for="status_absent" class="ml-3 text-sm text-gray-700 flex items-center cursor-pointer">
                                <i class="fas fa-times-circle text-red-500 mr-2 text-lg"></i> Absent
                            </label>
                        </div>
                        <div class="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-5 flex items-center shadow-sm hover:shadow-md hover:border-yellow-400 transition-all duration-200 cursor-pointer">
                            <input type="radio" id="status_late" name="status" value="late" class="h-5 w-5 text-yellow-600 focus:ring-yellow-500 border-gray-300" <?php echo (isset($_SESSION['form_data']['status']) && $_SESSION['form_data']['status'] == 'late') ? 'checked' : ''; ?>>
                            <label for="status_late" class="ml-3 text-sm text-gray-700 flex items-center cursor-pointer">
                                <i class="fas fa-clock text-yellow-500 mr-2 text-lg"></i> Late
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-10 flex justify-center">
                <button type="submit" class="bg-gradient-to-r from-primary to-primary-dark text-white py-3.5 px-12 rounded-md flex items-center text-base font-medium transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                    <i class="fas fa-save mr-3 text-lg"></i> Save Attendance
                </button>
            </div>
        </form>
    </div>

    <!-- Quick Help -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-lg p-5 max-w-4xl mx-auto mb-6 shadow-sm">
        <div class="flex items-start">
            <div class="flex-shrink-0 p-2 bg-blue-100 rounded-full">
                <i class="fas fa-info-circle text-blue-500 text-lg"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-sm font-medium text-blue-800">Quick Help</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>Use this form to record attendance for a single member. For marking multiple members at once, use the <a href="<?php echo BASE_URL; ?>attendance/bulk" class="text-blue-600 hover:text-blue-800 underline font-medium transition-colors duration-200">Bulk Attendance</a> feature.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Clear form data
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>
