<div class="container mx-auto max-w-6xl px-4 fade-in">
    <!-- Enhanced Header Section -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border-2 border-gray-100">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Profile & Settings</h1>
                    <p class="mt-2 opacity-90">Manage your profile and system settings</p>
                </div>
                <div class="hidden md:block">
                    <div class="bg-white bg-opacity-20 rounded-full p-4">
                        <i class="fas fa-user-cog text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-r-lg shadow-sm" role="alert">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i>
                </div>
                <div>
                    <p class="font-bold">Please fix the following errors:</p>
                    <ul class="list-disc ml-5 mt-2">
                        <?php foreach ($_SESSION['errors'] as $error) : ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])) : ?>
        <?php
        // Handle both array and string flash message formats
        $flash_data = $_SESSION['flash_message'];
        if (is_array($flash_data)) {
            $message = $flash_data['message'] ?? '';
            $type = $flash_data['type'] ?? 'info';
        } else {
            $message = $flash_data;
            $type = $_SESSION['flash_type'] ?? 'info';
        }

        // Set colors based on type
        $bg_color = $type === 'danger' ? 'bg-red-100' : ($type === 'warning' ? 'bg-yellow-100' : ($type === 'success' ? 'bg-green-100' : 'bg-blue-100'));
        $border_color = $type === 'danger' ? 'border-red-500' : ($type === 'warning' ? 'border-yellow-500' : ($type === 'success' ? 'border-green-500' : 'border-blue-500'));
        $text_color = $type === 'danger' ? 'text-red-700' : ($type === 'warning' ? 'text-yellow-700' : ($type === 'success' ? 'text-green-700' : 'text-blue-700'));
        $icon = $type === 'danger' ? 'fa-exclamation-circle' : ($type === 'warning' ? 'fa-exclamation-triangle' : ($type === 'success' ? 'fa-check-circle' : 'fa-info-circle'));
        $icon_color = $type === 'danger' ? 'text-red-500' : ($type === 'warning' ? 'text-yellow-500' : ($type === 'success' ? 'text-green-500' : 'text-blue-500'));
        ?>
        <div class="<?php echo $bg_color; ?> border-l-4 <?php echo $border_color; ?> <?php echo $text_color; ?> p-4 mb-6 rounded-r-lg shadow-sm" role="alert">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas <?php echo $icon; ?> <?php echo $icon_color; ?> text-xl mr-3"></i>
                </div>
                <div>
                    <p class="font-bold"><?php echo htmlspecialchars($message); ?></p>
                </div>
            </div>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
        <?php unset($_SESSION['flash_type']); ?>
    <?php endif; ?>

    <!-- Profile Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="profileTabs" role="tablist">
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg hover:text-primary hover:border-primary active" id="profile-tab" data-tabs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="true">
                        <i class="fas fa-user mr-2"></i> My Profile
                    </button>
                </li>
                <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') : ?>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary hover:border-primary" id="admin-tab" data-tabs-target="#admin" type="button" role="tab" aria-controls="admin" aria-selected="false">
                        <i class="fas fa-cogs mr-2"></i> Admin Settings
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary hover:border-primary" id="appearance-tab" data-tabs-target="#appearance" type="button" role="tab" aria-controls="appearance" aria-selected="false">
                        <i class="fas fa-palette mr-2"></i> Appearance
                    </button>
                </li>
                <?php endif; ?>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary hover:border-primary" id="security-tab" data-tabs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                        <i class="fas fa-shield-alt mr-2"></i> Security
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="bg-white rounded-2xl shadow-lg overflow-hidden border-2 border-gray-100 relative">
        <!-- Decorative elements -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-primary bg-opacity-5 rounded-full -mr-10 -mt-10"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-secondary bg-opacity-10 rounded-full -ml-8 -mb-8"></div>
        
        <div class="p-8 relative z-10">
            <div id="profileTabContent">
                <!-- My Profile Tab -->
                <div class="block" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    <form action="<?php echo BASE_URL; ?>profile/update" method="POST" enctype="multipart/form-data" id="profileForm">
                        <div class="mb-6">
                            <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                                <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                    <i class="fas fa-user text-primary"></i>
                                </span>
                                Personal Information
                            </h2>

                            <!-- Profile Picture Section -->
                            <div class="mb-6 text-center">
                                <div class="relative inline-block">
                                    <div class="w-32 h-32 rounded-full overflow-hidden border-4 border-primary shadow-lg mx-auto">
                                        <?php if (!empty($current_user->profile_picture) && file_exists($current_user->profile_picture)) : ?>
                                            <img src="<?php echo BASE_URL . $current_user->profile_picture; ?>" alt="Profile Picture" class="w-full h-full object-cover">
                                        <?php else : ?>
                                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                                <i class="fas fa-user text-4xl text-gray-400"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <label for="profile_picture" class="absolute bottom-0 right-0 bg-primary text-white rounded-full p-2 cursor-pointer hover:bg-primary-dark transition-colors">
                                        <i class="fas fa-camera text-sm"></i>
                                    </label>
                                    <input type="file" id="profile_picture" name="profile_picture" accept="image/*" class="hidden" onchange="previewImage(this)">
                                </div>
                                <p class="text-sm text-gray-600 mt-2">Click the camera icon to change your profile picture</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Username -->
                                <div class="form-group">
                                    <label for="username" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-user-tag text-primary mr-2"></i>
                                        Username <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($current_user->username); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all" required>
                                </div>

                                <!-- Full Name -->
                                <div class="form-group">
                                    <label for="full_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-id-card text-primary mr-2"></i>
                                        Full Name
                                    </label>
                                    <input type="text" id="full_name" name="full_name" value="<?php echo htmlspecialchars($current_user->full_name ?? ''); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                </div>

                                <!-- Email -->
                                <div class="form-group">
                                    <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-envelope text-primary mr-2"></i>
                                        Email <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($current_user->email); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all" required>
                                </div>

                                <!-- Role (Read-only) -->
                                <div class="form-group">
                                    <label for="role" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-user-shield text-primary mr-2"></i>
                                        Role
                                    </label>
                                    <input type="text" id="role" value="<?php echo ucfirst($current_user->role); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed" readonly>
                                </div>
                            </div>

                            <!-- Password Change Section -->
                            <div class="mt-8 pt-6 border-t border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-lock text-primary mr-2"></i>
                                    Change Password
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="new_password" class="block text-sm font-semibold text-gray-700 mb-2">
                                            New Password
                                        </label>
                                        <input type="password" id="new_password" name="new_password" 
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                                               placeholder="Leave blank to keep current password">
                                    </div>
                                    <div class="form-group">
                                        <label for="confirm_password" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Confirm Password
                                        </label>
                                        <input type="password" id="confirm_password" name="confirm_password" 
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                                               placeholder="Confirm new password">
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="mt-8 flex justify-end">
                                <button type="submit" class="bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg">
                                    <i class="fas fa-save mr-2"></i>
                                    Update Profile
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Admin Settings Tab -->
                <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') : ?>
                <div class="hidden" id="admin" role="tabpanel" aria-labelledby="admin-tab">
                    <form action="<?php echo BASE_URL; ?>profile/update-settings" method="POST" enctype="multipart/form-data" id="adminForm">
                        <div class="mb-6">
                            <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                                <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                    <i class="fas fa-cogs text-primary"></i>
                                </span>
                                System Configuration
                            </h2>

                            <!-- Church Logo Section -->
                            <div class="mb-8 p-6 bg-gray-50 rounded-lg border">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-image text-primary mr-2"></i>
                                    Church Logo
                                </h3>

                                <div class="flex items-center space-x-6">
                                    <!-- Current Logo Display -->
                                    <div class="flex-shrink-0">
                                        <div class="w-24 h-24 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
                                            <?php
                                            $current_logo = $settings_array['church_logo'] ?? '';
                                            if (!empty($current_logo) && file_exists($current_logo)) :
                                            ?>
                                                <img src="<?php echo BASE_URL . $current_logo; ?>" alt="Church Logo" class="w-full h-full object-contain">
                                            <?php else : ?>
                                                <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                                                    <i class="fas fa-church text-2xl text-gray-400"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Logo Upload Controls -->
                                    <div class="flex-1">
                                        <div class="mb-4">
                                            <label for="church_logo" class="block text-sm font-semibold text-gray-700 mb-2">
                                                Upload New Logo
                                            </label>
                                            <input type="file" id="church_logo" name="church_logo" accept="image/*"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <p class="text-xs text-gray-500 mt-1">Supported formats: JPG, PNG, GIF, SVG. Max size: 2MB</p>
                                        </div>

                                        <?php if (!empty($current_logo)) : ?>
                                        <button type="button" onclick="resetLogo()" class="text-red-600 hover:text-red-800 text-sm font-medium">
                                            <i class="fas fa-trash mr-1"></i>
                                            Reset to Default
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Church Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <!-- Church Name -->
                                <div class="form-group">
                                    <label for="church_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-church text-primary mr-2"></i>
                                        Church Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="church_name" name="church_name"
                                           value="<?php echo htmlspecialchars($settings_array['church_name'] ?? 'ICGC Emmanuel Temple'); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all" required>
                                </div>

                                <!-- Application Name -->
                                <div class="form-group">
                                    <label for="app_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-desktop text-primary mr-2"></i>
                                        Application Name
                                    </label>
                                    <input type="text" id="app_name" name="app_name"
                                           value="<?php echo htmlspecialchars($settings_array['app_name'] ?? 'Church Management System'); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                </div>

                                <!-- Church Address -->
                                <div class="form-group md:col-span-2">
                                    <label for="church_address" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-map-marker-alt text-primary mr-2"></i>
                                        Church Address
                                    </label>
                                    <textarea id="church_address" name="church_address" rows="3"
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                                              placeholder="Enter church address"><?php echo htmlspecialchars($settings_array['church_address'] ?? ''); ?></textarea>
                                </div>

                                <!-- Church Phone -->
                                <div class="form-group">
                                    <label for="church_phone" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-phone text-primary mr-2"></i>
                                        Church Phone
                                    </label>
                                    <input type="tel" id="church_phone" name="church_phone"
                                           value="<?php echo htmlspecialchars($settings_array['church_phone'] ?? ''); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                </div>

                                <!-- Church Email -->
                                <div class="form-group">
                                    <label for="church_email" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-envelope text-primary mr-2"></i>
                                        Church Email
                                    </label>
                                    <input type="email" id="church_email" name="church_email"
                                           value="<?php echo htmlspecialchars($settings_array['church_email'] ?? ''); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                </div>

                                <!-- Church Website -->
                                <div class="form-group">
                                    <label for="church_website" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-globe text-primary mr-2"></i>
                                        Church Website
                                    </label>
                                    <input type="url" id="church_website" name="church_website"
                                           value="<?php echo htmlspecialchars($settings_array['church_website'] ?? ''); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                </div>

                                <!-- Application Description -->
                                <div class="form-group">
                                    <label for="app_description" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-info-circle text-primary mr-2"></i>
                                        Application Description
                                    </label>
                                    <input type="text" id="app_description" name="app_description"
                                           value="<?php echo htmlspecialchars($settings_array['app_description'] ?? 'Comprehensive church management solution'); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button type="submit" class="bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg">
                                    <i class="fas fa-save mr-2"></i>
                                    Update Settings
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- Appearance Tab -->
                <div class="hidden" id="appearance" role="tabpanel" aria-labelledby="appearance-tab">
                    <form action="<?php echo BASE_URL; ?>profile/update-settings" method="POST" id="appearanceForm">
                        <div class="mb-6">
                            <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                                <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                    <i class="fas fa-palette text-primary"></i>
                                </span>
                                Appearance Settings
                            </h2>

                            <!-- Color Scheme -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <!-- Primary Color -->
                                <div class="form-group">
                                    <label for="primary_color" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-paint-brush text-primary mr-2"></i>
                                        Primary Color
                                    </label>
                                    <div class="flex items-center space-x-3">
                                        <input type="color" id="primary_color" name="primary_color"
                                               value="<?php echo htmlspecialchars($settings_array['primary_color'] ?? '#3F7D58'); ?>"
                                               class="w-16 h-12 border border-gray-300 rounded-lg cursor-pointer">
                                        <input type="text" id="primary_color_text"
                                               value="<?php echo htmlspecialchars($settings_array['primary_color'] ?? '#3F7D58'); ?>"
                                               class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                                               readonly>
                                    </div>
                                </div>

                                <!-- Secondary Color -->
                                <div class="form-group">
                                    <label for="secondary_color" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-palette text-primary mr-2"></i>
                                        Secondary Color
                                    </label>
                                    <div class="flex items-center space-x-3">
                                        <input type="color" id="secondary_color" name="secondary_color"
                                               value="<?php echo htmlspecialchars($settings_array['secondary_color'] ?? '#D3E671'); ?>"
                                               class="w-16 h-12 border border-gray-300 rounded-lg cursor-pointer">
                                        <input type="text" id="secondary_color_text"
                                               value="<?php echo htmlspecialchars($settings_array['secondary_color'] ?? '#D3E671'); ?>"
                                               class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                                               readonly>
                                    </div>
                                </div>
                            </div>

                            <!-- Color Preview -->
                            <div class="mb-6 p-6 bg-gray-50 rounded-lg border">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">Color Preview</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div id="primary-preview" class="p-4 rounded-lg text-white text-center font-semibold" style="background-color: <?php echo htmlspecialchars($settings_array['primary_color'] ?? '#3F7D58'); ?>">
                                        Primary Color Sample
                                    </div>
                                    <div id="secondary-preview" class="p-4 rounded-lg text-gray-800 text-center font-semibold" style="background-color: <?php echo htmlspecialchars($settings_array['secondary_color'] ?? '#D3E671'); ?>">
                                        Secondary Color Sample
                                    </div>
                                </div>
                            </div>

                            <!-- Regional Settings -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                                <!-- Timezone -->
                                <div class="form-group">
                                    <label for="timezone" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-clock text-primary mr-2"></i>
                                        Timezone
                                    </label>
                                    <select id="timezone" name="timezone" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                        <option value="UTC" <?php echo ($settings_array['timezone'] ?? 'UTC') === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                        <option value="America/New_York" <?php echo ($settings_array['timezone'] ?? '') === 'America/New_York' ? 'selected' : ''; ?>>Eastern Time</option>
                                        <option value="America/Chicago" <?php echo ($settings_array['timezone'] ?? '') === 'America/Chicago' ? 'selected' : ''; ?>>Central Time</option>
                                        <option value="America/Denver" <?php echo ($settings_array['timezone'] ?? '') === 'America/Denver' ? 'selected' : ''; ?>>Mountain Time</option>
                                        <option value="America/Los_Angeles" <?php echo ($settings_array['timezone'] ?? '') === 'America/Los_Angeles' ? 'selected' : ''; ?>>Pacific Time</option>
                                        <option value="Africa/Accra" <?php echo ($settings_array['timezone'] ?? '') === 'Africa/Accra' ? 'selected' : ''; ?>>Ghana Time</option>
                                        <option value="Europe/London" <?php echo ($settings_array['timezone'] ?? '') === 'Europe/London' ? 'selected' : ''; ?>>London Time</option>
                                    </select>
                                </div>

                                <!-- Date Format -->
                                <div class="form-group">
                                    <label for="date_format" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-calendar text-primary mr-2"></i>
                                        Date Format
                                    </label>
                                    <select id="date_format" name="date_format" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                        <option value="Y-m-d" <?php echo ($settings_array['date_format'] ?? 'Y-m-d') === 'Y-m-d' ? 'selected' : ''; ?>>2024-12-31</option>
                                        <option value="m/d/Y" <?php echo ($settings_array['date_format'] ?? '') === 'm/d/Y' ? 'selected' : ''; ?>>12/31/2024</option>
                                        <option value="d/m/Y" <?php echo ($settings_array['date_format'] ?? '') === 'd/m/Y' ? 'selected' : ''; ?>>31/12/2024</option>
                                        <option value="F j, Y" <?php echo ($settings_array['date_format'] ?? '') === 'F j, Y' ? 'selected' : ''; ?>>December 31, 2024</option>
                                    </select>
                                </div>

                                <!-- Time Format -->
                                <div class="form-group">
                                    <label for="time_format" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-clock text-primary mr-2"></i>
                                        Time Format
                                    </label>
                                    <select id="time_format" name="time_format" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                                        <option value="H:i" <?php echo ($settings_array['time_format'] ?? 'H:i') === 'H:i' ? 'selected' : ''; ?>>24-hour (23:59)</option>
                                        <option value="g:i A" <?php echo ($settings_array['time_format'] ?? '') === 'g:i A' ? 'selected' : ''; ?>>12-hour (11:59 PM)</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button type="submit" class="bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg">
                                    <i class="fas fa-save mr-2"></i>
                                    Update Appearance
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <?php endif; ?>

                <!-- Security Tab -->
                <div class="hidden" id="security" role="tabpanel" aria-labelledby="security-tab">
                    <div class="mb-6">
                        <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                            <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                <i class="fas fa-shield-alt text-primary"></i>
                            </span>
                            Security Information
                        </h2>

                        <!-- Account Security Status -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <!-- Last Login -->
                            <div class="bg-gray-50 p-4 rounded-lg border">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2 flex items-center">
                                    <i class="fas fa-sign-in-alt text-green-600 mr-2"></i>
                                    Last Login
                                </h3>
                                <p class="text-gray-600">
                                    <?php echo isset($current_user->last_login) ? date('F j, Y \a\t g:i A', strtotime($current_user->last_login)) : 'Never'; ?>
                                </p>
                            </div>

                            <!-- Account Created -->
                            <div class="bg-gray-50 p-4 rounded-lg border">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2 flex items-center">
                                    <i class="fas fa-user-plus text-blue-600 mr-2"></i>
                                    Account Created
                                </h3>
                                <p class="text-gray-600">
                                    <?php echo isset($current_user->created_at) ? date('F j, Y', strtotime($current_user->created_at)) : 'Unknown'; ?>
                                </p>
                            </div>
                        </div>

                        <!-- Security Recommendations -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                                <i class="fas fa-lightbulb text-blue-600 mr-2"></i>
                                Security Recommendations
                            </h3>
                            <ul class="space-y-2 text-blue-700">
                                <li class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                    Use a strong, unique password
                                </li>
                                <li class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                    Change your password regularly
                                </li>
                                <li class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                    Don't share your login credentials
                                </li>
                                <li class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                    Log out when using shared computers
                                </li>
                            </ul>
                        </div>

                        <!-- Session Management -->
                        <div class="bg-gray-50 p-6 rounded-lg border">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <i class="fas fa-desktop text-primary mr-2"></i>
                                Session Management
                            </h3>
                            <p class="text-gray-600 mb-4">
                                You are currently logged in. Your session will expire automatically after a period of inactivity.
                            </p>
                            <button type="button" onclick="logoutAllSessions()" class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200">
                                <i class="fas fa-sign-out-alt mr-2"></i>
                                Logout from All Devices
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Profile Page -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabButtons = document.querySelectorAll('[data-tabs-target]');
    const tabContents = document.querySelectorAll('[role="tabpanel"]');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetId = button.getAttribute('data-tabs-target');
            const targetContent = document.querySelector(targetId);

            // Remove active classes from all tabs
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'border-primary', 'text-primary');
                btn.classList.add('border-transparent');
            });

            // Hide all tab contents
            tabContents.forEach(content => {
                content.classList.add('hidden');
                content.classList.remove('block');
            });

            // Activate clicked tab
            button.classList.add('active', 'border-primary', 'text-primary');
            button.classList.remove('border-transparent');

            // Show target content
            if (targetContent) {
                targetContent.classList.remove('hidden');
                targetContent.classList.add('block');
            }
        });
    });

    // Color picker functionality
    const primaryColorPicker = document.getElementById('primary_color');
    const primaryColorText = document.getElementById('primary_color_text');
    const primaryPreview = document.getElementById('primary-preview');

    const secondaryColorPicker = document.getElementById('secondary_color');
    const secondaryColorText = document.getElementById('secondary_color_text');
    const secondaryPreview = document.getElementById('secondary-preview');

    if (primaryColorPicker) {
        primaryColorPicker.addEventListener('input', function() {
            primaryColorText.value = this.value;
            if (primaryPreview) {
                primaryPreview.style.backgroundColor = this.value;
            }
        });
    }

    if (secondaryColorPicker) {
        secondaryColorPicker.addEventListener('input', function() {
            secondaryColorText.value = this.value;
            if (secondaryPreview) {
                secondaryPreview.style.backgroundColor = this.value;
            }
        });
    }

    // Password confirmation validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');

    if (newPassword && confirmPassword) {
        confirmPassword.addEventListener('input', function() {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        });
    }
});

// Profile picture preview
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = input.closest('.relative').querySelector('img');
            if (img) {
                img.src = e.target.result;
            } else {
                // Create new image if none exists
                const container = input.closest('.relative').querySelector('.w-32');
                container.innerHTML = `<img src="${e.target.result}" alt="Profile Picture" class="w-full h-full object-cover">`;
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Reset logo function
function resetLogo() {
    if (confirm('Are you sure you want to reset the logo to default?')) {
        fetch('<?php echo BASE_URL; ?>profile/reset-logo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to reset logo');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

// Logout all sessions
function logoutAllSessions() {
    if (confirm('Are you sure you want to logout from all devices? You will need to login again.')) {
        window.location.href = '<?php echo BASE_URL; ?>logout';
    }
}
</script>

<style>
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    box-shadow: 0 0 0 3px rgba(63, 125, 88, 0.1);
}

.tab-button.active {
    border-bottom-color: #3F7D58 !important;
    color: #3F7D58 !important;
}
</style>
