/**
 * Performance Monitor for Dashboard
 * Tracks page load times and optimization metrics
 */

(function() {
    'use strict';
    
    // Track page load performance
    window.addEventListener('load', function() {
        // Use Performance API if available
        if ('performance' in window) {
            const perfData = performance.getEntriesByType('navigation')[0];
            
            if (perfData) {
                const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
                const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart;
                const totalTime = perfData.loadEventEnd - perfData.fetchStart;
                
                // Log performance metrics (only in development)
                if (window.location.hostname === 'localhost') {
                    console.group('🚀 Dashboard Performance Metrics');
                    console.log(`📊 Total Load Time: ${totalTime.toFixed(2)}ms`);
                    console.log(`⚡ DOM Content Loaded: ${domContentLoaded.toFixed(2)}ms`);
                    console.log(`🎯 Load Event: ${loadTime.toFixed(2)}ms`);
                    
                    // Performance rating
                    if (totalTime < 1000) {
                        console.log('✅ Performance: Excellent (< 1s)');
                    } else if (totalTime < 2000) {
                        console.log('🟡 Performance: Good (< 2s)');
                    } else {
                        console.log('🔴 Performance: Needs Improvement (> 2s)');
                    }
                    console.groupEnd();
                }
            }
        }
    });
    
    // Track chart initialization times
    window.chartPerformance = {
        start: function(chartName) {
            this[chartName + '_start'] = performance.now();
        },
        
        end: function(chartName) {
            if (this[chartName + '_start']) {
                const duration = performance.now() - this[chartName + '_start'];
                if (window.location.hostname === 'localhost') {
                    console.log(`📈 ${chartName} Chart Load: ${duration.toFixed(2)}ms`);
                }
                delete this[chartName + '_start'];
            }
        }
    };
    
    // Monitor memory usage (if available)
    if ('memory' in performance) {
        setInterval(function() {
            const memory = performance.memory;
            if (memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB threshold
                console.warn('⚠️ High memory usage detected:', 
                    (memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + 'MB');
            }
        }, 30000); // Check every 30 seconds
    }
    
})();
