<?php
/**
 * Birthday SMS Page - Dedicated page for sending birthday wishes
 */

// Initialize member object
$database = new Database();
$member = new Member($database->getConnection());

// Get recipients from URL parameters
$recipients_param = isset($_GET['recipients']) ? sanitize($_GET['recipients']) : '';
$recipient_param = isset($_GET['recipient']) ? sanitize($_GET['recipient']) : '';

// Initialize recipients array
$selected_recipients = [];

// Process recipients
if (!empty($recipients_param)) {
    $recipient_ids = array_filter(array_map('trim', explode(',', $recipients_param)));
    foreach ($recipient_ids as $id) {
        if (is_numeric($id) && $member->getById($id)) {
            $selected_recipients[] = [
                'id' => $member->id,
                'first_name' => $member->first_name,
                'last_name' => $member->last_name,
                'phone_number' => $member->phone_number,
                'email' => $member->email
            ];
        }
    }
} elseif (!empty($recipient_param)) {
    if (is_numeric($recipient_param) && $member->getById($recipient_param)) {
        $selected_recipients[] = [
            'id' => $member->id,
            'first_name' => $member->first_name,
            'last_name' => $member->last_name,
            'phone_number' => $member->phone_number,
            'email' => $member->email
        ];
    }
}

// Default birthday message
$default_message = "Dear {first_name}, Happy Birthday! 🎉 May God bless you with many more years of joy, peace, and prosperity. We celebrate you today! - ICGC Emmanuel Temple";
?>

<div class="container mx-auto max-w-4xl">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg p-6 mb-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">
                    <i class="fas fa-birthday-cake mr-3"></i>
                    Birthday Wishes SMS
                </h1>
                <p class="text-blue-100">Send personalized birthday messages to celebrate our members</p>
            </div>
            <div class="text-right">
                <div class="bg-white bg-opacity-20 rounded-lg p-3">
                    <div class="text-sm text-blue-100">Recipients Selected</div>
                    <div class="text-2xl font-bold"><?php echo count($selected_recipients); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- SMS Form -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <form id="birthday-sms-form" action="<?php echo BASE_URL; ?>birthdays/send-sms" method="POST">
            <!-- Recipients Section -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-users mr-2 text-blue-500"></i>
                    Birthday Celebrants (<?php echo count($selected_recipients); ?>)
                </h3>
                
                <?php if (!empty($selected_recipients)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-4">
                    <?php foreach ($selected_recipients as $recipient): ?>
                    <div class="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex-shrink-0 w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                            <?php echo strtoupper(substr($recipient['first_name'], 0, 1)); ?>
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="font-medium text-gray-900">
                                <?php echo htmlspecialchars($recipient['first_name'] . ' ' . $recipient['last_name']); ?>
                            </div>
                            <div class="text-sm text-gray-600">
                                <?php echo htmlspecialchars($recipient['phone_number']); ?>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-birthday-cake mr-1"></i>
                                Birthday
                            </span>
                        </div>
                        <!-- Hidden input for form submission -->
                        <input type="hidden" name="recipients[]" value="<?php echo htmlspecialchars($recipient['phone_number']); ?>">
                        <input type="hidden" name="recipient_ids[]" value="<?php echo $recipient['id']; ?>">
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-exclamation-circle text-4xl mb-3"></i>
                    <p>No recipients selected. Please go back to the birthday page and select celebrants.</p>
                    <a href="<?php echo BASE_URL; ?>birthdays" class="mt-3 inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Birthdays
                    </a>
                </div>
                <?php endif; ?>
            </div>

            <?php if (!empty($selected_recipients)): ?>
            <!-- Message Section -->
            <div class="mb-6">
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-comment-alt mr-2 text-green-500"></i>
                    Birthday Message
                </label>
                <div class="relative">
                    <textarea 
                        id="message" 
                        name="message" 
                        rows="6" 
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                        placeholder="Enter your birthday message..."
                        required><?php echo htmlspecialchars($default_message); ?></textarea>
                    <div class="absolute bottom-3 right-3 text-sm text-gray-500">
                        <span id="char-count">0</span>/160 characters
                    </div>
                </div>
                <div class="mt-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle mr-1"></i>
                    Use <code>{first_name}</code> to personalize the message for each recipient.
                </div>
            </div>

            <!-- Preview Section -->
            <div class="mb-6">
                <h4 class="text-md font-medium text-gray-700 mb-3">
                    <i class="fas fa-eye mr-2 text-purple-500"></i>
                    Message Preview
                </h4>
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-2">Preview for: <strong><?php echo htmlspecialchars($selected_recipients[0]['first_name'] ?? 'Member'); ?></strong></div>
                    <div id="message-preview" class="text-gray-800 italic">
                        <?php echo str_replace('{first_name}', htmlspecialchars($selected_recipients[0]['first_name'] ?? 'Member'), htmlspecialchars($default_message)); ?>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-3 justify-between">
                <a href="<?php echo BASE_URL; ?>birthdays" class="flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Birthdays
                </a>
                
                <div class="flex gap-3">
                    <button type="button" id="preview-btn" class="flex items-center justify-center px-6 py-3 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors">
                        <i class="fas fa-eye mr-2"></i>
                        Preview All
                    </button>
                    
                    <button type="submit" id="send-btn" class="flex items-center justify-center px-8 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Birthday Wishes
                        <span class="ml-2 bg-green-400 text-green-900 px-2 py-1 rounded-full text-sm">
                            <?php echo count($selected_recipients); ?>
                        </span>
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </form>
    </div>
</div>

<!-- Preview Modal -->
<div id="preview-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-hidden">
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-lg font-semibold">Message Preview for All Recipients</h3>
                <button type="button" id="close-preview" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="preview-content" class="p-4 max-h-80 overflow-y-auto">
                <!-- Preview content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageTextarea = document.getElementById('message');
    const charCount = document.getElementById('char-count');
    const messagePreview = document.getElementById('message-preview');
    const previewBtn = document.getElementById('preview-btn');
    const previewModal = document.getElementById('preview-modal');
    const closePreview = document.getElementById('close-preview');
    const sendBtn = document.getElementById('send-btn');
    
    // Character count
    function updateCharCount() {
        const count = messageTextarea.value.length;
        charCount.textContent = count;
        charCount.className = count > 160 ? 'text-red-500 font-bold' : 'text-gray-500';
    }
    
    // Update preview
    function updatePreview() {
        const message = messageTextarea.value;
        const firstName = '<?php echo htmlspecialchars($selected_recipients[0]['first_name'] ?? 'Member'); ?>';
        const preview = message.replace(/{first_name}/g, firstName);
        messagePreview.textContent = preview;
    }
    
    // Event listeners
    messageTextarea.addEventListener('input', function() {
        updateCharCount();
        updatePreview();
    });
    
    // Preview all messages
    previewBtn.addEventListener('click', function() {
        const message = messageTextarea.value;
        const recipients = <?php echo json_encode($selected_recipients); ?>;
        
        let previewContent = '';
        recipients.forEach(function(recipient, index) {
            const personalizedMessage = message.replace(/{first_name}/g, recipient.first_name);
            previewContent += `
                <div class="mb-4 p-3 border border-gray-200 rounded-lg">
                    <div class="font-medium text-gray-800 mb-1">${recipient.first_name} ${recipient.last_name}</div>
                    <div class="text-sm text-gray-600 mb-2">${recipient.phone_number}</div>
                    <div class="text-gray-800 italic">"${personalizedMessage}"</div>
                </div>
            `;
        });
        
        document.getElementById('preview-content').innerHTML = previewContent;
        previewModal.classList.remove('hidden');
    });
    
    // Close preview
    closePreview.addEventListener('click', function() {
        previewModal.classList.add('hidden');
    });
    
    // Close modal on outside click
    previewModal.addEventListener('click', function(e) {
        if (e.target === previewModal) {
            previewModal.classList.add('hidden');
        }
    });
    
    // Form submission
    document.getElementById('birthday-sms-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (confirm('Send birthday wishes to all selected recipients?')) {
            sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
            sendBtn.disabled = true;
            
            // Submit the form
            this.submit();
        }
    });
    
    // Initialize
    updateCharCount();
    updatePreview();
});
</script>
