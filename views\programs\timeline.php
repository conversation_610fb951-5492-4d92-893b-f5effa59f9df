<?php
/**
 * Programs Timeline View - Church Program & Activities Planner
 */

// Header configuration
$header_title = 'Annual Program Timeline';
$header_subtitle = ($year ?? date('Y')) . ' - Complete yearly overview of all church programs and activities';
$header_icon = 'fas fa-timeline';
$header_width = 'container mx-auto';

// Custom navigation for timeline page
$navigation_buttons = [
    'create' => [
        'url' => BASE_URL . 'programs/create',
        'text' => 'New Program',
        'icon' => 'fas fa-plus',
        'style' => 'bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white'
    ],
    'list' => [
        'url' => BASE_URL . 'programs/list',
        'text' => 'List View',
        'icon' => 'fas fa-list',
        'style' => 'bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-gray-800'
    ],
    'dashboard' => [
        'url' => BASE_URL . 'programs/dashboard',
        'text' => 'Dashboard',
        'icon' => 'fas fa-chart-pie',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ],
    'calendar' => [
        'url' => BASE_URL . 'programs/calendar',
        'text' => 'Calendar',
        'icon' => 'fas fa-calendar-alt',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ]
];

// Include shared header
include 'components/header.php';
?>

    <!-- Program Summary -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-chart-bar mr-3 text-primary"></i>
                <?php echo $year; ?> Program Summary
            </h2>
            <p class="text-gray-600 text-sm mt-2">Annual overview and statistics</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Total Programs -->
                <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-calendar-check text-white text-xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-blue-600 mb-1">
                        <?php
                        $total_programs = 0;
                        foreach ($timeline_data as $month) {
                            $total_programs += count($month['programs']);
                        }
                        echo $total_programs;
                        ?>
                    </h3>
                    <p class="text-sm text-blue-700 font-medium">Total Programs</p>
                </div>

                <!-- Completed Programs -->
                <div class="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-green-600 mb-1">
                        <?php
                        $completed = 0;
                        foreach ($timeline_data as $month) {
                            foreach ($month['programs'] as $program) {
                                if ($program['status'] === 'completed') $completed++;
                            }
                        }
                        echo $completed;
                        ?>
                    </h3>
                    <p class="text-sm text-green-700 font-medium">Completed</p>
                </div>
                <!-- In Progress Programs -->
                <div class="text-center p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg">
                    <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-play-circle text-white text-xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-yellow-600 mb-1">
                        <?php
                        $in_progress = 0;
                        foreach ($timeline_data as $month) {
                            foreach ($month['programs'] as $program) {
                                if ($program['status'] === 'in_progress') $in_progress++;
                            }
                        }
                        echo $in_progress;
                        ?>
                    </h3>
                    <p class="text-sm text-yellow-700 font-medium">In Progress</p>
                </div>

                <!-- Planned Programs -->
                <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                    <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-purple-600 mb-1">
                        <?php
                        $planned = 0;
                        foreach ($timeline_data as $month) {
                            foreach ($month['programs'] as $program) {
                                if ($program['status'] === 'planned') $planned++;
                            }
                        }
                        echo $planned;
                        ?>
                    </h3>
                    <p class="text-sm text-purple-700 font-medium">Planned</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Year Navigation and Filters -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
            <div class="flex flex-col lg:flex-row justify-between items-center gap-4">
                <!-- Year Navigation -->
                <div class="flex items-center space-x-4">
                    <a href="<?php echo BASE_URL; ?>programs/timeline?year=<?php echo $year - 1; ?>"
                       class="inline-flex items-center justify-center w-10 h-10 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all duration-200 shadow-sm">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <h2 class="text-2xl font-bold text-gray-800"><?php echo $year; ?></h2>
                    <a href="<?php echo BASE_URL; ?>programs/timeline?year=<?php echo $year + 1; ?>"
                       class="inline-flex items-center justify-center w-10 h-10 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all duration-200 shadow-sm">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>

                <!-- Filters and Actions -->
                <div class="flex flex-wrap items-center gap-3">
                    <!-- Year Selector -->
                    <select onchange="window.location.href='<?php echo BASE_URL; ?>programs/timeline?year=' + this.value"
                            class="px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm font-medium">
                        <?php for ($y = date('Y') - 2; $y <= date('Y') + 3; $y++): ?>
                            <option value="<?php echo $y; ?>" <?php echo ($y == $year) ? 'selected' : ''; ?>>
                                <?php echo $y; ?>
                            </option>
                        <?php endfor; ?>
                    </select>

                    <!-- Category Filter -->
                    <select id="categoryFilter" onchange="filterTimeline()"
                            class="px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                        <?php endforeach; ?>
                    </select>

                    <!-- Department Filter -->
                    <select id="departmentFilter" onchange="filterTimeline()"
                            class="px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                        <option value="">All Departments</option>
                        <?php foreach ($departments as $department): ?>
                            <option value="<?php echo $department['id']; ?>"><?php echo htmlspecialchars($department['name']); ?></option>
                        <?php endforeach; ?>
                    </select>

                    <!-- Print Button -->
                    <button onclick="window.print()" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-all duration-200">
                        <i class="fas fa-print mr-2"></i> Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Timeline -->
    <div class="space-y-8">
        <?php foreach ($timeline_data as $month_num => $month_data): ?>
            <div class="bg-white rounded-xl shadow-md overflow-hidden timeline-month" data-month="<?php echo $month_num; ?>">
                <!-- Month Header -->
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                                <i class="fas fa-calendar-alt text-primary text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-800"><?php echo $month_data['name']; ?></h3>
                                <p class="text-gray-600 program-count">
                                    <span class="font-medium"><?php echo count($month_data['programs']); ?></span>
                                    program<?php echo count($month_data['programs']) !== 1 ? 's' : ''; ?> scheduled
                                </p>
                            </div>
                        </div>
                        <a href="<?php echo BASE_URL; ?>programs/calendar?month=<?php echo $month_num; ?>&year=<?php echo $year; ?>"
                           class="inline-flex items-center px-4 py-2 bg-primary hover:bg-primary-dark text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-sm">
                            <i class="fas fa-calendar mr-2"></i> View Calendar
                        </a>
                    </div>
                </div>

                <!-- Month Programs -->
                <div class="p-6">
                    <?php if (empty($month_data['programs'])): ?>
                        <div class="text-center py-12 no-programs">
                            <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <i class="fas fa-calendar-plus text-2xl text-gray-400"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-600 mb-2">No programs scheduled</h4>
                            <p class="text-gray-500">This month is currently free of scheduled programs.</p>
                        </div>
                    <?php else: ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 programs-grid">
                            <?php foreach ($month_data['programs'] as $program): ?>
                                <div class="bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-200 p-5 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 program-card"
                                     data-category="<?php echo $program['category_id']; ?>"
                                     data-department="<?php echo $program['department_id']; ?>">

                                    <!-- Program Header -->
                                    <div class="flex items-start space-x-4 mb-4">
                                        <div class="flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center shadow-sm"
                                             style="background-color: <?php echo $program['color_code'] ?? '#3F7D58'; ?>20;">
                                            <i class="<?php echo $program['icon'] ?? 'fas fa-calendar'; ?> text-lg"
                                               style="color: <?php echo $program['color_code'] ?? '#3F7D58'; ?>"></i>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <h4 class="text-lg font-semibold text-gray-900 mb-1 hover:text-primary transition-colors duration-200">
                                                <a href="<?php echo BASE_URL; ?>programs/show?id=<?php echo $program['id']; ?>" class="hover:underline">
                                                    <?php echo htmlspecialchars($program['title']); ?>
                                                </a>
                                            </h4>
                                            <div class="flex items-center space-x-3 text-sm text-gray-600">
                                                <span class="flex items-center font-medium">
                                                    <i class="fas fa-calendar mr-1 text-primary"></i>
                                                    <?php echo date('M j', strtotime($program['start_date'])); ?>
                                                    <?php if ($program['start_date'] !== $program['end_date']): ?>
                                                        - <?php echo date('M j', strtotime($program['end_date'])); ?>
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Program Details -->
                                    <div class="space-y-3">
                                        <!-- Status -->
                                        <div class="flex items-center justify-between">
                                            <?php
                                            $status_styles = [
                                                'planned' => 'bg-blue-100 text-blue-800 border-blue-200',
                                                'in_progress' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
                                                'completed' => 'bg-green-100 text-green-800 border-green-200',
                                                'cancelled' => 'bg-red-100 text-red-800 border-red-200',
                                                'postponed' => 'bg-gray-100 text-gray-800 border-gray-200'
                                            ];
                                            $status_style = $status_styles[$program['status']] ?? 'bg-gray-100 text-gray-800 border-gray-200';
                                            ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border <?php echo $status_style; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $program['status'])); ?>
                                            </span>

                                            <!-- Category Badge -->
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white"
                                                  style="background-color: <?php echo $program['color_code'] ?? '#3F7D58'; ?>">
                                                <?php echo htmlspecialchars($program['category_name']); ?>
                                            </span>
                                        </div>

                                        <!-- Department and Coordinator -->
                                        <div class="flex items-center justify-between text-sm text-gray-600">
                                            <span class="flex items-center">
                                                <i class="fas fa-building mr-1 text-gray-400"></i>
                                                <?php echo htmlspecialchars($program['department_name']); ?>
                                            </span>
                                            <?php if (!empty($program['coordinator_name'])): ?>
                                                <span class="flex items-center">
                                                    <i class="fas fa-user mr-1 text-gray-400"></i>
                                                    <?php echo htmlspecialchars($program['coordinator_name']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Description -->
                                        <?php if (!empty($program['description'])): ?>
                                            <div class="mt-3 pt-3 border-t border-gray-100">
                                                <p class="text-sm text-gray-600 line-clamp-2">
                                                    <?php echo htmlspecialchars(substr($program['description'], 0, 120)); ?>
                                                    <?php echo strlen($program['description']) > 120 ? '...' : ''; ?>
                                                </p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>


</div>

<script>
function filterTimeline() {
    const categoryFilter = document.getElementById('categoryFilter').value;
    const departmentFilter = document.getElementById('departmentFilter').value;

    document.querySelectorAll('.program-card').forEach(card => {
        const categoryMatch = !categoryFilter || card.dataset.category === categoryFilter;
        const departmentMatch = !departmentFilter || card.dataset.department === departmentFilter;

        if (categoryMatch && departmentMatch) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });

    // Update program counts
    document.querySelectorAll('.timeline-month').forEach(month => {
        const visiblePrograms = month.querySelectorAll('.program-card:not([style*="display: none"])').length;
        const countElement = month.querySelector('.program-count');

        // Update count text
        const programText = visiblePrograms === 1 ? 'program' : 'programs';
        countElement.innerHTML = `<span class="font-medium">${visiblePrograms}</span> ${programText} scheduled`;

        // Show/hide no programs message
        const noPrograms = month.querySelector('.no-programs');
        const programsGrid = month.querySelector('.programs-grid');

        if (visiblePrograms === 0) {
            if (noPrograms) noPrograms.style.display = 'flex';
            if (programsGrid) programsGrid.style.display = 'none';
        } else {
            if (noPrograms) noPrograms.style.display = 'none';
            if (programsGrid) programsGrid.style.display = 'grid';
        }
    });
}

// Add print styles
document.addEventListener('DOMContentLoaded', function() {
    const printStyles = `
        @media print {
            .no-print { display: none !important; }
            .container { max-width: none !important; margin: 0 !important; }
            .shadow-md { box-shadow: none !important; }
            .timeline-month { break-inside: avoid; margin-bottom: 1rem; }
        }
    `;

    const styleSheet = document.createElement('style');
    styleSheet.textContent = printStyles;
    document.head.appendChild(styleSheet);
});
</script>
