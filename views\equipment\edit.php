<div class="container mx-auto px-4 py-6 max-w-6xl">
    <!-- Hero Banner -->
    <div class="bg-gradient-to-r from-primary to-primary-light rounded-lg shadow-md p-6 mb-8 text-white">
        <div class="flex flex-col md:flex-row items-center justify-between">
            <div class="mb-6 md:mb-0 md:mr-6">
                <div class="flex items-center mb-3">
                    <div class="bg-white p-2 rounded-full shadow-md mr-3">
                        <i class="fas fa-edit text-primary text-xl"></i>
                    </div>
                    <h1 class="text-2xl md:text-3xl font-bold">Edit Equipment</h1>
                </div>
                <p class="opacity-90 mb-4">Update details for <?php echo $this->equipment->name; ?></p>
                <div class="flex flex-wrap gap-3">
                    <a href="<?php echo BASE_URL; ?>equipment" class="bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-md flex items-center text-sm font-medium transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Equipment
                    </a>
                </div>
            </div>
            <div class="flex-shrink-0">
                <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->equipment->id; ?>" class="bg-white text-primary hover:bg-gray-100 py-3 px-6 rounded-md flex items-center text-sm font-medium transition-all duration-300 shadow-md hover:shadow-lg">
                    <i class="fas fa-eye mr-2"></i> View Equipment
                </a>
            </div>
        </div>
    </div>

    <!-- Equipment Form -->
    <div class="bg-white rounded-lg shadow-md p-6 border border-gray-100 transition-all duration-300 hover:shadow-lg">
        <div class="mb-6 border-b border-gray-100 pb-4">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <div class="bg-primary bg-opacity-10 text-primary p-2 rounded-full mr-3">
                    <i class="fas fa-tools"></i>
                </div>
                Equipment Details
            </h2>
            <p class="text-sm text-gray-600 mt-1">Update the information for this equipment item</p>
        </div>
        <form action="<?php echo url('equipment/' . $equipment->id); ?>" method="POST" class="space-y-8">
            <input type="hidden" name="id" value="<?php echo $this->equipment->id; ?>">

            <!-- Basic Information Section -->
            <div class="p-5 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
                <h3 class="text-md font-medium text-gray-700 mb-4 flex items-center">
                    <i class="fas fa-info-circle text-primary mr-2"></i>
                    Basic Information
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div class="relative">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Equipment Name <span class="text-red-500">*</span></label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-tag text-gray-400"></i>
                        </div>
                        <input type="text" id="name" name="name" value="<?php echo $this->equipment->name; ?>" class="w-full rounded-md border-gray-300 pl-10 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Enter the full name of the equipment</p>
                </div>

                <!-- Category -->
                <div class="relative">
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category <span class="text-red-500">*</span></label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-th-large text-gray-400"></i>
                        </div>
                        <select id="category" name="category" class="w-full rounded-md border-gray-300 pl-10 pr-10 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 appearance-none" required>
                            <option value="sound" <?php echo ($this->equipment->category == 'sound') ? 'selected' : ''; ?>>Sound Equipment</option>
                            <option value="musical" <?php echo ($this->equipment->category == 'musical') ? 'selected' : ''; ?>>Musical Instruments</option>
                            <option value="furniture" <?php echo ($this->equipment->category == 'furniture') ? 'selected' : ''; ?>>Furniture</option>
                            <option value="electronics" <?php echo ($this->equipment->category == 'electronics') ? 'selected' : ''; ?>>Electronics</option>
                            <option value="office" <?php echo ($this->equipment->category == 'office') ? 'selected' : ''; ?>>Office Equipment</option>
                            <option value="other" <?php echo ($this->equipment->category == 'other') ? 'selected' : ''; ?>>Other</option>
                            <!-- Dynamic categories will be added here -->
                            <option value="new_category" class="font-semibold text-primary">+ Add New Category</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Select the category this equipment belongs to</p>
                </div>

                <!-- Purchase Date -->
                <div class="relative">
                    <label for="purchase_date" class="block text-sm font-medium text-gray-700 mb-2">Purchase Date</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-400"></i>
                        </div>
                        <input type="date" id="purchase_date" name="purchase_date" value="<?php echo $this->equipment->purchase_date; ?>" class="w-full rounded-md border-gray-300 pl-10 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">When was this equipment purchased</p>
                </div>

                <!-- Purchase Price -->
                <div class="relative">
                    <label for="purchase_price" class="block text-sm font-medium text-gray-700 mb-2">Purchase Price (GH₵)</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-money-bill-wave text-gray-400"></i>
                        </div>
                        <input type="number" id="purchase_price" name="purchase_price" value="<?php echo $this->equipment->purchase_price; ?>" step="0.01" min="0" class="w-full rounded-md border-gray-300 pl-10 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">How much did this equipment cost</p>
                </div>

                <!-- Condition -->
                <div class="relative">
                    <label for="condition" class="block text-sm font-medium text-gray-700 mb-2">Condition</label>
                    <div class="relative rounded-md shadow-sm">
                        <select id="condition" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 pl-10 appearance-none">
                            <option value="excellent" <?php echo ($this->equipment->status == 'excellent') ? 'selected' : ''; ?>>Excellent</option>
                            <option value="good" <?php echo ($this->equipment->status == 'good') ? 'selected' : ''; ?>>Good</option>
                            <option value="fair" <?php echo ($this->equipment->status == 'fair') ? 'selected' : ''; ?>>Fair</option>
                            <option value="poor" <?php echo ($this->equipment->status == 'poor') ? 'selected' : ''; ?>>Poor</option>
                        </select>
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <?php
                            $iconClass = '';
                            $bgClass = '';
                            switch ($this->equipment->status) {
                                case 'excellent':
                                    $iconClass = 'fa-check-circle text-green-600';
                                    $bgClass = 'bg-green-100';
                                    break;
                                case 'good':
                                    $iconClass = 'fa-thumbs-up text-blue-600';
                                    $bgClass = 'bg-blue-100';
                                    break;
                                case 'fair':
                                    $iconClass = 'fa-exclamation-circle text-yellow-600';
                                    $bgClass = 'bg-yellow-100';
                                    break;
                                case 'poor':
                                    $iconClass = 'fa-exclamation-triangle text-red-600';
                                    $bgClass = 'bg-red-100';
                                    break;
                                default:
                                    $iconClass = 'fa-check-circle text-green-600';
                                    $bgClass = 'bg-green-100';
                            }
                            ?>
                            <div class="rounded-full p-1 <?php echo $bgClass; ?>">
                                <i class="fas <?php echo $iconClass; ?>"></i>
                            </div>
                        </div>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Current condition of this equipment</p>
                </div>

                <!-- Location -->
                <div class="relative">
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-map-marker-alt text-gray-400"></i>
                        </div>
                        <input type="text" id="location" name="location" value="<?php echo $this->equipment->location; ?>" class="w-full rounded-md border-gray-300 pl-10 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Where is this equipment stored or used</p>
                </div>

                </div>
            </div>

            <!-- Additional Details Section -->
            <div class="p-5 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
                <h3 class="text-md font-medium text-gray-700 mb-4 flex items-center">
                    <i class="fas fa-clipboard-list text-primary mr-2"></i>
                    Additional Details
                </h3>
                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="4" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"><?php echo $this->equipment->description; ?></textarea>
                    <p class="text-xs text-gray-500 mt-1">Provide any additional details about this equipment item</p>
                </div>
            </div>

            <div class="pt-6 mt-8 border-t border-gray-100 flex flex-col sm:flex-row justify-end gap-3">
                <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->equipment->id; ?>" class="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2.5 px-6 rounded-lg font-medium transition-all duration-300 flex items-center justify-center border border-gray-200">
                    <i class="fas fa-times mr-2"></i> Cancel
                </a>
                <button type="submit" class="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-white py-2.5 px-6 rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow flex items-center justify-center">
                    <i class="fas fa-save mr-2"></i> Update Equipment
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add New Category Modal -->
<div id="addCategoryModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden overflow-y-auto h-full w-full z-50 backdrop-blur-sm transition-all duration-300">
    <div class="relative top-20 mx-auto p-6 border border-gray-200 w-96 shadow-2xl rounded-xl bg-white transform transition-all duration-300 scale-95 opacity-0">
        <div class="mt-3">
            <div class="mx-auto flex items-center justify-center h-14 w-14 rounded-full bg-primary-light bg-opacity-20 mb-4">
                <i class="fas fa-th-large text-primary text-xl"></i>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 text-center">Add New Category</h3>
            <div class="mt-4 px-2">
                <div class="mb-4">
                    <label for="newCategoryName" class="block text-sm font-medium text-gray-700 mb-2">Category Name</label>
                    <input type="text" id="newCategoryName" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" placeholder="Enter new category name">
                    <p class="text-xs text-gray-500 mt-1">This will be added to the category dropdown list.</p>
                </div>
                <div class="mb-4">
                    <label for="newCategoryValue" class="block text-sm font-medium text-gray-700 mb-2">Category Value (for database)</label>
                    <input type="text" id="newCategoryValue" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" placeholder="Enter category value (lowercase, no spaces)">
                    <p class="text-xs text-gray-500 mt-1">Use lowercase letters and underscores instead of spaces.</p>
                </div>
            </div>
            <div class="flex justify-end mt-4 space-x-3">
                <button id="cancelAddCategory" class="bg-white border border-gray-300 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-all duration-300">Cancel</button>
                <button id="confirmAddCategory" class="bg-gradient-to-r from-primary to-primary-light px-4 py-2 rounded-lg text-white hover:from-primary-dark hover:to-primary shadow-md hover:shadow-lg transition-all duration-300">Add Category</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle Add New Category functionality
        const categorySelect = document.getElementById('category');
        const addCategoryModal = document.getElementById('addCategoryModal');
        const modalContent = addCategoryModal.querySelector('.relative');
        const newCategoryNameInput = document.getElementById('newCategoryName');
        const newCategoryValueInput = document.getElementById('newCategoryValue');
        const cancelAddCategoryBtn = document.getElementById('cancelAddCategory');
        const confirmAddCategoryBtn = document.getElementById('confirmAddCategory');

        // Store added categories in localStorage to persist them
        let customCategories = [];
        if (localStorage.getItem('customEquipmentCategories')) {
            try {
                customCategories = JSON.parse(localStorage.getItem('customEquipmentCategories'));
                // Add custom categories to the dropdown
                customCategories.forEach(category => {
                    addCategoryToDropdown(category.value, category.name);
                });
            } catch (e) {
                console.error('Error loading custom categories:', e);
            }
        }

        // Show modal when "Add New Category" is selected
        categorySelect.addEventListener('change', function() {
            if (this.value === 'new_category') {
                // Show modal with animation
                addCategoryModal.classList.remove('hidden');
                setTimeout(() => {
                    modalContent.classList.add('scale-100');
                    modalContent.classList.remove('scale-95', 'opacity-0');
                    newCategoryNameInput.focus();
                }, 10);

                // Reset the select to the previous value
                this.value = '<?php echo $this->equipment->category; ?>';
            }
        });

        // Auto-generate category value from name
        newCategoryNameInput.addEventListener('input', function() {
            const name = this.value;
            const value = name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
            newCategoryValueInput.value = value;
        });

        // Close modal when Cancel is clicked
        cancelAddCategoryBtn.addEventListener('click', function() {
            closeAddCategoryModal();
        });

        // Close when clicking outside the modal
        addCategoryModal.addEventListener('click', function(e) {
            if (e.target === addCategoryModal) {
                closeAddCategoryModal();
            }
        });

        // Add new category when Confirm is clicked
        confirmAddCategoryBtn.addEventListener('click', function() {
            const categoryName = newCategoryNameInput.value.trim();
            const categoryValue = newCategoryValueInput.value.trim();

            if (!categoryName || !categoryValue) {
                alert('Please enter both a category name and value.');
                return;
            }

            // Add to dropdown
            addCategoryToDropdown(categoryValue, categoryName);

            // Save to localStorage
            customCategories.push({ name: categoryName, value: categoryValue });
            localStorage.setItem('customEquipmentCategories', JSON.stringify(customCategories));

            // Select the new category
            categorySelect.value = categoryValue;

            // Close modal
            closeAddCategoryModal();

            // Show success message
            const successMessage = document.createElement('div');
            successMessage.className = 'bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded-r-md';
            successMessage.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    <p>New category "${categoryName}" added successfully!</p>
                </div>
            `;

            const formContainer = document.querySelector('.bg-white.rounded-lg');
            formContainer.parentNode.insertBefore(successMessage, formContainer);

            // Remove success message after 3 seconds
            setTimeout(() => {
                successMessage.remove();
            }, 3000);
        });

        // Function to close the modal
        function closeAddCategoryModal() {
            modalContent.classList.add('scale-95', 'opacity-0');
            modalContent.classList.remove('scale-100');

            setTimeout(() => {
                addCategoryModal.classList.add('hidden');
                newCategoryNameInput.value = '';
                newCategoryValueInput.value = '';
            }, 300);
        }

        // Function to add a category to the dropdown
        function addCategoryToDropdown(value, name) {
            // Check if category already exists
            const existingOption = Array.from(categorySelect.options).find(option => option.value === value);
            if (existingOption) return;

            // Create new option
            const newOption = document.createElement('option');
            newOption.value = value;
            newOption.textContent = name;

            // Insert before the "Add New Category" option
            const addNewOption = categorySelect.querySelector('option[value="new_category"]');
            categorySelect.insertBefore(newOption, addNewOption);
        }
    });
</script>
