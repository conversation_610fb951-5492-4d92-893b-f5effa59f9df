<?php
/**
 * Program Activity Model for Church Program & Activities Planner
 */

class ProgramActivity {
    // Database connection and table name
    private $conn;
    private $table_name = "program_activities";

    // Object properties
    public $id;
    public $program_id;
    public $title;
    public $description;
    public $assigned_to;
    public $start_datetime;
    public $end_datetime;
    public $location;
    public $status;
    public $priority;
    public $estimated_cost;
    public $actual_cost;
    public $notes;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db = null) {
        $this->conn = $db;

        // If no database connection is provided, get one from the Database class
        if ($this->conn === null) {
            require_once 'config/database.php';
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }

    /**
     * Get all activities for a program
     *
     * @param int $program_id
     * @return PDOStatement
     */
    public function getByProgram($program_id) {
        $query = "SELECT pa.*, 
                         CONCAT(m.first_name, ' ', m.last_name) as assigned_to_name,
                         m.email as assigned_to_email,
                         m.phone_number as assigned_to_phone
                  FROM " . $this->table_name . " pa
                  LEFT JOIN members m ON pa.assigned_to = m.id
                  WHERE pa.program_id = :program_id
                  ORDER BY pa.start_datetime ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':program_id', $program_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get activity by ID
     *
     * @param int $id
     * @return array|false
     */
    public function getById($id) {
        $query = "SELECT pa.*, 
                         CONCAT(m.first_name, ' ', m.last_name) as assigned_to_name,
                         m.email as assigned_to_email,
                         m.phone_number as assigned_to_phone,
                         p.title as program_title
                  FROM " . $this->table_name . " pa
                  LEFT JOIN members m ON pa.assigned_to = m.id
                  LEFT JOIN church_programs p ON pa.program_id = p.id
                  WHERE pa.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create new activity
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (program_id, title, description, assigned_to, start_datetime, end_datetime,
                   location, status, priority, estimated_cost, notes)
                  VALUES
                  (:program_id, :title, :description, :assigned_to, :start_datetime, :end_datetime,
                   :location, :status, :priority, :estimated_cost, :notes)";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->location = htmlspecialchars(strip_tags($this->location));
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Set defaults
        if (empty($this->status)) {
            $this->status = 'pending';
        }
        if (empty($this->priority)) {
            $this->priority = 'medium';
        }
        if (!isset($this->estimated_cost)) {
            $this->estimated_cost = 0.00;
        }

        // Bind parameters
        $stmt->bindParam(':program_id', $this->program_id);
        $stmt->bindParam(':title', $this->title);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':assigned_to', $this->assigned_to);
        $stmt->bindParam(':start_datetime', $this->start_datetime);
        $stmt->bindParam(':end_datetime', $this->end_datetime);
        $stmt->bindParam(':location', $this->location);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':priority', $this->priority);
        $stmt->bindParam(':estimated_cost', $this->estimated_cost);
        $stmt->bindParam(':notes', $this->notes);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Update activity
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET title = :title,
                      description = :description,
                      assigned_to = :assigned_to,
                      start_datetime = :start_datetime,
                      end_datetime = :end_datetime,
                      location = :location,
                      status = :status,
                      priority = :priority,
                      estimated_cost = :estimated_cost,
                      actual_cost = :actual_cost,
                      notes = :notes,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->location = htmlspecialchars(strip_tags($this->location));
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':title', $this->title);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':assigned_to', $this->assigned_to);
        $stmt->bindParam(':start_datetime', $this->start_datetime);
        $stmt->bindParam(':end_datetime', $this->end_datetime);
        $stmt->bindParam(':location', $this->location);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':priority', $this->priority);
        $stmt->bindParam(':estimated_cost', $this->estimated_cost);
        $stmt->bindParam(':actual_cost', $this->actual_cost);
        $stmt->bindParam(':notes', $this->notes);

        return $stmt->execute();
    }

    /**
     * Delete activity
     *
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        return $stmt->execute();
    }

    /**
     * Update activity status
     *
     * @param string $new_status
     * @return bool
     */
    public function updateStatus($new_status) {
        $query = "UPDATE " . $this->table_name . " 
                  SET status = :status, updated_at = CURRENT_TIMESTAMP 
                  WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $new_status);
        $stmt->bindParam(':id', $this->id);
        return $stmt->execute();
    }

    /**
     * Get activities by assigned member
     *
     * @param int $member_id
     * @return PDOStatement
     */
    public function getByAssignedMember($member_id) {
        $query = "SELECT pa.*, 
                         p.title as program_title,
                         p.start_date as program_start_date
                  FROM " . $this->table_name . " pa
                  LEFT JOIN church_programs p ON pa.program_id = p.id
                  WHERE pa.assigned_to = :member_id
                  ORDER BY pa.start_datetime ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get upcoming activities
     *
     * @param int $limit
     * @return PDOStatement
     */
    public function getUpcoming($limit = 10) {
        $query = "SELECT pa.*, 
                         CONCAT(m.first_name, ' ', m.last_name) as assigned_to_name,
                         p.title as program_title
                  FROM " . $this->table_name . " pa
                  LEFT JOIN members m ON pa.assigned_to = m.id
                  LEFT JOIN church_programs p ON pa.program_id = p.id
                  WHERE pa.start_datetime >= NOW() AND pa.status != 'cancelled'
                  ORDER BY pa.start_datetime ASC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get activity statistics for a program
     *
     * @param int $program_id
     * @return array
     */
    public function getProgramActivityStats($program_id) {
        $stats = [];

        // Total activities
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE program_id = :program_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':program_id', $program_id);
        $stmt->execute();
        $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Activities by status
        $query = "SELECT status, COUNT(*) as count FROM " . $this->table_name . " 
                  WHERE program_id = :program_id GROUP BY status";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':program_id', $program_id);
        $stmt->execute();
        $status_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($status_counts as $status) {
            $stats['by_status'][$status['status']] = $status['count'];
        }

        // Total estimated and actual costs
        $query = "SELECT 
                    COALESCE(SUM(estimated_cost), 0) as total_estimated,
                    COALESCE(SUM(actual_cost), 0) as total_actual
                  FROM " . $this->table_name . " 
                  WHERE program_id = :program_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':program_id', $program_id);
        $stmt->execute();
        $cost_data = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['total_estimated_cost'] = $cost_data['total_estimated'];
        $stats['total_actual_cost'] = $cost_data['total_actual'];

        return $stats;
    }

    /**
     * Validate activity data
     *
     * @return array
     */
    public function validate() {
        $errors = [];

        if (empty($this->program_id)) {
            $errors[] = "Program ID is required";
        }

        if (empty($this->title)) {
            $errors[] = "Activity title is required";
        }

        if (empty($this->start_datetime)) {
            $errors[] = "Start date and time is required";
        }

        if (empty($this->end_datetime)) {
            $errors[] = "End date and time is required";
        }

        if (!empty($this->start_datetime) && !empty($this->end_datetime)) {
            if (strtotime($this->start_datetime) > strtotime($this->end_datetime)) {
                $errors[] = "End date and time must be after start date and time";
            }
        }

        if (!empty($this->estimated_cost) && $this->estimated_cost < 0) {
            $errors[] = "Estimated cost cannot be negative";
        }

        if (!empty($this->actual_cost) && $this->actual_cost < 0) {
            $errors[] = "Actual cost cannot be negative";
        }

        return $errors;
    }
}
