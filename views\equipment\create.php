<div class="container mx-auto px-4 py-6 max-w-4xl">
    <!-- Header with Icon -->
    <div class="flex justify-between items-center mb-8">
        <div class="flex items-center">
            <div class="bg-gradient-to-r from-primary to-primary-light p-3 rounded-full shadow-lg mr-4">
                <i class="fas fa-tools text-white text-2xl"></i>
            </div>
            <div>
                <h1 class="text-2xl font-bold text-gray-800 mb-1">Add Equipment</h1>
                <p class="text-gray-600">Record a new equipment item for ICGC Emmanuel Temple</p>
            </div>
        </div>
        <a href="<?php echo BASE_URL; ?>equipment" class="bg-white border border-gray-300 hover:bg-gray-100 text-gray-700 py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
            <i class="fas fa-arrow-left mr-2 text-primary"></i> Back to Equipment
        </a>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-50 border-l-4 border-red-500 text-red-700 p-5 mb-8 rounded-r-md shadow-sm" role="alert">
            <div class="flex items-center mb-2">
                <i class="fas fa-exclamation-circle text-red-500 mr-2 text-xl"></i>
                <p class="font-bold text-lg">Please fix the following errors:</p>
            </div>
            <ul class="list-disc ml-8 mt-2 space-y-1">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Equipment Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 transition-all duration-300 hover:shadow-xl overflow-hidden">
        <div class="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-100">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-clipboard-list text-primary mr-2"></i> Equipment Details
            </h2>
        </div>
        <form action="<?php echo url('equipment'); ?>" method="POST" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div class="form-field-container" data-tooltip="Enter the equipment name">
                    <div class="form-field-header">
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-blue-100 text-blue-600">
                                <i class="fas fa-tag"></i>
                            </div>
                            <span>Equipment Name</span> <span class="text-red-500 ml-1">*</span>
                        </label>
                    </div>
                    <input type="text" id="name" name="name" value="<?php echo isset($_SESSION['form_data']['name']) ? $_SESSION['form_data']['name'] : ''; ?>" class="form-input" required>
                </div>

                <!-- Category -->
                <div class="form-field-container" data-tooltip="Select the equipment category">
                    <div class="form-field-header">
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-purple-100 text-purple-600">
                                <i class="fas fa-th-large"></i>
                            </div>
                            <span>Category</span> <span class="text-red-500 ml-1">*</span>
                        </label>
                    </div>
                    <div class="relative">
                        <div class="flex space-x-2">
                            <div class="flex-grow relative">
                                <select id="category" name="category" class="form-select" required>
                                    <option value="">Select Category</option>
                                    <option value="sound" <?php echo (isset($_SESSION['form_data']['category']) && $_SESSION['form_data']['category'] == 'sound') ? 'selected' : ''; ?>>Sound Equipment</option>
                                    <option value="musical" <?php echo (isset($_SESSION['form_data']['category']) && $_SESSION['form_data']['category'] == 'musical') ? 'selected' : ''; ?>>Musical Instruments</option>
                                    <option value="furniture" <?php echo (isset($_SESSION['form_data']['category']) && $_SESSION['form_data']['category'] == 'furniture') ? 'selected' : ''; ?>>Furniture</option>
                                    <option value="electronics" <?php echo (isset($_SESSION['form_data']['category']) && $_SESSION['form_data']['category'] == 'electronics') ? 'selected' : ''; ?>>Electronics</option>
                                    <option value="office" <?php echo (isset($_SESSION['form_data']['category']) && $_SESSION['form_data']['category'] == 'office') ? 'selected' : ''; ?>>Office Equipment</option>
                                    <option value="other" <?php echo (isset($_SESSION['form_data']['category']) && $_SESSION['form_data']['category'] == 'other') ? 'selected' : ''; ?>>Other</option>
                                    <!-- Dynamic categories will be added here -->
                                    <option value="new_category" class="font-semibold text-primary">+ Add New Category</option>
                                </select>
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-500">
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Purchase Date -->
                <div class="form-field-container" data-tooltip="When was this equipment purchased?">
                    <div class="form-field-header">
                        <label for="purchase_date" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-green-100 text-green-600">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <span>Purchase Date</span>
                        </label>
                    </div>
                    <input type="date" id="purchase_date" name="purchase_date" value="<?php echo isset($_SESSION['form_data']['purchase_date']) ? $_SESSION['form_data']['purchase_date'] : ''; ?>" class="form-input">
                </div>

                <!-- Purchase Price -->
                <div class="form-field-container" data-tooltip="How much did this equipment cost?">
                    <div class="form-field-header">
                        <label for="purchase_price" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-yellow-100 text-yellow-600">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <span>Purchase Price (GH₵)</span>
                        </label>
                    </div>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500">GH₵</span>
                        </div>
                        <input type="number" id="purchase_price" name="purchase_price" step="0.01" min="0" value="<?php echo isset($_SESSION['form_data']['purchase_price']) ? $_SESSION['form_data']['purchase_price'] : ''; ?>" class="form-input pl-12">
                    </div>
                </div>

                <!-- Condition -->
                <div class="form-field-container" data-tooltip="What is the current condition of this equipment?">
                    <div class="form-field-header">
                        <label for="condition" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-red-100 text-red-600">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <span>Condition</span>
                        </label>
                    </div>
                    <div class="relative">
                        <select id="condition" name="status" class="form-select">
                            <option value="excellent" <?php echo (isset($_SESSION['form_data']['status']) && $_SESSION['form_data']['status'] == 'excellent') ? 'selected' : ''; ?>>Excellent</option>
                            <option value="good" <?php echo (!isset($_SESSION['form_data']['status']) || $_SESSION['form_data']['status'] == 'good') ? 'selected' : ''; ?>>Good</option>
                            <option value="fair" <?php echo (isset($_SESSION['form_data']['status']) && $_SESSION['form_data']['status'] == 'fair') ? 'selected' : ''; ?>>Fair</option>
                            <option value="poor" <?php echo (isset($_SESSION['form_data']['status']) && $_SESSION['form_data']['status'] == 'poor') ? 'selected' : ''; ?>>Poor</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-500">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                </div>

                <!-- Location -->
                <div class="form-field-container" data-tooltip="Where is this equipment stored or used?">
                    <div class="form-field-header">
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-indigo-100 text-indigo-600">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <span>Location</span>
                        </label>
                    </div>
                    <input type="text" id="location" name="location" value="<?php echo isset($_SESSION['form_data']['location']) ? $_SESSION['form_data']['location'] : ''; ?>" class="form-input" placeholder="e.g. Main Auditorium, Office, Storage Room">
                </div>

                <!-- Description -->
                <div class="md:col-span-2 form-field-container" data-tooltip="Provide additional details about this equipment">
                    <div class="form-field-header">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-gray-100 text-gray-600">
                                <i class="fas fa-align-left"></i>
                            </div>
                            <span>Description</span>
                        </label>
                    </div>
                    <textarea id="description" name="description" rows="3" class="form-textarea" placeholder="Enter any additional details about this equipment..."><?php echo isset($_SESSION['form_data']['description']) ? $_SESSION['form_data']['description'] : ''; ?></textarea>
                </div>
            </div>

            <div class="mt-8 flex justify-end space-x-3">
                <a href="<?php echo BASE_URL; ?>equipment" class="bg-white border border-gray-300 hover:bg-gray-100 text-gray-700 py-2.5 px-6 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                    <i class="fas fa-times mr-2"></i> Cancel
                </a>
                <button type="submit" class="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-white py-2.5 px-8 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 flex items-center">
                    <i class="fas fa-save mr-2"></i> Save Equipment
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add New Category Modal -->
<div id="addCategoryModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden overflow-y-auto h-full w-full z-50 backdrop-blur-sm transition-all duration-300">
    <div class="relative top-20 mx-auto p-6 border border-gray-200 w-96 shadow-2xl rounded-xl bg-white transform transition-all duration-300 scale-95 opacity-0">
        <div class="mt-3">
            <div class="mx-auto flex items-center justify-center h-14 w-14 rounded-full bg-primary-light bg-opacity-20 mb-4">
                <i class="fas fa-th-large text-primary text-xl"></i>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 text-center">Add New Category</h3>
            <div class="mt-4 px-2">
                <div class="mb-4">
                    <label for="newCategoryName" class="block text-sm font-medium text-gray-700 mb-2">Category Name</label>
                    <input type="text" id="newCategoryName" class="form-input" placeholder="Enter new category name">
                    <p class="text-xs text-gray-500 mt-1">This will be added to the category dropdown list.</p>
                </div>
                <div class="mb-4">
                    <label for="newCategoryValue" class="block text-sm font-medium text-gray-700 mb-2">Category Value (for database)</label>
                    <input type="text" id="newCategoryValue" class="form-input" placeholder="Enter category value (lowercase, no spaces)">
                    <p class="text-xs text-gray-500 mt-1">Use lowercase letters and underscores instead of spaces.</p>
                </div>
            </div>
            <div class="flex justify-end mt-4 space-x-3">
                <button id="cancelAddCategory" class="bg-white border border-gray-300 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-all duration-300">Cancel</button>
                <button id="confirmAddCategory" class="bg-gradient-to-r from-primary to-primary-light px-4 py-2 rounded-lg text-white hover:from-primary-dark hover:to-primary shadow-md hover:shadow-lg transition-all duration-300">Add Category</button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Form styling */
    .form-field-container {
        position: relative;
        margin-bottom: 0.5rem;
    }

    .form-field-header {
        margin-bottom: 0.5rem;
    }

    .icon-container {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    .form-input, .form-select, .form-textarea {
        width: 100%;
        padding: 0.625rem 0.75rem;
        border-width: 1px;
        border-color: #E5E7EB;
        border-radius: 0.375rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
        border-color: #4CBF26;
        box-shadow: 0 0 0 3px rgba(76, 191, 38, 0.2);
        outline: none;
    }

    .form-input:hover, .form-select:hover, .form-textarea:hover {
        border-color: #4CBF26;
    }

    /* Tooltip for form fields */
    .form-field-container::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        padding: 5px 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s ease;
        white-space: nowrap;
        z-index: 20;
    }

    .form-field-container:hover::after {
        opacity: 0.9;
        bottom: calc(100% + 5px);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add focus effects to form fields
        const formInputs = document.querySelectorAll('.form-input, .form-select, .form-textarea');

        formInputs.forEach(input => {
            input.addEventListener('focus', function() {
                const container = this.closest('.form-field-container');
                if (container) {
                    container.classList.add('focused');
                }
            });

            input.addEventListener('blur', function() {
                const container = this.closest('.form-field-container');
                if (container) {
                    container.classList.remove('focused');
                }
            });
        });

        // Highlight fields sequentially on page load
        const formFieldContainers = document.querySelectorAll('.form-field-container');
        formFieldContainers.forEach((container, index) => {
            setTimeout(() => {
                container.classList.add('highlight');
                setTimeout(() => {
                    container.classList.remove('highlight');
                }, 600);
            }, index * 150);
        });

        // Handle Add New Category functionality
        const categorySelect = document.getElementById('category');
        const addCategoryModal = document.getElementById('addCategoryModal');
        const modalContent = addCategoryModal.querySelector('.relative');
        const newCategoryNameInput = document.getElementById('newCategoryName');
        const newCategoryValueInput = document.getElementById('newCategoryValue');
        const cancelAddCategoryBtn = document.getElementById('cancelAddCategory');
        const confirmAddCategoryBtn = document.getElementById('confirmAddCategory');

        // Load categories from server database (not localStorage)
        loadEquipmentCategories();

        // Show modal when "Add New Category" is selected
        categorySelect.addEventListener('change', function() {
            if (this.value === 'new_category') {
                // Show modal with animation
                addCategoryModal.classList.remove('hidden');
                setTimeout(() => {
                    modalContent.classList.add('scale-100');
                    modalContent.classList.remove('scale-95', 'opacity-0');
                    newCategoryNameInput.focus();
                }, 10);

                // Reset the select to the first option
                this.selectedIndex = 0;
            }
        });

        // Auto-generate category value from name
        newCategoryNameInput.addEventListener('input', function() {
            const name = this.value;
            const value = name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
            newCategoryValueInput.value = value;
        });

        // Close modal when Cancel is clicked
        cancelAddCategoryBtn.addEventListener('click', function() {
            closeAddCategoryModal();
        });

        // Close when clicking outside the modal
        addCategoryModal.addEventListener('click', function(e) {
            if (e.target === addCategoryModal) {
                closeAddCategoryModal();
            }
        });

        // Add new category when Confirm is clicked
        confirmAddCategoryBtn.addEventListener('click', function() {
            const categoryName = newCategoryNameInput.value.trim();
            const categoryValue = newCategoryValueInput.value.trim();

            if (!categoryName || !categoryValue) {
                alert('Please enter both a category name and value.');
                return;
            }

            // Save to database via API
            saveEquipmentCategory(categoryName, categoryValue)
                .then(response => {
                    if (response.success) {
                        // Add to dropdown
                        addCategoryToDropdown(categoryValue, categoryName);

                        // Select the new category
                        categorySelect.value = categoryValue;

                        // Close modal
                        addCategoryModal.style.display = 'none';

                        // Show success message
                        showNotification('Category added successfully!', 'success');
                    } else {
                        showNotification('Error: ' + response.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error saving category:', error);
                    showNotification('Failed to save category. Please try again.', 'error');
                });

            return; // Exit early since we're handling async
            closeAddCategoryModal();

            // Show success message
            const successMessage = document.createElement('div');
            successMessage.className = 'bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded-r-md';
            successMessage.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    <p>New category "${categoryName}" added successfully!</p>
                </div>
            `;

            const formContainer = document.querySelector('.bg-white.rounded-xl');
            formContainer.parentNode.insertBefore(successMessage, formContainer);

            // Remove success message after 3 seconds
            setTimeout(() => {
                successMessage.remove();
            }, 3000);
        });

        // Function to close the modal
        function closeAddCategoryModal() {
            modalContent.classList.add('scale-95', 'opacity-0');
            modalContent.classList.remove('scale-100');

            setTimeout(() => {
                addCategoryModal.classList.add('hidden');
                newCategoryNameInput.value = '';
                newCategoryValueInput.value = '';
            }, 300);
        }

        // Function to add a category to the dropdown
        function addCategoryToDropdown(value, name) {
            // Check if category already exists
            const existingOption = Array.from(categorySelect.options).find(option => option.value === value);
            if (existingOption) return;

            // Create new option
            const newOption = document.createElement('option');
            newOption.value = value;
            newOption.textContent = name;

            // Insert before the "Add New Category" option
            const addNewOption = categorySelect.querySelector('option[value="new_category"]');
            categorySelect.insertBefore(newOption, addNewOption);
        }
    });
</script>

<style>
    /* Form styling */
    .form-field-container {
        position: relative;
        margin-bottom: 0.5rem;
    }

    .form-field-header {
        margin-bottom: 0.5rem;
    }

    .icon-container {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    .form-input, .form-select, .form-textarea {
        width: 100%;
        padding: 0.625rem 0.75rem;
        border-width: 1px;
        border-color: #E5E7EB;
        border-radius: 0.375rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
        border-color: #4CBF26;
        box-shadow: 0 0 0 3px rgba(76, 191, 38, 0.2);
        outline: none;
    }

    .form-input:hover, .form-select:hover, .form-textarea:hover {
        border-color: #4CBF26;
    }

    /* Tooltip for form fields */
    .form-field-container::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        padding: 5px 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s ease;
        white-space: nowrap;
        z-index: 20;
    }

    .form-field-container:hover::after {
        opacity: 0.9;
        bottom: calc(100% + 5px);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add focus effects to form fields
        const formInputs = document.querySelectorAll('.form-input, .form-select, .form-textarea');

        formInputs.forEach(input => {
            input.addEventListener('focus', function() {
                const container = this.closest('.form-field-container');
                if (container) {
                    container.classList.add('focused');
                }
            });

            input.addEventListener('blur', function() {
                const container = this.closest('.form-field-container');
                if (container) {
                    container.classList.remove('focused');
                }
            });
        });

        // Highlight fields sequentially on page load
        const formFieldContainers = document.querySelectorAll('.form-field-container');
        formFieldContainers.forEach((container, index) => {
            setTimeout(() => {
                container.classList.add('highlight');
                setTimeout(() => {
                    container.classList.remove('highlight');
                }, 600);
            }, index * 150);
        });
    });

    // API Functions for Equipment Categories
    async function loadEquipmentCategories() {
        try {
            const response = await fetch('<?php echo url("api/equipment-categories"); ?>', {
                method: 'GET',
                credentials: 'same-origin'
            });

            const data = await response.json();

            if (data.success && data.data) {
                // Clear existing custom categories and add from database
                data.data.forEach(category => {
                    addCategoryToDropdown(category.value, category.name);
                });
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    async function saveEquipmentCategory(name, value) {
        try {
            const response = await fetch('<?php echo url("api/equipment-categories"); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    name: name,
                    value: value,
                    csrf_token: '<?php echo generate_csrf_token(); ?>'
                })
            });

            return await response.json();
        } catch (error) {
            console.error('Error saving category:', error);
            throw error;
        }
    }

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
</script>

<?php
// Clear form data
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>
