<?php
/**
 * Time Helper Functions
 * 
 * Collection of utility functions for time and date formatting
 */

/**
 * Format time elapsed string
 * 
 * @param string $datetime The datetime to compare
 * @param bool $full Whether to show full format or abbreviated
 * @return string Formatted time elapsed string
 */
function time_elapsed_string($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    $diff->w = floor($diff->d / 7);
    $diff->d -= $diff->w * 7;

    $string = array(
        'y' => 'year',
        'm' => 'month',
        'w' => 'week',
        'd' => 'day',
        'h' => 'hour',
        'i' => 'minute',
        's' => 'second',
    );
    
    foreach ($string as $k => &$v) {
        if ($diff->$k) {
            $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
        } else {
            unset($string[$k]);
        }
    }

    if (!$full) $string = array_slice($string, 0, 1);
    return $string ? implode(', ', $string) . ' ago' : 'just now';
}

/**
 * Check if date is today
 * 
 * @param string $date The date to check
 * @return bool True if date is today
 */
function is_today($date) {
    return date('Y-m-d') === date('Y-m-d', strtotime($date));
}

/**
 * Check if date is this week
 * 
 * @param string $date The date to check
 * @return bool True if date is this week
 */
function is_this_week($date) {
    $week_start = date('Y-m-d', strtotime('monday this week'));
    $week_end = date('Y-m-d', strtotime('sunday this week'));
    $check_date = date('Y-m-d', strtotime($date));
    
    return $check_date >= $week_start && $check_date <= $week_end;
}
?>
