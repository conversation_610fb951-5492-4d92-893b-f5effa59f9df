<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Church Program Planner Migration - ICGC <PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            display: block;
            width: 100%;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .steps {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
            margin-top: 20px;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: rgba(255,255,255,0.7);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ Church Program & Activities Planner</h1>
        <h2>Database Migration Setup</h2>
        
        <div class="info">
            <h3>About This Migration</h3>
            <p>This migration will create the comprehensive database structure for the Church Program & Activities Planner feature, including:</p>
            <ul>
                <li><strong>Program Categories</strong> - Organize programs by type (Worship, Youth, Outreach, etc.)</li>
                <li><strong>Ministry Departments</strong> - Link programs to church departments</li>
                <li><strong>Church Programs</strong> - Main programs with full lifecycle management</li>
                <li><strong>Program Activities</strong> - Sub-tasks and activities within programs</li>
                <li><strong>Participants & Resources</strong> - Track participation and resource needs</li>
                <li><strong>Reminders & History</strong> - Automated notifications and audit trails</li>
            </ul>
        </div>
        
        <div class="warning">
            <h3>⚠️ Important Notes</h3>
            <ul>
                <li>This migration is safe and will not affect existing data</li>
                <li>All new tables use "IF NOT EXISTS" to prevent conflicts</li>
                <li>Default categories and departments will be populated</li>
                <li>Make sure you have database backup before proceeding</li>
            </ul>
        </div>
        
        <form method="post">
            <div class="form-group">
                <label for="db_host">Database Host:</label>
                <input type="text" id="db_host" name="db_host" value="localhost" required>
            </div>
            
            <div class="form-group">
                <label for="db_name">Database Name:</label>
                <input type="text" id="db_name" name="db_name" value="icgc_finance" required>
            </div>
            
            <div class="form-group">
                <label for="db_username">Database Username:</label>
                <input type="text" id="db_username" name="db_username" value="root" required>
            </div>
            
            <div class="form-group">
                <label for="db_password">Database Password:</label>
                <input type="password" id="db_password" name="db_password" value="">
            </div>
            
            <button type="submit" name="migrate" class="btn">🚀 Run Migration</button>
        </form>
        
        <?php
        if (isset($_POST['migrate'])) {
            echo '<div class="result">';
            
            // Set database configuration variables
            $host = $_POST['db_host'];
            $dbname = $_POST['db_name'];
            $username = $_POST['db_username'];
            $password = $_POST['db_password'];
            
            // Include the migration script
            include 'add_program_planner.php';
            
            echo '</div>';
            
            echo '<div class="steps">';
            echo '<h3>🎉 Next Steps:</h3>';
            echo '<div class="step">✅ Migration completed successfully!</div>';
            echo '<div class="step">🔗 Access the Program Planner at <a href="../../programs">Programs & Activities</a></div>';
            echo '<div class="step">📝 Start creating your first church program</div>';
            echo '<div class="step">👥 Assign coordinators and departments to programs</div>';
            echo '<div class="step">📅 View your annual program timeline</div>';
            echo '</div>';
        }
        ?>
    </div>
</body>
</html>
