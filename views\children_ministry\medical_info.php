<?php
/**
 * Medical Information View
 */

// Ensure we have the required data
if (!isset($child_info) || !$child_info) {
    redirect('children-ministry/children');
    exit;
}

$child = $child_info['child'];
$medical_info = $child_info['medical_info'];
?>

<div class="max-w-4xl mx-auto px-4 py-8">
    <!-- Enhanced Page Header -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-8">
        <div class="flex flex-col md:flex-row md:justify-between md:items-center">
            <div class="mb-4 md:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Medical Information</h1>
                <div class="flex items-center text-gray-600">
                    <i class="fas fa-user-circle mr-2"></i>
                    <span class="font-medium"><?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?></span>
                    <span class="mx-2">•</span>
                    <span>Age <?php echo $child['age']; ?></span>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo BASE_URL; ?>children-ministry/view-child?id=<?php echo $child['id']; ?>"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Child
                </a>
            </div>
        </div>
    </div>

    <!-- Enhanced Medical Information Form -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Medical Information Form</h3>
            <p class="text-gray-600 text-sm mt-1">Please provide accurate medical information to ensure the safety and well-being of the child.</p>
        </div>

        <div class="p-6">

            <form action="<?php echo BASE_URL; ?>children-ministry/save-medical-info" method="POST" class="space-y-8">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="child_id" value="<?php echo $child['id']; ?>">

                <!-- Medical Information Section -->
                <div class="space-y-6">
                    <h4 class="text-base font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        Medical Details
                    </h4>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Allergies -->
                        <div class="space-y-2">
                            <label for="allergies" class="block text-sm font-medium text-gray-900">
                                <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                                Allergies
                            </label>
                            <textarea id="allergies" name="allergies" rows="4"
                                      placeholder="List any known allergies (food, medication, environmental, etc.)"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"><?php echo htmlspecialchars($medical_info['allergies'] ?? ''); ?></textarea>
                            <p class="text-xs text-gray-500">Include severity and reactions if known</p>
                        </div>

                        <!-- Medical Conditions -->
                        <div class="space-y-2">
                            <label for="medical_conditions" class="block text-sm font-medium text-gray-900">
                                <i class="fas fa-stethoscope text-blue-600 mr-2"></i>
                                Medical Conditions
                            </label>
                            <textarea id="medical_conditions" name="medical_conditions" rows="4"
                                      placeholder="List any ongoing medical conditions (asthma, diabetes, etc.)"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"><?php echo htmlspecialchars($medical_info['medical_conditions'] ?? ''); ?></textarea>
                            <p class="text-xs text-gray-500">Include chronic conditions and their management</p>
                        </div>

                        <!-- Medications -->
                        <div class="space-y-2">
                            <label for="medications" class="block text-sm font-medium text-gray-900">
                                <i class="fas fa-pills text-green-600 mr-2"></i>
                                Current Medications
                            </label>
                            <textarea id="medications" name="medications" rows="4"
                                      placeholder="List current medications, dosages, and timing"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"><?php echo htmlspecialchars($medical_info['medications'] ?? ''); ?></textarea>
                            <p class="text-xs text-gray-500">Include prescription and over-the-counter medications</p>
                        </div>

                        <!-- Special Needs -->
                        <div class="space-y-2">
                            <label for="special_needs" class="block text-sm font-medium text-gray-900">
                                <i class="fas fa-heart text-purple-600 mr-2"></i>
                                Special Needs
                            </label>
                            <textarea id="special_needs" name="special_needs" rows="4"
                                      placeholder="Describe any special needs or accommodations required"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"><?php echo htmlspecialchars($medical_info['special_needs'] ?? ''); ?></textarea>
                            <p class="text-xs text-gray-500">Include physical, developmental, or behavioral needs</p>
                        </div>
                    </div>
                </div>

                <!-- Emergency Medical Contact -->
                <div class="space-y-6">
                    <h4 class="text-base font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-phone text-red-600 mr-2"></i>
                        Emergency Medical Contact
                    </h4>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <label for="emergency_medical_contact" class="block text-sm font-medium text-gray-900">
                                Emergency Contact Name
                            </label>
                            <input type="text" id="emergency_medical_contact" name="emergency_medical_contact"
                                   value="<?php echo htmlspecialchars($medical_info['emergency_medical_contact'] ?? ''); ?>"
                                   placeholder="Name of emergency medical contact"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>

                        <div class="space-y-2">
                            <label for="emergency_medical_phone" class="block text-sm font-medium text-gray-900">
                                Emergency Contact Phone
                            </label>
                            <input type="tel" id="emergency_medical_phone" name="emergency_medical_phone"
                                   value="<?php echo htmlspecialchars($medical_info['emergency_medical_phone'] ?? ''); ?>"
                                   placeholder="Phone number"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>
                    </div>
                </div>

                <!-- Doctor Information -->
                <div class="space-y-6">
                    <h4 class="text-base font-semibold text-gray-900 border-b border-gray-200 pb-2">
                        <i class="fas fa-user-md text-blue-600 mr-2"></i>
                        Primary Doctor Information
                    </h4>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                            <label for="doctor_name" class="block text-sm font-medium text-gray-900">
                                Doctor's Name
                            </label>
                            <input type="text" id="doctor_name" name="doctor_name"
                                   value="<?php echo htmlspecialchars($medical_info['doctor_name'] ?? ''); ?>"
                                   placeholder="Primary doctor or pediatrician name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>

                        <div class="space-y-2">
                            <label for="doctor_phone" class="block text-sm font-medium text-gray-900">
                                Doctor's Phone
                            </label>
                            <input type="tel" id="doctor_phone" name="doctor_phone"
                                   value="<?php echo htmlspecialchars($medical_info['doctor_phone'] ?? ''); ?>"
                                   placeholder="Doctor's office phone number"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>
                    </div>
                </div>

                <!-- Important Notice -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-600 text-lg"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-semibold text-blue-900 mb-3">Important Notice</h3>
                            <div class="text-sm text-blue-800 space-y-2">
                                <div class="flex items-start">
                                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5 text-xs"></i>
                                    <span>This information will be kept confidential and used only for the child's safety and care.</span>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5 text-xs"></i>
                                    <span>Please update this information whenever there are changes to the child's medical status.</span>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5 text-xs"></i>
                                    <span>In case of medical emergency, we will contact emergency services and the listed contacts.</span>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-check text-blue-600 mr-2 mt-0.5 text-xs"></i>
                                    <span>Staff members will be notified of critical medical information on a need-to-know basis.</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                    <a href="<?php echo BASE_URL; ?>children-ministry/view-child?id=<?php echo $child['id']; ?>"
                       class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        Save Medical Information
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Enhanced Medical Information Display -->
    <?php if ($medical_info): ?>
        <div class="mt-8 bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Current Medical Information Summary</h3>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <?php if ($medical_info['allergies']): ?>
                        <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                            <h4 class="font-semibold text-red-900 mb-3 flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                Allergies
                            </h4>
                            <p class="text-red-800 text-sm leading-relaxed"><?php echo nl2br(htmlspecialchars($medical_info['allergies'])); ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if ($medical_info['medical_conditions']): ?>
                        <div class="border border-blue-200 rounded-lg p-4 bg-blue-50">
                            <h4 class="font-semibold text-blue-900 mb-3 flex items-center">
                                <i class="fas fa-stethoscope mr-2"></i>
                                Medical Conditions
                            </h4>
                            <p class="text-blue-800 text-sm leading-relaxed"><?php echo nl2br(htmlspecialchars($medical_info['medical_conditions'])); ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if ($medical_info['medications']): ?>
                        <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                            <h4 class="font-semibold text-green-900 mb-3 flex items-center">
                                <i class="fas fa-pills mr-2"></i>
                                Current Medications
                            </h4>
                            <p class="text-green-800 text-sm leading-relaxed"><?php echo nl2br(htmlspecialchars($medical_info['medications'])); ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if ($medical_info['special_needs']): ?>
                        <div class="border border-purple-200 rounded-lg p-4 bg-purple-50">
                            <h4 class="font-semibold text-purple-900 mb-3 flex items-center">
                                <i class="fas fa-heart mr-2"></i>
                                Special Needs
                            </h4>
                            <p class="text-purple-800 text-sm leading-relaxed"><?php echo nl2br(htmlspecialchars($medical_info['special_needs'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if ($medical_info['emergency_medical_contact'] || $medical_info['doctor_name']): ?>
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="font-semibold text-gray-900 mb-4">Contact Information</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <?php if ($medical_info['emergency_medical_contact']): ?>
                                <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-phone text-red-600 mr-2"></i>
                                        <p class="text-sm font-semibold text-gray-900">Emergency Contact</p>
                                    </div>
                                    <p class="text-gray-900 font-medium"><?php echo htmlspecialchars($medical_info['emergency_medical_contact']); ?></p>
                                    <?php if ($medical_info['emergency_medical_phone']): ?>
                                        <p class="text-gray-600 text-sm mt-1"><?php echo htmlspecialchars($medical_info['emergency_medical_phone']); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($medical_info['doctor_name']): ?>
                                <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-user-md text-blue-600 mr-2"></i>
                                        <p class="text-sm font-semibold text-gray-900">Primary Doctor</p>
                                    </div>
                                    <p class="text-gray-900 font-medium"><?php echo htmlspecialchars($medical_info['doctor_name']); ?></p>
                                    <?php if ($medical_info['doctor_phone']): ?>
                                        <p class="text-gray-600 text-sm mt-1"><?php echo htmlspecialchars($medical_info['doctor_phone']); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-save draft functionality could be added here
    // Form validation
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        // Basic validation - ensure at least one field is filled
        const fields = ['allergies', 'medical_conditions', 'medications', 'special_needs'];
        const hasContent = fields.some(field => {
            const value = document.getElementById(field).value.trim();
            return value.length > 0;
        });
        
        if (!hasContent) {
            const proceed = confirm('No medical information has been entered. Do you want to save an empty medical record?');
            if (!proceed) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
