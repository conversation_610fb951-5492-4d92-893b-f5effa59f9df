<?php
/**
 * Smart Category Dashboard Controller
 * Intelligently routes between core and dynamic dashboards
 */

class SmartCategoryDashboardController {
    private $database;
    private $conn;

    public function __construct() {
        $this->database = new Database();
        $this->conn = $this->database->getConnection();
    }

    /**
     * Smart routing method - determines whether to use core or dynamic dashboard
     */
    public function showDashboard($categorySlug) {
        try {
            // Get category information
            $category = $this->getCategoryBySlug($categorySlug);
            
            if (!$category) {
                $_SESSION['flash_message'] = "Category '{$categorySlug}' not found.";
                $_SESSION['flash_type'] = 'danger';
                redirect('finance');
                return;
            }

            // Check if this is a core category with specialized dashboard
            if ($category->is_core && !empty($category->dashboard_route)) {
                $this->routeToCoreController($category);
            } else {
                $this->routeToDynamicController($category);
            }

        } catch (Exception $e) {
            error_log("Error in SmartCategoryDashboardController::showDashboard: " . $e->getMessage());
            $_SESSION['flash_message'] = 'Error loading category dashboard: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finance');
        }
    }

    /**
     * Route to core controller for specialized dashboards
     */
    private function routeToCoreController($category) {
        switch ($category->name) {
            case 'tithe':
                require_once 'controllers/FinanceDashboardController.php';
                $controller = new FinanceDashboardController();
                $controller->titheDashboard();
                break;
                
            case 'pledge':
                require_once 'controllers/FinanceDashboardController.php';
                $controller = new FinanceDashboardController();
                $controller->pledgeDashboard();
                break;
                
            case 'offering':
            case 'project_offering':
            case 'utilities':
            case 'events':
                // For these, we'll use the existing category dashboard with enhanced features
                $this->routeToEnhancedDynamic($category);
                break;
                
            default:
                // Fallback to dynamic
                $this->routeToDynamicController($category);
                break;
        }
    }

    /**
     * Route to dynamic controller with enhanced features for core categories
     */
    private function routeToEnhancedDynamic($category) {
        require_once 'controllers/EnhancedDynamicCategoryDashboardController.php';
        $controller = new EnhancedDynamicCategoryDashboardController();
        $controller->showDashboard($category->slug, true); // true = is core category
    }

    /**
     * Route to standard dynamic controller
     */
    private function routeToDynamicController($category) {
        require_once 'controllers/DynamicCategoryDashboardController.php';
        $controller = new DynamicCategoryDashboardController();
        $controller->showDashboard($category->slug);
    }

    /**
     * Get category by slug
     */
    private function getCategoryBySlug($slug) {
        try {
            $query = "SELECT * FROM custom_finance_categories WHERE slug = ? AND is_active = 1";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$slug]);
            
            return $stmt->fetch(PDO::FETCH_OBJ);
            
        } catch (Exception $e) {
            error_log("Error getting category by slug: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get dashboard analytics for any category
     */
    public function getDashboardAnalytics($categoryName, $isCore = false) {
        try {
            $analytics = [];
            
            // Base analytics for all categories
            $analytics['basic'] = $this->getBasicAnalytics($categoryName);
            
            // Enhanced analytics for core categories
            if ($isCore) {
                $analytics['enhanced'] = $this->getEnhancedAnalytics($categoryName);
                $analytics['trends'] = $this->getAdvancedTrends($categoryName);
                $analytics['insights'] = $this->getDataInsights($categoryName);
            }
            
            return $analytics;
            
        } catch (Exception $e) {
            error_log("Error getting dashboard analytics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Basic analytics for all categories
     */
    private function getBasicAnalytics($categoryName) {
        $query = "SELECT 
            COUNT(*) as total_count,
            COALESCE(SUM(amount), 0) as total_amount,
            COALESCE(AVG(amount), 0) as average_amount,
            MIN(transaction_date) as first_transaction,
            MAX(transaction_date) as last_transaction
            FROM finances 
            WHERE category = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$categoryName]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Enhanced analytics for core categories
     */
    private function getEnhancedAnalytics($categoryName) {
        // Monthly breakdown
        $monthlyQuery = "SELECT 
            DATE_FORMAT(transaction_date, '%Y-%m') as month,
            COUNT(*) as count,
            SUM(amount) as amount
            FROM finances 
            WHERE category = ?
            AND transaction_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
            ORDER BY month ASC";
        
        $stmt = $this->conn->prepare($monthlyQuery);
        $stmt->execute([$categoryName]);
        $monthly = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Payment method breakdown
        $paymentQuery = "SELECT 
            payment_method,
            COUNT(*) as count,
            SUM(amount) as amount
            FROM finances 
            WHERE category = ?
            GROUP BY payment_method";
        
        $stmt = $this->conn->prepare($paymentQuery);
        $stmt->execute([$categoryName]);
        $paymentMethods = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'monthly_breakdown' => $monthly,
            'payment_methods' => $paymentMethods
        ];
    }

    /**
     * Advanced trends analysis
     */
    private function getAdvancedTrends($categoryName) {
        // Growth rate calculation
        $growthQuery = "SELECT 
            DATE_FORMAT(transaction_date, '%Y-%m') as month,
            SUM(amount) as amount
            FROM finances 
            WHERE category = ?
            AND transaction_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 24 MONTH)
            GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
            ORDER BY month ASC";
        
        $stmt = $this->conn->prepare($growthQuery);
        $stmt->execute([$categoryName]);
        $trends = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Calculate growth rates
        $growthRates = [];
        for ($i = 1; $i < count($trends); $i++) {
            $current = $trends[$i]['amount'];
            $previous = $trends[$i-1]['amount'];
            
            if ($previous > 0) {
                $growthRate = (($current - $previous) / $previous) * 100;
                $growthRates[] = [
                    'month' => $trends[$i]['month'],
                    'growth_rate' => round($growthRate, 2)
                ];
            }
        }
        
        return [
            'monthly_amounts' => $trends,
            'growth_rates' => $growthRates
        ];
    }

    /**
     * Data insights and recommendations
     */
    private function getDataInsights($categoryName) {
        $insights = [];
        
        // Get basic stats for insights
        $basic = $this->getBasicAnalytics($categoryName);
        
        // Average transaction insight
        if ($basic['average_amount'] > 0) {
            $insights[] = [
                'type' => 'average',
                'message' => "Average transaction amount is GH₵ " . number_format($basic['average_amount'], 2),
                'icon' => 'fas fa-chart-line'
            ];
        }
        
        // Frequency insight
        if ($basic['total_count'] > 0) {
            $daysSinceFirst = (strtotime($basic['last_transaction']) - strtotime($basic['first_transaction'])) / (60 * 60 * 24);
            $avgFrequency = $daysSinceFirst > 0 ? $basic['total_count'] / ($daysSinceFirst / 30) : 0;
            
            $insights[] = [
                'type' => 'frequency',
                'message' => "Average of " . round($avgFrequency, 1) . " transactions per month",
                'icon' => 'fas fa-calendar'
            ];
        }
        
        return $insights;
    }
}
