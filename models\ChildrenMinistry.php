<?php
/**
 * Children's Ministry Model
 * Main model for children's ministry functionality
 */

require_once 'FamilyRelationship.php';
require_once 'ChildrenAgeGroup.php';
require_once 'ChildrenMedicalInfo.php';
require_once 'ChildrenCheckinLog.php';
require_once 'GuardianContact.php';

class ChildrenMinistry {
    // Database connection
    private $conn;
    
    // Related models
    public $familyRelationship;
    public $ageGroup;
    public $medicalInfo;
    public $checkinLog;
    public $guardianContact;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
        
        // Initialize related models
        $this->familyRelationship = new FamilyRelationship($db);
        $this->ageGroup = new ChildrenAgeGroup($db);
        $this->medicalInfo = new ChildrenMedicalInfo($db);
        $this->checkinLog = new ChildrenCheckinLog($db);
        $this->guardianContact = new GuardianContact($db);
    }

    /**
     * Get all children (members under configured age limit)
     *
     * @param bool $active_only
     * @return PDOStatement
     */
    public function getAllChildren($active_only = true) {
        // Include query optimizer with proper path handling
        if (defined('BASE_DIR')) {
            require_once BASE_DIR . '/utils/query_optimizer.php';
        } else {
            require_once __DIR__ . '/../utils/query_optimizer.php';
        }

        $max_age = $this->getMaxChildAge();

        // Use optimized query from QueryOptimizer
        $query = QueryOptimizer::getOptimizedChildrenQuery($max_age, $active_only);

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':max_age', $max_age);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get children's ministry dashboard statistics
     *
     * @return array
     */
    public function getDashboardStats() {
        $max_age = $this->getMaxChildAge();
        
        // Total children count
        $query = "SELECT COUNT(*) as total_children
                  FROM members 
                  WHERE TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) <= :max_age
                  AND member_status = 'active'";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':max_age', $max_age);
        $stmt->execute();
        $total_children = $stmt->fetch(PDO::FETCH_ASSOC)['total_children'];

        // Today's attendance
        $query = "SELECT COUNT(DISTINCT child_id) as todays_attendance
                  FROM children_checkin_log 
                  WHERE attendance_date = CURDATE()";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $todays_attendance = $stmt->fetch(PDO::FETCH_ASSOC)['todays_attendance'];

        // Currently checked in
        $query = "SELECT COUNT(*) as currently_checked_in
                  FROM children_checkin_log 
                  WHERE attendance_date = CURDATE()
                  AND check_out_time IS NULL";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $currently_checked_in = $stmt->fetch(PDO::FETCH_ASSOC)['currently_checked_in'];

        // Children with medical alerts
        $query = "SELECT COUNT(DISTINCT cmi.child_id) as medical_alerts
                  FROM children_medical_info cmi
                  JOIN members m ON cmi.child_id = m.id
                  WHERE (cmi.allergies IS NOT NULL AND cmi.allergies != '')
                     OR (cmi.medical_conditions IS NOT NULL AND cmi.medical_conditions != '')
                     OR (cmi.special_needs IS NOT NULL AND cmi.special_needs != '')
                  AND m.member_status = 'active'
                  AND TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) <= :max_age";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':max_age', $max_age);
        $stmt->execute();
        $medical_alerts = $stmt->fetch(PDO::FETCH_ASSOC)['medical_alerts'];

        // QR Family check-ins today (updated pattern to match actual notes)
        $query = "SELECT COUNT(DISTINCT ccl.child_id) as qr_family_checkins
                  FROM children_checkin_log ccl
                  WHERE ccl.attendance_date = CURDATE()
                  AND (ccl.notes LIKE '%QR Family Attendance%'
                       OR ccl.notes LIKE '%QR family attendance%'
                       OR ccl.notes LIKE '%QR%')";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $qr_family_checkins = $stmt->fetch(PDO::FETCH_ASSOC)['qr_family_checkins'];

        // Age group breakdown
        $age_groups = $this->ageGroup->getChildrenCountByAgeGroup()->fetchAll(PDO::FETCH_ASSOC);

        return [
            'total_children' => $total_children,
            'todays_attendance' => $todays_attendance,
            'currently_checked_in' => $currently_checked_in,
            'medical_alerts' => $medical_alerts,
            'qr_family_checkins' => $qr_family_checkins,
            'age_groups' => $age_groups
        ];
    }

    /**
     * Get recent check-ins
     *
     * @param int $limit
     * @return PDOStatement
     */
    public function getRecentCheckins($limit = 10) {
        $query = "SELECT ccl.*, 
                         m.first_name, m.last_name, m.profile_picture,
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                         s.name as service_name,
                         ci.first_name as checked_in_by_first_name,
                         ci.last_name as checked_in_by_last_name
                  FROM children_checkin_log ccl
                  JOIN members m ON ccl.child_id = m.id
                  JOIN services s ON ccl.service_id = s.id
                  JOIN members ci ON ccl.checked_in_by = ci.id
                  ORDER BY ccl.check_in_time DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get children's services
     *
     * @return PDOStatement
     */
    public function getChildrenServices() {
        $query = "SELECT * FROM services 
                  WHERE name LIKE '%children%' 
                     OR name LIKE '%youth%' 
                     OR name LIKE '%kids%'
                     OR name LIKE '%sunday school%'
                  ORDER BY day_of_week, time";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }



    /**
     * Search children
     *
     * @param string $search_term
     * @return PDOStatement
     */
    public function searchChildren($search_term) {
        $max_age = $this->getMaxChildAge();
        
        $query = "SELECT m.*,
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                         ag.name as age_group_name
                  FROM members m
                  LEFT JOIN children_age_groups ag ON (
                      TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) BETWEEN ag.min_age AND ag.max_age
                      AND ag.is_active = 1
                  )
                  WHERE TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) <= :max_age
                  AND m.member_status = 'active'
                  AND (m.first_name LIKE :search
                       OR m.last_name LIKE :search
                       OR CONCAT(m.first_name, ' ', m.last_name) LIKE :search)
                  ORDER BY m.created_at DESC, m.last_name, m.first_name";

        $stmt = $this->conn->prepare($query);
        $search_param = "%{$search_term}%";
        $stmt->bindParam(':max_age', $max_age);
        $stmt->bindParam(':search', $search_param);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get family information for a child
     *
     * @param int $child_id
     * @return array
     */
    public function getChildFamilyInfo($child_id) {
        // Get child information
        $query = "SELECT m.*, 
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                         ag.name as age_group_name
                  FROM members m
                  LEFT JOIN children_age_groups ag ON (
                      TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) BETWEEN ag.min_age AND ag.max_age
                      AND ag.is_active = 1
                  )
                  WHERE m.id = :child_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();
        $child = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$child) {
            return null;
        }

        // Get parents/guardians from family_relationships (church members)
        $parents = $this->familyRelationship->getParentsByChild($child_id)->fetchAll(PDO::FETCH_ASSOC);

        // Get guardian contacts (non-member guardians)
        $guardians = $this->guardianContact->getByChildId($child_id)->fetchAll(PDO::FETCH_ASSOC);

        // Combine parents and guardians for unified display
        $all_guardians = array_merge($parents, $guardians);

        // Get medical information
        $medical_info = $this->medicalInfo->getByChildId($child_id);

        // Get recent attendance
        $attendance = $this->checkinLog->getChildAttendanceHistory($child_id, 5)->fetchAll(PDO::FETCH_ASSOC);

        return [
            'child' => $child,
            'parents' => $parents, // Church member parents
            'guardians' => $guardians, // Non-member guardians
            'all_guardians' => $all_guardians, // Combined list
            'medical_info' => $medical_info,
            'recent_attendance' => $attendance
        ];
    }

    /**
     * Get maximum child age from settings
     *
     * @return int
     */
    public function getMaxChildAge() {
        $query = "SELECT setting_value FROM children_ministry_settings 
                  WHERE setting_key = 'max_child_age'";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? (int)$result['setting_value'] : 17;
    }

    /**
     * Update children's ministry setting
     *
     * @param string $key
     * @param string $value
     * @return bool
     */
    public function updateSetting($key, $value) {
        $query = "INSERT INTO children_ministry_settings (setting_key, setting_value, created_at, updated_at)
                  VALUES (:key, :value, NOW(), NOW())
                  ON DUPLICATE KEY UPDATE 
                  setting_value = :value, updated_at = NOW()";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':key', $key);
        $stmt->bindParam(':value', $value);

        return $stmt->execute();
    }

    /**
     * Get children's ministry setting
     *
     * @param string $key
     * @param string $default
     * @return string
     */
    public function getSetting($key, $default = '') {
        $query = "SELECT setting_value FROM children_ministry_settings 
                  WHERE setting_key = :key";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':key', $key);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? $result['setting_value'] : $default;
    }
}
