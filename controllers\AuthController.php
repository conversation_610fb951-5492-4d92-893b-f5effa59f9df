<?php
/**
 * Authentication Controller
 */

require_once 'models/User.php';
require_once 'utils/validation.php';

class AuthController {
    private $database;
    private $user;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->user = new User($this->database->getConnection());
    }

    /**
     * Login page
     *
     * @return void
     */
    public function loginPage() {
        // If already logged in, redirect to dashboard
        if (isset($_SESSION['user_id'])) {
            redirect('dashboard');
        }

        require_once 'views/auth/login.php';
    }

    /**
     * Process login
     *
     * @return void
     */
    public function login() {
        // If already logged in, redirect to dashboard
        if (isset($_SESSION['user_id'])) {
            redirect('dashboard');
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            if (isset($_POST['csrf_token']) && isset($_SESSION['csrf_token']) && $_POST['csrf_token'] === $_SESSION['csrf_token']) {
                // CSRF token is valid
            } else {
                set_flash_message('Invalid form submission. Please try again.', 'danger');
                redirect('login');
                exit;
            }
            
            // Validate input
            $validation = new Validation($_POST);
            $validation->required('email')->email('email')
                      ->required('password')->min('password', 6);

            if ($validation->fails()) {
                // Set error message and redirect back
                set_flash_message('Please check your inputs and try again.', 'danger');
                redirect('login');
            }

            // Get user by email
            $email = sanitize($_POST['email']);
            $password = $_POST['password'];

            // Try to find user by email
            if ($this->user->getByEmail($email)) {
                // Check if user is blocked
                if (isset($this->user->status) && $this->user->status === 'blocked') {
                    set_flash_message('Your account has been blocked. Please contact the administrator.', 'danger');
                    redirect('login');
                    exit;
                }

                // Verify password
                if ($this->user->verifyPassword($password)) {
                    // Set session variables
                    $_SESSION['user_id'] = $this->user->id;
                    $_SESSION['user_name'] = $this->user->username;
                    $_SESSION['user_email'] = $this->user->email;
                    $_SESSION['user_role'] = $this->user->role;
                    $_SESSION['user_status'] = $this->user->status ?? 'active';

                    // Redirect to dashboard
                    redirect('dashboard');
                } else {
                    // Invalid password
                    set_flash_message('Invalid email or password.', 'danger');
                    redirect('login');
                }
            } else {
                // User not found
                set_flash_message('Invalid email or password.', 'danger');
                redirect('login');
            }
        } else {
            // Not a POST request, redirect to login page
            redirect('login');
        }
    }

    /**
     * Process logout
     *
     * @return void
     */
    public function logout() {
        // Unset all session variables
        $_SESSION = array();

        // Destroy the session
        session_destroy();

        // Redirect to login page
        redirect('login');
    }

    /**
     * Forgot password page
     *
     * @return void
     */
    public function forgotPasswordPage() {
        // If already logged in, redirect to dashboard
        if (isset($_SESSION['user_id'])) {
            redirect('dashboard');
        }

        require_once 'views/auth/forgot_password.php';
    }

    /**
     * Process forgot password
     *
     * @return void
     */
    public function forgotPassword() {
        // If already logged in, redirect to dashboard
        if (isset($_SESSION['user_id'])) {
            redirect('dashboard');
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate input
            $validation = new Validation($_POST);
            $validation->required('email')->email('email');

            if ($validation->fails()) {
                // Set error message and redirect back
                set_flash_message('Please enter a valid email address.', 'danger');
                redirect('forgot-password');
            }

            // Get user by email
            $email = sanitize($_POST['email']);

            if ($this->user->getByEmail($email)) {
                // Generate reset token
                $token = generate_random_string(32);
                $expiry = date('Y-m-d H:i:s', strtotime('+1 hour'));

                // Save token to database
                // This would typically be done in a password_resets table
                // For simplicity, we'll just show a success message

                // Send reset email
                // This would typically use a mail library
                // For simplicity, we'll just show a success message

                set_flash_message('Password reset instructions have been sent to your email.', 'success');
                redirect('login');
            } else {
                // User not found - for security, don't reveal this
                set_flash_message('Password reset instructions have been sent to your email if the account exists.', 'info');
                redirect('login');
            }
        } else {
            // Not a POST request, redirect to forgot password page
            redirect('forgot-password');
        }
    }

    /**
     * Reset password page
     *
     * @return void
     */
    public function resetPasswordPage() {
        // If already logged in, redirect to dashboard
        if (isset($_SESSION['user_id'])) {
            redirect('dashboard');
        }

        // Check if token is valid
        // This would typically validate against a password_resets table
        // For simplicity, we'll just show the reset form

        require_once 'views/auth/reset_password.php';
    }

    /**
     * Process reset password
     *
     * @return void
     */
    public function resetPassword() {
        // If already logged in, redirect to dashboard
        if (isset($_SESSION['user_id'])) {
            redirect('dashboard');
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate input
            $validation = new Validation($_POST);
            $validation->required('password')->min('password', 6)
                      ->required('password_confirmation')->matches('password_confirmation', 'password');

            if ($validation->fails()) {
                // Set error message and redirect back
                set_flash_message('Please check your inputs and try again.', 'danger');
                redirect('reset-password?token=' . $_POST['token']);
            }

            // Validate token
            // This would typically validate against a password_resets table
            // For simplicity, we'll just show a success message

            // Update password
            // This would typically update the user's password
            // For simplicity, we'll just show a success message

            set_flash_message('Your password has been reset successfully. You can now login with your new password.', 'success');
            redirect('login');
        } else {
            // Not a POST request, redirect to reset password page
            redirect('reset-password?token=' . $_GET['token']);
        }
    }
}
