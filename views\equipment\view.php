<div class="container mx-auto px-4 py-6 max-w-6xl">
    <!-- Equipment Header with Hero Section -->
    <div class="bg-gradient-to-r from-gray-50 to-white rounded-xl shadow-lg border border-gray-100 mb-8 overflow-hidden">
        <div class="p-6 md:p-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                <div class="flex items-center">
                    <div class="bg-gradient-to-r from-primary to-primary-light p-3 rounded-full shadow-lg mr-4">
                        <i class="fas fa-desktop text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mb-1"><?php echo $this->equipment->name; ?></h1>
                        <p class="text-gray-600"><?php echo ucfirst($this->equipment->category); ?> • ID #<?php echo $this->equipment->id; ?></p>
                    </div>
                </div>
                <div class="flex flex-wrap gap-2">
                    <a href="<?php echo BASE_URL; ?>equipment" class="bg-white border border-gray-300 hover:bg-gray-100 text-gray-700 py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-arrow-left mr-2 text-primary"></i> Back
                    </a>
                    <a href="<?php echo BASE_URL; ?>equipment/edit?id=<?php echo $this->equipment->id; ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-edit mr-2"></i> Edit
                    </a>
                    <a href="<?php echo BASE_URL; ?>equipment/qrcode?id=<?php echo $this->equipment->id; ?>" class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-qrcode mr-2"></i> QR Code
                    </a>
                </div>
            </div>

            <!-- Equipment Status Badge -->
            <div class="flex items-center mb-2">
                <span class="text-sm text-gray-600 mr-2">Status:</span>
                <?php if ($this->equipment->status === 'excellent') : ?>
                    <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full bg-green-100 text-green-800 border border-green-200">
                        <i class="fas fa-check-circle mr-1"></i> Excellent
                    </span>
                <?php elseif ($this->equipment->status === 'good') : ?>
                    <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                        <i class="fas fa-thumbs-up mr-1"></i> Good
                    </span>
                <?php elseif ($this->equipment->status === 'fair') : ?>
                    <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200">
                        <i class="fas fa-exclamation-circle mr-1"></i> Fair
                    </span>
                <?php else : ?>
                    <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full bg-red-100 text-red-800 border border-red-200">
                        <i class="fas fa-exclamation-triangle mr-1"></i> Poor
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Equipment Details -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Equipment Info Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 col-span-2 transition-all duration-300 hover:shadow-xl">
            <h2 class="text-lg font-semibold text-gray-800 mb-6 flex items-center border-b border-gray-100 pb-3">
                <div class="bg-blue-100 text-blue-600 p-2 rounded-full mr-3">
                    <i class="fas fa-info-circle"></i>
                </div>
                Equipment Information
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                    <div class="flex items-center mb-1 text-gray-500">
                        <i class="fas fa-tag mr-2"></i>
                        <p class="text-sm">Name</p>
                    </div>
                    <p class="font-medium text-gray-800"><?php echo $this->equipment->name; ?></p>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                    <div class="flex items-center mb-1 text-gray-500">
                        <i class="fas fa-th-large mr-2"></i>
                        <p class="text-sm">Category</p>
                    </div>
                    <p class="font-medium text-gray-800"><?php echo ucfirst($this->equipment->category); ?></p>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                    <div class="flex items-center mb-1 text-gray-500">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        <p class="text-sm">Location</p>
                    </div>
                    <p class="font-medium text-gray-800"><?php echo $this->equipment->location ?: 'N/A'; ?></p>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                    <div class="flex items-center mb-1 text-gray-500">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <p class="text-sm">Purchase Date</p>
                    </div>
                    <p class="font-medium text-gray-800"><?php echo $this->equipment->purchase_date ? format_date($this->equipment->purchase_date) : 'N/A'; ?></p>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                    <div class="flex items-center mb-1 text-gray-500">
                        <i class="fas fa-money-bill-wave mr-2"></i>
                        <p class="text-sm">Purchase Price</p>
                    </div>
                    <p class="font-medium text-gray-800"><?php echo $this->equipment->purchase_price ? 'GH₵ ' . number_format($this->equipment->purchase_price, 2) : 'N/A'; ?></p>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-100">
                    <div class="flex items-center mb-1 text-gray-500">
                        <i class="fas fa-clock mr-2"></i>
                        <p class="text-sm">Age</p>
                    </div>
                    <p class="font-medium text-gray-800">
                        <?php
                        if ($this->equipment->purchase_date) {
                            $purchase_date = new DateTime($this->equipment->purchase_date);
                            $now = new DateTime();
                            $diff = $purchase_date->diff($now);
                            echo $diff->y > 0 ? $diff->y . ' year' . ($diff->y > 1 ? 's' : '') : $diff->m . ' month' . ($diff->m > 1 ? 's' : '');
                        } else {
                            echo 'N/A';
                        }
                        ?>
                    </p>
                </div>
            </div>
            <?php if ($this->equipment->description) : ?>
                <div class="mt-6 bg-gray-50 rounded-lg p-4 border border-gray-100">
                    <div class="flex items-center mb-2 text-gray-500">
                        <i class="fas fa-align-left mr-2"></i>
                        <p class="text-sm">Description</p>
                    </div>
                    <p class="text-gray-800"><?php echo $this->equipment->description; ?></p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Equipment Stats Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-xl">
            <div class="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-100">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                    <div class="bg-purple-100 text-purple-600 p-2 rounded-full mr-3">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    Equipment Stats
                </h2>
            </div>
            <div class="p-6">
                <div class="space-y-5">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="bg-blue-50 rounded-lg p-4 border border-blue-100 text-center">
                            <p class="text-xs text-blue-600 uppercase font-semibold mb-1">Records</p>
                            <p class="text-2xl font-bold text-blue-700"><?php echo count($maintenance_records); ?></p>
                        </div>
                        <div class="bg-green-50 rounded-lg p-4 border border-green-100 text-center">
                            <p class="text-xs text-green-600 uppercase font-semibold mb-1">Total Cost</p>
                            <p class="text-2xl font-bold text-green-700">GH₵ <?php echo number_format($total_maintenance_cost, 2); ?></p>
                        </div>
                    </div>

                    <!-- Timeline -->
                    <div class="border-l-2 border-gray-200 pl-4 ml-2 space-y-4">
                        <div class="relative">
                            <div class="absolute -left-6 mt-1 w-4 h-4 rounded-full bg-primary"></div>
                            <p class="text-sm text-gray-600">Added to Inventory</p>
                            <p class="font-medium"><?php echo format_date($this->equipment->created_at); ?></p>
                        </div>
                        <div class="relative">
                            <div class="absolute -left-6 mt-1 w-4 h-4 rounded-full bg-indigo-500"></div>
                            <p class="text-sm text-gray-600">Last Updated</p>
                            <p class="font-medium"><?php echo format_date($this->equipment->updated_at); ?></p>
                        </div>
                        <?php if (!empty($maintenance_records)) : ?>
                        <div class="relative">
                            <div class="absolute -left-6 mt-1 w-4 h-4 rounded-full bg-yellow-500"></div>
                            <p class="text-sm text-gray-600">Last Maintenance</p>
                            <p class="font-medium"><?php echo format_date($maintenance_records[0]['maintenance_date']); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Action Button -->
                    <div class="pt-4 border-t border-gray-200 mt-4">
                        <a href="<?php echo BASE_URL; ?>equipment/maintenance/add?equipment_id=<?php echo $this->equipment->id; ?>" class="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-white py-3 px-4 rounded-lg flex items-center justify-center w-full shadow-sm hover:shadow-md transition-all duration-300">
                            <i class="fas fa-tools mr-2"></i> Add Maintenance Record
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance History -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden mb-8 transition-all duration-300 hover:shadow-xl">
        <div class="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-100 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <div class="bg-yellow-100 text-yellow-600 p-2 rounded-full mr-3">
                    <i class="fas fa-history"></i>
                </div>
                Maintenance History
            </h2>
            <a href="<?php echo BASE_URL; ?>equipment/maintenance/add?equipment_id=<?php echo $this->equipment->id; ?>&redirect_to_equipment=1" class="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-white py-2 px-4 rounded-lg flex items-center text-sm shadow-sm hover:shadow transition-all duration-300">
                <i class="fas fa-plus-circle mr-2"></i> Add Record
            </a>
        </div>

        <?php if (empty($maintenance_records)) : ?>
            <div class="p-8 text-center">
                <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-tools text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-gray-500 font-medium mb-2">No Maintenance Records</h3>
                <p class="text-gray-400 text-sm mb-6">This equipment doesn't have any maintenance records yet.</p>
                <a href="<?php echo BASE_URL; ?>equipment/maintenance/add?equipment_id=<?php echo $this->equipment->id; ?>&redirect_to_equipment=1" class="text-primary hover:text-primary-dark inline-flex items-center">
                    <i class="fas fa-plus-circle mr-2"></i> Add the first maintenance record
                </a>
            </div>
        <?php else : ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                            <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performed By</th>
                            <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($maintenance_records as $index => $record) : ?>
                            <tr class="<?php echo $index % 2 === 0 ? 'bg-white' : 'bg-gray-50'; ?> hover:bg-blue-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-calendar-day text-blue-600"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900"><?php echo format_date($record['maintenance_date']); ?></div>
                                            <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($record['created_at'])); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 line-clamp-2"><?php echo $record['description']; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium <?php echo $record['cost'] ? 'text-green-600' : 'text-gray-500'; ?>">
                                        <?php echo $record['cost'] ? 'GH₵ ' . number_format($record['cost'], 2) : 'N/A'; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $record['performed_by'] ?: 'N/A'; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-3">
                                        <a href="<?php echo BASE_URL; ?>equipment/maintenance/edit?id=<?php echo $record['id']; ?>&redirect_to_equipment=1" class="text-indigo-600 hover:text-indigo-900 hover:scale-110 transition-transform duration-150" title="Edit Record">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="javascript:void(0)" onclick="confirmDeleteMaintenance(<?php echo $record['id']; ?>)" class="text-red-600 hover:text-red-900 hover:scale-110 transition-transform duration-150" title="Delete Record">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Maintenance Confirmation Modal -->
<div id="deleteMaintenanceModal" class="fixed inset-0 bg-black bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 flex items-center justify-center">
    <div class="relative mx-auto p-6 border w-96 shadow-xl rounded-xl bg-white transform transition-all opacity-0 scale-95" id="deleteMaintenanceModalContent">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-xl leading-6 font-bold text-gray-900 mb-2">Delete Maintenance Record</h3>
            <div class="mb-6">
                <p class="text-gray-600">Are you sure you want to delete this maintenance record? This action cannot be undone.</p>
            </div>
            <div class="flex justify-center space-x-4">
                <button id="cancelDeleteMaintenance" class="bg-white border border-gray-300 px-5 py-2.5 rounded-lg text-gray-700 font-medium hover:bg-gray-100 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    <i class="fas fa-times mr-2"></i> Cancel
                </button>
                <a id="confirmDeleteMaintenance" href="#" class="bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 px-5 py-2.5 rounded-lg text-white font-medium shadow-sm hover:shadow transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <i class="fas fa-trash-alt mr-2"></i> Delete
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Delete maintenance confirmation with animation
    function confirmDeleteMaintenance(id) {
        const modal = document.getElementById('deleteMaintenanceModal');
        const modalContent = document.getElementById('deleteMaintenanceModalContent');
        const confirmBtn = document.getElementById('confirmDeleteMaintenance');
        const cancelBtn = document.getElementById('cancelDeleteMaintenance');

        // Show modal
        modal.classList.remove('hidden');

        // Animate in
        setTimeout(() => {
            modalContent.classList.add('opacity-100', 'scale-100');
            modalContent.classList.remove('opacity-0', 'scale-95');
        }, 10);

        // Set delete URL
        confirmBtn.href = '<?php echo BASE_URL; ?>equipment/maintenance/delete?id=' + id + '&redirect=equipment';

        // Handle cancel button
        const closeModal = () => {
            // Animate out
            modalContent.classList.remove('opacity-100', 'scale-100');
            modalContent.classList.add('opacity-0', 'scale-95');

            // Hide modal after animation
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        };

        // Remove existing event listeners to prevent duplicates
        const newCancelBtn = cancelBtn.cloneNode(true);
        cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);
        newCancelBtn.addEventListener('click', closeModal);

        // Close when clicking outside the modal
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        // Close on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    }

    // Add animation to the page elements on load
    document.addEventListener('DOMContentLoaded', function() {
        const elements = document.querySelectorAll('.bg-white.rounded-xl');
        elements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 100 + (index * 150));
        });
    });
</script>
