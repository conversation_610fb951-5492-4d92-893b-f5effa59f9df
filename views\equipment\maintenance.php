<div class="container mx-auto px-4 py-6 max-w-6xl">
    <!-- Hero Banner -->
    <div class="bg-gradient-to-r from-primary to-primary-light rounded-lg shadow-md p-6 mb-8 text-white">
        <div class="flex flex-col md:flex-row items-center justify-between">
            <div class="mb-6 md:mb-0 md:mr-6">
                <div class="flex items-center mb-3">
                    <div class="bg-white p-2 rounded-full shadow-md mr-3">
                        <i class="fas fa-tools text-primary text-xl"></i>
                    </div>
                    <h1 class="text-2xl md:text-3xl font-bold">Equipment Maintenance</h1>
                </div>
                <p class="opacity-90 mb-4">Track and manage maintenance records for all church equipment</p>
                <div class="flex flex-wrap gap-3">
                    <a href="<?php echo BASE_URL; ?>equipment" class="bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-md flex items-center text-sm font-medium transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Equipment
                    </a>
                </div>
            </div>
            <div class="flex-shrink-0">
                <a href="<?php echo BASE_URL; ?>equipment/maintenance/add" class="bg-white text-primary hover:bg-gray-100 py-3 px-6 rounded-md flex items-center text-sm font-medium transition-all duration-300 shadow-md hover:shadow-lg">
                    <i class="fas fa-plus-circle mr-2"></i> Add Maintenance Record
                </a>
            </div>
        </div>
    </div>

    <!-- Maintenance Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <!-- Total Records Card -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md p-4 text-white transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium opacity-75">Total Records</p>
                    <h3 class="text-2xl font-bold"><?php echo $stats['total_records']; ?></h3>
                </div>
                <div class="bg-white bg-opacity-30 rounded-full p-3 shadow-inner">
                    <i class="fas fa-tools text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Cost Card -->
        <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg shadow-md p-4 text-white transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium opacity-75">Total Cost</p>
                    <h3 class="text-2xl font-bold">GH₵ <?php echo number_format($stats['total_cost'], 2); ?></h3>
                </div>
                <div class="bg-white bg-opacity-30 rounded-full p-3 shadow-inner">
                    <i class="fas fa-money-bill-wave text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Recent Maintenance Card -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md p-4 text-white transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium opacity-75">Recent Maintenance</p>
                    <h3 class="text-2xl font-bold"><?php echo $stats['recent_maintenance']; ?></h3>
                    <p class="text-xs opacity-75">in the last 30 days</p>
                </div>
                <div class="bg-white bg-opacity-30 rounded-full p-3 shadow-inner">
                    <i class="fas fa-calendar-check text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Average Cost Card -->
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-md p-4 text-white transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium opacity-75">Avg. Cost per Record</p>
                    <h3 class="text-2xl font-bold">GH₵ <?php echo $stats['total_records'] > 0 ? number_format($stats['total_cost'] / $stats['total_records'], 2) : '0.00'; ?></h3>
                </div>
                <div class="bg-white bg-opacity-30 rounded-full p-3 shadow-inner">
                    <i class="fas fa-chart-line text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance Cost Chart -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8 border border-gray-100 transition-all duration-300 hover:shadow-lg">
        <div class="p-4 bg-gradient-to-r from-gray-50 to-white border-b flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full mr-3">
                    <i class="fas fa-chart-bar"></i>
                </div>
                Maintenance Cost by Month (<?php echo date('Y'); ?>)
            </h2>
        </div>
        <div class="p-6">
            <div class="h-72">
                <canvas id="maintenanceChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Maintenance Records Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 transition-all duration-300 hover:shadow-lg">
        <div class="p-4 bg-gradient-to-r from-gray-50 to-white border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <div class="bg-blue-100 text-blue-600 p-2 rounded-full mr-3">
                    <i class="fas fa-history"></i>
                </div>
                Maintenance Records
            </h2>
            <div class="flex flex-wrap gap-2">
                <a href="<?php echo BASE_URL; ?>equipment/maintenance/add" class="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-white py-2 px-4 rounded-md flex items-center text-sm shadow-sm hover:shadow transition-all duration-300">
                    <i class="fas fa-plus-circle mr-2"></i> Add Record
                </a>
                <a href="<?php echo BASE_URL; ?>equipment/reports" class="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white py-2 px-4 rounded-md flex items-center text-sm shadow-sm hover:shadow transition-all duration-300">
                    <i class="fas fa-file-alt mr-2"></i> Generate Report
                </a>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr class="bg-gradient-to-r from-gray-50 to-white">
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Equipment</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Description</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Cost</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Performed By</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($maintenance_records)) : ?>
                        <tr>
                            <td colspan="6" class="px-6 py-8 text-center text-sm text-gray-500">
                                <div class="flex flex-col items-center justify-center space-y-3">
                                    <div class="bg-gray-100 rounded-full p-3">
                                        <i class="fas fa-tools text-gray-400 text-xl"></i>
                                    </div>
                                    <p>No maintenance records found</p>
                                    <a href="<?php echo BASE_URL; ?>equipment/maintenance/add" class="text-primary hover:text-primary-dark text-sm flex items-center">
                                        <i class="fas fa-plus-circle mr-1"></i> Add your first record
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ($maintenance_records as $record) : ?>
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-tools text-primary"></i>
                                        </div>
                                        <div>
                                            <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $record['equipment_id']; ?>" class="text-sm font-medium text-gray-900 hover:text-primary">
                                                <?php echo $record['equipment_name']; ?>
                                            </a>
                                            <div class="text-xs text-gray-500 mt-0.5"><?php echo isset($record['equipment_category']) ? ucfirst($record['equipment_category']) : ''; ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo format_date($record['maintenance_date']); ?></div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 max-w-xs truncate"><?php echo $record['description']; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $record['cost'] ? 'GH₵ ' . number_format($record['cost'], 2) : 'N/A'; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $record['performed_by'] ?: 'N/A'; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="<?php echo BASE_URL; ?>equipment/maintenance/edit?id=<?php echo $record['id']; ?>" class="text-indigo-600 hover:text-indigo-900 bg-indigo-50 hover:bg-indigo-100 p-1.5 rounded-full inline-block transition-colors duration-150" title="Edit Record">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $record['id']; ?>)" class="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 p-1.5 rounded-full inline-block transition-colors duration-150" title="Delete Record">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm hidden overflow-y-auto h-full w-full z-50 transition-all duration-300">
    <div class="relative top-20 mx-auto p-6 border border-gray-200 w-96 shadow-2xl rounded-xl bg-white transform transition-all duration-300 scale-95 opacity-0">
        <div class="mt-3">
            <div class="mx-auto flex items-center justify-center h-14 w-14 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 text-center">Delete Maintenance Record</h3>
            <div class="mt-4 px-1 py-3">
                <p class="text-sm text-gray-500 text-center">Are you sure you want to delete this maintenance record? This action cannot be undone.</p>
            </div>
            <div class="flex justify-center gap-3 mt-5">
                <button id="cancelDelete" class="bg-gray-100 hover:bg-gray-200 px-5 py-2.5 rounded-lg text-gray-800 font-medium transition-all duration-300 border border-gray-200">
                    <i class="fas fa-times mr-2"></i> Cancel
                </button>
                <a id="confirmDelete" href="#" class="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 px-5 py-2.5 rounded-lg text-white font-medium transition-all duration-300 shadow-sm hover:shadow">
                    <i class="fas fa-trash-alt mr-2"></i> Delete
                </a>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Delete confirmation with animation
    function confirmDelete(id) {
        const modal = document.getElementById('deleteModal');
        const modalContent = modal.querySelector('.relative');
        const confirmBtn = document.getElementById('confirmDelete');

        // Show modal with animation
        modal.classList.remove('hidden');
        setTimeout(() => {
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);

        // Set delete URL
        confirmBtn.href = '<?php echo BASE_URL; ?>equipment/maintenance/delete?id=' + id;

        // Handle cancel button
        document.getElementById('cancelDelete').onclick = function() {
            // Hide with animation
            modalContent.classList.remove('scale-100', 'opacity-100');
            modalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        };

        // Close when clicking outside
        modal.onclick = function(e) {
            if (e.target === modal) {
                document.getElementById('cancelDelete').click();
            }
        };
    }

    // Maintenance cost chart with enhanced styling
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('maintenanceChart').getContext('2d');
        const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        const data = <?php echo json_encode($chart_data); ?>;

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(63, 125, 88, 0.8)');
        gradient.addColorStop(1, 'rgba(63, 125, 88, 0.2)');

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: months,
                datasets: [{
                    label: 'Maintenance Cost (GH₵)',
                    data: data,
                    backgroundColor: gradient,
                    borderColor: '#3F7D58',
                    borderWidth: 1,
                    borderRadius: 4,
                    hoverBackgroundColor: '#3F7D58'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: '"Inter", sans-serif',
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        padding: 10,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return 'GH₵ ' + context.parsed.y.toFixed(2);
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                family: '"Inter", sans-serif'
                            },
                            callback: function(value) {
                                return 'GH₵ ' + value;
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: '"Inter", sans-serif'
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    });
</script>
