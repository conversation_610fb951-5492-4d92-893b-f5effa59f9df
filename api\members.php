<?php
/**
 * Members API
 *
 * This file handles API requests for members data
 */

// Include necessary files
require_once '../config/database.php';
require_once '../models/Member.php';
require_once '../helpers/functions.php';

// Set headers for JSON response
header('Content-Type: application/json');

// CRITICAL SECURITY FIX: Restrict CORS to application domain only
// The wildcard '*' is dangerous and allows any website to access this API
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false || 
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
    // If origin is not allowed, we don't set the CORS header
    // This will cause the browser to block the request
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET');

// Initialize database and member model
$database = new Database();
$member = new Member($database->getConnection());

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle GET requests
if ($method === 'GET') {
    try {
        // Get pagination parameters
        $page = isset($_GET['page']) ? (int)sanitize($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? (int)sanitize($_GET['limit']) : 100;

        // Ensure valid pagination values
        $page = max(1, $page); // Minimum page is 1
        $limit = min(500, max(10, $limit)); // Limit between 10 and 500

        // Calculate offset
        $offset = ($page - 1) * $limit;

        // Initialize filters array
        $filters = [];

    // Add search filter if provided
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $filters['search'] = sanitize($_GET['search']);
    }

    // Add department filter if provided
    if (isset($_GET['department']) && !empty($_GET['department'])) {
        $filters['department'] = sanitize($_GET['department']);
    }

    // Add status filter if provided
    if (isset($_GET['status']) && !empty($_GET['status'])) {
        $filters['status'] = sanitize($_GET['status']);
    }

    // Get filtered and paginated members
    $stmt = $member->getFiltered($filters, $limit, $offset);
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get total count for pagination
    $totalCount = $member->getFilteredCount($filters);

    // Prepare response with pagination metadata
    $response = [
        'data' => $members,
        'total' => $totalCount,
        'page' => $page,
        'limit' => $limit,
        'pages' => ceil($totalCount / $limit)
    ];

        echo json_encode($response);
        exit;

    } catch (Exception $e) {
        error_log("Members API error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
        exit;
    }
}

// If not a GET request, return error
http_response_code(405); // Method Not Allowed
echo json_encode(['error' => 'Method not allowed']);
