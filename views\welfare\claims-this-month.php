<?php
$page_title = "Members Who Claimed This Month";
$active_page = "welfare";
?>

<div class="max-w-7xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Members Who Claimed This Month</h1>
            <p class="text-gray-600 mt-1"><?php echo date('F Y'); ?> • <?php echo $pagination['total_count']; ?> total claims</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>welfare"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Welfare
            </a>
            <a href="<?php echo BASE_URL; ?>welfare/claim"
               class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                <i class="fas fa-hand-holding-heart mr-2"></i>
                Record Claim
            </a>
        </div>
    </div>

    <!-- Claims List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900">All Claims for <?php echo date('F Y'); ?></h2>
                <div class="text-sm text-gray-600">
                    Showing <?php echo count($claims); ?> of <?php echo $pagination['total_count']; ?> claims
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <?php if (empty($claims)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-hand-holding-heart text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Claims Found</h3>
                    <p class="text-gray-500 mb-6">No welfare claims have been recorded for this month.</p>
                    <a href="<?php echo BASE_URL; ?>welfare/claim" 
                       class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Record First Claim
                    </a>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php foreach ($claims as $claim): ?>
                        <div class="bg-orange-50 rounded-lg p-4 hover:bg-orange-100 transition-colors border border-orange-200">
                            <div class="flex items-start justify-between">
                                <div class="flex items-center flex-1">
                                    <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-hand-holding-heart text-orange-600"></i>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <p class="font-medium text-gray-900">
                                            <?php echo htmlspecialchars($claim['first_name'] . ' ' . $claim['last_name']); ?>
                                        </p>
                                        <p class="text-sm text-gray-600">
                                            <?php echo $claim['phone_number']; ?>
                                        </p>
                                        <p class="text-xs text-gray-500 mt-1">
                                            <?php echo date('M d, Y', strtotime($claim['claim_date'])); ?>
                                        </p>
                                        <?php if (!empty($claim['claim_reason'])): ?>
                                            <p class="text-xs text-gray-600 mt-1 italic">
                                                "<?php echo htmlspecialchars(substr($claim['claim_reason'], 0, 50)); ?><?php echo strlen($claim['claim_reason']) > 50 ? '...' : ''; ?>"
                                            </p>
                                        <?php endif; ?>
                                        <div class="mt-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                <?php
                                                switch($claim['status']) {
                                                    case 'disbursed': echo 'bg-green-100 text-green-800'; break;
                                                    case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                    case 'approved': echo 'bg-blue-100 text-blue-800'; break;
                                                    case 'rejected': echo 'bg-red-100 text-red-800'; break;
                                                    default: echo 'bg-gray-100 text-gray-800';
                                                }
                                                ?>">
                                                <?php echo ucfirst($claim['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right ml-3">
                                    <p class="font-bold text-orange-700 text-lg">₵<?php echo number_format($claim['claim_amount'], 2); ?></p>

                                    <!-- View History Button -->
                                    <div class="mt-2 mb-2">
                                        <a href="<?php echo BASE_URL; ?>welfare/history/<?php echo $claim['member_id']; ?>"
                                           class="inline-flex items-center px-3 py-1 bg-orange-50 hover:bg-orange-100 text-orange-700 rounded-lg text-xs font-medium transition-colors border border-orange-200">
                                            <i class="fas fa-history mr-1"></i>
                                            View History
                                        </a>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex space-x-1">
                                        <a href="<?php echo BASE_URL; ?>welfare/edit-claim/<?php echo $claim['claim_id']; ?>"
                                           class="text-blue-600 hover:text-blue-800 p-1 rounded transition-colors"
                                           title="Edit Claim">
                                            <i class="fas fa-edit text-sm"></i>
                                        </a>
                                        <button onclick="deleteClaim(<?php echo $claim['claim_id']; ?>, '<?php echo htmlspecialchars($claim['first_name'] . ' ' . $claim['last_name']); ?>')"
                                                class="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
                                                title="Delete Claim">
                                            <i class="fas fa-trash text-sm"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <div class="mt-8 flex justify-center">
                        <nav class="flex items-center space-x-2">
                            <?php if ($pagination['has_prev']): ?>
                                <a href="?page=<?php echo $pagination['current_page'] - 1; ?>" 
                                   class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>" 
                                   class="px-3 py-2 <?php echo $i == $pagination['current_page'] ? 'bg-orange-500 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'; ?> rounded-lg transition-colors">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <a href="?page=<?php echo $pagination['current_page'] + 1; ?>" 
                                   class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                    
                    <div class="mt-4 text-center text-sm text-gray-600">
                        Page <?php echo $pagination['current_page']; ?> of <?php echo $pagination['total_pages']; ?> 
                        (<?php echo $pagination['total_count']; ?> total claims)
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function deleteClaim(claimId, memberName) {
    if (confirm(`Are you sure you want to delete the claim for ${memberName}?`)) {
        // Add your delete claim logic here
        window.location.href = `<?php echo BASE_URL; ?>welfare/delete-claim/${claimId}`;
    }
}
</script>
