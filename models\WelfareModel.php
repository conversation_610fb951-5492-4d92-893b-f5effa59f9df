<?php
require_once 'config/database.php';

class WelfareModel {
    private $conn;

    // Error handling
    public $error;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Get database connection
    public function getConnection() {
        return $this->conn;
    }

    // Get welfare statistics for dashboard
    public function getWelfareStats() {
        try {
            $stats = [];

            // Total monthly dues collected this year
            $sql = "SELECT COALESCE(SUM(amount), 0) as total_collected
                    FROM welfare_payments
                    WHERE YEAR(payment_date) = YEAR(CURDATE())
                    AND is_monthly_welfare = 1
                    AND status = 'disbursed'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['total_collected'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_collected'];

            // Monthly dues collected this month
            $sql = "SELECT COALESCE(SUM(amount), 0) as monthly_collected
                    FROM welfare_payments
                    WHERE YEAR(payment_date) = YEAR(CURDATE())
                    AND MONTH(payment_date) = MONTH(CURDATE())
                    AND is_monthly_welfare = 1
                    AND status = 'disbursed'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['monthly_collected'] = $stmt->fetch(PDO::FETCH_ASSOC)['monthly_collected'];

            // Members who paid this month
            $sql = "SELECT COUNT(DISTINCT member_id) as members_paid
                    FROM welfare_payments
                    WHERE YEAR(payment_date) = YEAR(CURDATE())
                    AND MONTH(payment_date) = MONTH(CURDATE())
                    AND is_monthly_welfare = 1
                    AND status = 'disbursed'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['members_paid'] = $stmt->fetch(PDO::FETCH_ASSOC)['members_paid'];

            // Members who claimed welfare this month
            $sql = "SELECT COUNT(DISTINCT member_id) as members_claimed
                    FROM welfare_claims
                    WHERE claim_month = MONTH(CURDATE())
                    AND claim_year = YEAR(CURDATE())
                    AND status = 'disbursed'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['members_claimed'] = $stmt->fetch(PDO::FETCH_ASSOC)['members_claimed'];

            return $stats;
        } catch (PDOException $e) {
            error_log("Error getting welfare stats: " . $e->getMessage());
            return [
                'total_disbursed' => 0,
                'monthly_disbursed' => 0,
                'members_helped' => 0,
                'pending_payments' => 0
            ];
        }
    }

    // Get recent welfare payments
    public function getRecentPayments($limit = 10) {
        try {
            $sql = "SELECT wp.*,
                           CONCAT(m.first_name, ' ', m.last_name) as member_name,
                           wc.name as category_name
                    FROM welfare_payments wp
                    JOIN members m ON wp.member_id = m.id
                    LEFT JOIN welfare_categories wc ON wp.category_id = wc.id
                    ORDER BY wp.payment_date DESC
                    LIMIT :limit";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting recent welfare payments: " . $e->getMessage());
            return [];
        }
    }

    // Get active welfare categories
    public function getActiveCategories() {
        try {
            $sql = "SELECT * FROM welfare_categories WHERE is_active = 1 ORDER BY name";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting active welfare categories: " . $e->getMessage());
            return [];
        }
    }

    // Get all welfare categories (including inactive)
    public function getAllCategories() {
        try {
            $sql = "SELECT wc.*, 
                           COUNT(wp.id) as payment_count,
                           COALESCE(SUM(wp.amount), 0) as total_amount
                    FROM welfare_categories wc
                    LEFT JOIN welfare_payments wp ON wc.id = wp.category_id AND wp.status = 'disbursed'
                    GROUP BY wc.id
                    ORDER BY wc.name";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting all welfare categories: " . $e->getMessage());
            return [];
        }
    }

    // Add new welfare payment
    public function addPayment($data) {
        try {
            $sql = "INSERT INTO welfare_payments 
                    (member_id, category_id, amount, reason, payment_date, payment_method, 
                     reference_number, notes, approved_by, status) 
                    VALUES 
                    (:member_id, :category_id, :amount, :reason, :payment_date, :payment_method, 
                     :reference_number, :notes, :approved_by, 'disbursed')";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':member_id', $data['member_id']);
            $stmt->bindParam(':category_id', $data['category_id']);
            $stmt->bindParam(':amount', $data['amount']);
            $stmt->bindParam(':reason', $data['reason']);
            $stmt->bindParam(':payment_date', $data['payment_date']);
            $stmt->bindParam(':payment_method', $data['payment_method']);
            $stmt->bindParam(':reference_number', $data['reference_number']);
            $stmt->bindParam(':notes', $data['notes']);
            $stmt->bindParam(':approved_by', $data['approved_by']);
            
            $stmt->execute();
            return $this->conn->lastInsertId();
        } catch (PDOException $e) {
            error_log("Error adding welfare payment: " . $e->getMessage());
            throw $e;
        }
    }

    // COMPLETELY REWRITTEN - Get welfare history for a specific member
    public function getMemberPayments($memberId) {
        $transactions = [];

        try {
            // Step 1: Get ALL welfare payments for this member (ultra-simple query)
            $sql = "SELECT id, amount, payment_date, payment_method, reference_number, notes, status, reason, created_at FROM welfare_payments WHERE member_id = " . intval($memberId);
            $result = $this->conn->query($sql);

            if ($result) {
                while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                    $transactions[] = [
                        'id' => $row['id'],
                        'amount' => $row['amount'],
                        'transaction_date' => $row['payment_date'],
                        'payment_method' => $row['payment_method'],
                        'reference_number' => $row['reference_number'],
                        'notes' => $row['notes'],
                        'status' => $row['status'],
                        'is_monthly_welfare' => 1,
                        'payment_month' => null,
                        'payment_year' => null,
                        'category_name' => 'Welfare Payment',
                        'approved_by_name' => 'System',
                        'transaction_type' => 'payment',
                        'reason' => $row['reason'] ?: 'Welfare payment',
                        'created_at' => $row['created_at']
                    ];
                }
            }

            // Step 2: Get ALL welfare claims for this member (ultra-simple query)
            $sql2 = "SELECT id, claim_amount, claim_date, notes, status, claim_reason, created_at FROM welfare_claims WHERE member_id = " . intval($memberId);
            $result2 = $this->conn->query($sql2);

            if ($result2) {
                while ($row = $result2->fetch(PDO::FETCH_ASSOC)) {
                    $transactions[] = [
                        'id' => $row['id'],
                        'amount' => $row['claim_amount'],
                        'transaction_date' => $row['claim_date'],
                        'payment_method' => null,
                        'reference_number' => null,
                        'notes' => $row['notes'],
                        'status' => $row['status'],
                        'is_monthly_welfare' => 0,
                        'payment_month' => null,
                        'payment_year' => null,
                        'category_name' => 'Welfare Assistance Claim',
                        'approved_by_name' => 'System',
                        'transaction_type' => 'claim',
                        'reason' => $row['claim_reason'] ?: 'Welfare assistance',
                        'created_at' => $row['created_at']
                    ];
                }
            }

            // Step 3: Sort by date (newest first)
            usort($transactions, function($a, $b) {
                return strtotime($b['transaction_date']) - strtotime($a['transaction_date']);
            });

            return $transactions;

        } catch (Exception $e) {
            error_log("Error in getMemberPayments: " . $e->getMessage());
            return [];
        }
    }

    // Get welfare statistics for a specific member (both payments and claims)
    public function getMemberStats($memberId) {
        try {
            $stats = [
                'total_paid' => 0,
                'total_claimed' => 0,
                'payment_count' => 0,
                'claim_count' => 0,
                'last_activity' => null
            ];

            // Get payment statistics (all payments regardless of status)
            $sql1 = "SELECT COUNT(*) as payment_count, COALESCE(SUM(amount), 0) as total_paid, MAX(payment_date) as last_payment
                     FROM welfare_payments
                     WHERE member_id = ?";
            $stmt1 = $this->conn->prepare($sql1);
            $stmt1->execute([$memberId]);
            $paymentData = $stmt1->fetch(PDO::FETCH_ASSOC);

            if ($paymentData) {
                $stats['payment_count'] = $paymentData['payment_count'];
                $stats['total_paid'] = $paymentData['total_paid'];
                $stats['last_activity'] = $paymentData['last_payment'];
            }

            // Get claim statistics (all claims regardless of status)
            $sql2 = "SELECT COUNT(*) as claim_count, COALESCE(SUM(claim_amount), 0) as total_claimed, MAX(claim_date) as last_claim
                     FROM welfare_claims
                     WHERE member_id = ?";
            $stmt2 = $this->conn->prepare($sql2);
            $stmt2->execute([$memberId]);
            $claimData = $stmt2->fetch(PDO::FETCH_ASSOC);

            if ($claimData) {
                $stats['claim_count'] = $claimData['claim_count'];
                $stats['total_claimed'] = $claimData['total_claimed'];

                // Determine the latest activity date
                if ($claimData['last_claim'] && $stats['last_activity']) {
                    $stats['last_activity'] = (strtotime($claimData['last_claim']) > strtotime($stats['last_activity']))
                        ? $claimData['last_claim'] : $stats['last_activity'];
                } elseif ($claimData['last_claim']) {
                    $stats['last_activity'] = $claimData['last_claim'];
                }
            }

            return $stats;

        } catch (PDOException $e) {
            error_log("Error getting member welfare stats: " . $e->getMessage());
            return [
                'total_paid' => 0,
                'total_claimed' => 0,
                'payment_count' => 0,
                'claim_count' => 0,
                'last_activity' => null
            ];
        }
    }

    // Search members with welfare history - COMPLETELY REWRITTEN
    public function searchMembersWithWelfare($search = '', $filter = 'all', $limit = 10) {
        try {

            // Build search condition
            $searchCondition = '';
            $searchParams = [];

            if (!empty($search)) {
                $searchCondition = " AND (LOWER(m.first_name) LIKE LOWER(?) OR LOWER(m.last_name) LIKE LOWER(?) OR m.phone_number LIKE ?)";
                $searchTerm = "%$search%";
                $searchParams = [$searchTerm, $searchTerm, $searchTerm];
            }

            // Build filter condition
            $filterCondition = '';
            if ($filter === 'payments_only') {
                $filterCondition = " AND COALESCE(p.payment_count, 0) > 0";
            } elseif ($filter === 'claims_only') {
                $filterCondition = " AND COALESCE(c.claim_count, 0) > 0";
            } elseif ($filter === 'both') {
                $filterCondition = " AND COALESCE(p.payment_count, 0) > 0 AND COALESCE(c.claim_count, 0) > 0";
            } elseif ($filter === 'recent') {
                $filterCondition = " AND (p.last_payment_date >= DATE_SUB(NOW(), INTERVAL 30 DAY) OR c.last_claim_date >= DATE_SUB(NOW(), INTERVAL 30 DAY))";
            }

            // Main query to get members with welfare activity
            $sql = "SELECT m.id, m.first_name, m.last_name, m.phone_number,
                           COALESCE(p.payment_count, 0) as payment_count,
                           COALESCE(p.total_paid, 0) as total_paid,
                           COALESCE(c.claim_count, 0) as claim_count,
                           COALESCE(c.total_claimed, 0) as total_claimed,
                           GREATEST(
                               COALESCE(p.last_payment_date, '1900-01-01'),
                               COALESCE(c.last_claim_date, '1900-01-01')
                           ) as last_activity_date
                    FROM members m
                    LEFT JOIN (
                        SELECT member_id, COUNT(*) as payment_count, SUM(amount) as total_paid, MAX(payment_date) as last_payment_date
                        FROM welfare_payments
                        WHERE status = 'disbursed'
                        GROUP BY member_id
                    ) p ON m.id = p.member_id
                    LEFT JOIN (
                        SELECT member_id, COUNT(*) as claim_count, SUM(claim_amount) as total_claimed, MAX(claim_date) as last_claim_date
                        FROM welfare_claims
                        WHERE status = 'approved'
                        GROUP BY member_id
                    ) c ON m.id = c.member_id
                    WHERE 1=1 $searchCondition $filterCondition
                    ORDER BY last_activity_date DESC, m.first_name ASC, m.last_name ASC
                    LIMIT ?";

            $stmt = $this->conn->prepare($sql);

            // Bind parameters
            $paramIndex = 1;
            foreach ($searchParams as $param) {
                $stmt->bindValue($paramIndex++, $param, PDO::PARAM_STR);
            }
            $stmt->bindValue($paramIndex, $limit, PDO::PARAM_INT);

            $stmt->execute();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get total count for the search
            $countSql = "SELECT COUNT(DISTINCT m.id) as total
                        FROM members m
                        LEFT JOIN (
                            SELECT member_id, COUNT(*) as payment_count
                            FROM welfare_payments
                            WHERE status = 'disbursed'
                            GROUP BY member_id
                        ) p ON m.id = p.member_id
                        LEFT JOIN (
                            SELECT member_id, COUNT(*) as claim_count
                            FROM welfare_claims
                            WHERE status = 'approved'
                            GROUP BY member_id
                        ) c ON m.id = c.member_id
                        WHERE 1=1 $searchCondition $filterCondition";

            $countStmt = $this->conn->prepare($countSql);

            // Bind search parameters for count query
            $paramIndex = 1;
            foreach ($searchParams as $param) {
                $countStmt->bindValue($paramIndex++, $param, PDO::PARAM_STR);
            }

            $countStmt->execute();
            $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            return [
                'members' => $members,
                'total_count' => intval($totalCount),
                'showing' => count($members),
                'search_term' => $search,
                'filter' => $filter
            ];

        } catch (PDOException $e) {
            error_log("Error searching members with welfare: " . $e->getMessage());
            return [
                'members' => [],
                'total_count' => 0,
                'showing' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    // Get members with welfare history with pagination and filtering
    public function getMembersWithWelfare($page = 1, $limit = 20, $search = '', $filter = 'all') {
        try {
            // Calculate offset for pagination
            $offset = ($page - 1) * $limit;

            // Build search condition
            $searchCondition = '';
            $searchParams = [];
            if (!empty($search)) {
                $searchCondition = " AND (LOWER(m.first_name) LIKE LOWER(?) OR LOWER(m.last_name) LIKE LOWER(?) OR m.phone_number LIKE ?)";
                $searchTerm = "%$search%";
                $searchParams = [$searchTerm, $searchTerm, $searchTerm];
            }

            // Build filter condition
            $filterCondition = '';
            switch ($filter) {
                case 'payments_only':
                    $filterCondition = " AND payment_count > 0 AND claim_count = 0";
                    break;
                case 'claims_only':
                    $filterCondition = " AND claim_count > 0 AND payment_count = 0";
                    break;
                case 'both':
                    $filterCondition = " AND payment_count > 0 AND claim_count > 0";
                    break;
                case 'recent':
                    $filterCondition = " AND last_activity_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
                    break;
            }

            // Get members with welfare activity using subquery for better performance
            $sql = "SELECT * FROM (
                        SELECT m.id, m.first_name, m.last_name, m.phone_number,
                               COALESCE(p.payment_count, 0) as payment_count,
                               COALESCE(p.total_paid, 0) as total_paid,
                               COALESCE(c.claim_count, 0) as claim_count,
                               COALESCE(c.total_claimed, 0) as total_claimed,
                               GREATEST(
                                   COALESCE(p.last_payment_date, '1900-01-01'),
                                   COALESCE(c.last_claim_date, '1900-01-01')
                               ) as last_activity_date
                        FROM members m
                        LEFT JOIN (
                            SELECT member_id, COUNT(*) as payment_count, SUM(amount) as total_paid, MAX(payment_date) as last_payment_date
                            FROM welfare_payments GROUP BY member_id
                        ) p ON m.id = p.member_id
                        LEFT JOIN (
                            SELECT member_id, COUNT(*) as claim_count, SUM(claim_amount) as total_claimed, MAX(claim_date) as last_claim_date
                            FROM welfare_claims GROUP BY member_id
                        ) c ON m.id = c.member_id
                        WHERE (p.member_id IS NOT NULL OR c.member_id IS NOT NULL)
                    ) welfare_members
                    WHERE 1=1 $searchCondition $filterCondition
                    ORDER BY last_activity_date DESC
                    LIMIT $limit OFFSET $offset";

            $stmt = $this->conn->prepare($sql);
            if (!empty($searchParams)) {
                $stmt->execute($searchParams);
            } else {
                $stmt->execute();
            }
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get total count for pagination
            $countSql = "SELECT COUNT(*) as total FROM (
                            SELECT m.id
                            FROM members m
                            LEFT JOIN (
                                SELECT member_id, COUNT(*) as payment_count, MAX(payment_date) as last_payment_date
                                FROM welfare_payments GROUP BY member_id
                            ) p ON m.id = p.member_id
                            LEFT JOIN (
                                SELECT member_id, COUNT(*) as claim_count, MAX(claim_date) as last_claim_date
                                FROM welfare_claims GROUP BY member_id
                            ) c ON m.id = c.member_id
                            WHERE (p.member_id IS NOT NULL OR c.member_id IS NOT NULL)
                         ) welfare_members
                         WHERE 1=1 $searchCondition $filterCondition";

            $countStmt = $this->conn->prepare($countSql);
            if (!empty($searchParams)) {
                $countStmt->execute($searchParams);
            } else {
                $countStmt->execute();
            }
            $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            return [
                'members' => $members,
                'total_count' => $totalCount,
                'current_page' => $page,
                'per_page' => $limit,
                'total_pages' => ceil($totalCount / $limit),
                'has_next' => $page < ceil($totalCount / $limit),
                'has_prev' => $page > 1
            ];

        } catch (PDOException $e) {
            error_log("Error getting members with welfare: " . $e->getMessage());
            return [];
        }
    }

    // Add new welfare category
    public function addCategory($data) {
        try {
            $sql = "INSERT INTO welfare_categories (name, description) VALUES (:name, :description)";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':description', $data['description']);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error adding welfare category: " . $e->getMessage());
            throw $e;
        }
    }

    // Update welfare category
    public function updateCategory($data) {
        try {
            $sql = "UPDATE welfare_categories 
                    SET name = :name, description = :description, is_active = :is_active 
                    WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $data['id']);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':is_active', $data['is_active']);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error updating welfare category: " . $e->getMessage());
            throw $e;
        }
    }

    // Delete welfare category
    public function deleteCategory($categoryId) {
        try {
            // Check if category has payments
            $sql = "SELECT COUNT(*) as payment_count FROM welfare_payments WHERE category_id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $categoryId);
            $stmt->execute();
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['payment_count'];
            
            if ($count > 0) {
                throw new Exception("Cannot delete category with existing payments. Deactivate instead.");
            }
            
            $sql = "DELETE FROM welfare_categories WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $categoryId);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error deleting welfare category: " . $e->getMessage());
            throw $e;
        }
    }

    // Get monthly welfare data for charts
    public function getMonthlyWelfareData($year = null) {
        try {
            if (!$year) $year = date('Y');
            
            $sql = "SELECT MONTH(payment_date) as month, 
                           COALESCE(SUM(amount), 0) as total_amount,
                           COUNT(*) as payment_count
                    FROM welfare_payments 
                    WHERE YEAR(payment_date) = :year AND status = 'disbursed'
                    GROUP BY MONTH(payment_date)
                    ORDER BY MONTH(payment_date)";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':year', $year);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting monthly welfare data: " . $e->getMessage());
            return [];
        }
    }

    // Get top welfare recipients
    public function getTopRecipients($limit = 5, $year = null) {
        try {
            $yearCondition = $year ? "AND YEAR(wp.payment_date) = :year" : "";
            
            $sql = "SELECT m.id, m.first_name, m.last_name,
                           COUNT(wp.id) as payment_count,
                           COALESCE(SUM(wp.amount), 0) as total_received
                    FROM members m
                    JOIN welfare_payments wp ON m.id = wp.member_id
                    WHERE wp.status = 'disbursed' $yearCondition
                    GROUP BY m.id
                    ORDER BY total_received DESC
                    LIMIT :limit";
            
            $stmt = $this->conn->prepare($sql);
            if ($year) {
                $stmt->bindParam(':year', $year);
            }
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting top welfare recipients: " . $e->getMessage());
            return [];
        }
    }

    // Update payment status
    public function updatePaymentStatus($data) {
        try {
            $sql = "UPDATE welfare_payments SET status = :status, notes = :notes WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $data['id']);
            $stmt->bindParam(':status', $data['status']);
            $stmt->bindParam(':notes', $data['notes']);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error updating payment status: " . $e->getMessage());
            throw $e;
        }
    }

    // Delete welfare payment
    public function deletePayment($paymentId) {
        try {
            $sql = "DELETE FROM welfare_payments WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $paymentId);

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error deleting welfare payment: " . $e->getMessage());
            throw $e;
        }
    }

    // Delete welfare claim
    public function deleteClaim($claimId) {
        try {
            $sql = "DELETE FROM welfare_claims WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $claimId);

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error deleting welfare claim: " . $e->getMessage());
            throw $e;
        }
    }

    // Get welfare payment by ID
    public function getPaymentById($paymentId) {
        try {
            $sql = "SELECT wp.*, m.first_name, m.last_name
                    FROM welfare_payments wp
                    JOIN members m ON wp.member_id = m.id
                    WHERE wp.id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $paymentId);
            $stmt->execute();

            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting welfare payment: " . $e->getMessage());
            return false;
        }
    }

    // Get welfare claim by ID
    public function getClaimById($claimId) {
        try {
            $sql = "SELECT wc.*, m.first_name, m.last_name
                    FROM welfare_claims wc
                    JOIN members m ON wc.member_id = m.id
                    WHERE wc.id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $claimId);
            $stmt->execute();

            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting welfare claim: " . $e->getMessage());
            return false;
        }
    }

    // Get welfare record by ID (generic method for RESTful compliance)
    public function getById($id) {
        // First try to get as payment
        $payment = $this->getPaymentById($id);
        if ($payment) {
            $payment['type'] = 'payment';
            return $payment;
        }

        // Then try to get as claim
        $claim = $this->getClaimById($id);
        if ($claim) {
            $claim['type'] = 'claim';
            return $claim;
        }

        return false;
    }

    // Update welfare payment
    public function updatePayment($paymentId, $data) {
        try {
            $sql = "UPDATE welfare_payments SET
                    member_id = :member_id,
                    amount = :amount,
                    payment_date = :payment_date,
                    payment_method = :payment_method,
                    notes = :notes
                    WHERE id = :id";

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $paymentId);
            $stmt->bindParam(':member_id', $data['member_id']);
            $stmt->bindParam(':amount', $data['amount']);
            $stmt->bindParam(':payment_date', $data['payment_date']);
            $stmt->bindParam(':payment_method', $data['payment_method']);
            $stmt->bindParam(':notes', $data['notes']);

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error updating welfare payment: " . $e->getMessage());
            throw $e;
        }
    }

    // Update welfare claim
    public function updateClaim($claimId, $data) {
        try {
            $sql = "UPDATE welfare_claims SET
                    member_id = :member_id,
                    claim_amount = :claim_amount,
                    claim_reason = :claim_reason,
                    claim_date = :claim_date,
                    status = :status,
                    notes = :notes
                    WHERE id = :id";

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $claimId);
            $stmt->bindParam(':member_id', $data['member_id']);
            $stmt->bindParam(':claim_amount', $data['claim_amount']);
            $stmt->bindParam(':claim_reason', $data['claim_reason']);
            $stmt->bindParam(':claim_date', $data['claim_date']);
            $stmt->bindParam(':status', $data['status']);
            $stmt->bindParam(':notes', $data['notes']);

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error updating welfare claim: " . $e->getMessage());
            throw $e;
        }
    }

    // Get yearly statistics
    public function getYearlyStats($year) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_payments,
                        COALESCE(SUM(amount), 0) as total_amount,
                        COUNT(DISTINCT member_id) as unique_recipients,
                        AVG(amount) as average_payment
                    FROM welfare_payments 
                    WHERE YEAR(payment_date) = :year AND status = 'disbursed'";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':year', $year);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting yearly welfare stats: " . $e->getMessage());
            return [];
        }
    }

    // Get category breakdown
    public function getCategoryBreakdown($year) {
        try {
            $sql = "SELECT wc.name, 
                           COUNT(wp.id) as payment_count,
                           COALESCE(SUM(wp.amount), 0) as total_amount
                    FROM welfare_categories wc
                    LEFT JOIN welfare_payments wp ON wc.id = wp.category_id 
                        AND YEAR(wp.payment_date) = :year AND wp.status = 'disbursed'
                    GROUP BY wc.id, wc.name
                    ORDER BY total_amount DESC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':year', $year);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting category breakdown: " . $e->getMessage());
            return [];
        }
    }

    // Get monthly trends
    public function getMonthlyTrends($year) {
        try {
            $sql = "SELECT MONTH(payment_date) as month,
                           MONTHNAME(payment_date) as month_name,
                           COUNT(*) as payment_count,
                           COALESCE(SUM(amount), 0) as total_amount
                    FROM welfare_payments 
                    WHERE YEAR(payment_date) = :year AND status = 'disbursed'
                    GROUP BY MONTH(payment_date), MONTHNAME(payment_date)
                    ORDER BY MONTH(payment_date)";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':year', $year);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting monthly trends: " . $e->getMessage());
            return [];
        }
    }

    // Get monthly welfare settings
    public function getMonthlySettings() {
        try {
            $sql = "SELECT * FROM welfare_monthly_settings WHERE is_active = 1 LIMIT 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!$result) {
                // Return default settings if none exist
                return [
                    'monthly_amount' => 50.00,
                    'due_day' => 1,
                    'grace_period_days' => 7,
                    'is_active' => true
                ];
            }
            return $result;
        } catch (PDOException $e) {
            error_log("Error getting monthly welfare settings: " . $e->getMessage());
            return [];
        }
    }

    // Get members who have paid monthly welfare for current month (limited for dashboard)
    public function getMembersWhoPayedThisMonth($limit = 10) {
        try {
            $sql = "SELECT m.id as member_id, m.first_name, m.last_name, m.phone_number,
                           wp.id as payment_id, wp.amount, wp.payment_date, wp.payment_method
                    FROM members m
                    JOIN welfare_payments wp ON m.id = wp.member_id
                    WHERE wp.is_monthly_welfare = 1
                    AND wp.payment_month = MONTH(CURDATE())
                    AND wp.payment_year = YEAR(CURDATE())
                    AND wp.status = 'disbursed'
                    ORDER BY wp.payment_date DESC";

            if ($limit > 0) {
                $sql .= " LIMIT " . intval($limit);
            }

            $stmt = $this->conn->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting members who paid this month: " . $e->getMessage());
            return [];
        }
    }

    // Get total count of members who paid this month
    public function getMembersWhoPayedThisMonthCount() {
        try {
            $sql = "SELECT COUNT(*) as total
                    FROM members m
                    JOIN welfare_payments wp ON m.id = wp.member_id
                    WHERE wp.is_monthly_welfare = 1
                    AND wp.payment_month = MONTH(CURDATE())
                    AND wp.payment_year = YEAR(CURDATE())
                    AND wp.status = 'disbursed'";

            $stmt = $this->conn->prepare($sql);
            $stmt->execute();

            return $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        } catch (PDOException $e) {
            error_log("Error getting count of members who paid this month: " . $e->getMessage());
            return 0;
        }
    }

    // Get members who have claimed welfare this month (limited for dashboard)
    public function getMembersWhoClaimedThisMonth($limit = 10) {
        try {
            $sql = "SELECT m.id as member_id, m.first_name, m.last_name, m.phone_number,
                           wc.id as claim_id, wc.claim_amount, wc.claim_date, wc.claim_reason, wc.status
                    FROM members m
                    JOIN welfare_claims wc ON m.id = wc.member_id
                    WHERE wc.claim_month = MONTH(CURDATE())
                    AND wc.claim_year = YEAR(CURDATE())
                    ORDER BY wc.claim_date DESC";

            if ($limit > 0) {
                $sql .= " LIMIT " . intval($limit);
            }

            $stmt = $this->conn->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting members who claimed this month: " . $e->getMessage());
            return [];
        }
    }

    // Get total count of members who claimed this month
    public function getMembersWhoClaimedThisMonthCount() {
        try {
            $sql = "SELECT COUNT(*) as total
                    FROM members m
                    JOIN welfare_claims wc ON m.id = wc.member_id
                    WHERE wc.claim_month = MONTH(CURDATE())
                    AND wc.claim_year = YEAR(CURDATE())";

            $stmt = $this->conn->prepare($sql);
            $stmt->execute();

            return $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        } catch (PDOException $e) {
            error_log("Error getting count of members who claimed this month: " . $e->getMessage());
            return 0;
        }
    }

    // Get paginated list of members who paid this month
    public function getMembersWhoPayedThisMonthPaginated($page = 1, $limit = 20) {
        try {
            $offset = ($page - 1) * $limit;

            $sql = "SELECT m.id as member_id, m.first_name, m.last_name, m.phone_number,
                           wp.id as payment_id, wp.amount, wp.payment_date, wp.payment_method
                    FROM members m
                    JOIN welfare_payments wp ON m.id = wp.member_id
                    WHERE wp.is_monthly_welfare = 1
                    AND wp.payment_month = MONTH(CURDATE())
                    AND wp.payment_year = YEAR(CURDATE())
                    AND wp.status = 'disbursed'
                    ORDER BY wp.payment_date DESC
                    LIMIT :limit OFFSET :offset";

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();

            $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $totalCount = $this->getMembersWhoPayedThisMonthCount();

            return [
                'payments' => $payments,
                'total_count' => $totalCount,
                'current_page' => $page,
                'per_page' => $limit,
                'total_pages' => ceil($totalCount / $limit),
                'has_next' => $page < ceil($totalCount / $limit),
                'has_prev' => $page > 1
            ];
        } catch (PDOException $e) {
            error_log("Error getting paginated payments: " . $e->getMessage());
            return [
                'payments' => [],
                'total_count' => 0,
                'current_page' => 1,
                'per_page' => $limit,
                'total_pages' => 0,
                'has_next' => false,
                'has_prev' => false
            ];
        }
    }

    // Get paginated list of members who claimed this month
    public function getMembersWhoClaimedThisMonthPaginated($page = 1, $limit = 20) {
        try {
            $offset = ($page - 1) * $limit;

            $sql = "SELECT m.id as member_id, m.first_name, m.last_name, m.phone_number,
                           wc.id as claim_id, wc.claim_amount, wc.claim_date, wc.claim_reason, wc.status
                    FROM members m
                    JOIN welfare_claims wc ON m.id = wc.member_id
                    WHERE wc.claim_month = MONTH(CURDATE())
                    AND wc.claim_year = YEAR(CURDATE())
                    ORDER BY wc.claim_date DESC
                    LIMIT :limit OFFSET :offset";

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();

            $claims = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $totalCount = $this->getMembersWhoClaimedThisMonthCount();

            return [
                'claims' => $claims,
                'total_count' => $totalCount,
                'current_page' => $page,
                'per_page' => $limit,
                'total_pages' => ceil($totalCount / $limit),
                'has_next' => $page < ceil($totalCount / $limit),
                'has_prev' => $page > 1
            ];
        } catch (PDOException $e) {
            error_log("Error getting paginated claims: " . $e->getMessage());
            return [
                'claims' => [],
                'total_count' => 0,
                'current_page' => 1,
                'per_page' => $limit,
                'total_pages' => 0,
                'has_next' => false,
                'has_prev' => false
            ];
        }
    }

    // Add monthly welfare payment
    public function addMonthlyWelfarePayment($data) {
        try {
            $sql = "INSERT INTO welfare_payments
                    (member_id, category_id, amount, reason, payment_date, payment_method,
                     reference_number, notes, approved_by, status, is_monthly_welfare,
                     payment_month, payment_year, welfare_amount_due, payment_status)
                    VALUES
                    (:member_id, :category_id, :amount, :reason, :payment_date, :payment_method,
                     :reference_number, :notes, :approved_by, 'disbursed', 1,
                     :payment_month, :payment_year, :welfare_amount_due, :payment_status)";

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':member_id', $data['member_id']);
            $stmt->bindParam(':category_id', $data['category_id']);
            $stmt->bindParam(':amount', $data['amount']);
            $stmt->bindParam(':reason', $data['reason']);
            $stmt->bindParam(':payment_date', $data['payment_date']);
            $stmt->bindParam(':payment_method', $data['payment_method']);
            $stmt->bindParam(':reference_number', $data['reference_number']);
            $stmt->bindParam(':notes', $data['notes']);
            $stmt->bindParam(':approved_by', $data['approved_by']);
            $stmt->bindParam(':payment_month', $data['payment_month']);
            $stmt->bindParam(':payment_year', $data['payment_year']);
            $stmt->bindParam(':welfare_amount_due', $data['welfare_amount_due']);
            $stmt->bindParam(':payment_status', $data['payment_status']);

            $stmt->execute();
            return $this->conn->lastInsertId();
        } catch (PDOException $e) {
            error_log("Error adding monthly welfare payment: " . $e->getMessage());
            throw $e;
        }
    }

    // Add welfare claim with business logic validation
    public function addWelfareClaim($data) {
        try {
            // Clear any previous errors
            $this->error = null;

            // Validate required data
            if (empty($data['member_id'])) {
                $this->error = 'Member ID is required.';
                return false;
            }

            if (empty($data['claim_amount']) || $data['claim_amount'] <= 0) {
                $this->error = 'Claim amount must be greater than zero.';
                return false;
            }

            // Check if member has already claimed this month
            if ($this->hasMemberClaimedThisMonth($data['member_id'])) {
                $this->error = 'This member has already claimed welfare assistance this month.';
                return false;
            }

            // Check if there are sufficient funds
            if (!$this->canProcessClaim($data['claim_amount'])) {
                $funds = $this->getAvailableWelfareFunds();
                $this->error = 'Insufficient welfare funds. Available balance: ₵' . number_format($funds['available_balance'], 2) .
                              '. Requested amount: ₵' . number_format($data['claim_amount'], 2);
                return false;
            }

            // All validations passed, proceed with insertion
            $sql = "INSERT INTO welfare_claims
                    (member_id, claim_month, claim_year, claim_amount, claim_reason,
                     claim_date, approved_by, status, notes)
                    VALUES
                    (:member_id, :claim_month, :claim_year, :claim_amount, :claim_reason,
                     :claim_date, :approved_by, :status, :notes)";

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':member_id', $data['member_id']);
            $stmt->bindParam(':claim_month', $data['claim_month']);
            $stmt->bindParam(':claim_year', $data['claim_year']);
            $stmt->bindParam(':claim_amount', $data['claim_amount']);
            $stmt->bindParam(':claim_reason', $data['claim_reason']);
            $stmt->bindParam(':claim_date', $data['claim_date']);
            $stmt->bindParam(':approved_by', $data['approved_by']);
            $stmt->bindParam(':status', $data['status']);
            $stmt->bindParam(':notes', $data['notes']);

            $stmt->execute();
            return $this->conn->lastInsertId();
        } catch (PDOException $e) {
            $this->error = 'Database error: ' . $e->getMessage();
            error_log("Error adding welfare claim: " . $e->getMessage());
            return false;
        }
    }

    // Check if member has already paid this month
    public function hasMemberPaidThisMonth($memberId) {
        try {
            $sql = "SELECT COUNT(*) as count
                    FROM welfare_payments
                    WHERE member_id = :member_id
                    AND is_monthly_welfare = 1
                    AND payment_month = MONTH(CURDATE())
                    AND payment_year = YEAR(CURDATE())
                    AND status = 'disbursed'";

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':member_id', $memberId);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] > 0;
        } catch (PDOException $e) {
            error_log("Error checking if member paid this month: " . $e->getMessage());
            return false;
        }
    }

    // Check if member has claimed welfare this month
    public function hasMemberClaimedThisMonth($memberId) {
        try {
            $sql = "SELECT COUNT(*) as count
                    FROM welfare_claims
                    WHERE member_id = :member_id
                    AND claim_month = MONTH(CURDATE())
                    AND claim_year = YEAR(CURDATE())";

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':member_id', $memberId);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] > 0;
        } catch (PDOException $e) {
            error_log("Error checking if member claimed this month: " . $e->getMessage());
            return false;
        }
    }

    // Get available welfare fund balance
    public function getAvailableWelfareFunds() {
        try {
            // Total collected from dues (all payments)
            $sql1 = "SELECT COALESCE(SUM(amount), 0) as total_collected
                     FROM welfare_payments";
            $stmt1 = $this->conn->prepare($sql1);
            $stmt1->execute();
            $totalCollected = $stmt1->fetch(PDO::FETCH_ASSOC)['total_collected'];

            // Total disbursed through claims (only disbursed claims)
            $sql2 = "SELECT COALESCE(SUM(claim_amount), 0) as total_disbursed
                     FROM welfare_claims
                     WHERE status = 'disbursed'";
            $stmt2 = $this->conn->prepare($sql2);
            $stmt2->execute();
            $totalDisbursed = $stmt2->fetch(PDO::FETCH_ASSOC)['total_disbursed'];

            return [
                'total_collected' => $totalCollected,
                'total_disbursed' => $totalDisbursed,
                'available_balance' => $totalCollected - $totalDisbursed
            ];

        } catch (PDOException $e) {
            error_log("Error getting welfare fund balance: " . $e->getMessage());
            return [
                'total_collected' => 0,
                'total_disbursed' => 0,
                'available_balance' => 0
            ];
        }
    }

    // Check if claim amount is within available funds
    public function canProcessClaim($claimAmount) {
        $funds = $this->getAvailableWelfareFunds();
        return $claimAmount <= $funds['available_balance'];
    }

    // Get welfare summary statistics for filtered results
    public function getWelfareSummaryStats($search = '', $filter = 'all') {
        try {
            // Build search condition
            $searchCondition = '';
            $searchParams = [];
            if (!empty($search)) {
                $searchCondition = " AND (m.first_name LIKE ? OR m.last_name LIKE ? OR m.phone_number LIKE ?)";
                $searchTerm = "%$search%";
                $searchParams = [$searchTerm, $searchTerm, $searchTerm];
            }

            // Build filter condition
            $filterCondition = '';
            switch ($filter) {
                case 'payments_only':
                    $filterCondition = " AND payment_count > 0 AND claim_count = 0";
                    break;
                case 'claims_only':
                    $filterCondition = " AND claim_count > 0 AND payment_count = 0";
                    break;
                case 'both':
                    $filterCondition = " AND payment_count > 0 AND claim_count > 0";
                    break;
                case 'recent':
                    $filterCondition = " AND last_activity_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
                    break;
            }

            $sql = "SELECT
                        COUNT(*) as total_members,
                        COALESCE(SUM(payment_count), 0) as total_payments,
                        COALESCE(SUM(total_paid), 0) as total_paid_amount,
                        COALESCE(SUM(claim_count), 0) as total_claims,
                        COALESCE(SUM(total_claimed), 0) as total_claimed_amount
                    FROM (
                        SELECT m.id,
                               COALESCE(p.payment_count, 0) as payment_count,
                               COALESCE(p.total_paid, 0) as total_paid,
                               COALESCE(c.claim_count, 0) as claim_count,
                               COALESCE(c.total_claimed, 0) as total_claimed,
                               GREATEST(
                                   COALESCE(p.last_payment_date, '1900-01-01'),
                                   COALESCE(c.last_claim_date, '1900-01-01')
                               ) as last_activity_date
                        FROM members m
                        LEFT JOIN (
                            SELECT member_id, COUNT(*) as payment_count, SUM(amount) as total_paid, MAX(payment_date) as last_payment_date
                            FROM welfare_payments GROUP BY member_id
                        ) p ON m.id = p.member_id
                        LEFT JOIN (
                            SELECT member_id, COUNT(*) as claim_count, SUM(claim_amount) as total_claimed, MAX(claim_date) as last_claim_date
                            FROM welfare_claims GROUP BY member_id
                        ) c ON m.id = c.member_id
                        WHERE (p.member_id IS NOT NULL OR c.member_id IS NOT NULL)
                    ) welfare_members
                    WHERE 1=1 $searchCondition $filterCondition";

            $stmt = $this->conn->prepare($sql);
            if (!empty($searchParams)) {
                $stmt->execute($searchParams);
            } else {
                $stmt->execute();
            }

            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Error getting welfare summary stats: " . $e->getMessage());
            return [
                'total_members' => 0,
                'total_payments' => 0,
                'total_paid_amount' => 0,
                'total_claims' => 0,
                'total_claimed_amount' => 0
            ];
        }
    }

    /**
     * Get welfare history for a specific member
     *
     * @param int $memberId Member ID
     * @return array Member's welfare history (payments and claims)
     */
    public function getMemberWelfareHistory($memberId) {
        try {
            $sql = "
                SELECT
                    'payment' as transaction_type,
                    wp.id,
                    wp.amount,
                    wp.payment_date as transaction_date,
                    wp.payment_method,
                    wp.notes,
                    wp.created_at,
                    m.first_name,
                    m.last_name,
                    'Monthly Welfare' as category_name,
                    NULL as claim_reason,
                    'completed' as status,
                    CASE WHEN wp.is_monthly_welfare = 1 THEN 1 ELSE 0 END as is_monthly_welfare
                FROM welfare_payments wp
                JOIN members m ON wp.member_id = m.id
                WHERE wp.member_id = ?

                UNION ALL

                SELECT
                    'claim' as transaction_type,
                    wc.id,
                    wc.claim_amount as amount,
                    wc.claim_date as transaction_date,
                    NULL as payment_method,
                    wc.notes,
                    wc.created_at,
                    m.first_name,
                    m.last_name,
                    'Welfare Assistance' as category_name,
                    wc.claim_reason,
                    wc.status,
                    0 as is_monthly_welfare
                FROM welfare_claims wc
                JOIN members m ON wc.member_id = m.id
                WHERE wc.member_id = ?

                ORDER BY transaction_date DESC, created_at DESC
            ";

            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$memberId, $memberId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error getting member welfare history: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get welfare statistics for a specific member
     *
     * @param int $memberId Member ID
     * @return array Member's welfare statistics
     */
    public function getMemberWelfareStats($memberId) {
        try {
            $sql = "
                SELECT
                    COALESCE(SUM(CASE WHEN type = 'payment' THEN amount ELSE 0 END), 0) as total_paid,
                    COALESCE(SUM(CASE WHEN type = 'claim' THEN amount ELSE 0 END), 0) as total_claimed,
                    COUNT(CASE WHEN type = 'payment' THEN 1 END) as payment_count,
                    COUNT(CASE WHEN type = 'claim' THEN 1 END) as claim_count,
                    MAX(CASE WHEN type = 'payment' THEN date END) as last_payment_date,
                    MAX(CASE WHEN type = 'claim' THEN date END) as last_claim_date
                FROM (
                    SELECT 'payment' as type, amount, payment_date as date
                    FROM welfare_payments
                    WHERE member_id = ?

                    UNION ALL

                    SELECT 'claim' as type, claim_amount as amount, claim_date as date
                    FROM welfare_claims
                    WHERE member_id = ?
                ) as welfare_data
            ";

            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$memberId, $memberId]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);

            // Calculate net contribution
            $stats['net_contribution'] = $stats['total_paid'] - $stats['total_claimed'];

            return $stats;

        } catch (Exception $e) {
            error_log("Error getting member welfare stats: " . $e->getMessage());
            return [
                'total_paid' => 0,
                'total_claimed' => 0,
                'payment_count' => 0,
                'claim_count' => 0,
                'last_payment_date' => null,
                'last_claim_date' => null,
                'net_contribution' => 0
            ];
        }
    }
}
