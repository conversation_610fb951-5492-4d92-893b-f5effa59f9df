<?php
/**
 * Family Management View
 */

// Ensure we have the required data
if (!isset($family_groups)) {
    $family_groups = [];
}
?>

<div class="container mx-auto fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-100">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-8 text-white relative overflow-hidden">
            <div class="absolute right-0 top-0 opacity-10">
                <i class="fas fa-home text-9xl transform -rotate-12 translate-x-8 -translate-y-8"></i>
            </div>
            <h1 class="text-3xl font-bold">Family Management</h1>
            <p class="mt-2 opacity-90 max-w-2xl">Manage parent-child relationships and family connections with comprehensive tracking and organization.</p>
        </div>
        <div class="p-6 bg-white">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-3 mb-4 md:mb-0">
                    <a href="<?php echo BASE_URL; ?>children-ministry" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 px-5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-md font-medium">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                    </a>
                    <a href="<?php echo BASE_URL; ?>members/add" class="bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-md font-medium">
                        <i class="fas fa-user-plus mr-2"></i> Register Member & Children
                    </a>

                </div>
            </div>
        </div>
    </div>

    <!-- Family Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-gradient-to-br from-primary-light to-primary rounded-xl shadow-lg p-6 flex items-center transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <div class="rounded-full bg-white bg-opacity-30 p-4 mr-4 text-white">
                <i class="fas fa-home text-2xl"></i>
            </div>
            <div>
                <div class="text-3xl font-bold text-white"><?php echo count($family_groups); ?></div>
                <div class="text-sm text-white text-opacity-90">Total Families</div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-xl shadow-lg p-6 flex items-center transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <div class="rounded-full bg-white bg-opacity-30 p-4 mr-4 text-white">
                <i class="fas fa-users text-2xl"></i>
            </div>
            <div>
                <div class="text-3xl font-bold text-white"><?php echo count($family_groups); ?></div>
                <div class="text-sm text-white text-opacity-90">Total Parents</div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl shadow-lg p-6 flex items-center transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <div class="rounded-full bg-white bg-opacity-30 p-4 mr-4 text-white">
                <i class="fas fa-child text-2xl"></i>
            </div>
            <div>
                <div class="text-3xl font-bold text-white">
                    <?php
                    $total_children = 0;
                    foreach ($family_groups as $family) {
                        $total_children += count($family['children']);
                    }
                    echo $total_children;
                    ?>
                </div>
                <div class="text-sm text-white text-opacity-90">Total Children</div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl shadow-lg p-6 flex items-center transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <div class="rounded-full bg-white bg-opacity-30 p-4 mr-4 text-white">
                <i class="fas fa-link text-2xl"></i>
            </div>
            <div>
                <div class="text-3xl font-bold text-white">
                    <?php
                    $total_relationships = 0;
                    foreach ($family_groups as $family) {
                        $total_relationships += count($family['children']);
                    }
                    echo $total_relationships;
                    ?>
                </div>
                <div class="text-sm text-white text-opacity-90">Relationships</div>
            </div>
        </div>
    </div>

    <!-- Family Groups -->
    <?php if (empty($family_groups)): ?>
        <div class="bg-white rounded-lg shadow-md p-12 text-center">
            <div class="text-gray-400 mb-4">
                <i class="fas fa-home text-6xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">No Family Relationships Found</h3>
            <p class="text-gray-500 mb-4">Family relationships will appear here once members are registered with family connections.</p>
        </div>
    <?php else: ?>
        <div class="space-y-6">
            <?php foreach ($family_groups as $family): ?>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <!-- Parent Information -->
                    <div class="flex items-center justify-between mb-4 pb-4 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-primary-light text-white flex items-center justify-center mr-4">
                                <i class="fas fa-user text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800"><?php echo htmlspecialchars($family['parent']['name']); ?></h3>
                                <p class="text-gray-600 text-sm">Parent/Guardian</p>
                            </div>
                        </div>

                    </div>

                    <!-- Children -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php foreach ($family['children'] as $child): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center mb-3">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3">
                                        <i class="fas fa-child"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800"><?php echo htmlspecialchars($child['child_first_name'] . ' ' . $child['child_last_name']); ?></h4>
                                        <p class="text-sm text-gray-600">Age <?php echo $child['child_age']; ?></p>
                                    </div>
                                </div>
                                
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Relationship:</span>
                                        <span class="font-medium capitalize"><?php echo htmlspecialchars($child['relationship_type']); ?></span>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Status:</span>
                                        <div class="flex space-x-1">
                                            <?php if ($child['is_primary']): ?>
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Primary</span>
                                            <?php endif; ?>
                                            <?php if ($child['can_pickup']): ?>
                                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Can Pickup</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <?php if ($child['notes']): ?>
                                        <div>
                                            <span class="text-gray-600">Notes:</span>
                                            <p class="text-gray-800 text-xs mt-1"><?php echo htmlspecialchars($child['notes']); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mt-3 pt-3 border-t border-gray-100 flex space-x-2">
                                    <a href="<?php echo BASE_URL; ?>children-ministry/view-child?id=<?php echo $child['child_id']; ?>"
                                       class="flex-1 bg-gradient-to-r from-primary to-primary-dark text-white text-center py-2 px-3 rounded-lg text-xs font-medium transition-all duration-200 hover:shadow-md">
                                        <i class="fas fa-eye mr-1"></i>View Child
                                    </a>
                                    <button onclick="deleteRelationship(<?php echo $child['id']; ?>, '<?php echo htmlspecialchars($family['parent_first_name'] . ' ' . $family['parent_last_name']); ?>', '<?php echo htmlspecialchars($child['child_first_name'] . ' ' . $child['child_last_name']); ?>')"
                                            class="bg-gradient-to-r from-red-500 to-red-600 text-white py-2 px-3 rounded-lg text-xs font-medium transition-all duration-200 hover:shadow-md">
                                        <i class="fas fa-trash mr-1"></i>Remove
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>



<script>








function deleteRelationship(relationshipId, parentName, childName) {
    if (confirm(`Are you sure you want to delete the relationship between ${parentName} and ${childName}?\n\nThis action cannot be undone.`)) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/icgc/children-ministry/delete-relationship';

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generate_csrf_token(); ?>';
        form.appendChild(csrfInput);

        // Add relationship ID
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = relationshipId;
        form.appendChild(idInput);

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    }
}


</script>
