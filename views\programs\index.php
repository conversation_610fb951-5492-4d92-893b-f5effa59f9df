<?php
/**
 * Programs Index View - Church Program & Activities Planner
 */

// Set default values for filtering
$current_year = date('Y');
$current_month = date('n');

// Header configuration
$header_title = 'Church Programs';
$header_subtitle = 'Manage your ministry programs and activities';
$header_icon = 'fas fa-list';
$header_width = 'w-[90%] mx-auto';

// Custom navigation for list page
$navigation_buttons = [
    'back' => [
        'url' => BASE_URL . 'dashboard',
        'text' => 'Back to Dashboard',
        'icon' => 'fas fa-arrow-left',
        'style' => 'bg-gray-100 hover:bg-gray-200 text-gray-700'
    ],
    'create' => [
        'url' => BASE_URL . 'programs/create',
        'text' => 'New Program',
        'icon' => 'fas fa-plus',
        'style' => 'bg-primary hover:bg-primary-dark text-white'
    ],
    // Exclude default dashboard button to avoid duplication
    'dashboard' => ['show' => false]
];

// Include shared header
include 'components/header.php';
?>

<div class="w-[90%] mx-auto">
    <!-- Programs List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Simplified Filters -->
        <div class="p-4 border-b border-gray-200 bg-gray-50">
            <form method="GET" action="<?php echo BASE_URL; ?>programs" class="flex flex-wrap gap-3 items-end">
                <div class="flex-1 min-w-[200px]">
                    <input type="text" name="search"
                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                           placeholder="Search programs..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-1 focus:ring-primary focus:border-primary">
                </div>

                <div>
                    <select name="status" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-1 focus:ring-primary focus:border-primary">
                        <option value="">All Status</option>
                        <option value="planned" <?php echo ($_GET['status'] ?? '') === 'planned' ? 'selected' : ''; ?>>Planned</option>
                        <option value="in_progress" <?php echo ($_GET['status'] ?? '') === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                        <option value="completed" <?php echo ($_GET['status'] ?? '') === 'completed' ? 'selected' : ''; ?>>Completed</option>
                    </select>
                </div>

                <div>
                    <select name="category" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-1 focus:ring-primary focus:border-primary">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo ($_GET['category'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <select name="year" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-1 focus:ring-primary focus:border-primary">
                        <?php for ($y = date('Y') - 1; $y <= date('Y') + 1; $y++): ?>
                            <option value="<?php echo $y; ?>" <?php echo ($_GET['year'] ?? date('Y')) == $y ? 'selected' : ''; ?>>
                                <?php echo $y; ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>

                <button type="submit" class="px-4 py-2 bg-primary hover:bg-primary-dark text-white text-sm rounded-md transition-colors">
                    Filter
                </button>

                <a href="<?php echo BASE_URL; ?>programs" class="px-3 py-2 text-gray-600 hover:text-gray-800 text-sm">
                    Clear
                </a>
            </form>
        </div>

        <!-- Simplified Programs List -->
        <div class="p-4">
            <?php if (empty($programs)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-calendar-plus text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-800 mb-2">No programs found</h3>
                    <p class="text-gray-600 mb-4">Create your first church program to get started.</p>
                    <a href="<?php echo BASE_URL; ?>programs/create" class="inline-flex items-center px-4 py-2 bg-primary hover:bg-primary-dark text-white text-sm rounded-lg transition-colors">
                        <i class="fas fa-plus mr-2"></i> Create Program
                    </a>
                </div>
            <?php else: ?>
                <div class="space-y-3">
                    <?php foreach ($programs as $program): ?>
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h3 class="font-medium text-gray-900">
                                            <a href="<?php echo BASE_URL; ?>programs/show?id=<?php echo $program['id']; ?>" class="hover:text-primary">
                                                <?php echo htmlspecialchars($program['title']); ?>
                                            </a>
                                        </h3>

                                        <?php
                                        $status_styles = [
                                            'planned' => 'bg-blue-100 text-blue-700',
                                            'in_progress' => 'bg-yellow-100 text-yellow-700',
                                            'completed' => 'bg-green-100 text-green-700',
                                            'cancelled' => 'bg-red-100 text-red-700',
                                            'postponed' => 'bg-gray-100 text-gray-700'
                                        ];
                                        $status_style = $status_styles[$program['status']] ?? 'bg-gray-100 text-gray-700';
                                        ?>
                                        <span class="px-2 py-1 rounded text-xs font-medium <?php echo $status_style; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $program['status'])); ?>
                                        </span>
                                    </div>

                                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                                        <span><?php echo htmlspecialchars($program['category_name']); ?></span>
                                        <span>•</span>
                                        <span><?php echo date('M j, Y', strtotime($program['start_date'])); ?></span>
                                        <?php if (!empty($program['coordinator_name'])): ?>
                                            <span>•</span>
                                            <span><?php echo htmlspecialchars($program['coordinator_name']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo BASE_URL; ?>programs/show?id=<?php echo $program['id']; ?>"
                                       class="p-2 text-gray-400 hover:text-primary transition-colors"
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>programs/edit?id=<?php echo $program['id']; ?>"
                                       class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
</div>

