<?php
/**
 * ProgramGuest Model
 * Handles database operations for program invited guests
 */

class ProgramGuest {
    private $conn;
    private $table_name = "program_invited_guests";

    // Object properties
    public $id;
    public $program_id;
    public $guest_name;
    public $guest_role;
    public $contact_email;
    public $contact_phone;
    public $organization;
    public $special_notes;
    public $invitation_status;
    public $invitation_sent_date;
    public $response_date;
    public $created_at;
    public $updated_at;
    public $created_by;

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create a new program guest
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET program_id = :program_id,
                      guest_name = :guest_name,
                      guest_role = :guest_role,
                      contact_email = :contact_email,
                      contact_phone = :contact_phone,
                      organization = :organization,
                      special_notes = :special_notes,
                      invitation_status = :invitation_status,
                      created_by = :created_by,
                      created_at = NOW(),
                      updated_at = NOW()";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->program_id = htmlspecialchars(strip_tags($this->program_id));
        $this->guest_name = htmlspecialchars(strip_tags($this->guest_name));
        $this->guest_role = htmlspecialchars(strip_tags($this->guest_role));
        $this->contact_email = htmlspecialchars(strip_tags($this->contact_email));
        $this->contact_phone = htmlspecialchars(strip_tags($this->contact_phone));
        $this->organization = htmlspecialchars(strip_tags($this->organization));
        $this->special_notes = htmlspecialchars(strip_tags($this->special_notes));
        $this->invitation_status = htmlspecialchars(strip_tags($this->invitation_status));
        $this->created_by = htmlspecialchars(strip_tags($this->created_by));

        // Bind data
        $stmt->bindParam(":program_id", $this->program_id);
        $stmt->bindParam(":guest_name", $this->guest_name);
        $stmt->bindParam(":guest_role", $this->guest_role);
        $stmt->bindParam(":contact_email", $this->contact_email);
        $stmt->bindParam(":contact_phone", $this->contact_phone);
        $stmt->bindParam(":organization", $this->organization);
        $stmt->bindParam(":special_notes", $this->special_notes);
        $stmt->bindParam(":invitation_status", $this->invitation_status);
        $stmt->bindParam(":created_by", $this->created_by);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Get all guests for a specific program
     * @param int $program_id
     * @return PDOStatement
     */
    public function getByProgram($program_id) {
        $query = "SELECT g.*, u.username as created_by_name
                  FROM " . $this->table_name . " g
                  LEFT JOIN users u ON g.created_by = u.id
                  WHERE g.program_id = :program_id
                  ORDER BY g.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":program_id", $program_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get a single guest by ID
     * @param int $id
     * @return array|false
     */
    public function getById($id) {
        $query = "SELECT g.*, u.username as created_by_name
                  FROM " . $this->table_name . " g
                  LEFT JOIN users u ON g.created_by = u.id
                  WHERE g.id = :id
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Update guest information
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET guest_name = :guest_name,
                      guest_role = :guest_role,
                      contact_email = :contact_email,
                      contact_phone = :contact_phone,
                      organization = :organization,
                      special_notes = :special_notes,
                      invitation_status = :invitation_status,
                      updated_at = NOW()
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->guest_name = htmlspecialchars(strip_tags($this->guest_name));
        $this->guest_role = htmlspecialchars(strip_tags($this->guest_role));
        $this->contact_email = htmlspecialchars(strip_tags($this->contact_email));
        $this->contact_phone = htmlspecialchars(strip_tags($this->contact_phone));
        $this->organization = htmlspecialchars(strip_tags($this->organization));
        $this->special_notes = htmlspecialchars(strip_tags($this->special_notes));
        $this->invitation_status = htmlspecialchars(strip_tags($this->invitation_status));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind data
        $stmt->bindParam(":guest_name", $this->guest_name);
        $stmt->bindParam(":guest_role", $this->guest_role);
        $stmt->bindParam(":contact_email", $this->contact_email);
        $stmt->bindParam(":contact_phone", $this->contact_phone);
        $stmt->bindParam(":organization", $this->organization);
        $stmt->bindParam(":special_notes", $this->special_notes);
        $stmt->bindParam(":invitation_status", $this->invitation_status);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    /**
     * Delete a guest
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        return $stmt->execute();
    }

    /**
     * Update invitation status
     * @param int $id
     * @param string $status
     * @return bool
     */
    public function updateInvitationStatus($id, $status) {
        $query = "UPDATE " . $this->table_name . " 
                  SET invitation_status = :status,
                      updated_at = NOW()";
        
        if ($status === 'confirmed' || $status === 'declined') {
            $query .= ", response_date = NOW()";
        }
        
        $query .= " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":status", $status);
        $stmt->bindParam(":id", $id);
        
        return $stmt->execute();
    }

    /**
     * Get guest statistics for a program
     * @param int $program_id
     * @return array
     */
    public function getProgramGuestStats($program_id) {
        $query = "SELECT 
                    COUNT(*) as total_guests,
                    SUM(CASE WHEN invitation_status = 'invited' THEN 1 ELSE 0 END) as invited,
                    SUM(CASE WHEN invitation_status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
                    SUM(CASE WHEN invitation_status = 'declined' THEN 1 ELSE 0 END) as declined,
                    SUM(CASE WHEN invitation_status = 'attended' THEN 1 ELSE 0 END) as attended,
                    SUM(CASE WHEN invitation_status = 'no_show' THEN 1 ELSE 0 END) as no_show
                  FROM " . $this->table_name . "
                  WHERE program_id = :program_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":program_id", $program_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create multiple guests for a program
     * @param int $program_id
     * @param array $guests_data
     * @param int $created_by
     * @return bool
     */
    public function createMultiple($program_id, $guests_data, $created_by) {
        try {
            $this->conn->beginTransaction();

            foreach ($guests_data as $guest_data) {
                // Skip empty guest entries
                if (empty($guest_data['name'])) {
                    continue;
                }

                $this->program_id = $program_id;
                $this->guest_name = $guest_data['name'];
                $this->guest_role = $guest_data['role'] ?? '';
                $this->contact_email = $guest_data['email'] ?? '';
                $this->contact_phone = $guest_data['phone'] ?? '';
                $this->organization = $guest_data['organization'] ?? '';
                $this->special_notes = $guest_data['notes'] ?? '';
                $this->invitation_status = 'invited';
                $this->created_by = $created_by;

                if (!$this->create()) {
                    $this->conn->rollBack();
                    return false;
                }
            }

            $this->conn->commit();
            return true;

        } catch (Exception $e) {
            $this->conn->rollBack();
            return false;
        }
    }

    /**
     * Get all guests with search and filter options
     * @param array $filters
     * @return PDOStatement
     */
    public function getAll($filters = []) {
        $query = "SELECT g.*, p.title as program_title, u.username as created_by_name
                  FROM " . $this->table_name . " g
                  LEFT JOIN church_programs p ON g.program_id = p.id
                  LEFT JOIN users u ON g.created_by = u.id
                  WHERE 1=1";

        $params = [];

        if (!empty($filters['program_id'])) {
            $query .= " AND g.program_id = :program_id";
            $params[':program_id'] = $filters['program_id'];
        }

        if (!empty($filters['status'])) {
            $query .= " AND g.invitation_status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($filters['search'])) {
            $query .= " AND (g.guest_name LIKE :search OR g.guest_role LIKE :search OR g.organization LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        $query .= " ORDER BY g.created_at DESC";

        if (!empty($filters['limit'])) {
            $query .= " LIMIT :limit";
            $params[':limit'] = (int)$filters['limit'];
        }

        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            if ($key === ':limit') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }
        
        $stmt->execute();
        return $stmt;
    }

    /**
     * Update guests for a program (handles create, update, delete)
     * @param int $program_id
     * @param array $guests_data
     * @param int $updated_by
     * @return bool
     */
    public function updateProgramGuests($program_id, $guests_data, $updated_by) {
        try {
            $this->conn->beginTransaction();

            // Get existing guests for this program
            $existing_guests = $this->getByProgram($program_id)->fetchAll(PDO::FETCH_ASSOC);
            $existing_guest_ids = array_column($existing_guests, 'id');
            $submitted_guest_ids = [];

            // Process submitted guests
            foreach ($guests_data as $guest_data) {
                // Skip empty guest entries
                if (empty($guest_data['name'])) {
                    continue;
                }

                if (!empty($guest_data['id'])) {
                    // Update existing guest
                    $this->id = $guest_data['id'];
                    $this->guest_name = $guest_data['name'];
                    $this->guest_role = $guest_data['role'] ?? '';
                    $this->contact_email = $guest_data['email'] ?? '';
                    $this->contact_phone = $guest_data['phone'] ?? '';
                    $this->organization = $guest_data['organization'] ?? '';
                    $this->special_notes = $guest_data['notes'] ?? '';

                    if (!$this->update()) {
                        $this->conn->rollBack();
                        return false;
                    }

                    $submitted_guest_ids[] = $guest_data['id'];
                } else {
                    // Create new guest
                    $this->program_id = $program_id;
                    $this->guest_name = $guest_data['name'];
                    $this->guest_role = $guest_data['role'] ?? '';
                    $this->contact_email = $guest_data['email'] ?? '';
                    $this->contact_phone = $guest_data['phone'] ?? '';
                    $this->organization = $guest_data['organization'] ?? '';
                    $this->special_notes = $guest_data['notes'] ?? '';
                    $this->invitation_status = 'invited';
                    $this->created_by = $updated_by;

                    if (!$this->create()) {
                        $this->conn->rollBack();
                        return false;
                    }

                    $submitted_guest_ids[] = $this->id;
                }
            }

            // Delete guests that were removed
            $guests_to_delete = array_diff($existing_guest_ids, $submitted_guest_ids);
            foreach ($guests_to_delete as $guest_id) {
                $this->id = $guest_id;
                if (!$this->delete()) {
                    $this->conn->rollBack();
                    return false;
                }
            }

            $this->conn->commit();
            return true;

        } catch (Exception $e) {
            $this->conn->rollBack();
            return false;
        }
    }
}
