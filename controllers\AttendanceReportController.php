<?php
/**
 * Attendance Report Controller
 * Handles advanced attendance reporting and analysis
 */

require_once 'models/Attendance.php';
require_once 'models/Service.php';
require_once 'models/Member.php';
require_once 'models/Setting.php';
require_once 'utils/validation.php';

class AttendanceReportController {
    private $database;
    private $attendance;
    private $service;
    private $member;
    private $setting;

    /**
     * Constructor
     */
    public function __construct() {
        try {
            $this->database = new Database();
            $this->attendance = new Attendance($this->database->getConnection());
            $this->service = new Service($this->database->getConnection());
            $this->member = new Member($this->database->getConnection());
            $this->setting = new Setting($this->database->getConnection());
        } catch (Exception $e) {
            error_log("AttendanceReportController constructor error: " . $e->getMessage());
            $this->setting = null;
        }
    }

    /**
     * Display weekly reports dashboard
     */
    public function weeklyReports() {
        // Get all services for filter
        $stmt = $this->service->getAll();
        $services = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Get default date range (current week)
        $start_date = date('Y-m-d', strtotime('monday this week'));
        $end_date = date('Y-m-d', strtotime('sunday this week'));

        // Check if custom date range is provided
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $start_date = sanitize($_GET['start_date']);
        }
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $end_date = sanitize($_GET['end_date']);
        }

        // Get service filter if provided
        $service_id = isset($_GET['service_id']) ? sanitize($_GET['service_id']) : null;

        // Get weekly attendance data
        $weekly_data = $this->getWeeklyAttendanceData($start_date, $end_date, $service_id);

        // Get attendance summary for the period
        $attendance_summary = $this->getAttendanceSummary($start_date, $end_date, $service_id);

        // Set page title and active page
        $page_title = 'Weekly Attendance Reports - ICGC Emmanuel Temple';
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/weekly-reports.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display attendance patterns analysis
     */
    public function attendancePatterns() {
        // Get pagination parameters
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $per_page = 20; // Members per page
        $offset = ($page - 1) * $per_page;

        // Get all members with their attendance patterns
        $all_members_with_patterns = $this->getAttendancePatterns();

        // Sort members by pattern priority (Very Regular first)
        $pattern_priority = [
            'Very Regular' => 1,
            'Regular' => 2,
            'Somewhat Regular' => 3,
            'Irregular' => 4,
            'Very Irregular' => 5,
            'Extended Absence' => 6
        ];

        usort($all_members_with_patterns, function($a, $b) use ($pattern_priority) {
            $priority_a = $pattern_priority[$a['pattern']] ?? 999;
            $priority_b = $pattern_priority[$b['pattern']] ?? 999;

            if ($priority_a === $priority_b) {
                // If same pattern, sort by attendance rate (highest first)
                return $b['attendance_rate'] - $a['attendance_rate'];
            }

            return $priority_a - $priority_b;
        });

        // Calculate pagination
        $total_members = count($all_members_with_patterns);
        $total_pages = ceil($total_members / $per_page);

        // Get members for current page
        $members_with_patterns = array_slice($all_members_with_patterns, $offset, $per_page);

        // Get attendance consistency statistics
        $consistency_stats = $this->getConsistencyStats($all_members_with_patterns);

        // Pagination data
        $pagination = [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_members' => $total_members,
            'per_page' => $per_page,
            'has_prev' => $page > 1,
            'has_next' => $page < $total_pages,
            'prev_page' => $page - 1,
            'next_page' => $page + 1,
            'start_member' => $offset + 1,
            'end_member' => min($offset + $per_page, $total_members)
        ];

        // Set page title and active page
        $page_title = getPageTitle('Attendance Patterns');
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/patterns.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Get member attendance history via AJAX
     */
    public function getMemberHistory() {
        // Check if it's an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }

        // Get member ID from request
        $member_id = isset($_GET['member_id']) ? (int)sanitize($_GET['member_id']) : 0;

        if (!$member_id) {
            http_response_code(400);
            echo json_encode(['error' => 'Member ID is required']);
            return;
        }

        // Get member details
        $member = $this->member->getById($member_id);
        if (!$member) {
            http_response_code(404);
            echo json_encode(['error' => 'Member not found']);
            return;
        }

        // Get QR attendance history for the last 3 months (only QR-based attendance)
        $start_date = date('Y-m-d', strtotime('-3 months'));
        $end_date = date('Y-m-d');

        $attendance_history = $this->getQrAttendanceHistory($member_id, $start_date, $end_date);

        // Calculate statistics
        $total_services = count($attendance_history);
        $present_count = 0;
        $absent_count = 0;
        $late_count = 0;

        foreach ($attendance_history as $record) {
            if ($record['status'] === 'present') {
                $present_count++;
            } elseif ($record['status'] === 'absent') {
                $absent_count++;
            } elseif ($record['status'] === 'late') {
                $late_count++;
            }
        }

        // Calculate attendance rate
        $attendance_rate = $total_services > 0 ? round(($present_count + $late_count) / $total_services * 100) : 0;

        // Determine pattern
        $pattern = $this->determineAttendancePattern($attendance_rate, $attendance_history);

        // Prepare response
        $response = [
            'member' => [
                'id' => $member->id,
                'name' => $member->first_name . ' ' . $member->last_name
            ],
            'statistics' => [
                'total_services' => $total_services,
                'present_count' => $present_count,
                'absent_count' => $absent_count,
                'late_count' => $late_count,
                'attendance_rate' => $attendance_rate,
                'pattern' => $pattern
            ],
            'history' => array_map(function($record) {
                return [
                    'date' => $record['attendance_date'],
                    'service_name' => $record['service_name'] ?? 'Unknown Service',
                    'status' => $record['status'],
                    'formatted_date' => date('F j, Y', strtotime($record['attendance_date']))
                ];
            }, $attendance_history)
        ];

        header('Content-Type: application/json');
        echo json_encode($response);
    }

    /**
     * Remove all old manual attendance records (keep only QR attendance)
     */
    public function cleanupOldAttendanceRecords() {
        // Check if it's an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }

        try {
            // Start transaction
            $this->attendance->conn->beginTransaction();

            // First, get count of records to be removed
            $count_query = "SELECT COUNT(*) as count FROM " . $this->attendance->table_name . "
                           WHERE marked_via = 'manual' OR marked_via IS NULL OR qr_session_id IS NULL";
            $count_stmt = $this->attendance->conn->prepare($count_query);
            $count_stmt->execute();
            $count_result = $count_stmt->fetch(PDO::FETCH_ASSOC);
            $records_to_remove = $count_result['count'];

            // Archive old records before deletion (optional - for backup)
            $archive_query = "INSERT INTO attendance_archive
                             (member_id, service_id, attendance_date, status, marked_by, created_at, updated_at, archived_at)
                             SELECT member_id, service_id, attendance_date, status, marked_by, created_at, updated_at, NOW()
                             FROM " . $this->attendance->table_name . "
                             WHERE marked_via = 'manual' OR marked_via IS NULL OR qr_session_id IS NULL";

            $archive_stmt = $this->attendance->conn->prepare($archive_query);
            $archive_stmt->execute();
            $archived_count = $archive_stmt->rowCount();

            // Delete old manual attendance records
            $delete_query = "DELETE FROM " . $this->attendance->table_name . "
                            WHERE marked_via = 'manual' OR marked_via IS NULL OR qr_session_id IS NULL";
            $delete_stmt = $this->attendance->conn->prepare($delete_query);
            $delete_stmt->execute();
            $deleted_count = $delete_stmt->rowCount();

            // Commit transaction
            $this->attendance->conn->commit();

            // Return success response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Old attendance records cleaned up successfully',
                'records_removed' => $deleted_count,
                'records_archived' => $archived_count,
                'qr_records_remaining' => $this->getQrRecordsCount()
            ]);

        } catch (Exception $e) {
            // Rollback transaction on error
            $this->attendance->conn->rollback();

            error_log("Cleanup Error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'error' => 'Failed to cleanup old records',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get count of QR attendance records
     */
    private function getQrRecordsCount() {
        $query = "SELECT COUNT(*) as count FROM " . $this->attendance->table_name . "
                  WHERE marked_via = 'qr' AND qr_session_id IS NOT NULL";
        $stmt = $this->attendance->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }

    /**
     * Display comparative analysis
     */
    public function comparativeAnalysis() {
        // Get all services for filter
        $stmt = $this->service->getAll();
        $services = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Default periods (current month vs previous month)
        $period1_start = date('Y-m-01');
        $period1_end = date('Y-m-d');
        $period2_start = date('Y-m-01', strtotime('-1 month'));
        $period2_end = date('Y-m-t', strtotime('-1 month'));

        // Check if custom periods are provided
        if (isset($_GET['period1_start']) && !empty($_GET['period1_start'])) {
            $period1_start = sanitize($_GET['period1_start']);
        }
        if (isset($_GET['period1_end']) && !empty($_GET['period1_end'])) {
            $period1_end = sanitize($_GET['period1_end']);
        }
        if (isset($_GET['period2_start']) && !empty($_GET['period2_start'])) {
            $period2_start = sanitize($_GET['period2_start']);
        }
        if (isset($_GET['period2_end']) && !empty($_GET['period2_end'])) {
            $period2_end = sanitize($_GET['period2_end']);
        }

        // Get service filter if provided
        $service_id = isset($_GET['service_id']) ? sanitize($_GET['service_id']) : null;

        // Get comparison data
        $period1_data = $this->getAttendanceSummary($period1_start, $period1_end, $service_id);
        $period2_data = $this->getAttendanceSummary($period2_start, $period2_end, $service_id);

        // Calculate differences
        $comparison_data = $this->calculateComparison($period1_data, $period2_data);

        // Set page title and active page
        $page_title = 'Attendance Comparison - ICGC Emmanuel Temple';
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/comparison.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display attendance goals
     */
    public function attendanceGoals() {
        // Get all services for filter
        $stmt = $this->service->getAll();
        $services = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Handle form submission for setting goals
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'set_goal') {
            $this->handleGoalSubmission();
        }

        // Get current goals
        $goals = $this->getAttendanceGoals();

        // Get current progress
        $progress = $this->getGoalProgress($goals);

        // Set page title and active page
        $page_title = 'Attendance Goals - ICGC Emmanuel Temple';
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/goals.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display absent member reminders
     */
    public function absentReminders() {
        // Get members absent for multiple weeks
        $absent_members = $this->getConsistentlyAbsentMembers();

        // Handle form submission for sending reminders
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'send_reminders') {
            $this->handleReminderSubmission();
        }

        // Get reminder history
        $reminder_history = $this->getReminderHistory();

        // Get real statistics for the dashboard
        $dashboard_stats = $this->getDashboardStats();

        // Include SMS helper functions
        require_once 'utils/sms_helper.php';

        // Get SMS settings and balance
        $setting = new Setting($this->database->getConnection());
        $settings = $setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';
        $sms_settings_configured = !empty($api_key);

        // Get SMS balance if API key is configured
        $sms_balance = 0;
        if ($sms_settings_configured) {
            try {
                $balance_result = check_sms_balance($api_key);
                $sms_balance = $balance_result['status'] ? $balance_result['balance'] : 0;
            } catch (Exception $e) {
                error_log('SMS Balance Error: ' . $e->getMessage());
                $sms_balance = 0;
            }
        }

        // Set page title and active page
        $page_title = getPageTitle('Absent Member Reminders');
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/reminders.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Get weekly attendance data
     *
     * @param string $start_date
     * @param string $end_date
     * @param int|null $service_id
     * @return array
     */
    private function getWeeklyAttendanceData($start_date, $end_date, $service_id = null) {
        return $this->attendance->getAttendanceByDateRange($start_date, $end_date, $service_id);
    }

    /**
     * Get attendance summary for a date range
     *
     * @param string $start_date
     * @param string $end_date
     * @param int|null $service_id
     * @return array
     */
    private function getAttendanceSummary($start_date, $end_date, $service_id = null) {
        return $this->attendance->getAttendanceSummary($start_date, $end_date, $service_id);
    }

    /**
     * Get attendance patterns for all members
     *
     * @return array
     */
    private function getAttendancePatterns() {
        // Get all active members
        $members = $this->member->getByStatus('active');
        $patterns = [];

        foreach ($members as $member) {
            // Get QR attendance history for the last 3 months (only QR-based attendance)
            $start_date = date('Y-m-d', strtotime('-3 months'));
            $end_date = date('Y-m-d');

            $attendance_history = $this->getQrAttendanceHistory($member->id, $start_date, $end_date);

            // Calculate consistency score and pattern
            $total_services = count($attendance_history);
            $present_count = 0;
            $absent_count = 0;
            $late_count = 0;

            foreach ($attendance_history as $record) {
                if ($record['status'] === 'present') {
                    $present_count++;
                } elseif ($record['status'] === 'absent') {
                    $absent_count++;
                } elseif ($record['status'] === 'late') {
                    $late_count++;
                }
            }

            // Calculate attendance rate
            $attendance_rate = $total_services > 0 ? round(($present_count + $late_count) / $total_services * 100) : 0;

            // Determine pattern category
            $pattern = $this->determineAttendancePattern($attendance_rate, $attendance_history);

            $patterns[] = [
                'member_id' => $member->id,
                'name' => $member->first_name . ' ' . $member->last_name,
                'total_services' => $total_services,
                'present_count' => $present_count,
                'absent_count' => $absent_count,
                'late_count' => $late_count,
                'attendance_rate' => $attendance_rate,
                'pattern' => $pattern,
                'history' => $attendance_history
            ];
        }

        return $patterns;
    }

    /**
     * Get QR attendance history for a specific member (only QR-based attendance)
     *
     * @param int $member_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    private function getQrAttendanceHistory($member_id, $start_date, $end_date) {
        $query = "SELECT a.*, s.name as service_name, s.day_of_week, s.time,
                         qr.token as qr_token, qr.created_at as qr_session_created
                  FROM " . $this->attendance->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  LEFT JOIN attendance_qr_sessions qr ON a.qr_session_id = qr.id
                  WHERE a.member_id = :member_id
                  AND a.attendance_date BETWEEN :start_date AND :end_date
                  AND a.marked_via = 'qr'
                  AND a.qr_session_id IS NOT NULL
                  ORDER BY a.attendance_date DESC, s.time ASC";

        $stmt = $this->attendance->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Determine attendance pattern based on attendance rate and history
     *
     * @param int $attendance_rate
     * @param array $history
     * @return string
     */
    private function determineAttendancePattern($attendance_rate, $history) {
        // Check for consecutive absences
        $max_consecutive_absences = 0;
        $current_consecutive_absences = 0;

        foreach ($history as $record) {
            if ($record['status'] === 'absent') {
                $current_consecutive_absences++;
                if ($current_consecutive_absences > $max_consecutive_absences) {
                    $max_consecutive_absences = $current_consecutive_absences;
                }
            } else {
                $current_consecutive_absences = 0;
            }
        }

        // Determine pattern
        if ($attendance_rate >= 90) {
            return 'Very Regular';
        } elseif ($attendance_rate >= 75) {
            return 'Regular';
        } elseif ($attendance_rate >= 50) {
            return 'Somewhat Regular';
        } elseif ($max_consecutive_absences >= 4) {
            return 'Extended Absence';
        } elseif ($attendance_rate >= 25) {
            return 'Irregular';
        } else {
            return 'Very Irregular';
        }
    }

    /**
     * Get consistency statistics
     *
     * @param array $members_with_patterns
     * @return array
     */
    private function getConsistencyStats($members_with_patterns) {
        $stats = [
            'Very Regular' => 0,
            'Regular' => 0,
            'Somewhat Regular' => 0,
            'Irregular' => 0,
            'Very Irregular' => 0,
            'Extended Absence' => 0
        ];

        foreach ($members_with_patterns as $member) {
            $stats[$member['pattern']]++;
        }

        return $stats;
    }

    /**
     * Calculate comparison between two periods
     *
     * @param array $period1_data
     * @param array $period2_data
     * @return array
     */
    private function calculateComparison($period1_data, $period2_data) {
        $comparison = [];

        // Calculate differences
        $comparison['total_attendance'] = [
            'period1' => $period1_data['total_attendance'],
            'period2' => $period2_data['total_attendance'],
            'difference' => $period1_data['total_attendance'] - $period2_data['total_attendance'],
            'percentage' => $period2_data['total_attendance'] > 0 ?
                round(($period1_data['total_attendance'] - $period2_data['total_attendance']) / $period2_data['total_attendance'] * 100, 1) : 0
        ];

        $comparison['average_attendance'] = [
            'period1' => $period1_data['average_attendance'],
            'period2' => $period2_data['average_attendance'],
            'difference' => $period1_data['average_attendance'] - $period2_data['average_attendance'],
            'percentage' => $period2_data['average_attendance'] > 0 ?
                round(($period1_data['average_attendance'] - $period2_data['average_attendance']) / $period2_data['average_attendance'] * 100, 1) : 0
        ];

        $comparison['attendance_rate'] = [
            'period1' => $period1_data['attendance_rate'],
            'period2' => $period2_data['attendance_rate'],
            'difference' => $period1_data['attendance_rate'] - $period2_data['attendance_rate'],
            'percentage' => $period2_data['attendance_rate'] > 0 ?
                round(($period1_data['attendance_rate'] - $period2_data['attendance_rate']) / $period2_data['attendance_rate'] * 100, 1) : 0
        ];

        // Add more comparison metrics as needed

        return $comparison;
    }

    /**
     * Get attendance goals
     *
     * @return array
     */
    private function getAttendanceGoals() {
        // Check if attendance_goals table exists and get real data
        try {
            $check_query = "SHOW TABLES LIKE 'attendance_goals'";
            $check_stmt = $this->database->getConnection()->prepare($check_query);
            $check_stmt->execute();
            $table_exists = $check_stmt->rowCount() > 0;

            if ($table_exists) {
                $query = "SELECT * FROM attendance_goals WHERE status = 'active' ORDER BY created_at DESC";
                $stmt = $this->database->getConnection()->prepare($query);
                $stmt->execute();
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        } catch (Exception $e) {
            error_log('Error getting attendance goals: ' . $e->getMessage());
        }

        // Return empty array if no goals table or no goals configured
        return [];
    }

    /**
     * Get goal progress
     *
     * @param array $goals
     * @return array
     */
    private function getGoalProgress($goals) {
        $progress = [];

        foreach ($goals as $goal) {
            $service_id = $goal['service_id'];
            $start_date = $goal['start_date'];
            $end_date = $goal['end_date'];

            // Get attendance data for this service and date range
            $attendance_data = $this->attendance->getAttendanceSummary($start_date, $end_date, $service_id);

            // Calculate progress
            $current = $attendance_data['average_attendance'];
            $target = $goal['target'];
            $percentage = $target > 0 ? round(($current / $target) * 100) : 0;

            $progress[$goal['id']] = [
                'current' => $current,
                'target' => $target,
                'percentage' => $percentage,
                'remaining' => max(0, $target - $current)
            ];
        }

        return $progress;
    }

    /**
     * Handle goal submission
     */
    private function handleGoalSubmission() {
        // Validate and process form data
        $name = sanitize($_POST['name'] ?? '');
        $service_id = sanitize($_POST['service_id'] ?? '');
        $target = sanitize($_POST['target'] ?? '');
        $start_date = sanitize($_POST['start_date'] ?? '');
        $end_date = sanitize($_POST['end_date'] ?? '');

        // Validate inputs
        $errors = [];

        if (empty($name)) {
            $errors[] = 'Goal name is required';
        }

        if (empty($service_id)) {
            $errors[] = 'Service is required';
        }

        if (empty($target) || !is_numeric($target) || $target <= 0) {
            $errors[] = 'Target must be a positive number';
        }

        if (empty($start_date)) {
            $errors[] = 'Start date is required';
        }

        if (empty($end_date)) {
            $errors[] = 'End date is required';
        }

        if (strtotime($end_date) < strtotime($start_date)) {
            $errors[] = 'End date must be after start date';
        }

        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $_SESSION['form_data'] = $_POST;
            redirect('attendance/goals');
            exit;
        }

        // Save goal (would typically save to database)
        // For now, just show success message
        set_flash_message('Attendance goal set successfully', 'success');
        redirect('attendance/goals');
    }

    /**
     * Get consistently absent members
     *
     * @return array
     */
    private function getConsistentlyAbsentMembers() {
        // First, check if we have any QR sessions at all
        $qr_sessions_query = "SELECT COUNT(*) as count FROM attendance_qr_sessions WHERE status = 'expired'";
        $stmt = $this->database->getConnection()->prepare($qr_sessions_query);
        $stmt->execute();
        $qr_sessions_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($qr_sessions_count == 0) {
            // No QR sessions exist, return empty array
            return [];
        }

        // Get all active members who have valid phone numbers (since we're focusing on SMS)
        $query = "SELECT m.* FROM members m
                  WHERE m.member_status = 'active'
                  AND m.phone_number IS NOT NULL
                  AND m.phone_number != ''
                  AND LENGTH(m.phone_number) >= 10
                  ORDER BY m.first_name, m.last_name";

        $stmt = $this->database->getConnection()->prepare($query);
        $stmt->execute();
        $members = $stmt->fetchAll(PDO::FETCH_OBJ);

        $absent_members = [];

        foreach ($members as $member) {
            // Check if member has ever attended via QR
            $ever_attended_qr = $this->hasEverAttendedViaQr($member->id);

            if (!$ever_attended_qr) {
                // They've never used QR attendance, skip them for now
                // (They might be new members or haven't adopted QR yet)
                continue;
            }

            // Count consecutive absences from recent QR services
            $consecutive_absences = $this->countConsecutiveQrAbsences($member->id);

            // If member has been absent for 3 or more consecutive QR services
            if ($consecutive_absences >= 3) {
                // Format department name
                $department_name = 'Not assigned';
                if (!empty($member->department) && $member->department !== 'none') {
                    $department_name = ucwords(str_replace('_', ' ', $member->department));
                }

                $absent_members[] = [
                    'member_id' => $member->id,
                    'name' => $member->first_name . ' ' . $member->last_name,
                    'phone' => $member->phone_number,
                    'department' => $department_name,
                    'consecutive_absences' => $consecutive_absences,
                    'last_attendance' => $this->getLastQrAttendanceDate($member->id)
                ];
            }
        }

        return $absent_members;
    }

    /**
     * Get department name by ID (safely handles missing departments table)
     *
     * @param int $department_id
     * @return string
     */
    private function getDepartmentName($department_id) {
        try {
            // Check if departments table exists
            $check_query = "SHOW TABLES LIKE 'departments'";
            $check_stmt = $this->database->getConnection()->prepare($check_query);
            $check_stmt->execute();
            $table_exists = $check_stmt->rowCount() > 0;

            if (!$table_exists) {
                return 'Department ' . $department_id;
            }

            // Get department name
            $query = "SELECT name FROM departments WHERE id = :department_id";
            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->bindParam(':department_id', $department_id);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result ? $result['name'] : 'Department ' . $department_id;

        } catch (Exception $e) {
            // If any error occurs, return a default value
            error_log('Error getting department name: ' . $e->getMessage());
            return 'Department ' . $department_id;
        }
    }

    /**
     * Check if member has ever attended via QR
     *
     * @param int $member_id
     * @return bool
     */
    private function hasEverAttendedViaQr($member_id) {
        $query = "SELECT COUNT(*) as count FROM " . $this->attendance->table_name . "
                  WHERE member_id = :member_id
                  AND marked_via = 'qr'
                  AND qr_session_id IS NOT NULL";

        $stmt = $this->attendance->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['count'] > 0;
    }

    /**
     * Count consecutive QR absences for a member
     *
     * @param int $member_id
     * @return int
     */
    private function countConsecutiveQrAbsences($member_id) {
        // Get recent QR sessions (last 6 weeks for more realistic tracking)
        $query = "SELECT DISTINCT qr.attendance_date, qr.id as session_id, qr.service_id
                  FROM attendance_qr_sessions qr
                  WHERE qr.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 6 WEEK)
                  AND qr.status = 'expired'
                  ORDER BY qr.attendance_date DESC, qr.created_at DESC
                  LIMIT 15";

        $stmt = $this->attendance->conn->prepare($query);
        $stmt->execute();
        $recent_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($recent_sessions)) {
            // No recent QR sessions, can't determine absence pattern
            return 0;
        }

        $consecutive_absences = 0;
        $total_sessions_checked = 0;

        foreach ($recent_sessions as $session) {
            $total_sessions_checked++;

            // Check if member attended this session
            $attendance_query = "SELECT COUNT(*) as count FROM " . $this->attendance->table_name . "
                               WHERE member_id = :member_id
                               AND qr_session_id = :session_id
                               AND marked_via = 'qr'
                               AND status IN ('present', 'late')";

            $attendance_stmt = $this->attendance->conn->prepare($attendance_query);
            $attendance_stmt->bindParam(':member_id', $member_id);
            $attendance_stmt->bindParam(':session_id', $session['session_id']);
            $attendance_stmt->execute();
            $attendance_result = $attendance_stmt->fetch(PDO::FETCH_ASSOC);

            if ($attendance_result['count'] == 0) {
                // Member was absent from this session
                $consecutive_absences++;
            } else {
                // Member attended, break the consecutive absence streak
                break;
            }

            // Don't count more than 10 consecutive absences for practical purposes
            if ($consecutive_absences >= 10) {
                break;
            }
        }

        return $consecutive_absences;
    }

    /**
     * Get last QR attendance date for a member
     *
     * @param int $member_id
     * @return string
     */
    private function getLastQrAttendanceDate($member_id) {
        $query = "SELECT a.attendance_date FROM " . $this->attendance->table_name . " a
                  WHERE a.member_id = :member_id
                  AND a.marked_via = 'qr'
                  AND a.qr_session_id IS NOT NULL
                  ORDER BY a.attendance_date DESC
                  LIMIT 1";

        $stmt = $this->attendance->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            return $result['attendance_date'];
        }

        return 'Never';
    }

    /**
     * Get last attendance date for a member (legacy method - keeping for compatibility)
     *
     * @param int $member_id
     * @return string
     */
    private function getLastAttendanceDate($member_id) {
        return $this->getLastQrAttendanceDate($member_id);
    }

    /**
     * Get reminder history
     *
     * @return array
     */
    private function getReminderHistory() {
        // Check if the reminder_history table exists
        try {
            // Try to load the ReminderHistory model
            require_once 'models/ReminderHistory.php';
            $reminderHistory = new ReminderHistory($this->database->getConnection());

            // Get all reminder history records
            $stmt = $reminderHistory->getAll();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // If there's an error (e.g., table doesn't exist), return empty array
            error_log('Error getting reminder history: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Handle reminder submission
     */
    private function handleReminderSubmission() {
        // Validate and process form data
        $member_ids = $_POST['member_ids'] ?? [];
        $message = sanitize($_POST['message'] ?? '');

        // Validate inputs
        $errors = [];

        if (empty($member_ids)) {
            $errors[] = 'Please select at least one member';
        }

        if (empty($message)) {
            $errors[] = 'Message is required';
        }

        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $_SESSION['form_data'] = $_POST;
            redirect('attendance/reminders');
            exit;
        }

        // Load required utilities and models
        require_once 'utils/sms_helper.php';
        require_once 'models/Setting.php';
        require_once 'models/ReminderHistory.php';

        // Get settings for SMS API
        $setting = new Setting($this->database->getConnection());
        $settings = $setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';
        $sender_id = $settings['sms_sender_id'] ?? 'ICGC';

        // Initialize ReminderHistory model
        $reminderHistory = new ReminderHistory($this->database->getConnection());

        // Track success and failure counts
        $success_count = 0;
        $failed_count = 0;

        // Process each member
        foreach ($member_ids as $member_id) {
            // Get member details
            $member = $this->member->getById($member_id);

            if (!$member || empty($member->phone)) {
                $failed_count++;
                continue;
            }

            // Personalize message if needed
            $personalized_message = str_replace(
                ['{first_name}', '{last_name}', '{full_name}'],
                [$member->first_name, $member->last_name, $member->first_name . ' ' . $member->last_name],
                $message
            );

            // Send SMS
            $result = send_sms($member->phone, $personalized_message, $sender_id, $api_key);

            // Record in reminder history
            $reminderHistory->member_id = $member_id;
            $reminderHistory->message = $personalized_message;
            $reminderHistory->sent_at = date('Y-m-d H:i:s');
            $reminderHistory->status = $result['status'] ? 'delivered' : 'failed';
            $reminderHistory->created_at = date('Y-m-d H:i:s');
            $reminderHistory->updated_at = date('Y-m-d H:i:s');

            try {
                $reminderHistory->create();
            } catch (Exception $e) {
                // If table doesn't exist, we'll just continue without recording
                error_log('Error creating reminder history: ' . $e->getMessage());
            }

            // Update counts
            if ($result['status']) {
                $success_count++;
            } else {
                $failed_count++;
            }
        }

        // Set appropriate message
        if ($success_count > 0) {
            if ($failed_count > 0) {
                set_flash_message('Reminders sent successfully to ' . $success_count . ' members. Failed to send to ' . $failed_count . ' members.', 'warning');
            } else {
                set_flash_message('Reminders sent successfully to ' . $success_count . ' members.', 'success');
            }
        } else {
            set_flash_message('Failed to send reminders. Please check your SMS settings and try again.', 'danger');
        }

        redirect('attendance/reminders');
    }

    /**
     * Get real dashboard statistics
     *
     * @return array
     */
    private function getDashboardStats() {
        $stats = [
            'total_members' => 0,
            'members_with_phones' => 0,
            'qr_active_members' => 0,
            'recent_qr_sessions' => 0,
            'total_qr_attendance' => 0,
            'last_qr_session_date' => null
        ];

        try {
            // Get total active members
            $query = "SELECT COUNT(*) as count FROM members WHERE member_status = 'active'";
            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->execute();
            $stats['total_members'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            // Get members with valid phone numbers
            $query = "SELECT COUNT(*) as count FROM members
                      WHERE member_status = 'active'
                      AND phone_number IS NOT NULL
                      AND phone_number != ''
                      AND LENGTH(phone_number) >= 10";
            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->execute();
            $stats['members_with_phones'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            // Get members who have used QR attendance
            $query = "SELECT COUNT(DISTINCT member_id) as count FROM attendance
                      WHERE marked_via = 'qr' AND qr_session_id IS NOT NULL";
            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->execute();
            $stats['qr_active_members'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            // Get recent QR sessions (last 30 days)
            $query = "SELECT COUNT(*) as count FROM attendance_qr_sessions
                      WHERE attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->execute();
            $stats['recent_qr_sessions'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            // Get total QR attendance records
            $query = "SELECT COUNT(*) as count FROM attendance
                      WHERE marked_via = 'qr' AND qr_session_id IS NOT NULL";
            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->execute();
            $stats['total_qr_attendance'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            // Get last QR session date
            $query = "SELECT MAX(attendance_date) as last_date FROM attendance_qr_sessions";
            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['last_qr_session_date'] = $result['last_date'];

        } catch (Exception $e) {
            error_log('Error getting dashboard stats: ' . $e->getMessage());
        }

        return $stats;
    }
}
