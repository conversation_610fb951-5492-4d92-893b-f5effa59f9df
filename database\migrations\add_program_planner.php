<?php
/**
 * Church Program & Activities Planner Database Migration
 * 
 * This migration creates the necessary tables for the comprehensive
 * Church Program & Activities Planner feature.
 */

// Include database configuration
require_once dirname(dirname(__DIR__)) . '/config/database.php';

try {
    // Create database connection
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Church Program & Activities Planner Migration</h2>";
    echo "<p>Creating tables for the program planner feature...</p>";
    
    // 1. Program Categories Table
    $sql_categories = "CREATE TABLE IF NOT EXISTS program_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        color_code VARCHAR(7) DEFAULT '#3B82F6',
        icon VARCHAR(50) DEFAULT 'fas fa-calendar',
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $stmt = $conn->prepare($sql_categories);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Program categories table created successfully</p>";
    
    // 2. Ministry Departments Table (Enhanced from existing departments)
    $sql_departments = "CREATE TABLE IF NOT EXISTS ministry_departments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        head_pastor_id INT NULL,
        contact_email VARCHAR(100),
        contact_phone VARCHAR(20),
        budget_allocation DECIMAL(10,2) DEFAULT 0.00,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (head_pastor_id) REFERENCES members(id) ON DELETE SET NULL
    )";
    
    $stmt = $conn->prepare($sql_departments);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Ministry departments table created successfully</p>";
    
    // 3. Enhanced Programs Table (replaces/enhances events table)
    $sql_programs = "CREATE TABLE IF NOT EXISTS church_programs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        category_id INT NOT NULL,
        department_id INT NOT NULL,
        coordinator_id INT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        start_time TIME NULL,
        end_time TIME NULL,
        location VARCHAR(200),
        budget_allocated DECIMAL(10,2) DEFAULT 0.00,
        budget_spent DECIMAL(10,2) DEFAULT 0.00,
        expected_attendance INT DEFAULT 0,
        actual_attendance INT DEFAULT 0,
        status ENUM('planned', 'in_progress', 'completed', 'cancelled', 'postponed') DEFAULT 'planned',
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        is_recurring BOOLEAN DEFAULT FALSE,
        recurrence_pattern VARCHAR(50) NULL,
        requires_registration BOOLEAN DEFAULT FALSE,
        max_participants INT NULL,
        notes TEXT,
        created_by INT NOT NULL,
        approved_by INT NULL,
        approved_at DATETIME NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES program_categories(id) ON DELETE RESTRICT,
        FOREIGN KEY (department_id) REFERENCES ministry_departments(id) ON DELETE RESTRICT,
        FOREIGN KEY (coordinator_id) REFERENCES members(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
        FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_start_date (start_date),
        INDEX idx_status (status),
        INDEX idx_department (department_id),
        INDEX idx_category (category_id)
    )";
    
    $stmt = $conn->prepare($sql_programs);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Church programs table created successfully</p>";
    
    // 4. Program Activities Table (sub-activities within programs)
    $sql_activities = "CREATE TABLE IF NOT EXISTS program_activities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        assigned_to INT NULL,
        start_datetime DATETIME NOT NULL,
        end_datetime DATETIME NOT NULL,
        location VARCHAR(200),
        status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
        priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
        estimated_cost DECIMAL(10,2) DEFAULT 0.00,
        actual_cost DECIMAL(10,2) DEFAULT 0.00,
        notes TEXT,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_to) REFERENCES members(id) ON DELETE SET NULL,
        INDEX idx_program (program_id),
        INDEX idx_start_datetime (start_datetime),
        INDEX idx_status (status)
    )";
    
    $stmt = $conn->prepare($sql_activities);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Program activities table created successfully</p>";
    
    // 5. Program Participants Table
    $sql_participants = "CREATE TABLE IF NOT EXISTS program_participants (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        member_id INT NOT NULL,
        role ENUM('participant', 'volunteer', 'coordinator', 'speaker') DEFAULT 'participant',
        registration_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        attendance_status ENUM('registered', 'attended', 'absent', 'cancelled') DEFAULT 'registered',
        notes TEXT,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        UNIQUE KEY unique_program_member (program_id, member_id),
        INDEX idx_program (program_id),
        INDEX idx_member (member_id),
        INDEX idx_role (role)
    )";
    
    $stmt = $conn->prepare($sql_participants);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Program participants table created successfully</p>";
    
    // 6. Program Resources Table
    $sql_resources = "CREATE TABLE IF NOT EXISTS program_resources (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        resource_name VARCHAR(200) NOT NULL,
        resource_type ENUM('equipment', 'material', 'venue', 'transport', 'catering', 'other') NOT NULL,
        quantity_needed INT DEFAULT 1,
        quantity_secured INT DEFAULT 0,
        estimated_cost DECIMAL(10,2) DEFAULT 0.00,
        actual_cost DECIMAL(10,2) DEFAULT 0.00,
        supplier_contact VARCHAR(200),
        status ENUM('needed', 'ordered', 'secured', 'delivered') DEFAULT 'needed',
        notes TEXT,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        INDEX idx_program (program_id),
        INDEX idx_type (resource_type),
        INDEX idx_status (status)
    )";
    
    $stmt = $conn->prepare($sql_resources);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Program resources table created successfully</p>";

    // 7. Program Reminders Table
    $sql_reminders = "CREATE TABLE IF NOT EXISTS program_reminders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        reminder_type ENUM('email', 'sms', 'notification') NOT NULL,
        recipient_type ENUM('coordinator', 'participants', 'department', 'all') NOT NULL,
        message TEXT NOT NULL,
        send_datetime DATETIME NOT NULL,
        status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
        sent_at DATETIME NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        INDEX idx_program (program_id),
        INDEX idx_send_datetime (send_datetime),
        INDEX idx_status (status)
    )";

    $stmt = $conn->prepare($sql_reminders);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Program reminders table created successfully</p>";

    // 8. Program Status History Table
    $sql_status_history = "CREATE TABLE IF NOT EXISTS program_status_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        old_status VARCHAR(50),
        new_status VARCHAR(50) NOT NULL,
        changed_by INT NOT NULL,
        change_reason TEXT,
        changed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE RESTRICT,
        INDEX idx_program (program_id),
        INDEX idx_changed_at (changed_at)
    )";

    $stmt = $conn->prepare($sql_status_history);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Program status history table created successfully</p>";

    // Insert default program categories
    $default_categories = [
        ['name' => 'Worship Services', 'description' => 'Regular worship services and special services', 'color_code' => '#8B5CF6', 'icon' => 'fas fa-pray'],
        ['name' => 'Youth Programs', 'description' => 'Programs and activities for youth ministry', 'color_code' => '#10B981', 'icon' => 'fas fa-users'],
        ['name' => 'Children Ministry', 'description' => 'Programs for children and Sunday school', 'color_code' => '#F59E0B', 'icon' => 'fas fa-child'],
        ['name' => 'Outreach & Evangelism', 'description' => 'Community outreach and evangelistic programs', 'color_code' => '#EF4444', 'icon' => 'fas fa-bullhorn'],
        ['name' => 'Training & Education', 'description' => 'Bible studies, seminars, and training programs', 'color_code' => '#3B82F6', 'icon' => 'fas fa-graduation-cap'],
        ['name' => 'Fellowship & Social', 'description' => 'Fellowship events and social gatherings', 'color_code' => '#06B6D4', 'icon' => 'fas fa-handshake'],
        ['name' => 'Special Events', 'description' => 'Conferences, revivals, and special occasions', 'color_code' => '#8B5CF6', 'icon' => 'fas fa-star'],
        ['name' => 'Fundraising', 'description' => 'Fundraising events and financial campaigns', 'color_code' => '#059669', 'icon' => 'fas fa-donate'],
        ['name' => 'Community Service', 'description' => 'Community service and charity programs', 'color_code' => '#DC2626', 'icon' => 'fas fa-heart'],
        ['name' => 'Administrative', 'description' => 'Administrative meetings and church business', 'color_code' => '#6B7280', 'icon' => 'fas fa-cogs']
    ];

    $insert_category = "INSERT IGNORE INTO program_categories (name, description, color_code, icon) VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($insert_category);

    foreach ($default_categories as $category) {
        $stmt->execute([$category['name'], $category['description'], $category['color_code'], $category['icon']]);
    }
    echo "<p style='color: green;'>✓ Default program categories inserted successfully</p>";

    // Insert default ministry departments based on existing member departments
    $default_departments = [
        ['name' => 'Head Pastor Office', 'description' => 'Senior pastoral leadership and oversight'],
        ['name' => 'Choir Ministry', 'description' => 'Music and worship ministry'],
        ['name' => 'Media Ministry', 'description' => 'Audio, video, and technical support'],
        ['name' => 'Ushering Ministry', 'description' => 'Welcome and hospitality services'],
        ['name' => 'Children Ministry', 'description' => 'Programs for children and families'],
        ['name' => 'Youth Ministry (New Breed)', 'description' => 'Youth programs and activities'],
        ['name' => 'Protocol Ministry', 'description' => 'Event coordination and protocol'],
        ['name' => 'Welfare Ministry', 'description' => 'Member care and welfare services'],
        ['name' => 'Intercessors Ministry', 'description' => 'Prayer and intercession ministry'],
        ['name' => 'Traffic Ministry', 'description' => 'Parking and traffic coordination'],
        ['name' => 'Administration', 'description' => 'Church administration and management'],
        ['name' => 'Instrumentalist Ministry', 'description' => 'Musical instruments and accompaniment'],
        ['name' => 'Deacons Board', 'description' => 'Deacon ministry and church governance']
    ];

    $insert_department = "INSERT IGNORE INTO ministry_departments (name, description) VALUES (?, ?)";
    $stmt = $conn->prepare($insert_department);

    foreach ($default_departments as $department) {
        $stmt->execute([$department['name'], $department['description']]);
    }
    echo "<p style='color: green;'>✓ Default ministry departments inserted successfully</p>";

    echo "<h3>Migration Completed Successfully!</h3>";
    echo "<p style='color: blue;'>All tables for the Church Program & Activities Planner have been created:</p>";
    echo "<ul>";
    echo "<li>✓ program_categories - For categorizing different types of programs</li>";
    echo "<li>✓ ministry_departments - For organizing programs by ministry departments</li>";
    echo "<li>✓ church_programs - Main programs table with comprehensive fields</li>";
    echo "<li>✓ program_activities - Sub-activities within programs</li>";
    echo "<li>✓ program_participants - Member participation tracking</li>";
    echo "<li>✓ program_resources - Resource management for programs</li>";
    echo "<li>✓ program_reminders - Automated reminder system</li>";
    echo "<li>✓ program_status_history - Audit trail for program changes</li>";
    echo "</ul>";
    echo "<p style='color: green;'>Default categories and departments have been populated.</p>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>
