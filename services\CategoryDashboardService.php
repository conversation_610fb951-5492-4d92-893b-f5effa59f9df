<?php
/**
 * Category Dashboard Service
 * Handles data retrieval and processing for dynamic category dashboards
 */

class CategoryDashboardService {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Get comprehensive dashboard data for a category
     */
    public function getDashboardData($category) {
        return [
            'overview' => $this->getOverviewStats($category),
            'recentPayments' => $this->getRecentPayments($category),
            'monthlyTrends' => $this->getMonthlyTrends($category),
            'topContributors' => $this->getTopContributors($category),
            'yearlyComparison' => $this->getYearlyComparison($category)
        ];
    }
    
    /**
     * Get overview statistics
     */
    private function getOverviewStats($category) {
        // Total amount collected
        $totalStmt = $this->db->prepare("
            SELECT COALESCE(SUM(amount), 0) as total_amount,
                   COUNT(*) as total_payments
            FROM finances
            WHERE category = ? AND transaction_type = ?
        ");
        $transactionType = $this->getTransactionType($category->category_type);
        $totalStmt->execute([$category->name, $transactionType]);
        $totals = $totalStmt->fetch(PDO::FETCH_OBJ);
        
        // This month's stats
        $monthStmt = $this->db->prepare("
            SELECT COALESCE(SUM(amount), 0) as month_amount,
                   COUNT(*) as month_payments
            FROM finances
            WHERE category = ?
            AND transaction_type = ?
            AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
            AND YEAR(transaction_date) = YEAR(CURRENT_DATE())
        ");
        $monthStmt->execute([$category->name, $transactionType]);
        $monthStats = $monthStmt->fetch(PDO::FETCH_OBJ);
        
        // Unique contributors (for member payments)
        $contributorsCount = 0;
        if ($category->requires_member) {
            $contributorsStmt = $this->db->prepare("
                SELECT COUNT(DISTINCT member_id) as unique_contributors
                FROM finances
                WHERE category = ? AND transaction_type = ? AND member_id IS NOT NULL
            ");
            $contributorsStmt->execute([$category->name, $transactionType]);
            $contributorsCount = $contributorsStmt->fetchColumn();
        }
        
        // Calculate growth percentage
        $lastMonthStmt = $this->db->prepare("
            SELECT COALESCE(SUM(amount), 0) as last_month_amount
            FROM finances
            WHERE category = ?
            AND transaction_type = ?
            AND MONTH(transaction_date) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH)
            AND YEAR(transaction_date) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)
        ");
        $lastMonthStmt->execute([$category->name, $transactionType]);
        $lastMonthAmount = $lastMonthStmt->fetchColumn();
        
        $growthPercentage = 0;
        if ($lastMonthAmount > 0) {
            $growthPercentage = (($monthStats->month_amount - $lastMonthAmount) / $lastMonthAmount) * 100;
        }
        
        return [
            'total_amount' => $totals->total_amount,
            'total_payments' => $totals->total_payments,
            'month_amount' => $monthStats->month_amount,
            'month_payments' => $monthStats->month_payments,
            'unique_contributors' => $contributorsCount,
            'growth_percentage' => round($growthPercentage, 1)
        ];
    }
    
    /**
     * Get recent payments
     */
    private function getRecentPayments($category, $limit = 10) {
        $sql = "
            SELECT ft.*,
                   CASE
                       WHEN ft.member_id IS NOT NULL THEN CONCAT(m.first_name, ' ', m.last_name)
                       ELSE 'Anonymous'
                   END as member_name
            FROM finances ft
            LEFT JOIN members m ON ft.member_id = m.id
            WHERE ft.category = ? AND ft.transaction_type = ?
            ORDER BY ft.transaction_date DESC, ft.created_at DESC
            LIMIT ?
        ";
        
        $stmt = $this->db->prepare($sql);
        $transactionType = $this->getTransactionType($category->category_type);
        $stmt->execute([$category->name, $transactionType, $limit]);
        
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    /**
     * Get monthly trends for the last 12 months
     */
    private function getMonthlyTrends($category) {
        $sql = "
            SELECT
                DATE_FORMAT(transaction_date, '%Y-%m') as month,
                COALESCE(SUM(amount), 0) as total_amount,
                COUNT(*) as payment_count
            FROM finances
            WHERE category = ?
            AND transaction_type = ?
            AND transaction_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
            ORDER BY month ASC
        ";
        
        $stmt = $this->db->prepare($sql);
        $transactionType = $this->getTransactionType($category->category_type);
        $stmt->execute([$category->name, $transactionType]);
        
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    /**
     * Get top contributors (for member payment categories)
     */
    private function getTopContributors($category, $limit = 10) {
        if (!$category->requires_member) {
            return [];
        }
        
        $sql = "
            SELECT
                CONCAT(m.first_name, ' ', m.last_name) as member_name,
                COALESCE(SUM(ft.amount), 0) as total_amount,
                COUNT(ft.id) as payment_count,
                MAX(ft.transaction_date) as last_payment_date
            FROM finances ft
            INNER JOIN members m ON ft.member_id = m.id
            WHERE ft.category = ? AND ft.transaction_type = ?
            GROUP BY ft.member_id, m.first_name, m.last_name
            ORDER BY total_amount DESC
            LIMIT ?
        ";
        
        $stmt = $this->db->prepare($sql);
        $transactionType = $this->getTransactionType($category->category_type);
        $stmt->execute([$category->name, $transactionType, $limit]);
        
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    /**
     * Get yearly comparison
     */
    private function getYearlyComparison($category) {
        $currentYear = date('Y');
        $lastYear = $currentYear - 1;
        
        $sql = "
            SELECT
                YEAR(transaction_date) as year,
                COALESCE(SUM(amount), 0) as total_amount,
                COUNT(*) as payment_count
            FROM finances
            WHERE category = ?
            AND transaction_type = ?
            AND YEAR(transaction_date) IN (?, ?)
            GROUP BY YEAR(transaction_date)
            ORDER BY year DESC
        ";
        
        $stmt = $this->db->prepare($sql);
        $transactionType = $this->getTransactionType($category->category_type);
        $stmt->execute([$category->name, $transactionType, $currentYear, $lastYear]);
        
        $results = $stmt->fetchAll(PDO::FETCH_OBJ);
        
        $comparison = [
            'current_year' => ['year' => $currentYear, 'total_amount' => 0, 'payment_count' => 0],
            'last_year' => ['year' => $lastYear, 'total_amount' => 0, 'payment_count' => 0]
        ];
        
        foreach ($results as $result) {
            if ($result->year == $currentYear) {
                $comparison['current_year'] = (array) $result;
            } else {
                $comparison['last_year'] = (array) $result;
            }
        }
        
        return $comparison;
    }
    
    /**
     * Get member payment history with pagination
     */
    public function getMemberPaymentHistory($categoryId, $page = 1, $limit = 10, $search = '') {
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE fc.id = ?";
        $params = [$categoryId];
        
        if (!empty($search)) {
            $whereClause .= " AND (CONCAT(m.first_name, ' ', m.last_name) LIKE ? OR ft.amount LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }
        
        // Get total count
        $countSql = "
            SELECT COUNT(*)
            FROM finances ft
            INNER JOIN custom_finance_categories fc ON ft.category = fc.name
            LEFT JOIN members m ON ft.member_id = m.id
            {$whereClause}
        ";
        
        $countStmt = $this->db->prepare($countSql);
        $countStmt->execute($params);
        $totalRecords = $countStmt->fetchColumn();
        
        // Get paginated data
        $sql = "
            SELECT ft.*,
                   CASE
                       WHEN ft.member_id IS NOT NULL THEN CONCAT(m.first_name, ' ', m.last_name)
                       ELSE 'Anonymous'
                   END as member_name
            FROM finances ft
            INNER JOIN custom_finance_categories fc ON ft.category = fc.name
            LEFT JOIN members m ON ft.member_id = m.id
            {$whereClause}
            ORDER BY ft.transaction_date DESC, ft.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $records = $stmt->fetchAll(PDO::FETCH_OBJ);
        
        return [
            'records' => $records,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($totalRecords / $limit),
                'total_records' => $totalRecords,
                'per_page' => $limit
            ]
        ];
    }
    
    /**
     * Get transaction type based on category type
     */
    private function getTransactionType($categoryType) {
        return $categoryType === 'expenses' ? 'expense' : 'income';
    }
    
    /**
     * Export category data
     */
    public function exportCategoryData($category, $format = 'csv') {
        $sql = "
            SELECT ft.*,
                   CASE
                       WHEN ft.member_id IS NOT NULL THEN CONCAT(m.first_name, ' ', m.last_name)
                       ELSE 'Anonymous'
                   END as member_name
            FROM finances ft
            LEFT JOIN members m ON ft.member_id = m.id
            WHERE ft.category = ? AND ft.transaction_type = ?
            ORDER BY ft.transaction_date DESC
        ";
        
        $stmt = $this->db->prepare($sql);
        $transactionType = $this->getTransactionType($category->category_type);
        $stmt->execute([$category->name, $transactionType]);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($format === 'csv') {
            $this->exportToCsv($data, $category->label);
        } else {
            $this->exportToExcel($data, $category->label);
        }
    }
    
    /**
     * Export to CSV
     */
    private function exportToCsv($data, $categoryLabel) {
        $filename = strtolower(str_replace(' ', '_', $categoryLabel)) . '_payments_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        if (!empty($data)) {
            // Write headers
            fputcsv($output, array_keys($data[0]));
            
            // Write data
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export to Excel (basic implementation)
     */
    private function exportToExcel($data, $categoryLabel) {
        // For now, export as CSV with .xls extension
        // Can be enhanced with proper Excel library later
        $this->exportToCsv($data, $categoryLabel);
    }
}
