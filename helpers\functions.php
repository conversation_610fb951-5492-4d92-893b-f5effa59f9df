<?php
/**
 * Helper functions for the ICGC Church Management System
 */

// Load centralized utilities
require_once __DIR__ . '/../utils/sanitizer.php';
require_once __DIR__ . '/../utils/csrf.php';
require_once __DIR__ . '/../utils/functions.php';

/**
 * Sanitize input data (backward compatibility wrapper)
 *
 * @param string|array $data The data to sanitize
 * @return string|array The sanitized data
 */
function sanitize($data) {
    return InputSanitizer::sanitizeString($data);
}

/**
 * Set flash message
 *
 * @param string $message The message to display
 * @param string $type The type of message (success, danger, warning, info)
 * @return void
 */
function set_flash_message($message, $type = 'info') {
    // Session should already be started by bootstrap

    $_SESSION['flash_message'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * Get flash message
 *
 * @return array|null The flash message or null if none exists
 */
function get_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $flash_message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $flash_message;
    }
    return null;
}

/**
 * Generate a URL with proper base URL handling
 * COMPLETELY REWRITTEN to fix double URL issues
 *
 * @param string $path The path to append to base URL
 * @return string The complete URL
 */
function url($path = '') {
    // Ensure BASE_URL is defined
    if (!defined('BASE_URL')) {
        define('BASE_URL', '/icgc/');
    }

    // If empty path, return base URL
    if (empty($path)) {
        return rtrim(BASE_URL, '/');
    }

    // If it's already a full URL (http/https), return as-is
    if (preg_match('/^https?:\/\//', $path)) {
        return $path;
    }

    // If path already starts with our base URL, return as-is
    if (strpos($path, BASE_URL) === 0) {
        return $path;
    }

    // If path starts with /icgc/, return as-is (already complete)
    if (strpos($path, '/icgc/') === 0) {
        return $path;
    }

    // Clean up the base URL and path
    $baseUrl = rtrim(BASE_URL, '/');
    $path = ltrim($path, '/');

    // Combine them
    return $baseUrl . '/' . $path;
}

/**
 * Generate an asset URL
 *
 * @param string $path The asset path
 * @return string The complete asset URL
 */
function asset($path) {
    return url('assets/' . ltrim($path, '/'));
}

/**
 * Generate a route URL with parameters
 *
 * @param string $name Route name or path
 * @param array $params Parameters to replace in route
 * @return string The complete URL
 */
function route($name, $params = []) {
    // Route name to URL mapping
    $routes = [
        'groups.index' => 'groups',
        'groups.members' => 'groups/members/{id}',
        'groups.schedule' => 'groups/members/{id}?tab=schedule',
        'groups.attendance' => 'groups/members/{id}?tab=attendance',
        'groups.dues' => 'groups/members/{id}?tab=dues',
        'groups.announcements' => 'groups/members/{id}?tab=announcements',
        'dashboard' => 'dashboard',
    ];

    $urlPath = $routes[$name] ?? $name;

    // Replace parameters in the URL
    foreach ($params as $key => $value) {
        $urlPath = str_replace('{' . $key . '}', $value, $urlPath);
    }

    return url($urlPath);
}

/**
 * Redirect to a URL
 *
 * @param string $url The URL to redirect to
 * @return void
 */
function redirect($url) {
    // Use the url() function to properly handle the URL
    $redirectUrl = url($url);

    // Prevent headers already sent error
    if (!headers_sent()) {
        header('Location: ' . $redirectUrl);
        exit;
    } else {
        // Fallback to JavaScript redirect if headers already sent
        echo "<script>window.location.href = '" . $redirectUrl . "';</script>";
        exit;
    }
}

/**
 * Format date
 *
 * @param string $date The date to format
 * @param string $format The format to use
 * @return string The formatted date
 */
function format_date($date, $format = 'F j, Y') {
    if (empty($date) || $date == '0000-00-00') {
        return 'N/A';
    }
    return date($format, strtotime($date));
}

/**
 * Format currency
 *
 * @param float $amount The amount to format
 * @param string $currency The currency symbol
 * @return string The formatted currency
 */
function format_currency($amount, $currency = 'GH₵') {
    return $currency . ' ' . number_format($amount, 2);
}

/**
 * Get time ago
 *
 * @param string $datetime The datetime to format
 * @return string The time ago
 */
function time_ago($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;
    
    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $mins = floor($diff / 60);
        return $mins . ' minute' . ($mins > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 2592000) {
        $weeks = floor($diff / 604800);
        return $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 31536000) {
        $months = floor($diff / 2592000);
        return $months . ' month' . ($months > 1 ? 's' : '') . ' ago';
    } else {
        $years = floor($diff / 31536000);
        return $years . ' year' . ($years > 1 ? 's' : '') . ' ago';
    }
}

/**
 * Generate random string
 *
 * @param int $length The length of the string
 * @return string The random string
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

// Note: Authentication functions (is_logged_in, has_permission, etc.) have been moved to bootstrap.php

/**
 * Get current page URL
 *
 * @return string The current page URL
 */
function get_current_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    return $protocol . '://' . $host . $uri;
}

/**
 * Truncate text
 *
 * @param string $text The text to truncate
 * @param int $length The maximum length
 * @param string $append The string to append if truncated
 * @return string The truncated text
 */
function truncate_text($text, $length = 100, $append = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    $text = substr($text, 0, $length);
    $text = substr($text, 0, strrpos($text, ' '));
    
    return $text . $append;
}

/**
 * Get church name from settings for dynamic page titles
 * @return string
 */
function getChurchName() {
    try {
        require_once 'models/Setting.php';
        require_once 'config/database.php';

        $database = new Database();
        $setting = new Setting($database->getConnection());

        return $setting->getValue('church_name') ?? 'ICGC Emmanuel Temple';
    } catch (Exception $e) {
        error_log('Error getting church name: ' . $e->getMessage());
        return 'ICGC Emmanuel Temple';
    }
}

/**
 * Generate dynamic page title with church name
 * @param string $pageTitle The page-specific title
 * @return string Complete page title
 */
function getPageTitle($pageTitle) {
    $churchName = getChurchName();
    return $pageTitle . ' - ' . $churchName;
}

/**
 * Get church name for controllers with setting object
 * @param object|null $setting The setting object from controller
 * @return string Church name
 */
function getChurchNameFromSetting($setting = null) {
    $church_name = 'ICGC Emmanuel Temple'; // Default fallback
    if ($setting !== null) {
        try {
            $church_name = $setting->getValue('church_name') ?? 'ICGC Emmanuel Temple';
        } catch (Exception $e) {
            error_log("Error getting church name: " . $e->getMessage());
        }
    }
    return $church_name;
}
?>
