<?php
/**
 * Validation Utility
 */

class Validation {
    private $errors = [];
    private $data = [];

    /**
     * Constructor
     * 
     * @param array $data
     */
    public function __construct($data) {
        $this->data = $data;
    }

    /**
     * Check if field is required
     * 
     * @param string $field
     * @param string $message
     * @return $this
     */
    public function required($field, $message = null) {
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->errors[$field] = $message ?? ucfirst($field) . ' is required';
        }
        return $this;
    }

    /**
     * Check if field is valid email
     *
     * @param string $field
     * @param string $message
     * @return $this
     */
    public function email($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $email = trim($this->data[$field]);

            // Basic email validation
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $this->errors[$field] = $message ?? 'Invalid email format';
                return $this;
            }

            // Check for common typos and suggest corrections
            $correctedEmail = $this->correctEmailTypos($email);
            if ($correctedEmail !== $email) {
                // Store the corrected email
                $this->data[$field] = $correctedEmail;
            }
        }
        return $this;
    }

    /**
     * Correct common email typos
     *
     * @param string $email
     * @return string
     */
    private function correctEmailTypos($email) {
        $commonTypos = [
            'gmail.co' => 'gmail.com',
            'gmail.con' => 'gmail.com',
            'gmial.com' => 'gmail.com',
            'gmai.com' => 'gmail.com',
            'yahoo.co' => 'yahoo.com',
            'yahoo.con' => 'yahoo.com',
            'yahooo.com' => 'yahoo.com',
            'hotmail.co' => 'hotmail.com',
            'hotmail.con' => 'hotmail.com',
            'outlook.co' => 'outlook.com',
            'outlook.con' => 'outlook.com'
        ];

        $emailParts = explode('@', $email);
        if (count($emailParts) === 2) {
            $domain = strtolower($emailParts[1]);

            if (isset($commonTypos[$domain])) {
                return $emailParts[0] . '@' . $commonTypos[$domain];
            }
        }

        return $email;
    }

    /**
     * Check if field has minimum length
     * 
     * @param string $field
     * @param int $length
     * @param string $message
     * @return $this
     */
    public function min($field, $length, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) < $length) {
            $this->errors[$field] = $message ?? ucfirst($field) . ' must be at least ' . $length . ' characters';
        }
        return $this;
    }

    /**
     * Check if field has maximum length
     * 
     * @param string $field
     * @param int $length
     * @param string $message
     * @return $this
     */
    public function max($field, $length, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) > $length) {
            $this->errors[$field] = $message ?? ucfirst($field) . ' must not exceed ' . $length . ' characters';
        }
        return $this;
    }

    /**
     * Check if field matches another field
     * 
     * @param string $field
     * @param string $match_field
     * @param string $message
     * @return $this
     */
    public function matches($field, $match_field, $message = null) {
        if (isset($this->data[$field]) && isset($this->data[$match_field])) {
            if ($this->data[$field] !== $this->data[$match_field]) {
                $this->errors[$field] = $message ?? ucfirst($field) . ' does not match ' . $match_field;
            }
        }
        return $this;
    }

    /**
     * Check if field is numeric
     *
     * @param string $field
     * @param string $message
     * @return $this
     */
    public function numeric($field, $message = null) {
        if (isset($this->data[$field]) && !is_numeric($this->data[$field])) {
            $this->errors[$field] = $message ?? ucfirst($field) . ' must be a number';
        }
        return $this;
    }

    /**
     * Validate name field (letters, spaces, hyphens, apostrophes only)
     *
     * @param string $field
     * @param string $message
     * @return $this
     */
    public function name($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $value = trim($this->data[$field]);

            // Check for minimum length (2 characters excluding spaces)
            $nameWithoutSpaces = preg_replace('/\s/', '', $value);
            if (strlen($nameWithoutSpaces) < 2) {
                $this->errors[$field] = $message ?? ucfirst($field) . ' must be at least 2 characters long';
                return $this;
            }

            // Check for invalid characters (only letters, spaces, hyphens, apostrophes allowed)
            if (!preg_match('/^[a-zA-Z\s\'-]+$/', $value)) {
                $this->errors[$field] = $message ?? ucfirst($field) . ' can only contain letters, spaces, hyphens, and apostrophes';
                return $this;
            }

            // Auto-format the name (trim spaces, capitalize)
            $formatted = $this->formatName($value);
            $this->data[$field] = $formatted;
        }
        return $this;
    }

    /**
     * Format name by trimming spaces and capitalizing properly
     *
     * @param string $name
     * @return string
     */
    private function formatName($name) {
        // Trim extra spaces
        $name = preg_replace('/\s+/', ' ', trim($name));

        // Capitalize first letter of each word
        return ucwords(strtolower($name));
    }

    /**
     * Validate date of birth
     *
     * @param string $field
     * @param string $message
     * @return $this
     */
    public function dateOfBirth($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $dateValue = $this->data[$field];
            $birthDate = new DateTime($dateValue);
            $today = new DateTime();

            // Check if date is in the future
            if ($birthDate > $today) {
                $this->errors[$field] = $message ?? 'Date of birth cannot be in the future';
                return $this;
            }

            // Calculate age
            $age = $today->diff($birthDate)->y;

            // Check maximum age (200 years)
            if ($age > 200) {
                $this->errors[$field] = $message ?? 'Age cannot exceed 200 years. Please check the date.';
                return $this;
            }

            // Store calculated age for reference
            $this->data['calculated_age'] = $age;
        }
        return $this;
    }

    /**
     * Get the data array (including any formatted values)
     *
     * @return array
     */
    public function getData() {
        return $this->data;
    }

    /**
     * Check if field is in array of allowed values
     * 
     * @param string $field
     * @param array $allowed
     * @param string $message
     * @return $this
     */
    public function in($field, $allowed, $message = null) {
        if (isset($this->data[$field]) && !in_array($this->data[$field], $allowed)) {
            $this->errors[$field] = $message ?? ucfirst($field) . ' contains invalid value';
        }
        return $this;
    }

    /**
     * Check if validation passes
     * 
     * @return bool
     */
    public function passes() {
        return empty($this->errors);
    }

    /**
     * Check if validation fails
     * 
     * @return bool
     */
    public function fails() {
        return !$this->passes();
    }

    /**
     * Get validation errors
     * 
     * @return array
     */
    public function errors() {
        return $this->errors;
    }
}
