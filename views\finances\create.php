<!-- Streamlined Finance Form -->
<div class="fade-in">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Add Financial Transaction</h1>
        <a href="<?php echo url('finances'); ?>" class="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i> Back to Finance
        </a>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-r-md mb-6">
            <div class="flex items-center mb-2">
                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                <h4 class="font-bold">Please fix the following errors:</h4>
            </div>
            <ul class="list-disc ml-6 mt-2">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <div class="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto border-t-4 border-primary">
        <div class="mb-4 pb-2 border-b border-gray-200">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-primary-light text-white mr-3">
                    <i class="fas fa-money-bill-wave text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-700">Please fill in the transaction details below</h3>
            </div>
        </div>
        <form action="<?php echo url('finances/store'); ?>" method="POST" id="financeForm" class="space-y-6">
            <!-- CSRF Token -->
            <?php echo csrf_field(); ?>

            <!-- Transaction Type -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <i class="fas fa-exchange-alt text-primary mr-2"></i>
                    Transaction Type
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <label class="flex items-center p-4 border border-gray-300 rounded-md cursor-pointer hover:bg-green-50 hover:border-green-300 transition-colors duration-200">
                        <input type="radio" name="transaction_type" value="income" class="mr-3 text-green-600 focus:ring-green-500" checked>
                        <div class="flex items-center">
                            <div class="p-2 rounded-full bg-green-100 mr-3">
                                <i class="fas fa-plus-circle text-green-600"></i>
                            </div>
                            <div>
                                <span class="font-medium text-green-600">Income</span>
                                <p class="text-sm text-gray-500">Money received</p>
                            </div>
                        </div>
                    </label>
                    <label class="flex items-center p-4 border border-gray-300 rounded-md cursor-pointer hover:bg-red-50 hover:border-red-300 transition-colors duration-200">
                        <input type="radio" name="transaction_type" value="expense" class="mr-3 text-red-600 focus:ring-red-500">
                        <div class="flex items-center">
                            <div class="p-2 rounded-full bg-red-100 mr-3">
                                <i class="fas fa-minus-circle text-red-600"></i>
                            </div>
                            <div>
                                <span class="font-medium text-red-600">Expense</span>
                                <p class="text-sm text-gray-500">Money spent</p>
                            </div>
                        </div>
                    </label>
                </div>
            </div>

            <!-- Category Selection -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <i class="fas fa-tags text-primary mr-2"></i>
                    Category
                </h2>

                <!-- Income Categories -->
                <div id="income-categories" class="category-section">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Income Category *</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-plus-circle text-green-600"></i>
                        </div>
                        <select name="income_category" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm bg-white" required>
                            <option value="">Select income category</option>
                            <?php
                            // Include the finance categories configuration
                            require_once 'config/finance_categories.php';
                            $incomeCategories = FinanceCategoriesConfig::getIncomeCategories();

                            foreach ($incomeCategories as $key => $category) :
                                $selected = (isset($_SESSION['form_data']['income_category']) && $_SESSION['form_data']['income_category'] == $key) ? 'selected' : '';
                            ?>
                            <option value="<?php echo $key; ?>"
                                    data-requires-member="<?php echo $category['requires_member'] ? 'true' : 'false'; ?>"
                                    <?php echo $selected; ?>>
                                <?php echo htmlspecialchars($category['label']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <!-- Expense Categories -->
                <div id="expense-categories" class="category-section hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Expense Category *</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-minus-circle text-red-600"></i>
                        </div>
                        <select name="expense_category" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm bg-white">
                            <option value="">Select expense category</option>
                            <?php
                            $expenseCategories = FinanceCategoriesConfig::getExpenseCategories();

                            foreach ($expenseCategories as $key => $category) :
                                $selected = (isset($_SESSION['form_data']['expense_category']) && $_SESSION['form_data']['expense_category'] == $key) ? 'selected' : '';
                            ?>
                            <option value="<?php echo $key; ?>"
                                    data-requires-member="<?php echo $category['requires_member'] ? 'true' : 'false'; ?>"
                                    <?php echo $selected; ?>>
                                <?php echo htmlspecialchars($category['label']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>


            </div>

            <!-- Member Selection (for tithe/welfare) -->
            <div id="member-selection" class="hidden">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <i class="fas fa-user text-primary mr-2"></i>
                    Member Selection
                </h2>

                <!-- Inline Member Search and Selection -->
                <div class="space-y-4">
                    <!-- Search Input -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search Member *</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i id="search-icon" class="fas fa-search text-gray-400 transition-colors duration-200"></i>
                            </div>
                            <input type="text" id="member-search"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm transition-all duration-200"
                                   placeholder="🔍 Type to search members by name or phone..."
                                   autocomplete="off">
                            <div id="search-loading" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none hidden">
                                <i class="fas fa-spinner fa-spin text-primary"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden Select for Form Submission -->
                    <select name="member_id" id="member-select" class="hidden">
                        <option value="">Select member</option>
                        <?php
                        // Include Member model
                        require_once 'models/Member.php';

                        // Get database connection
                        $db = new Database();
                        $conn = $db->getConnection();

                        // Create member object
                        $member = new Member($conn);

                        // Get all active members
                        $members = $member->getByStatus('active');

                        foreach ($members as $member_item) :
                            $selected = (isset($_SESSION['form_data']['member_id']) && $_SESSION['form_data']['member_id'] == $member_item->id) ? 'selected' : '';
                            $memberName = htmlspecialchars($member_item->first_name . ' ' . $member_item->last_name);
                        ?>
                        <option value="<?php echo $member_item->id; ?>"
                                data-name="<?php echo $memberName; ?>"
                                data-phone="<?php echo htmlspecialchars($member_item->phone_number ?? ''); ?>"
                                data-picture="<?php echo htmlspecialchars($member_item->profile_picture ?? ''); ?>"
                                data-department="<?php echo htmlspecialchars($member_item->department ?? ''); ?>"
                                data-role="<?php echo htmlspecialchars($member_item->role ?? ''); ?>"
                                <?php echo $selected; ?>>
                            <?php echo $memberName; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>

                    <!-- Inline Search Results Display -->
                    <div id="member-search-results" class="hidden">
                        <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-sm font-medium text-gray-700">
                                    <i class="fas fa-users text-primary mr-2"></i>Search Results
                                </h4>
                                <button type="button" id="clear-search" class="text-xs text-gray-500 hover:text-gray-700">
                                    <i class="fas fa-times mr-1"></i>Clear
                                </button>
                            </div>
                            <div id="search-results-container" class="space-y-2 max-h-64 overflow-y-auto">
                                <!-- Results will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Selected Member Display -->
                    <div id="selected-member-info" class="p-4 bg-green-50 border border-green-200 rounded-md hidden">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div id="member-avatar" class="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-bold">
                                    <span id="member-initial">?</span>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-800" id="member-display-name">Member Name</p>
                                    <p class="text-sm text-gray-600" id="member-display-phone">Phone Number</p>
                                </div>
                            </div>
                            <button type="button" id="remove-member" class="text-red-500 hover:text-red-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transaction Details -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <i class="fas fa-file-invoice-dollar text-primary mr-2"></i>
                    Transaction Details
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Amount -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount (GH₵) *</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 font-semibold">GH₵</span>
                            </div>
                            <input type="number" name="amount" step="0.01" min="0" max="1000000"
                                   class="w-full pl-12 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm"
                                   placeholder="0.00"
                                   value="<?php echo isset($_SESSION['form_data']['amount']) ? $_SESSION['form_data']['amount'] : ''; ?>" required>
                        </div>
                    </div>

                    <!-- Transaction Date -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Transaction Date *</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-alt text-gray-400"></i>
                            </div>
                            <input type="date" name="transaction_date"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm"
                                   value="<?php echo isset($_SESSION['form_data']['transaction_date']) ? $_SESSION['form_data']['transaction_date'] : date('Y-m-d'); ?>"
                                   max="<?= date('Y-m-d') ?>" required>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-credit-card text-gray-400"></i>
                            </div>
                            <select name="payment_method" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm bg-white">
                                <option value="cash" <?php echo (isset($_SESSION['form_data']['payment_method']) && $_SESSION['form_data']['payment_method'] == 'cash') ? 'selected' : ''; ?>>Cash</option>
                                <option value="bank_transfer" <?php echo (isset($_SESSION['form_data']['payment_method']) && $_SESSION['form_data']['payment_method'] == 'bank_transfer') ? 'selected' : ''; ?>>Bank Transfer</option>
                                <option value="mobile_money" <?php echo (isset($_SESSION['form_data']['payment_method']) && $_SESSION['form_data']['payment_method'] == 'mobile_money') ? 'selected' : ''; ?>>Mobile Money</option>
                                <option value="cheque" <?php echo (isset($_SESSION['form_data']['payment_method']) && $_SESSION['form_data']['payment_method'] == 'cheque') ? 'selected' : ''; ?>>Cheque</option>
                                <option value="other" <?php echo (isset($_SESSION['form_data']['payment_method']) && $_SESSION['form_data']['payment_method'] == 'other') ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Reference Number -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Reference Number</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-hashtag text-gray-400"></i>
                        </div>
                        <input type="text" name="reference_number"
                               class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm"
                               placeholder="Auto-generated if empty"
                               value="<?php echo isset($_SESSION['form_data']['reference_number']) ? $_SESSION['form_data']['reference_number'] : ''; ?>">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Leave empty for auto-generation</p>
                </div>

                <!-- Description -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm resize-none"
                              placeholder="Additional details about this transaction..."><?php echo isset($_SESSION['form_data']['description']) ? $_SESSION['form_data']['description'] : ''; ?></textarea>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
                <button type="submit" class="flex-1 sm:flex-none bg-primary hover:bg-primary-dark text-white font-medium py-2 px-6 rounded-md transition-colors duration-200 flex items-center justify-center">
                    <i class="fas fa-save mr-2"></i>Save Transaction
                </button>
                <a href="<?php echo url('finances'); ?>" class="flex-1 sm:flex-none bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded-md transition-colors duration-200 text-center flex items-center justify-center">
                    <i class="fas fa-times mr-2"></i>Cancel
                </a>
            </div>

        </form>
    </div>
</div>

<script>
// Global functions that need to be accessible
function toggleMemberSelection(show) {
    const memberSelection = document.getElementById('member-selection');
    const memberInfo = document.getElementById('selected-member-info');

    if (show) {
        memberSelection.classList.remove('hidden');
        document.querySelector('select[name="member_id"]').required = true;
    } else {
        memberSelection.classList.add('hidden');
        document.querySelector('select[name="member_id"]').required = false;
        if (memberInfo) memberInfo.classList.add('hidden');
    }
}

// Streamlined Finance Form Interactions
document.addEventListener('DOMContentLoaded', function() {
    const transactionTypeInputs = document.querySelectorAll('input[name="transaction_type"]');
    const incomeCategories = document.getElementById('income-categories');
    const expenseCategories = document.getElementById('expense-categories');
    const memberSelection = document.getElementById('member-selection');
    const memberSelect = document.getElementById('member-select');
    const memberInfo = document.getElementById('selected-member-info');

    // Handle transaction type changes
    transactionTypeInputs.forEach(input => {
        input.addEventListener('change', function() {
            updateFormSections(this.value);
        });
    });

    // Handle income category changes for member requirement
    const incomeCategorySelect = document.querySelector('select[name="income_category"]');

    if (incomeCategorySelect) {
        incomeCategorySelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const requiresMember = selectedOption.dataset.requiresMember === 'true';
            toggleMemberSelection(requiresMember);
        });
    }

    // Handle expense category changes
    const expenseCategorySelect = document.querySelector('select[name="expense_category"]');
    if (expenseCategorySelect) {
        expenseCategorySelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const requiresMember = selectedOption.dataset.requiresMember === 'true';
            toggleMemberSelection(requiresMember);
        });
    }

    // Enhanced inline member search functionality
    const memberSearch = document.getElementById('member-search');
    const memberSearchResults = document.getElementById('member-search-results');
    const searchResultsContainer = document.getElementById('search-results-container');
    const clearSearchBtn = document.getElementById('clear-search');
    const removeMemberBtn = document.getElementById('remove-member');
    const searchIcon = document.getElementById('search-icon');
    const searchLoading = document.getElementById('search-loading');
    let searchTimeout;

    if (memberSearch) {
        memberSearch.addEventListener('input', function() {
            clearTimeout(searchTimeout);

            // Show loading state
            if (this.value.length >= 2) {
                showSearchLoading();
                searchTimeout = setTimeout(() => {
                    handleMemberSearch(this.value);
                    hideSearchLoading();
                }, 300);
            } else {
                hideSearchLoading();
                memberSearchResults.classList.add('hidden');
                memberInfo.classList.add('hidden');
            }
        });

        memberSearch.addEventListener('focus', function() {
            if (this.value.length >= 2) {
                handleMemberSearch(this.value);
            }
            // Change search icon color on focus
            searchIcon.classList.remove('text-gray-400');
            searchIcon.classList.add('text-primary');
        });

        memberSearch.addEventListener('blur', function() {
            // Reset search icon color on blur
            setTimeout(() => {
                searchIcon.classList.remove('text-primary');
                searchIcon.classList.add('text-gray-400');
            }, 200);
        });

        // Keyboard navigation for search results
        memberSearch.addEventListener('keydown', function(e) {
            const items = searchResultsContainer.querySelectorAll('.member-result-item');
            const currentActive = searchResultsContainer.querySelector('.member-result-item.active');
            let activeIndex = currentActive ? Array.from(items).indexOf(currentActive) : -1;

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                activeIndex = activeIndex < items.length - 1 ? activeIndex + 1 : 0;
                setActiveItem(items, activeIndex);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                activeIndex = activeIndex > 0 ? activeIndex - 1 : items.length - 1;
                setActiveItem(items, activeIndex);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (currentActive) {
                    selectMemberFromSearch(currentActive);
                }
            } else if (e.key === 'Escape') {
                clearSearch();
                this.blur();
            }
        });
    }

    // Clear search functionality
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', clearSearch);
    }

    // Remove selected member functionality
    if (removeMemberBtn) {
        removeMemberBtn.addEventListener('click', function() {
            clearSearch();
            memberSelect.value = '';
            memberSelect.dispatchEvent(new Event('change'));
        });
    }

    // Set active item for keyboard navigation
    function setActiveItem(items, activeIndex) {
        items.forEach((item, index) => {
            if (index === activeIndex) {
                item.classList.add('active');
                item.scrollIntoView({ block: 'nearest' });
            } else {
                item.classList.remove('active');
            }
        });
    }
    }

    // Show search loading state
    function showSearchLoading() {
        if (searchLoading) {
            searchLoading.classList.remove('hidden');
        }
    }

    // Hide search loading state
    function hideSearchLoading() {
        if (searchLoading) {
            searchLoading.classList.add('hidden');
        }
    }

    // Member selection with info display
    if (memberSelect) {
        memberSelect.addEventListener('change', function() {
            updateMemberInfo(this);
        });
    }

    // Update form sections
    function updateFormSections(type) {
        if (type === 'income') {
            incomeCategories.classList.remove('hidden');
            expenseCategories.classList.add('hidden');
            memberSelection.classList.add('hidden');
            document.querySelector('select[name="income_category"]').required = true;
            document.querySelector('select[name="expense_category"]').required = false;
        } else {
            incomeCategories.classList.add('hidden');
            expenseCategories.classList.remove('hidden');
            memberSelection.classList.add('hidden');
            document.querySelector('select[name="income_category"]').required = false;
            document.querySelector('select[name="expense_category"]').required = true;
            document.querySelector('select[name="member_id"]').required = false;
        }
    }

    // Toggle member selection is now defined globally

    // Handle member search
    function handleMemberSearch(searchTerm) {
        if (searchTerm.length < 2) {
            memberSearchResults.classList.add('hidden');
            return;
        }

        const options = memberSelect.querySelectorAll('option');
        const results = [];

        options.forEach(option => {
            if (option.value) {
                const name = option.dataset.name.toLowerCase();
                const phone = (option.dataset.phone || '').toLowerCase();
                const department = (option.dataset.department || '').toLowerCase();
                const searchLower = searchTerm.toLowerCase();

                if (name.includes(searchLower) || phone.includes(searchLower) || department.includes(searchLower)) {
                    results.push(option);
                }
            }
        });

        displaySearchResults(results);
    }

    // Display inline search results
    function displaySearchResults(results) {
        if (results.length === 0) {
            searchResultsContainer.innerHTML = `
                <div class="text-center py-6">
                    <div class="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <p class="text-gray-500 text-sm">No members found</p>
                    <p class="text-gray-400 text-xs mt-1">Try a different name or phone number</p>
                </div>
            `;
        } else {
            const resultsHTML = results.map(option => {
                const name = option.dataset.name;
                const phone = option.dataset.phone || 'No phone';
                const department = option.dataset.department || 'No department';
                const picture = option.dataset.picture;
                const role = option.dataset.role || 'Member';

                return `
                    <div class="member-result-item p-3 bg-white border border-gray-200 rounded-md hover:border-primary hover:bg-primary-light hover:bg-opacity-10 cursor-pointer transition-all duration-200"
                         data-member-id="${option.value}"
                         data-name="${name}"
                         data-phone="${phone}"
                         data-picture="${picture}"
                         data-department="${department}"
                         data-role="${role}">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-bold text-sm">
                                ${picture ?
                                    `<img src="/icgc/uploads/members/${picture}" alt="${name}" class="w-10 h-10 rounded-full object-cover">` :
                                    name.charAt(0).toUpperCase()
                                }
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="font-medium text-gray-900 truncate">${name}</div>
                                <div class="text-sm text-gray-600 truncate">${phone} • ${department}</div>
                                <div class="text-xs text-gray-500">${role}</div>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="fas fa-plus-circle text-primary"></i>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            searchResultsContainer.innerHTML = resultsHTML;

            // Add click handlers to results
            searchResultsContainer.querySelectorAll('.member-result-item').forEach(item => {
                item.addEventListener('click', function() {
                    selectMemberFromSearch(this);
                });
            });
        }

        // Show results section
        memberSearchResults.classList.remove('hidden');

        // Update header with count
        const headerText = memberSearchResults.querySelector('h4');
        if (headerText && results.length > 0) {
            headerText.innerHTML = `<i class="fas fa-users text-primary mr-2"></i>Found ${results.length} member${results.length > 1 ? 's' : ''}`;
        }
    }

    // Select member from search results
    function selectMemberFromSearch(resultItem) {
        const memberId = resultItem.dataset.memberId;
        const memberName = resultItem.dataset.name;

        // Add selection animation
        resultItem.style.background = 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(22, 163, 74, 0.1) 100%)';
        resultItem.style.transform = 'scale(0.98)';

        setTimeout(() => {
            // Update the hidden select
            memberSelect.value = memberId;

            // Clear search input and hide results
            memberSearch.value = '';
            memberSearchResults.classList.add('hidden');

            // Update member info display
            updateMemberInfo(memberSelect);

            // Trigger change event for validation
            memberSelect.dispatchEvent(new Event('change'));
        }, 150);
    }

    // Clear search function
    function clearSearch() {
        memberSearch.value = '';
        memberSearchResults.classList.add('hidden');
        memberInfo.classList.add('hidden');
        memberSelect.value = '';

        // Reset search input styling
        memberSearch.style.borderColor = '';
        memberSearch.style.backgroundColor = '';
    }

    // Update member info display
    function updateMemberInfo(selectElement) {
        const selectedOption = selectElement.options[selectElement.selectedIndex];

        if (selectedOption.value) {
            const name = selectedOption.dataset.name;
            const phone = selectedOption.dataset.phone;
            const picture = selectedOption.dataset.picture;
            const department = selectedOption.dataset.department || 'No department';
            const role = selectedOption.dataset.role || 'Member';

            document.getElementById('member-display-name').textContent = name;
            document.getElementById('member-display-phone').textContent = `${phone || 'No phone'} • ${department} • ${role}`;

            const avatar = document.getElementById('member-avatar');
            const initial = document.getElementById('member-initial');

            if (picture) {
                avatar.innerHTML = `<img src="/icgc/uploads/members/${picture}" alt="${name}" class="w-10 h-10 rounded-full object-cover">`;
            } else {
                initial.textContent = name.charAt(0).toUpperCase();
                avatar.innerHTML = '';
                avatar.appendChild(initial);
            }

            memberInfo.classList.remove('hidden');
        } else {
            memberInfo.classList.add('hidden');
        }
    }

    // Initialize form state
    const checkedInput = document.querySelector('input[name="transaction_type"]:checked');
    if (checkedInput) {
        updateFormSections(checkedInput.value);

        // Check if income category is already selected and requires member
        if (checkedInput.value === 'income' && incomeCategorySelect && incomeCategorySelect.value) {
            const selectedOption = incomeCategorySelect.options[incomeCategorySelect.selectedIndex];
            const requiresMember = selectedOption.dataset.requiresMember === 'true';
            toggleMemberSelection(requiresMember);
        }
    }
});
</script>

<style>
/* Streamlined Finance Form Styles */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.category-section {
    transition: all 0.3s ease;
}

.category-section.hidden {
    display: none;
}

/* Form styling to match existing patterns */
input[type="radio"]:checked + div {
    background-color: #f0f9ff;
    border-color: #3b82f6;
}

input[type="radio"]:checked + div .bg-green-100 {
    background-color: #dcfce7;
}

input[type="radio"]:checked + div .bg-red-100 {
    background-color: #fee2e2;
}

/* Inline Member search results styling */
#member-search-results {
    transition: all 0.3s ease;
}

#search-results-container {
    max-height: 300px;
    overflow-y: auto;
}

/* Search input enhancements */
#member-search {
    transition: all 0.3s ease;
}

#member-search:focus {
    box-shadow: 0 0 0 3px rgba(63, 125, 88, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

#search-icon {
    transition: all 0.3s ease;
}

#search-loading {
    transition: all 0.3s ease;
}

/* Clear search button */
#clear-search {
    transition: all 0.2s ease;
}

#clear-search:hover {
    background-color: rgba(239, 68, 68, 0.1);
    border-radius: 4px;
    padding: 2px 4px;
}

/* Remove member button */
#remove-member {
    transition: all 0.2s ease;
    padding: 4px;
    border-radius: 4px;
}

#remove-member:hover {
    background-color: rgba(239, 68, 68, 0.1);
}

/* Inline member result items styling */
.member-result-item {
    transition: all 0.3s ease;
}

.member-result-item:hover,
.member-result-item.active {
    border-color: var(--primary) !important;
    background: rgba(63, 125, 88, 0.05) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(63, 125, 88, 0.15);
}

.member-result-item.active {
    border-color: var(--primary) !important;
    background: rgba(63, 125, 88, 0.1) !important;
}

/* Member avatar styling */
.member-result-item .w-10.h-10 {
    transition: all 0.3s ease;
}

.member-result-item:hover .w-10.h-10 {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(63, 125, 88, 0.3);
}

/* Plus icon animation */
.member-result-item .fa-plus-circle {
    transition: all 0.3s ease;
}

.member-result-item:hover .fa-plus-circle {
    transform: scale(1.2);
    color: var(--primary-dark);
}

/* Selected member info styling */
#selected-member-info {
    animation: slideIn 0.3s ease-out;
    border: 2px solid #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

/* Scrollbar styling for inline results */
#search-results-container::-webkit-scrollbar {
    width: 4px;
}

#search-results-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
}

#search-results-container::-webkit-scrollbar-thumb {
    background: var(--primary-light);
    border-radius: 2px;
}

#search-results-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

/* Enhanced member info display */
#selected-member-info {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Button hover effects */
button:hover, a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Focus states for better accessibility */
input:focus, select:focus, textarea:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(63, 125, 88, 0.1);
    border-color: var(--primary);
}

/* Primary color utilities */
.text-primary { color: var(--primary); }
.bg-primary { background-color: var(--primary); }
.bg-primary-light { background-color: var(--primary-light); }
.bg-primary-dark { background-color: var(--primary-dark); }
.border-primary { border-color: var(--primary); }
.border-primary-light { border-color: var(--primary-light); }

/* Focus ring for primary color */
.focus\\:ring-primary:focus {
    box-shadow: 0 0 0 3px rgba(63, 125, 88, 0.1);
}

.focus\\:border-primary:focus {
    border-color: var(--primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .grid-cols-1.md\\:grid-cols-2, .grid-cols-1.md\\:grid-cols-3 {
        grid-template-columns: 1fr;
    }

    .flex-col.sm\\:flex-row {
        flex-direction: column;
    }
}
</style>
