<?php
/**
 * Enhanced Router Class
 *
 * Handles routing, authentication, and authorization based on the routes.php configuration file.
 * Supports multiple authentication methods:
 * - Regular authentication: ['auth' => true] (default)
 * - Public routes: ['auth' => false]
 * - Token-based auth: ['token_auth' => true] (for QR attendance)
 * - Role-based access: ['role' => 'admin']
 * - CSRF protection: ['csrf' => true] or automatic for state-changing operations
 */

// Load required dependencies
require_once dirname(__DIR__) . '/config/database.php';
require_once __DIR__ . '/SecurityMiddleware.php';

class Router {
    private $routes;
    private $request;
    private $method;

    /**
     * Constructor
     *
     * @param array $routes Array of route definitions
     * @param string $request The request URI
     * @param string $method The HTTP method
     */
    public function __construct($routes, $request, $method = 'GET') {
        $this->routes = $routes;
        $this->request = $request;

        // Handle HTTP method override for RESTful forms
        if ($method === 'POST' && isset($_POST['_method'])) {
            $this->method = strtoupper($_POST['_method']);
        } else {
            $this->method = strtoupper($method);
        }
    }

    /**
     * Dispatch the request to the appropriate controller
     *
     * @return bool True if route was found and dispatched, false otherwise
     */
    public function dispatch() {
        foreach ($this->routes as $route) {
            // Destructure the route definition with defaults
            list($method, $pattern, $controllerName, $action, $options) = array_pad($route, 5, []);

            // Check if the HTTP method matches
            if ($this->method !== strtoupper($method)) {
                continue;
            }

            // Check if the request URI matches the pattern
            if (preg_match($pattern, $this->request, $matches)) {
                // Security and Authentication Checks
                $this->performSecurityChecks($options);

                // Remove the full match from the beginning of the array
                array_shift($matches);

                // Handle direct script includes (for legacy/simple API endpoints)
                if ($action === null && strpos($controllerName, '.php') !== false) {
                    $this->includeScript($controllerName);
                    return true;
                }

                // Handle controller method calls
                if ($this->callControllerAction($controllerName, $action, $matches)) {
                    return true;
                }
            }
        }

        return false; // No route found
    }

    /**
     * Perform security checks based on route options
     *
     * @param array $options Route options
     * @throws SecurityException If security checks fail
     */
    private function performSecurityChecks($options) {
        // Check if route requires authentication
        $isPublic = isset($options['auth']) && $options['auth'] === false;
        $requiresToken = isset($options['token_auth']) && $options['token_auth'] === true;
        $requiredRole = $options['role'] ?? null;
        $requiresCSRF = isset($options['csrf']) && $options['csrf'] === true;

        // Build middleware list based on route options
        $middlewareList = [];

        // Handle token-based authentication (for QR routes)
        if ($requiresToken) {
            $middlewareList[] = 'token_auth';
            $middlewareList[] = 'sanitize';
            $middlewareList[] = 'audit';
        } else if (!$isPublic) {
            // Handle regular authentication
            if (function_exists('check_auth')) {
                check_auth();
            }
            if (function_exists('check_user_status')) {
                check_user_status();
            }

            // Add role middleware if required
            if ($requiredRole) {
                $middlewareList[] = 'role';
            }

            $middlewareList[] = 'sanitize';
            $middlewareList[] = 'audit';
        }

        // Add CSRF protection for state-changing operations
        if ($requiresCSRF || $this->shouldValidateCSRF()) {
            $middlewareList[] = 'csrf';
        }

        // Add rate limiting for sensitive operations
        if ($requiredRole === 'admin' || $requiresToken) {
            $middlewareList[] = 'rate_limit';
        }

        // Apply middleware
        if (!empty($middlewareList)) {
            SecurityMiddleware::apply($middlewareList, $options);
        }
    }



    /**
     * Determine if CSRF validation should be performed
     *
     * @return bool Whether CSRF should be validated
     */
    private function shouldValidateCSRF() {
        // Validate CSRF for all state-changing operations
        return in_array($this->method, ['POST', 'PUT', 'PATCH', 'DELETE']);
    }

    /**
     * Include a script file (for legacy API endpoints)
     * 
     * @param string $scriptPath Path to the script file
     */
    private function includeScript($scriptPath) {
        $fullPath = dirname(__DIR__) . '/' . ltrim($scriptPath, '/');
        
        if (file_exists($fullPath)) {
            require_once $fullPath;
        } else {
            throw new Exception("Script file not found: $fullPath");
        }
    }

    /**
     * Call a controller action with parameters
     * 
     * @param string $controllerName Name of the controller class
     * @param string $action Name of the action method
     * @param array $params Parameters to pass to the action
     * @return bool True if successful, false otherwise
     */
    private function callControllerAction($controllerName, $action, $params = []) {
        try {
            // Handle special cases
            if ($controllerName === 'UserController' && $action === 'redirectToSettings') {
                redirect('settings#users');
            }

            // Load controller file
            $controllerFile = 'controllers/' . $controllerName . '.php';
            if (file_exists($controllerFile)) {
                require_once $controllerFile;
            }

            // Instantiate controller
            if (!class_exists($controllerName)) {
                throw new Exception("Controller class not found: $controllerName");
            }

            $controller = new $controllerName();

            // Check if action method exists
            if (!method_exists($controller, $action)) {
                throw new Exception("Action method not found: $controllerName::$action");
            }

            // Call the action with parameters (full match already removed by array_shift above)
            // No need to slice again since array_shift already removed the full match
            call_user_func_array([$controller, $action], $params);
            return true;

        } catch (Exception $e) {
            error_log("Router error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all routes (for debugging)
     * 
     * @return array
     */
    public function getRoutes() {
        return $this->routes;
    }

    /**
     * Get current request info (for debugging)
     * 
     * @return array
     */
    public function getRequestInfo() {
        return [
            'request' => $this->request,
            'method' => $this->method
        ];
    }
}
