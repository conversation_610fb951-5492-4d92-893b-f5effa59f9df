<?php
/**
 * Parent Search API
 * Search for parents and their children for family check-in
 */

// Set content type to JSON
header('Content-Type: application/json');

// Include bootstrap to ensure proper initialization
require_once dirname(__DIR__) . '/bootstrap.php';

// Start session
session_start();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['search_term'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Search term is required']);
    exit;
}

$search_term = trim($input['search_term']);

if (empty($search_term)) {
    echo json_encode(['success' => false, 'message' => 'Please enter a search term']);
    exit;
}

try {
    // Initialize database connection
    $database = new Database();
    $conn = $database->getConnection();

    // Search for parent by name, phone, or member ID
    $query = "SELECT m.*, 
                     TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
              FROM members m 
              WHERE m.member_status = 'active'
              AND (
                  CONCAT(m.first_name, ' ', m.last_name) LIKE :search_name
                  OR m.phone_number LIKE :search_phone
                  OR m.id = :search_id
                  OR m.first_name LIKE :search_first
                  OR m.last_name LIKE :search_last
              )
              AND TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) >= 18
              ORDER BY 
                  CASE 
                      WHEN m.id = :search_id THEN 1
                      WHEN CONCAT(m.first_name, ' ', m.last_name) = :exact_name THEN 2
                      WHEN m.phone_number = :exact_phone THEN 3
                      ELSE 4
                  END,
                  m.first_name, m.last_name
              LIMIT 10";

    $stmt = $conn->prepare($query);
    
    // Prepare search parameters
    $search_name = "%{$search_term}%";
    $search_phone = "%{$search_term}%";
    $search_first = "%{$search_term}%";
    $search_last = "%{$search_term}%";
    $exact_name = $search_term;
    $exact_phone = $search_term;
    
    // Try to parse as member ID (numeric)
    $search_id = is_numeric($search_term) ? (int)$search_term : 0;

    $stmt->bindParam(':search_name', $search_name);
    $stmt->bindParam(':search_phone', $search_phone);
    $stmt->bindParam(':search_id', $search_id);
    $stmt->bindParam(':search_first', $search_first);
    $stmt->bindParam(':search_last', $search_last);
    $stmt->bindParam(':exact_name', $exact_name);
    $stmt->bindParam(':exact_phone', $exact_phone);
    
    $stmt->execute();
    $parents = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($parents)) {
        echo json_encode([
            'success' => false, 
            'message' => 'No parent found with that information. Please check your name, phone number, or member ID.'
        ]);
        exit;
    }

    // If multiple parents found, return the first one (best match)
    $parent = $parents[0];

    // Get children for this parent
    require_once '../models/FamilyRelationship.php';
    $familyRelationship = new FamilyRelationship($conn);
    
    $children_stmt = $familyRelationship->getChildrenByParent($parent['id']);
    $children = $children_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Check for medical alerts for children
    foreach ($children as &$child) {
        // Check if child has medical information
        $medical_query = "SELECT COUNT(*) as has_medical FROM children_medical_info WHERE child_id = :child_id";
        $medical_stmt = $conn->prepare($medical_query);
        $medical_stmt->bindParam(':child_id', $child['id']);
        $medical_stmt->execute();
        $medical_result = $medical_stmt->fetch(PDO::FETCH_ASSOC);
        
        $child['medical_alerts'] = $medical_result['has_medical'] > 0;
    }

    echo json_encode([
        'success' => true,
        'parent' => [
            'id' => $parent['id'],
            'first_name' => $parent['first_name'],
            'last_name' => $parent['last_name'],
            'phone_number' => $parent['phone_number'],
            'email' => $parent['email'],
            'age' => $parent['age']
        ],
        'children' => $children,
        'total_found' => count($parents)
    ]);

} catch (Exception $e) {
    error_log("Error in parent search API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'error' => 'An error occurred while searching. Please try again.'
    ]);
}
?>
