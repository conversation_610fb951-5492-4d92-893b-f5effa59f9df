<?php
// Page title and active page are set by controller
// Start output buffering
ob_start();
?>

<div class="container mx-auto fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-100">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-8 text-white relative overflow-hidden">
            <div class="absolute right-0 top-0 opacity-10">
                <i class="fas fa-users text-9xl transform -rotate-12 translate-x-8 -translate-y-8"></i>
            </div>
            <h1 class="text-3xl font-bold">Members Directory</h1>
            <p class="mt-2 opacity-90 max-w-2xl">Manage and view all church members in one place. Search, filter, and perform actions on member records.</p>
        </div>
        <div class="p-6 bg-white">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Quick Stats Cards -->
                <div class="flex flex-wrap gap-4 mb-4 md:mb-0">
                    <div class="bg-gradient-to-br from-primary-light to-primary rounded-xl shadow-lg p-4 flex items-center w-48 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <div class="rounded-full bg-white bg-opacity-30 p-3 mr-3 text-white">
                            <i class="fas fa-users fa-lg"></i>
                        </div>
                        <div>
                            <div class="text-3xl font-bold text-white"><?php echo $member_count; ?></div>
                            <div class="text-sm text-white text-opacity-90">Total Members</div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-xl shadow-lg p-4 flex items-center w-48 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <div class="rounded-full bg-white bg-opacity-30 p-3 mr-3 text-white">
                            <i class="fas fa-user-check fa-lg"></i>
                        </div>
                        <div>
                            <div class="text-3xl font-bold text-white">
                                <?php
                                    $activeCount = 0;
                                    foreach($members as $member) {
                                        if($member['member_status'] === 'active') $activeCount++;
                                    }
                                    echo $activeCount;
                                ?>
                            </div>
                            <div class="text-sm text-white text-opacity-90">Active</div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl shadow-lg p-4 flex items-center w-48 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <div class="rounded-full bg-white bg-opacity-30 p-3 mr-3 text-white">
                            <i class="fas fa-user-plus fa-lg"></i>
                        </div>
                        <div>
                            <div class="text-3xl font-bold text-white">
                                <?php
                                    $newCount = 0;
                                    $thirtyDaysAgo = date('Y-m-d', strtotime('-30 days'));
                                    foreach($members as $member) {
                                        if($member['created_at'] >= $thirtyDaysAgo) $newCount++;
                                    }
                                    echo $newCount;
                                ?>
                            </div>
                            <div class="text-sm text-white text-opacity-90">New (30d)</div>
                        </div>
                    </div>
                </div>
                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-3">
                    <a href="<?php echo url('members/add'); ?>" class="bg-gradient-to-r from-primary to-primary-dark text-white py-3 px-5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-md font-medium">
                        <i class="fas fa-user-plus mr-2"></i> Add New Member
                    </a>
                    <a href="<?php echo url('groups'); ?>" class="bg-gradient-to-r from-[#3F7D58] to-[#2c5a3f] hover:from-[#2c5a3f] hover:to-[#3F7D58] text-white py-3 px-5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-md font-medium">
                        <i class="fas fa-layer-group mr-2"></i> Groups Management
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Search and Filters -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-100">
        <div class="bg-gray-50 border-b border-gray-100 px-4 py-2">
            <h2 class="text-sm font-semibold text-gray-700 flex items-center">
                <i class="fas fa-filter text-primary mr-2"></i> Search & Filters
            </h2>
        </div>
        <div class="p-3">
            <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-3">
                <!-- Compact Search Bar -->
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-primary"></i>
                        </div>
                        <input type="text" id="search-input" placeholder="Search members..."
                            value="<?php echo htmlspecialchars($search ?? ''); ?>"
                            class="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary shadow-sm transition-all duration-300"
                            autocomplete="off">
                        <div id="search-loading" class="absolute inset-y-0 right-0 flex items-center px-4 text-primary hidden">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                </div>

                <!-- Compact Filters -->
                <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                    <div class="relative w-full">
                        <div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                            <i class="fas fa-sitemap text-primary text-xs"></i>
                        </div>
                        <select id="department-filter" class="appearance-none pl-7 pr-8 py-2 text-sm w-full sm:w-40 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary shadow-sm transition-all duration-300 bg-white">
                            <option value="">All Departments</option>
                            <?php if (!empty($departments)): ?>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo htmlspecialchars($dept); ?>"
                                            <?php echo ($department ?? '') === $dept ? 'selected' : ''; ?>>
                                        <?php echo ucfirst(str_replace('_', ' ', $dept)); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none text-primary">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                        <div id="department-loading" class="absolute inset-y-0 right-0 flex items-center pr-6 text-primary hidden">
                            <i class="fas fa-spinner fa-spin text-xs"></i>
                        </div>
                    </div>

                    <div class="relative w-full">
                        <div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                            <i class="fas fa-user-check text-primary text-xs"></i>
                        </div>
                        <select id="status-filter" class="appearance-none pl-7 pr-8 py-2 text-sm w-full sm:w-32 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary shadow-sm transition-all duration-300 bg-white">
                            <option value="" <?php echo ($status ?? 'active') === '' ? 'selected' : ''; ?>>All Status</option>
                            <option value="active" <?php echo ($status ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($status ?? 'active') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="transferred" <?php echo ($status ?? 'active') === 'transferred' ? 'selected' : ''; ?>>Transferred</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none text-primary">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                        <div id="status-loading" class="absolute inset-y-0 right-0 flex items-center pr-6 text-primary hidden">
                            <i class="fas fa-spinner fa-spin text-xs"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Enhanced Members Table -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
        <div class="bg-gray-50 border-b border-gray-100 px-6 py-4 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0">
            <h2 class="text-lg font-semibold text-gray-700 flex items-center">
                <i class="fas fa-users text-primary mr-2"></i> Members List
            </h2>
            <div class="flex items-center space-x-4">
                <div class="text-sm bg-primary px-3 py-1 rounded-full flex items-center text-white">
                    <i class="fas fa-list mr-1"></i>
                    <span id="members-count" class="font-medium"><?php echo isset($pagination) ? $pagination['total_members'] : count($members ?? []); ?></span> members
                    <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                        <span class="ml-2 text-xs opacity-75">
                            (Page <?php echo $pagination['current_page']; ?> of <?php echo $pagination['total_pages']; ?>)
                        </span>
                    <?php endif; ?>

                </div>
                <div class="text-xs text-gray-500 italic">
                    Results update in real-time as you search and filter
                </div>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr class="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-primary uppercase tracking-wider">Name</th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-primary uppercase tracking-wider">Contact</th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-primary uppercase tracking-wider">Department</th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-primary uppercase tracking-wider">Role</th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-primary uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-primary uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="members-table-body" class="bg-white divide-y divide-gray-200">
                    <?php if (!empty($members)): ?>
                        <?php foreach ($members as $member): ?>
                            <?php
                            // Check if member is newly registered (within last 7 days)
                            $isNewMember = false;
                            if (!empty($member['created_at'])) {
                                $createdDate = new DateTime($member['created_at']);
                                $now = new DateTime();
                                $daysDifference = $now->diff($createdDate)->days;
                                $isNewMember = $daysDifference <= 7;
                            }
                            ?>
                            <tr class="hover:bg-gray-50 transition-all duration-200 group border-b border-gray-100 <?php echo $isNewMember ? 'bg-green-50 border-l-4 border-green-400' : ''; ?>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-14 w-14 relative group-hover:scale-105 transition-transform duration-300">
                                            <?php if (!empty($member['profile_picture']) && file_exists($member['profile_picture'])): ?>
                                                <img class="h-14 w-14 rounded-full object-cover border-2 border-white shadow-md"
                                                     src="<?php echo url($member['profile_picture']); ?>"
                                                     alt="<?php echo $member['first_name'] . ' ' . $member['last_name']; ?>">
                                            <?php else: ?>
                                                <div class="h-14 w-14 rounded-full bg-gradient-to-r from-primary-light to-primary flex items-center justify-center shadow-md">
                                                    <span class="text-white font-bold text-lg"><?php echo substr($member['first_name'], 0, 1) . substr($member['last_name'], 0, 1); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($member['member_status'] === 'active'): ?>
                                                <div class="absolute bottom-0 right-0 h-4 w-4 rounded-full bg-green-500 border-2 border-white ring-2 ring-green-100"></div>
                                            <?php elseif ($member['member_status'] === 'inactive'): ?>
                                                <div class="absolute bottom-0 right-0 h-4 w-4 rounded-full bg-yellow-500 border-2 border-white ring-2 ring-yellow-100"></div>
                                            <?php elseif ($member['member_status'] === 'transferred'): ?>
                                                <div class="absolute bottom-0 right-0 h-4 w-4 rounded-full bg-blue-500 border-2 border-white ring-2 ring-blue-100"></div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 group-hover:text-primary transition-colors duration-200 flex items-center">
                                                <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                                                <?php if ($isNewMember): ?>
                                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 animate-pulse">
                                                        <i class="fas fa-star mr-1"></i>NEW
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-xs text-gray-500 flex items-center mt-1">
                                                <span class="inline-flex items-center justify-center bg-gray-100 text-gray-500 h-4 w-4 rounded-full mr-1">
                                                    <i class="fas fa-briefcase text-xs"></i>
                                                </span>
                                                <?php echo htmlspecialchars($member['occupation'] ?: 'Not specified'); ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 flex items-center group-hover:text-primary transition-colors duration-200">
                                        <span class="inline-flex items-center justify-center bg-green-100 text-green-600 h-6 w-6 rounded-full mr-2">
                                            <i class="fas fa-phone text-xs"></i>
                                        </span>
                                        <?php if ($member['phone_number']): ?>
                                            <a href="tel:<?php echo $member['phone_number']; ?>" class="hover:underline">
                                                <?php echo $member['phone_number']; ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-gray-400 italic">Not available</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-xs text-gray-500 flex items-center mt-2">
                                        <span class="inline-flex items-center justify-center bg-blue-100 text-blue-600 h-5 w-5 rounded-full mr-2">
                                            <i class="fas fa-envelope text-xs"></i>
                                        </span>
                                        <?php if ($member['email']): ?>
                                            <a href="mailto:<?php echo $member['email']; ?>" class="hover:underline truncate max-w-[150px]">
                                                <?php echo $member['email']; ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-gray-400 italic">No email</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $deptInfo = $department_styles[$member['department']] ?? ['None', 'bg-gray-100 text-gray-800'];
                                        $deptName = $deptInfo[0];
                                        $deptClass = $deptInfo[1];
                                    ?>
                                    <span class="px-3 py-1 inline-flex text-xs leading-5 font-medium rounded-full <?php echo $deptClass; ?> group-hover:shadow-sm transition-all duration-200">
                                        <?php echo $deptName; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $roleIcons = [
                                            'member' => '<i class="fas fa-user"></i>',
                                            'worker' => '<i class="fas fa-hard-hat"></i>',
                                            'leader' => '<i class="fas fa-user-tie"></i>',
                                            'pastor' => '<i class="fas fa-pray"></i>',
                                            'deacon' => '<i class="fas fa-church"></i>',
                                            'pastors_wife' => '<i class="fas fa-female"></i>',
                                            'administration' => '<i class="fas fa-briefcase"></i>'
                                        ];

                                        $roleNames = [
                                            'member' => 'Member',
                                            'worker' => 'Worker',
                                            'leader' => 'Leader',
                                            'pastor' => 'Pastor',
                                            'deacon' => 'Deacon',
                                            'pastors_wife' => 'Pastor\'s Wife',
                                            'administration' => 'Administration'
                                        ];

                                        $roleIcon = $roleIcons[$member['role']] ?? '<i class="fas fa-user"></i>';
                                        $roleName = $roleNames[$member['role']] ?? 'Member';
                                    ?>
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center justify-center bg-primary bg-opacity-10 text-primary h-6 w-6 rounded-full mr-2">
                                            <?php echo $roleIcon; ?>
                                        </span>
                                        <span class="text-sm text-gray-700"><?php echo $roleName; ?></span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $statusInfo = [
                                            'active' => ['bg-green-100 text-green-800 border-green-200', '<i class="fas fa-check-circle mr-1"></i>'],
                                            'inactive' => ['bg-yellow-100 text-yellow-800 border-yellow-200', '<i class="fas fa-exclamation-circle mr-1"></i>'],
                                            'transferred' => ['bg-blue-100 text-blue-800 border-blue-200', '<i class="fas fa-exchange-alt mr-1"></i>']
                                        ];

                                        $statusData = $statusInfo[$member['member_status']] ?? ['bg-gray-100 text-gray-800 border-gray-200', '<i class="fas fa-question-circle mr-1"></i>'];
                                        $statusClass = $statusData[0];
                                        $statusIcon = $statusData[1];
                                    ?>
                                    <span class="px-3 py-1.5 inline-flex items-center text-xs leading-5 font-medium rounded-full border shadow-sm group-hover:shadow transition-all duration-200 <?php echo $statusClass; ?>">
                                        <?php echo $statusIcon; ?>
                                        <?php echo ucfirst($member['member_status']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2 opacity-80 group-hover:opacity-100 transition-opacity duration-200">
                                        <a href="<?php echo url('members/' . $member['id']); ?>"
                                           class="p-2 bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 hover:shadow-md transition-all duration-200 transform hover:scale-105"
                                           title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo url('members/' . $member['id'] . '/edit'); ?>"
                                           class="p-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 hover:shadow-md transition-all duration-200 transform hover:scale-105"
                                           title="Edit Member">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button onclick="confirmDelete(<?php echo $member['id']; ?>, '<?php echo addslashes($member['first_name'] . ' ' . $member['last_name']); ?>')"
                                           class="p-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 hover:shadow-md transition-all duration-200 transform hover:scale-105"
                                           title="Delete Member">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="px-6 py-16 text-center">
                                <div class="flex flex-col items-center justify-center text-gray-500 max-w-md mx-auto">
                                    <div class="w-24 h-24 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-4">
                                        <i class="fas fa-users text-4xl text-primary"></i>
                                    </div>
                                    <p class="text-xl font-semibold text-gray-700">No members found</p>
                                    <p class="text-sm mt-2 text-gray-500 text-center">Your church member directory is empty. Add your first member to start building your congregation database.</p>
                                    <a href="<?php echo url('members/add'); ?>" class="mt-6 inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white text-sm font-medium rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md">
                                        <i class="fas fa-user-plus mr-2"></i> Add New Member
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Enhanced Pagination -->
        <div class="bg-white px-6 py-4 flex items-center justify-between border-t border-gray-100">
            <div class="flex-1 flex justify-between sm:hidden">
                <button id="mobile-prev-page" class="relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark transition-colors duration-300 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-chevron-left mr-2"></i> Previous
                </button>
                <button id="mobile-next-page" class="ml-3 relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark transition-colors duration-300 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed">
                    Next <i class="fas fa-chevron-right ml-2"></i>
                </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700 flex items-center">
                        <i class="fas fa-list-ol text-primary mr-2"></i>
                        Showing <span id="page-start" class="font-medium mx-1"><?php echo isset($pagination) ? (($pagination['current_page'] - 1) * $pagination['limit']) + 1 : 1; ?></span> to <span id="page-end" class="font-medium mx-1"><?php echo isset($pagination) ? min($pagination['current_page'] * $pagination['limit'], $pagination['total_members']) : count($members ?? []); ?></span> of <span class="font-medium mx-1 total-members-count"><?php echo isset($pagination) ? $pagination['total_members'] : count($members ?? []); ?></span> results
                    </p>
                </div>
                <div class="pagination-wrapper border border-gray-200 p-2 rounded-lg">
                    <nav id="pagination-container" class="relative z-0 inline-flex rounded-lg shadow-sm min-h-[40px]" aria-label="Pagination">
                        <!-- Pagination will be generated dynamically -->
                    </nav>
                    <div id="fallback-pagination" class="mt-2 text-sm text-gray-500">
                        <span>Page <span id="current-page-display"><?php echo isset($pagination) ? $pagination['current_page'] : 1; ?></span> of <span id="total-pages-display"><?php echo isset($pagination) ? $pagination['total_pages'] : 1; ?></span></span>
                        <button id="prev-page-fallback" class="ml-2 px-2 py-1 bg-gray-100 rounded hover:bg-gray-200 disabled:opacity-50">Prev</button>
                        <button id="next-page-fallback" class="ml-2 px-2 py-1 bg-gray-100 rounded hover:bg-gray-200 disabled:opacity-50">Next</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden z-50 transition-opacity duration-300">
    <div class="bg-white rounded-xl p-6 max-w-md mx-auto shadow-2xl transform transition-all duration-300 scale-95 opacity-0" id="modalContent">
        <div class="text-center mb-6">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">Confirm Delete</h2>
            <p class="text-gray-600">Are you sure you want to delete the member <strong id="deleteMemberName" class="text-gray-800"></strong>? This action cannot be undone.</p>
        </div>
        <div class="flex justify-center space-x-4">
            <button id="cancelDelete" class="px-5 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-300 font-medium flex items-center">
                <i class="fas fa-times mr-2"></i> Cancel
            </button>
            <form id="deleteForm" action="<?php echo url('members/delete'); ?>" method="POST">
                <input type="hidden" name="id" id="deleteMemberId">
                <?php echo csrf_field(); ?>
                <button type="submit" class="px-5 py-2.5 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-300 font-medium flex items-center">
                    <i class="fas fa-trash mr-2"></i> Delete
                </button>
            </form>
        </div>
    </div>
</div>

<script>
    // Global variables for filters and pagination
    let currentSearch = '<?php echo htmlspecialchars($search ?? ''); ?>';
    let currentDepartment = '<?php echo htmlspecialchars($department ?? ''); ?>';
    let currentStatus = '<?php echo htmlspecialchars($status ?? 'active'); ?>';
    let currentPage = <?php echo $pagination['current_page'] ?? 1; ?>;
    let totalPages = <?php echo $pagination['total_pages'] ?? 1; ?>;
    let itemsPerPage = <?php echo $pagination['limit'] ?? 25; ?>;
    let debounceTimeout = null;
    const apiBaseUrl = '<?php echo url('members/search'); ?>';

    // Helper function to check if member is recently registered
    function isRecentlyRegistered(createdAt) {
        if (!createdAt) return false;

        const createdDate = new Date(createdAt);
        const now = new Date();
        const daysDifference = (now - createdDate) / (1000 * 60 * 60 * 24);

        return daysDifference <= 7; // Consider "new" if registered within last 7 days
    }

    // Load department and role names for display
    async function loadDepartmentAndRoleNames() {
        try {
            // Load departments
            const deptResponse = await fetch('<?php echo url('api/departments.php'); ?>');
            const deptData = await deptResponse.json();

            if (deptData.success) {
                window.departmentNames = {};
                deptData.data.forEach(dept => {
                    window.departmentNames[dept.name] = dept.display_name;
                });
            }

            // Load roles
            const roleResponse = await fetch('<?php echo url('api/roles.php'); ?>');
            const roleData = await roleResponse.json();

            if (roleData.success) {
                window.roleNames = {};
                roleData.data.forEach(role => {
                    window.roleNames[role.name] = role.display_name;
                });
            }
        } catch (error) {
            console.error('Error loading department and role names:', error);
            // Fallback to empty objects
            window.departmentNames = {};
            window.roleNames = {};
        }
    }

    // Initialize real-time search and filters
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, initializing members page');
        console.log('Initial pagination values:', {
            currentPage: currentPage,
            totalPages: totalPages,
            itemsPerPage: itemsPerPage,
            totalMembers: <?php echo isset($pagination) ? $pagination['total_members'] : 'unknown'; ?>
        });

        // Load department and role names first
        loadDepartmentAndRoleNames();

        initializeSearch();
        initializeFilters();
        addTableRowEffects();

        // Initial fetch of members
        // Check if pagination container exists
        const paginationContainer = document.getElementById('pagination-container');
        console.log('Pagination container found:', paginationContainer);

        // Only fetch if no members are already loaded server-side
        const hasServerMembers = <?php echo !empty($members) ? 'true' : 'false'; ?>;
        if (!hasServerMembers) {
            // Set a small delay to ensure all DOM elements are fully loaded
            setTimeout(() => {
                console.log('No server members, performing initial fetch');
                fetchMembers();
            }, 100);
        } else {
            console.log('Members already loaded server-side, skipping initial fetch');
            // Update pagination if needed
            updatePaginationDisplay(currentPage, totalPages);

            // Set up pagination buttons for server-side loaded data
            if (totalPages > 1) {
                generatePagination(currentPage, totalPages);
            }

            // Set up fallback pagination buttons
            setupFallbackPagination();
        }
    });

    // Initialize search functionality
    function initializeSearch() {
        const searchInput = document.getElementById('search-input');

        searchInput.addEventListener('input', function() {
            // Clear any existing timeout
            if (debounceTimeout) {
                clearTimeout(debounceTimeout);
            }

            // Show loading indicator
            document.getElementById('search-loading').classList.remove('hidden');

            // Set a new timeout to debounce the search
            debounceTimeout = setTimeout(() => {
                currentSearch = this.value.trim();
                currentPage = 1; // Reset to first page when search changes
                fetchMembers();
            }, 500); // Wait 500ms after user stops typing
        });
    }

    // Initialize filter functionality
    function initializeFilters() {
        // Department filter
        document.getElementById('department-filter').addEventListener('change', function() {
            currentDepartment = this.value;
            currentPage = 1; // Reset to first page when filter changes
            document.getElementById('department-loading').classList.remove('hidden');
            fetchMembers();
        });

        // Status filter
        document.getElementById('status-filter').addEventListener('change', function() {
            currentStatus = this.value;
            currentPage = 1; // Reset to first page when filter changes
            document.getElementById('status-loading').classList.remove('hidden');
            fetchMembers();
        });
    }

    // Fetch members based on current filters
    function fetchMembers() {
        // Show loading skeleton
        showLoadingSkeleton();

        // Build query parameters
        let queryParams = [];

        // Add pagination parameters
        queryParams.push(`page=${currentPage}`);
        queryParams.push(`limit=${itemsPerPage}`);

        if (currentSearch) {
            queryParams.push(`search=${encodeURIComponent(currentSearch)}`);
        }

        if (currentDepartment) {
            queryParams.push(`department=${encodeURIComponent(currentDepartment)}`);
        }

        if (currentStatus) {
            queryParams.push(`status=${encodeURIComponent(currentStatus)}`);
        }

        // Build the URL
        const url = `${apiBaseUrl}${queryParams.length ? '?' + queryParams.join('&') : ''}`;

        // Show loading indicators
        if (currentSearch) document.getElementById('search-loading').classList.remove('hidden');
        if (currentDepartment) document.getElementById('department-loading').classList.remove('hidden');
        if (currentStatus) document.getElementById('status-loading').classList.remove('hidden');

        // Fetch data
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(response => {
                console.log('API Response:', response); // Debug log

                // Check if we have a valid response structure
                if (!response || typeof response !== 'object') {
                    console.error('Invalid API response:', response);
                    return;
                }

                // The API returns members in 'members' property
                const members = Array.isArray(response.members) ? response.members : [];
                updateMembersTable(members);

                // Update pagination info if available
                if (response.pagination) {
                    const pagination = response.pagination;

                    // Update total count
                    document.querySelectorAll('.total-members-count').forEach(el => {
                        el.textContent = pagination.total_members;
                    });

                    // Update pagination variables
                    totalPages = pagination.total_pages || 1;
                    console.log('Total Pages set to:', totalPages); // Debug log

                    // Calculate start and end item numbers
                    const start = (currentPage - 1) * itemsPerPage + 1;
                    const end = Math.min(currentPage * itemsPerPage, pagination.total_members);

                    // Update the page info
                    document.getElementById('page-start').textContent = pagination.total_members > 0 ? start : 0;
                    document.getElementById('page-end').textContent = end;

                    // Update fallback pagination display
                    document.getElementById('current-page-display').textContent = currentPage;
                    document.getElementById('total-pages-display').textContent = totalPages;

                    const prevFallback = document.getElementById('prev-page-fallback');
                    const nextFallback = document.getElementById('next-page-fallback');

                    if (prevFallback && nextFallback) {
                        prevFallback.disabled = currentPage <= 1;
                        nextFallback.disabled = currentPage >= totalPages;

                        prevFallback.onclick = () => changePage(currentPage - 1);
                        nextFallback.onclick = () => changePage(currentPage + 1);
                    }

                    // Generate pagination controls
                    generatePagination(currentPage, totalPages);

                    // Update mobile pagination buttons
                    const prevButton = document.getElementById('mobile-prev-page');
                    const nextButton = document.getElementById('mobile-next-page');

                    prevButton.disabled = currentPage <= 1;
                    nextButton.disabled = currentPage >= totalPages;

                    prevButton.onclick = () => changePage(currentPage - 1);
                    nextButton.onclick = () => changePage(currentPage + 1);
                }

                hideLoadingIndicators();
            })
            .catch(error => {
                console.error('Error fetching members:', error);
                hideLoadingIndicators();

                // Show error message to user
                const tableBody = document.getElementById('members-table-body');
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-10 text-center">
                            <div class="flex flex-col items-center justify-center text-red-500">
                                <i class="fas fa-exclamation-circle text-4xl mb-3"></i>
                                <p class="text-lg">Error loading members</p>
                                <p class="text-sm mt-1">Please try again later</p>
                            </div>
                        </td>
                    </tr>
                `;
            });
    }

    // Update the members table with new data
    function updateMembersTable(members) {
        const tableBody = document.getElementById('members-table-body');
        const membersCount = document.getElementById('members-count');

        // Update the count (will be updated by pagination info later)
        // membersCount.textContent = members.length;

        // Clear the table
        tableBody.innerHTML = '';

        // If no members found
        if (members.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="px-6 py-10 text-center">
                        <div class="flex flex-col items-center justify-center text-gray-500">
                            <i class="fas fa-search text-4xl mb-3 text-gray-300"></i>
                            <p class="text-lg">No members found</p>
                            <p class="text-sm mt-1">Try adjusting your search or filters</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        // Add members to the table
        members.forEach(member => {
            // Create profile picture HTML
            let profilePicHtml = '';
            if (member.profile_picture && member.profile_picture !== '') {
                profilePicHtml = `
                    <img class="h-12 w-12 rounded-full object-cover border-2 border-white shadow"
                         src="<?php echo BASE_URL; ?>${member.profile_picture}"
                         alt="${member.first_name} ${member.last_name}">
                `;
            } else {
                profilePicHtml = `
                    <div class="h-12 w-12 rounded-full bg-gradient-to-r from-primary-light to-primary flex items-center justify-center shadow">
                        <span class="text-white font-bold text-lg">${member.first_name.charAt(0)}${member.last_name.charAt(0)}</span>
                    </div>
                `;
            }

            // Create status badge HTML
            let statusClass = '';
            switch (member.member_status) {
                case 'active':
                    statusClass = 'bg-green-100 text-green-800 border-green-200';
                    break;
                case 'inactive':
                    statusClass = 'bg-yellow-100 text-yellow-800 border-yellow-200';
                    break;
                case 'transferred':
                    statusClass = 'bg-blue-100 text-blue-800 border-blue-200';
                    break;
                default:
                    statusClass = 'bg-gray-100 text-gray-800 border-gray-200';
            }

            // Get department and role names from global cache
            const departmentName = window.departmentNames ? window.departmentNames[member.department] : null;
            const roleName = window.roleNames ? window.roleNames[member.role] : null;

            // Check if member is newly registered (within last 7 days)
            const isNewMember = isRecentlyRegistered(member.created_at);
            const newMemberBadge = isNewMember ? '<span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 animate-pulse"><i class="fas fa-star mr-1"></i>NEW</span>' : '';

            // Create row HTML
            const rowHtml = `
                <tr class="hover:bg-gray-50 transition-colors duration-150 ${isNewMember ? 'bg-green-50 border-l-4 border-green-400' : ''}">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-12 w-12 relative">
                                ${profilePicHtml}
                                ${member.member_status === 'active' ? '<div class="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-white"></div>' : ''}
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900 hover:text-primary transition-colors duration-200 flex items-center">
                                    ${member.first_name} ${member.last_name}
                                    ${newMemberBadge}
                                </div>
                                <div class="text-xs text-gray-500 flex items-center mt-1">
                                    <i class="fas fa-briefcase text-gray-400 mr-1"></i>
                                    ${member.occupation || 'Not specified'}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 flex items-center">
                            <i class="fas fa-phone text-gray-400 mr-2"></i>
                            ${member.phone_number || 'Not available'}
                        </div>
                        <div class="text-xs text-gray-500 flex items-center mt-1">
                            <i class="fas fa-envelope text-gray-400 mr-2"></i>
                            ${member.email || 'No email'}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            ${departmentName || 'None'}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            ${roleName || 'Member'}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-medium rounded-full border ${statusClass}">
                            ${member.member_status.charAt(0).toUpperCase() + member.member_status.slice(1)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-1">
                            <a href="<?php echo url('members/'); ?>${member.id}"
                               class="p-1.5 bg-indigo-50 text-indigo-600 rounded-md hover:bg-indigo-100 transition-colors duration-200"
                               title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="<?php echo url('members/'); ?>${member.id}/edit"
                               class="p-1.5 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors duration-200"
                               title="Edit Member">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="confirmDelete(${member.id}, '${member.first_name} ${member.last_name}')"
                               class="p-1.5 bg-red-50 text-red-600 rounded-md hover:bg-red-100 transition-colors duration-200"
                               title="Delete Member">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;

            tableBody.innerHTML += rowHtml;
        });

        // Re-add hover effects to the new rows
        addTableRowEffects();
    }

    // Show loading skeleton
    function showLoadingSkeleton() {
        const tableBody = document.getElementById('members-table-body');
        if (!tableBody) return;

        // Create skeleton rows
        let skeletonHTML = '';
        for (let i = 0; i < 8; i++) {
            skeletonHTML += `
                <tr class="animate-pulse border-b border-gray-100">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-14 w-14">
                                <div class="h-14 w-14 rounded-full bg-gray-200"></div>
                            </div>
                            <div class="ml-4 space-y-2">
                                <div class="h-4 bg-gray-200 rounded w-32"></div>
                                <div class="h-3 bg-gray-200 rounded w-24"></div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="space-y-2">
                            <div class="h-4 bg-gray-200 rounded w-28"></div>
                            <div class="h-3 bg-gray-200 rounded w-32"></div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="h-6 bg-gray-200 rounded-full w-20"></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="h-4 bg-gray-200 rounded w-16"></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="h-6 bg-gray-200 rounded-full w-16"></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex space-x-1">
                            <div class="h-8 w-8 bg-gray-200 rounded"></div>
                            <div class="h-8 w-8 bg-gray-200 rounded"></div>
                            <div class="h-8 w-8 bg-gray-200 rounded"></div>
                        </div>
                    </td>
                </tr>
            `;
        }
        tableBody.innerHTML = skeletonHTML;
    }

    // Hide loading skeleton and show actual content
    function hideLoadingSkeleton() {
        // Loading indicators will be hidden when content is loaded
    }

    // Hide all loading indicators
    function hideLoadingIndicators() {
        document.getElementById('search-loading').classList.add('hidden');
        document.getElementById('department-loading').classList.add('hidden');
        document.getElementById('status-loading').classList.add('hidden');
    }

    // Update pagination display without fetching new data
    function updatePaginationDisplay(currentPage, totalPages) {
        // Update pagination controls
        generatePagination(currentPage, totalPages);

        // Update page info displays
        const currentPageDisplay = document.getElementById('current-page-display');
        const totalPagesDisplay = document.getElementById('total-pages-display');

        if (currentPageDisplay) currentPageDisplay.textContent = currentPage;
        if (totalPagesDisplay) totalPagesDisplay.textContent = totalPages;
    }

    // Add hover effects to table rows
    function addTableRowEffects() {
        const tableRows = document.querySelectorAll('#members-table-body tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('shadow-sm');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('shadow-sm');
            });
        });
    }

    // Generate pagination controls
    function generatePagination(currentPage, totalPages) {
        console.log('Generating pagination:', currentPage, totalPages); // Debug log
        const container = document.getElementById('pagination-container');
        if (!container) {
            console.error('Pagination container not found!');
            return;
        }

        container.innerHTML = '';

        // Don't show pagination if there's only one page
        if (totalPages <= 1) {
            console.log('Only one page, not showing pagination');
            return;
        }

        console.log('Creating pagination buttons');

        // Previous button
        const prevButton = document.createElement('button');
        prevButton.className = `relative inline-flex items-center px-3 py-2 rounded-l-lg border border-gray-200 bg-white text-sm font-medium ${currentPage > 1 ? 'text-gray-500 hover:bg-gray-50' : 'text-gray-300 cursor-not-allowed'} transition-colors duration-300`;
        prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevButton.disabled = currentPage <= 1;
        prevButton.addEventListener('click', () => changePage(currentPage - 1));
        container.appendChild(prevButton);

        // Page numbers
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        // Adjust start page if we're near the end
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // First page if not visible
        if (startPage > 1) {
            const firstPageBtn = document.createElement('button');
            firstPageBtn.className = 'bg-white border-gray-200 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors duration-300';
            firstPageBtn.textContent = '1';
            firstPageBtn.addEventListener('click', () => changePage(1));
            container.appendChild(firstPageBtn);

            // Ellipsis if needed
            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'relative inline-flex items-center px-4 py-2 border border-gray-200 bg-white text-sm font-medium text-gray-700';
                ellipsis.textContent = '...';
                container.appendChild(ellipsis);
            }
        }

        // Page buttons
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            if (i === currentPage) {
                pageBtn.className = 'z-10 bg-primary-light bg-opacity-20 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors duration-300';
                pageBtn.setAttribute('aria-current', 'page');
            } else {
                pageBtn.className = 'bg-white border-gray-200 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors duration-300';
            }
            pageBtn.textContent = i.toString();
            pageBtn.addEventListener('click', () => changePage(i));
            container.appendChild(pageBtn);
        }

        // Last page if not visible
        if (endPage < totalPages) {
            // Ellipsis if needed
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'relative inline-flex items-center px-4 py-2 border border-gray-200 bg-white text-sm font-medium text-gray-700';
                ellipsis.textContent = '...';
                container.appendChild(ellipsis);
            }

            const lastPageBtn = document.createElement('button');
            lastPageBtn.className = 'bg-white border-gray-200 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors duration-300';
            lastPageBtn.textContent = totalPages.toString();
            lastPageBtn.addEventListener('click', () => changePage(totalPages));
            container.appendChild(lastPageBtn);
        }

        // Next button
        const nextButton = document.createElement('button');
        nextButton.className = `relative inline-flex items-center px-3 py-2 rounded-r-lg border border-gray-200 bg-white text-sm font-medium ${currentPage < totalPages ? 'text-gray-500 hover:bg-gray-50' : 'text-gray-300 cursor-not-allowed'} transition-colors duration-300`;
        nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextButton.disabled = currentPage >= totalPages;
        nextButton.addEventListener('click', () => changePage(currentPage + 1));
        container.appendChild(nextButton);
    }

    // Set up fallback pagination buttons
    function setupFallbackPagination() {
        const prevFallback = document.getElementById('prev-page-fallback');
        const nextFallback = document.getElementById('next-page-fallback');

        if (prevFallback && nextFallback) {
            prevFallback.disabled = currentPage <= 1;
            nextFallback.disabled = currentPage >= totalPages;

            prevFallback.onclick = () => changePage(currentPage - 1);
            nextFallback.onclick = () => changePage(currentPage + 1);
        }
    }

    // Change page and fetch new data
    function changePage(page) {
        if (page < 1 || page > totalPages) return;

        currentPage = page;
        fetchMembers();

        // Scroll to top of table
        document.querySelector('.bg-white.rounded-xl.shadow-md.overflow-hidden').scrollIntoView({ behavior: 'smooth' });
    }



    // Enhanced delete confirmation with animations
    function confirmDelete(id, name) {
        const modal = document.getElementById('deleteModal');
        const modalContent = document.getElementById('modalContent');
        const deleteIdInput = document.getElementById('deleteMemberId');
        const memberNameSpan = document.getElementById('deleteMemberName');

        // Set the member name and ID
        memberNameSpan.textContent = name;
        deleteIdInput.value = id;

        // Show modal with animation
        modal.classList.remove('hidden');
        setTimeout(() => {
            modalContent.classList.add('scale-100', 'opacity-100');
            modalContent.classList.remove('scale-95', 'opacity-0');
        }, 10);

        // Close modal with animation
        document.getElementById('cancelDelete').addEventListener('click', function() {
            modalContent.classList.remove('scale-100', 'opacity-100');
            modalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        });

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modalContent.classList.remove('scale-100', 'opacity-100');
                modalContent.classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    modal.classList.add('hidden');
                }, 300);
            }
        });
    }
</script>

<?php
// Get the contents of the output buffer
$content = ob_get_clean();

// Include the layout template
include 'views/layouts/main.php';
?>
