<?php
/**
 * Ministry Department Model for Church Program & Activities Planner
 */

class MinistryDepartment {
    // Database connection and table name
    private $conn;
    private $table_name = "ministry_departments";

    // Object properties
    public $id;
    public $name;
    public $description;
    public $head_pastor_id;
    public $contact_email;
    public $contact_phone;
    public $budget_allocation;
    public $is_active;
    public $created_at;
    public $updated_at;

    // Error handling
    public $error;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db = null) {
        $this->conn = $db;

        // If no database connection is provided, get one from the Database class
        if ($this->conn === null) {
            require_once 'config/database.php';
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }

    /**
     * Get all departments
     *
     * @param bool $active_only
     * @return PDOStatement
     */
    public function getAll($active_only = true) {
        $query = "SELECT md.*, 
                         CONCAT(m.first_name, ' ', m.last_name) as head_pastor_name,
                         m.email as head_pastor_email,
                         m.phone_number as head_pastor_phone
                  FROM " . $this->table_name . " md
                  LEFT JOIN members m ON md.head_pastor_id = m.id";
        
        if ($active_only) {
            $query .= " WHERE md.is_active = 1";
        }
        
        $query .= " ORDER BY md.name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get active departments
     *
     * @return PDOStatement
     */
    public function getActive() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE is_active = 1 ORDER BY name ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get department by ID
     *
     * @param int $id
     * @return array|false
     */
    public function getById($id) {
        $query = "SELECT md.*, 
                         CONCAT(m.first_name, ' ', m.last_name) as head_pastor_name,
                         m.email as head_pastor_email,
                         m.phone_number as head_pastor_phone
                  FROM " . $this->table_name . " md
                  LEFT JOIN members m ON md.head_pastor_id = m.id
                  WHERE md.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create new department
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (name, description, head_pastor_id, contact_email, contact_phone, budget_allocation, is_active)
                  VALUES
                  (:name, :description, :head_pastor_id, :contact_email, :contact_phone, :budget_allocation, :is_active)";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->contact_email = htmlspecialchars(strip_tags($this->contact_email));
        $this->contact_phone = htmlspecialchars(strip_tags($this->contact_phone));

        // Set defaults
        if (!isset($this->budget_allocation)) {
            $this->budget_allocation = 0.00;
        }
        if (!isset($this->is_active)) {
            $this->is_active = 1;
        }

        // Bind parameters
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':head_pastor_id', $this->head_pastor_id);
        $stmt->bindParam(':contact_email', $this->contact_email);
        $stmt->bindParam(':contact_phone', $this->contact_phone);
        $stmt->bindParam(':budget_allocation', $this->budget_allocation);
        $stmt->bindParam(':is_active', $this->is_active);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Update department
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET name = :name,
                      description = :description,
                      head_pastor_id = :head_pastor_id,
                      contact_email = :contact_email,
                      contact_phone = :contact_phone,
                      budget_allocation = :budget_allocation,
                      is_active = :is_active,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->contact_email = htmlspecialchars(strip_tags($this->contact_email));
        $this->contact_phone = htmlspecialchars(strip_tags($this->contact_phone));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':head_pastor_id', $this->head_pastor_id);
        $stmt->bindParam(':contact_email', $this->contact_email);
        $stmt->bindParam(':contact_phone', $this->contact_phone);
        $stmt->bindParam(':budget_allocation', $this->budget_allocation);
        $stmt->bindParam(':is_active', $this->is_active);

        return $stmt->execute();
    }

    /**
     * Get program count for department
     *
     * @param int $department_id
     * @return PDOStatement
     */
    public function getProgramCount($department_id) {
        $query = "SELECT COUNT(*) FROM church_programs WHERE department_id = :department_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':department_id', $department_id);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Delete department
     *
     * @return bool
     */
    public function delete() {
        try {
            // Clear any previous errors
            $this->error = null;

            // Check if department is being used by any programs
            $check_query = "SELECT COUNT(*) as count FROM church_programs WHERE department_id = :id";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(':id', $this->id);
            $check_stmt->execute();
            $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

            if ($result['count'] > 0) {
                $this->error = 'Cannot delete department. It is being used by ' . $result['count'] . ' program(s).';
                return false; // Cannot delete department that's in use
            }

            $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $this->id);

            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Error deleting department. Please try again.';
                return false;
            }
        } catch (Exception $e) {
            $this->error = 'Database error: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * Get departments with program count and budget info
     *
     * @return PDOStatement
     */
    public function getDepartmentsWithStats() {
        $query = "SELECT md.*, 
                         CONCAT(m.first_name, ' ', m.last_name) as head_pastor_name,
                         COUNT(p.id) as program_count,
                         COUNT(CASE WHEN p.status = 'planned' THEN 1 END) as planned_count,
                         COUNT(CASE WHEN p.status = 'in_progress' THEN 1 END) as in_progress_count,
                         COUNT(CASE WHEN p.status = 'completed' THEN 1 END) as completed_count,
                         COALESCE(SUM(p.budget_allocated), 0) as total_budget_allocated,
                         COALESCE(SUM(p.budget_spent), 0) as total_budget_spent
                  FROM " . $this->table_name . " md
                  LEFT JOIN members m ON md.head_pastor_id = m.id
                  LEFT JOIN church_programs p ON md.id = p.department_id
                  WHERE md.is_active = 1
                  GROUP BY md.id
                  ORDER BY md.name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get members in department
     *
     * @param int $department_id
     * @return PDOStatement
     */
    public function getDepartmentMembers($department_id = null) {
        $dept_id = $department_id ?? $this->id;

        // Get department name from ministry_departments table
        $dept_query = "SELECT name FROM " . $this->table_name . " WHERE id = :id";
        $dept_stmt = $this->conn->prepare($dept_query);
        $dept_stmt->bindParam(':id', $dept_id);
        $dept_stmt->execute();
        $dept_result = $dept_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$dept_result) {
            // Return empty result if department not found
            $empty_query = "SELECT * FROM members WHERE 1=0";
            $stmt = $this->conn->prepare($empty_query);
            $stmt->execute();
            return $stmt;
        }

        // Try to find matching department in the departments table by display name
        $dept_lookup_query = "SELECT name FROM departments WHERE display_name = :display_name AND is_active = 1";
        $dept_lookup_stmt = $this->conn->prepare($dept_lookup_query);
        $dept_lookup_stmt->bindParam(':display_name', $dept_result['name']);
        $dept_lookup_stmt->execute();
        $dept_lookup_result = $dept_lookup_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$dept_lookup_result) {
            // Return empty result if no matching department found
            $empty_query = "SELECT * FROM members WHERE 1=0";
            $stmt = $this->conn->prepare($empty_query);
            $stmt->execute();
            return $stmt;
        }

        $member_dept = $dept_lookup_result['name'];

        $query = "SELECT * FROM members
                  WHERE department = :department AND member_status = 'active'
                  ORDER BY first_name ASC, last_name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':department', $member_dept);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Validate department data
     *
     * @return array
     */
    public function validate() {
        $errors = [];

        if (empty($this->name)) {
            $errors[] = "Department name is required";
        }

        if (!empty($this->contact_email) && !filter_var($this->contact_email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Contact email must be a valid email address";
        }

        if (!empty($this->budget_allocation) && $this->budget_allocation < 0) {
            $errors[] = "Budget allocation cannot be negative";
        }

        // Check for duplicate name (excluding current record if updating)
        $check_query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE name = :name";
        if (!empty($this->id)) {
            $check_query .= " AND id != :id";
        }

        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(':name', $this->name);
        if (!empty($this->id)) {
            $check_stmt->bindParam(':id', $this->id);
        }
        $check_stmt->execute();
        $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

        if ($result['count'] > 0) {
            $errors[] = "A department with this name already exists";
        }

        return $errors;
    }

    /**
     * Toggle department status
     *
     * @param int $status
     * @return bool
     */
    public function toggleStatus($status) {
        $query = "UPDATE " . $this->table_name . " SET is_active = :status WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $this->id);
        return $stmt->execute();
    }
}
