# Database Migrations

This directory contains database migration scripts for the ICGC Emmanuel Temple Church Management System, managed by **Phinx**.

## Purpose

Migrations are used to manage the database schema in a structured, version-controlled, and repeatable way. They allow you to evolve your database schema over time across different development environments.

## Usage

All database schema changes should be run from the **Command Line Interface (CLI)** using the Phinx tool.

### Common Commands

**Run all pending migrations:**
```bash
vendor/bin/phinx migrate
```

**Create a new migration file:**
```bash
vendor/bin/phinx create MyNewMigrationName
```

This will create the database and import the initial `schema.sql`.
