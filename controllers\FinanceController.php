<?php
/**
 * Finance Controller
 */

require_once 'models/Finance.php';
require_once 'utils/validation.php';
require_once 'helpers/functions.php';
require_once 'controllers/BaseRestfulController.php';

class FinanceController extends BaseRestfulController {
    private $database;
    private $finance;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->finance = new Finance($this->database->getConnection());
    }

    /**
     * Display finances list
     *
     * @return void
     */
    public function index() {
        try {
            // Get all finances for display
            $stmt = $this->finance->getAll();
            $finances = $stmt->fetchAll();

            // Calculate totals using updated methods that include custom categories
            $total_income = $this->finance->getTotalIncome();
            $total_expense = $this->finance->getTotalExpenses();
            $balance = $total_income - $total_expense;

            // Get monthly data for charts
            $monthly_data = $this->finance->getMonthlyData(6);

            // Get income categories for pie chart
            $income_categories = $this->finance->getIncomeBySubcategory();

            // Get expense categories for pie chart
            $expense_categories = $this->finance->getExpenseBySubcategory();

            // Get monthly averages
            $averages = $this->finance->getMonthlyAverages();
            $monthly_avg_income = $averages['avg_income'];
            $monthly_avg_expense = $averages['avg_expense'];

            // Get growth rates
            $growth_rates = $this->finance->getGrowthRates();
            $income_growth = $growth_rates['income_growth'];
            $expense_growth = $growth_rates['expense_growth'];

            // Get top categories
            $top_categories = $this->finance->getTopCategories();
            $top_income_category = $top_categories['top_income']['category'];
            $top_income_amount = $top_categories['top_income']['amount'];
            $top_income_percentage = $top_categories['top_income']['percentage'];
            $top_expense_category = $top_categories['top_expense']['category'];
            $top_expense_amount = $top_categories['top_expense']['amount'];
            $top_expense_percentage = $top_categories['top_expense']['percentage'];

            // Get top tithe contributors
            $top_tithe_contributors = $this->finance->getTopTitheContributors(5);

            // Get recent transactions
            $recent_transactions = array_slice($finances, 0, 5);

            // Get custom categories for dynamic dashboard links (exclude core categories)
            require_once 'models/CustomFinanceCategory.php';
            $customCategory = new CustomFinanceCategory($this->database->getConnection());

            // Get all categories (including inactive) in a single query for better performance
            $allCategories = $customCategory->getAll(false); // Include inactive categories

            // Group categories by type for better performance
            $allMemberPaymentCategories = [];
            $allGeneralIncomeCategories = [];
            $allExpenseCategories = [];

            foreach ($allCategories as $category) {
                switch ($category->category_type) {
                    case 'member_payments':
                        $allMemberPaymentCategories[] = $category;
                        break;
                    case 'general_income':
                        $allGeneralIncomeCategories[] = $category;
                        break;
                    case 'expenses':
                        $allExpenseCategories[] = $category;
                        break;
                }
            }

            // Filter out core categories (they have hardcoded dashboard buttons)
            $memberPaymentCategories = array_filter($allMemberPaymentCategories, function($cat) {
                return !isset($cat->is_core) || !$cat->is_core;
            });

            $generalIncomeCategories = array_filter($allGeneralIncomeCategories, function($cat) {
                return !isset($cat->is_core) || !$cat->is_core;
            });

            $expenseCategories = array_filter($allExpenseCategories, function($cat) {
                return !isset($cat->is_core) || !$cat->is_core;
            });

            // Calculate category statistics for enhanced dashboard
            $categoryStats = [
                'member_payments' => [
                    'total' => count($memberPaymentCategories),
                    'active' => count(array_filter($memberPaymentCategories, function($cat) { return $cat->is_active; })),
                    'inactive' => count(array_filter($memberPaymentCategories, function($cat) { return !$cat->is_active; }))
                ],
                'general_income' => [
                    'total' => count($generalIncomeCategories),
                    'active' => count(array_filter($generalIncomeCategories, function($cat) { return $cat->is_active; })),
                    'inactive' => count(array_filter($generalIncomeCategories, function($cat) { return !$cat->is_active; }))
                ],
                'expenses' => [
                    'total' => count($expenseCategories),
                    'active' => count(array_filter($expenseCategories, function($cat) { return $cat->is_active; })),
                    'inactive' => count(array_filter($expenseCategories, function($cat) { return !$cat->is_active; }))
                ]
            ];

            // Set page title and active page
            $page_title = getPageTitle('Finances');
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/finances/index.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading finance dashboard: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('dashboard');
        }
    }

    /**
     * Display finance form
     *
     * @return void
     */
    public function create() {
        // Set page title and active page
        $page_title = 'Add Finance - ICGC Emmanuel Temple';
        $active_page = 'finances';

        // UNIFIED CATEGORY SYSTEM - Load all categories from database
        require_once 'models/CustomFinanceCategory.php';
        $customCategory = new CustomFinanceCategory($this->database->getConnection());

        // Get all active categories by type (no filtering needed - unified system)
        $memberPaymentCategories = $customCategory->getByType('member_payments');
        $generalIncomeCategories = $customCategory->getByType('general_income');
        $expenseCategories = $customCategory->getByType('expenses');

        // Start output buffering
        ob_start();

        // Load the new tab-based view
        require_once 'views/finances/create_tabs.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display legacy finance form (for backward compatibility)
     *
     * @return void
     */
    public function createLegacy() {
        // Set page title and active page
        $page_title = getPageTitle('Add Finance');
        $active_page = 'finances';

        // Start output buffering
        ob_start();

        // Load the legacy view
        require_once 'views/finances/create.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store finance record
     *
     * @return void
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('finances/add');
            return;
        }

        try {
            // Check if new schema is available
            $conn = $this->database->getConnection();
            $checkColumns = "SHOW COLUMNS FROM finances LIKE 'transaction_type'";
            $stmt = $conn->prepare($checkColumns);
            $stmt->execute();
            $hasNewSchema = $stmt->rowCount() > 0;

            if ($hasNewSchema) {
                // Process transaction data for new schema (model will handle validation)
                $transactionData = $this->processTransactionData($_POST);

                // Create transaction with new schema (model will handle all validation)
                if ($this->createTransaction($transactionData)) {
                    $_SESSION['flash_message'] = 'Transaction recorded successfully';
                    $_SESSION['flash_type'] = 'success';

                    // Redirect to the specific category dashboard
                    $dashboardUrl = $this->getCategoryDashboardUrl($transactionData['category']);
                    redirect($dashboardUrl);
                } else {
                    // Get detailed error message from model
                    $errorMessage = $this->finance->error ?? 'Failed to create transaction';
                    $_SESSION['flash_message'] = $errorMessage;
                    $_SESSION['flash_type'] = 'danger';
                    $_SESSION['form_data'] = $_POST;
                    redirect('finances/add');
                }
            } else {
                throw new Exception('Database schema not supported. Please update your database.');
            }

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances/add');
        }
    }



    /**
     * Process and normalize transaction data
     */
    private function processTransactionData($data) {
        $transactionType = $data['transaction_type'];

        // Determine category and subcategory
        if ($transactionType === 'income') {
            $category = $data['income_category'];
            $subcategory = null;
        } else {
            $category = $data['expense_category'];
            $subcategory = $this->finance->getExpenseSubcategory($category);
        }

        // Generate reference number if not provided
        $referenceNumber = !empty($data['reference_number'])
            ? $data['reference_number']
            : $this->finance->generateReferenceNumber($transactionType);

        return [
            'transaction_type' => $transactionType,
            'category' => $category,
            'subcategory' => $subcategory,
            'amount' => floatval($data['amount']),
            'description' => trim($data['description'] ?? ''),
            'transaction_date' => $data['transaction_date'],
            'reference_number' => $referenceNumber,
            'payment_method' => $data['payment_method'] ?? 'cash',
            'member_id' => !empty($data['member_id']) ? intval($data['member_id']) : null,
            'recorded_by' => $this->getCurrentUserId()
        ];
    }

    /**
     * Create transaction with enhanced logic
     */
    private function createTransaction($data) {
        // Set finance properties
        foreach ($data as $key => $value) {
            if (property_exists($this->finance, $key)) {
                $this->finance->$key = $value;
            }
        }

        $this->finance->created_at = date('Y-m-d H:i:s');
        $this->finance->updated_at = date('Y-m-d H:i:s');

        return $this->finance->create();
    }

    /**
     * Get current user ID with fallback
     */
    private function getCurrentUserId() {
        if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])) {
            return $_SESSION['user_id'];
        }

        // Fallback to first admin user
        $stmt = $this->database->getConnection()->prepare(
            "SELECT id FROM users WHERE role = 'admin' LIMIT 1"
        );
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_OBJ);

        return $admin ? $admin->id : 1;
    }

    /**
     * Enhanced reports with new data structure (with fallback for old schema)
     */
    public function enhancedReports() {
        try {
            $conn = $this->database->getConnection();

            // Check if new columns exist
            $checkColumns = "SHOW COLUMNS FROM finances LIKE 'transaction_type'";
            $stmt = $conn->prepare($checkColumns);
            $stmt->execute();
            $hasNewColumns = $stmt->rowCount() > 0;

            // Use the updated Finance model methods for accurate calculations
            $total_income = $this->finance->getTotalIncome();
            $total_expenses = $this->finance->getTotalExpenses();

            // Get total transaction count
            $countQuery = "SELECT COUNT(*) as total_transactions FROM finances";
            $stmt = $conn->prepare($countQuery);
            $stmt->execute();
            $total_transactions = $stmt->fetch(PDO::FETCH_OBJ)->total_transactions ?? 0;

            // Get income by category
            $incomeQuery = "SELECT category, SUM(amount) as total, COUNT(*) as count
                           FROM finances
                           WHERE transaction_type = 'income'
                           GROUP BY category
                           ORDER BY total DESC";
            $stmt = $conn->prepare($incomeQuery);
            $stmt->execute();
            $income_by_category = $stmt->fetchAll(PDO::FETCH_OBJ);

            // Get expenses by subcategory
            $expenseQuery = "SELECT COALESCE(subcategory, 'General') as subcategory, SUM(amount) as total, COUNT(*) as count
                            FROM finances
                            WHERE transaction_type = 'expense'
                            GROUP BY subcategory
                            ORDER BY total DESC";
            $stmt = $conn->prepare($expenseQuery);
            $stmt->execute();
            $expenses_by_subcategory = $stmt->fetchAll(PDO::FETCH_OBJ);

            // Get payment methods analysis
            $paymentQuery = "SELECT payment_method, SUM(amount) as total, COUNT(*) as count
                            FROM finances
                            WHERE payment_method IS NOT NULL
                            GROUP BY payment_method
                            ORDER BY total DESC";
            $stmt = $conn->prepare($paymentQuery);
            $stmt->execute();
            $payment_methods = $stmt->fetchAll(PDO::FETCH_OBJ);

            // Get monthly trends
            $monthlyQuery = "SELECT
                DATE_FORMAT(transaction_date, '%Y-%m') as month,
                SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END) as net_amount,
                COUNT(*) as transaction_count
                FROM finances
                GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
                ORDER BY month DESC
                LIMIT 6";
            $stmt = $conn->prepare($monthlyQuery);
            $stmt->execute();
            $monthly_trends = $stmt->fetchAll(PDO::FETCH_OBJ);

            // Get recent transactions
            $recentQuery = "SELECT * FROM finances
                           ORDER BY created_at DESC
                           LIMIT 10";
            $stmt = $conn->prepare($recentQuery);
            $stmt->execute();
            $recent_transactions = $stmt->fetchAll(PDO::FETCH_OBJ);

            // Set page variables
            $page_title = 'Enhanced Financial Reports - ICGC Emmanuel Temple';
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load enhanced reports view
            require_once 'views/finances/enhanced_reports.php';

            // Get content
            $content = ob_get_clean();

            // Include layout
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading reports: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances');
        }
    }

    /**
     * Display edit finance form
     *
     * @return void
     */
    public function edit() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Finance ID is required', 'danger');
            redirect('finances');
            exit;
        }

        $id = sanitize($_GET['id']);

        // Get finance record
        if (!$this->finance->getById($id)) {
            set_flash_message('Finance record not found', 'danger');
            redirect('finances');
            exit;
        }

        // Set page title and active page
        $page_title = getPageTitle('Edit Finance');
        $active_page = 'finances';

        // Generate the category dashboard URL for the back button
        $categoryDashboardUrl = $this->getCategoryDashboardUrl($this->finance->category);

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/finances/edit.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Show single finance record (RESTful)
     *
     * @param int|null $id Finance ID from route parameter
     * @return void
     */
    public function show($id = null) {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        // Check role permissions
        if (!in_array($_SESSION['role'], ['admin', 'staff'])) {
            set_flash_message('Access denied. Staff privileges required.', 'danger');
            redirect('dashboard');
            return;
        }

        $finance_id = $this->getId($id, 'id', 'id');
        if (!$finance_id) {
            $this->handleResponse(false, 'Invalid finance ID', 'finances');
            return;
        }

        $finance = $this->finance->getById($finance_id);
        if (!$finance) {
            $this->handleResponse(false, 'Finance record not found', 'finances');
            return;
        }

        // For AJAX requests, return JSON
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'data' => $finance]);
            exit;
        }

        $active_page = 'finances';
        $page_title = 'View Finance Record';

        require_once 'views/finances/show.php';
    }

    /**
     * Update finance record
     *
     * @return void
     */
    public function update($id = null) {
        // Support both RESTful (PUT) and legacy (POST) methods
        if (!$this->checkHttpMethod(['POST', 'PUT'])) {
            redirect('finances');
            return;
        }

        if (!$this->validateCsrf('finances')) {
            return;
        }

        // Get ID from multiple sources (supports both legacy and RESTful)
        $finance_id = $this->getId($id, 'id', 'id');
        if (!$finance_id) {
            $this->handleResponse(false, 'Finance ID is required', 'finances');
            return;
        }

        // Model will handle all validation

            // Get finance record
            $id = sanitize($_POST['id']);
            if (!$this->finance->getById($id)) {
                set_flash_message('Finance record not found', 'danger');
                redirect('finances');
                exit;
            }

            // Set finance properties
            $this->finance->id = $id;
            $this->finance->category = sanitize($_POST['category']);
            $this->finance->amount = sanitize($_POST['amount']);
            $this->finance->description = sanitize($_POST['description'] ?? '');
            $this->finance->transaction_date = sanitize($_POST['transaction_date']);

            // Handle member_id for all transactions
            if (isset($_POST['member_id']) && !empty($_POST['member_id'])) {
                // If member_id is provided in the form, use it
                $this->finance->member_id = intval($_POST['member_id']);
            } else {
                // If no member_id is provided, set to null (removes member assignment)
                $this->finance->member_id = null;
            }

            $this->finance->updated_at = date('Y-m-d H:i:s');

            // Update finance record (model will handle all validation)
            if ($this->finance->update()) {
                // Set success message
                set_flash_message('Finance record updated successfully', 'success');

                // Redirect to the specific category dashboard
                $dashboardUrl = $this->getCategoryDashboardUrl($this->finance->category);
                redirect($dashboardUrl);
            } else {
                // Get detailed error message from model
                $errorMessage = $this->finance->error ?? 'Failed to update finance record';
                set_flash_message($errorMessage, 'danger');
                $_SESSION['form_data'] = $_POST;
                redirect('finances/edit?id=' . $id);
            }
    }

    /**
     * Delete finance record
     *
     * @return void
     */
    public function delete($id = null) {
        // Support both RESTful (DELETE) and legacy (POST) methods
        if (!$this->checkHttpMethod(['POST', 'DELETE'])) {
            redirect('finances');
            return;
        }

        if (!$this->validateCsrf('finances')) {
            return;
        }

        // Get ID from multiple sources (supports both legacy and RESTful)
        $finance_id = $this->getId($id, 'id', 'id');
        if (!$finance_id) {
            $this->handleResponse(false, 'Finance ID is required', 'finances');
            return;
        }

        $id = sanitize($_GET['id']);

        // Get finance record
        if (!$this->finance->getById($id)) {
            set_flash_message('Finance record not found', 'danger');
            redirect('finances');
            exit;
        }

        // Store category for redirect before deleting
        $category = $this->finance->category;

        // Delete finance record
        if ($this->finance->delete()) {
            set_flash_message('Finance record deleted successfully', 'success');
        } else {
            set_flash_message('Failed to delete finance record', 'danger');
        }

        // Determine redirect URL
        $redirectUrl = 'finances'; // default

        // Check if redirect parameter is provided
        if (isset($_GET['redirect']) && !empty($_GET['redirect'])) {
            $redirectUrl = 'finances/' . $_GET['redirect'];
        } elseif (isset($_GET['category']) && !empty($_GET['category'])) {
            // If category parameter is provided, redirect to category dashboard
            $dashboardUrl = $this->getCategoryDashboardUrl($_GET['category']);
            $redirectUrl = $dashboardUrl;
        } elseif (!empty($category)) {
            // If no redirect parameter but we have the transaction category, redirect to its dashboard
            $dashboardUrl = $this->getCategoryDashboardUrl($category);
            $redirectUrl = $dashboardUrl;
        }

        redirect($redirectUrl);
    }

    /**
     * Generate finance report
     *
     * @return void
     */
    public function report() {
        // Set page title and active page
        $page_title = getPageTitle('Finance Report');
        $active_page = 'finances';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/finances/report.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display tithe transactions
     *
     * @return void
     */
    public function tithes() {
        try {
            // Get all tithe transactions
            $tithes = $this->finance->getAllTithes();

            // Get all members who have paid tithes
            $tithe_members = [];

            // Include Member model
            require_once 'models/Member.php';
            $db = new Database();
            $conn = $db->getConnection();

            // Create Member model instance
            $memberModel = new Member($conn);

            // Get all members for the view
            $members = $memberModel->getAll();

            // Process each tithe record
            foreach ($tithes as $tithe) {


                if (!empty($tithe['member_id'])) {
                    $member_id = $tithe['member_id'];

                    // Initialize member record if not already set
                    if (!isset($tithe_members[$member_id])) {
                        // Get member details including profile picture
                        $memberDetails = new Member($conn);
                        if ($memberDetails->getById($member_id)) {
                            $tithe_members[$member_id] = [
                                'id' => $member_id,
                                'name' => $memberDetails->first_name . ' ' . $memberDetails->last_name,
                                'profile_picture' => $memberDetails->profile_picture,
                                'phone_number' => $memberDetails->phone_number,
                                'total_tithes' => 0,
                                'last_tithe_date' => null
                            ];
                        }
                    }

                    // Update tithe totals and last date
                    if (isset($tithe_members[$member_id])) {
                        $tithe_members[$member_id]['total_tithes'] += $tithe['amount'];

                        // Update last tithe date if it's more recent
                        $tithe_date = strtotime($tithe['transaction_date']);
                        if ($tithe_members[$member_id]['last_tithe_date'] === null ||
                            $tithe_date > strtotime($tithe_members[$member_id]['last_tithe_date'])) {
                            $tithe_members[$member_id]['last_tithe_date'] = $tithe['transaction_date'];
                        }
                    }
                }
            }



        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading tithe data: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances');
            return;
        }

        try {
            // Set page title and active page
            $page_title = getPageTitle('Tithe Tracking');
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/finances/tithes.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading tithe page: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances');
        }
    }

    /**
     * Display tithe details for a specific member
     *
     * @return void
     */
    public function memberTithes() {
        try {
            // Check if member ID is provided
            if (!isset($_GET['id']) || empty($_GET['id'])) {
                set_flash_message('Member ID is required', 'danger');
                redirect('finances/tithes');
                exit;
            }

            $member_id = intval($_GET['id']);

            // Get member details
            require_once 'models/Member.php';
            $db = new Database();
            $conn = $db->getConnection();
            $member = new Member($conn);

            if (!$member->getById($member_id)) {
                set_flash_message('Member not found', 'danger');
                redirect('finances/tithes');
                exit;
            }

            // Get tithe transactions for this member
            $tithes = $this->finance->getTithesByMember($member_id);

            // Calculate total tithes
            $total_tithes = 0;
            foreach ($tithes as $tithe) {
                $total_tithes += $tithe['amount'];
            }

            // Set page title and active page
            $page_title = 'Tithe History: ' . $member->first_name . ' ' . $member->last_name . ' - ICGC Emmanuel Temple';
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/finances/member_tithes.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading member tithe data: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances/tithes');
        }
    }

    /**
     * Display welfare details for a specific member
     *
     * @return void
     */
    public function memberWelfare() {
        try {
            // Check if member ID is provided
            if (!isset($_GET['id']) || empty($_GET['id'])) {
                set_flash_message('Member ID is required', 'danger');
                redirect('finances/welfare');
                exit;
            }

            $member_id = intval($_GET['id']);

            // Get member details
            require_once 'models/Member.php';
            $db = new Database();
            $conn = $db->getConnection();
            $member = new Member($conn);

            if (!$member->getById($member_id)) {
                set_flash_message('Member not found', 'danger');
                redirect('finances/welfare');
                exit;
            }

            // Get welfare transactions for this member
            $welfare_payments = $this->finance->getWelfareByMember($member_id);

            // Calculate total welfare
            $total_welfare = 0;
            foreach ($welfare_payments as $payment) {
                $total_welfare += $payment['amount'];
            }

            // Set page title and active page
            $page_title = 'Welfare History: ' . $member->first_name . ' ' . $member->last_name . ' - ICGC Emmanuel Temple';
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/finances/member_welfare.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading member welfare data: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances/welfare');
        }
    }

    /**
     * Display welfare transactions
     *
     * @return void
     */
    public function welfare() {
        try {
            // Get all welfare transactions
            $welfare_payments = $this->finance->getAllWelfare();

            // Get all members who have paid welfare
            $welfare_members = [];

            // Include Member model
            require_once 'models/Member.php';
            $db = new Database();
            $conn = $db->getConnection();

            // Create Member model instance
            $memberModel = new Member($conn);

            // Get all members for the view
            $members = $memberModel->getAll();
            // Note: getAll() already returns the fetched results, not a statement

            // Process each welfare record
            foreach ($welfare_payments as $payment) {


                if (!empty($payment['member_id'])) {
                    $member_id = $payment['member_id'];

                    // Initialize member record if not already set
                    if (!isset($welfare_members[$member_id])) {
                        // Get member details including profile picture
                        $memberDetails = new Member($conn);
                        if ($memberDetails->getById($member_id)) {
                            $welfare_members[$member_id] = [
                                'id' => $member_id,
                                'name' => $memberDetails->first_name . ' ' . $memberDetails->last_name,
                                'profile_picture' => $memberDetails->profile_picture,
                                'phone_number' => $memberDetails->phone_number,
                                'total_welfare' => 0,
                                'last_welfare_date' => null
                            ];
                        }
                    }

                    // Update welfare totals and last date
                    if (isset($welfare_members[$member_id])) {
                        $welfare_members[$member_id]['total_welfare'] += $payment['amount'];

                        // Update last welfare date if it's more recent
                        $payment_date = strtotime($payment['transaction_date']);
                        if ($welfare_members[$member_id]['last_welfare_date'] === null ||
                            $payment_date > strtotime($welfare_members[$member_id]['last_welfare_date'])) {
                            $welfare_members[$member_id]['last_welfare_date'] = $payment['transaction_date'];
                        }
                    }
                }
            }



            // Calculate total welfare and average welfare
            $total_welfare = 0;
            foreach ($welfare_members as $member) {
                $total_welfare += $member['total_welfare'];
            }
            $avg_welfare = count($welfare_members) > 0 ? $total_welfare / count($welfare_members) : 0;

            // Convert welfare_members from associative array to indexed array for easier use in the view
            $welfare_members = array_values($welfare_members);

            // Get top welfare contributors
            $top_welfare_contributors = $this->finance->getTopWelfareContributors(5);

            // Set page title and active page
            $page_title = 'Welfare Tracking - ICGC Emmanuel Temple';
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/finances/welfare.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading welfare data: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances');
        }
    }

    /**
     * Filter finances by criteria
     *
     * @return void
     */
    public function filter() {
        $category = isset($_GET['category']) ? sanitize($_GET['category']) : null;
        $start_date = isset($_GET['start_date']) ? sanitize($_GET['start_date']) : null;
        $end_date = isset($_GET['end_date']) ? sanitize($_GET['end_date']) : null;

        // Get filtered finances
        if ($start_date && $end_date) {
            $stmt = $this->finance->getByDateRange($start_date, $end_date);
            $finances = $stmt->fetchAll();
        } else {
            $stmt = $this->finance->getAll();
            $finances = $stmt->fetchAll();
        }

        // Filter by category if provided
        if ($category) {
            $finances = array_filter($finances, function($finance) use ($category) {
                return $finance['category'] === $category;
            });
        }

        // Calculate totals using updated methods that include custom categories
        $total_income = $this->finance->getTotalIncome();
        $total_expense = $this->finance->getTotalExpenses();
        $balance = $total_income - $total_expense;

        // Set page title and active page
        $page_title = getPageTitle('Filtered Finances');
        $active_page = 'finances';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/finances/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display transactions list
     *
     * @return void
     */
    public function transactions() {
        try {
            // Get pagination parameters
            $page = isset($_GET['page']) ? (int)sanitize($_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? (int)sanitize($_GET['limit']) : 20;

            // Ensure valid pagination values
            $page = max(1, $page); // Minimum page is 1
            $limit = min(100, max(10, $limit)); // Limit between 10 and 100

            // Calculate offset
            $offset = ($page - 1) * $limit;

            // Build filters from GET parameters
            $filters = [];

            if (isset($_GET['search']) && !empty($_GET['search'])) {
                $filters['search'] = sanitize($_GET['search']);
            }

            if (isset($_GET['category']) && !empty($_GET['category'])) {
                $filters['category'] = sanitize($_GET['category']);
            }

            if (isset($_GET['type']) && !empty($_GET['type'])) {
                $filters['type'] = sanitize($_GET['type']);
            }

            if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
                $filters['start_date'] = sanitize($_GET['start_date']);
            }

            if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
                $filters['end_date'] = sanitize($_GET['end_date']);
            }

            if (isset($_GET['min_amount']) && !empty($_GET['min_amount'])) {
                $filters['min_amount'] = (float)sanitize($_GET['min_amount']);
            }

            if (isset($_GET['max_amount']) && !empty($_GET['max_amount'])) {
                $filters['max_amount'] = (float)sanitize($_GET['max_amount']);
            }

            if (isset($_GET['member_id']) && !empty($_GET['member_id'])) {
                $filters['member_id'] = (int)sanitize($_GET['member_id']);
            }

            // Get a new database connection with buffered queries
            require_once 'config/database.php';
            $database = new Database();
            $options = [
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
            ];
            $bufferedConn = $database->getConnection($options);

            // Create a new Finance model instance with the buffered connection
            $bufferedFinance = new Finance($bufferedConn);

            // Get filtered finances with pagination using the buffered connection
            if (!empty($filters)) {
                $stmt = $bufferedFinance->getFiltered($filters, $limit, $offset);
                $totalCount = $bufferedFinance->getFilteredCount($filters);
            } else {
                $stmt = $bufferedFinance->getAllPaginated($limit, $offset);
                $totalCount = $bufferedFinance->getTotalCount();
            }

            $finances = $stmt->fetchAll();

            // Set transactions variable for the view
            $transactions = $finances;

            // Calculate total pages for pagination
            $totalPages = ceil($totalCount / $limit);

            // Calculate totals (for summary stats)
            $total_income = $bufferedFinance->getTotalIncome();
            $total_expense = $bufferedFinance->getTotalExpenses();
            $balance = $total_income - $total_expense;

            // Check if there are archived records
            $archivedCount = $bufferedFinance->getArchivedCount();
            $hasArchivedRecords = ($archivedCount > 0);

            // Set page title and active page
            $page_title = 'Transaction Records - ICGC Emmanuel Temple';
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/finances/transactions.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading transactions: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances');
        }
    }

    /**
     * Display archived transactions
     *
     * @return void
     */
    public function archived() {
        try {
            // Get pagination parameters
            $page = isset($_GET['page']) ? (int)sanitize($_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? (int)sanitize($_GET['limit']) : 20;

            // Ensure valid pagination values
            $page = max(1, $page); // Minimum page is 1
            $limit = min(100, max(10, $limit)); // Limit between 10 and 100

            // Calculate offset
            $offset = ($page - 1) * $limit;

            // Build filters from GET parameters
            $filters = [];

            if (isset($_GET['search']) && !empty($_GET['search'])) {
                $filters['search'] = sanitize($_GET['search']);
            }

            if (isset($_GET['category']) && !empty($_GET['category'])) {
                $filters['category'] = sanitize($_GET['category']);
            }

            if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
                $filters['start_date'] = sanitize($_GET['start_date']);
            }

            if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
                $filters['end_date'] = sanitize($_GET['end_date']);
            }

            // Get a new database connection with buffered queries
            require_once 'config/database.php';
            $database = new Database();
            $options = [
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
            ];
            $bufferedConn = $database->getConnection($options);

            // Create a new Finance model instance with the buffered connection
            $bufferedFinance = new Finance($bufferedConn);

            // Get archived finances with pagination using the buffered connection
            $stmt = $bufferedFinance->getArchivedRecords($filters, $limit, $offset);

            // Check if archive table exists
            if ($stmt === null) {
                // No archive table exists yet
                $finances = [];
                $totalCount = 0;
                $archivedExists = false;
            } else {
                $finances = $stmt->fetchAll();
                $totalCount = $bufferedFinance->getArchivedCount($filters);
                $archivedExists = true;
            }

            // Set transactions variable for the view
            $transactions = $finances;

            // Calculate total pages for pagination
            $totalPages = ceil($totalCount / $limit);

            // Set page title and active page
            $page_title = 'Archived Transactions - ICGC Emmanuel Temple';
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/finances/archived.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error loading archived transactions: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances');
        }
    }

    /**
     * Archive old transactions
     *
     * @return void
     */
    public function archiveOld() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to archive transactions.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'finance/transactions');
            exit;
        }

        try {
            // Check if this is a POST request
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // Get cutoff date from form
                $cutoffDate = isset($_POST['cutoff_date']) ? sanitize($_POST['cutoff_date']) : null;

                if (empty($cutoffDate)) {
                    throw new Exception('Cutoff date is required');
                }

                // Validate date format
                $dateObj = DateTime::createFromFormat('Y-m-d', $cutoffDate);
                if (!$dateObj || $dateObj->format('Y-m-d') !== $cutoffDate) {
                    throw new Exception('Invalid date format. Please use YYYY-MM-DD format.');
                }

                // Get a new database connection with buffered queries
                require_once 'config/database.php';
                $database = new Database();
                $options = [
                    PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
                ];
                $bufferedConn = $database->getConnection($options);

                // Create a new Finance model instance with the buffered connection
                $bufferedFinance = new Finance($bufferedConn);

                // Archive old records using the buffered connection
                $result = $bufferedFinance->archiveOldRecords($cutoffDate);

                // Set flash message based on result
                $_SESSION['flash_message'] = $result['message'];
                $_SESSION['flash_type'] = $result['status'] === 'error' ? 'danger' : $result['status'];

                // Redirect back to transactions page
                header('Location: ' . BASE_URL . 'finance/transactions');
                exit;
            }

            // If not a POST request, display the archive form
            $page_title = 'Archive Old Transactions - ICGC Emmanuel Temple';
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/finances/archive_form.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'finance/transactions');
            exit;
        }
    }

    /**
     * Perform database maintenance
     *
     * @return void
     */
    public function maintenance() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to perform database maintenance.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'finance/transactions');
            exit;
        }

        try {
            // Use the maintenance helper script instead of direct model calls
            // This avoids the PDO unbuffered query issue entirely

            // Get the full path to the maintenance helper script
            $helperScript = __DIR__ . '/../maintenance_helper.php';

            if (file_exists($helperScript)) {
                // Execute the helper script via HTTP request to avoid scope issues
                $helperUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]/" . basename(dirname(__DIR__)) . "/maintenance_helper.php";

                // Use cURL to make the request
                $ch = curl_init($helperUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if ($httpCode === 200 && $response) {
                    // Parse the JSON response
                    $result = json_decode($response, true);

                    if ($result && isset($result['status'])) {
                        // Set flash message based on result
                        $_SESSION['flash_message'] = $result['message'];
                        $_SESSION['flash_type'] = $result['status'] === 'error' ? 'danger' : $result['status'];
                    } else {
                        throw new Exception('Invalid response from maintenance helper');
                    }
                } else {
                    throw new Exception('Failed to execute maintenance helper: HTTP ' . $httpCode);
                }
            } else {
                // Fall back to the model method if the helper script doesn't exist
                // Get a new database connection with buffered queries
                require_once 'config/database.php';
                $database = new Database();
                $options = [
                    PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
                ];
                $bufferedConn = $database->getConnection($options);

                // Create a new Finance model instance with the buffered connection
                $bufferedFinance = new Finance($bufferedConn);

                // Perform maintenance using the buffered connection
                $result = $bufferedFinance->performMaintenance();

                // Set flash message based on result
                $_SESSION['flash_message'] = $result['message'];
                $_SESSION['flash_type'] = $result['status'] === 'error' ? 'danger' : $result['status'];
            }

            // Redirect back to transactions page
            header('Location: ' . BASE_URL . 'finance/transactions');
            exit;
        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'finance/transactions');
            exit;
        }
    }

    /**
     * Show migration status page
     */
    public function migrationStatus() {
        try {
            $conn = $this->database->getConnection();

            // Get current columns
            $checkColumns = "SHOW COLUMNS FROM finances";
            $stmt = $conn->prepare($checkColumns);
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $existing_columns = array_column($columns, 'Field');

            // Check if migration is complete
            $required_columns = ['transaction_type', 'subcategory', 'reference_number', 'payment_method'];
            $missing_columns = array_diff($required_columns, $existing_columns);
            $migration_complete = empty($missing_columns);

            // Get sample data
            $sampleQuery = "SELECT * FROM finances ORDER BY id DESC LIMIT 5";
            $stmt = $conn->prepare($sampleQuery);
            $stmt->execute();
            $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Set page variables
            $page_title = getPageTitle('Finance Migration Status');
            $active_page = 'finances';

            // Start output buffering
            ob_start();

            // Load migration status view
            require_once 'views/finances/migration_status.php';

            // Get content
            $content = ob_get_clean();

            // Include layout
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error checking migration status: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            redirect('finances');
        }
    }

    /**
     * Run database migration
     */
    public function runMigration() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('finances/migration-status');
            return;
        }

        try {
            $conn = $this->database->getConnection();
            $success_messages = [];
            $error_messages = [];

            // Step 1: Add new columns if they don't exist
            $checkColumns = "SHOW COLUMNS FROM finances";
            $stmt = $conn->prepare($checkColumns);
            $stmt->execute();
            $currentColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $existingColumns = array_column($currentColumns, 'Field');

            $newColumns = [
                'transaction_type' => "ADD COLUMN transaction_type ENUM('income', 'expense') NOT NULL DEFAULT 'income' AFTER category",
                'subcategory' => "ADD COLUMN subcategory VARCHAR(50) NULL AFTER transaction_type",
                'reference_number' => "ADD COLUMN reference_number VARCHAR(50) NULL AFTER description",
                'payment_method' => "ADD COLUMN payment_method ENUM('cash', 'bank_transfer', 'mobile_money', 'cheque', 'other') DEFAULT 'cash' AFTER reference_number"
            ];

            foreach ($newColumns as $column => $sql) {
                if (!in_array($column, $existingColumns)) {
                    try {
                        $alterSql = "ALTER TABLE finances " . $sql;
                        $conn->exec($alterSql);
                        $success_messages[] = "Added column: $column";
                    } catch (PDOException $e) {
                        $error_messages[] = "Error adding column $column: " . $e->getMessage();
                    }
                } else {
                    $success_messages[] = "Column $column already exists";
                }
            }

            // Step 2: Update existing records
            try {
                // Set transaction_type for existing records
                $updateExpenses = "UPDATE finances SET transaction_type = 'expense' WHERE category = 'expense'";
                $conn->exec($updateExpenses);
                $success_messages[] = "Updated expense records";

                $updateIncome = "UPDATE finances SET transaction_type = 'income' WHERE category != 'expense'";
                $conn->exec($updateIncome);
                $success_messages[] = "Updated income records";

                // Generate reference numbers for existing records
                $updateRef = "UPDATE finances SET reference_number = CONCAT(
                    CASE WHEN transaction_type = 'income' THEN 'INC' ELSE 'EXP' END,
                    DATE_FORMAT(transaction_date, '%Y%m%d'),
                    LPAD(id, 4, '0')
                ) WHERE reference_number IS NULL";
                $conn->exec($updateRef);
                $success_messages[] = "Generated reference numbers";

            } catch (PDOException $e) {
                $error_messages[] = "Error updating records: " . $e->getMessage();
            }

            // Step 3: Expand category ENUM
            try {
                $expandEnum = "ALTER TABLE finances
                              MODIFY COLUMN category ENUM(
                                  'tithe', 'offering', 'project_offering', 'donation', 'seed', 'pledge', 'pastors_appreciation',
                                  'welfare', 'children_service_offering', 'others',
                                  'utilities', 'rent', 'salaries', 'maintenance', 'equipment', 'supplies',
                                  'events', 'missions', 'charity', 'other_expenses'
                              ) NOT NULL";
                $conn->exec($expandEnum);
                $success_messages[] = "Expanded category options";
            } catch (PDOException $e) {
                $error_messages[] = "Error expanding categories: " . $e->getMessage();
            }

            // Set flash messages
            if (!empty($success_messages)) {
                $_SESSION['flash_message'] = 'Migration completed successfully: ' . implode(', ', $success_messages);
                $_SESSION['flash_type'] = 'success';
            }

            if (!empty($error_messages)) {
                $_SESSION['flash_message'] .= ' Errors: ' . implode(', ', $error_messages);
                $_SESSION['flash_type'] = 'warning';
            }

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Migration failed: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
        }

        redirect('finances/migration-status');
    }

    /**
     * Add new payment type via AJAX
     */
    public function addPaymentType() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        try {
            $categoryType = $_POST['category_type'] ?? '';
            $label = trim($_POST['label'] ?? '');
            $description = trim($_POST['description'] ?? '');

            // Validate input
            if (empty($categoryType) || empty($label)) {
                echo json_encode(['success' => false, 'message' => 'Category type and label are required']);
                return;
            }

            // Validate category type
            $validTypes = ['member_payment', 'general_income', 'expense'];
            if (!in_array($categoryType, $validTypes)) {
                echo json_encode(['success' => false, 'message' => 'Invalid category type']);
                return;
            }

            // Load the finance categories config
            require_once 'config/finance_categories.php';

            // Generate a unique key for the new category
            $key = strtolower(str_replace([' ', '-', '_'], '_', $label));
            $key = preg_replace('/[^a-z0-9_]/', '', $key);

            // Ensure uniqueness by checking existing categories
            $existingCategories = [];
            switch ($categoryType) {
                case 'member_payment':
                    $existingCategories = FinanceCategoriesConfig::getMemberPaymentCategories();
                    break;
                case 'general_income':
                    $existingCategories = FinanceCategoriesConfig::getGeneralIncomeCategories();
                    break;
                case 'expense':
                    $existingCategories = FinanceCategoriesConfig::getExpenseCategories();
                    break;
            }

            // Make key unique if it already exists
            $originalKey = $key;
            $counter = 1;
            while (array_key_exists($key, $existingCategories)) {
                $key = $originalKey . '_' . $counter;
                $counter++;
            }

            // Create new category data
            $newCategory = [
                'label' => $label,
                'description' => $description,
                'icon' => 'fas fa-money-bill-wave', // Default icon
                'color' => '#10B981' // Default color
            ];

            // Read the current config file
            $configFile = __DIR__ . '/../config/finance_categories.php';
            $configContent = file_get_contents($configFile);

            // Find the appropriate section to add the new category
            $sectionMap = [
                'member_payment' => 'MEMBER_PAYMENT_CATEGORIES',
                'general_income' => 'GENERAL_INCOME_CATEGORIES',
                'expense' => 'EXPENSE_CATEGORIES'
            ];

            $sectionName = $sectionMap[$categoryType];

            // Find the section and add the new category
            $pattern = "/const {$sectionName} = \[(.*?)\];/s";
            if (preg_match($pattern, $configContent, $matches)) {
                $sectionContent = $matches[1];

                // Add the new category at the end
                $newCategoryString = "\n        '{$key}' => [\n";
                $newCategoryString .= "            'label' => '{$label}',\n";
                $newCategoryString .= "            'description' => '{$description}',\n";
                $newCategoryString .= "            'icon' => 'fas fa-money-bill-wave',\n";
                $newCategoryString .= "            'color' => '#10B981'\n";
                $newCategoryString .= "        ],";

                $newSectionContent = $sectionContent . $newCategoryString;
                $newConfigContent = str_replace($matches[0], "const {$sectionName} = [{$newSectionContent}\n    ];", $configContent);

                // Write back to file
                if (file_put_contents($configFile, $newConfigContent)) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Payment type added successfully',
                        'key' => $key,
                        'label' => $label
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Failed to save configuration']);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'Could not find configuration section']);
            }

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Get the dashboard URL for a specific category
     *
     * @param string $category Category name
     * @return string Dashboard URL
     */
    private function getCategoryDashboardUrl($category) {
        try {
            // Get database connection
            $database = new Database();
            $conn = $database->getConnection();

            // First, try to find the category in custom_finance_categories table
            $categoryQuery = "SELECT dashboard_route, category_type FROM custom_finance_categories
                             WHERE name = ? AND is_active = 1 LIMIT 1";
            $stmt = $conn->prepare($categoryQuery);
            $stmt->execute([$category]);
            $categoryData = $stmt->fetch(PDO::FETCH_OBJ);

            if ($categoryData) {
                // If we have a stored dashboard route, use it
                if (!empty($categoryData->dashboard_route)) {
                    return $categoryData->dashboard_route;
                }

                // If no stored route but we found the category in database,
                // use the database category_type (prioritize database over keywords)
                $type = 'income'; // Default
                if ($categoryData->category_type === 'expenses') {
                    $type = 'expense';
                } elseif ($categoryData->category_type === 'member_payments') {
                    $type = 'income'; // Member payments are income
                } elseif ($categoryData->category_type === 'general_income') {
                    $type = 'income'; // General income is income
                }

                return "finance/dashboard/category?category=" . urlencode($category) . "&type={$type}";
            }

            // If not found in custom categories, check for core categories
            $coreCategories = [
                'tithe' => 'finance/dashboard/tithe',
                'pledge' => 'finance/dashboard/pledge'
            ];

            $categoryLower = strtolower($category);
            if (isset($coreCategories[$categoryLower])) {
                return $coreCategories[$categoryLower];
            }

            // For unknown categories, try to determine the type and create a dynamic URL
            $categoryType = $this->determineCategoryType($category);
            $type = ($categoryType === 'expenses') ? 'expense' : 'income';

            return "finance/dashboard/category?category=" . urlencode($category) . "&type={$type}";

        } catch (Exception $e) {
            error_log("Error getting category dashboard URL: " . $e->getMessage());
            // Fallback to general finance page
            return 'finances';
        }
    }

    /**
     * Determine the category type for unknown categories
     *
     * @param string $category Category name
     * @return string Category type (member_payments, general_income, or expenses)
     */
    private function determineCategoryType($category) {
        // Common expense keywords
        $expenseKeywords = [
            'utilities', 'rent', 'maintenance', 'repair', 'supplies', 'equipment',
            'fuel', 'transport', 'travel', 'food', 'catering', 'event', 'program',
            'salary', 'allowance', 'bonus', 'insurance', 'tax', 'fee', 'cost',
            'expense', 'payment', 'bill', 'purchase', 'buy'
        ];

        // Common member payment keywords
        $memberPaymentKeywords = [
            'tithe', 'pledge', 'offering', 'donation', 'contribution', 'dues',
            'membership', 'subscription'
        ];

        $categoryLower = strtolower($category);

        // Check for member payment keywords
        foreach ($memberPaymentKeywords as $keyword) {
            if (strpos($categoryLower, $keyword) !== false) {
                return 'member_payments';
            }
        }

        // Check for expense keywords
        foreach ($expenseKeywords as $keyword) {
            if (strpos($categoryLower, $keyword) !== false) {
                return 'expenses';
            }
        }

        // Default to general income
        return 'general_income';
    }



}
