<div class="container mx-auto px-4 py-6 max-w-7xl fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border-2 border-gray-100">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white">
            <h1 class="text-3xl font-bold">Reports Dashboard</h1>
            <p class="mt-2 opacity-90">Generate and analyze church data reports</p>
        </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Members Card -->
        <div class="bg-white rounded-xl shadow-md p-5 border-l-4 border-blue-500 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center">
                <div class="rounded-full bg-blue-100 p-3 mr-4">
                    <i class="fas fa-users text-blue-500 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500">Total Members</p>
                    <h3 class="text-2xl font-bold text-gray-800" id="total-members">Loading...</h3>
                </div>
            </div>
        </div>

        <!-- Total Attendance Card -->
        <div class="bg-white rounded-xl shadow-md p-5 border-l-4 border-green-500 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center">
                <div class="rounded-full bg-green-100 p-3 mr-4">
                    <i class="fas fa-clipboard-check text-green-500 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500">Last Sunday Attendance</p>
                    <h3 class="text-2xl font-bold text-gray-800" id="total-attendance">Loading...</h3>
                </div>
            </div>
        </div>

        <!-- Total Income Card -->
        <div class="bg-white rounded-xl shadow-md p-5 border-l-4 border-yellow-500 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center">
                <div class="rounded-full bg-yellow-100 p-3 mr-4">
                    <i class="fas fa-hand-holding-usd text-yellow-500 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500">Monthly Income</p>
                    <h3 class="text-2xl font-bold text-gray-800" id="total-income">Loading...</h3>
                </div>
            </div>
        </div>

        <!-- New Members Card -->
        <div class="bg-white rounded-xl shadow-md p-5 border-l-4 border-purple-500 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center">
                <div class="rounded-full bg-purple-100 p-3 mr-4">
                    <i class="fas fa-user-plus text-purple-500 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500">New Members (30 days)</p>
                    <h3 class="text-2xl font-bold text-gray-800" id="new-members">Loading...</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Generator -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-8 border-2 border-gray-100 relative overflow-hidden">
        <!-- Decorative elements -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-primary bg-opacity-5 rounded-full -mr-10 -mt-10"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-secondary bg-opacity-10 rounded-full -ml-8 -mb-8"></div>

        <div class="relative z-10">
            <div class="flex items-center mb-6">
                <div class="p-3 rounded-full bg-primary bg-opacity-10 text-primary mr-4">
                    <i class="fas fa-file-alt text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold text-gray-800">Generate Custom Report</h2>
            </div>

            <form action="<?php echo BASE_URL; ?>reports/generate" method="POST" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <!-- Report Type -->
                    <div>
                        <label for="report_type" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <span class="bg-primary bg-opacity-10 text-primary p-1.5 rounded-full mr-2 shadow-sm">
                                <i class="fas fa-file-invoice"></i>
                            </span>
                            Report Type <span class="text-red-500 ml-1">*</span>
                        </label>
                        <select id="report_type" name="report_type" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2.5" required>
                            <option value="">Select Report Type</option>
                            <option value="member">Member Report</option>
                            <option value="attendance">Attendance Report</option>
                            <option value="finance">Finance Report</option>
                            <option value="visitor">Visitor Report</option>
                            <option value="group">Group Report</option>
                        </select>
                    </div>

                    <!-- Start Date -->
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <span class="bg-primary bg-opacity-10 text-primary p-1.5 rounded-full mr-2 shadow-sm">
                                <i class="fas fa-calendar-alt"></i>
                            </span>
                            Start Date <span class="text-red-500 ml-1">*</span>
                        </label>
                        <div class="relative rounded-lg shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-day text-gray-400"></i>
                            </div>
                            <input type="date" id="start_date" name="start_date" class="w-full pl-10 rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2.5" required>
                        </div>
                    </div>

                    <!-- End Date -->
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <span class="bg-primary bg-opacity-10 text-primary p-1.5 rounded-full mr-2 shadow-sm">
                                <i class="fas fa-calendar-alt"></i>
                            </span>
                            End Date <span class="text-red-500 ml-1">*</span>
                        </label>
                        <div class="relative rounded-lg shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-day text-gray-400"></i>
                            </div>
                            <input type="date" id="end_date" name="end_date" class="w-full pl-10 rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2.5" required>
                        </div>
                    </div>

                    <!-- Additional Filters -->
                    <div id="additional-filters" class="hidden">
                        <!-- Will be populated dynamically based on report type -->
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-3 px-6 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-file-export mr-2"></i> Generate Report
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Member Reports -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-100">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4 text-white">
                <div class="flex items-center">
                    <div class="p-2 rounded-full bg-white/20 mr-3">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <h2 class="text-lg font-semibold">Member Reports</h2>
                </div>
            </div>
            <div class="p-6">
                <ul class="space-y-3">
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/members/new?period=month" class="text-gray-700 hover:text-blue-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors duration-200">
                                <i class="fas fa-user-plus text-blue-500"></i>
                            </div>
                            <span>New members this month</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-blue-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/members/birthdays?period=month" class="text-gray-700 hover:text-blue-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors duration-200">
                                <i class="fas fa-birthday-cake text-blue-500"></i>
                            </div>
                            <span>Birthdays this month</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-blue-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/members/department" class="text-gray-700 hover:text-blue-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors duration-200">
                                <i class="fas fa-sitemap text-blue-500"></i>
                            </div>
                            <span>Members by department</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-blue-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/members/status" class="text-gray-700 hover:text-blue-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors duration-200">
                                <i class="fas fa-user-check text-blue-500"></i>
                            </div>
                            <span>Members by status</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-blue-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Attendance Reports -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-100">
            <div class="bg-gradient-to-r from-green-500 to-green-600 px-6 py-4 text-white">
                <div class="flex items-center">
                    <div class="p-2 rounded-full bg-white/20 mr-3">
                        <i class="fas fa-clipboard-check text-xl"></i>
                    </div>
                    <h2 class="text-lg font-semibold">Attendance Reports</h2>
                </div>
            </div>
            <div class="p-6">
                <ul class="space-y-3">
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/attendance/weekly" class="text-gray-700 hover:text-green-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors duration-200">
                                <i class="fas fa-calendar-week text-green-500"></i>
                            </div>
                            <span>Weekly attendance</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-green-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/attendance/monthly" class="text-gray-700 hover:text-green-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors duration-200">
                                <i class="fas fa-calendar-alt text-green-500"></i>
                            </div>
                            <span>Monthly attendance</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-green-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/attendance/service" class="text-gray-700 hover:text-green-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors duration-200">
                                <i class="fas fa-church text-green-500"></i>
                            </div>
                            <span>Attendance by service</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-green-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/attendance/comparison" class="text-gray-700 hover:text-green-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors duration-200">
                                <i class="fas fa-chart-bar text-green-500"></i>
                            </div>
                            <span>Attendance comparison</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-green-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Finance Reports -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-100">
            <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 px-6 py-4 text-white">
                <div class="flex items-center">
                    <div class="p-2 rounded-full bg-white/20 mr-3">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <h2 class="text-lg font-semibold">Finance Reports</h2>
                </div>
            </div>
            <div class="p-6">
                <ul class="space-y-3">
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/finance/monthly" class="text-gray-700 hover:text-yellow-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3 group-hover:bg-yellow-200 transition-colors duration-200">
                                <i class="fas fa-calendar-alt text-yellow-500"></i>
                            </div>
                            <span>Monthly income & expenses</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-yellow-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/finance/category" class="text-gray-700 hover:text-yellow-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3 group-hover:bg-yellow-200 transition-colors duration-200">
                                <i class="fas fa-tags text-yellow-500"></i>
                            </div>
                            <span>Income by category</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-yellow-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo BASE_URL; ?>reports/finance/yearly" class="text-gray-700 hover:text-yellow-600 flex items-center group transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3 group-hover:bg-yellow-200 transition-colors duration-200">
                                <i class="fas fa-chart-pie text-yellow-500"></i>
                            </div>
                            <span>Yearly financial summary</span>
                            <i class="fas fa-chevron-right ml-auto text-gray-400 group-hover:text-yellow-500 transition-colors duration-200"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Data Visualization Section -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8 border-2 border-gray-100">
        <div class="bg-gradient-to-r from-indigo-500 to-purple-500 px-6 py-4 text-white">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-white/20 mr-3">
                    <i class="fas fa-chart-pie text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold">Data Visualization</h2>
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Attendance Trends Chart -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-all duration-300">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Attendance Trends</h3>
                    <div class="h-64">
                        <canvas id="attendance-trends-chart"></canvas>
                    </div>
                </div>

                <!-- Financial Overview Chart -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-all duration-300">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Financial Overview</h3>
                    <div class="h-64">
                        <canvas id="financial-overview-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set default dates
        const today = new Date();
        const endDateField = document.getElementById('end_date');
        endDateField.value = today.toISOString().slice(0, 10);

        // Set default start date to 30 days ago
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        const startDateField = document.getElementById('start_date');
        startDateField.value = startDate.toISOString().slice(0, 10);

        // Load stats for the dashboard cards
        loadDashboardStats();

        // Initialize charts
        initializeCharts();

        // Add event listener for report type change
        document.getElementById('report_type').addEventListener('change', function() {
            const reportType = this.value;
            const additionalFilters = document.getElementById('additional-filters');

            // Clear previous filters
            additionalFilters.innerHTML = '';

            // Show/hide additional filters based on report type
            if (reportType) {
                additionalFilters.classList.remove('hidden');

                // Add specific filters based on report type
                switch(reportType) {
                    case 'member':
                        additionalFilters.innerHTML = `
                            <label for="department" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <span class="bg-primary bg-opacity-10 text-primary p-1.5 rounded-full mr-2 shadow-sm">
                                    <i class="fas fa-sitemap"></i>
                                </span>
                                Department
                            </label>
                            <select id="department" name="department" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2.5">
                                <option value="">All Departments</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo htmlspecialchars($dept['name']); ?>">
                                        <?php echo htmlspecialchars($dept['display_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        `;
                        break;
                    case 'attendance':
                        additionalFilters.innerHTML = `
                            <label for="service_id" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <span class="bg-primary bg-opacity-10 text-primary p-1.5 rounded-full mr-2 shadow-sm">
                                    <i class="fas fa-church"></i>
                                </span>
                                Service
                            </label>
                            <select id="service_id" name="service_id" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2.5">
                                <option value="">All Services</option>
                                <option value="1">Sunday Morning Service</option>
                                <option value="2">Tuesday Bible Study</option>
                                <option value="3">Friday Prayer Meeting</option>
                            </select>
                        `;
                        break;
                    case 'finance':
                        additionalFilters.innerHTML = `
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <span class="bg-primary bg-opacity-10 text-primary p-1.5 rounded-full mr-2 shadow-sm">
                                    <i class="fas fa-tags"></i>
                                </span>
                                Category
                            </label>
                            <select id="category" name="category" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2.5">
                                <option value="">All Categories</option>
                                <option value="tithe">Tithe</option>
                                <option value="offering">Offering</option>
                                <option value="project_offering">Project Offering</option>
                                <option value="pledge">Pledge</option>
                                <option value="seed">Seed</option>
                                <option value="welfare">Welfare</option>
                                <option value="expense">Expense</option>
                            </select>
                        `;
                        break;
                }
            } else {
                additionalFilters.classList.add('hidden');
            }
        });
    });

    // Function to load dashboard stats
    function loadDashboardStats() {
        // In a real implementation, these would come from AJAX calls to the server
        // For now, we'll use placeholder data

        // Total members
        const totalMembers = 245;
        document.getElementById('total-members').textContent = totalMembers;

        // Last Sunday attendance
        const lastSundayAttendance = 187;
        document.getElementById('total-attendance').textContent = lastSundayAttendance;

        // Monthly income
        const monthlyIncome = 'GH₵ 12,450.00';
        document.getElementById('total-income').textContent = monthlyIncome;

        // New members
        const newMembers = 8;
        document.getElementById('new-members').textContent = newMembers;

        // Animate the numbers for a better effect
        animateNumbers();
    }

    // Function to animate numbers
    function animateNumbers() {
        const elements = document.querySelectorAll('#total-members, #total-attendance, #new-members');

        elements.forEach(element => {
            const finalValue = parseInt(element.textContent);
            let startValue = 0;
            const duration = 1500;
            const increment = Math.ceil(finalValue / (duration / 20));

            const timer = setInterval(() => {
                startValue += increment;
                if (startValue >= finalValue) {
                    element.textContent = finalValue;
                    clearInterval(timer);
                } else {
                    element.textContent = startValue;
                }
            }, 20);
        });

        // Animate the income separately since it's a currency string
        const incomeElement = document.getElementById('total-income');
        const finalValue = 12450;
        let startValue = 0;
        const duration = 1500;
        const increment = Math.ceil(finalValue / (duration / 20));

        const timer = setInterval(() => {
            startValue += increment;
            if (startValue >= finalValue) {
                incomeElement.textContent = 'GH₵ ' + finalValue.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                clearInterval(timer);
            } else {
                incomeElement.textContent = 'GH₵ ' + startValue.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
            }
        }, 20);
    }

    // Function to initialize charts
    function initializeCharts() {
        // Attendance Trends Chart
        const attendanceTrendsCtx = document.getElementById('attendance-trends-chart').getContext('2d');
        new Chart(attendanceTrendsCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Sunday Service',
                    data: [165, 178, 190, 175, 185, 195, 187, 192, 198, 205, 210, 187],
                    borderColor: '#10B981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }, {
                    label: 'Tuesday Bible Study',
                    data: [85, 90, 88, 92, 95, 98, 94, 96, 100, 102, 105, 95],
                    borderColor: '#6366F1',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            drawBorder: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Financial Overview Chart
        const financialOverviewCtx = document.getElementById('financial-overview-chart').getContext('2d');
        new Chart(financialOverviewCtx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Income',
                    data: [12000, 13500, 14200, 13800, 15000, 16500, 15800, 16200, 17500, 18000, 19200, 18500],
                    backgroundColor: 'rgba(16, 185, 129, 0.7)',
                    borderRadius: 4
                }, {
                    label: 'Expenses',
                    data: [9500, 10200, 11000, 10500, 11800, 12500, 12000, 12800, 13500, 14000, 15000, 14500],
                    backgroundColor: 'rgba(239, 68, 68, 0.7)',
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += 'GH₵ ' + context.parsed.y.toLocaleString('en-US');
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            drawBorder: false
                        },
                        ticks: {
                            callback: function(value) {
                                return 'GH₵ ' + value.toLocaleString('en-US');
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
</script>
