<?php
/**
 * Render Helper Functions
 * Provides utility functions for rendering views
 */

/**
 * Render a view with data
 * @param string $view View file path
 * @param array $data Data to pass to the view
 * @return void
 */
function render($view, $data = []) {
    // Extract data to make variables available in the view
    extract($data);

    // Set active page for sidebar highlighting
    $active_page = explode('/', $view)[0];

    // Start output buffering
    ob_start();

    // Include the view file
    $view_file = 'views/' . $view . '.php';
    if (file_exists($view_file)) {
        require_once $view_file;
    } else {
        echo "View file not found: {$view_file}";
    }

    // Get the content from the buffer
    $content = ob_get_clean();

    // Include the layout
    require_once 'views/layouts/main.php';
}

/**
 * Set flash message
 * @param string $name Message name
 * @param string $message Message content
 * @param string $type Message type (success, danger, warning, info)
 * @return void
 */
function flash($name, $message = '', $type = 'info') {
    // If message is empty, just display the message
    if (empty($message)) {
        // Check if the message exists in session
        if (isset($_SESSION[$name])) {
            // Get the message
            $message = $_SESSION[$name];
            $message_type = $_SESSION[$name . '_type'];
            
            // Unset the message
            unset($_SESSION[$name]);
            unset($_SESSION[$name . '_type']);
            
            // Display the message
            echo '<div class="bg-' . $message_type . '-100 border-l-4 border-' . $message_type . '-500 text-' . $message_type . '-700 p-4 mb-4" role="alert">';
            echo '<p>' . $message . '</p>';
            echo '</div>';
        }
    } else {
        // Set the message in session
        $_SESSION[$name] = $message;
        $_SESSION[$name . '_type'] = $type;
    }
}
