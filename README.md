# ICGC Emmanuel Temple Church Management System

## Enterprise-Level System Architecture

The ICGC church management system has been upgraded with enterprise-level features to enhance performance, security, and maintainability. This document outlines the key improvements and how to use them.

## New Features and Improvements

### 1. Centralized Error Handling

A robust error handling system has been implemented to capture, log, and display errors appropriately:

- **Error Logging**: All errors are now logged to `logs/app_errors.log` with detailed context
- **User-friendly Error Pages**: Custom 404 and 500 error pages provide a better user experience
- **Development vs. Production**: Different error details are shown based on environment

Usage:
```php
try {
    // Your code here
} catch (Exception $e) {
    // Let the global error handler handle it
    handle_exception($e);
}

// Or log errors manually
log_error('Something went wrong', 'error', ['context' => 'additional info']);
```

### 2. Caching System

A file-based caching system improves performance for frequently accessed data:

- **Simple API**: Easy to use functions for getting, setting, and clearing cache
- **Automatic Expiration**: Set custom TTL (time-to-live) for cached items
- **Cache Statistics**: Monitor cache usage and performance

Usage:
```php
// Get from cache or compute
$data = cache_remember('cache_key', function() {
    // This will only run if cache is missing or expired
    return expensive_database_query();
}, 3600); // Cache for 1 hour

// Manually set/get cache
cache_set('settings', $settings_array, 86400); // Cache for 1 day
$settings = cache_get('settings');

// Clear cache
cache_clear(); // All cache
cache_clear('prefix_'); // By prefix
```

### 3. API Response Standardization

A consistent API response format provides better integration capabilities:

- **Unified Format**: All API responses follow the same structure
- **Status Codes**: Appropriate HTTP status codes are used
- **Error Handling**: Standardized error reporting

Usage:
```php
// Success responses
api_success($data, 'Operation successful');
api_created($new_resource, 'Resource created');

// Error responses
api_error('Something went wrong', 400);
api_not_found('Resource not found');
api_validation_error($validation_errors, 'Validation failed');
```

### 4. Query Builder

A fluent query builder makes database operations safer and more readable:

- **SQL Injection Prevention**: Automatic parameter binding
- **Method Chaining**: Intuitive API for building complex queries
- **Common Operations**: Simple methods for common database operations

Usage:
```php
// Select with conditions
$activeMembers = query('members')
    ->select('id', 'name', 'email')
    ->where('status', 'active')
    ->where('department_id', 5)
    ->orderBy('name')
    ->get();

// Insert data
$id = query('members')->insert([
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'status' => 'active'
]);

// Update with conditions
query('members')
    ->where('id', $id)
    ->update(['status' => 'inactive']);
```

### 5. Performance Optimizations

Performance improvements have been implemented throughout the system:

- **Apache Configuration**: Optimized .htaccess with caching and compression
- **Resource Optimization**: CSS/JS minification and browser caching
- **Database Query Optimization**: Improved query performance

## Environment Configuration

The system now better handles different environments:

- **Development**: Full error details, no caching
- **Production**: User-friendly errors, full caching, optimized resources

Set the environment in the `.env` file or server configuration:

```
APP_ENV=production  # or development
```

## Security Enhancements

- **CSRF Protection**: All forms are protected against Cross-Site Request Forgery
- **XSS Prevention**: Input validation and output escaping
- **SQL Injection Protection**: Prepared statements and parameter binding
- **Session Security**: HTTP-only cookies, session management
- **Content Security Policy**: Restricted resource loading

## Monitoring and Maintenance

- **Error Logs**: Check `logs/app_errors.log` for application errors
- **Database Schema**: Use `database/scripts/check_schema.php` to verify database integrity
- **Cache Management**: Use cache statistics to monitor cache usage

## Single Tenant Application

This is a single-tenant church management system designed for individual churches:

- **Simplified Architecture**: No complex tenant isolation needed
- **Direct Data Access**: All data belongs to one organization
- **Streamlined Configuration**: Single set of settings and configurations

## Documentation

For more detailed information, please see:

- [API Documentation](docs/api.md)
- [Developer Guide](docs/developer-guide.md)
- [Administrator Manual](docs/admin-manual.md)

## License

This Church Management System is proprietary software. All rights reserved.

&copy; 2023-2025 ICGC Emmanuel Temple
