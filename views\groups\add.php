<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Add New Group</h1>
        <a href="<?php echo BASE_URL; ?>groups" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-arrow-left mr-2"></i> Back to Groups
        </a>
    </div>

    <!-- Flash Messages -->
    <?php
    $flash_message = get_flash_message();
    if ($flash_message):
        $alert_class = '';
        switch ($flash_message['type']) {
            case 'success':
                $alert_class = 'bg-green-100 border-green-500 text-green-700';
                break;
            case 'danger':
                $alert_class = 'bg-red-100 border-red-500 text-red-700';
                break;
            case 'warning':
                $alert_class = 'bg-yellow-100 border-yellow-500 text-yellow-700';
                break;
            default:
                $alert_class = 'bg-blue-100 border-blue-500 text-blue-700';
        }
    ?>
        <div class="flash-message border-l-4 p-4 mb-4 <?php echo $alert_class; ?>" role="alert">
            <p><?php echo htmlspecialchars($flash_message['message']); ?></p>
        </div>
    <?php endif; ?>

    <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto">
        <form action="<?php echo BASE_URL; ?>groups" method="POST">
            <div class="mb-4">
                <label for="group_name" class="block text-gray-700 text-sm font-bold mb-2">Group Name *</label>
                <input type="text" name="group_name" id="group_name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
            </div>

            <div class="mb-4">
                <label for="group_description" class="block text-gray-700 text-sm font-bold mb-2">Description</label>
                <textarea name="group_description" id="group_description" rows="4" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
            </div>

            <div class="mb-4">
                <label for="group_type_id" class="block text-gray-700 text-sm font-bold mb-2">Group Type</label>
                <select name="group_type_id" id="group_type_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" onchange="toggleNewTypeInput()">
                    <option value="">Select Group Type</option>
                    <?php foreach ($groupTypes as $type): ?>
                    <option value="<?php echo $type->group_type_id; ?>"><?php echo $type->type_name; ?></option>
                    <?php endforeach; ?>
                    <option value="new">+ Create New Type</option>
                </select>

                <!-- New Group Type Input (hidden by default) -->
                <div id="newTypeContainer" class="mt-3 hidden">
                    <label for="new_group_type" class="block text-gray-700 text-sm font-bold mb-2">New Group Type Name *</label>
                    <input type="text" name="new_group_type" id="new_group_type" placeholder="Enter new group type name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <p class="text-xs text-gray-500 mt-1">This will create a new group type that can be used for future groups.</p>
                </div>
            </div>

            <div class="mb-4">
                <label for="parent_group_id" class="block text-gray-700 text-sm font-bold mb-2">Parent Group (Optional)</label>
                <select name="parent_group_id" id="parent_group_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">None</option>
                    <?php foreach ($parentGroups as $parent): ?>
                    <option value="<?php echo $parent->group_id; ?>"><?php echo $parent->group_name; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- CSRF Token -->
            <?php echo csrf_field(); ?>

            <div class="flex items-center justify-end">
                <button type="submit" class="bg-[#3F7D58] hover:bg-[#2c5a3f] text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200">
                    Create Group
                </button>
            </div>
        </form>
    </div>
</div>

<script src="<?php echo BASE_URL; ?>assets/js/groups-common.js"></script>