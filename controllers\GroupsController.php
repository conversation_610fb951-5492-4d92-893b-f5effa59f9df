<?php
/**
 * Groups Controller
 * Handles all group-related actions
 */

require_once 'models/Group.php';
require_once 'models/Member.php';
require_once 'helpers/functions.php';

class GroupsController {
    private $groupModel;
    private $memberModel;

    public function __construct() {
        $this->groupModel = new Group();
        $this->memberModel = new Member();
    }

    /**
     * Display groups index page
     */
    public function index() {
        try {
            // Get all active groups
            $groups = $this->groupModel->getAllGroups('active');
            $allGroupTypes = $this->groupModel->getAllGroupTypes();

            // Count statistics
            $totalGroups = $groups ? count($groups) : 0;
            $totalMembers = 0;

            $groupsByType = [];
            $activeGroupTypes = [];

            // Initialize group type counts
            if ($allGroupTypes) {
                foreach ($allGroupTypes as $type) {
                    $groupsByType[$type->type_name] = 0;
                }
            }

            // Find largest group
            $largestGroup = null;
            $largestGroupSize = 0;

            if ($groups) {
                foreach ($groups as $group) {
                    if (isset($group->type_name) && isset($groupsByType[$group->type_name])) {
                        $groupsByType[$group->type_name]++;

                        // Add to active group types if not already added
                        $typeExists = false;
                        foreach ($activeGroupTypes as $activeType) {
                            if ($activeType->type_name === $group->type_name) {
                                $typeExists = true;
                                break;
                            }
                        }
                        if (!$typeExists && $allGroupTypes) {
                            foreach ($allGroupTypes as $type) {
                                if ($type->type_name === $group->type_name) {
                                    $activeGroupTypes[] = $type;
                                    break;
                                }
                            }
                        }
                    }
                    $totalMembers += $group->member_count ?? 0;

                    // Track largest group
                    $memberCount = $group->member_count ?? 0;
                    if ($memberCount > $largestGroupSize) {
                        $largestGroupSize = $memberCount;
                        $largestGroup = $group;
                    }
                }
            }

            // Get recent group activities (new members joined)
            $recentActivities = $this->getRecentGroupActivities();

            $data = [
                'groups' => $groups,
                'groupTypes' => $activeGroupTypes, // Only group types with active groups for Quick Filter
                'allGroupTypes' => $allGroupTypes, // All group types for Manage Filters modal
                'totalGroups' => $totalGroups,
                'totalMembers' => $totalMembers,
                'groupsByType' => $groupsByType,
                'recentActivities' => $recentActivities,
                'largestGroup' => $largestGroup,
                'largestGroupSize' => $largestGroupSize,
                'page_title' => getPageTitle('Groups')
            ];

            render('groups/index', $data);
        } catch (Exception $e) {
            error_log('Error in GroupsController::index: ' . $e->getMessage());
            $_SESSION['flash_message'] = ['message' => 'Error loading groups: ' . $e->getMessage(), 'type' => 'error'];
            redirect('/dashboard');
        }
    }

    /**
     * Get recent group activities
     */
    private function getRecentGroupActivities() {
        try {
            $sql = "SELECT mg.*, m.first_name, m.last_name, g.group_name
                    FROM member_groups mg
                    JOIN members m ON mg.member_id = m.id
                    JOIN groups g ON mg.group_id = g.group_id
                    WHERE mg.status = 'active'
                    ORDER BY mg.created_at DESC
                    LIMIT 10";

            $database = new Database();
            $conn = $database->getConnection();
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_OBJ);
        } catch (Exception $e) {
            error_log('Error getting recent activities: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Display add group form
     */
    public function add() {
        $groupTypes = $this->groupModel->getAllGroupTypes();
        $parentGroups = $this->groupModel->getAllGroups('active');

        // Set page title and active page
        $page_title = getPageTitle('Add New Group');
        $active_page = 'groups';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/groups/add.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Process add group form
     */
    public function create() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // Handle new group type creation
            $group_type_id = null;
            if (!empty($_POST['group_type_id']) && $_POST['group_type_id'] === 'new') {
                // Create new group type
                if (empty($_POST['new_group_type'])) {
                    set_flash_message('New group type name is required', 'danger');
                    redirect('groups/add');
                    return;
                }

                $new_type_name = trim($_POST['new_group_type']);
                $group_type_id = $this->groupModel->createGroupType($new_type_name);

                if (!$group_type_id) {
                    error_log("Failed to create group type: " . $new_type_name);
                    set_flash_message('Failed to create new group type: ' . $new_type_name, 'danger');
                    redirect('groups/add');
                    return;
                } else {
                    error_log("Successfully created group type: " . $new_type_name . " with ID: " . $group_type_id);
                }
            } else {
                $group_type_id = !empty($_POST['group_type_id']) ? (int)$_POST['group_type_id'] : null;
            }

            // Prepare data for model (model will handle all validation)
            $data = [
                'group_name' => trim($_POST['group_name'] ?? ''),
                'group_description' => trim($_POST['group_description'] ?? ''),
                'group_type_id' => $group_type_id,
                'parent_group_id' => !empty($_POST['parent_group_id']) ? (int)$_POST['parent_group_id'] : null,
                'status' => 'active'
            ];

            // Create group (model will handle all validation)
            if ($this->groupModel->createGroup($data)) {
                set_flash_message('Group added successfully', 'success');
                redirect('groups');
            } else {
                // Get detailed error message from model
                $errorMessage = $this->groupModel->error ?? 'Something went wrong';
                set_flash_message($errorMessage, 'danger');
                redirect('groups/add');
            }
        } else {
            // If not POST request, redirect to add form
            redirect('groups/add');
        }
    }

    /**
     * Display edit group form
     */
    public function edit($id = null) {
        // Get group ID from parameter or fallback to legacy methods
        if ($id === null) {
            $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

            if (!$id) {
                // Try to get ID from URL path
                $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
                $pathParts = explode('/', $path);
                $id = end($pathParts);
            }
        }

        $id = (int)$id;
        // Get group by ID
        $group = $this->groupModel->getGroupById($id);

        if (!$group) {
            set_flash_message('Group not found', 'danger');
            redirect('groups');
            return;
        }

        $groupTypes = $this->groupModel->getAllGroupTypes();
        $parentGroups = $this->groupModel->getAllGroups('active');

        // Remove current group from parent groups list to prevent circular reference
        foreach ($parentGroups as $key => $parentGroup) {
            if ($parentGroup->group_id == $id) {
                unset($parentGroups[$key]);
                break;
            }
        }

        $data = [
            'group' => $group,
            'groupTypes' => $groupTypes,
            'parentGroups' => $parentGroups,
            'page_title' => getPageTitle('Edit Group')
        ];

        render('groups/edit', $data);
    }

    /**
     * Process edit group form
     */
    public function update($id = null) {
        // Get group ID from parameter or fallback to legacy methods
        if ($id === null) {
            $id = isset($_GET['id']) ? (int)$_GET['id'] : $this->extractIdFromUrl();
        }

        $id = (int)$id;

        if (!$id) {
            set_flash_message('Invalid group ID', 'danger');
            redirect('groups');
            return;
        }
        // Check if form was submitted (POST or PUT)
        if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'PUT') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // Prepare data for model (model will handle all validation)
            $data = [
                'group_name' => trim($_POST['group_name'] ?? ''),
                'group_description' => trim($_POST['group_description'] ?? ''),
                'group_type_id' => !empty($_POST['group_type_id']) ? (int)$_POST['group_type_id'] : null,
                'parent_group_id' => !empty($_POST['parent_group_id']) ? (int)$_POST['parent_group_id'] : null,
                'status' => $_POST['status'] ?? 'active'
            ];

            // Update group (model will handle all validation)
            if ($this->groupModel->updateGroup($id, $data)) {
                set_flash_message('Group updated successfully', 'success');
                redirect('groups');
            } else {
                // Get detailed error message from model
                $errorMessage = $this->groupModel->error ?? 'Something went wrong';
                set_flash_message($errorMessage, 'danger');
                redirect("groups/edit/{$id}");
            }
        } else {
            // If not POST request, redirect to edit form
            redirect("groups/edit/{$id}");
        }
    }

    /**
     * Delete a group
     */
    public function delete($id = null) {
        // Get group ID from parameter or fallback to legacy methods
        if ($id === null) {
            $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

            if (!$id) {
                // Try to get ID from URL path
                $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
                $pathParts = explode('/', $path);
                $id = end($pathParts);
            }
        }

        $id = (int)$id;

        if (!$id) {
            set_flash_message('Invalid group ID', 'danger');
            redirect('groups');
            return;
        }
        // Check if form was submitted (POST or DELETE)
        if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'DELETE') {
            // Validate CSRF token
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Invalid CSRF token', 'danger');
                redirect('groups');
                return;
            }

            // Delete group
            if ($this->groupModel->deleteGroup($id)) {
                set_flash_message('Group deleted successfully', 'success');
            } else {
                set_flash_message('Something went wrong', 'danger');
            }
        }

        redirect('groups');
    }

    /**
     * Display group members - COMPLETELY REWRITTEN
     */
    public function members($id = null) {
        // Log method call for debugging (can be removed in production)
        if (defined('DEBUG') && DEBUG) {
            error_log("GroupsController::members() called with ID: " . ($id ?? 'null'));
        }

        try {
            // Get group ID from parameter or URL
            if ($id === null) {
                $id = $this->extractIdFromUrl();
                if (defined('DEBUG') && DEBUG) {
                    error_log("Extracted ID from URL: " . ($id ?? 'null'));
                }
            }

            $id = (int)$id;

            if (!$id || $id <= 0) {
                error_log("Invalid group ID: $id");
                $this->showError('Invalid group ID provided');
                return;
            }

            // Get group by ID with error checking
            $group = $this->groupModel->getGroupById($id);

            if (!$group) {
                error_log("Group not found for ID: $id");
                $this->showError("Group with ID $id not found");
                return;
            }


            // SIMPLIFIED VERSION - Only get basic data that works

            // Check if this is an AJAX request
            if (isset($_GET['ajax']) && $_GET['ajax'] === 'members') {
                $this->getMembersAjax($id);
                return;
            }

            // Get basic member count
            $memberCount = $this->groupModel->getGroupMemberCount($id);

            // Get basic members list (simplified)
            $members = $this->groupModel->getGroupMembers($id);

            // Get real data for tabs
            $meetingSchedule = $this->groupModel->getMeetingSchedule($id);
            $announcements = $this->groupModel->getGroupAnnouncements($id, 10);
            $meetings = $this->groupModel->getGroupMeetings($id, 10);
            $attendanceStats = $this->groupModel->getAttendanceStats($id);
            $availableRoles = ['Member', 'Leader', 'Assistant', 'Secretary', 'Treasurer']; // Default roles
            $duesSettings = $this->groupModel->getDuesSettings($id);
            $currentPeriod = date('Y-m');
            $duesStatistics = $this->groupModel->getDuesStatistics($id, $currentPeriod);
            $memberDuesStatus = $this->groupModel->getMemberDuesStatus($id, $currentPeriod);

            // Basic pagination
            $pagination = [
                'current_page' => 1,
                'total_pages' => 1,
                'total_members' => $memberCount
            ];

            // FULL DATA ARRAY - Restored for original view
            $data = [
                'group' => $group, // This is the key fix - ensure group object is passed
                'membersData' => [
                    'members' => $members,
                    'total' => $memberCount,
                    'current_page' => 1,
                    'total_pages' => 1
                ],
                'members' => $members, // For compatibility
                'memberCount' => $memberCount,
                'availableRoles' => $availableRoles,
                'currentFilters' => [
                    'page' => 1,
                    'limit' => 20,
                    'search' => '',
                    'status' => 'active',
                    'role' => '',
                    'sort' => 'name',
                    'order' => 'ASC'
                ],
                'meetingSchedule' => $meetingSchedule,
                'announcements' => $announcements,
                'meetings' => $meetings,
                'attendanceStats' => $attendanceStats,
                'duesSettings' => $duesSettings,
                'duesStatistics' => $duesStatistics,
                'memberDuesStatus' => $memberDuesStatus,
                'currentPeriod' => $currentPeriod,
                'page_title' => getPageTitle('Group Members - ' . $group->group_name)
            ];

            error_log("About to render view with data");
            error_log("Group name: " . $group->group_name);
            error_log("Member count: $memberCount");

            // Back to original view now that routing is fixed
            render('groups/members', $data);
            error_log("View rendered successfully");

        } catch (Exception $e) {
            error_log('FATAL ERROR in GroupsController::members: ' . $e->getMessage());
            error_log('File: ' . $e->getFile() . ' Line: ' . $e->getLine());
            error_log('Stack trace: ' . $e->getTraceAsString());

            $this->showError('Error loading group members: ' . $e->getMessage());
        }
    }

    /**
     * AJAX endpoint for loading members with pagination and filtering
     */
    private function getMembersAjax($groupId) {
        header('Content-Type: application/json');

        try {
            // Validate CSRF token for AJAX requests
            $token = $_GET['csrf_token'] ?? '';
            if (!verify_csrf_token($token)) {
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid CSRF token'
                ]);
                exit;
            }
            $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? min(100, max(10, (int)$_GET['limit'])) : 20;
            $search = isset($_GET['search']) ? trim($_GET['search']) : '';
            $status = isset($_GET['status']) ? $_GET['status'] : 'active';
            $role = isset($_GET['role']) ? $_GET['role'] : '';
            $sortBy = isset($_GET['sort']) ? $_GET['sort'] : 'name';
            $sortOrder = isset($_GET['order']) ? $_GET['order'] : 'ASC';

            $membersData = $this->groupModel->getGroupMembersPaginated(
                $groupId, $page, $limit, $search, $status, $role, $sortBy, $sortOrder
            );

            // Generate HTML for members
            $html = '';
            if (!empty($membersData['members'])) {
                foreach ($membersData['members'] as $member) {
                    $html .= $this->renderMemberRow($member);
                }
            } else {
                $html = '<tr><td colspan="5" class="px-6 py-8 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-users text-4xl text-gray-300 mb-2"></i>
                        <p class="text-lg font-medium">No members found</p>
                        <p class="text-sm">Try adjusting your search or filter criteria</p>
                    </div>
                </td></tr>';
            }

            echo json_encode([
                'success' => true,
                'html' => $html,
                'pagination' => [
                    'total' => $membersData['total'],
                    'pages' => $membersData['pages'],
                    'current_page' => $membersData['current_page'],
                    'per_page' => $membersData['per_page'],
                    'has_next' => $membersData['has_next'],
                    'has_prev' => $membersData['has_prev']
                ]
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => 'Failed to load members: ' . $e->getMessage()
            ]);
        }
        exit;
    }

    /**
     * Render a single member row for AJAX responses
     */
    private function renderMemberRow($member) {
        $profilePicture = !empty($member->profile_picture)
            ? "<img src=\"/icgc/uploads/members/{$member->profile_picture}\" alt=\"{$member->first_name}\" class=\"h-12 w-12 rounded-full object-cover border-2 border-green-200 shadow-md\">"
            : "<div class=\"h-12 w-12 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center text-white font-bold text-lg shadow-md border-2 border-green-200\">"
              . substr($member->first_name ?? 'U', 0, 1) . substr($member->last_name ?? 'N', 0, 1) . "</div>";

        $fullName = htmlspecialchars(($member->first_name ?? 'Unknown') . ' ' . ($member->last_name ?? 'Name'));
        $joinedDate = isset($member->joined_date) ? date('M d, Y', strtotime($member->joined_date)) : 'Unknown';

        return "
        <tr class=\"hover:bg-gray-50 transition-colors duration-150\">
            <td class=\"px-6 py-4 whitespace-nowrap\">
                <div class=\"flex items-center\">
                    <div class=\"flex-shrink-0 h-12 w-12\">
                        {$profilePicture}
                    </div>
                    <div class=\"ml-4\">
                        <div class=\"text-sm font-bold text-gray-900\">{$fullName}</div>
                        <div class=\"text-xs text-gray-500 flex items-center gap-2\">
                            <span class=\"bg-gray-100 px-2 py-1 rounded-full\">ID: " . ($member->id ?? 'N/A') . "</span>
                            <span class=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">" . ucfirst($member->gender ?? 'unknown') . "</span>
                        </div>
                    </div>
                </div>
            </td>
            <td class=\"px-6 py-4 whitespace-nowrap\">
                <div class=\"space-y-1\">
                    <div class=\"flex items-center text-sm text-gray-900\">
                        <i class=\"fas fa-phone text-green-600 mr-2\"></i>
                        " . htmlspecialchars($member->phone_number ?? 'N/A') . "
                    </div>
                    <div class=\"flex items-center text-sm text-gray-500\">
                        <i class=\"fas fa-envelope text-blue-600 mr-2\"></i>
                        " . htmlspecialchars($member->email ?? 'N/A') . "
                    </div>
                </div>
            </td>
            <td class=\"px-6 py-4 whitespace-nowrap\">
                <span class=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300 shadow-sm\">
                    <i class=\"fas fa-user-tag mr-1\"></i>
                    " . ucfirst($member->role_in_group ?? 'member') . "
                </span>
            </td>
            <td class=\"px-6 py-4 whitespace-nowrap\">
                <div class=\"flex items-center text-sm text-gray-600\">
                    <i class=\"fas fa-calendar-plus text-orange-600 mr-2\"></i>
                    <span class=\"bg-orange-50 px-2 py-1 rounded-full text-orange-800 font-medium text-xs\">
                        {$joinedDate}
                    </span>
                </div>
            </td>
            <td class=\"px-6 py-4 whitespace-nowrap text-right\">
                <div class=\"flex justify-end space-x-2\">
                    <a href=\"" . BASE_URL . "members/view/" . ($member->id ?? 1) . "\" class=\"bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200 flex items-center\" title=\"View Member\">
                        <i class=\"fas fa-eye mr-1\"></i>View
                    </a>
                    <button onclick=\"confirmRemoveMember(" . ($member->id ?? 1) . ", '" . addslashes($fullName) . "')\" class=\"bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200 flex items-center\" title=\"Remove from Group\">
                        <i class=\"fas fa-user-minus mr-1\"></i>Remove
                    </button>
                </div>
            </td>
        </tr>";
    }

    /**
     * Display add members to group form
     */
    public function addMembers($id = null) {
        // Get group ID from parameter or fallback to legacy methods
        if ($id === null) {
            $id = $this->extractIdFromUrl();
        }

        $id = (int)$id;

        if (!$id) {
            set_flash_message('Invalid group ID', 'danger');
            redirect('groups');
            return;
        }

        // Get group by ID
        $group = $this->groupModel->getGroupById($id);

        if (!$group) {
            set_flash_message('Group not found', 'danger');
            redirect('groups');
            return;
        }

        // Handle AJAX request for paginated members
        if (isset($_GET['ajax']) && $_GET['ajax'] === 'members') {
            $this->getAvailableMembersAjax($id);
            return;
        }

        // Get pagination parameters
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = isset($_GET['per_page']) ? min(50, max(10, (int)$_GET['per_page'])) : 20; // Allow 10-50 members per page
        $offset = ($page - 1) * $limit;
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $gender = isset($_GET['gender']) ? trim($_GET['gender']) : '';

        // Get current group members to exclude them
        $currentMembers = $this->groupModel->getGroupMembers($id);
        $currentMemberIds = array_map(function($member) {
            return $member->id;
        }, $currentMembers);

        // Get paginated adults only (18+ years) - children are automatically excluded for safety
        $availableMembersStmt = $this->memberModel->getAdultsOnly($limit, $offset, $search, $gender, $currentMemberIds);
        $availableMembers = $availableMembersStmt->fetchAll(PDO::FETCH_OBJ);

        // Get total count for pagination (excluding current group members)
        $totalAvailable = $this->memberModel->countAdultsOnly($search, $gender, $currentMemberIds);
        $totalPages = ceil($totalAvailable / $limit);

        // Pagination data
        $pagination = [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_records' => $totalAvailable,
            'per_page' => $limit,
            'has_prev' => $page > 1,
            'has_next' => $page < $totalPages,
            'prev_page' => $page > 1 ? $page - 1 : null,
            'next_page' => $page < $totalPages ? $page + 1 : null
        ];

        $data = [
            'group' => $group,
            'availableMembers' => $availableMembers,
            'pagination' => $pagination,
            'search' => $search,
            'gender' => $gender,
            'page_title' => getPageTitle('Add Members to ' . $group->group_name)
        ];

        render('groups/add-members', $data);
    }

    /**
     * AJAX endpoint to get available members with pagination
     */
    private function getAvailableMembersAjax($groupId) {
        header('Content-Type: application/json');

        try {
            // Validate CSRF token for AJAX requests
            $token = $_GET['csrf_token'] ?? '';
            if (!verify_csrf_token($token)) {
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid CSRF token'
                ]);
                exit;
            }
            // Get pagination parameters
            $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
            $limit = isset($_GET['per_page']) ? min(50, max(10, (int)$_GET['per_page'])) : 20; // Allow 10-50 members per page
            $offset = ($page - 1) * $limit;
            $search = isset($_GET['search']) ? trim($_GET['search']) : '';
            $gender = isset($_GET['gender']) ? trim($_GET['gender']) : '';

            // Get current group members to exclude them
            $currentMembers = $this->groupModel->getGroupMembers($groupId);
            $currentMemberIds = array_map(function($member) {
                return $member->id;
            }, $currentMembers);

            // Get paginated adults only (18+ years)
            $availableMembersStmt = $this->memberModel->getAdultsOnly($limit, $offset, $search, $gender, $currentMemberIds);
            $availableMembers = $availableMembersStmt->fetchAll(PDO::FETCH_OBJ);

            // Get total count for pagination
            $totalAvailable = $this->memberModel->countAdultsOnly($search, $gender, $currentMemberIds);
            $totalPages = ceil($totalAvailable / $limit);

            // Generate HTML for the member rows
            $html = '';
            if (empty($availableMembers)) {
                $html = '<tr>
                    <td colspan="5" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Available Members</h3>
                            <p class="text-gray-500">No members match your search criteria or all members are already part of this group.</p>
                        </div>
                    </td>
                </tr>';
            } else {
                foreach ($availableMembers as $member) {
                    $profilePicture = !empty($member->profile_picture)
                        ? '<img class="h-12 w-12 rounded-full object-cover border-2 border-green-200 shadow-md" src="/icgc/uploads/members/' . htmlspecialchars($member->profile_picture) . '" alt="' . htmlspecialchars($member->first_name) . '">'
                        : '<div class="h-12 w-12 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center text-white font-bold text-lg shadow-md border-2 border-green-200">' .
                          substr($member->first_name, 0, 1) . substr($member->last_name, 0, 1) . '</div>';

                    $age = 'N/A';
                    if (!empty($member->date_of_birth)) {
                        $birthdate = new DateTime($member->date_of_birth);
                        $today = new DateTime();
                        $age = $birthdate->diff($today)->y . ' years';
                    }

                    $genderClass = $member->gender === 'Male' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800';
                    $genderIcon = $member->gender === 'Male' ? 'fa-mars' : 'fa-venus';

                    $html .= '<tr class="member-row hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-200"
                                  data-name="' . strtolower($member->first_name . ' ' . $member->last_name) . '"
                                  data-phone="' . htmlspecialchars($member->phone_number) . '"
                                  data-email="' . strtolower($member->email) . '"
                                  data-gender="' . htmlspecialchars($member->gender) . '">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <input type="checkbox" name="member_ids[]" value="' . $member->id . '" class="member-checkbox h-5 w-5 text-green-600 focus:ring-green-500 rounded border-2 border-gray-300">
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-12 w-12">' . $profilePicture . '</div>
                                <div class="ml-4">
                                    <div class="text-sm font-bold text-gray-900">' . htmlspecialchars($member->first_name . ' ' . $member->last_name) . '</div>
                                    <div class="text-xs text-gray-500 flex items-center gap-2">
                                        <span class="bg-gray-100 px-2 py-1 rounded-full">ID: ' . $member->id . '</span>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="space-y-1">
                                <div class="flex items-center text-sm text-gray-900">
                                    <i class="fas fa-phone text-green-600 mr-2"></i>
                                    ' . htmlspecialchars($member->phone_number) . '
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <i class="fas fa-envelope text-blue-600 mr-2"></i>
                                    ' . htmlspecialchars($member->email) . '
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold ' . $genderClass . ' border shadow-sm">
                                <i class="fas ' . $genderIcon . ' mr-1"></i>
                                ' . htmlspecialchars($member->gender) . '
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-birthday-cake text-orange-600 mr-2"></i>
                                <span class="bg-orange-50 px-2 py-1 rounded-full text-orange-800 font-medium text-xs">' . $age . '</span>
                            </div>
                        </td>
                    </tr>';
                }
            }

            // Pagination data
            $pagination = [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_records' => $totalAvailable,
                'per_page' => $limit,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null
            ];

            echo json_encode([
                'success' => true,
                'html' => $html,
                'pagination' => $pagination
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to load members: ' . $e->getMessage()
            ]);
        }

        exit;
    }

    /**
     * Process add members to group form
     * @param int $id Group ID
     */
    public function addMembersProcess($id = null) {
        // Get group ID from parameter or fallback to legacy methods
        if ($id === null) {
            $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

            if (!$id) {
                // Try to get ID from URL path
                $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
                $pathParts = explode('/', $path);
                $id = end($pathParts);
            }
        }

        $id = (int)$id;
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Get selected member IDs
            $memberIds = isset($_POST['member_ids']) ? $_POST['member_ids'] : [];

            if (empty($memberIds)) {
                set_flash_message('No members selected', 'danger');
                redirect("groups/members/{$id}");
                return;
            }

            // Get role for members
            $role = isset($_POST['role_in_group']) ? $_POST['role_in_group'] : 'member';

            // Add members to group
            $result = $this->groupModel->addMembersToGroup($memberIds, $id, $role);

            // Create appropriate success message based on results
            $messages = [];
            if ($result['added'] > 0) {
                $messages[] = $result['added'] . ' new member(s) added to the group';
            }
            if ($result['updated'] > 0) {
                $messages[] = $result['updated'] . ' member(s) were already in the group and updated';
            }
            if ($result['failed'] > 0) {
                $messages[] = $result['failed'] . ' member(s) failed to be processed';
            }

            if (!empty($messages)) {
                $message = implode(', ', $messages) . '.';
                $type = ($result['failed'] > 0) ? 'yellow' : 'green';
                set_flash_message($message, $type === 'yellow' ? 'warning' : 'success');
            } else {
                set_flash_message('No members were processed', 'danger');
            }

            redirect("groups/members/{$id}");
        } else {
            // If not POST request, redirect to members page
            redirect("groups/members/{$id}");
        }
    }

    /**
     * Remove a member from a group
     */
    public function removeMember($groupId = null, $memberId = null) {
        // Get IDs from parameters or fallback to legacy URL parsing
        if ($groupId === null || $memberId === null) {
            $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
            $pathParts = explode('/', $path);

            // The URL format is /groups/remove-member/{groupId}/{memberId}
            $groupId = isset($pathParts[count($pathParts) - 2]) ? (int)$pathParts[count($pathParts) - 2] : 0;
            $memberId = isset($pathParts[count($pathParts) - 1]) ? (int)$pathParts[count($pathParts) - 1] : 0;
        }

        $groupId = (int)$groupId;
        $memberId = (int)$memberId;

        if (!$groupId || !$memberId) {
            set_flash_message('Invalid request', 'danger');
            redirect('groups');
            return;
        }

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Remove member from group
            if ($this->groupModel->removeMemberFromGroup($memberId, $groupId)) {
                set_flash_message('Member removed from group successfully', 'success');
            } else {
                set_flash_message('Something went wrong', 'danger');
            }
        }

        redirect("groups/members/{$groupId}");
    }

    /**
     * Update a member's role in a group
     */
    public function updateRole($groupId = null, $memberId = null) {
        // Get IDs from parameters or fallback to legacy URL parsing
        if ($groupId === null || $memberId === null) {
            $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
            $pathParts = explode('/', $path);

            // The URL format is /groups/update-role/{groupId}/{memberId}
            $groupId = isset($pathParts[count($pathParts) - 2]) ? (int)$pathParts[count($pathParts) - 2] : 0;
            $memberId = isset($pathParts[count($pathParts) - 1]) ? (int)$pathParts[count($pathParts) - 1] : 0;
        }

        $groupId = (int)$groupId;
        $memberId = (int)$memberId;

        if (!$groupId || !$memberId) {
            set_flash_message('Invalid request', 'danger');
            redirect('groups');
            return;
        }
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Get new role
            $role = $_POST['role'] ?? 'member';

            // Update member role
            if ($this->groupModel->updateMemberRole($memberId, $groupId, $role)) {
                set_flash_message('Member role updated successfully', 'success');
            } else {
                set_flash_message('Something went wrong', 'danger');
            }
        }

        redirect("groups/members/{$groupId}");
    }

    /**
     * Save meeting schedule for a group
     */
    public function saveSchedule() {
        // Get group ID from URL
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', $path);
        $groupId = end($pathParts);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Invalid CSRF token', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            $scheduleData = [
                'group_id' => $groupId,
                'meeting_day' => $_POST['meeting_day'],
                'meeting_time' => $_POST['meeting_time'],
                'meeting_location' => $_POST['meeting_location'] ?? '',
                'description' => $_POST['description'] ?? '',
                'duration' => intval($_POST['duration'] ?? 60)
            ];

            // Validate required fields
            if (empty($scheduleData['meeting_day']) || empty($scheduleData['meeting_time'])) {
                set_flash_message('Please select both meeting day and time', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Save schedule
            if ($this->groupModel->saveSchedule($scheduleData)) {
                set_flash_message('Meeting schedule saved successfully', 'success');
            } else {
                set_flash_message('Failed to save meeting schedule', 'danger');
            }
        }

        redirect("groups/members/{$groupId}");
    }

    /**
     * Delete meeting schedule for a group
     */
    public function deleteSchedule() {
        // Get group ID from URL
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', $path);
        $groupId = end($pathParts);

        if ($this->groupModel->deleteSchedule($groupId)) {
            set_flash_message('Meeting schedule deleted successfully', 'success');
        } else {
            set_flash_message('Failed to delete meeting schedule', 'danger');
        }

        redirect("groups/members/{$groupId}");
    }

    /**
     * Create a new group announcement
     */
    public function createAnnouncement() {
        // Get group ID from URL
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', $path);
        $groupId = end($pathParts);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Invalid CSRF token', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // For now, we'll use the first available member ID - in a real app, this would come from session
            $stmt = $this->memberModel->getAll();
            $firstMember = $stmt->fetch(PDO::FETCH_OBJ);
            $postedBy = $firstMember ? $firstMember->id : 101; // TODO: Get from session when user authentication is implemented

            $announcementData = [
                'group_id' => $groupId,
                'title' => trim($_POST['title']),
                'content' => trim($_POST['content']),
                'announcement_type' => $_POST['announcement_type'],
                'posted_by' => $postedBy,
                'expires_at' => !empty($_POST['expires_at']) ? $_POST['expires_at'] : null
            ];

            // Validate required fields
            if (empty($announcementData['title']) || empty($announcementData['content'])) {
                set_flash_message('Title and content are required', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Create announcement
            if ($this->groupModel->createAnnouncement($announcementData)) {
                set_flash_message('Announcement created successfully', 'success');
            } else {
                set_flash_message('Failed to create announcement', 'danger');
            }
        }

        redirect("groups/members/{$groupId}");
    }

    /**
     * Update an existing announcement
     */
    public function updateAnnouncement() {
        // Get announcement ID from URL
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', $path);
        $announcementId = end($pathParts);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            $announcementData = [
                'title' => trim($_POST['title']),
                'content' => trim($_POST['content']),
                'announcement_type' => $_POST['announcement_type'],
                'expires_at' => !empty($_POST['expires_at']) ? $_POST['expires_at'] : null
            ];

            // Validate required fields
            if (empty($announcementData['title']) || empty($announcementData['content'])) {
                set_flash_message('Title and content are required', 'danger');
                redirect($_SERVER['HTTP_REFERER'] ?? 'groups');
                return;
            }

            // Update announcement
            if ($this->groupModel->updateAnnouncement($announcementId, $announcementData)) {
                set_flash_message('Announcement updated successfully', 'success');
            } else {
                set_flash_message('Failed to update announcement', 'danger');
            }
        }

        redirect($_SERVER['HTTP_REFERER'] ?? 'groups');
    }

    /**
     * Delete an announcement
     */
    public function deleteAnnouncement() {
        // Get announcement ID from URL
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', $path);
        $announcementId = end($pathParts);

        if ($this->groupModel->deleteAnnouncement($announcementId)) {
            set_flash_message('Announcement deleted successfully', 'success');
        } else {
            set_flash_message('Failed to delete announcement', 'danger');
        }

        redirect($_SERVER['HTTP_REFERER'] ?? 'groups');
    }

    /**
     * Create a new meeting and record attendance
     */
    public function createMeeting() {
        // Get group ID from URL
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', $path);
        $groupId = end($pathParts);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Invalid CSRF token', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // For now, we'll use the first available member ID - in a real app, this would come from session
            $stmt = $this->memberModel->getAll();
            $firstMember = $stmt->fetch(PDO::FETCH_OBJ);
            $createdBy = $firstMember ? $firstMember->id : 101; // TODO: Get from session when user authentication is implemented

            $meetingData = [
                'group_id' => $groupId,
                'meeting_date' => $_POST['meeting_date'],
                'meeting_time' => $_POST['meeting_time'],
                'meeting_location' => $_POST['meeting_location'] ?? '',
                'meeting_topic' => $_POST['meeting_topic'] ?? '',
                'meeting_notes' => $_POST['meeting_notes'] ?? '',
                'created_by' => $createdBy
            ];

            // Validate required fields
            if (empty($meetingData['meeting_date']) || empty($meetingData['meeting_time'])) {
                set_flash_message('Meeting date and time are required', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Create meeting
            $meetingId = $this->groupModel->createMeeting($meetingData);

            if ($meetingId) {
                // Record attendance for selected members
                if (!empty($_POST['attendance'])) {
                    foreach ($_POST['attendance'] as $memberId => $status) {
                        $attendanceData = [
                            'meeting_id' => $meetingId,
                            'member_id' => $memberId,
                            'attendance_status' => $status,
                            'check_in_time' => ($status === 'present') ? date('Y-m-d H:i:s') : null,
                            'notes' => $_POST['notes'][$memberId] ?? '',
                            'recorded_by' => $createdBy
                        ];

                        $this->groupModel->recordAttendance($attendanceData);
                    }
                }

                set_flash_message('Meeting and attendance recorded successfully', 'success');
            } else {
                set_flash_message('Failed to create meeting', 'danger');
            }
        }

        redirect("groups/members/{$groupId}");
    }

    /**
     * View attendance for a specific meeting
     */
    public function viewAttendance() {
        // Get meeting ID from URL
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', $path);
        $meetingId = end($pathParts);

        // Get meeting details
        $meeting = $this->groupModel->getMeetingById($meetingId);
        if (!$meeting) {
            set_flash_message('Meeting not found', 'danger');
            redirect('groups');
            return;
        }

        // Get attendance records
        $attendance = $this->groupModel->getMeetingAttendance($meetingId);

        $data = [
            'meeting' => $meeting,
            'attendance' => $attendance,
            'page_title' => getPageTitle('Meeting Attendance - ' . $meeting->meeting_topic)
        ];

        render('groups/attendance', $data);
    }

    /**
     * Save dues settings for a group
     */
    public function saveDuesSettings() {
        // Get group ID from URL
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', $path);
        $groupId = end($pathParts);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Invalid CSRF token', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            $duesData = [
                'dues_amount' => floatval($_POST['dues_amount']),
                'dues_frequency' => $_POST['dues_frequency'],
                'due_date' => intval($_POST['due_date']),
                'currency' => $_POST['currency']
            ];

            // Validate required fields
            if (empty($duesData['dues_amount']) || $duesData['dues_amount'] <= 0) {
                set_flash_message('Please enter a valid dues amount', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Save dues settings
            if ($this->groupModel->saveDuesSettings($groupId, $duesData)) {
                set_flash_message('Dues settings saved successfully', 'success');
            } else {
                set_flash_message('Failed to save dues settings', 'danger');
            }
        }

        redirect("groups/members/{$groupId}?tab=dues");
    }

    /**
     * Record a dues payment
     */
    public function recordDuesPayment() {
        // Get group ID from URL
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', $path);
        $groupId = end($pathParts);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Invalid CSRF token', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // For now, we'll use the first available member ID - in a real app, this would come from session
            $stmt = $this->memberModel->getAll();
            $firstMember = $stmt->fetch(PDO::FETCH_OBJ);
            $recordedBy = $firstMember ? $firstMember->id : 101; // TODO: Get from session when user authentication is implemented

            // Check if this is completing a partial payment
            $isCompletingPartial = isset($_POST['completing_partial']) && $_POST['completing_partial'] === 'true';

            $paymentData = [
                'group_id' => $groupId,
                'member_id' => intval($_POST['member_id']),
                'payment_amount' => floatval($_POST['payment_amount']),
                'payment_date' => $_POST['payment_date'],
                'payment_period' => $_POST['payment_period'],
                'payment_method' => $_POST['payment_method'],
                'payment_reference' => $_POST['payment_reference'] ?? '',
                'payment_notes' => $_POST['payment_notes'] ?? '',
                'recorded_by' => $recordedBy,
                'is_additional' => $isCompletingPartial
            ];

            // Validate required fields
            if (empty($paymentData['member_id']) || empty($paymentData['payment_amount']) || $paymentData['payment_amount'] <= 0) {
                set_flash_message('Please select a member and enter a valid payment amount', 'danger');
                redirect("groups/members/{$groupId}");
                return;
            }

            // Record payment
            if ($this->groupModel->recordDuesPayment($paymentData)) {
                if ($isCompletingPartial) {
                    set_flash_message('Partial payment completed successfully', 'success');
                } else {
                    set_flash_message('Payment recorded successfully', 'success');
                }
            } else {
                set_flash_message('Failed to record payment', 'danger');
            }
        }

        redirect("groups/members/{$groupId}?tab=dues");
    }

    /**
     * Validate group form data
     * @param array $postData POST data
     * @param string $action Action type (create/update)
     * @return array|false Validated data or false on failure
     */
    private function validateGroupData($postData, $action = 'create') {
        // Sanitize POST data
        $postData = filter_var_array($postData, FILTER_SANITIZE_STRING);

        // Handle group type
        $group_type_id = null;
        if (!empty($postData['new_group_type'])) {
            // Create new group type
            $newTypeData = ['type_name' => trim($postData['new_group_type'])];
            $group_type_id = $this->groupModel->createGroupType($newTypeData);
            if (!$group_type_id) {
                set_flash_message('Failed to create new group type', 'danger');
                $this->redirectBasedOnAction($action);
                return false;
            }
        } else {
            $group_type_id = !empty($postData['group_type_id']) ? (int)$postData['group_type_id'] : null;
        }

        // Prepare validated data
        $data = [
            'group_name' => trim($postData['group_name']),
            'group_description' => trim($postData['group_description']),
            'group_type_id' => $group_type_id,
            'parent_group_id' => !empty($postData['parent_group_id']) ? (int)$postData['parent_group_id'] : null,
            'status' => $action === 'create' ? 'active' : $postData['status']
        ];

        // Validate required fields
        if (empty($data['group_name'])) {
            set_flash_message('Group name is required', 'danger');
            $this->redirectBasedOnAction($action);
            return false;
        }

        return $data;
    }

    /**
     * Redirect based on action type
     * @param string $action Action type
     * @param int|null $id Group ID for update actions
     */
    private function redirectBasedOnAction($action, $id = null) {
        switch ($action) {
            case 'create':
                redirect('groups/add');
                break;
            case 'update':
                redirect("groups/edit/{$id}");
                break;
            default:
                redirect('groups');
        }
    }

    /**
     * Extract ID from URL path - Fixed to handle groups/members/ID correctly
     * @return int|null Extracted ID or null if not found
     */
    private function extractIdFromUrl() {
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

        // Remove base path if present
        if (strpos($path, '/icgc/') === 0) {
            $path = substr($path, 5); // Remove '/icgc/'
        }

        $pathParts = array_filter(explode('/', $path)); // Remove empty parts

        // For groups/members/ID pattern, get the last numeric part
        foreach (array_reverse($pathParts) as $part) {
            if (is_numeric($part)) {
                return (int)$part;
            }
        }

        return null;
    }

    /**
     * Show error message and redirect to groups page
     * @param string $message Error message to display
     */
    private function showError($message) {
        error_log("GroupsController Error: $message");
        set_flash_message($message, 'danger');
        redirect('groups');
    }
}
