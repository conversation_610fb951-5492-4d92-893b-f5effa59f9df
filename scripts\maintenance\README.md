# Maintenance Scripts

This directory contains maintenance scripts for the ICGC Emmanuel Temple Church Management System.
These scripts are designed to be run from the **Command Line Interface (CLI)** only. They should never be accessible via a web browser.

## Purpose

Maintenance scripts are used to perform various administrative and cleanup tasks on the application. They can be used to:

- Fix data integrity issues (e.g., records stuck in a certain state).
- Perform data migrations or large-scale updates.
- Clean up old or temporary data.
- Perform other batch operations that are not suitable for the web interface.

## Files

- `sms_tasks.php` - A tool for managing SMS-related data.

## Usage

These scripts must be run from the server's terminal. Navigate to the project's root directory and execute the script using PHP.

**Example: Fixing stuck SMS messages**
```bash
# To see what the script would do without making changes
php scripts/maintenance/sms_tasks.php --fix-stuck --dry-run

# To actually fix the stuck messages
php scripts/maintenance/sms_tasks.php --fix-stuck
```
