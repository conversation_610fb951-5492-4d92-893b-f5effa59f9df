<?php
/**
 * Dynamic Category Dashboard Controller
 * Automatically generates dashboards for any payment category
 */

class CategoryDashboardController {
    private $db;
    private $categoryDashboardService;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->categoryDashboardService = new CategoryDashboardService();
    }
    
    /**
     * Show dashboard for a specific category
     */
    public function show($categorySlug) {
        try {
            // Get category by slug
            $category = $this->getCategoryBySlug($categorySlug);
            
            if (!$category) {
                $_SESSION['error'] = "Category dashboard not found.";
                header('Location: ' . BASE_URL . 'finance');
                exit;
            }
            
            // Get dashboard data
            $dashboardData = $this->categoryDashboardService->getDashboardData($category);
            
            // Prepare view data
            $data = [
                'category' => $category,
                'dashboardData' => $dashboardData,
                'pageTitle' => $category->label . ' Dashboard'
            ];
            
            // Load the dynamic dashboard view
            $content = $this->loadView('category_dashboard/index', $data);
            include 'views/layouts/main.php';
            
        } catch (Exception $e) {
            error_log("Category Dashboard Error: " . $e->getMessage());
            $_SESSION['error'] = "Error loading dashboard.";
            header('Location: ' . BASE_URL . 'finance');
            exit;
        }
    }
    
    /**
     * Get category by slug
     */
    private function getCategoryBySlug($slug) {
        $stmt = $this->db->prepare("
            SELECT * FROM custom_finance_categories
            WHERE slug = ? AND is_active = 1
        ");
        $stmt->execute([$slug]);
        return $stmt->fetch(PDO::FETCH_OBJ);
    }
    
    /**
     * Get member payment history for category
     */
    public function getMemberHistory($categorySlug) {
        try {
            $category = $this->getCategoryBySlug($categorySlug);
            
            if (!$category) {
                http_response_code(404);
                echo json_encode(['error' => 'Category not found']);
                return;
            }
            
            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 10;
            $search = $_GET['search'] ?? '';
            
            $memberHistory = $this->categoryDashboardService->getMemberPaymentHistory(
                $category->id, 
                $page, 
                $limit, 
                $search
            );
            
            header('Content-Type: application/json');
            echo json_encode($memberHistory);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Error loading member history']);
        }
    }
    
    /**
     * Export category data
     */
    public function export($categorySlug) {
        try {
            $category = $this->getCategoryBySlug($categorySlug);
            
            if (!$category) {
                $_SESSION['error'] = "Category not found.";
                header('Location: ' . BASE_URL . 'finance');
                exit;
            }
            
            $format = $_GET['format'] ?? 'csv';
            $this->categoryDashboardService->exportCategoryData($category, $format);
            
        } catch (Exception $e) {
            error_log("Export Error: " . $e->getMessage());
            $_SESSION['error'] = "Error exporting data.";
            header('Location: ' . BASE_URL . 'finance/category-dashboard/' . $categorySlug);
            exit;
        }
    }
    
    /**
     * Load view file
     */
    private function loadView($viewPath, $data = []) {
        extract($data);
        ob_start();
        include "views/{$viewPath}.php";
        return ob_get_clean();
    }
}
