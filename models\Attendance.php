<?php
/**
 * Attendance Model
 */

class Attendance {
    // Database connection and table name
    public $conn;
    public $table_name = "attendance";

    // Object properties
    public $id;
    public $service_id;
    public $member_id;
    public $attendance_date;
    public $status;
    public $qr_session_id;
    public $marked_via;
    public $marked_by_user;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all attendance records with service and member details
     *
     * @return PDOStatement
     */
    public function getAll() {
        $query = "SELECT a.*, s.name as service_name,
                  CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  LEFT JOIN members m ON a.member_id = m.id
                  ORDER BY a.attendance_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get all attendance records with pagination
     *
     * @param int $limit
     * @param int $offset
     * @return PDOStatement
     */
    public function getAllWithPagination($limit, $offset) {
        $query = "SELECT a.*, s.name as service_name,
                  CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  LEFT JOIN members m ON a.member_id = m.id
                  ORDER BY a.attendance_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Count all attendance records
     *
     * @return int
     */
    public function countAll() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'];
    }

    /**
     * Get attendance by ID
     *
     * @param int $id
     * @return bool
     */
    public function getById($id) {
        // Debug the ID value
        error_log("Fetching attendance with ID: " . $id);

        $query = "SELECT a.*, s.name as service_name,
                  CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  LEFT JOIN members m ON a.member_id = m.id
                  WHERE a.id = :id
                  LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            $this->id = $row['id'];
            $this->service_id = $row['service_id'];
            $this->member_id = $row['member_id'];
            $this->attendance_date = $row['attendance_date'];
            $this->status = $row['status'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];

            error_log("Found attendance record with ID: " . $id);
            return true;
        }

        error_log("No attendance record found with ID: " . $id);
        return false;
    }

    /**
     * Create attendance record
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (service_id, member_id, attendance_date, status, qr_session_id, marked_via, marked_by_user, created_at, updated_at)
                  VALUES
                  (:service_id, :member_id, :attendance_date, :status, :qr_session_id, :marked_via, :marked_by_user, :created_at, :updated_at)";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->service_id = htmlspecialchars(strip_tags($this->service_id));
        $this->member_id = $this->member_id ? htmlspecialchars(strip_tags($this->member_id)) : null;
        $this->attendance_date = htmlspecialchars(strip_tags($this->attendance_date));
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->qr_session_id = $this->qr_session_id ? htmlspecialchars(strip_tags($this->qr_session_id)) : null;
        $this->marked_via = $this->marked_via ? htmlspecialchars(strip_tags($this->marked_via)) : 'manual';
        $this->marked_by_user = $this->marked_by_user ? htmlspecialchars(strip_tags($this->marked_by_user)) : null;
        $this->created_at = htmlspecialchars(strip_tags($this->created_at));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':service_id', $this->service_id);
        $stmt->bindParam(':member_id', $this->member_id);
        $stmt->bindParam(':attendance_date', $this->attendance_date);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':qr_session_id', $this->qr_session_id);
        $stmt->bindParam(':marked_via', $this->marked_via);
        $stmt->bindParam(':marked_by_user', $this->marked_by_user);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Update attendance record
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET service_id = :service_id,
                      member_id = :member_id,
                      attendance_date = :attendance_date,
                      status = :status,
                      qr_session_id = :qr_session_id,
                      marked_via = :marked_via,
                      marked_by_user = :marked_by_user,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->id = htmlspecialchars(strip_tags($this->id));
        $this->service_id = htmlspecialchars(strip_tags($this->service_id));
        $this->member_id = $this->member_id ? htmlspecialchars(strip_tags($this->member_id)) : null;
        $this->attendance_date = htmlspecialchars(strip_tags($this->attendance_date));
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->qr_session_id = $this->qr_session_id ? htmlspecialchars(strip_tags($this->qr_session_id)) : null;
        $this->marked_via = $this->marked_via ? htmlspecialchars(strip_tags($this->marked_via)) : 'manual';
        $this->marked_by_user = $this->marked_by_user ? htmlspecialchars(strip_tags($this->marked_by_user)) : null;
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':service_id', $this->service_id);
        $stmt->bindParam(':member_id', $this->member_id);
        $stmt->bindParam(':attendance_date', $this->attendance_date);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':qr_session_id', $this->qr_session_id);
        $stmt->bindParam(':marked_via', $this->marked_via);
        $stmt->bindParam(':marked_by_user', $this->marked_by_user);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Delete attendance record
     *
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameter
        $stmt->bindParam(':id', $this->id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Get attendance by service
     *
     * @param int $service_id
     * @return PDOStatement
     */
    public function getByService($service_id) {
        $query = "SELECT a.*, s.name as service_name,
                  CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  LEFT JOIN members m ON a.member_id = m.id
                  WHERE a.service_id = :service_id
                  ORDER BY a.attendance_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get attendance by date
     *
     * @param string $date
     * @param int|null $service_id Optional service ID to filter by
     * @return PDOStatement
     */
    public function getByDate($date, $service_id = null) {
        $query = "SELECT a.*, s.name as service_name,
                  CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  LEFT JOIN members m ON a.member_id = m.id
                  WHERE a.attendance_date = :date";

        // Add service filter if provided
        if ($service_id) {
            $query .= " AND a.service_id = :service_id";
        }

        $query .= " ORDER BY s.name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);

        // Bind service_id if provided
        if ($service_id) {
            $stmt->bindParam(':service_id', $service_id);
        }

        $stmt->execute();

        return $stmt;
    }

    /**
     * Count attendance for a specific date
     *
     * @param string $date
     * @param int|null $service_id Optional service ID to filter by
     * @return int
     */
    public function getCountByDate($date, $service_id = null) {
        $query = "SELECT COUNT(*) as total
                  FROM " . $this->table_name . " a
                  WHERE a.attendance_date = :date
                  AND a.status = 'present'";

        // Add service filter if provided
        if ($service_id) {
            $query .= " AND a.service_id = :service_id";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);

        // Bind service_id if provided
        if ($service_id) {
            $stmt->bindParam(':service_id', $service_id);
        }

        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return (int)$row['total'];
    }

    /**
     * Get recent attendance records
     *
     * @param int $limit
     * @return array
     */
    public function getRecent($limit = 5) {
        $query = "SELECT a.*, s.name as service_name,
                  CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  LEFT JOIN members m ON a.member_id = m.id
                  ORDER BY a.attendance_date DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get the most recent service with attendance data
     *
     * @return array
     */
    public function getMostRecentService() {
        $query = "SELECT a.service_id, a.attendance_date, s.name as service_name, s.day_of_week, s.time
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  ORDER BY a.attendance_date DESC, s.time DESC
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        // If no attendance records exist, return default values
        if (!$result) {
            return [
                'service_id' => 1,
                'attendance_date' => date('Y-m-d'),
                'service_name' => 'Sunday Morning Service',
                'day_of_week' => 'sunday',
                'time' => '09:00:00'
            ];
        }

        return $result;
    }

    /**
     * Get attendance statistics
     *
     * @return array
     */
    public function getStats() {
        // Get total attendance count
        $query1 = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt1 = $this->conn->prepare($query1);
        $stmt1->execute();
        $total = $stmt1->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

        // Get attendance by status
        $query2 = "SELECT status, COUNT(*) as count FROM " . $this->table_name . " GROUP BY status";
        $stmt2 = $this->conn->prepare($query2);
        $stmt2->execute();
        $by_status = $stmt2->fetchAll(PDO::FETCH_ASSOC);

        // Get attendance by service
        $query3 = "SELECT s.name, COUNT(*) as count
                   FROM " . $this->table_name . " a
                   LEFT JOIN services s ON a.service_id = s.id
                   GROUP BY a.service_id
                   ORDER BY count DESC";
        $stmt3 = $this->conn->prepare($query3);
        $stmt3->execute();
        $by_service = $stmt3->fetchAll(PDO::FETCH_ASSOC);

        // Get attendance trend (last 5 weeks)
        $query4 = "SELECT DATE_FORMAT(attendance_date, '%Y-%m-%d') as date, COUNT(*) as count
                   FROM " . $this->table_name . "
                   WHERE attendance_date >= DATE_SUB(CURDATE(), INTERVAL 5 WEEK)
                   GROUP BY DATE_FORMAT(attendance_date, '%Y-%m-%d')
                   ORDER BY date ASC";
        $stmt4 = $this->conn->prepare($query4);
        $stmt4->execute();
        $trend = $stmt4->fetchAll(PDO::FETCH_ASSOC);

        return [
            'total' => $total,
            'by_status' => $by_status,
            'by_service' => $by_service,
            'trend' => $trend
        ];
    }

    /**
     * Get attendance by date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return PDOStatement
     */
    public function getByDateRange($start_date, $end_date) {
        $query = "SELECT a.*, s.name as service_name,
                  CONCAT(m.first_name, ' ', m.last_name) as member_name
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  LEFT JOIN members m ON a.member_id = m.id
                  WHERE a.attendance_date BETWEEN :start_date AND :end_date
                  ORDER BY a.attendance_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get attendance by date range and service
     *
     * @param string $start_date
     * @param string $end_date
     * @param int $service_id
     * @return PDOStatement
     */
    public function getByDateRangeAndService($start_date, $end_date, $service_id) {
        $query = "SELECT a.attendance_date, s.name as service_name,
                  COUNT(DISTINCT a.member_id) as total_present,
                  SUM(CASE WHEN m.gender = 'male' THEN 1 ELSE 0 END) as male_count,
                  SUM(CASE WHEN m.gender = 'female' THEN 1 ELSE 0 END) as female_count,
                  SUM(CASE WHEN m.age_group = 'children' THEN 1 ELSE 0 END) as children_count
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  LEFT JOIN members m ON a.member_id = m.id
                  WHERE a.attendance_date BETWEEN :start_date AND :end_date
                  AND a.service_id = :service_id
                  AND a.status = 'present'
                  GROUP BY a.attendance_date, s.name
                  ORDER BY a.attendance_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get average attendance for a month and service
     *
     * @param string $year_month Format: 'YYYY-MM'
     * @param int $service_id
     * @return int
     */
    public function getAverageByMonthAndService($year_month, $service_id) {
        $query = "SELECT AVG(attendance_count) as average_attendance
                  FROM (
                      SELECT COUNT(DISTINCT a.member_id) as attendance_count
                      FROM " . $this->table_name . " a
                      WHERE DATE_FORMAT(a.attendance_date, '%Y-%m') = :year_month
                      AND a.service_id = :service_id
                      AND a.status = 'present'
                      GROUP BY a.attendance_date
                  ) as attendance_counts";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':year_month', $year_month);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result && $result['average_attendance'] ? round($result['average_attendance']) : 0;
    }

    /**
     * Get service name by ID
     *
     * @param int $service_id
     * @return string
     */
    public function getServiceNameById($service_id) {
        $query = "SELECT name FROM services WHERE id = :service_id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['name'] : 'Unknown Service';
    }

    /**
     * Create multiple attendance records in bulk
     *
     * @param int $service_id
     * @param string $attendance_date
     * @param array $member_data Array of member IDs and their statuses
     * @return bool
     */
    public function bulkCreate($service_id, $attendance_date, $member_data) {
        // Start transaction
        $this->conn->beginTransaction();

        try {
            // First, check if there are any existing records for this service and date
            $query = "SELECT member_id FROM " . $this->table_name . "
                      WHERE service_id = :service_id AND attendance_date = :attendance_date";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':service_id', $service_id);
            $stmt->bindParam(':attendance_date', $attendance_date);
            $stmt->execute();

            $existing_records = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Prepare the insert query
            $insert_query = "INSERT INTO " . $this->table_name . "
                          (service_id, member_id, attendance_date, status, created_at, updated_at)
                          VALUES
                          (:service_id, :member_id, :attendance_date, :status, :created_at, :updated_at)";

            $insert_stmt = $this->conn->prepare($insert_query);

            // Prepare the update query
            $update_query = "UPDATE " . $this->table_name . "
                          SET status = :status, updated_at = :updated_at
                          WHERE service_id = :service_id AND attendance_date = :attendance_date AND member_id = :member_id";

            $update_stmt = $this->conn->prepare($update_query);

            // Current timestamp
            $timestamp = date('Y-m-d H:i:s');

            // Process each member
            foreach ($member_data as $member_id => $status) {
                // Sanitize inputs
                $member_id = htmlspecialchars(strip_tags($member_id));
                $status = htmlspecialchars(strip_tags($status));

                // Check if record already exists for this member
                if (in_array($member_id, $existing_records)) {
                    // Update existing record
                    $update_stmt->bindParam(':service_id', $service_id);
                    $update_stmt->bindParam(':member_id', $member_id);
                    $update_stmt->bindParam(':attendance_date', $attendance_date);
                    $update_stmt->bindParam(':status', $status);
                    $update_stmt->bindParam(':updated_at', $timestamp);
                    $update_stmt->execute();
                } else {
                    // Insert new record
                    $insert_stmt->bindParam(':service_id', $service_id);
                    $insert_stmt->bindParam(':member_id', $member_id);
                    $insert_stmt->bindParam(':attendance_date', $attendance_date);
                    $insert_stmt->bindParam(':status', $status);
                    $insert_stmt->bindParam(':created_at', $timestamp);
                    $insert_stmt->bindParam(':updated_at', $timestamp);
                    $insert_stmt->execute();
                }
            }

            // Commit transaction
            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->conn->rollBack();
            return false;
        }
    }

    /**
     * Get members with attendance status for a specific date and service
     *
     * @param string $date
     * @param int $service_id
     * @param string $status
     * @param int $limit Optional limit for pagination
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public function getMembersByDateAndStatus($date, $service_id, $status, $limit = null, $offset = null) {
        $query = "SELECT m.id, m.first_name, m.last_name, m.department, a.status
                  FROM members m
                  LEFT JOIN " . $this->table_name . " a ON m.id = a.member_id AND a.attendance_date = :date AND a.service_id = :service_id
                  WHERE a.status = :status
                  ORDER BY m.first_name, m.last_name";

        // Add limit and offset for pagination if provided
        if ($limit !== null) {
            $query .= " LIMIT :limit";
            if ($offset !== null) {
                $query .= " OFFSET :offset";
            }
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->bindParam(':status', $status);

        if ($limit !== null) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            if ($offset !== null) {
                $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            }
        }

        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get all members with their attendance status for a specific date and service
     *
     * @param string $date
     * @param int $service_id
     * @return array
     */
    public function getAllMembersWithAttendanceStatus($date, $service_id) {
        $query = "SELECT m.*, a.status
                  FROM members m
                  LEFT JOIN " . $this->table_name . " a ON m.id = a.member_id AND a.attendance_date = :date AND a.service_id = :service_id
                  ORDER BY m.first_name, m.last_name";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get members who were present on a specific date and service
     *
     * @param string $date
     * @param int $service_id
     * @param int $limit Optional limit for pagination
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public function getPresentMembers($date, $service_id, $limit = null, $offset = null) {
        return $this->getMembersByDateAndStatus($date, $service_id, 'present', $limit, $offset);
    }

    /**
     * Count members by status for a specific date and service
     *
     * @param string $date
     * @param int $service_id
     * @param string $status
     * @return int
     */
    public function countMembersByStatus($date, $service_id, $status) {
        $query = "SELECT COUNT(*) as count
                  FROM members m
                  LEFT JOIN " . $this->table_name . " a ON m.id = a.member_id AND a.attendance_date = :date AND a.service_id = :service_id
                  WHERE a.status = :status";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->bindParam(':status', $status);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['count'];
    }

    /**
     * Get members who were absent on a specific date and service
     *
     * @param string $date
     * @param int $service_id
     * @param int $limit Optional limit for pagination
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public function getAbsentMembers($date, $service_id, $limit = null, $offset = null) {
        return $this->getMembersByDateAndStatus($date, $service_id, 'absent', $limit, $offset);
    }

    /**
     * Get members who were late on a specific date and service
     *
     * @param string $date
     * @param int $service_id
     * @param int $limit Optional limit for pagination
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public function getLateMembers($date, $service_id, $limit = null, $offset = null) {
        return $this->getMembersByDateAndStatus($date, $service_id, 'late', $limit, $offset);
    }

    /**
     * Get attendance record for a specific member, date, and service
     *
     * @param int $member_id
     * @param string $date
     * @param int $service_id
     * @return array|false
     */
    public function getByMemberDateService($member_id, $date, $service_id) {
        $query = "SELECT * FROM " . $this->table_name . "
                  WHERE member_id = :member_id
                  AND attendance_date = :date
                  AND service_id = :service_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get recent attendance dates for a specific service
     *
     * @param int $service_id
     * @param int $limit
     * @return array
     */
    public function getRecentDatesForService($service_id, $limit = 5) {
        $query = "SELECT DISTINCT attendance_date
                  FROM " . $this->table_name . "
                  WHERE service_id = :service_id
                  ORDER BY attendance_date DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * Count members by gender and status for a specific date and service
     *
     * @param string $date
     * @param int $service_id
     * @param string $status
     * @param string $gender
     * @return int
     */
    public function countMembersByGenderAndStatus($date, $service_id, $status, $gender) {
        $query = "SELECT COUNT(*) as count
                  FROM members m
                  LEFT JOIN " . $this->table_name . " a ON m.id = a.member_id AND a.attendance_date = :date AND a.service_id = :service_id
                  WHERE a.status = :status AND m.gender = :gender";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':gender', $gender);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['count'];
    }

    /**
     * Get gender-based attendance statistics for a specific date and service
     *
     * @param string $date
     * @param int $service_id
     * @return array
     */
    public function getGenderStats($date, $service_id) {
        // Get male statistics
        $male_present = $this->countMembersByGenderAndStatus($date, $service_id, 'present', 'male');
        $male_absent = $this->countMembersByGenderAndStatus($date, $service_id, 'absent', 'male');
        $male_late = $this->countMembersByGenderAndStatus($date, $service_id, 'late', 'male');
        $male_total = $male_present + $male_absent + $male_late;

        // Get female statistics
        $female_present = $this->countMembersByGenderAndStatus($date, $service_id, 'present', 'female');
        $female_absent = $this->countMembersByGenderAndStatus($date, $service_id, 'absent', 'female');
        $female_late = $this->countMembersByGenderAndStatus($date, $service_id, 'late', 'female');
        $female_total = $female_present + $female_absent + $female_late;

        return [
            'male' => [
                'present' => $male_present,
                'absent' => $male_absent,
                'late' => $male_late,
                'total' => $male_total
            ],
            'female' => [
                'present' => $female_present,
                'absent' => $female_absent,
                'late' => $female_late,
                'total' => $female_total
            ]
        ];
    }

    /**
     * Get attendance data for a date range
     *
     * @param string $start_date
     * @param string $end_date
     * @param int|null $service_id
     * @return array
     */
    public function getAttendanceByDateRange($start_date, $end_date, $service_id = null) {
        $query = "SELECT a.*, s.name as service_name, s.day_of_week, s.time,
                  DATE_FORMAT(a.attendance_date, '%Y-%m-%d') as date,
                  COUNT(DISTINCT a.member_id) as total_members,
                  SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_count,
                  SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent_count,
                  SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as late_count
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  WHERE a.attendance_date BETWEEN :start_date AND :end_date";

        if ($service_id) {
            $query .= " AND a.service_id = :service_id";
        }

        $query .= " GROUP BY a.attendance_date, a.service_id
                   ORDER BY a.attendance_date DESC, s.time ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);

        if ($service_id) {
            $stmt->bindParam(':service_id', $service_id);
        }

        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get attendance summary for a date range
     *
     * @param string $start_date
     * @param string $end_date
     * @param int|null $service_id
     * @return array
     */
    public function getAttendanceSummary($start_date, $end_date, $service_id = null) {
        // Get attendance data for the date range
        $attendance_data = $this->getAttendanceByDateRange($start_date, $end_date, $service_id);

        // Calculate summary statistics
        $total_services = count($attendance_data);
        $total_present = 0;
        $total_absent = 0;
        $total_late = 0;
        $total_members = 0;
        $attendance_by_day = [];
        $attendance_by_service = [];

        foreach ($attendance_data as $record) {
            $total_present += $record['present_count'];
            $total_absent += $record['absent_count'];
            $total_late += $record['late_count'];
            $total_members += $record['total_members'];

            // Group by day
            $day = date('l', strtotime($record['date']));
            if (!isset($attendance_by_day[$day])) {
                $attendance_by_day[$day] = [
                    'present' => 0,
                    'absent' => 0,
                    'late' => 0,
                    'total' => 0
                ];
            }
            $attendance_by_day[$day]['present'] += $record['present_count'];
            $attendance_by_day[$day]['absent'] += $record['absent_count'];
            $attendance_by_day[$day]['late'] += $record['late_count'];
            $attendance_by_day[$day]['total'] += $record['present_count'] + $record['absent_count'] + $record['late_count'];

            // Group by service
            $service = $record['service_name'];
            if (!isset($attendance_by_service[$service])) {
                $attendance_by_service[$service] = [
                    'present' => 0,
                    'absent' => 0,
                    'late' => 0,
                    'total' => 0,
                    'service_id' => $record['service_id']
                ];
            }
            $attendance_by_service[$service]['present'] += $record['present_count'];
            $attendance_by_service[$service]['absent'] += $record['absent_count'];
            $attendance_by_service[$service]['late'] += $record['late_count'];
            $attendance_by_service[$service]['total'] += $record['present_count'] + $record['absent_count'] + $record['late_count'];
        }

        // Calculate averages and rates
        $total_attendance = $total_present + $total_absent + $total_late;
        $average_attendance = $total_services > 0 ? round($total_attendance / $total_services) : 0;
        $average_present = $total_services > 0 ? round($total_present / $total_services) : 0;
        $attendance_rate = $total_attendance > 0 ? round(($total_present + $total_late) / $total_attendance * 100) : 0;

        return [
            'total_services' => $total_services,
            'total_present' => $total_present,
            'total_absent' => $total_absent,
            'total_late' => $total_late,
            'total_attendance' => $total_attendance,
            'average_attendance' => $average_attendance,
            'average_present' => $average_present,
            'attendance_rate' => $attendance_rate,
            'attendance_by_day' => $attendance_by_day,
            'attendance_by_service' => $attendance_by_service,
            'records' => $attendance_data
        ];
    }

    /**
     * Get member by ID
     *
     * @param int $member_id
     * @return array|false
     */
    public function getMemberById($member_id) {
        $query = "SELECT * FROM members WHERE id = :member_id AND member_status = 'active'";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get attendance history for a specific member
     *
     * @param int $member_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function getMemberAttendanceHistory($member_id, $start_date, $end_date) {
        $query = "SELECT a.*, s.name as service_name, s.day_of_week, s.time
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  WHERE a.member_id = :member_id
                  AND a.attendance_date BETWEEN :start_date AND :end_date
                  ORDER BY a.attendance_date DESC, s.time ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get last attendance record for a member
     *
     * @param int $member_id
     * @return array|false
     */
    public function getLastAttendanceRecord($member_id) {
        $query = "SELECT a.*, s.name as service_name
                  FROM " . $this->table_name . " a
                  LEFT JOIN services s ON a.service_id = s.id
                  WHERE a.member_id = :member_id
                  AND a.status = 'present'
                  ORDER BY a.attendance_date DESC, s.time DESC
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get members who haven't attended for 3 consecutive Sundays
     *
     * @param int $limit Optional limit for pagination
     * @param int $offset Optional offset for pagination
     * @return array
     */
    public function getMembersAbsentFor3Sundays($limit = null, $offset = null) {
        // Get the last 3 Sunday dates
        $last_3_sundays = $this->getLastNSundays(3);

        if (count($last_3_sundays) < 3) {
            return []; // Not enough Sunday data
        }

        // Tenant filtering removed - single tenant application

        // Build the query to find members absent for all 3 Sundays
        $query = "SELECT DISTINCT m.id, m.first_name, m.last_name, m.phone_number, m.department, m.member_status,
                         MAX(last_present.attendance_date) as last_attendance_date,
                         last_present_service.name as last_service_name
                  FROM members m
                  LEFT JOIN (
                      SELECT a1.member_id
                      FROM " . $this->table_name . " a1
                      JOIN services s1 ON a1.service_id = s1.id
                      WHERE s1.day_of_week = 'sunday'
                      AND a1.attendance_date = :sunday1
                      AND a1.status IN ('present', 'late')

                  ) present_sunday1 ON m.id = present_sunday1.member_id
                  LEFT JOIN (
                      SELECT a2.member_id
                      FROM " . $this->table_name . " a2
                      JOIN services s2 ON a2.service_id = s2.id
                      WHERE s2.day_of_week = 'sunday'
                      AND a2.attendance_date = :sunday2
                      AND a2.status IN ('present', 'late')

                  ) present_sunday2 ON m.id = present_sunday2.member_id
                  LEFT JOIN (
                      SELECT a3.member_id
                      FROM " . $this->table_name . " a3
                      JOIN services s3 ON a3.service_id = s3.id
                      WHERE s3.day_of_week = 'sunday'
                      AND a3.attendance_date = :sunday3
                      AND a3.status IN ('present', 'late')

                  ) present_sunday3 ON m.id = present_sunday3.member_id
                  LEFT JOIN " . $this->table_name . " last_present ON m.id = last_present.member_id
                      AND last_present.status IN ('present', 'late')

                  LEFT JOIN services last_present_service ON last_present.service_id = last_present_service.id
                  WHERE m.member_status = 'active'

                  AND present_sunday1.member_id IS NULL
                  AND present_sunday2.member_id IS NULL
                  AND present_sunday3.member_id IS NULL
                  GROUP BY m.id, m.first_name, m.last_name, m.phone_number, m.department, m.member_status
                  ORDER BY last_attendance_date ASC, m.first_name ASC, m.last_name ASC";

        // Add limit and offset for pagination if provided
        if ($limit !== null) {
            $query .= " LIMIT :limit";
            if ($offset !== null) {
                $query .= " OFFSET :offset";
            }
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':sunday1', $last_3_sundays[0]);
        $stmt->bindParam(':sunday2', $last_3_sundays[1]);
        $stmt->bindParam(':sunday3', $last_3_sundays[2]);

        // Tenant parameter binding removed - single tenant application

        if ($limit !== null) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            if ($offset !== null) {
                $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            }
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Find member by identifier (ID, name, or phone)
     *
     * @param string $identifier
     * @return array|false
     */
    public function findMemberByIdentifier($identifier) {
        // Try to find by ID first
        if (is_numeric($identifier)) {
            $query = "SELECT * FROM members WHERE id = :identifier AND member_status = 'active'";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':identifier', $identifier);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result) return $result;
        }

        // Try to find by phone number
        $query = "SELECT * FROM members WHERE phone_number = :identifier AND member_status = 'active'";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':identifier', $identifier);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result) return $result;

        // Try to find by name (first name + last name)
        $name_parts = explode(' ', trim($identifier), 2);
        if (count($name_parts) >= 2) {
            $query = "SELECT * FROM members WHERE first_name LIKE :first_name AND last_name LIKE :last_name AND member_status = 'active'";
            $stmt = $this->conn->prepare($query);
            $first_name = '%' . $name_parts[0] . '%';
            $last_name = '%' . $name_parts[1] . '%';
            $stmt->bindParam(':first_name', $first_name);
            $stmt->bindParam(':last_name', $last_name);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result) return $result;
        }

        return false;
    }

    /**
     * Count members who haven't attended for 3 consecutive Sundays
     *
     * @return int
     */
    public function countMembersAbsentFor3Sundays() {
        // Get the last 3 Sunday dates
        $last_3_sundays = $this->getLastNSundays(3);

        if (count($last_3_sundays) < 3) {
            return 0; // Not enough Sunday data
        }

        // Tenant filtering removed - single tenant application

        $query = "SELECT COUNT(DISTINCT m.id) as count
                  FROM members m
                  LEFT JOIN (
                      SELECT a1.member_id
                      FROM " . $this->table_name . " a1
                      JOIN services s1 ON a1.service_id = s1.id
                      WHERE s1.day_of_week = 'sunday'
                      AND a1.attendance_date = :sunday1
                      AND a1.status IN ('present', 'late')

                  ) present_sunday1 ON m.id = present_sunday1.member_id
                  LEFT JOIN (
                      SELECT a2.member_id
                      FROM " . $this->table_name . " a2
                      JOIN services s2 ON a2.service_id = s2.id
                      WHERE s2.day_of_week = 'sunday'
                      AND a2.attendance_date = :sunday2
                      AND a2.status IN ('present', 'late')

                  ) present_sunday2 ON m.id = present_sunday2.member_id
                  LEFT JOIN (
                      SELECT a3.member_id
                      FROM " . $this->table_name . " a3
                      JOIN services s3 ON a3.service_id = s3.id
                      WHERE s3.day_of_week = 'sunday'
                      AND a3.attendance_date = :sunday3
                      AND a3.status IN ('present', 'late')

                  ) present_sunday3 ON m.id = present_sunday3.member_id
                  WHERE m.member_status = 'active'

                  AND present_sunday1.member_id IS NULL
                  AND present_sunday2.member_id IS NULL
                  AND present_sunday3.member_id IS NULL";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':sunday1', $last_3_sundays[0]);
        $stmt->bindParam(':sunday2', $last_3_sundays[1]);
        $stmt->bindParam(':sunday3', $last_3_sundays[2]);

        // Tenant parameter binding removed - single tenant application

        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['count'];
    }

    /**
     * Get the last N Sunday dates
     *
     * @param int $n Number of Sundays to get
     * @return array Array of Sunday dates in Y-m-d format
     */
    private function getLastNSundays($n = 3) {
        $sundays = [];
        $current_date = new DateTime();

        // If today is Sunday, start from today, otherwise find the most recent Sunday
        if ($current_date->format('w') == 0) {
            // Today is Sunday
            $last_sunday = clone $current_date;
        } else {
            // Find the most recent Sunday
            $days_since_sunday = $current_date->format('w');
            $last_sunday = clone $current_date;
            $last_sunday->sub(new DateInterval('P' . $days_since_sunday . 'D'));
        }

        // Get the last N Sundays
        for ($i = 0; $i < $n; $i++) {
            $sundays[] = $last_sunday->format('Y-m-d');
            $last_sunday->sub(new DateInterval('P7D')); // Go back 7 days
        }

        return $sundays;
    }
}
