<?php
// Include helpers for dynamic church name
require_once 'helpers/functions.php';

// Set page title and active page
$page_title = ($this->member->first_name ?? 'Member') . ' ' . ($this->member->last_name ?? '') . ' - Member Details';
$active_page = 'members';

// Start output buffering
ob_start();
?>

<div class="container mx-auto max-w-6xl px-4 py-6 overflow-hidden">
    <!-- Enhanced Header with Theme Colors -->
    <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl shadow-xl overflow-hidden mb-8">
        <div class="relative p-8">
            <!-- Background Pattern -->
            <div class="absolute inset-0 bg-black bg-opacity-10"></div>
            <div class="absolute top-0 right-0 w-64 h-64 transform translate-x-32 -translate-y-32">
                <div class="w-full h-full bg-white bg-opacity-10 rounded-full"></div>
            </div>
            <div class="absolute bottom-0 left-0 w-48 h-48 transform -translate-x-24 translate-y-24">
                <div class="w-full h-full bg-white bg-opacity-5 rounded-full"></div>
            </div>

            <div class="relative z-10 flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div class="flex items-center space-x-6 mb-6 lg:mb-0">
                    <!-- Enhanced Profile Picture -->
                    <div class="relative">
                        <?php if (!empty($this->member->profile_picture) && file_exists($this->member->profile_picture)): ?>
                            <img class="w-20 h-20 rounded-full object-cover border-4 border-white shadow-lg ring-4 ring-white ring-opacity-30"
                                 src="<?php echo BASE_URL . $this->member->profile_picture; ?>"
                                 alt="<?php echo htmlspecialchars($this->member->first_name . ' ' . $this->member->last_name); ?>">
                        <?php else: ?>
                            <div class="w-20 h-20 rounded-full bg-white bg-opacity-20 flex items-center justify-center border-4 border-white shadow-lg ring-4 ring-white ring-opacity-30">
                                <span class="text-white font-bold text-2xl">
                                    <?php echo strtoupper(substr($this->member->first_name ?? 'M', 0, 1) . substr($this->member->last_name ?? 'M', 0, 1)); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        <!-- Status Indicator -->
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-3 border-white <?php echo ($this->member->member_status ?? 'active') === 'active' ? 'bg-green-500' : 'bg-red-500'; ?> flex items-center justify-center">
                            <i class="fas fa-<?php echo ($this->member->member_status ?? 'active') === 'active' ? 'check' : 'times'; ?> text-white text-xs"></i>
                        </div>
                    </div>

                    <!-- Member Info -->
                    <div class="text-white">
                        <h1 class="text-3xl font-bold mb-2">
                            <?php echo htmlspecialchars($this->member->first_name . ' ' . $this->member->last_name); ?>
                        </h1>
                        <div class="flex flex-wrap items-center gap-4 text-white text-opacity-90 mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-user-tag mr-2"></i>
                                <span><?php echo htmlspecialchars($this->member->role_display_name ?? 'Member'); ?></span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-building mr-2"></i>
                                <span><?php echo htmlspecialchars($this->member->department_display_name ?? 'No Department'); ?></span>
                            </div>
                            <?php
                            $total_family = count($this->parents_data ?? []) + count($this->children_data ?? []) + count($this->siblings_data ?? []);
                            if ($total_family > 0):
                            ?>
                            <div class="flex items-center">
                                <i class="fas fa-heart mr-2"></i>
                                <span><?php echo $total_family; ?> family member<?php echo $total_family > 1 ? 's' : ''; ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                        <p class="text-white text-opacity-80 text-sm">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Member since: <?php echo date('F j, Y', strtotime($this->member->created_at)); ?>
                        </p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    <a href="<?php echo BASE_URL; ?>members"
                       class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-3 px-6 rounded-xl flex items-center transition-all duration-200 backdrop-blur-sm border border-white border-opacity-20 hover:border-opacity-40">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Members
                    </a>
                    <a href="<?php echo BASE_URL; ?>members/edit/<?php echo $this->member->id; ?>"
                       class="bg-white text-primary hover:bg-gray-50 py-3 px-6 rounded-xl flex items-center transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <i class="fas fa-edit mr-2"></i> Edit Member
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Member Information -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                <div class="bg-gradient-to-r from-primary/10 to-secondary/10 p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800 flex items-center">
                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        Member Information
                    </h2>
                </div>
                <div class="p-6 overflow-hidden">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Contact Information</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900 break-words">
                                        <?php echo $this->member->phone_number ?? 'Not provided'; ?>
                                    </div>
                                </div>

                                <?php if (!empty($this->member->email)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900 break-words">
                                        <?php echo $this->member->email; ?>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($this->member->location)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900 break-words">
                                        <?php echo $this->member->location; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Personal Details</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-birthday-cake"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <?php
                                        if (!empty($this->member->date_of_birth) &&
                                            $this->member->date_of_birth !== '0000-00-00' &&
                                            $this->member->date_of_birth !== '1970-01-01') {
                                            try {
                                                $dob = new DateTime($this->member->date_of_birth);
                                                // Check if the parsed date is valid (not -0001 or other invalid dates)
                                                if ($dob->format('Y') > 1900 && $dob->format('Y') <= date('Y')) {
                                                    echo $dob->format('F j, Y');

                                                    // Calculate age
                                                    $today = new DateTime();
                                                    $age = $today->diff($dob)->y;
                                                    echo " ($age years old)";
                                                } else {
                                                    echo 'Not provided';
                                                }
                                            } catch (Exception $e) {
                                                echo 'Not provided';
                                            }
                                        } else {
                                            echo 'Not provided';
                                        }
                                        ?>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-venus-mars"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <?php echo ucfirst($this->member->gender ?? 'Not specified'); ?>
                                    </div>
                                </div>

                                <?php if (!empty($this->member->marital_status)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <?php echo ucfirst(str_replace('_', ' ', $this->member->marital_status)); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Emergency Contact Information -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Emergency Contact Information</h4>
                        <div class="space-y-3">
                            <?php if (!empty($this->member->emergency_contact_name)): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="ml-3 text-sm text-gray-900">
                                    <span class="font-medium">Emergency Contact:</span> <?php echo htmlspecialchars($this->member->emergency_contact_name); ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($this->member->emergency_contact_phone)): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                    <i class="fas fa-phone-alt"></i>
                                </div>
                                <div class="ml-3 text-sm text-gray-900 break-words">
                                    <span class="font-medium">Emergency Phone:</span>
                                    <a href="tel:<?php echo htmlspecialchars($this->member->emergency_contact_phone); ?>"
                                       class="text-blue-600 hover:text-blue-800">
                                        <?php echo htmlspecialchars($this->member->emergency_contact_phone); ?>
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if (empty($this->member->emergency_contact_name) && empty($this->member->emergency_contact_phone)): ?>
                            <div class="text-sm text-gray-500 italic">No emergency contact information provided</div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Additional Personal Information -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Additional Information</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                            <div class="space-y-3">
                                <?php if (!empty($this->member->occupation)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900 break-words">
                                        <span class="font-medium">Occupation:</span> <?php echo htmlspecialchars($this->member->occupation); ?>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($this->member->school)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900 break-words">
                                        <span class="font-medium">School:</span> <?php echo htmlspecialchars($this->member->school); ?>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($this->member->address)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-home"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900 break-words">
                                        <span class="font-medium">Address:</span> <?php echo htmlspecialchars($this->member->address); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Church Information -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Church Information</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <span class="font-medium">Department:</span> <?php echo htmlspecialchars($this->member->department_display_name ?? 'Not assigned'); ?>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-user-tag"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <span class="font-medium">Role:</span> <?php echo htmlspecialchars($this->member->role_display_name ?? 'Member'); ?>
                                    </div>
                                </div>

                                <?php if (!empty($this->member->baptism_status)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-water"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <span class="font-medium">Baptism Status:</span> <?php echo ucfirst(str_replace('_', ' ', $this->member->baptism_status)); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-calendar-plus"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <span class="font-medium">Member since:</span>
                                        <?php
                                        if (!empty($this->member->membership_date)) {
                                            $membership = new DateTime($this->member->membership_date);
                                            echo $membership->format('F j, Y');
                                        } elseif (!empty($this->member->created_at)) {
                                            $created = new DateTime($this->member->created_at);
                                            echo $created->format('F j, Y');
                                        } else {
                                            echo 'Unknown';
                                        }
                                        ?>
                                    </div>
                                </div>

                                <?php if (!empty($this->member->registration_type)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <span class="font-medium">Registration Type:</span> <?php echo ucfirst(str_replace('_', ' ', $this->member->registration_type)); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Quick Actions -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                <div class="bg-gradient-to-r from-secondary/20 to-primary/10 p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-800 flex items-center">
                        <div class="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-bolt text-primary text-sm"></i>
                        </div>
                        Quick Actions
                    </h3>
                </div>
                <div class="p-4 space-y-3">
                    <?php if (!empty($this->member->email)): ?>
                    <button onclick="openEmailComposer('<?php echo htmlspecialchars($this->member->email); ?>', '<?php echo htmlspecialchars($this->member->first_name . ' ' . $this->member->last_name); ?>')"
                       class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                        <i class="fas fa-envelope mr-2"></i> Send Email
                    </button>
                    <?php endif; ?>

                    <?php if (!empty($this->member->phone_number)): ?>
                    <a href="tel:<?php echo htmlspecialchars($this->member->phone_number); ?>"
                       class="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                        <i class="fas fa-phone mr-2"></i> Call Member
                    </a>
                    <?php endif; ?>

                    <a href="<?php echo BASE_URL; ?>attendance/member?id=<?php echo $this->member->id; ?>"
                       class="w-full bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                        <i class="fas fa-calendar-check mr-2"></i> View Attendance
                    </a>
                </div>
            </div>

            <!-- Member Statistics -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                <div class="bg-gradient-to-r from-primary/10 to-secondary/20 p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-800 flex items-center">
                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-chart-bar text-white text-sm"></i>
                        </div>
                        Member Statistics
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-600 flex items-center">
                                <i class="fas fa-hashtag text-gray-400 mr-2"></i>
                                Member ID
                            </span>
                            <span class="text-sm font-bold text-gray-900 bg-blue-100 px-2 py-1 rounded">#<?php echo $this->member->id; ?></span>
                        </div>

                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-600 flex items-center">
                                <i class="fas fa-circle text-gray-400 mr-2"></i>
                                Status
                            </span>
                            <span class="px-3 py-1 text-xs font-semibold rounded-full <?php echo $status_bg . ' ' . $status_class; ?>">
                                <?php echo ucfirst($this->member->member_status ?? 'Active'); ?>
                            </span>
                        </div>

                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-600 flex items-center">
                                <i class="fas fa-building text-gray-400 mr-2"></i>
                                Department
                            </span>
                            <span class="text-sm font-bold text-gray-900"><?php echo ucfirst(str_replace('_', ' ', $this->member->department ?? 'None')); ?></span>
                        </div>

                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-600 flex items-center">
                                <i class="fas fa-user-tag text-gray-400 mr-2"></i>
                                Role
                            </span>
                            <span class="text-sm font-bold text-gray-900"><?php echo ucfirst($this->member->role ?? 'Member'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <!-- Family Information -->
    <div class="mt-8">
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-shadow duration-300">
            <div class="bg-gradient-to-r from-secondary/15 to-primary/15 p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800 flex items-center">
                    <div class="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-users text-primary text-sm"></i>
                    </div>
                    Family Information
                    <?php
                    $total_relationships = count($this->parents_data ?? []) + count($this->children_data ?? []) + count($this->siblings_data ?? []);
                    if ($total_relationships > 0):
                    ?>
                    <span class="ml-3 px-2 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full">
                        <?php echo $total_relationships; ?> relationship<?php echo $total_relationships > 1 ? 's' : ''; ?>
                    </span>
                    <?php endif; ?>
                </h2>
            </div>

            <?php if (!empty($this->parents_data) || !empty($this->children_data) || !empty($this->siblings_data)): ?>
            <div class="p-6">
                <?php if (!empty($this->parents_data)): ?>
                <div class="mb-6">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-friends text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800">Parents/Guardians</h3>
                        <span class="ml-3 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                            <?php echo count($this->parents_data); ?> parent<?php echo count($this->parents_data) > 1 ? 's' : ''; ?>
                        </span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php foreach ($this->parents_data as $parent): ?>
                        <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-xl p-4 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mr-4 shadow-md">
                                        <span class="text-white text-sm font-bold">
                                            <?php echo strtoupper(substr($parent['first_name'], 0, 1) . substr($parent['last_name'], 0, 1)); ?>
                                        </span>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-bold text-gray-900">
                                            <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                                        </p>
                                        <p class="text-xs text-gray-600">
                                            <i class="fas fa-envelope mr-1"></i>
                                            <?php echo htmlspecialchars($parent['email'] ?? 'No email'); ?>
                                        </p>
                                        <?php if (!empty($parent['phone_number'])): ?>
                                        <p class="text-xs text-gray-600">
                                            <i class="fas fa-phone mr-1"></i>
                                            <?php echo htmlspecialchars($parent['phone_number']); ?>
                                        </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="<?php echo BASE_URL; ?>members/view/<?php echo $parent['id']; ?>"
                                       class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors flex items-center">
                                        <i class="fas fa-eye mr-1"></i> View
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($this->children_data)): ?>
                <div class="mt-6">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-child text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800">Children</h3>
                        <span class="ml-3 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                            <?php echo count($this->children_data); ?> child<?php echo count($this->children_data) > 1 ? 'ren' : ''; ?>
                        </span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php foreach ($this->children_data as $child): ?>
                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-4 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4 shadow-md">
                                        <span class="text-white text-sm font-bold">
                                            <?php echo strtoupper(substr($child['first_name'], 0, 1) . substr($child['last_name'], 0, 1)); ?>
                                        </span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-bold text-gray-900">
                                            <?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?>
                                        </p>
                                        <div class="flex items-center text-xs text-gray-600 mt-1">
                                            <i class="fas fa-birthday-cake mr-1 text-pink-500"></i>
                                            <?php
                                            if (!empty($child['age']) && is_numeric($child['age'])) {
                                                echo $child['age'] . ' years old';
                                            } elseif (!empty($child['date_of_birth']) &&
                                                     $child['date_of_birth'] !== '0000-00-00' &&
                                                     $child['date_of_birth'] !== '1970-01-01') {
                                                try {
                                                    $dob = new DateTime($child['date_of_birth']);
                                                    if ($dob->format('Y') > 1900 && $dob->format('Y') <= date('Y')) {
                                                        $today = new DateTime();
                                                        $age = $today->diff($dob)->y;
                                                        echo $age . ' years old';
                                                    } else {
                                                        echo 'Age not specified';
                                                    }
                                                } catch (Exception $e) {
                                                    echo 'Age not specified';
                                                }
                                            } else {
                                                echo 'Age not specified';
                                            }
                                            ?>
                                        </div>
                                        <?php if (!empty($child['gender'])): ?>
                                        <div class="flex items-center text-xs text-gray-600 mt-1">
                                            <i class="fas fa-<?php echo $child['gender'] === 'male' ? 'mars' : 'venus'; ?> mr-1 text-<?php echo $child['gender'] === 'male' ? 'blue' : 'pink'; ?>-500"></i>
                                            <?php echo ucfirst($child['gender']); ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (!empty($child['school'])): ?>
                                        <div class="flex items-center text-xs text-gray-600 mt-1">
                                            <i class="fas fa-graduation-cap mr-1 text-green-500"></i>
                                            <?php echo htmlspecialchars($child['school']); ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (!empty($child['department'])): ?>
                                        <div class="flex items-center text-xs text-gray-600 mt-1">
                                            <i class="fas fa-users mr-1 text-blue-500"></i>
                                            <?php echo ucfirst(str_replace('_', ' ', $child['department'])); ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="flex flex-col space-y-2">
                                    <a href="<?php echo BASE_URL; ?>members/view/<?php echo $child['id']; ?>"
                                       class="inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-xs font-medium rounded-lg transition-colors">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>attendance/member?id=<?php echo $child['id']; ?>"
                                       class="inline-flex items-center px-3 py-1.5 bg-purple-500 hover:bg-purple-600 text-white text-xs font-medium rounded-lg transition-colors">
                                        <i class="fas fa-calendar-check mr-1"></i>Attendance
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($this->siblings_data)): ?>
                <div class="mt-6">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-friends text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800">Siblings</h3>
                        <span class="ml-3 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                            <?php echo count($this->siblings_data); ?> sibling<?php echo count($this->siblings_data) > 1 ? 's' : ''; ?>
                        </span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php foreach ($this->siblings_data as $sibling): ?>
                        <div class="bg-gradient-to-r from-green-50 to-teal-50 border border-green-200 rounded-xl p-4 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mr-4 shadow-md">
                                        <span class="text-white text-sm font-bold">
                                            <?php echo strtoupper(substr($sibling['first_name'], 0, 1) . substr($sibling['last_name'], 0, 1)); ?>
                                        </span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-bold text-gray-900">
                                            <?php echo htmlspecialchars($sibling['first_name'] . ' ' . $sibling['last_name']); ?>
                                        </p>
                                        <div class="flex items-center text-xs text-gray-600 mt-1">
                                            <i class="fas fa-birthday-cake mr-1 text-pink-500"></i>
                                            <?php
                                            if (!empty($sibling['age']) && is_numeric($sibling['age'])) {
                                                echo $sibling['age'] . ' years old';
                                            } elseif (!empty($sibling['date_of_birth']) &&
                                                     $sibling['date_of_birth'] !== '0000-00-00' &&
                                                     $sibling['date_of_birth'] !== '1970-01-01') {
                                                try {
                                                    $dob = new DateTime($sibling['date_of_birth']);
                                                    if ($dob->format('Y') > 1900 && $dob->format('Y') <= date('Y')) {
                                                        $today = new DateTime();
                                                        $age = $today->diff($dob)->y;
                                                        echo $age . ' years old';
                                                    } else {
                                                        echo 'Age not specified';
                                                    }
                                                } catch (Exception $e) {
                                                    echo 'Age not specified';
                                                }
                                            } else {
                                                echo 'Age not specified';
                                            }
                                            ?>
                                        </div>
                                        <?php if (!empty($sibling['department'])): ?>
                                        <div class="flex items-center text-xs text-gray-600 mt-1">
                                            <i class="fas fa-users mr-1 text-green-500"></i>
                                            <?php echo ucfirst(str_replace('_', ' ', $sibling['department'])); ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="flex flex-col space-y-2">
                                    <a href="<?php echo BASE_URL; ?>members/view/<?php echo $sibling['id']; ?>"
                                       class="inline-flex items-center px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white text-xs font-medium rounded-lg transition-colors">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>attendance/member?id=<?php echo $sibling['id']; ?>"
                                       class="inline-flex items-center px-3 py-1.5 bg-teal-500 hover:bg-teal-600 text-white text-xs font-medium rounded-lg transition-colors">
                                        <i class="fas fa-calendar-check mr-1"></i>Attendance
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

            <?php else: ?>
                <!-- No Family Relationships -->
                <div class="p-6">
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-users text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Family Relationships</h3>
                        <p class="text-gray-500 mb-4">
                            <?php
                            // Calculate member age to show appropriate message
                            $member_age = 0;
                            if (!empty($this->member->date_of_birth)) {
                                $birth_date = new DateTime($this->member->date_of_birth);
                                $today = new DateTime();
                                $member_age = $birth_date->diff($today)->y;
                            }

                            if ($member_age <= 18): ?>
                                This child member doesn't have any parent relationships set up yet.
                            <?php else: ?>
                                This member doesn't have any family relationships (children, parents, or siblings) set up yet.
                            <?php endif; ?>
                        </p>

                        <div class="flex flex-col sm:flex-row gap-3 justify-center">
                            <?php if ($member_age <= 18): ?>
                                <a href="<?php echo url('members/' . $this->member->id . '/edit?assign_parent=1'); ?>"
                                   class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-colors">
                                    <i class="fas fa-user-plus mr-2"></i>
                                    Assign Parent
                                </a>
                            <?php else: ?>
                                <a href="<?php echo url('members/' . $this->member->id . '/edit'); ?>"
                                   class="inline-flex items-center px-4 py-2 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-lg transition-colors">
                                    <i class="fas fa-child mr-2"></i>
                                    Add Child
                                </a>
                            <?php endif; ?>

                            <!-- Auto-detection disabled - all relationships are manual only -->
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Email Composer Modal -->
<div id="emailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-2xl bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-4 border-b">
                <h3 class="text-lg font-bold text-gray-900 flex items-center">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-envelope text-white text-sm"></i>
                    </div>
                    Compose Email
                </h3>
                <button onclick="closeEmailComposer()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Email Form -->
            <form id="emailForm" class="mt-4">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">To:</label>
                        <input type="email" id="emailTo" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-700">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject:</label>
                        <input type="text" id="emailSubject"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                               placeholder="Enter email subject">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Message:</label>
                        <textarea id="emailMessage" rows="8"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                  placeholder="Type your message here..."></textarea>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t">
                    <button type="button" onclick="closeEmailComposer()"
                            class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg transition-colors">
                        Cancel
                    </button>
                    <button type="button" onclick="sendEmail()"
                            class="px-6 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
                        <i class="fas fa-paper-plane mr-2"></i>Send Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Email Options Modal -->
<div id="emailOptionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-32 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-2xl bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-4 border-b">
                <h3 class="text-lg font-bold text-gray-900 flex items-center">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-envelope text-white text-sm"></i>
                    </div>
                    Choose Email Method
                </h3>
                <button onclick="closeEmailOptions()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Email Options -->
            <div class="mt-4 space-y-3">
                <button onclick="openGmail()"
                        class="w-full p-4 bg-red-50 hover:bg-red-100 border border-red-200 rounded-lg flex items-center transition-colors">
                    <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mr-4">
                        <i class="fab fa-google text-white"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-medium text-gray-900">Open in Gmail</div>
                        <div class="text-sm text-gray-600">Opens Gmail in your browser</div>
                    </div>
                </button>

                <button onclick="openDefaultClient()"
                        class="w-full p-4 bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg flex items-center transition-colors">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-desktop text-white"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-medium text-gray-900">Open in Default Email Client</div>
                        <div class="text-sm text-gray-600">Opens your system's default email app</div>
                    </div>
                </button>

                <button onclick="copyEmailContent()"
                        class="w-full p-4 bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg flex items-center transition-colors">
                    <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-copy text-white"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-medium text-gray-900">Copy Email Content</div>
                        <div class="text-sm text-gray-600">Copy to clipboard and paste anywhere</div>
                    </div>
                </button>
            </div>

            <!-- Cancel Button -->
            <div class="mt-6 pt-4 border-t">
                <button onclick="closeEmailOptions()"
                        class="w-full px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg transition-colors">
                    Cancel
                </button>
            </div>
            </form>
        </div>
    </div>
</div>

<script>
function openEmailComposer(email, name) {
    // Get church name dynamically
    const churchName = '<?php echo htmlspecialchars(getChurchName()); ?>';
    document.getElementById('emailTo').value = email;
    document.getElementById('emailSubject').value = `Message from ${churchName} regarding ${name}`;
    document.getElementById('emailMessage').value = `Dear ${name.split(' ')[0]},\n\nI hope this message finds you well.\n\n\n\nBest regards,\n${churchName}`;
    document.getElementById('emailModal').classList.remove('hidden');
}

function closeEmailComposer() {
    document.getElementById('emailModal').classList.add('hidden');
    // Clear form
    document.getElementById('emailSubject').value = '';
    document.getElementById('emailMessage').value = '';
}

function sendEmail() {
    const to = document.getElementById('emailTo').value;
    const subject = document.getElementById('emailSubject').value;
    const message = document.getElementById('emailMessage').value;

    if (!subject.trim()) {
        alert('Please enter a subject for the email.');
        return;
    }

    if (!message.trim()) {
        alert('Please enter a message for the email.');
        return;
    }

    // Show options for sending email
    showEmailOptions(to, subject, message);
}

// Global variables to store email data
let currentEmailData = {};

function showEmailOptions(to, subject, message) {
    // Store email data globally
    currentEmailData = { to, subject, message };

    // Close the composer modal
    closeEmailComposer();

    // Show options modal
    document.getElementById('emailOptionsModal').classList.remove('hidden');
}

function closeEmailOptions() {
    document.getElementById('emailOptionsModal').classList.add('hidden');
}

function openGmail() {
    const { to, subject, message } = currentEmailData;

    // Create Gmail compose URL
    const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(to)}&su=${encodeURIComponent(subject)}&body=${encodeURIComponent(message)}`;

    // Open Gmail in new tab
    window.open(gmailUrl, '_blank');

    closeEmailOptions();

    // Show success message
    setTimeout(() => {
        alert('Gmail opened in a new tab with your composed message!');
    }, 500);
}

function openDefaultClient() {
    const { to, subject, message } = currentEmailData;

    // Create mailto link
    const mailtoLink = `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(message)}`;

    // Try multiple methods to open the email client
    try {
        // Method 1: Create a temporary link and click it
        const tempLink = document.createElement('a');
        tempLink.href = mailtoLink;
        tempLink.style.display = 'none';
        document.body.appendChild(tempLink);
        tempLink.click();
        document.body.removeChild(tempLink);

        closeEmailOptions();

        // Show success message
        setTimeout(() => {
            alert('Default email client opened with your composed message!');
        }, 500);

    } catch (error) {
        // Fallback method
        try {
            window.open(mailtoLink, '_self');
            closeEmailOptions();
            setTimeout(() => {
                alert('Default email client opened with your composed message!');
            }, 500);
        } catch (fallbackError) {
            // If all else fails, show the mailto link
            closeEmailOptions();
            alert('Please copy this link and paste it in your browser address bar:\n\n' + mailtoLink);
        }
    }
}

function copyEmailContent() {
    const { to, subject, message } = currentEmailData;

    // Create formatted email content
    const emailContent = `To: ${to}\nSubject: ${subject}\n\nMessage:\n${message}`;

    // Copy to clipboard
    navigator.clipboard.writeText(emailContent).then(() => {
        closeEmailOptions();
        alert('Email content copied to clipboard! You can now paste it into any email client.');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = emailContent;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        closeEmailOptions();
        alert('Email content copied to clipboard! You can now paste it into any email client.');
    });
}

// Close modal when clicking outside
document.getElementById('emailModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEmailComposer();
    }
});

document.getElementById('emailOptionsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEmailOptions();
    }
});

// Auto-detection has been disabled - all family relationships are now manual only

</script>

<?php
$content = ob_get_clean();
require_once 'views/layouts/main.php';
?>
