<?php
/**
 * Equipment Category Model
 * Handles database operations for equipment categories
 */

class EquipmentCategory {
    private $conn;
    private $table_name = "equipment_categories";

    // Object properties
    public $id;
    public $name;
    public $value;
    public $description;
    public $is_active;
    public $created_by;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create equipment category table if it doesn't exist
     */
    public function createTable() {
        $query = "CREATE TABLE IF NOT EXISTS " . $this->table_name . " (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            value VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_by INT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_value (value),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        try {
            $this->conn->exec($query);
            return true;
        } catch (PDOException $e) {
            error_log("Error creating equipment_categories table: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a new equipment category
     */
    public function create() {
        // Ensure table exists
        $this->createTable();

        $query = "INSERT INTO " . $this->table_name . " 
                  (name, value, description, is_active, created_by, created_at, updated_at) 
                  VALUES (:name, :value, :description, :is_active, :created_by, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->value = htmlspecialchars(strip_tags($this->value));
        $this->description = htmlspecialchars(strip_tags($this->description ?? ''));
        $this->is_active = $this->is_active ?? 1;
        $this->created_by = $this->created_by ?? null;

        // Bind parameters
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':value', $this->value);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':is_active', $this->is_active);
        $stmt->bindParam(':created_by', $this->created_by);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Get all active equipment categories
     */
    public function getAll() {
        // Ensure table exists
        $this->createTable();

        $query = "SELECT id, name, value, description, is_active, created_by, created_at, updated_at 
                  FROM " . $this->table_name . " 
                  WHERE is_active = 1 
                  ORDER BY name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }

    /**
     * Get category by value
     */
    public function getByValue($value) {
        // Ensure table exists
        $this->createTable();

        $query = "SELECT id, name, value, description, is_active, created_by, created_at, updated_at 
                  FROM " . $this->table_name . " 
                  WHERE value = :value 
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':value', $value);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_OBJ);

        if ($row) {
            $this->id = $row->id;
            $this->name = $row->name;
            $this->value = $row->value;
            $this->description = $row->description;
            $this->is_active = $row->is_active;
            $this->created_by = $row->created_by;
            $this->created_at = $row->created_at;
            $this->updated_at = $row->updated_at;
            return true;
        }

        return false;
    }

    /**
     * Check if category value already exists
     */
    public function valueExists($value) {
        // Ensure table exists
        $this->createTable();

        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE value = :value";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':value', $value);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'] > 0;
    }

    /**
     * Validate category data
     */
    public function validate() {
        $errors = [];

        if (empty($this->name)) {
            $errors[] = 'Category name is required';
        }

        if (empty($this->value)) {
            $errors[] = 'Category value is required';
        }

        if (strlen($this->name) > 255) {
            $errors[] = 'Category name must be less than 255 characters';
        }

        if (strlen($this->value) > 255) {
            $errors[] = 'Category value must be less than 255 characters';
        }

        // Check for valid value format (alphanumeric, underscore, hyphen)
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $this->value)) {
            $errors[] = 'Category value can only contain letters, numbers, underscores, and hyphens';
        }

        return $errors;
    }

    /**
     * Get default equipment categories
     */
    public static function getDefaultCategories() {
        return [
            ['name' => 'Audio Equipment', 'value' => 'audio'],
            ['name' => 'Video Equipment', 'value' => 'video'],
            ['name' => 'Lighting Equipment', 'value' => 'lighting'],
            ['name' => 'Musical Instruments', 'value' => 'instruments'],
            ['name' => 'Furniture', 'value' => 'furniture'],
            ['name' => 'Office Equipment', 'value' => 'office'],
            ['name' => 'Kitchen Equipment', 'value' => 'kitchen'],
            ['name' => 'Cleaning Supplies', 'value' => 'cleaning'],
            ['name' => 'Safety Equipment', 'value' => 'safety'],
            ['name' => 'Other', 'value' => 'other']
        ];
    }

    /**
     * Initialize default categories
     */
    public function initializeDefaults() {
        $defaults = self::getDefaultCategories();
        $created = 0;

        foreach ($defaults as $category) {
            if (!$this->valueExists($category['value'])) {
                $this->name = $category['name'];
                $this->value = $category['value'];
                $this->description = '';
                $this->is_active = 1;
                $this->created_by = null;

                if ($this->create()) {
                    $created++;
                }
            }
        }

        return $created;
    }
}
