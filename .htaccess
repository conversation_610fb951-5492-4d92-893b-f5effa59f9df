# ICGC Church Management System - .htaccess Configuration
# Enhances security and performance

# Enable URL rewriting
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Set application base
    RewriteBase /icgc/

    # Redirect to HTTPS if not already using it
    # Uncomment when SS<PERSON> is configured
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Prevent direct access to sensitive directories
    RewriteRule ^(config|database|logs|vendor|cache)(/|$) - [F,L]

    # Handle 404 errors - Route to main index.php (NOT public/index.php)
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [QSA,L]
</IfModule>

# Set security headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header set X-Frame-Options "SAMEORIGIN"

    # XSS protection
    Header set X-XSS-Protection "1; mode=block"

    # Prevent MIME type sniffing
    Header set X-Content-Type-Options "nosniff"

    # Referrer policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"

    # Content Security Policy (CSP) - Basic protection
    # Customize as needed for your external resources
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://ajax.googleapis.com https://cdnjs.cloudflare.com 'unsafe-inline'; style-src 'self' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data: https://chart.googleapis.com https://api.qrserver.com https://qr-server.com; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com data:; connect-src 'self';"
</IfModule>

# PHP settings
<IfModule mod_php8.c>
    # PHP error reporting
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log

    # File upload settings
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_file_uploads 20

    # Memory and execution time
    php_value memory_limit 256M
    php_value max_execution_time 300
    php_value max_input_time 300

    # Session settings
    php_value session.gc_maxlifetime 3600
    php_value session.cookie_lifetime 0
    php_value session.cookie_httponly 1
    php_value session.use_strict_mode 1
</IfModule>

# Enable compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive on

    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"

    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"

    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"

    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Security file restrictions
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.md">
    Order allow,deny
    Deny from all
</Files>

# Prevent direct access to sensitive files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# PHP settings
php_flag display_errors off
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300
