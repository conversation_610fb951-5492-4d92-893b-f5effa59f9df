<div class="container mx-auto px-4 max-w-7xl">


    <!-- QR Code Generation Section -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8 border border-gray-100">
        <div class="bg-gradient-to-r from-purple-600 to-indigo-600 px-6 py-5 text-white">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-white bg-opacity-20 mr-4 shadow-md">
                    <i class="fas fa-qrcode text-xl"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold mb-1">Generate QR Code for Attendance</h1>
                    <p class="text-sm opacity-90">Create a QR code that members can scan to mark their attendance</p>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <?php if (empty($services)): ?>
                <!-- No Services Warning -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400 text-2xl"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-yellow-800">No Services Available</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>You need to add at least one service before you can generate QR codes for attendance.</p>
                            </div>
                            <div class="mt-4">
                                <a href="<?php echo BASE_URL; ?>services/add"
                                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-yellow-800 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-all duration-300">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add Your First Service
                                </a>
                                <a href="<?php echo BASE_URL; ?>services"
                                   class="ml-3 inline-flex items-center px-4 py-2 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-white hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-all duration-300">
                                    <i class="fas fa-list mr-2"></i>
                                    View All Services
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-100">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">Generate New QR Code</h3>
                    <p class="text-sm text-gray-600 mb-4">Create a QR code for a service that members can scan to mark their attendance.</p>
                    
                    <form action="<?php echo BASE_URL; ?>attendance/qr-generate" method="POST" class="space-y-4">
                        <!-- Service Selection with Management -->
                        <div>
                            <label for="service_id" class="block text-sm font-medium text-gray-700 mb-1">Service</label>
                            <div class="flex space-x-2 items-center">
                                <select id="service_id" name="service_id" class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                                    <option value="">Select a service</option>
                                    <?php if (!empty($services)): ?>
                                        <?php foreach ($services as $service): ?>
                                            <option value="<?php echo htmlspecialchars($service['id']); ?>" data-service-id="<?php echo htmlspecialchars($service['id']); ?>">
                                                <?php echo htmlspecialchars($service['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <option value="" disabled>No services available</option>
                                    <?php endif; ?>
                                </select>
                                <button type="button" onclick="showAddServiceModal()" class="flex-shrink-0 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors duration-200" title="Add New Service" id="add-service-btn">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button type="button" onclick="editSelectedService()" class="flex-shrink-0 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" title="Edit Selected Service" id="edit-service-btn" disabled>
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" onclick="deleteSelectedService()" class="flex-shrink-0 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" title="Delete Selected Service" id="delete-service-btn" disabled>
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <div class="mt-1 text-xs text-gray-500">
                                <span class="inline-flex items-center">
                                    <i class="fas fa-plus text-green-600 mr-1"></i> Add new service
                                    <span class="mx-2">•</span>
                                    <i class="fas fa-edit text-blue-600 mr-1"></i> Edit selected service
                                    <span class="mx-2">•</span>
                                    <i class="fas fa-trash text-red-600 mr-1"></i> Delete selected service
                                </span>
                            </div>
                            <?php if (empty($services)): ?>
                                <p class="mt-2 text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded-md p-2">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    No services found. Click the <i class="fas fa-plus text-green-600 mx-1"></i> button above to add your first service.
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Date Selection -->
                        <div>
                            <label for="attendance_date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                            <input type="date" id="attendance_date" name="attendance_date" 
                                   value="<?php echo date('Y-m-d'); ?>" 
                                   class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                        </div>
                        
                        <!-- Duration Selection -->
                        <div>
                            <label for="duration" class="block text-sm font-medium text-gray-700 mb-1">QR Code Validity (minutes)</label>
                            <select id="duration" name="duration" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                                <option value="30">30 minutes</option>
                                <option value="60">1 hour</option>
                                <option value="120">2 hours</option>
                                <option value="180">3 hours</option>
                                <option value="240" selected>4 hours (Recommended)</option>
                                <option value="300">5 hours</option>
                                <option value="360">6 hours</option>
                            </select>
                        </div>
                        
                        <div class="pt-2">
                            <button type="submit"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-all duration-300
                                           <?php echo !empty($services) ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500' : 'bg-gray-400 cursor-not-allowed'; ?>"
                                    <?php echo empty($services) ? 'disabled' : ''; ?>>
                                <i class="fas fa-qrcode mr-2"></i>
                                <?php echo !empty($services) ? 'Generate QR Code' : 'Add Services First'; ?>
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-6 border border-blue-100">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">How QR Attendance Works</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">1</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">Generate a QR code for a specific service and date.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">2</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">Display the QR code on a screen or print it out.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">3</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">Members scan the QR code with their smartphones.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">4</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">They search for their name and mark themselves present.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                <span class="text-xs font-bold">5</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-gray-700">Attendance is automatically recorded in the system.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 text-center">
                        <i class="fas fa-info-circle text-blue-500 mr-1"></i>
                        <span class="text-sm text-blue-700">QR codes are valid for the duration you select.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>







    <!-- Recent QR Sessions -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8 border border-gray-100">
        <div class="flex items-center justify-between p-5 border-b border-gray-100 bg-gradient-to-r from-indigo-100 to-purple-100 relative">
            <!-- Decorative elements -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-indigo-200 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>

            <div class="flex items-center relative z-10">
                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white flex items-center justify-center mr-3 shadow-md">
                    <i class="fas fa-qrcode text-lg"></i>
                </div>
                <h2 class="text-lg font-bold text-gray-800">Recent QR Sessions</h2>
            </div>

            <div class="flex items-center space-x-3 relative z-10">
                <a href="<?php echo BASE_URL; ?>attendance/qr-analytics" class="bg-white bg-opacity-80 hover:bg-opacity-100 text-indigo-700 px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 shadow-sm">
                    <i class="fas fa-chart-line mr-1"></i> Analytics
                </a>
                <a href="<?php echo BASE_URL; ?>attendance/qr-export" class="bg-white bg-opacity-80 hover:bg-opacity-100 text-indigo-700 px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 shadow-sm">
                    <i class="fas fa-download mr-1"></i> Export
                </a>
                <a href="<?php echo BASE_URL; ?>attendance/qr-sessions-archive" class="bg-white bg-opacity-80 hover:bg-opacity-100 text-indigo-700 px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 shadow-sm">
                    <i class="fas fa-archive mr-1"></i> Archive
                </a>
                <span class="text-indigo-700 text-sm">Last 10 sessions</span>
            </div>
        </div>

        <div class="p-6">
            <?php if (empty($recent_sessions)): ?>
                <div class="text-center py-8 text-gray-500">
                    <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-4 shadow-md">
                        <i class="fas fa-qrcode text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-base font-medium">No recent QR sessions found</p>
                    <p class="text-sm text-gray-400 mt-1">Generate a QR code to get started</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($recent_sessions as $session): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo htmlspecialchars($session['service_name']); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo format_date($session['attendance_date']); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo time_ago($session['created_at']); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php
                                        $expires_at = strtotime($session['expires_at']);
                                        $now = time();
                                        if ($expires_at > $now) {
                                            $time_left = $expires_at - $now;
                                            $minutes_left = floor($time_left / 60);
                                            echo 'In ' . $minutes_left . ' min';
                                        } else {
                                            echo 'Expired';
                                        }
                                        ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($session['status'] === 'active' && strtotime($session['expires_at']) > time()): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                        <?php elseif ($session['status'] === 'closed'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Closed</span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Expired</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <span class="font-medium"><?php echo $session['attendance_count'] ?? 0; ?></span>
                                        <span class="text-gray-500">members</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div class="flex space-x-2">
                                            <?php if ($session['status'] === 'active' && strtotime($session['expires_at']) > time()): ?>
                                                <a href="<?php echo BASE_URL; ?>attendance/qr-display?token=<?php echo $session['token']; ?>"
                                                   class="text-blue-600 hover:text-blue-900" title="View QR Code">
                                                    <i class="fas fa-qrcode"></i>
                                                </a>
                                                <button onclick="extendSession(<?php echo $session['id']; ?>)"
                                                        class="text-green-600 hover:text-green-900" title="Extend Session">
                                                    <i class="fas fa-clock"></i>
                                                </button>
                                                <button onclick="closeSession(<?php echo $session['id']; ?>)"
                                                        class="text-red-600 hover:text-red-900" title="Close Session">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>
                                            <a href="<?php echo BASE_URL; ?>attendance/qr-session-details?session_id=<?php echo $session['id']; ?>"
                                               class="text-blue-600 hover:text-blue-900" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button onclick="viewSessionStats(<?php echo $session['id']; ?>)"
                                                    class="text-purple-600 hover:text-purple-900" title="View Statistics">
                                                <i class="fas fa-chart-bar"></i>
                                            </button>
                                            <a href="<?php echo BASE_URL; ?>attendance/qr-session-export?session_id=<?php echo $session['id']; ?>"
                                               class="text-gray-600 hover:text-gray-900" title="Export Data">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button onclick="deleteSession(<?php echo $session['id']; ?>)"
                                                    class="text-red-600 hover:text-red-900" title="Delete Session">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Session Management Modal -->
<div id="sessionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" id="modalTitle">Session Management</h3>
            <div id="modalContent">
                <!-- Dynamic content will be loaded here -->
            </div>
            <div class="flex justify-end mt-4">
                <button onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Service Modal -->
<div id="addServiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Add New Service</h3>
                <button type="button" onclick="hideAddServiceModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addServiceForm" class="space-y-4">
                <div>
                    <label for="service_name" class="block text-sm font-medium text-gray-700 mb-1">Service Name</label>
                    <input type="text" id="service_name" name="service_name" required
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                           placeholder="e.g., Sunday Morning Service">
                </div>
                <div>
                    <label for="service_description" class="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                    <textarea id="service_description" name="service_description" rows="2"
                              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                              placeholder="Brief description of the service"></textarea>
                </div>
                <div>
                    <label for="service_day" class="block text-sm font-medium text-gray-700 mb-1">Day of Week</label>
                    <select id="service_day" name="service_day" required
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Select day</option>
                        <option value="sunday">Sunday</option>
                        <option value="monday">Monday</option>
                        <option value="tuesday">Tuesday</option>
                        <option value="wednesday">Wednesday</option>
                        <option value="thursday">Thursday</option>
                        <option value="friday">Friday</option>
                        <option value="saturday">Saturday</option>
                    </select>
                </div>
                <div>
                    <label for="service_time" class="block text-sm font-medium text-gray-700 mb-1">Time</label>
                    <input type="time" id="service_time" name="service_time" required
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideAddServiceModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-green-600 to-green-700 rounded-md hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500">
                        <i class="fas fa-plus mr-1"></i> Add Service
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Service Modal -->
<div id="editServiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Edit Service</h3>
                <button type="button" onclick="hideEditServiceModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editServiceForm" class="space-y-4">
                <input type="hidden" id="edit_service_id" name="service_id">
                <div>
                    <label for="edit_service_name" class="block text-sm font-medium text-gray-700 mb-1">Service Name</label>
                    <input type="text" id="edit_service_name" name="service_name" required
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                           placeholder="e.g., Sunday Morning Service">
                </div>
                <div>
                    <label for="edit_service_description" class="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                    <textarea id="edit_service_description" name="service_description" rows="2"
                              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                              placeholder="Brief description of the service"></textarea>
                </div>
                <div>
                    <label for="edit_service_day" class="block text-sm font-medium text-gray-700 mb-1">Day of Week</label>
                    <select id="edit_service_day" name="service_day" required
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Select day</option>
                        <option value="sunday">Sunday</option>
                        <option value="monday">Monday</option>
                        <option value="tuesday">Tuesday</option>
                        <option value="wednesday">Wednesday</option>
                        <option value="thursday">Thursday</option>
                        <option value="friday">Friday</option>
                        <option value="saturday">Saturday</option>
                    </select>
                </div>
                <div>
                    <label for="edit_service_time" class="block text-sm font-medium text-gray-700 mb-1">Time</label>
                    <input type="time" id="edit_service_time" name="service_time" required
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideEditServiceModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-md hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-save mr-1"></i> Update Service
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Service Modal -->
<div id="deleteServiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Service</h3>
            <p class="text-sm text-gray-500 mb-4">
                Are you sure you want to delete "<span id="deleteServiceName"></span>"?
            </p>
            <div id="deleteServiceOptions" class="mb-4">
                <!-- Options will be populated by JavaScript -->
            </div>
            <div class="flex justify-center space-x-3">
                <button type="button" onclick="hideDeleteServiceModal()"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Cancel
                </button>
                <button type="button" onclick="confirmDeleteService()" id="confirmDeleteBtn"
                        class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-600 to-red-700 rounded-md hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-red-500">
                    <i class="fas fa-trash mr-1"></i> Delete
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Archive Confirmation Modal -->
<div id="archiveServiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
                <i class="fas fa-archive text-yellow-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Archive Service</h3>
            <p class="text-sm text-gray-500 mb-4">
                Archive "<span id="archiveServiceName"></span>"? This will hide it from the dropdown but preserve all historical data.
            </p>
            <div class="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                <p class="text-xs text-blue-700">
                    <i class="fas fa-info-circle mr-1"></i>
                    Archived services can be restored later if needed. This is the recommended approach for services with existing attendance data.
                </p>
            </div>
            <div class="flex justify-center space-x-3">
                <button type="button" onclick="hideArchiveServiceModal()"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Cancel
                </button>
                <button type="button" onclick="confirmArchiveService()"
                        class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-yellow-600 to-orange-600 rounded-md hover:from-yellow-700 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                    <i class="fas fa-archive mr-1"></i> Archive
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete QR Session Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 text-center mb-4">Delete QR Session</h3>
            <div class="text-center mb-6">
                <p class="text-sm text-gray-600 mb-2">Are you sure you want to delete this QR session?</p>
                <p class="text-sm text-red-600 font-medium">This will permanently remove the session and all associated data. This action cannot be undone.</p>
            </div>
            <div class="flex justify-center space-x-3">
                <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors">
                    Cancel
                </button>
                <button onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                    <i class="fas fa-trash mr-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Service Management Functions
let selectedServiceId = null;

// Enable/disable edit and delete buttons based on service selection
document.addEventListener('DOMContentLoaded', function() {
    const serviceSelect = document.getElementById('service_id');
    const editBtn = document.getElementById('edit-service-btn');
    const deleteBtn = document.getElementById('delete-service-btn');

    if (serviceSelect && editBtn && deleteBtn) {
        // Initialize button states
        editBtn.classList.add('opacity-50', 'cursor-not-allowed');
        deleteBtn.classList.add('opacity-50', 'cursor-not-allowed');

        serviceSelect.addEventListener('change', function() {
            const selectedValue = this.value;

            if (selectedValue) {
                // Enable both edit and delete buttons
                editBtn.disabled = false;
                editBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                selectedServiceId = selectedValue;
            } else {
                // Disable both edit and delete buttons
                editBtn.disabled = true;
                editBtn.classList.add('opacity-50', 'cursor-not-allowed');
                deleteBtn.disabled = true;
                deleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
                selectedServiceId = null;
            }
        });
    }
});

// Show Add Service Modal
function showAddServiceModal() {
    const modal = document.getElementById('addServiceModal');
    if (modal) {
        modal.classList.remove('hidden');
        const nameField = document.getElementById('service_name');
        if (nameField) {
            nameField.focus();
        }
    }
}

// Hide Add Service Modal
function hideAddServiceModal() {
    const modal = document.getElementById('addServiceModal');
    const form = document.getElementById('addServiceForm');
    if (modal) {
        modal.classList.add('hidden');
    }
    if (form) {
        form.reset();
    }
}

// Show Edit Service Modal
function editSelectedService() {
    if (!selectedServiceId) {
        alert('Please select a service to edit.');
        return;
    }

    // Get service data from the dropdown option
    const serviceSelect = document.getElementById('service_id');
    const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];

    if (!selectedOption || !selectedOption.value) {
        alert('Please select a valid service to edit.');
        return;
    }

    // Fetch service details from server
    fetchServiceDetails(selectedServiceId);
}

// Hide Edit Service Modal
function hideEditServiceModal() {
    const modal = document.getElementById('editServiceModal');
    const form = document.getElementById('editServiceForm');
    if (modal) {
        modal.classList.add('hidden');
    }
    if (form) {
        form.reset();
    }
}

// Fetch service details for editing
function fetchServiceDetails(serviceId) {
    const formData = new FormData();
    formData.append('action', 'get_service');
    formData.append('service_id', serviceId);

    fetch('<?php echo BASE_URL; ?>attendance/service-manage', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Populate the edit form
            document.getElementById('edit_service_id').value = data.service.id;
            document.getElementById('edit_service_name').value = data.service.name;
            document.getElementById('edit_service_description').value = data.service.description || '';
            document.getElementById('edit_service_day').value = data.service.day_of_week;
            document.getElementById('edit_service_time').value = data.service.time;

            // Show the modal
            document.getElementById('editServiceModal').classList.remove('hidden');
            document.getElementById('edit_service_name').focus();
        } else {
            alert('Error loading service details: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while loading service details.');
    });
}

// Show Delete Service Modal
function deleteSelectedService() {
    if (!selectedServiceId) {
        alert('Please select a service to delete.');
        return;
    }

    const serviceSelect = document.getElementById('service_id');
    const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
    const serviceName = selectedOption.text;

    const nameSpan = document.getElementById('deleteServiceName');
    const modal = document.getElementById('deleteServiceModal');
    const optionsDiv = document.getElementById('deleteServiceOptions');
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    if (nameSpan && modal) {
        nameSpan.textContent = serviceName;

        // Reset to default delete option
        optionsDiv.innerHTML = `
            <div class="text-sm text-gray-600 bg-gray-50 border border-gray-200 rounded-md p-3">
                <i class="fas fa-info-circle mr-1"></i>
                Checking if service can be safely deleted...
            </div>
        `;
        confirmBtn.innerHTML = '<i class="fas fa-trash mr-1"></i> Delete';
        confirmBtn.onclick = confirmDeleteService;

        modal.classList.remove('hidden');
    }
}

// Hide Delete Service Modal
function hideDeleteServiceModal() {
    document.getElementById('deleteServiceModal').classList.add('hidden');
}

// Show Archive Service Modal
function showArchiveServiceModal() {
    const serviceSelect = document.getElementById('service_id');
    const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
    const serviceName = selectedOption.text;

    const nameSpan = document.getElementById('archiveServiceName');
    const modal = document.getElementById('archiveServiceModal');

    if (nameSpan && modal) {
        nameSpan.textContent = serviceName;
        modal.classList.remove('hidden');
    }
}

// Hide Archive Service Modal
function hideArchiveServiceModal() {
    document.getElementById('archiveServiceModal').classList.add('hidden');
}

// Add Service Form Submission
document.addEventListener('DOMContentLoaded', function() {
    const addServiceForm = document.getElementById('addServiceForm');
    if (addServiceForm) {
        addServiceForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'add_service');
            formData.append('name', document.getElementById('service_name').value);
            formData.append('description', document.getElementById('service_description').value);
            formData.append('day_of_week', document.getElementById('service_day').value);
            formData.append('time', document.getElementById('service_time').value);

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Adding...';
            submitBtn.disabled = true;

            fetch('<?php echo BASE_URL; ?>attendance/service-manage', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Add new service to dropdown
                    const serviceSelect = document.getElementById('service_id');
                    const newOption = document.createElement('option');
                    newOption.value = data.service_id;
                    newOption.textContent = data.service_name;
                    newOption.setAttribute('data-service-id', data.service_id);
                    serviceSelect.appendChild(newOption);

                    // Select the new service
                    serviceSelect.value = data.service_id;
                    selectedServiceId = data.service_id;
                    document.getElementById('delete-service-btn').disabled = false;
                    document.getElementById('delete-service-btn').classList.remove('opacity-50', 'cursor-not-allowed');

                    hideAddServiceModal();
                    showSuccessMessage('Service added successfully!');
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding the service.');
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }

    // Edit Service Form Submission
    const editServiceForm = document.getElementById('editServiceForm');
    if (editServiceForm) {
        editServiceForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'edit_service');
            formData.append('service_id', document.getElementById('edit_service_id').value);
            formData.append('name', document.getElementById('edit_service_name').value);
            formData.append('description', document.getElementById('edit_service_description').value);
            formData.append('day_of_week', document.getElementById('edit_service_day').value);
            formData.append('time', document.getElementById('edit_service_time').value);

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Updating...';
            submitBtn.disabled = true;

            fetch('<?php echo BASE_URL; ?>attendance/service-manage', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update service in dropdown
                    const serviceSelect = document.getElementById('service_id');
                    const optionToUpdate = serviceSelect.querySelector(`option[value="${data.service_id}"]`);
                    if (optionToUpdate) {
                        optionToUpdate.textContent = data.service_name;
                    }

                    hideEditServiceModal();
                    showSuccessMessage('Service updated successfully!');
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the service.');
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
});

// Confirm Delete Service
function confirmDeleteService() {
    if (!selectedServiceId) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'delete_service');
    formData.append('service_id', selectedServiceId);

    fetch('<?php echo BASE_URL; ?>attendance/service-manage', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove service from dropdown
            removeServiceFromDropdown();
            hideDeleteServiceModal();
            showSuccessMessage(data.message);
        } else {
            if (data.canArchive) {
                // Show archive option instead
                showArchiveOption(data.error, data.usageDetails);
            } else {
                alert('Error: ' + data.error);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the service.');
    });
}

// Show archive option when deletion is not possible
function showArchiveOption(errorMessage, usageDetails) {
    const optionsDiv = document.getElementById('deleteServiceOptions');
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    optionsDiv.innerHTML = `
        <div class="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-3 mb-3">
            <i class="fas fa-exclamation-triangle mr-1"></i>
            ${errorMessage}
        </div>
        <div class="text-sm text-blue-600 bg-blue-50 border border-blue-200 rounded-md p-3">
            <i class="fas fa-info-circle mr-1"></i>
            <strong>Recommended:</strong> Archive this service instead. This will hide it from the dropdown while preserving all historical data.
        </div>
    `;

    confirmBtn.innerHTML = '<i class="fas fa-archive mr-1"></i> Archive Instead';
    confirmBtn.className = confirmBtn.className.replace('from-red-600 to-red-700', 'from-yellow-600 to-orange-600');
    confirmBtn.onclick = function() {
        hideDeleteServiceModal();
        showArchiveServiceModal();
    };
}

// Confirm Archive Service
function confirmArchiveService() {
    if (!selectedServiceId) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'archive_service');
    formData.append('service_id', selectedServiceId);

    fetch('<?php echo BASE_URL; ?>attendance/service-manage', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove service from dropdown
            removeServiceFromDropdown();
            hideArchiveServiceModal();
            showSuccessMessage(data.message);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while archiving the service.');
    });
}

// Helper function to remove service from dropdown
function removeServiceFromDropdown() {
    const serviceSelect = document.getElementById('service_id');
    const optionToRemove = serviceSelect.querySelector(`option[value="${selectedServiceId}"]`);
    if (optionToRemove) {
        optionToRemove.remove();
    }

    // Reset selection
    serviceSelect.value = '';
    selectedServiceId = null;
    const deleteBtn = document.getElementById('delete-service-btn');
    deleteBtn.disabled = true;
    deleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
}

// Session Management Functions
function extendSession(sessionId) {
    const minutes = prompt('How many minutes to extend the session?', '30');
    if (minutes && !isNaN(minutes)) {
        fetch('<?php echo BASE_URL; ?>attendance/qr-session-manage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=extend&session_id=${sessionId}&additional_minutes=${minutes}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while extending the session');
        });
    }
}

function closeSession(sessionId) {
    if (confirm('Are you sure you want to close this QR session? This action cannot be undone.')) {
        fetch('<?php echo BASE_URL; ?>attendance/qr-session-manage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=close&session_id=${sessionId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while closing the session');
        });
    }
}

let sessionToDelete = null;

function deleteSession(sessionId) {
    sessionToDelete = sessionId;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    sessionToDelete = null;
}

function confirmDelete() {
    if (sessionToDelete) {
        fetch('<?php echo BASE_URL; ?>attendance/qr-session-manage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=delete&session_id=${sessionToDelete}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showSuccessMessage(data.message);
                // Close modal
                closeDeleteModal();
                // Reload page after a short delay
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showErrorMessage('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage('An error occurred while deleting the session');
        });
    }
}

function viewSessionStats(sessionId) {
    fetch('<?php echo BASE_URL; ?>attendance/qr-session-manage', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get_stats&session_id=${sessionId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSessionStatsModal(data.stats, data.attendance_records);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while loading session statistics');
    });
}

function showSessionStatsModal(stats, attendanceRecords) {
    const modalTitle = document.getElementById('modalTitle');
    const modalContent = document.getElementById('modalContent');

    modalTitle.textContent = 'Session Statistics';

    let content = `
        <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-blue-50 p-3 rounded">
                    <div class="text-2xl font-bold text-blue-600">${stats.total_attendance || 0}</div>
                    <div class="text-sm text-gray-600">Total Attendance</div>
                </div>
                <div class="bg-green-50 p-3 rounded">
                    <div class="text-2xl font-bold text-green-600">${stats.present_count || 0}</div>
                    <div class="text-sm text-gray-600">Present</div>
                </div>
            </div>

            <div class="mt-4">
                <h4 class="font-medium text-gray-900 mb-2">Recent Attendance</h4>
                <div class="max-h-40 overflow-y-auto">
    `;

    if (attendanceRecords && attendanceRecords.length > 0) {
        attendanceRecords.slice(0, 10).forEach(record => {
            content += `
                <div class="flex justify-between items-center py-1 border-b border-gray-100">
                    <span class="text-sm">${record.first_name} ${record.last_name}</span>
                    <span class="text-xs text-gray-500">${new Date(record.created_at).toLocaleTimeString()}</span>
                </div>
            `;
        });
    } else {
        content += '<div class="text-sm text-gray-500">No attendance records found</div>';
    }

    content += `
                </div>
            </div>
        </div>
    `;

    modalContent.innerHTML = content;
    document.getElementById('sessionModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('sessionModal').classList.add('hidden');
}

// Success and error message functions
function showSuccessMessage(message) {
    // Create a temporary success notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function showErrorMessage(message) {
    // Create a temporary error notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-exclamation-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Close modal when clicking outside
document.getElementById('sessionModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Close delete modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

// Close service modals when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    const addServiceModal = document.getElementById('addServiceModal');
    const editServiceModal = document.getElementById('editServiceModal');
    const deleteServiceModal = document.getElementById('deleteServiceModal');
    const archiveServiceModal = document.getElementById('archiveServiceModal');

    if (addServiceModal) {
        addServiceModal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideAddServiceModal();
            }
        });
    }

    if (editServiceModal) {
        editServiceModal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideEditServiceModal();
            }
        });
    }

    if (deleteServiceModal) {
        deleteServiceModal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideDeleteServiceModal();
            }
        });
    }

    if (archiveServiceModal) {
        archiveServiceModal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideArchiveServiceModal();
            }
        });
    }
});

// Close delete modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const deleteModal = document.getElementById('deleteModal');
        const addServiceModal = document.getElementById('addServiceModal');
        const deleteServiceModal = document.getElementById('deleteServiceModal');

        if (deleteModal && !deleteModal.classList.contains('hidden')) {
            closeDeleteModal();
        }
        if (addServiceModal && !addServiceModal.classList.contains('hidden')) {
            hideAddServiceModal();
        }

        const editServiceModal = document.getElementById('editServiceModal');
        if (editServiceModal && !editServiceModal.classList.contains('hidden')) {
            hideEditServiceModal();
        }

        if (deleteServiceModal && !deleteServiceModal.classList.contains('hidden')) {
            hideDeleteServiceModal();
        }

        const archiveServiceModal = document.getElementById('archiveServiceModal');
        if (archiveServiceModal && !archiveServiceModal.classList.contains('hidden')) {
            hideArchiveServiceModal();
        }
    }
});

// Add custom scrollbar styles
document.head.insertAdjacentHTML('beforeend', `
    <style>
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }
    </style>
`);

// Form validation for QR generation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[action*="qr-generate"]');
    if (form) {
        form.addEventListener('submit', function(e) {
            const serviceSelect = document.getElementById('service_id');
            const dateInput = document.getElementById('attendance_date');
            const durationSelect = document.getElementById('duration');

            // Check if service is selected
            if (!serviceSelect.value) {
                e.preventDefault();
                alert('Please select a service before generating QR code.');
                serviceSelect.focus();
                return false;
            }

            // Check if date is selected
            if (!dateInput.value) {
                e.preventDefault();
                alert('Please select a date for the attendance.');
                dateInput.focus();
                return false;
            }

            // Check if duration is selected
            if (!durationSelect.value) {
                e.preventDefault();
                alert('Please select QR code validity duration.');
                durationSelect.focus();
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Generating QR Code...';
            }
        });
    }
});
</script>
