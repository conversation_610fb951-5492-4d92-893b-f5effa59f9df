<?php
/**
 * Global Error Handler
 * 
 * Centralized error handling for the finance system.
 * Provides consistent error logging, user notifications, and debugging.
 * 
 * @package Handlers
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

// Load environment configuration if not already loaded
if (!class_exists('EnvironmentConfig')) {
    require_once dirname(__DIR__) . '/config/environment.php';
    EnvironmentConfig::load();
}

// Load exceptions
if (file_exists(dirname(__DIR__) . '/exceptions/BaseException.php')) {
    require_once dirname(__DIR__) . '/exceptions/BaseException.php';
}

class ErrorHandler
{
    /**
     * @var bool Whether error handler is initialized
     */
    private static $initialized = false;

    /**
     * @var string Log file path
     */
    private static $logFile;

    /**
     * Initialize error handler
     * 
     * @return void
     */
    public static function initialize(): void
    {
        if (self::$initialized) {
            return;
        }

        // Load configuration
        EnvironmentConfig::load();

        // Set log file with safety check
        $logFile = EnvironmentConfig::get('logging.file', 'logs/error.log');
        self::$logFile = is_string($logFile) ? $logFile : 'logs/error.log';

        // Set error handlers
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleShutdown']);

        // Set error reporting based on environment
        if (EnvironmentConfig::get('app.debug', false)) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT);
            ini_set('display_errors', 0);
        }

        self::$initialized = true;
    }

    /**
     * Handle PHP errors
     * 
     * @param int $severity Error severity
     * @param string $message Error message
     * @param string $file File where error occurred
     * @param int $line Line number where error occurred
     * @return bool Whether error was handled
     */
    public static function handleError(int $severity, string $message, string $file, int $line): bool
    {
        // Don't handle errors that are suppressed with @
        if (!(error_reporting() & $severity)) {
            return false;
        }

        // Convert error to exception
        $exception = new ErrorException($message, 0, $severity, $file, $line);
        
        // Handle the exception
        self::handleException($exception);

        return true;
    }

    /**
     * Handle uncaught exceptions
     * 
     * @param Throwable $exception Exception to handle
     * @return void
     */
    public static function handleException(Throwable $exception): void
    {
        try {
            // Log the exception
            self::logException($exception);

            // Send appropriate response based on request type
            if (self::isAjaxRequest()) {
                self::sendJsonErrorResponse($exception);
            } else {
                self::sendHtmlErrorResponse($exception);
            }

        } catch (Throwable $e) {
            // Fallback error handling if our error handler fails
            error_log("Error handler failed: " . $e->getMessage());
            self::sendFallbackErrorResponse();
        }
    }

    /**
     * Handle fatal errors during shutdown
     * 
     * @return void
     */
    public static function handleShutdown(): void
    {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $exception = new ErrorException(
                $error['message'],
                0,
                $error['type'],
                $error['file'],
                $error['line']
            );
            
            self::handleException($exception);
        }
    }

    /**
     * Log exception to file
     * 
     * @param Throwable $exception Exception to log
     * @return void
     */
    private static function logException(Throwable $exception): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'user_id' => $_SESSION['user_id'] ?? null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ];

        // Add context if it's a BaseException
        if ($exception instanceof BaseException) {
            $logData['context'] = $exception->getContext();
            $logData['error_code'] = $exception->getErrorCode();
        }

        // Create log entry
        $logEntry = json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

        // Ensure log directory exists with safety check
        $logFile = is_string(self::$logFile) ? self::$logFile : 'logs/error.log';
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // Write to log file
        file_put_contents(self::$logFile, $logEntry . PHP_EOL, FILE_APPEND | LOCK_EX);

        // Also log to PHP error log
        error_log("EXCEPTION: " . $exception->getMessage() . " in " . $exception->getFile() . ":" . $exception->getLine());
    }

    /**
     * Send JSON error response for AJAX requests
     * 
     * @param Throwable $exception Exception
     * @return void
     */
    private static function sendJsonErrorResponse(Throwable $exception): void
    {
        // Determine status code
        $statusCode = 500;
        if ($exception instanceof SecurityException) {
            $statusCode = 403;
        } elseif ($exception instanceof ValidationException) {
            $statusCode = 400;
        }

        // Prepare response data
        $response = [
            'success' => false,
            'message' => self::getUserFriendlyMessage($exception),
            'error_code' => self::getErrorCode($exception)
        ];

        // Add debug information if in debug mode
        if (EnvironmentConfig::get('app.debug', false)) {
            $response['debug'] = [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }

        // Add validation errors if applicable
        if ($exception instanceof ValidationException) {
            $response['validation_errors'] = $exception->getValidationErrors();
        }

        // Send response
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    /**
     * Send HTML error response for regular requests
     * 
     * @param Throwable $exception Exception
     * @return void
     */
    private static function sendHtmlErrorResponse(Throwable $exception): void
    {
        // Set flash message
        $message = self::getUserFriendlyMessage($exception);
        set_flash_message($message, 'danger');

        // Determine redirect location
        $redirectUrl = 'dashboard';
        if ($exception instanceof SecurityException) {
            $redirectUrl = 'login';
        } elseif (isset($_SERVER['HTTP_REFERER'])) {
            $redirectUrl = $_SERVER['HTTP_REFERER'];
        }

        // Redirect
        redirect($redirectUrl);
    }

    /**
     * Send fallback error response when error handler fails
     * 
     * @return void
     */
    private static function sendFallbackErrorResponse(): void
    {
        if (self::isAjaxRequest()) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'An unexpected error occurred. Please try again.'
            ]);
        } else {
            http_response_code(500);
            echo '<h1>Error</h1><p>An unexpected error occurred. Please try again.</p>';
        }
        exit;
    }

    /**
     * Get user-friendly error message
     * 
     * @param Throwable $exception Exception
     * @return string User-friendly message
     */
    private static function getUserFriendlyMessage(Throwable $exception): string
    {
        if ($exception instanceof BaseException) {
            return $exception->getUserMessage();
        }

        // Default messages for common exception types
        if ($exception instanceof PDOException) {
            return 'A database error occurred. Please try again.';
        }

        if ($exception instanceof ErrorException) {
            return 'A system error occurred. Please try again.';
        }

        // Generic message for unknown exceptions
        return 'An unexpected error occurred. Please try again.';
    }

    /**
     * Get error code for exception
     * 
     * @param Throwable $exception Exception
     * @return string Error code
     */
    private static function getErrorCode(Throwable $exception): string
    {
        if ($exception instanceof BaseException) {
            return $exception->getErrorCode();
        }

        return get_class($exception);
    }

    /**
     * Check if current request is AJAX
     * 
     * @return bool Whether request is AJAX
     */
    private static function isAjaxRequest(): bool
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Get formatted error page for display
     * 
     * @param Throwable $exception Exception
     * @return string HTML error page
     */
    public static function getErrorPage(Throwable $exception): string
    {
        $title = 'Application Error';
        $message = self::getUserFriendlyMessage($exception);
        $debug = '';

        if (EnvironmentConfig::get('app.debug', false)) {
            $debug = '<div class="debug-info">' .
                     '<h3>Debug Information</h3>' .
                     '<p><strong>Exception:</strong> ' . get_class($exception) . '</p>' .
                     '<p><strong>Message:</strong> ' . htmlspecialchars($exception->getMessage()) . '</p>' .
                     '<p><strong>File:</strong> ' . $exception->getFile() . '</p>' .
                     '<p><strong>Line:</strong> ' . $exception->getLine() . '</p>' .
                     '<pre>' . htmlspecialchars($exception->getTraceAsString()) . '</pre>' .
                     '</div>';
        }

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <title>{$title}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .error-container { max-width: 600px; margin: 0 auto; }
                .error-message { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; }
                .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; }
                pre { background: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto; }
            </style>
        </head>
        <body>
            <div class='error-container'>
                <h1>{$title}</h1>
                <div class='error-message'>{$message}</div>
                {$debug}
                <p><a href='javascript:history.back()'>Go Back</a> | <a href='/'>Home</a></p>
            </div>
        </body>
        </html>";
    }
}
