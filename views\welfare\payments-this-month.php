<?php
$page_title = "Members Who Paid This Month";
$active_page = "welfare";
?>

<div class="max-w-7xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Members Who Paid This Month</h1>
            <p class="text-gray-600 mt-1"><?php echo date('F Y'); ?> • <?php echo $pagination['total_count']; ?> total payments</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>welfare"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Welfare
            </a>
            <a href="<?php echo BASE_URL; ?>welfare/add"
               class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                <i class="fas fa-coins mr-2"></i>
                Record Payment
            </a>
        </div>
    </div>

    <!-- Payments List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900">All Payments for <?php echo date('F Y'); ?></h2>
                <div class="text-sm text-gray-600">
                    Showing <?php echo count($payments); ?> of <?php echo $pagination['total_count']; ?> payments
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <?php if (empty($payments)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-coins text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Payments Found</h3>
                    <p class="text-gray-500 mb-6">No welfare payments have been recorded for this month.</p>
                    <a href="<?php echo BASE_URL; ?>welfare/add" 
                       class="bg-emerald-500 hover:bg-emerald-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Record First Payment
                    </a>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php foreach ($payments as $payment): ?>
                        <div class="bg-green-50 rounded-lg p-4 hover:bg-green-100 transition-colors border border-green-200">
                            <div class="flex items-start justify-between">
                                <div class="flex items-center flex-1">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-green-600"></i>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <p class="font-medium text-gray-900">
                                            <?php echo htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']); ?>
                                        </p>
                                        <p class="text-sm text-gray-600">
                                            <?php echo $payment['phone_number']; ?>
                                        </p>
                                        <p class="text-xs text-gray-500 mt-1">
                                            <?php echo date('M d, Y', strtotime($payment['payment_date'])); ?> •
                                            <?php echo ucfirst(str_replace('_', ' ', $payment['payment_method'])); ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right ml-3">
                                    <p class="font-bold text-green-700 text-lg">₵<?php echo number_format($payment['amount'], 2); ?></p>

                                    <!-- View History Button -->
                                    <div class="mt-2 mb-2">
                                        <a href="<?php echo BASE_URL; ?>welfare/history/<?php echo $payment['member_id']; ?>"
                                           class="inline-flex items-center px-3 py-1 bg-emerald-50 hover:bg-emerald-100 text-emerald-700 rounded-lg text-xs font-medium transition-colors border border-emerald-200">
                                            <i class="fas fa-history mr-1"></i>
                                            View History
                                        </a>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex space-x-1">
                                        <a href="<?php echo BASE_URL; ?>welfare/edit-payment/<?php echo $payment['payment_id']; ?>"
                                           class="text-blue-600 hover:text-blue-800 p-1 rounded transition-colors"
                                           title="Edit Payment">
                                            <i class="fas fa-edit text-sm"></i>
                                        </a>
                                        <button onclick="deletePayment(<?php echo $payment['payment_id']; ?>, '<?php echo htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']); ?>')"
                                                class="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
                                                title="Delete Payment">
                                            <i class="fas fa-trash text-sm"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <div class="mt-8 flex justify-center">
                        <nav class="flex items-center space-x-2">
                            <?php if ($pagination['has_prev']): ?>
                                <a href="?page=<?php echo $pagination['current_page'] - 1; ?>" 
                                   class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>" 
                                   class="px-3 py-2 <?php echo $i == $pagination['current_page'] ? 'bg-emerald-500 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'; ?> rounded-lg transition-colors">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <a href="?page=<?php echo $pagination['current_page'] + 1; ?>" 
                                   class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                    
                    <div class="mt-4 text-center text-sm text-gray-600">
                        Page <?php echo $pagination['current_page']; ?> of <?php echo $pagination['total_pages']; ?> 
                        (<?php echo $pagination['total_count']; ?> total payments)
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function deletePayment(paymentId, memberName) {
    if (confirm(`Are you sure you want to delete the payment for ${memberName}?`)) {
        // Add your delete payment logic here
        window.location.href = `<?php echo BASE_URL; ?>welfare/delete-payment/${paymentId}`;
    }
}
</script>
