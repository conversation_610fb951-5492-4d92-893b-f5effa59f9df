<?php
// Set page title and active page
$page_title = 'Analytics Dashboard - ICGC Emmanuel Temple';
$active_page = 'dashboard';

// Include chart components
require_once 'views/dashboard/charts.php';

// Start output buffering
ob_start();
?>

<div class="container mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-primary-dark">Analytics Dashboard</h1>
            <p class="text-sm text-gray-600">Detailed metrics and insights for ICGC Emmanuel Temple</p>
        </div>
        <div class="flex space-x-2">
            <div class="text-sm bg-white px-3 py-2 rounded-lg shadow-sm border border-secondary">
                <i class="far fa-calendar-alt text-primary mr-2"></i><?php echo date('l, F j, Y'); ?>
            </div>
            <button class="bg-primary text-white px-3 py-2 rounded-lg shadow-sm hover:bg-primary-dark transition-colors duration-200">
                <i class="fas fa-download mr-1"></i> Export
            </button>
        </div>
    </div>

    <!-- Date Range Selector -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
        <div class="flex flex-wrap items-center justify-between">
            <div class="flex items-center space-x-2">
                <span class="text-gray-700 font-medium">Date Range:</span>
                <div class="relative">
                    <select class="pl-3 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary text-sm">
                        <option value="today">Today</option>
                        <option value="yesterday">Yesterday</option>
                        <option value="last7days">Last 7 Days</option>
                        <option value="last30days" selected>Last 30 Days</option>
                        <option value="thismonth">This Month</option>
                        <option value="lastmonth">Last Month</option>
                        <option value="custom">Custom Range</option>
                    </select>
                </div>
            </div>
            <div class="flex space-x-2 mt-2 sm:mt-0">
                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                    <i class="fas fa-sync-alt mr-1"></i> Refresh
                </button>
                <button class="px-3 py-1 bg-secondary text-primary-dark rounded-md text-sm hover:bg-secondary-dark transition-colors duration-200">
                    <i class="fas fa-filter mr-1"></i> Filter
                </button>
            </div>
        </div>
    </div>

    <!-- Stats Summary -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-primary">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-secondary-light text-primary mr-4">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Total Members</p>
                    <h3 class="text-2xl font-bold text-gray-800">250</h3>
                </div>
            </div>
            <div class="mt-4 flex items-center">
                <span class="text-green-500 text-sm flex items-center mr-2">
                    <i class="fas fa-arrow-up mr-1"></i> 5.3%
                </span>
                <span class="text-gray-500 text-sm">vs last month</span>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-primary-light">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-secondary-light text-primary-light mr-4">
                    <i class="fas fa-clipboard-check text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Average Attendance</p>
                    <h3 class="text-2xl font-bold text-gray-800">180</h3>
                </div>
            </div>
            <div class="mt-4 flex items-center">
                <span class="text-green-500 text-sm flex items-center mr-2">
                    <i class="fas fa-arrow-up mr-1"></i> 2.1%
                </span>
                <span class="text-gray-500 text-sm">vs last month</span>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-primary">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-secondary-light text-primary mr-4">
                    <i class="fas fa-money-bill-wave text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Monthly Offerings</p>
                    <h3 class="text-2xl font-bold text-gray-800">GH₵ 5,200</h3>
                </div>
            </div>
            <div class="mt-4 flex items-center">
                <span class="text-red-500 text-sm flex items-center mr-2">
                    <i class="fas fa-arrow-down mr-1"></i> 1.2%
                </span>
                <span class="text-gray-500 text-sm">vs last month</span>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-primary-light">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-secondary-light text-primary-light mr-4">
                    <i class="fas fa-user-plus text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">New Members</p>
                    <h3 class="text-2xl font-bold text-gray-800">12</h3>
                </div>
            </div>
            <div class="mt-4 flex items-center">
                <span class="text-green-500 text-sm flex items-center mr-2">
                    <i class="fas fa-arrow-up mr-1"></i> 8.7%
                </span>
                <span class="text-gray-500 text-sm">vs last month</span>
            </div>
        </div>
    </div>

    <!-- Charts - First Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <?php renderAttendanceChart(); ?>
        <?php renderFinancialChart(); ?>
    </div>

    <!-- Charts - Second Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <?php renderMembershipChart(); ?>
        <?php renderDepartmentChart(); ?>
    </div>

    <!-- Recent Activities -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-primary-dark">Recent Activities</h2>
            <span class="text-xs text-white bg-primary px-2 py-1 rounded-full">Today</span>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user-plus text-blue-500"></i>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">New member registered</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">Admin User</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500">10:30 AM</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-clipboard-check text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">Attendance recorded</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">Staff User</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500">9:45 AM</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-money-bill-wave text-yellow-500"></i>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">Offering recorded</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">Finance Officer</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-500">8:30 AM</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="mt-4 text-right">
            <a href="#" class="text-primary text-sm hover:underline flex items-center justify-end">
                <span>View all activities</span>
                <i class="fas fa-arrow-right ml-1 text-xs"></i>
            </a>
        </div>
    </div>
</div>

<?php
// Get the contents of the output buffer
$content = ob_get_clean();

// Include the layout template
include 'views/layouts/main.php';
?>
