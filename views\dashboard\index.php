<?php
// Include helpers for dynamic church name
require_once 'helpers/functions.php';

// Helper function for time ago
if (!function_exists('time_ago')) {
    function time_ago($datetime) {
        $time = strtotime($datetime);
        $now = time();
        $diff = $now - $time;

        if ($diff < 60) {
            return 'Just now';
        } elseif ($diff < 3600) {
            $mins = floor($diff / 60);
            return $mins . ' minute' . ($mins > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 2592000) {
            $weeks = floor($diff / 604800);
            return $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 31536000) {
            $months = floor($diff / 2592000);
            return $months . ' month' . ($months > 1 ? 's' : '') . ' ago';
        } else {
            $years = floor($diff / 31536000);
            return $years . ' year' . ($years > 1 ? 's' : '') . ' ago';
        }
    }
}
?>

<div class="container mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-primary-dark">Dashboard</h1>
            <p class="text-sm text-gray-600">Welcome to <?php echo htmlspecialchars(getChurchName()); ?></p>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Notification Bell -->
            <div class="relative">
                <button onclick="toggleNotificationCenter()" class="text-gray-600 hover:text-primary transition-colors relative">
                    <i class="fas fa-bell text-xl"></i>
                    <span id="notification-badge" class="hidden absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
                </button>
            </div>
            <!-- Date Display -->
            <div class="text-sm bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-200">
                <i class="far fa-calendar-alt text-primary mr-2"></i><?php echo date('l, F j, Y'); ?>
            </div>
        </div>
    </div>

    <!-- Top Quick Actions -->
    <div class="flex flex-wrap gap-3 mb-6">
        <a href="<?php echo BASE_URL; ?>members/add" class="flex items-center px-4 py-2 bg-white rounded-lg hover:bg-gray-50 transition-colors border border-gray-200 shadow-sm">
            <i class="fas fa-user-plus text-primary mr-2"></i>
            <span class="text-sm font-medium text-gray-700">Add Member</span>
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/qr" class="flex items-center px-4 py-2 bg-white rounded-lg hover:bg-gray-50 transition-colors border border-gray-200 shadow-sm">
            <i class="fas fa-qrcode text-primary mr-2"></i>
            <span class="text-sm font-medium text-gray-700">QR Attendance</span>
        </a>
        <a href="<?php echo BASE_URL; ?>finances/add" class="flex items-center px-4 py-2 bg-white rounded-lg hover:bg-gray-50 transition-colors border border-gray-200 shadow-sm">
            <i class="fas fa-money-bill-wave text-primary mr-2"></i>
            <span class="text-sm font-medium text-gray-700">Add Transaction</span>
        </a>
        <a href="<?php echo BASE_URL; ?>sms/create" class="flex items-center px-4 py-2 bg-white rounded-lg hover:bg-gray-50 transition-colors border border-gray-200 shadow-sm">
            <i class="fas fa-sms text-primary mr-2"></i>
            <span class="text-sm font-medium text-gray-700">Send SMS</span>
        </a>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 mb-4">
        <!-- Total Members -->
        <div class="gradient-card card-blue transform hover:-translate-y-1 transition-all duration-200" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; min-height: 120px;">
            <div class="card-icon" style="background-color: #60a5fa; box-shadow: 0 0 15px rgba(96, 165, 250, 0.5);">
                <i class="fas fa-users" style="color: white;"></i>
            </div>
            <div class="card-title" style="color: white;">Total Members</div>
            <div class="card-value" style="color: white;"><?php echo $total_members; ?></div>
            <div class="card-subtitle" style="color: white;">
                <a href="<?php echo BASE_URL; ?>members" class="hover:underline flex items-center" style="color: white;">
                    <span>View</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
            <div class="card-shine"></div>
        </div>

        <!-- New Members -->
        <div class="gradient-card card-yellow transform hover:-translate-y-1 transition-all duration-200" style="background: linear-gradient(135deg, #eab308 0%, #a16207 100%); color: white; min-height: 120px;">
            <div class="card-icon" style="background-color: #facc15; box-shadow: 0 0 15px rgba(250, 204, 21, 0.5);">
                <i class="fas fa-user-plus" style="color: white;"></i>
            </div>
            <div class="card-title" style="color: white;">New Members</div>
            <div class="card-value" style="color: white;"><?php echo $new_members; ?></div>
            <div class="text-[0.6rem] text-white opacity-75 mb-2">Last 30 days</div>
            <div class="card-subtitle" style="color: white;">
                <a href="<?php echo BASE_URL; ?>members?filter=new" class="hover:underline flex items-center" style="color: white;">
                    <span>View</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
            <div class="card-shine"></div>
        </div>

        <!-- Total Children -->
        <div class="gradient-card card-orange transform hover:-translate-y-1 transition-all duration-200" style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); color: white; min-height: 120px;">
            <div class="card-icon" style="background-color: #fb923c; box-shadow: 0 0 15px rgba(251, 146, 60, 0.5);">
                <i class="fas fa-child" style="color: white;"></i>
            </div>
            <div class="card-title" style="color: white;">Total Children</div>
            <div class="card-value" style="color: white;"><?php echo $total_children; ?></div>
            <div class="text-[0.6rem] text-white opacity-75 mb-2">Under 18 years</div>
            <div class="card-subtitle" style="color: white;">
                <a href="<?php echo BASE_URL; ?>children-ministry/children" class="hover:underline flex items-center" style="color: white;">
                    <span>View</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
            <div class="card-shine"></div>
        </div>

        <!-- Financial Balance -->
        <div class="gradient-card card-green transform hover:-translate-y-1 transition-all duration-200" style="background: linear-gradient(135deg, #22c55e 0%, #15803d 100%); color: white; min-height: 120px;">
            <div class="card-icon" style="background-color: #4ade80; box-shadow: 0 0 15px rgba(74, 222, 128, 0.5);">
                <i class="fas fa-money-bill-wave" style="color: white;"></i>
            </div>
            <div class="card-title" style="color: white;">Financial Balance</div>
            <div class="card-value" style="color: white;" data-financial-balance="<?php echo $financial_balance; ?>">GH₵ <?php echo number_format($financial_balance, 2); ?></div>
            <div class="card-subtitle" style="color: white;">
                <a href="<?php echo BASE_URL; ?>finance" class="hover:underline flex items-center" style="color: white;">
                    <span>View</span>
                    <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
            <div class="card-shine"></div>
        </div>

        <!-- Birthdays This Month -->
        <div class="gradient-card card-purple transform hover:-translate-y-1 transition-all duration-200" style="background: linear-gradient(135deg, #a855f7 0%, #7e22ce 100%); color: white; min-height: 160px;">
            <div class="card-icon" style="background-color: #c084fc; box-shadow: 0 0 15px rgba(192, 132, 252, 0.5);">
                <i class="fas fa-birthday-cake" style="color: white;"></i>
            </div>
            <div class="card-title" style="color: white;">Birthdays</div>
            <div class="card-value" style="color: white;" data-inactive-members="<?php echo $inactive_members; ?>"><?php echo count($birthdays_this_month); ?></div>
            <div class="text-subtitle" style="color: white;">This month</div>
            <div class="card-subtitle" style="color: white;">
                <?php if (!empty($birthdays_this_week)): ?>
                    <button onclick="showWeeklyBirthdaySMS()" class="bg-white bg-opacity-25 hover:bg-opacity-40 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:scale-105 border border-white border-opacity-30 w-full justify-center mt-2" title="Send SMS to this week's birthday celebrants (<?php echo count($birthdays_this_week); ?> members)">
                        <i class="fas fa-sms mr-2"></i>
                        <span>This Week (<?php echo count($birthdays_this_week); ?>)</span>
                    </button>
                <?php else: ?>
                    <a href="<?php echo BASE_URL; ?>birthdays" class="text-white hover:underline text-sm opacity-75 mt-2 inline-block">View All</a>
                <?php endif; ?>
            </div>
            <div class="card-shine"></div>
        </div>
    </div>

    <!-- Main Content - Two Column Layout -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-8">

        <!-- Left Column -->
        <div class="space-y-8">
            <!-- Member Demographics Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Gender Distribution Chart -->
                <div class="chart-container">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-primary-dark flex items-center">
                            <i class="fas fa-venus-mars mr-2 text-primary"></i>
                            Gender Distribution
                        </h3>
                        <div class="text-sm text-gray-600">
                            Total: <?php echo $total_members; ?> members
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="gender-chart"></canvas>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-4">
                        <div class="text-center p-3 bg-blue-50 rounded-lg">
                            <div class="text-xl font-bold text-blue-600" data-gender="male"><?php echo $gender_percentage['male']; ?>%</div>
                            <div class="text-sm text-gray-600">Male</div>
                            <div class="text-xs text-gray-500"><?php echo $male_members; ?> members</div>
                        </div>
                        <div class="text-center p-3 bg-pink-50 rounded-lg">
                            <div class="text-xl font-bold text-pink-600" data-gender="female"><?php echo $gender_percentage['female']; ?>%</div>
                            <div class="text-sm text-gray-600">Female</div>
                            <div class="text-xs text-gray-500"><?php echo $female_members; ?> members</div>
                        </div>
                    </div>
                </div>

                <!-- Age Distribution Chart -->
                <div class="chart-container">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-primary-dark flex items-center">
                            <i class="fas fa-users mr-2 text-primary"></i>
                            Age Distribution
                        </h3>
                        <div class="chart-controls">
                            <select id="age-chart-type" class="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="doughnut" selected>Doughnut</option>
                                <option value="bar">Bar Chart</option>
                                <option value="pie">Pie Chart</option>
                            </select>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="age-chart"></canvas>
                    </div>
                    <div class="grid grid-cols-2 gap-2 mt-4">
                        <div class="text-center p-2 bg-green-50 rounded">
                            <div class="text-sm font-bold text-green-600"><?php echo $age_percentage['under_18']; ?>%</div>
                            <div class="text-xs text-gray-600">Under 18</div>
                        </div>
                        <div class="text-center p-2 bg-blue-50 rounded">
                            <div class="text-sm font-bold text-blue-600"><?php echo $age_percentage['18_35']; ?>%</div>
                            <div class="text-xs text-gray-600">18-35</div>
                        </div>
                        <div class="text-center p-2 bg-purple-50 rounded">
                            <div class="text-sm font-bold text-purple-600"><?php echo $age_percentage['36_60']; ?>%</div>
                            <div class="text-xs text-gray-600">36-60</div>
                        </div>
                        <div class="text-center p-2 bg-orange-50 rounded">
                            <div class="text-sm font-bold text-orange-600"><?php echo $age_percentage['over_60']; ?>%</div>
                            <div class="text-xs text-gray-600">Over 60</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Trend Chart -->
            <div class="chart-container">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-primary-dark flex items-center">
                        <i class="fas fa-chart-line mr-2 text-primary"></i>
                        Attendance Trends
                    </h3>
                    <div class="chart-controls">
                        <select id="attendance-period" class="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="monthly" selected>Monthly</option>
                            <option value="weekly">Weekly</option>
                            <option value="yearly">Yearly</option>
                        </select>
                    </div>
                </div>
                <div class="h-64 relative">
                    <canvas id="attendance-trend-chart"></canvas>
                    <div id="attendance-chart-loading" class="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-75">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin text-2xl text-primary mb-2"></i>
                            <div class="text-sm text-gray-600">Loading Attendance Trends...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Members Absent for 3 Consecutive Sundays -->
            <div class="absent-members-alert-container">
                <!-- Enhanced Header with Gradient Background -->
                <div class="absent-alert-header">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="alert-icon-wrapper">
                                <i class="fas fa-exclamation-triangle text-white text-lg"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-bold text-white">Absent Members Alert</h3>
                                <p class="text-xs text-red-100 opacity-90">Members missing 3 consecutive QR sessions</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <?php if ($total_absent_members_3_sundays > 0): ?>
                                <div class="alert-count-badge">
                                    <span class="text-lg font-bold"><?php echo $total_absent_members_3_sundays; ?></span>
                                    <span class="text-xs">Total</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Content Area -->
                <div class="absent-alert-content">
                    <?php if (!empty($absent_members_3_sundays)): ?>
                        <div class="space-y-3">
                            <?php foreach ($absent_members_3_sundays as $index => $member): ?>
                                <div class="absent-member-card">
                                    <div class="flex items-center">
                                        <div class="member-avatar">
                                            <span class="text-sm font-bold text-red-600">
                                                <?php
                                                $name_parts = explode(' ', $member['name']);
                                                echo strtoupper(substr($name_parts[0], 0, 1) . (isset($name_parts[1]) ? substr($name_parts[1], 0, 1) : ''));
                                                ?>
                                            </span>
                                        </div>
                                        <div class="flex-1 ml-3">
                                            <div class="flex items-center justify-between">
                                                <h4 class="text-sm font-semibold text-gray-900">
                                                    <?php echo htmlspecialchars($member['name']); ?>
                                                </h4>
                                                <div class="member-actions">
                                                    <a href="tel:<?php echo $member['phone']; ?>"
                                                       class="action-btn action-btn-call"
                                                       title="Call Member">
                                                        <i class="fas fa-phone"></i>
                                                    </a>
                                                    <a href="<?php echo BASE_URL; ?>sms/create?phone=<?php echo urlencode($member['phone']); ?>&name=<?php echo urlencode($member['name']); ?>"
                                                       class="action-btn action-btn-sms"
                                                       title="Send SMS">
                                                        <i class="fas fa-sms"></i>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="member-details">
                                                <span class="detail-badge">
                                                    <i class="fas fa-users mr-1"></i>
                                                    <?php echo htmlspecialchars($member['department']); ?>
                                                </span>
                                                <span class="detail-badge">
                                                    <i class="fas fa-phone mr-1"></i>
                                                    <?php echo htmlspecialchars($member['phone']); ?>
                                                </span>
                                                <span class="detail-badge detail-badge-warning">
                                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                                    <?php echo $member['consecutive_absences']; ?> consecutive absences
                                                </span>
                                            </div>
                                            <div class="last-attendance">
                                                <i class="fas fa-calendar-times mr-1 text-red-500"></i>
                                                <span class="text-xs text-red-600 font-medium">
                                                    Last QR attendance: <?php echo $member['last_attendance'] ? date('M j, Y', strtotime($member['last_attendance'])) : 'Never attended via QR'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <?php if ($total_absent_members_3_sundays > 5): ?>
                            <div class="view-all-section">
                                <a href="<?php echo BASE_URL; ?>attendance/reminders" class="view-all-btn">
                                    <i class="fas fa-list-ul mr-2"></i>
                                    View All <?php echo $total_absent_members_3_sundays; ?> Absent Members
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="no-absent-members">
                            <div class="success-icon">
                                <i class="fas fa-check-circle text-green-500 text-3xl"></i>
                            </div>
                            <div class="success-content">
                                <h4 class="text-lg font-bold text-green-700 mb-1">Excellent Attendance!</h4>
                                <p class="text-sm text-green-600 mb-2">No members have been absent for 3 consecutive Sundays</p>
                                <div class="success-badge">
                                    <i class="fas fa-heart mr-1"></i>
                                    Everyone is faithfully attending
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-8">
            <!-- Financial Overview Chart -->
            <div class="chart-container">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-primary-dark flex items-center">
                        <i class="fas fa-chart-bar mr-2 text-primary"></i>
                        Financial Overview
                    </h3>
                    <div class="chart-controls">
                        <select id="financial-period" class="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="6months" selected>Last 6 Months</option>
                            <option value="3months">Last 3 Months</option>
                            <option value="yearly">This Year</option>
                        </select>
                    </div>
                </div>
                <div class="h-64 relative">
                    <canvas id="financial-overview-chart"></canvas>
                    <div id="financial-chart-loading" class="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-75">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin text-2xl text-primary mb-2"></i>
                            <div class="text-sm text-gray-600">Loading Financial Overview...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Department Distribution Chart -->
            <div class="chart-container">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-primary-dark flex items-center">
                        <i class="fas fa-sitemap mr-2 text-primary"></i>
                        Department Distribution
                    </h3>
                    <div class="chart-controls">
                        <select id="dept-chart-type" class="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="bar" selected>Bar Chart</option>
                            <option value="doughnut">Doughnut</option>
                            <option value="pie">Pie Chart</option>
                            <option value="polarArea">Polar Area</option>
                        </select>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="department-chart"></canvas>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-3 mt-4">
                    <?php if (!empty($department_counts)): ?>
                        <?php foreach ($department_counts as $dept => $count): ?>
                        <div class="text-center p-3 bg-gray-50 rounded-lg">
                            <div class="text-lg font-bold text-primary" data-dept-count="<?php echo $count; ?>"><?php echo $count; ?></div>
                            <div class="text-xs text-gray-600" data-dept-name="<?php echo htmlspecialchars($dept); ?>"><?php echo htmlspecialchars($dept); ?></div>
                            <div class="text-xs text-gray-500"><?php echo $total_members > 0 ? round(($count / $total_members) * 100, 1) : 0; ?>%</div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-span-full text-center p-6 bg-gray-50 rounded-lg">
                            <i class="fas fa-info-circle text-gray-400 text-2xl mb-2"></i>
                            <p class="text-gray-600">No department data available</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        </div>
    </div>
</div>

<style>
/* Override styles for gradient cards */
.gradient-card.card-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
}

.gradient-card.card-yellow {
    background: linear-gradient(135deg, #eab308 0%, #a16207 100%) !important;
}

.gradient-card.card-green {
    background: linear-gradient(135deg, #22c55e 0%, #15803d 100%) !important;
}

.gradient-card.card-purple {
    background: linear-gradient(135deg, #a855f7 0%, #7e22ce 100%) !important;
}

.gradient-card .card-title,
.gradient-card .card-value,
.gradient-card .card-subtitle,
.gradient-card .card-subtitle a {
    color: white !important;
}

.gradient-card .card-value {
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    line-height: 1 !important;
    margin-bottom: 0.3rem !important;
}

.gradient-card .card-icon i {
    color: white !important;
}

/* Decorative elements */
.gradient-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-image:
        radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 5%, transparent 15%),
        radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 10%, transparent 20%),
        radial-gradient(circle at 15% 85%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 10%, transparent 20%),
        radial-gradient(circle at 90% 90%, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 5%, transparent 15%),
        repeating-linear-gradient(45deg, rgba(255, 255, 255, 0.03) 0px, rgba(255, 255, 255, 0.03) 1px, transparent 1px, transparent 10px) !important;
    opacity: 0.6 !important;
    z-index: 0 !important;
}

.gradient-card::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 1.5rem !important;
    height: 1.5rem !important;
    background: rgba(255, 255, 255, 0.2) !important;
    clip-path: polygon(100% 0, 0 0, 100% 100%) !important;
    z-index: 0 !important;
}

.gradient-card {
    border-radius: 0.4rem 1rem 0.4rem 0.4rem !important;
    clip-path: polygon(0 0, 100% 0, 100% 92%, 92% 100%, 0 100%) !important;
    padding: 0.9rem !important;
}

.gradient-card .card-icon {
    border-radius: 0.5rem !important;
    transform: rotate(-5deg) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    width: 2.5rem !important;
    height: 2.5rem !important;
    margin-bottom: 0.5rem !important;
}

.gradient-card .card-icon i {
    transform: rotate(5deg) !important;
    font-size: 1.1rem !important;
}

.gradient-card .card-title {
    position: relative !important;
    display: inline-block !important;
    padding-bottom: 0.3rem !important;
    font-size: 0.7rem !important;
    margin-bottom: 0.2rem !important;
    letter-spacing: 0.05em !important;
}

.gradient-card .card-title::after {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    bottom: 0 !important;
    height: 1px !important;
    width: 1.2rem !important;
    background-color: rgba(255, 255, 255, 0.5) !important;
}

.gradient-card .card-subtitle {
    position: absolute !important;
    bottom: 0.7rem !important;
    left: 0.9rem !important;
    right: 0.9rem !important;
    z-index: 3 !important;
    font-size: 0.65rem !important;
}

/* Enhanced Birthday Button Styling */
.gradient-card .card-subtitle button {
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.gradient-card .card-subtitle button:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

/* Department card hover effects */
.chart-container .grid > div {
    transition: all 0.2s ease-in-out;
}

.chart-container .grid > div:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Chart container styling */
.chart-container {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    border: 1px solid rgba(229, 231, 235, 0.8);
}

/* Enhanced Absent Members Alert Styling */
.absent-members-alert-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid rgba(239, 68, 68, 0.2);
    position: relative;
}

.absent-alert-header {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
    padding: 1.25rem;
    position: relative;
    overflow: hidden;
}

.absent-alert-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.alert-icon-wrapper {
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.alert-count-badge {
    background: rgba(255, 255, 255, 0.95);
    color: #dc2626;
    padding: 0.5rem 0.75rem;
    border-radius: 0.75rem;
    text-align: center;
    min-width: 3rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.absent-alert-content {
    padding: 1.5rem;
    background: linear-gradient(to bottom, #fef2f2, #ffffff);
}

.absent-member-card {
    background: white;
    border: 1px solid rgba(239, 68, 68, 0.15);
    border-radius: 0.75rem;
    padding: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.absent-member-card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #dc2626, #f87171);
}

.absent-member-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
}

.member-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border: 2px solid #fca5a5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.member-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    text-decoration: none;
}

.action-btn-call {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.action-btn-call:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.action-btn-sms {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.action-btn-sms:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.member-details {
    display: flex;
    gap: 0.75rem;
    margin: 0.5rem 0;
    flex-wrap: wrap;
}

.detail-badge {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    border: 1px solid #e5e7eb;
}

.detail-badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #D97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.last-attendance {
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    background: #fef2f2;
    border-radius: 0.5rem;
    border: 1px solid #fecaca;
}

.view-all-section {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 2px dashed #fca5a5;
    text-align: center;
}

.view-all-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    border-radius: 0.75rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.view-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);
    background: linear-gradient(135deg, #b91c1c, #991b1b);
}

.no-absent-members {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    border-radius: 1rem;
    border: 2px dashed #86efac;
}

.success-icon {
    margin-bottom: 1rem;
}

.success-content h4 {
    margin-bottom: 0.5rem;
}

.success-badge {
    display: inline-flex;
    align-items: center;
    background: #22c55e;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .absent-alert-header {
        padding: 1rem;
    }

    .absent-alert-content {
        padding: 1rem;
    }

    .member-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .detail-badge {
        align-self: flex-start;
    }
}
</style>

<script>
// Weekly Birthday SMS Modal Function
function showWeeklyBirthdaySMS() {
    const weeklyBirthdays = [
        <?php if (!empty($birthdays_this_week)): ?>
        <?php foreach ($birthdays_this_week as $birthday): ?>
        {
            id: <?php echo $birthday['id']; ?>,
            name: "<?php echo htmlspecialchars($birthday['first_name'] . ' ' . $birthday['last_name']); ?>",
            birthday: "<?php echo date('M d', strtotime($birthday['date_of_birth'])); ?>",
            age: "<?php echo $birthday['age'] ?? 'N/A'; ?>",
            phone: "<?php echo htmlspecialchars($birthday['phone_number']); ?>",
            department: "<?php echo htmlspecialchars(ucfirst($birthday['department'] != 'none' ? str_replace('_', ' ', $birthday['department']) : 'No Department')); ?>"
        },
        <?php endforeach; ?>
        <?php endif; ?>
    ];

    if (weeklyBirthdays.length === 0) {
        alert('No birthdays this week!');
        return;
    }

    // Create modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modal.innerHTML = `
        <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-xl font-bold flex items-center">
                            <i class="fas fa-birthday-cake mr-2"></i>
                            This Week's Birthday Celebrants
                        </h3>
                        <p class="text-purple-100 mt-1">${weeklyBirthdays.length} member${weeklyBirthdays.length > 1 ? 's' : ''} celebrating this week</p>
                    </div>
                    <button onclick="this.closest('.fixed').remove()" class="text-white hover:text-purple-200 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Content -->
            <div class="p-6 max-h-96 overflow-y-auto">
                <div class="space-y-4">
                    ${weeklyBirthdays.map(birthday => `
                        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full flex items-center justify-center mr-4">
                                    <span class="font-bold">${birthday.name.split(' ').map(n => n[0]).join('')}</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">${birthday.name}</h4>
                                    <div class="text-sm text-gray-600">
                                        <span class="inline-flex items-center mr-3">
                                            <i class="fas fa-birthday-cake mr-1 text-purple-500"></i>
                                            ${birthday.birthday} (Age ${birthday.age})
                                        </span>
                                        <span class="inline-flex items-center mr-3">
                                            <i class="fas fa-phone mr-1 text-green-500"></i>
                                            ${birthday.phone}
                                        </span>
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-users mr-1 text-blue-500"></i>
                                            ${birthday.department}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <a href="<?php echo BASE_URL; ?>birthdays/sms?recipient=${birthday.id}"
                                   class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center">
                                    <i class="fas fa-sms mr-1"></i>
                                    SMS
                                </a>
                                <a href="<?php echo BASE_URL; ?>members/view?id=${birthday.id}"
                                   class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    View
                                </a>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <!-- Footer -->
            <div class="bg-gray-50 px-6 py-4 flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    <i class="fas fa-info-circle mr-1"></i>
                    Click SMS to send individual wishes or use Bulk SMS for all
                </div>
                <div class="flex space-x-3">
                    <button onclick="this.closest('.fixed').remove()"
                            class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg transition-colors">
                        Close
                    </button>
                    <a href="<?php echo BASE_URL; ?>birthdays/sms?recipients=${weeklyBirthdays.map(b => b.id).join(',')}"
                       class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center">
                        <i class="fas fa-sms mr-2"></i>
                        Send Bulk SMS
                    </a>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}
</script>

<!-- Performance monitoring (development only) -->
<?php if ($_SERVER['HTTP_HOST'] === 'localhost'): ?>
<script src="<?php echo BASE_URL; ?>assets/js/performance-monitor.js"></script>
<?php endif; ?>

<!-- Enhanced Chart Loading System with Race Condition Prevention -->
<script>
/**
 * ICGC Dashboard Chart Manager
 * Ensures reliable chart loading by managing dependencies and timing
 */
window.ICGC_DASHBOARD_CHARTS = {
    chartsLoaded: false,
    chartJsReady: false,
    domReady: false,
    scriptsToLoad: [],
    loadedScripts: new Set(),

    // Initialize the chart loading system
    init: function() {
        console.log('🚀 ICGC Dashboard Chart Manager initializing...');

        // Check if DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.domReady = true;
                this.checkAndInitCharts();
            });
        } else {
            this.domReady = true;
        }

        // Ensure Chart.js is loaded
        this.ensureChartJs();
    },

    // Ensure Chart.js is available
    ensureChartJs: function() {
        if (typeof Chart !== 'undefined') {
            console.log('✅ Chart.js already available');
            this.chartJsReady = true;
            this.checkAndInitCharts();
            return;
        }

        // Check if Chart.js script exists
        const existingScript = document.querySelector('script[src*="chart.js"]');
        if (existingScript) {
            console.log('⏳ Chart.js script found, waiting for load...');
            existingScript.addEventListener('load', () => {
                this.chartJsReady = true;
                this.checkAndInitCharts();
            });
            return;
        }

        // Load Chart.js if not present
        console.log('📦 Loading Chart.js...');
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
        script.onload = () => {
            console.log('✅ Chart.js loaded successfully');
            this.chartJsReady = true;
            this.checkAndInitCharts();
        };
        script.onerror = () => {
            console.error('❌ Failed to load Chart.js');
        };
        document.head.appendChild(script);
    },

    // Check if all dependencies are ready and initialize charts
    checkAndInitCharts: function() {
        if (!this.domReady || !this.chartJsReady || this.chartsLoaded) {
            console.log('⏳ Waiting for dependencies...', {
                domReady: this.domReady,
                chartJsReady: this.chartJsReady,
                chartsLoaded: this.chartsLoaded
            });
            return;
        }

        console.log('🎯 All dependencies ready, initializing charts...');
        this.chartsLoaded = true;

        // Initialize charts with proper error handling
        this.initializeAllCharts();
    },

    // Initialize all dashboard charts
    initializeAllCharts: function() {
        const charts = [
            { id: 'financial-overview-chart', init: this.initFinancialChart },
            { id: 'attendance-trend-chart', init: this.initAttendanceChart },
            { id: 'gender-chart', init: this.initGenderChart },
            { id: 'age-chart', init: this.initAgeChart },
            { id: 'department-chart', init: this.initDepartmentChart }
        ];

        charts.forEach(chart => {
            const element = document.getElementById(chart.id);
            if (element) {
                console.log(`🎨 Initializing ${chart.id}...`);
                try {
                    chart.init.call(this, element);
                } catch (error) {
                    console.error(`❌ Error initializing ${chart.id}:`, error);
                }
            } else {
                console.log(`⚠️ Chart element ${chart.id} not found`);
            }
        });

        // Load additional dashboard features
        this.loadDashboardFeatures();
    },

    // Initialize Financial Overview Chart
    initFinancialChart: function(canvas) {
        const dropdown = document.getElementById('financial-period');
        const loadingDiv = document.getElementById('financial-chart-loading');

        if (!dropdown) {
            console.error('❌ Financial period dropdown not found');
            return;
        }

        let chart = null;

        const updateChart = (period) => {
            if (loadingDiv) loadingDiv.style.display = 'flex';

            fetch(`api/dashboard_data.php?action=financial_overview&period=${period}`)
                .then(response => {
                    console.log('📊 Financial API Response:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📊 Financial Data:', data);
                    if (data.success && data.labels && data.income && data.expenses) {
                        if (chart) chart.destroy();

                        chart = new Chart(canvas, {
                            type: 'line',
                            data: {
                                labels: data.labels,
                                datasets: [{
                                    label: 'Income',
                                    data: data.income,
                                    borderColor: 'rgb(59, 130, 246)',
                                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                    tension: 0.4,
                                    fill: true
                                }, {
                                    label: 'Expenses',
                                    data: data.expenses,
                                    borderColor: 'rgb(239, 68, 68)',
                                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                    tension: 0.4,
                                    fill: true
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: { position: 'top' },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                return context.dataset.label + ': GH₵' + context.parsed.y.toLocaleString();
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            callback: function(value) {
                                                return 'GH₵' + value.toLocaleString();
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        console.log('✅ Financial chart created');
                    } else {
                        console.error('❌ Financial chart data invalid:', data);
                        // Show error message in chart area
                        canvas.getContext('2d').fillText('Error loading financial data', 10, 50);
                    }
                    if (loadingDiv) loadingDiv.style.display = 'none';
                })
                .catch(error => {
                    console.error('❌ Financial chart error:', error);
                    if (loadingDiv) loadingDiv.style.display = 'none';
                    // Show error message in chart area
                    const ctx = canvas.getContext('2d');
                    ctx.fillText('Failed to load financial data', 10, 50);
                });
        };

        // Initial load
        updateChart(dropdown.value);

        // Dropdown change handler
        dropdown.addEventListener('change', () => updateChart(dropdown.value));
    },

    // Initialize Attendance Trends Chart
    initAttendanceChart: function(canvas) {
        const dropdown = document.getElementById('attendance-period');
        const loadingDiv = document.getElementById('attendance-chart-loading');

        if (!dropdown) {
            console.error('❌ Attendance period dropdown not found');
            return;
        }

        let chart = null;

        const updateChart = (period) => {
            if (loadingDiv) loadingDiv.style.display = 'flex';

            fetch(`api/dashboard_data.php?action=attendance_trends&period=${period}`)
                .then(response => {
                    console.log('📈 Attendance API Response:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📈 Attendance Data:', data);
                    if (data.success && data.labels && data.data) {
                        if (chart) chart.destroy();

                        chart = new Chart(canvas, {
                            type: 'line',
                            data: {
                                labels: data.labels,
                                datasets: [{
                                    label: 'Attendance',
                                    data: data.data,
                                    borderColor: 'rgb(147, 51, 234)',
                                    backgroundColor: 'rgba(147, 51, 234, 0.1)',
                                    tension: 0.4,
                                    fill: true,
                                    pointBackgroundColor: 'rgb(147, 51, 234)',
                                    pointBorderColor: 'rgb(147, 51, 234)',
                                    pointRadius: 4,
                                    pointHoverRadius: 6
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: true,
                                        position: 'top'
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                return 'Attendance: ' + context.parsed.y + ' people';
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            stepSize: 1
                                        }
                                    }
                                },
                                elements: {
                                    line: {
                                        tension: 0.4
                                    }
                                }
                            }
                        });

                        console.log('✅ Attendance chart created');
                    }
                    if (loadingDiv) loadingDiv.style.display = 'none';
                })
                .catch(error => {
                    console.error('❌ Attendance chart error:', error);
                    if (loadingDiv) loadingDiv.style.display = 'none';
                });
        };

        // Initial load
        updateChart(dropdown.value);

        // Dropdown change handler
        dropdown.addEventListener('change', () => updateChart(dropdown.value));
    },

    // Initialize other charts (simplified versions)
    initGenderChart: function(canvas) {
        // Gender chart implementation
        console.log('✅ Gender chart initialized');
    },

    initAgeChart: function(canvas) {
        // Age chart implementation
        console.log('✅ Age chart initialized');
    },

    initDepartmentChart: function(canvas) {
        // Department chart implementation
        console.log('✅ Department chart initialized');
    },

    // Load additional dashboard features
    loadDashboardFeatures: function() {
        if (!window.ICGC_CHART_TAKEOVER_ACTIVE) {
            // Load dashboard.js for additional features
            this.loadScript('<?php echo url('assets/js/dashboard.js'); ?>', 'dashboard');
        }
    },

    // Utility function to load scripts
    loadScript: function(src, name) {
        if (this.loadedScripts.has(name)) return;

        const script = document.createElement('script');
        script.src = src;
        script.onload = () => {
            console.log(`✅ ${name} script loaded`);
            this.loadedScripts.add(name);
        };
        script.onerror = () => {
            console.error(`❌ Failed to load ${name} script`);
        };
        document.head.appendChild(script);
    }
};

// Initialize the chart manager with retry mechanism
window.ICGC_DASHBOARD_CHARTS.init();

// Fallback: If charts still don't load after 5 seconds, force retry
setTimeout(function() {
    if (!window.ICGC_DASHBOARD_CHARTS.chartsLoaded) {
        console.log('🔄 Charts not loaded after 5 seconds, forcing retry...');
        window.ICGC_DASHBOARD_CHARTS.chartsLoaded = false;
        window.ICGC_DASHBOARD_CHARTS.checkAndInitCharts();
    }
}, 5000);

// Additional fallback: Force chart initialization on window load
window.addEventListener('load', function() {
    setTimeout(function() {
        if (!window.ICGC_DASHBOARD_CHARTS.chartsLoaded) {
            console.log('🔄 Final fallback: Force initializing charts...');
            window.ICGC_DASHBOARD_CHARTS.chartsLoaded = false;
            window.ICGC_DASHBOARD_CHARTS.domReady = true;
            window.ICGC_DASHBOARD_CHARTS.chartJsReady = (typeof Chart !== 'undefined');
            window.ICGC_DASHBOARD_CHARTS.checkAndInitCharts();
        }
    }, 1000);
});
</script>