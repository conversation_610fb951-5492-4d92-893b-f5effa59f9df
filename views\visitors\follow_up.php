<div class="container mx-auto max-w-4xl">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-1">Add Follow-up</h1>
            <p class="text-sm text-gray-600">Record a follow-up interaction with <?php echo $this->visitor->first_name . ' ' . $this->visitor->last_name; ?></p>
        </div>
        <div>
            <a href="<?php echo url('visitors/' . $this->visitor->id); ?>" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Visitor
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['form_errors'])): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
            <div class="font-bold">Please fix the following errors:</div>
            <ul class="list-disc ml-5">
                <?php foreach ($_SESSION['form_errors'] as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['form_errors']); ?>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md p-6">
                <form action="<?php echo url('visitors/' . $this->visitor->id . '/follow-up'); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="visitor_id" value="<?php echo $this->visitor->id; ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Follow-up Date -->
                        <div>
                            <label for="follow_up_date" class="block text-sm font-medium text-gray-700 mb-1">Follow-up Date <span class="text-red-500">*</span></label>
                            <input type="date" name="follow_up_date" id="follow_up_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required value="<?php echo isset($_SESSION['form_data']['follow_up_date']) ? $_SESSION['form_data']['follow_up_date'] : date('Y-m-d'); ?>">
                        </div>

                        <!-- Follow-up Type -->
                        <div>
                            <label for="follow_up_type" class="block text-sm font-medium text-gray-700 mb-1">Follow-up Type <span class="text-red-500">*</span></label>
                            <select name="follow_up_type" id="follow_up_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required>
                                <option value="">-- Select Type --</option>
                                <option value="phone_call" <?php echo (isset($_SESSION['form_data']['follow_up_type']) && $_SESSION['form_data']['follow_up_type'] === 'phone_call') ? 'selected' : ''; ?>>Phone Call</option>
                                <option value="sms" <?php echo (isset($_SESSION['form_data']['follow_up_type']) && $_SESSION['form_data']['follow_up_type'] === 'sms') ? 'selected' : ''; ?>>SMS</option>
                                <option value="email" <?php echo (isset($_SESSION['form_data']['follow_up_type']) && $_SESSION['form_data']['follow_up_type'] === 'email') ? 'selected' : ''; ?>>Email</option>
                                <option value="home_visit" <?php echo (isset($_SESSION['form_data']['follow_up_type']) && $_SESSION['form_data']['follow_up_type'] === 'home_visit') ? 'selected' : ''; ?>>Home Visit</option>
                                <option value="church_visit" <?php echo (isset($_SESSION['form_data']['follow_up_type']) && $_SESSION['form_data']['follow_up_type'] === 'church_visit') ? 'selected' : ''; ?>>Church Visit</option>
                                <option value="other" <?php echo (isset($_SESSION['form_data']['follow_up_type']) && $_SESSION['form_data']['follow_up_type'] === 'other') ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>

                        <!-- Notes -->
                        <div class="md:col-span-2">
                            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                            <textarea name="notes" id="notes" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"><?php echo isset($_SESSION['form_data']['notes']) ? $_SESSION['form_data']['notes'] : ''; ?></textarea>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-6 rounded-md">
                            Save Follow-up
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b bg-gray-50">
                    <h2 class="text-lg font-semibold text-gray-800">Visitor Information</h2>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
                            <span class="text-gray-500 font-medium"><?php echo substr($this->visitor->first_name, 0, 1) . substr($this->visitor->last_name, 0, 1); ?></span>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900"><?php echo $this->visitor->first_name . ' ' . $this->visitor->last_name; ?></h3>
                            <div class="mt-1">
                                <?php
                                $status_class = '';
                                $status_bg = '';
                                switch ($this->visitor->visitor_status) {
                                    case 'new':
                                        $status_class = 'text-blue-800';
                                        $status_bg = 'bg-blue-100';
                                        break;
                                    case 'in_follow_up':
                                        $status_class = 'text-yellow-800';
                                        $status_bg = 'bg-yellow-100';
                                        break;
                                    case 'converted':
                                        $status_class = 'text-green-800';
                                        $status_bg = 'bg-green-100';
                                        break;
                                    case 'inactive':
                                        $status_class = 'text-gray-800';
                                        $status_bg = 'bg-gray-100';
                                        break;
                                }
                                ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_bg . ' ' . $status_class; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $this->visitor->visitor_status)); ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3 text-sm">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="ml-3 text-gray-900">
                                <?php echo $this->visitor->phone_number; ?>
                            </div>
                        </div>

                        <?php if (!empty($this->visitor->email)): ?>
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="ml-3 text-gray-900">
                                <?php echo $this->visitor->email; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="ml-3 text-gray-900">
                                <span class="font-medium">First Visit:</span> <?php echo date('M d, Y', strtotime($this->visitor->first_visit_date)); ?>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="ml-3 text-gray-900">
                                <span class="font-medium">Heard From:</span> <?php echo ucfirst(str_replace('_', ' ', $this->visitor->how_did_they_hear)); ?>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($this->visitor->address)): ?>
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Address</h4>
                        <p class="text-sm text-gray-600"><?php echo $this->visitor->address; ?></p>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($this->visitor->notes)): ?>
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Notes</h4>
                        <p class="text-sm text-gray-600"><?php echo nl2br($this->visitor->notes); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- SMS Form Section -->
            <div class="mt-4 bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b bg-purple-50">
                    <h2 class="text-lg font-semibold text-gray-800">Send SMS</h2>
                </div>
                <div class="p-4">
                    <form action="<?php echo BASE_URL; ?>visitors/send-sms" method="POST" id="sms-form">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="visitor_id" value="<?php echo $this->visitor->id; ?>">

                        <div class="mb-4">
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message <span class="text-red-500">*</span></label>
                            <textarea name="message" id="message" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required><?php echo isset($_SESSION['form_data']['message']) ? $_SESSION['form_data']['message'] : ''; ?></textarea>
                            <div class="flex justify-between items-center mt-1">
                                <span class="text-xs text-gray-500">Will be sent to: <?php echo $this->visitor->phone_number; ?></span>
                                <span class="text-xs text-gray-500"><span id="char-count">0</span>/1000 characters</span>
                            </div>
                        </div>

                        <div class="flex justify-between items-center">
                            <div>
                                <button type="button" id="template-btn" class="text-sm text-purple-600 hover:text-purple-800">
                                    <i class="fas fa-list mr-1"></i> Use Template
                                </button>
                            </div>
                            <button type="submit" class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-md flex items-center">
                                <i class="fas fa-paper-plane mr-2"></i> Send SMS
                            </button>
                        </div>

                        <!-- Template Selection (Hidden by default) -->
                        <div id="template-container" class="mt-3 hidden">
                            <label for="template" class="block text-sm font-medium text-gray-700 mb-1">Select Template</label>
                            <select id="template" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="">-- Select a template --</option>
                                <option value="Hello, this is <?php echo htmlspecialchars(getChurchName()); ?>. Thank you for visiting us. We hope to see you again soon.">Welcome Message</option>
                                <option value="<?php echo htmlspecialchars(getChurchName()); ?>: We're having a special service this Sunday at 9:00 AM. We'd love to see you there!">Service Invitation</option>
                                <option value="ICGC Emmanuel Temple: Just checking in to see how you're doing. Feel free to reach out if you need anything.">Follow-up Check-in</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>

            <!-- SMS Link removed as requested -->
        </div>
    </div>
</div>

<?php
// Clear form data after displaying
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>

<script>
    // Character counter for SMS message
    const messageField = document.getElementById('message');
    const charCount = document.getElementById('char-count');

    if (messageField && charCount) {
        messageField.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;

            // Change color when approaching limit
            if (count > 1000) {
                charCount.classList.add('text-red-500');
                charCount.classList.remove('text-gray-500');
            } else {
                charCount.classList.remove('text-red-500');
                charCount.classList.add('text-gray-500');
            }
        });

        // Trigger count on page load
        messageField.dispatchEvent(new Event('input'));
    }

    // Template selection functionality
    const templateBtn = document.getElementById('template-btn');
    const templateContainer = document.getElementById('template-container');
    const templateSelect = document.getElementById('template');

    if (templateBtn && templateContainer && templateSelect) {
        // Toggle template container visibility
        templateBtn.addEventListener('click', function() {
            templateContainer.classList.toggle('hidden');

            // Change button text based on visibility
            if (templateContainer.classList.contains('hidden')) {
                this.innerHTML = '<i class="fas fa-list mr-1"></i> Use Template';
            } else {
                this.innerHTML = '<i class="fas fa-times mr-1"></i> Hide Templates';
            }
        });

        // Apply selected template to message field
        templateSelect.addEventListener('change', function() {
            if (this.value && messageField) {
                messageField.value = this.value;
                messageField.dispatchEvent(new Event('input'));
                messageField.focus();
            }
        });
    }

    // Form submission confirmation
    const smsForm = document.getElementById('sms-form');

    if (smsForm) {
        smsForm.addEventListener('submit', function(e) {
            if (!confirm('Are you sure you want to send this SMS?')) {
                e.preventDefault();
                return false;
            }
            return true;
        });
    }
</script>
