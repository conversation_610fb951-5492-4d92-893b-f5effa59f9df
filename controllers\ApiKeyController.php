<?php
/**
 * API Key Controller
 * Manages API keys for secure API access
 */

class ApiKeyController {
    private $database;
    private $apiKey;
    private $user;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->apiKey = new ApiKey($this->database->getConnection());
        $this->user = new User($this->database->getConnection());
        
        // Check if user is logged in
        check_auth();
        
        // Check user status
        check_user_status();
    }
    
    /**
     * List API keys
     */
    public function index() {
        // Check permissions - only admin or above can manage API keys
        if (!check_permission('admin')) {
            set_flash_message('You do not have permission to access API key management.', 'danger');
            redirect('dashboard');
            exit;
        }
        
        // Get user ID
        $user_id = $_SESSION['user_id'];
        
        // Get API keys
        $stmt = $this->apiKey->getByUser($user_id);
        $keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Set page title and active page
        $page_title = 'API Keys - ICGC Emmanuel Temple';
        $active_page = 'settings';
        
        // Start output buffering
        ob_start();
        
        // Include the view
        require_once 'views/api-keys/index.php';
        
        // Get the contents of the output buffer
        $content = ob_get_clean();
        
        // Include the layout template
        include 'views/layouts/main.php';
    }
    
    /**
     * Create API key form
     */
    public function create() {
        // Check permissions - only admin or above can manage API keys
        if (!check_permission('admin')) {
            set_flash_message('You do not have permission to access API key management.', 'danger');
            redirect('dashboard');
            exit;
        }
        
        // Set page title and active page
        $page_title = 'Create API Key - ICGC Emmanuel Temple';
        $active_page = 'settings';
        
        // Start output buffering
        ob_start();
        
        // Include the view
        require_once 'views/api-keys/create.php';
        
        // Get the contents of the output buffer
        $content = ob_get_clean();
        
        // Include the layout template
        include 'views/layouts/main.php';
    }
    
    /**
     * Store new API key
     */
    public function store() {
        // Check permissions - only admin or above can manage API keys
        if (!check_permission('admin')) {
            set_flash_message('You do not have permission to access API key management.', 'danger');
            redirect('dashboard');
            exit;
        }
        
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('api-keys');
            exit;
        }
        
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
            set_flash_message('Invalid form submission.', 'danger');
            redirect('api-keys/create');
            exit;
        }
        
        // Get form data
        $description = sanitize($_POST['description'] ?? '');
        $permissions = isset($_POST['permissions']) && is_array($_POST['permissions']) 
            ? implode(',', array_map('sanitize', $_POST['permissions'])) 
            : 'read';
        
        // Set API key properties
        $this->apiKey->user_id = $_SESSION['user_id'];
        $this->apiKey->description = $description;
        $this->apiKey->permissions = $permissions;
        $this->apiKey->tenant_id = $_SESSION['tenant_id'] ?? null;
        $this->apiKey->status = 'active';
        
        // Create API key
        if ($this->apiKey->create()) {
            // API key is generated in the model
            set_flash_message('API key created successfully. Please copy the key as it will not be shown again.', 'success');
            $_SESSION['new_api_key'] = $this->apiKey->key;
            redirect('api-keys');
        } else {
            set_flash_message('Failed to create API key.', 'danger');
            redirect('api-keys/create');
        }
    }
    
    /**
     * Disable API key
     */
    public function disable($id) {
        // Check permissions - only admin or above can manage API keys
        if (!check_permission('admin')) {
            set_flash_message('You do not have permission to access API key management.', 'danger');
            redirect('dashboard');
            exit;
        }
        
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('api-keys');
            exit;
        }
        
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
            set_flash_message('Invalid form submission.', 'danger');
            redirect('api-keys');
            exit;
        }
        
        // Set API key ID
        $this->apiKey->id = (int)$id;
        
        // Disable API key
        if ($this->apiKey->disable()) {
            set_flash_message('API key disabled successfully.', 'success');
        } else {
            set_flash_message('Failed to disable API key.', 'danger');
        }
        
        redirect('api-keys');
    }
} 