<div class="container mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Attendance Summary</h1>
        <div class="flex space-x-2">
            <a href="<?php echo BASE_URL; ?>attendance" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Attendance
            </a>
            <a href="<?php echo BASE_URL; ?>attendance/bulk" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-users mr-2"></i> Mark More Attendance
            </a>
        </div>
    </div>

    <!-- Success Message -->
    <?php if (isset($_SESSION['success_message'])) : ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
            <p><?php echo $_SESSION['success_message']; ?></p>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <!-- Service and Date Info -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
                <h2 class="text-xl font-semibold text-gray-800"><?php echo $service['name']; ?></h2>
                <p class="text-gray-600 mt-1"><?php echo format_date($date); ?> (<?php echo ucfirst($service['day_of_week']); ?> - <?php echo date('h:i A', strtotime($service['time'])); ?>)</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="<?php echo BASE_URL; ?>attendance/view-by-date?date=<?php echo $date; ?>&service_id=<?php echo $service_id; ?>" class="text-primary hover:text-primary-dark flex items-center">
                    <i class="fas fa-list-alt mr-1"></i> View Detailed Records
                </a>
            </div>
        </div>
    </div>

    <!-- Attendance Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Marked -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Marked</p>
                    <p class="text-2xl font-semibold text-gray-800"><?php echo $total_marked; ?></p>
                </div>
            </div>
            <div class="mt-4">
                <p class="text-sm text-gray-500">
                    <?php echo $coverage_rate; ?>% of active members
                </p>
                <div class="h-2 bg-gray-200 rounded-full mt-1">
                    <div class="h-2 bg-blue-500 rounded-full" style="width: <?php echo $coverage_rate; ?>%"></div>
                </div>
            </div>
        </div>

        <!-- Present -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Present</p>
                    <p class="text-2xl font-semibold text-gray-800"><?php echo count($present_members); ?></p>
                </div>
            </div>
            <div class="mt-4">
                <p class="text-sm text-gray-500">
                    <?php echo $total_marked > 0 ? round(count($present_members) / $total_marked * 100) : 0; ?>% of attendance
                </p>
                <div class="h-2 bg-gray-200 rounded-full mt-1">
                    <div class="h-2 bg-green-500 rounded-full" style="width: <?php echo $total_marked > 0 ? (count($present_members) / $total_marked * 100) : 0; ?>%"></div>
                </div>
            </div>
        </div>

        <!-- Absent -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-500">
                    <i class="fas fa-times-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Absent</p>
                    <p class="text-2xl font-semibold text-gray-800"><?php echo count($absent_members); ?></p>
                </div>
            </div>
            <div class="mt-4">
                <p class="text-sm text-gray-500">
                    <?php echo $total_marked > 0 ? round(count($absent_members) / $total_marked * 100) : 0; ?>% of attendance
                </p>
                <div class="h-2 bg-gray-200 rounded-full mt-1">
                    <div class="h-2 bg-red-500 rounded-full" style="width: <?php echo $total_marked > 0 ? (count($absent_members) / $total_marked * 100) : 0; ?>%"></div>
                </div>
            </div>
        </div>

        <!-- Late -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                    <i class="fas fa-clock text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Late</p>
                    <p class="text-2xl font-semibold text-gray-800"><?php echo count($late_members); ?></p>
                </div>
            </div>
            <div class="mt-4">
                <p class="text-sm text-gray-500">
                    <?php echo $total_marked > 0 ? round(count($late_members) / $total_marked * 100) : 0; ?>% of attendance
                </p>
                <div class="h-2 bg-gray-200 rounded-full mt-1">
                    <div class="h-2 bg-yellow-500 rounded-full" style="width: <?php echo $total_marked > 0 ? (count($late_members) / $total_marked * 100) : 0; ?>%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Statistics -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Department Attendance</h2>
        
        <?php if (empty($department_stats)) : ?>
            <p class="text-gray-500">No department statistics available.</p>
        <?php else : ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Members</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Late</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance Rate</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($department_stats as $dept) : ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo $dept['name']; ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo $dept['total']; ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="text-green-600"><?php echo $dept['present']; ?></span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="text-red-600"><?php echo $dept['absent']; ?></span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="text-yellow-600"><?php echo $dept['late']; ?></span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <span class="mr-2 font-medium <?php echo $dept['rate'] >= 70 ? 'text-green-600' : ($dept['rate'] >= 50 ? 'text-yellow-600' : 'text-red-600'); ?>">
                                            <?php echo $dept['rate']; ?>%
                                        </span>
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="h-2 rounded-full <?php echo $dept['rate'] >= 70 ? 'bg-green-500' : ($dept['rate'] >= 50 ? 'bg-yellow-500' : 'bg-red-500'); ?>" style="width: <?php echo $dept['rate']; ?>%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- Attendance Trends -->
    <?php if (!empty($attendance_trends)) : ?>
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Recent Attendance Trends</h2>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Late</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($attendance_trends as $trend) : ?>
                        <tr class="<?php echo $trend['date'] == $date ? 'bg-blue-50' : ''; ?>">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <?php if ($trend['date'] == $date) : ?>
                                    <span class="font-bold"><?php echo $trend['formatted_date']; ?> (Today)</span>
                                <?php else : ?>
                                    <a href="<?php echo BASE_URL; ?>attendance/summary?date=<?php echo $trend['date']; ?>&service_id=<?php echo $service_id; ?>" class="text-primary hover:text-primary-dark">
                                        <?php echo $trend['formatted_date']; ?>
                                    </a>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo $trend['total']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600"><?php echo $trend['present']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600"><?php echo $trend['absent']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600"><?php echo $trend['late']; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <div class="flex items-center">
                                    <span class="mr-2 font-medium <?php echo $trend['rate'] >= 70 ? 'text-green-600' : ($trend['rate'] >= 50 ? 'text-yellow-600' : 'text-red-600'); ?>">
                                        <?php echo $trend['rate']; ?>%
                                    </span>
                                    <div class="w-24 bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full <?php echo $trend['rate'] >= 70 ? 'bg-green-500' : ($trend['rate'] >= 50 ? 'bg-yellow-500' : 'bg-red-500'); ?>" style="width: <?php echo $trend['rate']; ?>%"></div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>

    <!-- Member Lists -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Present Members -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-green-50 px-6 py-4 border-b border-green-100">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Present Members</h3>
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-semibold"><?php echo count($present_members); ?></span>
                </div>
            </div>
            <div class="p-4">
                <div class="mb-4">
                    <input type="text" id="present-search" placeholder="Search present members..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
                <div class="max-h-96 overflow-y-auto">
                    <?php if (empty($present_members)) : ?>
                        <p class="text-center py-4 text-gray-500">No present members recorded.</p>
                    <?php else : ?>
                        <ul id="present-list" class="divide-y divide-gray-200">
                            <?php foreach ($present_members as $member) : ?>
                                <li class="py-3 flex items-center member-item">
                                    <i class="fas fa-user-check text-green-500 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 member-name"><?php echo $member['first_name'] . ' ' . $member['last_name']; ?></p>
                                        <?php if (!empty($member['department'])) : ?>
                                            <p class="text-xs text-gray-500"><?php echo ucwords(str_replace('_', ' ', $member['department'])); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Absent Members -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-red-50 px-6 py-4 border-b border-red-100">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Absent Members</h3>
                    <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-semibold"><?php echo count($absent_members); ?></span>
                </div>
            </div>
            <div class="p-4">
                <div class="mb-4">
                    <input type="text" id="absent-search" placeholder="Search absent members..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
                <div class="max-h-96 overflow-y-auto">
                    <?php if (empty($absent_members)) : ?>
                        <p class="text-center py-4 text-gray-500">No absent members recorded.</p>
                    <?php else : ?>
                        <ul id="absent-list" class="divide-y divide-gray-200">
                            <?php foreach ($absent_members as $member) : ?>
                                <li class="py-3 flex items-center member-item">
                                    <i class="fas fa-user-times text-red-500 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 member-name"><?php echo $member['first_name'] . ' ' . $member['last_name']; ?></p>
                                        <?php if (!empty($member['department'])) : ?>
                                            <p class="text-xs text-gray-500"><?php echo ucwords(str_replace('_', ' ', $member['department'])); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Late Members -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-yellow-50 px-6 py-4 border-b border-yellow-100">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Late Members</h3>
                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-semibold"><?php echo count($late_members); ?></span>
                </div>
            </div>
            <div class="p-4">
                <div class="mb-4">
                    <input type="text" id="late-search" placeholder="Search late members..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
                <div class="max-h-96 overflow-y-auto">
                    <?php if (empty($late_members)) : ?>
                        <p class="text-center py-4 text-gray-500">No late members recorded.</p>
                    <?php else : ?>
                        <ul id="late-list" class="divide-y divide-gray-200">
                            <?php foreach ($late_members as $member) : ?>
                                <li class="py-3 flex items-center member-item">
                                    <i class="fas fa-user-clock text-yellow-500 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 member-name"><?php echo $member['first_name'] . ' ' . $member['last_name']; ?></p>
                                        <?php if (!empty($member['department'])) : ?>
                                            <p class="text-xs text-gray-500"><?php echo ucwords(str_replace('_', ' ', $member['department'])); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="<?php echo BASE_URL; ?>attendance/edit-bulk?date=<?php echo $date; ?>&service_id=<?php echo $service_id; ?>" class="bg-primary hover:bg-primary-dark text-white py-3 px-4 rounded-md flex items-center justify-center">
                <i class="fas fa-edit mr-2"></i> Edit Attendance
            </a>
            <a href="#" onclick="exportAttendance()" class="bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-4 rounded-md flex items-center justify-center">
                <i class="fas fa-file-export mr-2"></i> Export Data
            </a>
            <a href="<?php echo BASE_URL; ?>reports/attendance?date=<?php echo $date; ?>&service_id=<?php echo $service_id; ?>" class="bg-gray-600 hover:bg-gray-700 text-white py-3 px-4 rounded-md flex items-center justify-center">
                <i class="fas fa-chart-bar mr-2"></i> Detailed Report
            </a>
        </div>
    </div>
</div>

<script>
    // Search functionality for member lists
    document.addEventListener('DOMContentLoaded', function() {
        // Present members search
        document.getElementById('present-search').addEventListener('input', function() {
            filterList('present-list', this.value);
        });
        
        // Absent members search
        document.getElementById('absent-search').addEventListener('input', function() {
            filterList('absent-list', this.value);
        });
        
        // Late members search
        document.getElementById('late-search').addEventListener('input', function() {
            filterList('late-list', this.value);
        });
        
        // Filter function
        function filterList(listId, searchTerm) {
            const list = document.getElementById(listId);
            if (!list) return;
            
            const items = list.getElementsByClassName('member-item');
            const term = searchTerm.toLowerCase();
            
            for (let i = 0; i < items.length; i++) {
                const nameElement = items[i].querySelector('.member-name');
                if (!nameElement) continue;
                
                const name = nameElement.textContent.toLowerCase();
                
                if (name.includes(term)) {
                    items[i].style.display = '';
                } else {
                    items[i].style.display = 'none';
                }
            }
        }
    });
    
    // Export attendance data
    function exportAttendance() {
        // This is a placeholder - in a real implementation, this would trigger a download
        alert('Export functionality would be implemented here. This would generate a CSV or Excel file with the attendance data.');
    }
</script>
