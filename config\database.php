<?php
/**
 * Database Configuration
 */

// Load environment configuration
require_once __DIR__ . '/environment.php';
EnvironmentConfig::load();

// Database constants for backup/restore functionality (backward compatibility)
if (!defined('DB_HOST')) {
    define('DB_HOST', EnvironmentConfig::get('database.host'));
    define('DB_USER', EnvironmentConfig::get('database.username'));
    define('DB_PASS', EnvironmentConfig::get('database.password'));
    define('DB_NAME', EnvironmentConfig::get('database.name'));
}

class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $conn;

    /**
     * Constructor
     */
    public function __construct() {
        $this->host = EnvironmentConfig::get('database.host', 'localhost');
        $this->db_name = EnvironmentConfig::get('database.name', 'icgc_db');
        $this->username = EnvironmentConfig::get('database.username', 'root');
        $this->password = EnvironmentConfig::get('database.password', '');
    }

    /**
     * Get database connection
     *
     * @param array $options Additional PDO options
     * @return PDO
     */
    public function getConnection($options = []) {
        $this->conn = null;

        try {
            // Default PDO options
            $defaultOptions = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false // Disable emulated prepared statements
            ];

            // Merge default options with any provided options
            $pdoOptions = array_merge($defaultOptions, $options);

            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                $pdoOptions
            );
        } catch(PDOException $e) {
            error_log("Database Connection Error: " . $e->getMessage());
            throw new Exception("Database connection failed. Please contact the administrator.");
        }

        return $this->conn;
    }
}
