<?php
/**
 * Equipment Controller
 */

require_once 'models/Equipment.php';
require_once 'models/EquipmentMaintenance.php';
require_once 'utils/validation.php';
require_once 'helpers/functions.php';

class EquipmentController {
    private $database;
    private $equipment;
    private $maintenance;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->equipment = new Equipment($this->database->getConnection());
        $this->maintenance = new EquipmentMaintenance($this->database->getConnection());
    }

    /**
     * Display equipment list
     *
     * @return void
     */
    public function index() {
        // Get all equipment
        $stmt = $this->equipment->getAll();
        $equipments = $stmt->fetchAll();

        // Get equipment statistics
        $stats = [
            'total' => count($equipments),
            'excellent' => 0,
            'good' => 0,
            'fair' => 0,
            'poor' => 0
        ];

        // Calculate condition counts
        foreach ($equipments as $equipment) {
            $stats[$equipment['status']]++;
        }

        // Get maintenance statistics
        $maintenance_stats = $this->maintenance->getStatistics();

        // Set page title and active page
        $page_title = getPageTitle('Equipment');
        $active_page = 'equipment';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display equipment form
     *
     * @return void
     */
    public function create() {
        // Set page title and active page
        $page_title = getPageTitle('Add Equipment');
        $active_page = 'equipment';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/create.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store equipment record
     *
     * @return void
     */
    public function store() {
        try {
            // Check if form is submitted
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // Validate form data
                $errors = [];

                // Required fields
                if (empty($_POST['name'])) {
                    $errors[] = 'Name is required';
                }

                if (empty($_POST['category'])) {
                    $errors[] = 'Category is required';
                }

                // If there are errors, redirect back with errors
                if (!empty($errors)) {
                    $_SESSION['errors'] = $errors;
                    $_SESSION['form_data'] = $_POST;
                    redirect('equipment/add');
                    exit;
                }

                // Set equipment properties
                $this->equipment->name = sanitize($_POST['name']);
                $this->equipment->category = sanitize($_POST['category']);
                $this->equipment->description = sanitize($_POST['description'] ?? '');
                $this->equipment->purchase_date = !empty($_POST['purchase_date']) ? sanitize($_POST['purchase_date']) : null;
                $this->equipment->purchase_price = !empty($_POST['purchase_price']) ? sanitize($_POST['purchase_price']) : null;

                // Handle condition/status field
                if (isset($_POST['status'])) {
                    $this->equipment->status = sanitize($_POST['status']);
                    $this->equipment->equipment_condition = sanitize($_POST['status']); // For backward compatibility
                    $this->equipment->condition = sanitize($_POST['status']); // For backward compatibility
                } else if (isset($_POST['equipment_condition'])) {
                    $this->equipment->status = sanitize($_POST['equipment_condition']);
                    $this->equipment->equipment_condition = sanitize($_POST['equipment_condition']);
                    $this->equipment->condition = sanitize($_POST['equipment_condition']); // For backward compatibility
                } else if (isset($_POST['condition'])) {
                    $this->equipment->status = sanitize($_POST['condition']);
                    $this->equipment->equipment_condition = sanitize($_POST['condition']);
                    $this->equipment->condition = sanitize($_POST['condition']); // For backward compatibility
                } else {
                    $this->equipment->status = 'good'; // Default value
                    $this->equipment->equipment_condition = 'good';
                    $this->equipment->condition = 'good'; // For backward compatibility
                }

                $this->equipment->location = sanitize($_POST['location'] ?? '');
                $this->equipment->created_at = date('Y-m-d H:i:s');
                $this->equipment->updated_at = date('Y-m-d H:i:s');

                // Log for debugging
                error_log("Equipment condition value: " . $this->equipment->equipment_condition);

                // Log for debugging
                error_log("Equipment data: " . json_encode(["name" => $this->equipment->name, "condition" => $this->equipment->equipment_condition]));

                // Create equipment record
                if ($this->equipment->create()) {
                    // Set success message
                    $_SESSION['flash_message'] = 'Equipment added successfully';
                    $_SESSION['flash_type'] = 'success';

                    // Use direct header redirect to avoid any issues
                    header('Location: ' . BASE_URL . 'equipment');
                    exit;
                } else {
                    // Set error message
                    $_SESSION['flash_message'] = 'Failed to add equipment';
                    $_SESSION['flash_type'] = 'danger';

                    // Use direct header redirect
                    header('Location: ' . BASE_URL . 'equipment/add');
                    exit;
                }
            } else {
                // Not a POST request, redirect to equipment form
                redirect('equipment/add');
                exit;
            }
        } catch (Exception $e) {
            // Log the error
            error_log('Error in EquipmentController::store(): ' . $e->getMessage());

            // Set error message
            $_SESSION['flash_message'] = 'An error occurred while processing your request';
            $_SESSION['flash_type'] = 'danger';

            // Redirect back to the form
            header('Location: ' . BASE_URL . 'equipment/add');
            exit;
        }
    }

    /**
     * Display equipment details
     *
     * @return void
     */
    public function view() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Equipment ID is required', 'danger');
            redirect('equipment');
            exit;
        }

        $id = sanitize($_GET['id']);

        // Get equipment record
        if (!$this->equipment->getById($id)) {
            set_flash_message('Equipment not found', 'danger');
            redirect('equipment');
            exit;
        }

        // Get maintenance records for this equipment
        $stmt = $this->maintenance->getByEquipmentId($id);
        $maintenance_records = $stmt->fetchAll();

        // Calculate total maintenance cost
        $total_maintenance_cost = $this->maintenance->getTotalCostByEquipmentId($id);

        // Set page title and active page
        $page_title = 'View Equipment - ICGC Emmanuel Temple';
        $active_page = 'equipment';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/view.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display edit equipment form
     *
     * @return void
     */
    public function edit() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Equipment ID is required', 'danger');
            redirect('equipment');
            exit;
        }

        $id = sanitize($_GET['id']);

        // Get equipment record
        if (!$this->equipment->getById($id)) {
            set_flash_message('Equipment not found', 'danger');
            redirect('equipment');
            exit;
        }

        // Set page title and active page
        $page_title = 'Edit Equipment - ICGC Emmanuel Temple';
        $active_page = 'equipment';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/edit.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Update equipment record
     *
     * @return void
     */
    public function update() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Check if ID is provided
            if (!isset($_POST['id']) || empty($_POST['id'])) {
                set_flash_message('Equipment ID is required', 'danger');
                redirect('equipment');
                exit;
            }

            // Validate form data
            $errors = [];

            // Required fields
            if (empty($_POST['name'])) {
                $errors[] = 'Name is required';
            }

            if (empty($_POST['category'])) {
                $errors[] = 'Category is required';
            }

            // If there are errors, redirect back with errors
            if (!empty($errors)) {
                $_SESSION['errors'] = $errors;
                $_SESSION['form_data'] = $_POST;
                redirect('equipment/edit?id=' . $_POST['id']);
                exit;
            }

            // Get equipment record
            if (!$this->equipment->getById($_POST['id'])) {
                set_flash_message('Equipment not found', 'danger');
                redirect('equipment');
                exit;
            }

            // Set equipment properties
            $this->equipment->name = sanitize($_POST['name']);
            $this->equipment->category = sanitize($_POST['category']);
            $this->equipment->description = sanitize($_POST['description'] ?? '');
            $this->equipment->purchase_date = !empty($_POST['purchase_date']) ? sanitize($_POST['purchase_date']) : null;
            $this->equipment->purchase_price = !empty($_POST['purchase_price']) ? sanitize($_POST['purchase_price']) : null;

            // Handle condition/status field
            if (isset($_POST['status'])) {
                $this->equipment->status = sanitize($_POST['status']);
                $this->equipment->equipment_condition = sanitize($_POST['status']); // For backward compatibility
                $this->equipment->condition = sanitize($_POST['status']); // For backward compatibility
            } else if (isset($_POST['equipment_condition'])) {
                $this->equipment->status = sanitize($_POST['equipment_condition']);
                $this->equipment->equipment_condition = sanitize($_POST['equipment_condition']);
                $this->equipment->condition = sanitize($_POST['equipment_condition']); // For backward compatibility
            } else if (isset($_POST['condition'])) {
                $this->equipment->status = sanitize($_POST['condition']);
                $this->equipment->equipment_condition = sanitize($_POST['condition']);
                $this->equipment->condition = sanitize($_POST['condition']); // For backward compatibility
            } else {
                $this->equipment->status = 'good'; // Default value
                $this->equipment->equipment_condition = 'good';
                $this->equipment->condition = 'good'; // For backward compatibility
            }

            $this->equipment->location = sanitize($_POST['location'] ?? '');
            $this->equipment->updated_at = date('Y-m-d H:i:s');

            // Log for debugging
            error_log("Equipment update condition value: " . $this->equipment->equipment_condition);

            // Update equipment record
            if ($this->equipment->update()) {
                // Set success message
                set_flash_message('Equipment updated successfully', 'success');
                redirect('equipment/view?id=' . $_POST['id']);
            } else {
                // Set error message
                set_flash_message('Failed to update equipment', 'danger');
                redirect('equipment/edit?id=' . $_POST['id']);
            }
        } else {
            // Not a POST request, redirect to equipment list
            redirect('equipment');
        }
    }

    /**
     * Delete equipment record
     *
     * @return void
     */
    public function delete() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Equipment ID is required', 'danger');
            redirect('equipment');
            exit;
        }

        $id = sanitize($_GET['id']);

        // Get equipment record
        if (!$this->equipment->getById($id)) {
            set_flash_message('Equipment not found', 'danger');
            redirect('equipment');
            exit;
        }

        // Delete equipment record
        if ($this->equipment->delete()) {
            // Set success message
            set_flash_message('Equipment deleted successfully', 'success');
        } else {
            // Set error message
            set_flash_message('Failed to delete equipment', 'danger');
        }

        redirect('equipment');
    }

    /**
     * Display maintenance list
     *
     * @return void
     */
    public function maintenance() {
        // Get all maintenance records
        $stmt = $this->maintenance->getAll();
        $maintenance_records = $stmt->fetchAll();

        // Get maintenance statistics
        $stats = $this->maintenance->getStatistics();

        // Get cost by month for current year
        $cost_by_month = $this->maintenance->getCostByMonth();

        // Format data for chart
        $months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $chart_data = array_fill(0, 12, 0);

        foreach ($cost_by_month as $item) {
            $month_index = (int)$item['month'] - 1; // Convert to 0-based index
            $chart_data[$month_index] = (float)$item['total_cost'];
        }

        // Set page title and active page
        $page_title = getPageTitle('Equipment Maintenance');
        $active_page = 'equipment';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/maintenance.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display maintenance form
     *
     * @return void
     */
    public function createMaintenance() {
        // Get all equipment for selection
        $stmt = $this->equipment->getAll();
        $equipments = $stmt->fetchAll();

        // Check if equipment_id is provided in URL
        $selected_equipment_id = isset($_GET['equipment_id']) ? sanitize($_GET['equipment_id']) : null;

        // Set page title and active page
        $page_title = getPageTitle('Add Maintenance Record');
        $active_page = 'equipment';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/maintenance_form.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store maintenance record
     *
     * @return void
     */
    public function storeMaintenance() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate form data
            $errors = [];

            // Required fields
            if (empty($_POST['equipment_id'])) {
                $errors[] = 'Equipment is required';
            }

            if (empty($_POST['maintenance_date'])) {
                $errors[] = 'Maintenance date is required';
            }

            if (empty($_POST['description'])) {
                $errors[] = 'Description is required';
            }

            // If there are errors, redirect back with errors
            if (!empty($errors)) {
                $_SESSION['errors'] = $errors;
                $_SESSION['form_data'] = $_POST;
                redirect('equipment/maintenance/add');
                exit;
            }

            // Set maintenance properties
            $this->maintenance->equipment_id = sanitize($_POST['equipment_id']);
            $this->maintenance->maintenance_date = sanitize($_POST['maintenance_date']);
            $this->maintenance->description = sanitize($_POST['description']);
            $this->maintenance->cost = !empty($_POST['cost']) ? sanitize($_POST['cost']) : null;
            $this->maintenance->performed_by = sanitize($_POST['performed_by'] ?? '');
            $this->maintenance->created_at = date('Y-m-d H:i:s');
            $this->maintenance->updated_at = date('Y-m-d H:i:s');

            // Create maintenance record
            if ($this->maintenance->create()) {
                // Set success message
                set_flash_message('Maintenance record added successfully', 'success');

                // Redirect to equipment view if equipment_id is provided
                if (isset($_POST['redirect_to_equipment']) && $_POST['redirect_to_equipment'] == '1') {
                    redirect('equipment/view?id=' . $_POST['equipment_id']);
                } else {
                    redirect('equipment/maintenance');
                }
            } else {
                // Set error message
                set_flash_message('Failed to add maintenance record', 'danger');
                redirect('equipment/maintenance/add');
            }
        } else {
            // Not a POST request, redirect to maintenance form
            redirect('equipment/maintenance/add');
        }
    }

    /**
     * Display edit maintenance form
     *
     * @return void
     */
    public function editMaintenance() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Maintenance ID is required', 'danger');
            redirect('equipment/maintenance');
            exit;
        }

        $id = sanitize($_GET['id']);

        // Get maintenance record
        if (!$this->maintenance->getById($id)) {
            set_flash_message('Maintenance record not found', 'danger');
            redirect('equipment/maintenance');
            exit;
        }

        // Get all equipment for selection
        $stmt = $this->equipment->getAll();
        $equipments = $stmt->fetchAll();

        // Set page title and active page
        $page_title = 'Edit Maintenance Record - ICGC Emmanuel Temple';
        $active_page = 'equipment';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/maintenance_edit.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Update maintenance record
     *
     * @return void
     */
    public function updateMaintenance() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Check if ID is provided
            if (!isset($_POST['id']) || empty($_POST['id'])) {
                set_flash_message('Maintenance ID is required', 'danger');
                redirect('equipment/maintenance');
                exit;
            }

            // Validate form data
            $errors = [];

            // Required fields
            if (empty($_POST['equipment_id'])) {
                $errors[] = 'Equipment is required';
            }

            if (empty($_POST['maintenance_date'])) {
                $errors[] = 'Maintenance date is required';
            }

            if (empty($_POST['description'])) {
                $errors[] = 'Description is required';
            }

            // If there are errors, redirect back with errors
            if (!empty($errors)) {
                $_SESSION['errors'] = $errors;
                $_SESSION['form_data'] = $_POST;
                redirect('equipment/maintenance/edit?id=' . $_POST['id']);
                exit;
            }

            // Get maintenance record
            if (!$this->maintenance->getById($_POST['id'])) {
                set_flash_message('Maintenance record not found', 'danger');
                redirect('equipment/maintenance');
                exit;
            }

            // Set maintenance properties
            $this->maintenance->equipment_id = sanitize($_POST['equipment_id']);
            $this->maintenance->maintenance_date = sanitize($_POST['maintenance_date']);
            $this->maintenance->description = sanitize($_POST['description']);
            $this->maintenance->cost = !empty($_POST['cost']) ? sanitize($_POST['cost']) : null;
            $this->maintenance->performed_by = sanitize($_POST['performed_by'] ?? '');
            $this->maintenance->updated_at = date('Y-m-d H:i:s');

            // Update maintenance record
            if ($this->maintenance->update()) {
                // Set success message
                set_flash_message('Maintenance record updated successfully', 'success');

                // Redirect to equipment view if redirect_to_equipment is set
                if (isset($_POST['redirect_to_equipment']) && $_POST['redirect_to_equipment'] == '1') {
                    redirect('equipment/view?id=' . $_POST['equipment_id']);
                } else {
                    redirect('equipment/maintenance');
                }
            } else {
                // Set error message
                set_flash_message('Failed to update maintenance record', 'danger');
                redirect('equipment/maintenance/edit?id=' . $_POST['id']);
            }
        } else {
            // Not a POST request, redirect to maintenance list
            redirect('equipment/maintenance');
        }
    }

    /**
     * Delete maintenance record
     *
     * @return void
     */
    public function deleteMaintenance() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Maintenance ID is required', 'danger');
            redirect('equipment/maintenance');
            exit;
        }

        $id = sanitize($_GET['id']);

        // Get maintenance record
        if (!$this->maintenance->getById($id)) {
            set_flash_message('Maintenance record not found', 'danger');
            redirect('equipment/maintenance');
            exit;
        }

        // Store equipment_id for potential redirect
        $equipment_id = $this->maintenance->equipment_id;
        $redirect_to_equipment = isset($_GET['redirect']) && $_GET['redirect'] == 'equipment';

        // Delete maintenance record
        if ($this->maintenance->delete()) {
            // Set success message
            set_flash_message('Maintenance record deleted successfully', 'success');
        } else {
            // Set error message
            set_flash_message('Failed to delete maintenance record', 'danger');
        }

        // Redirect based on context
        if ($redirect_to_equipment) {
            redirect('equipment/view?id=' . $equipment_id);
        } else {
            redirect('equipment/maintenance');
        }
    }

    /**
     * Generate QR code for equipment
     *
     * @return void
     */
    public function generateQrCode() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Equipment ID is required', 'danger');
            redirect('equipment');
            exit;
        }

        $id = sanitize($_GET['id']);

        // Get equipment record
        if (!$this->equipment->getById($id)) {
            set_flash_message('Equipment not found', 'danger');
            redirect('equipment');
            exit;
        }

        // Set page title and active page
        $page_title = 'QR Code for ' . $this->equipment->name . ' - ICGC Emmanuel Temple';
        $active_page = 'equipment';

        // Generate QR code URL
        $equipment_url = BASE_URL . 'equipment/view?id=' . $this->equipment->id;

        // Use QR Server API to generate QR code directly
        $qr_url = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($equipment_url);

        // Get equipment condition
        $condition = isset($this->equipment->equipment_condition) ?
                    $this->equipment->equipment_condition :
                    $this->equipment->condition;

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/qrcode_new.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display equipment inventory
     *
     * @return void
     */
    public function inventory() {
        // Get all equipment
        $stmt = $this->equipment->getAll();
        $equipments = $stmt->fetchAll();

        // Set page title and active page
        $page_title = getPageTitle('Equipment Inventory');
        $active_page = 'equipment';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/inventory.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display equipment reports
     *
     * @return void
     */
    public function reports() {
        // Get equipment statistics
        $equipment_stats = [
            'total' => 0,
            'by_category' => [],
            'by_condition' => [
                'excellent' => 0,
                'good' => 0,
                'fair' => 0,
                'poor' => 0
            ],
            'total_value' => 0
        ];

        // Get all equipment
        $stmt = $this->equipment->getAll();
        $equipments = $stmt->fetchAll();

        $equipment_stats['total'] = count($equipments);

        // Process equipment data
        foreach ($equipments as $equipment) {
            // Count by category
            if (!isset($equipment_stats['by_category'][$equipment['category']])) {
                $equipment_stats['by_category'][$equipment['category']] = 0;
            }
            $equipment_stats['by_category'][$equipment['category']]++;

            // Count by condition
            $equipment_stats['by_condition'][$equipment['status']]++;

            // Sum total value
            if (!empty($equipment['purchase_price'])) {
                $equipment_stats['total_value'] += $equipment['purchase_price'];
            }
        }

        // Get maintenance statistics
        $maintenance_stats = $this->maintenance->getStatistics();

        // Get cost by month for current year
        $cost_by_month = $this->maintenance->getCostByMonth();

        // Format data for chart
        $months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $chart_data = array_fill(0, 12, 0);

        foreach ($cost_by_month as $item) {
            $month_index = (int)$item['month'] - 1; // Convert to 0-based index
            $chart_data[$month_index] = (float)$item['total_cost'];
        }

        // Set page title and active page
        $page_title = 'Equipment Reports - ICGC Emmanuel Temple';
        $active_page = 'equipment';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/equipment/reports.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }
}
