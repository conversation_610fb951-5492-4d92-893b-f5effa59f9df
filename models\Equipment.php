<?php
/**
 * Equipment Model
 */

class Equipment {
    // Database connection and table name
    private $conn;
    private $table_name = "equipment";

    // Object properties
    public $id;
    public $name;
    public $category;
    public $description;
    public $purchase_date;
    public $purchase_price;
    public $equipment_condition; // Old field
    public $condition; // For backward compatibility
    public $status; // New field for condition
    public $location;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all equipment
     *
     * @return PDOStatement
     */
    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY name ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get equipment by ID
     *
     * @param int $id
     * @return bool
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            $this->id = $row['id'];
            $this->name = $row['name'];
            $this->category = $row['category'];
            $this->description = $row['description'];
            $this->purchase_date = $row['purchase_date'];
            $this->purchase_price = $row['purchase_price'];

            // Handle condition/status fields
            if (isset($row['status'])) {
                $this->status = $row['status'];
                $this->condition = $row['status']; // For backward compatibility
                $this->equipment_condition = $row['status']; // For backward compatibility
            } else if (isset($row['condition'])) {
                $this->condition = $row['condition'];
                $this->status = $row['condition'];
                $this->equipment_condition = $row['condition'];
            } else if (isset($row['equipment_condition'])) {
                $this->equipment_condition = $row['equipment_condition'];
                $this->condition = $row['equipment_condition'];
                $this->status = $row['equipment_condition'];
            }

            $this->location = $row['location'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];

            return true;
        }

        return false;
    }

    /**
     * Create equipment
     *
     * @return bool
     */
    public function create() {
        try {
            // Build the query with the status field
            $query = "INSERT INTO " . $this->table_name . "
                      (name, category, description, purchase_date, purchase_price,
                       status, equipment_condition, location, created_at, updated_at)
                      VALUES
                      (:name, :category, :description, :purchase_date, :purchase_price,
                       :status, :equipment_condition, :location, :created_at, :updated_at)";

            // Log the query for debugging
            error_log("Equipment create query: " . $query);

            $stmt = $this->conn->prepare($query);

            // Sanitize inputs
            $this->name = htmlspecialchars(strip_tags($this->name));
            $this->category = htmlspecialchars(strip_tags($this->category));
            $this->description = htmlspecialchars(strip_tags($this->description));

            // Handle condition/status fields
            if (isset($this->status)) {
                $this->status = htmlspecialchars(strip_tags($this->status));
                $this->equipment_condition = $this->status; // For backward compatibility
            } else if (isset($this->condition)) {
                $this->status = htmlspecialchars(strip_tags($this->condition));
                $this->equipment_condition = $this->status;
            } else if (isset($this->equipment_condition)) {
                $this->status = htmlspecialchars(strip_tags($this->equipment_condition));
            } else {
                $this->status = 'good'; // Default value
                $this->equipment_condition = 'good';
            }

            $this->location = htmlspecialchars(strip_tags($this->location));
            $this->created_at = htmlspecialchars(strip_tags($this->created_at));
            $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

            // Log values for debugging
            error_log("Equipment values: Name={$this->name}, Category={$this->category}, Status={$this->status}, Equipment Condition={$this->equipment_condition}");

            // Log values for debugging
            error_log("Equipment values: Name={$this->name}, Category={$this->category}, Condition={$this->equipment_condition}");

            // Bind parameters
            $stmt->bindParam(':name', $this->name);
            $stmt->bindParam(':category', $this->category);
            $stmt->bindParam(':description', $this->description);
            $stmt->bindParam(':purchase_date', $this->purchase_date);
            $stmt->bindParam(':purchase_price', $this->purchase_price);
            $stmt->bindParam(':status', $this->status);
            $stmt->bindParam(':equipment_condition', $this->equipment_condition);
            $stmt->bindParam(':location', $this->location);
            $stmt->bindParam(':created_at', $this->created_at);
            $stmt->bindParam(':updated_at', $this->updated_at);

            // Execute query
            $result = $stmt->execute();

            if ($result) {
                return true;
            }

            // Log error if execution fails
            $errorInfo = $stmt->errorInfo();
            error_log("Equipment creation failed: " . implode(", ", $errorInfo));
            return false;
        } catch (PDOException $e) {
            // Log the error
            error_log("Database error in Equipment::create(): " . $e->getMessage());
            return false;
        } catch (Exception $e) {
            // Log any other errors
            error_log("General error in Equipment::create(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update equipment
     *
     * @return bool
     */
    public function update() {
        try {
            // Build the query with the status field
            $query = "UPDATE " . $this->table_name . "
                  SET name = :name,
                      category = :category,
                      description = :description,
                      purchase_date = :purchase_date,
                      purchase_price = :purchase_price,
                      status = :status,
                      equipment_condition = :equipment_condition,
                      location = :location,
                      updated_at = :updated_at
                  WHERE id = :id";

            // Log the query for debugging
            error_log("Equipment update query: " . $query);

            $stmt = $this->conn->prepare($query);

            // Sanitize inputs
            $this->id = htmlspecialchars(strip_tags($this->id));
            $this->name = htmlspecialchars(strip_tags($this->name));
            $this->category = htmlspecialchars(strip_tags($this->category));
            $this->description = htmlspecialchars(strip_tags($this->description));

            // Handle condition/status fields
            if (isset($this->status)) {
                $this->status = htmlspecialchars(strip_tags($this->status));
                $this->equipment_condition = $this->status; // For backward compatibility
            } else if (isset($this->condition)) {
                $this->status = htmlspecialchars(strip_tags($this->condition));
                $this->equipment_condition = $this->status;
            } else if (isset($this->equipment_condition)) {
                $this->status = htmlspecialchars(strip_tags($this->equipment_condition));
            } else {
                $this->status = 'good'; // Default value
                $this->equipment_condition = 'good';
            }

            $this->location = htmlspecialchars(strip_tags($this->location));
            $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

            // Log values for debugging
            error_log("Equipment update values: ID={$this->id}, Name={$this->name}, Status={$this->status}, Equipment Condition={$this->equipment_condition}");

            // Bind parameters
            $stmt->bindParam(':id', $this->id);
            $stmt->bindParam(':name', $this->name);
            $stmt->bindParam(':category', $this->category);
            $stmt->bindParam(':description', $this->description);
            $stmt->bindParam(':purchase_date', $this->purchase_date);
            $stmt->bindParam(':purchase_price', $this->purchase_price);
            $stmt->bindParam(':status', $this->status);
            $stmt->bindParam(':equipment_condition', $this->equipment_condition);
            $stmt->bindParam(':location', $this->location);
            $stmt->bindParam(':updated_at', $this->updated_at);

            // Execute query
            $result = $stmt->execute();

            if ($result) {
                return true;
            }

            // Log error if execution fails
            $errorInfo = $stmt->errorInfo();
            error_log("Equipment update failed: " . implode(", ", $errorInfo));
            return false;
        } catch (PDOException $e) {
            // Log the error
            error_log("Database error in Equipment::update(): " . $e->getMessage());
            return false;
        } catch (Exception $e) {
            // Log any other errors
            error_log("General error in Equipment::update(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete equipment
     *
     * @return bool
     */
    public function delete() {
        try {
            $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

            $stmt = $this->conn->prepare($query);

            // Sanitize input
            $this->id = htmlspecialchars(strip_tags($this->id));

            // Bind parameter
            $stmt->bindParam(':id', $this->id);

            // Execute query
            if ($stmt->execute()) {
                return true;
            }

            // Log error if execution fails
            error_log("Equipment deletion failed: " . implode(", ", $stmt->errorInfo()));
            return false;
        } catch (PDOException $e) {
            // Log the error
            error_log("Database error in Equipment::delete(): " . $e->getMessage());
            return false;
        } catch (Exception $e) {
            // Log any other errors
            error_log("General error in Equipment::delete(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get equipment by category
     *
     * @param string $category
     * @return PDOStatement
     */
    public function getByCategory($category) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE category = :category ORDER BY name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':category', $category);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get equipment by condition
     *
     * @param string $condition
     * @return PDOStatement
     */
    public function getByCondition($condition) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE `condition` = :condition ORDER BY name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':condition', $condition);
        $stmt->execute();

        return $stmt;
    }
}
