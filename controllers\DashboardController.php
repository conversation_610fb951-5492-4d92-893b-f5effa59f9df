<?php
/**
 * Dashboard Controller
 */

require_once 'config/database.php';
require_once 'models/Member.php';
require_once 'models/Attendance.php';
require_once 'models/Finance.php';
require_once 'models/Setting.php';
require_once 'utils/validation.php';

class DashboardController {
    private $database;
    private $member;
    private $attendance;
    private $finance;
    private $setting;

    /**
     * Constructor
     */
    public function __construct() {
        try {
            $this->database = new Database();
            $this->member = new Member($this->database->getConnection());
            $this->attendance = new Attendance($this->database->getConnection());
            $this->finance = new Finance($this->database->getConnection());
            $this->setting = new Setting($this->database->getConnection());
        } catch (Exception $e) {
            error_log("DashboardController constructor error: " . $e->getMessage());
            // Initialize with null to prevent further errors
            $this->setting = null;
        }
    }

    /**
     * Display dashboard
     *
     * @return void
     */
    public function index() {
        $this->showDashboard();
    }

    // Removed platform dashboard - not needed for single tenant

    /**
     * Show dashboard
     *
     * @return void
     */
    private function showDashboard() {
        // Check for cached dashboard data (5 minutes cache)
        $cache_key = 'dashboard_data';
        $cache_lifetime = 5 * 60; // 5 minutes
        $cached_data = isset($_SESSION[$cache_key]) ? $_SESSION[$cache_key] : null;
        $cached_time = isset($_SESSION[$cache_key . '_time']) ? $_SESSION[$cache_key . '_time'] : 0;

        if ($cached_data && (time() - $cached_time < $cache_lifetime)) {
            // Use cached data
            extract($cached_data);
        } else {
            // Generate fresh data
            $dashboard_data = $this->generateDashboardData();

            // Cache the data
            $_SESSION[$cache_key] = $dashboard_data;
            $_SESSION[$cache_key . '_time'] = time();

            // Extract variables for use
            extract($dashboard_data);
        }



        // Set page title and active page
        $page_title = getPageTitle('Dashboard');
        $active_page = 'dashboard';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/dashboard/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Generate all dashboard data (for caching)
     *
     * @return array
     */
    private function generateDashboardData() {
        // Get all member statistics in one optimized query
        $member_stats = $this->getMemberStatistics();
        $total_members = $member_stats['total_members'];
        $male_members = $member_stats['male_members'];
        $female_members = $member_stats['female_members'];
        $active_members = $member_stats['active_members'];
        $inactive_members = $member_stats['inactive_members'];
        $new_members = $member_stats['new_members'];
        $total_children = $member_stats['total_children'];

        // Get members by department
        $members_by_department = [];
        try {
            $members_by_department = $this->member->getCountByDepartment();
        } catch (Exception $e) {
            // Handle error
        }

        // Get recent attendance
        $recent_attendance = [];
        try {
            $recent_attendance = $this->attendance->getRecent(5);
        } catch (Exception $e) {
            // Handle error
        }

        // Get attendance stats
        $attendance_stats = [];
        try {
            $attendance_stats = $this->attendance->getStats();
        } catch (Exception $e) {
            // Handle error
        }

        // Get financial summary
        $total_income = 0;
        $total_expenses = 0;
        try {
            $total_income = $this->finance->getTotalIncome();
            $total_expenses = $this->finance->getTotalExpenses();
        } catch (Exception $e) {
            // Handle error
        }
        $balance = $total_income - $total_expenses;

        // Get recent transactions
        $recent_transactions = [];
        try {
            $recent_transactions = $this->finance->getRecent(5);
        } catch (Exception $e) {
            // Handle error
        }

        // Get income by category
        $income_by_category = [];
        try {
            $income_by_category = $this->finance->getIncomeByCategory();
        } catch (Exception $e) {
            // Handle error
        }

        // No upcoming events (events feature removed)
        $upcoming_events = [];

        // Get birthdays this month
        $birthdays_this_month = [];
        try {
            $birthdays_this_month = $this->member->getBirthdaysThisMonth();
        } catch (Exception $e) {
            // Handle error
        }

        // Get birthdays this week
        $birthdays_this_week = [];
        try {
            $birthdays_this_week = $this->member->getBirthdaysThisWeek();
        } catch (Exception $e) {
            // Handle error
        }

        // Get recent activities
        $recent_activities = [];
        try {
            $recent_activities = $this->getRecentActivities();
        } catch (Exception $e) {
            // Handle error
        }

        // Get members absent for 3 consecutive QR sessions
        $absent_members_3_sundays = [];
        $total_absent_members_3_sundays = 0;
        try {
            $qr_absent_data = $this->getQrAbsentMembers(5); // Get first 5 for dashboard
            $absent_members_3_sundays = $qr_absent_data['members'];
            $total_absent_members_3_sundays = $qr_absent_data['total'];
        } catch (Exception $e) {
            // Handle error
        }

        // Calculate financial balance
        $financial_balance = $balance;

        // Calculate gender percentages
        $gender_percentage = [
            'male' => $total_members > 0 ? round(($male_members / $total_members) * 100) : 0,
            'female' => $total_members > 0 ? round(($female_members / $total_members) * 100) : 0
        ];

        // Calculate actual age percentages from database
        $age_percentage = $this->calculateAgePercentages();

        // Set upcoming events list (empty as events feature is removed)
        $upcoming_events_list = [];

        // Set department counts (convert from members_by_department)
        $department_counts = [];

        // Get department display names (reuse existing connection)
        $conn = $this->database->getConnection();
        $stmt = $conn->prepare("SELECT name, display_name FROM departments WHERE is_active = 1");
        $stmt->execute();
        $dept_names_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $department_names = [];
        foreach ($dept_names_data as $dept) {
            $department_names[$dept['name']] = $dept['display_name'];
        }

        // Convert department data with proper names and filter out empty departments
        foreach ($members_by_department as $dept) {
            $dept_key = $dept['department'];
            $dept_name = $department_names[$dept_key] ?? ucfirst(str_replace('_', ' ', $dept_key));
            $count = (int)$dept['count'];

            // Only include departments with members
            if ($count > 0) {
                $department_counts[$dept_name] = $count;
            }
        }

        // Sort departments by count (descending)
        arsort($department_counts);

        // Return all data as array
        return compact(
            'total_members', 'male_members', 'female_members', 'active_members', 'inactive_members',
            'new_members', 'total_children', 'members_by_department', 'recent_attendance',
            'attendance_stats', 'total_income', 'total_expenses', 'balance', 'recent_transactions',
            'income_by_category', 'upcoming_events', 'birthdays_this_month', 'birthdays_this_week',
            'recent_activities', 'absent_members_3_sundays', 'total_absent_members_3_sundays',
            'financial_balance', 'gender_percentage', 'age_percentage', 'upcoming_events_list',
            'department_counts'
        );
    }

    /**
     * Clear dashboard cache (call this when data changes)
     *
     * @return void
     */
    public function clearCache() {
        unset($_SESSION['dashboard_data']);
        unset($_SESSION['dashboard_data_time']);
    }

    /**
     * Get recent activities from various sources
     *
     * @return array
     */
    private function getRecentActivities() {
        $activities = [];

        try {
            // Get recent member registrations (last 7 days)
            $recent_members = $this->getRecentMembers();
            foreach ($recent_members as $member) {
                $activities[] = [
                    'type' => 'member_registration',
                    'title' => 'New member registered',
                    'description' => $member['first_name'] . ' ' . $member['last_name'],
                    'timestamp' => $member['created_at'],
                    'icon' => 'fas fa-user-plus',
                    'color' => 'blue'
                ];
            }

            // Get recent financial transactions (last 7 days)
            $recent_finances = $this->getRecentFinances();
            foreach ($recent_finances as $finance) {
                $activities[] = [
                    'type' => 'financial_transaction',
                    'title' => ucfirst($finance['category']) . ' recorded',
                    'description' => 'GH₵ ' . number_format($finance['amount'], 2) . ' - ' . $finance['description'],
                    'timestamp' => $finance['created_at'],
                    'icon' => $finance['category'] === 'expense' ? 'fas fa-money-bill-wave' : 'fas fa-hand-holding-usd',
                    'color' => $finance['category'] === 'expense' ? 'red' : 'green'
                ];
            }

            // Get recent attendance records (last 7 days)
            $recent_attendance = $this->getRecentAttendanceActivities();
            foreach ($recent_attendance as $attendance) {
                $activities[] = [
                    'type' => 'attendance_record',
                    'title' => 'Attendance recorded',
                    'description' => $attendance['service_name'] . ' - ' . $attendance['total_present'] . ' present',
                    'timestamp' => $attendance['created_at'],
                    'icon' => 'fas fa-calendar-check',
                    'color' => 'purple'
                ];
            }

            // Sort activities by timestamp (most recent first)
            usort($activities, function($a, $b) {
                return strtotime($b['timestamp']) - strtotime($a['timestamp']);
            });

            // Return only the 5 most recent activities
            return array_slice($activities, 0, 5);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Get recent member registrations
     *
     * @return array
     */
    private function getRecentMembers() {
        $query = "SELECT first_name, last_name, created_at
                  FROM members
                  WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                  ORDER BY created_at DESC
                  LIMIT 10";

        $stmt = $this->database->getConnection()->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get recent financial transactions
     *
     * @return array
     */
    private function getRecentFinances() {
        $query = "SELECT category, amount, description, created_at
                  FROM finances
                  WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                  ORDER BY created_at DESC
                  LIMIT 10";

        $stmt = $this->database->getConnection()->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get recent attendance activities
     *
     * @return array
     */
    private function getRecentAttendanceActivities() {
        $query = "SELECT s.name as service_name,
                         COUNT(a.id) as total_present,
                         MAX(a.created_at) as created_at
                  FROM attendance a
                  JOIN services s ON a.service_id = s.id
                  WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                  AND a.status IN ('present', 'late')
                  GROUP BY a.service_id, a.attendance_date
                  ORDER BY created_at DESC
                  LIMIT 5";

        $stmt = $this->database->getConnection()->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get total count of children (members under 18)
     *
     * @return int
     */
    private function getChildrenCount() {
        $conn = $this->database->getConnection();

        $query = "SELECT COUNT(*) as count FROM members
                  WHERE TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) <= 17
                  AND member_status = 'active'
                  AND date_of_birth IS NOT NULL";

        $stmt = $conn->prepare($query);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    }

    /**
     * Calculate actual age percentages from database
     *
     * @return array
     */
    private function calculateAgePercentages() {
        $conn = $this->database->getConnection();

        // Get total active members with birth dates
        $total_query = "SELECT COUNT(*) as total FROM members
                       WHERE member_status = 'active' AND date_of_birth IS NOT NULL";
        $stmt = $conn->prepare($total_query);
        $stmt->execute();
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        if ($total == 0) {
            return ['under_18' => 0, '18_35' => 0, '36_60' => 0, 'over_60' => 0];
        }

        // Get age group counts
        $age_query = "SELECT
                        SUM(CASE WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) < 18 THEN 1 ELSE 0 END) as under_18,
                        SUM(CASE WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 18 AND 35 THEN 1 ELSE 0 END) as age_18_35,
                        SUM(CASE WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 36 AND 60 THEN 1 ELSE 0 END) as age_36_60,
                        SUM(CASE WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) > 60 THEN 1 ELSE 0 END) as over_60
                      FROM members
                      WHERE member_status = 'active' AND date_of_birth IS NOT NULL";

        $stmt = $conn->prepare($age_query);
        $stmt->execute();
        $counts = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'under_18' => round(($counts['under_18'] / $total) * 100, 1),
            '18_35' => round(($counts['age_18_35'] / $total) * 100, 1),
            '36_60' => round(($counts['age_36_60'] / $total) * 100, 1),
            'over_60' => round(($counts['over_60'] / $total) * 100, 1)
        ];
    }

    /**
     * Get all member statistics in one optimized query
     *
     * @return array
     */
    private function getMemberStatistics() {
        $conn = $this->database->getConnection();
        $thirty_days_ago = date('Y-m-d', strtotime('-30 days'));

        $query = "SELECT
                    COUNT(*) as total_members,
                    SUM(CASE WHEN gender = 'male' THEN 1 ELSE 0 END) as male_members,
                    SUM(CASE WHEN gender = 'female' THEN 1 ELSE 0 END) as female_members,
                    SUM(CASE WHEN member_status = 'active' THEN 1 ELSE 0 END) as active_members,
                    SUM(CASE WHEN member_status = 'inactive' THEN 1 ELSE 0 END) as inactive_members,
                    SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as new_members,
                    SUM(CASE WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) <= 17
                             AND member_status = 'active'
                             AND date_of_birth IS NOT NULL THEN 1 ELSE 0 END) as total_children
                  FROM members";

        $stmt = $conn->prepare($query);
        $stmt->execute([$thirty_days_ago]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get members absent from QR attendance sessions
     *
     * @param int $limit Number of members to return
     * @return array Array with 'members' and 'total' keys
     */
    private function getQrAbsentMembers($limit = 5) {
        $conn = $this->database->getConnection();

        // First, check if we have any QR sessions at all
        $qr_sessions_query = "SELECT COUNT(*) as count FROM attendance_qr_sessions WHERE status = 'expired'";
        $stmt = $conn->prepare($qr_sessions_query);
        $stmt->execute();
        $qr_sessions_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($qr_sessions_count == 0) {
            // No QR sessions exist, return empty data
            return ['members' => [], 'total' => 0];
        }

        // Get all active members who have valid phone numbers
        $query = "SELECT m.* FROM members m
                  WHERE m.member_status = 'active'
                  AND m.phone_number IS NOT NULL
                  AND m.phone_number != ''
                  AND LENGTH(m.phone_number) >= 10
                  ORDER BY m.first_name, m.last_name";

        $stmt = $conn->prepare($query);
        $stmt->execute();
        $members = $stmt->fetchAll(PDO::FETCH_OBJ);

        $absent_members = [];

        foreach ($members as $member) {
            // Check if member has ever attended via QR
            $ever_attended_qr = $this->hasEverAttendedViaQr($member->id);

            if (!$ever_attended_qr) {
                // They've never used QR attendance, skip them for now
                continue;
            }

            // Count consecutive absences from recent QR services
            $consecutive_absences = $this->countConsecutiveQrAbsences($member->id);

            // If member has been absent for 3 or more consecutive QR services
            if ($consecutive_absences >= 3) {
                // Format department name
                $department_name = 'Not assigned';
                if (!empty($member->department) && $member->department !== 'none') {
                    $department_name = ucwords(str_replace('_', ' ', $member->department));
                }

                $absent_members[] = [
                    'member_id' => $member->id,
                    'name' => $member->first_name . ' ' . $member->last_name,
                    'phone' => $member->phone_number,
                    'department' => $department_name,
                    'consecutive_absences' => $consecutive_absences,
                    'last_attendance' => $this->getLastQrAttendanceDate($member->id)
                ];
            }
        }

        // Sort by consecutive absences (highest first)
        usort($absent_members, function($a, $b) {
            return $b['consecutive_absences'] - $a['consecutive_absences'];
        });

        return [
            'members' => array_slice($absent_members, 0, $limit),
            'total' => count($absent_members)
        ];
    }

    /**
     * Check if member has ever attended via QR
     *
     * @param int $member_id
     * @return bool
     */
    private function hasEverAttendedViaQr($member_id) {
        $conn = $this->database->getConnection();

        $query = "SELECT COUNT(*) as count FROM attendance
                  WHERE member_id = :member_id
                  AND marked_via = 'qr'
                  AND qr_session_id IS NOT NULL";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;
    }

    /**
     * Count consecutive QR absences for a member
     *
     * @param int $member_id
     * @return int
     */
    private function countConsecutiveQrAbsences($member_id) {
        $conn = $this->database->getConnection();

        // Get recent QR sessions (last 6 weeks for more realistic tracking)
        $query = "SELECT DISTINCT qr.attendance_date, qr.id as session_id, qr.service_id
                  FROM attendance_qr_sessions qr
                  WHERE qr.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 6 WEEK)
                  AND qr.status = 'expired'
                  ORDER BY qr.attendance_date DESC, qr.created_at DESC
                  LIMIT 15";

        $stmt = $conn->prepare($query);
        $stmt->execute();
        $recent_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($recent_sessions)) {
            return 0;
        }

        $consecutive_absences = 0;

        foreach ($recent_sessions as $session) {
            // Check if member attended this QR session
            $attendance_query = "SELECT COUNT(*) as count FROM attendance
                                WHERE member_id = :member_id
                                AND qr_session_id = :session_id
                                AND marked_via = 'qr'
                                AND status IN ('present', 'late')";

            $stmt = $conn->prepare($attendance_query);
            $stmt->bindParam(':member_id', $member_id, PDO::PARAM_INT);
            $stmt->bindParam(':session_id', $session['session_id'], PDO::PARAM_INT);
            $stmt->execute();

            $attended = $stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

            if (!$attended) {
                $consecutive_absences++;
            } else {
                // Found attendance, break the consecutive absence streak
                break;
            }
        }

        return $consecutive_absences;
    }

    /**
     * Get last QR attendance date for a member
     *
     * @param int $member_id
     * @return string|null
     */
    private function getLastQrAttendanceDate($member_id) {
        $conn = $this->database->getConnection();

        $query = "SELECT MAX(a.attendance_date) as last_date
                  FROM attendance a
                  WHERE a.member_id = :member_id
                  AND a.marked_via = 'qr'
                  AND a.qr_session_id IS NOT NULL
                  AND a.status IN ('present', 'late')";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id, PDO::PARAM_INT);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['last_date'];
    }

    /**
     * Platform stats removed - not needed for single tenant system
     */
    private function getPlatformStats($tenant, $user) {
        // Return empty stats for single tenant system
        return [];
    }

    /**
     * Billing stats removed - not needed for single tenant system
     */
    private function getBillingStats() {
        // Return empty stats for single tenant system
        return [];
    }
}
