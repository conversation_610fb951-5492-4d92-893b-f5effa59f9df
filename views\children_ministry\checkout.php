<?php
/**
 * Children's Ministry Check-out View
 */

// Ensure we have the required data
if (!isset($checked_in_children)) {
    redirect('children-ministry');
    exit;
}
?>

<div class="container mx-auto fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-100">
        <div class="bg-gradient-to-r from-red-500 to-red-600 p-8 text-white relative overflow-hidden">
            <div class="absolute right-0 top-0 opacity-10">
                <i class="fas fa-sign-out-alt text-9xl transform -rotate-12 translate-x-8 -translate-y-8"></i>
            </div>
            <h1 class="text-3xl font-bold">Check-out Children</h1>
            <p class="mt-2 opacity-90 max-w-2xl">Securely check children out of services using our verified security code system.</p>
        </div>
        <div class="p-6 bg-white">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-3 mb-4 md:mb-0">
                    <a href="<?php echo BASE_URL; ?>children-ministry" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 px-5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-md font-medium">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Check-out Form -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Check-out Form</h3>
        
        <form action="<?php echo BASE_URL; ?>children-ministry/process-checkout" method="POST" class="space-y-6">
            <?php echo csrf_field(); ?>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Child Selection -->
                <div>
                    <label for="child_id" class="block text-sm font-medium text-gray-700 mb-1">
                        Select Child <span class="text-red-500">*</span>
                    </label>
                    <select id="child_id" name="child_id" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        <option value="">Choose a child...</option>
                        <?php foreach ($checked_in_children as $child): ?>
                            <option value="<?php echo $child['child_id']; ?>" 
                                    data-security-code="<?php echo htmlspecialchars($child['security_code']); ?>"
                                    data-service="<?php echo htmlspecialchars($child['service_name']); ?>"
                                    data-checkin-time="<?php echo $child['check_in_time']; ?>">
                                <?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?> 
                                (Age <?php echo $child['age']; ?>) - <?php echo htmlspecialchars($child['service_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Security Code -->
                <div>
                    <label for="security_code" class="block text-sm font-medium text-gray-700 mb-1">
                        Security Code <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="security_code" name="security_code" required 
                           placeholder="Enter 4-digit code"
                           maxlength="4" pattern="[0-9]{4}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary text-center text-lg font-mono">
                    <p class="text-xs text-gray-500 mt-1">Enter the 4-digit security code provided at check-in</p>
                </div>

                <!-- Checked Out By -->
                <div>
                    <label for="checked_out_by" class="block text-sm font-medium text-gray-700 mb-1">
                        Checked Out By <span class="text-red-500">*</span>
                    </label>
                    <input type="hidden" id="checked_out_by" name="checked_out_by" value="<?php echo current_user()['id'] ?? 1; ?>">
                    <input type="text" value="<?php echo htmlspecialchars(current_user()['name'] ?? 'Admin'); ?>" 
                           readonly class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                </div>
            </div>

            <!-- Child Check-in Information Display -->
            <div id="checkin-info" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 class="font-semibold text-blue-800 mb-2">Check-in Information</h4>
                <div id="checkin-details" class="text-sm text-blue-700"></div>
            </div>

            <!-- Security Code Hint -->
            <div id="security-hint" class="hidden bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 class="font-semibold text-yellow-800 mb-2">
                    <i class="fas fa-key mr-2"></i>
                    Security Code Required
                </h4>
                <p class="text-sm text-yellow-700">
                    Please ask the parent/guardian for the 4-digit security code that was provided when the child was checked in.
                </p>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4">
                <button type="reset" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                    Clear Form
                </button>
                <button type="submit" class="px-6 py-2 bg-secondary hover:bg-secondary-dark text-gray-800 rounded-md flex items-center">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Check Out Child
                </button>
            </div>
        </form>
    </div>

    <!-- Currently Checked In Children -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Currently Checked In Children</h3>
        
        <?php if (empty($checked_in_children)): ?>
            <div class="text-center py-8">
                <div class="text-gray-400 mb-4">
                    <i class="fas fa-users text-6xl"></i>
                </div>
                <h4 class="text-xl font-semibold text-gray-600 mb-2">No Children Checked In</h4>
                <p class="text-gray-500 mb-4">There are currently no children checked in to any services.</p>
                <p class="text-sm text-gray-400">Children will appear here when they check in via QR codes generated by the admin.</p>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php foreach ($checked_in_children as $child): ?>
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-center mb-3">
                            <?php if ($child['profile_picture']): ?>
                                <img src="<?php echo BASE_URL . $child['profile_picture']; ?>" alt="Profile" class="w-12 h-12 rounded-full mr-3">
                            <?php else: ?>
                                <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                                    <i class="fas fa-child text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h4 class="font-semibold text-gray-800"><?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?></h4>
                                <p class="text-sm text-gray-600">Age <?php echo $child['age']; ?></p>
                            </div>
                        </div>
                        
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Service:</span>
                                <span class="font-medium"><?php echo htmlspecialchars($child['service_name']); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Check-in:</span>
                                <span class="font-medium"><?php echo date('g:i A', strtotime($child['check_in_time'])); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Duration:</span>
                                <span class="font-medium">
                                    <?php 
                                    $duration = time() - strtotime($child['check_in_time']);
                                    $hours = floor($duration / 3600);
                                    $minutes = floor(($duration % 3600) / 60);
                                    echo $hours > 0 ? "{$hours}h {$minutes}m" : "{$minutes}m";
                                    ?>
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Checked in by:</span>
                                <span class="font-medium"><?php echo htmlspecialchars($child['checked_in_by_first_name'] . ' ' . $child['checked_in_by_last_name']); ?></span>
                            </div>
                        </div>
                        
                        <button onclick="selectChildForCheckout('<?php echo $child['child_id']; ?>')" 
                                class="w-full mt-3 bg-secondary hover:bg-secondary-dark text-gray-800 py-2 px-4 rounded-md text-sm font-medium">
                            Select for Check-out
                        </button>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const childSelect = document.getElementById('child_id');
    const securityCodeInput = document.getElementById('security_code');
    const checkinInfo = document.getElementById('checkin-info');
    const checkinDetails = document.getElementById('checkin-details');
    const securityHint = document.getElementById('security-hint');

    // Handle child selection
    childSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (this.value) {
            const service = selectedOption.dataset.service;
            const checkinTime = selectedOption.dataset.checkinTime;
            
            // Show check-in information
            const checkinDate = new Date(checkinTime);
            const duration = Math.floor((new Date() - checkinDate) / (1000 * 60)); // minutes
            const hours = Math.floor(duration / 60);
            const minutes = duration % 60;
            const durationText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
            
            checkinDetails.innerHTML = `
                <div class="grid grid-cols-2 gap-4">
                    <div><strong>Service:</strong> ${service}</div>
                    <div><strong>Check-in Time:</strong> ${checkinDate.toLocaleTimeString()}</div>
                    <div><strong>Duration:</strong> ${durationText}</div>
                    <div><strong>Status:</strong> <span class="text-green-600">Checked In</span></div>
                </div>
            `;
            checkinInfo.classList.remove('hidden');
            securityHint.classList.remove('hidden');
            
            // Focus on security code input
            securityCodeInput.focus();
        } else {
            checkinInfo.classList.add('hidden');
            securityHint.classList.add('hidden');
        }
    });

    // Format security code input
    securityCodeInput.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').substring(0, 4);
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const childId = childSelect.value;
        const securityCode = securityCodeInput.value;
        
        if (!childId || !securityCode) {
            e.preventDefault();
            alert('Please select a child and enter the security code.');
            return false;
        }
        
        if (securityCode.length !== 4) {
            e.preventDefault();
            alert('Security code must be 4 digits.');
            return false;
        }
    });
});

// Function to select child for checkout (called from child cards)
function selectChildForCheckout(childId) {
    const childSelect = document.getElementById('child_id');
    childSelect.value = childId;
    childSelect.dispatchEvent(new Event('change'));
    
    // Scroll to form
    document.querySelector('form').scrollIntoView({ behavior: 'smooth' });
}
</script>
