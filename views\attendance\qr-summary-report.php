<!-- QR Analytics Summary Report -->
<style>
body { 
    font-family: "Times New Roman", serif; 
    line-height: 1.6; 
    color: #2c3e50;
    background: #f8f9fa;
}

.report-container {
    max-width: 210mm;
    margin: 20px auto;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    min-height: 297mm;
}

.report-header {
    background: white;
    border-bottom: 3px solid #2c3e50;
    padding: 40px 40px 30px 40px;
    text-align: center;
}

.report-title {
    font-size: 28px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
    letter-spacing: 1px;
}

.church-name {
    font-size: 18px;
    color: #666;
    margin-bottom: 20px;
    font-weight: normal;
}

.report-subtitle {
    font-size: 16px;
    color: #888;
    margin-bottom: 30px;
}

.report-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

.meta-item {
    text-align: left;
}

.meta-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
    font-weight: 600;
}

.meta-value {
    font-size: 14px;
    color: #2c3e50;
    font-weight: normal;
}

.content {
    padding: 40px;
}

.section {
    margin-bottom: 40px;
    page-break-inside: avoid;
}

.section-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #2c3e50;
}

.section-title {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 20px 0;
}

.summary-card {
    background: white;
    padding: 25px;
    border: 1px solid #dee2e6;
    text-align: center;
}

.summary-number {
    font-size: 36px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1;
}

.summary-label {
    color: #666;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.summary-description {
    color: #888;
    font-size: 12px;
    margin-top: 5px;
}

.key-insights {
    background: #f8f9fa;
    padding: 25px;
    border-left: 4px solid #2c3e50;
    margin: 20px 0;
}

.insight-item {
    margin-bottom: 15px;
    padding-left: 20px;
    position: relative;
}

.insight-item:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #2c3e50;
    font-weight: bold;
}

.report-footer {
    background: #f8f9fa;
    padding: 20px;
    text-align: center;
    border-top: 1px solid #dee2e6;
    color: #666;
    font-size: 12px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 30px 0;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border: 1px solid #2c3e50;
    background: white;
    color: #2c3e50;
    text-decoration: none;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.btn:hover {
    background: #2c3e50;
    color: white;
}

.btn-primary {
    background: #2c3e50;
    color: white;
}

.btn-primary:hover {
    background: #1a252f;
}

@media print {
    body { background: white; }
    .report-container { 
        box-shadow: none; 
        margin: 0;
        max-width: none;
    }
    .action-buttons { display: none; }
    .section { 
        page-break-inside: avoid;
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .report-container { margin: 10px; }
    .content { padding: 20px; }
    .summary-grid { grid-template-columns: 1fr; }
    .report-meta { grid-template-columns: 1fr; }
}
</style>

<!-- Add breadcrumb navigation -->
<div class="mb-6">
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="<?php echo BASE_URL; ?>dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home mr-2"></i>
                    Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <a href="<?php echo BASE_URL; ?>attendance" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">Attendance</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <a href="<?php echo BASE_URL; ?>attendance/qr-analytics" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">QR Analytics</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Summary Report</span>
                </div>
            </li>
        </ol>
    </nav>
</div>

<!-- Report content starts here -->
<div class="report-container">
    <div class="report-header">
        <div class="header-content">
            <h1 class="report-title">QR ATTENDANCE SUMMARY REPORT</h1>
            <div class="church-name">ICGC Emmanuel Temple</div>
            <div class="report-subtitle">Church Management System</div>
            
            <div class="report-meta">
                <div class="meta-item">
                    <div class="meta-label">Report Period</div>
                    <div class="meta-value"><?php echo ucfirst($report_data['period']['type']); ?> - <?php echo htmlspecialchars($report_data['period']['value']); ?></div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Date Range</div>
                    <div class="meta-value"><?php echo date('F j, Y', strtotime($report_data['period']['start_date'])); ?> to <?php echo date('F j, Y', strtotime($report_data['period']['end_date'])); ?></div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Generated By</div>
                    <div class="meta-value"><?php echo htmlspecialchars($report_data['generated_by']); ?></div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Generated On</div>
                    <div class="meta-value"><?php echo date('F j, Y \a\t g:i A', strtotime($report_data['generated_at'])); ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="action-buttons">
        <button class="btn btn-primary" onclick="window.print()">
            Print Report
        </button>
        <a href="<?php echo BASE_URL; ?>attendance/qr-analytics-report?generate=true&period=<?php echo $report_data['period']['type']; ?>&value=<?php echo $report_data['period']['value']; ?>&format=excel" class="btn">
            Download CSV
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/qr-analytics-report?generate=true&period=<?php echo $report_data['period']['type']; ?>&value=<?php echo $report_data['period']['value']; ?>&format=detailed" class="btn">
            Detailed Report
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/qr-analytics" class="btn">
            Back to Dashboard
        </a>
    </div>
    
    <div class="content">
        <!-- Executive Summary -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">Executive Summary</h2>
            </div>
            <div class="summary-grid">
                <?php $overview = $report_data['overview'] ?? []; ?>
                <div class="summary-card">
                    <div class="summary-number"><?php echo number_format($overview['total_qr_attendance'] ?? 0); ?></div>
                    <div class="summary-label">Total QR Attendance</div>
                    <div class="summary-description">Members who used QR codes</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number"><?php echo number_format($overview['unique_members'] ?? 0); ?></div>
                    <div class="summary-label">Unique Members</div>
                    <div class="summary-description">Individual participants</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number"><?php echo number_format($overview['total_sessions'] ?? 0); ?></div>
                    <div class="summary-label">QR Sessions</div>
                    <div class="summary-description">Sessions created</div>
                </div>
            </div>
        </div>

        <!-- Key Insights -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">Key Insights</h2>
            </div>
            <div class="key-insights">
                <?php 
                $engagement = $report_data['member_engagement'] ?? [];
                $gender = $report_data['gender_distribution'] ?? [];
                $total_engaged = $engagement['regular_attendees'] + $engagement['occasional_attendees'] + $engagement['at_risk_members'];
                ?>
                
                <div class="insight-item">
                    <strong>Member Engagement:</strong> <?php echo number_format($engagement['regular_attendees'] ?? 0); ?> regular attendees (<?php echo $total_engaged > 0 ? number_format(($engagement['regular_attendees'] / $total_engaged) * 100, 1) : 0; ?>% of QR users) demonstrate strong engagement with 8+ attendances.
                </div>
                
                <div class="insight-item">
                    <strong>Gender Distribution:</strong> <?php echo number_format($gender['male_percentage'] ?? 0, 1); ?>% male and <?php echo number_format($gender['female_percentage'] ?? 0, 1); ?>% female participation in QR attendance system.
                </div>
                
                <div class="insight-item">
                    <strong>At-Risk Members:</strong> <?php echo number_format($engagement['at_risk_members'] ?? 0); ?> members with only 1-2 QR attendances may need additional engagement efforts.
                </div>
                
                <?php if (!empty($report_data['department_distribution'])): ?>
                    <?php 
                    $top_dept = '';
                    $top_count = 0;
                    foreach ($report_data['department_distribution'] as $dept => $data) {
                        if ($data['count'] > $top_count) {
                            $top_count = $data['count'];
                            $top_dept = $dept;
                        }
                    }
                    ?>
                    <div class="insight-item">
                        <strong>Top Department:</strong> <?php echo htmlspecialchars($top_dept); ?> leads QR adoption with <?php echo number_format($top_count); ?> members actively using the system.
                    </div>
                <?php endif; ?>
                
                <div class="insight-item">
                    <strong>System Adoption:</strong> Average of <?php echo number_format($overview['avg_attendance_per_session'] ?? 0, 1); ?> attendances per QR session indicates <?php echo ($overview['avg_attendance_per_session'] ?? 0) > 20 ? 'strong' : 'moderate'; ?> system utilization.
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">Quick Statistics</h2>
            </div>
            <div class="summary-grid">
                <div class="summary-card">
                    <div class="summary-number"><?php echo number_format($engagement['regular_attendees'] ?? 0); ?></div>
                    <div class="summary-label">Regular Users</div>
                    <div class="summary-description">8+ QR attendances</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number"><?php echo number_format($engagement['occasional_attendees'] ?? 0); ?></div>
                    <div class="summary-label">Occasional Users</div>
                    <div class="summary-description">3-7 QR attendances</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number"><?php echo number_format($engagement['at_risk_members'] ?? 0); ?></div>
                    <div class="summary-label">At-Risk Users</div>
                    <div class="summary-description">1-2 QR attendances</div>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">Recommendations</h2>
            </div>
            <div class="key-insights">
                <?php if (($engagement['at_risk_members'] ?? 0) > 0): ?>
                <div class="insight-item">
                    Focus on re-engaging <?php echo number_format($engagement['at_risk_members']); ?> at-risk members through targeted outreach and QR system training.
                </div>
                <?php endif; ?>
                
                <?php if (($overview['avg_attendance_per_session'] ?? 0) < 15): ?>
                <div class="insight-item">
                    Consider promoting QR attendance system more actively to increase average session participation.
                </div>
                <?php endif; ?>
                
                <div class="insight-item">
                    Continue monitoring engagement patterns to identify trends and optimize the QR attendance system.
                </div>
                
                <div class="insight-item">
                    Generate detailed reports monthly to track progress and identify areas for improvement.
                </div>
            </div>
        </div>
    </div>
    
    <div class="report-footer">
        <p><strong>ICGC Emmanuel Temple</strong> - Church Management System</p>
        <p>Generated on <?php echo date('l, F j, Y \a\t g:i A'); ?></p>
        <p><em>This report contains confidential church data. Please handle with care.</em></p>
    </div>
</div>
