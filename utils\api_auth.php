<?php
/**
 * API Authentication Utility
 * 
 * Helper functions for API authentication and authorization
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/ApiKey.php';

/**
 * Authenticate API request
 * 
 * @param string $required_permission The required permission
 * @return array The authentication result with status and tenant_id
 */
function authenticate_api_request($required_permission = null) {
    $result = [
        'authenticated' => false,
        'tenant_id' => null,
        'user_id' => null,
        'error' => null
    ];
    
    // Check for API key in header
    $api_key = null;
    $headers = getallheaders();
    
    if (isset($headers['X-API-Key'])) {
        $api_key = $headers['X-API-Key'];
    } elseif (isset($headers['x-api-key'])) {
        $api_key = $headers['x-api-key'];
    } elseif (isset($_GET['api_key'])) {
        // Also allow api_key as a query parameter (less secure but useful for testing)
        $api_key = $_GET['api_key'];
    }
    
    if (!$api_key) {
        $result['error'] = 'API key is required';
        return $result;
    }
    
    // Validate API key
    $database = new Database();
    $api_key_model = new ApiKey($database->getConnection());
    
    if ($api_key_model->validate($api_key, $required_permission)) {
        $result['authenticated'] = true;
        $result['user_id'] = $api_key_model->user_id;
    } else {
        $result['error'] = 'Invalid API key or insufficient permissions';
    }
    
    return $result;
}

/**
 * Send JSON response
 * 
 * @param mixed $data The data to send
 * @param int $status_code HTTP status code
 * @return void
 */
function send_json_response($data, $status_code = 200) {
    http_response_code($status_code);
    echo json_encode($data);
    exit;
}

/**
 * Send error response
 * 
 * @param string $message Error message
 * @param int $status_code HTTP status code
 * @return void
 */
function send_error_response($message, $status_code = 400) {
    send_json_response(['error' => $message], $status_code);
} 