<div class="container mx-auto max-w-6xl">
    <!-- <PERSON> Header -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="bg-primary-light p-3 rounded-full mr-4">
                    <i class="fas fa-sms text-primary text-xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">SMS Messages</h1>
                    <p class="text-gray-500 text-sm">View and manage all your SMS communications</p>
                    <div class="text-sm text-gray-600 flex items-center mt-1">
                        <i class="fas fa-coins mr-2 text-yellow-500"></i>
                        <span>Credit Balance: <strong class="<?php echo $sms_balance > 0 ? 'text-green-600' : 'text-red-600'; ?>"><?php echo number_format($sms_balance); ?> SMS</strong></span>
                        <?php if ($sms_balance <= 100): ?>
                            <span class="ml-2 px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">Low Balance</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="<?php echo BASE_URL; ?>sms" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center transition-colors duration-200">
                    <i class="fas fa-paper-plane mr-2"></i> Compose SMS
                </a>
            </div>
        </div>
    </div>

    <!-- Simplified Filter Bar -->
    <div class="bg-white rounded-lg shadow-md p-5 mb-6 border border-gray-200">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <div class="bg-primary-light bg-opacity-20 p-2 rounded-full mr-3">
                    <i class="fas fa-filter text-primary"></i>
                </div>
                <h3 class="text-base font-medium text-gray-800">Quick Filter</h3>
            </div>
            <button id="toggle-filters" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 py-1.5 px-3 rounded-full flex items-center transition-colors duration-200 focus:outline-none">
                <span id="toggle-text">Show Options</span>
                <i class="fas fa-chevron-down ml-1.5 text-xs"></i>
            </button>
        </div>

        <!-- Always visible search bar -->
        <div class="relative mt-3 mb-4">
            <input type="text" id="search-text" class="pl-10 w-full rounded-full border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 text-sm py-3 px-4 hover:border-primary transition-colors duration-200" placeholder="Search messages...">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-500"></i>
            </div>
            <button id="reset-search" class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200 focus:outline-none">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Expandable advanced filters -->
        <div id="filters-container" class="hidden mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <div class="relative">
                        <select id="status-filter" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 text-sm pl-9 pr-9 py-2.5 appearance-none hover:border-primary transition-colors duration-200">
                            <option value="">All Statuses</option>
                            <option value="sent">Sent</option>
                            <option value="pending">Pending</option>
                            <option value="partial">Partial</option>
                            <option value="failed">Failed</option>
                        </select>
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-tag text-gray-500 text-xs"></i>
                        </div>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-500 text-xs"></i>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <div class="relative flex-1">
                        <input type="date" id="start-date" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 text-sm pl-9 py-2.5 hover:border-primary transition-colors duration-200" placeholder="From">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-500 text-xs"></i>
                        </div>
                    </div>
                    <div class="relative flex-1">
                        <input type="date" id="end-date" class="w-full rounded-md border-2 border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20 text-sm pl-9 py-2.5 hover:border-primary transition-colors duration-200" placeholder="To">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-500 text-xs"></i>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end">
                    <button id="reset-filters" class="bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm py-2.5 px-4 rounded-md mr-3 transition-colors duration-200 flex items-center border border-gray-200 shadow-sm">
                        <i class="fas fa-undo-alt mr-2 text-xs"></i> Reset
                    </button>
                    <button id="apply-filters" class="bg-primary hover:bg-primary-dark text-white text-sm py-2.5 px-4 rounded-md transition-colors duration-200 flex items-center shadow-sm">
                        <i class="fas fa-filter mr-2 text-xs"></i> Apply
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- SMS Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 bg-gray-50">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Message History</h3>
                <div class="text-sm text-gray-500">
                    <?php if (!empty($messages)) : ?>
                        <?php if (isset($totalCount)): ?>
                            Showing <?php echo count($messages) > 0 ? ($page - 1) * $limit + 1 : 0; ?> to <?php echo min($page * $limit, $totalCount); ?> of <?php echo $totalCount; ?> messages
                        <?php else: ?>
                            Showing <?php echo count($messages); ?> message<?php echo count($messages) != 1 ? 's' : ''; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipients</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent By</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($messages)) : ?>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                                <div class="py-12">
                                    <div class="text-center">
                                        <div class="mx-auto h-24 w-24 rounded-full bg-primary-light flex items-center justify-center mb-4">
                                            <i class="fas fa-sms text-primary text-4xl"></i>
                                        </div>
                                        <h3 class="text-gray-800 text-xl font-medium mb-2">No SMS messages found</h3>
                                        <p class="text-gray-500 text-base mb-6 max-w-md mx-auto">You haven't sent any SMS messages yet. Start communicating with your church members by sending your first message.</p>
                                        <a href="<?php echo BASE_URL; ?>sms" class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary-dark transition-colors duration-200">
                                            <i class="fas fa-paper-plane mr-2"></i> Compose New SMS
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ($messages as $message) : ?>
                            <tr class="hover:bg-gray-50 transition-colors duration-150 ease-in-out">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo format_date($message['sent_date'], 'd M, Y'); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo format_date($message['sent_date'], 'H:i'); ?></div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 tooltip max-w-xs truncate" title="<?php echo htmlspecialchars($message['message']); ?>">
                                        <?php echo strlen($message['message']) > 50 ? htmlspecialchars(substr($message['message'], 0, 50)) . '...' : htmlspecialchars($message['message']); ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                        $recipients = explode(',', $message['recipients']);
                                        $count = count($recipients);
                                    ?>
                                    <div class="text-sm text-gray-900"><?php echo $count; ?> recipient<?php echo $count != 1 ? 's' : ''; ?></div>
                                    <?php if ($count > 0) : ?>
                                        <div class="text-xs text-gray-500 tooltip" title="<?php echo implode(', ', array_slice($recipients, 0, 3)); ?><?php echo $count > 3 ? ' and ' . ($count - 3) . ' more' : ''; ?>">
                                            <?php echo implode(', ', array_slice($recipients, 0, 1)); ?><?php echo $count > 1 ? ' and ' . ($count - 1) . ' more' : ''; ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($message['status'] == 'sent') : ?>
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i> Sent
                                        </span>
                                    <?php elseif ($message['status'] == 'partial') : ?>
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">
                                            <i class="fas fa-exclamation-triangle mr-1"></i> Partial
                                        </span>
                                    <?php elseif ($message['status'] == 'pending') : ?>
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-clock mr-1"></i> Pending
                                        </span>
                                    <?php else : ?>
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            <i class="fas fa-exclamation-circle mr-1"></i> Failed
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $message['sent_by_name'] ?? 'System'; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex space-x-2 justify-end">
                                        <a href="<?php echo BASE_URL; ?>sms/view?id=<?php echo $message['id']; ?>" class="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 p-2 rounded-full transition-colors duration-200 tooltip" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" onclick="confirmDelete(<?php echo $message['id']; ?>)" class="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 p-2 rounded-full transition-colors duration-200 tooltip" title="Delete">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>

            <!-- No results message (hidden by default) -->
            <div id="no-results" class="p-6 text-center hidden">
                <div class="bg-gray-50 rounded-lg p-4 inline-block mb-3">
                    <i class="fas fa-search text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-base font-medium text-gray-700 mb-1">No Matching Messages</h3>
                <p class="text-gray-500 text-sm">No messages match your search criteria.</p>
            </div>
        </div>
        <?php if (!empty($messages)) : ?>
        <!-- Pagination Controls -->
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium"><?php echo count($messages) > 0 ? ($page - 1) * $limit + 1 : 0; ?></span> to <span class="font-medium"><?php echo min($page * $limit, $totalCount); ?></span> of <span class="font-medium"><?php echo $totalCount; ?></span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <!-- Previous Page Link -->
                            <?php if ($page > 1): ?>
                                <a href="<?php echo BASE_URL; ?>sms/messages?page=<?php echo $page - 1; ?><?php echo isset($_GET['limit']) ? '&limit=' . htmlspecialchars($_GET['limit']) : ''; ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php else: ?>
                                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $startPage + 4);
                            if ($endPage - $startPage < 4 && $totalPages > 5) {
                                $startPage = max(1, $endPage - 4);
                            }

                            // Preserve any limit parameter
                            $limitParam = isset($_GET['limit']) ? '&limit=' . htmlspecialchars($_GET['limit']) : '';

                            for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="relative inline-flex items-center px-4 py-2 border border-primary bg-primary-light text-sm font-medium text-white">
                                        <?php echo $i; ?>
                                    </span>
                                <?php else: ?>
                                    <a href="<?php echo BASE_URL; ?>sms/messages?page=<?php echo $i; ?><?php echo $limitParam; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <!-- Next Page Link -->
                            <?php if ($page < $totalPages): ?>
                                <a href="<?php echo BASE_URL; ?>sms/messages?page=<?php echo $page + 1; ?><?php echo $limitParam; ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php else: ?>
                                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-6 py-3 border-t border-gray-200 text-right">
            <a href="<?php echo BASE_URL; ?>sms" class="text-primary hover:text-primary-dark text-sm font-medium transition-colors duration-200">
                <i class="fas fa-paper-plane mr-1"></i> Compose New Message
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
    // Toggle advanced filters
    document.getElementById('toggle-filters').addEventListener('click', function() {
        const container = document.getElementById('filters-container');
        const toggleText = document.getElementById('toggle-text');
        const icon = this.querySelector('i');

        if (container.classList.contains('hidden')) {
            container.classList.remove('hidden');
            toggleText.textContent = 'Hide Options';
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-up');
        } else {
            container.classList.add('hidden');
            toggleText.textContent = 'Show Options';
            icon.classList.remove('fa-chevron-up');
            icon.classList.add('fa-chevron-down');
        }
    });

    // Real-time search functionality
    const searchInput = document.getElementById('search-text');
    const resetSearchBtn = document.getElementById('reset-search');
    const tableRows = document.querySelectorAll('table tbody tr');

    // Function to filter table rows based on search text
    function filterTable(searchText) {
        const lowerSearchText = searchText.toLowerCase();
        let visibleCount = 0;

        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(lowerSearchText)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Show/hide no results message
        const noResults = document.getElementById('no-results');
        if (noResults) {
            if (visibleCount === 0 && searchText) {
                noResults.classList.remove('hidden');
            } else {
                noResults.classList.add('hidden');
            }
        }

        // Show/hide reset button
        if (searchText) {
            resetSearchBtn.style.display = 'flex';
        } else {
            resetSearchBtn.style.display = 'none';
        }
    }

    // Initialize reset button visibility
    resetSearchBtn.style.display = searchInput.value ? 'flex' : 'none';

    // Add event listeners for real-time search
    searchInput.addEventListener('input', function() {
        filterTable(this.value);
    });

    // Reset search
    resetSearchBtn.addEventListener('click', function() {
        searchInput.value = '';
        filterTable('');
        searchInput.focus();
    });

    // Apply advanced filters
    document.getElementById('apply-filters').addEventListener('click', function() {
        const status = document.getElementById('status-filter').value;
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;
        const searchText = document.getElementById('search-text').value;

        if (status || startDate || endDate || searchText) {
            let url = '<?php echo BASE_URL; ?>sms/filter?';
            let params = [];

            if (status) params.push('status=' + encodeURIComponent(status));
            if (startDate) params.push('start_date=' + encodeURIComponent(startDate));
            if (endDate) params.push('end_date=' + encodeURIComponent(endDate));
            if (searchText) params.push('search=' + encodeURIComponent(searchText));

            // Reset to page 1 when applying new filters
            params.push('page=1');

            // Preserve limit parameter if it exists
            const currentLimit = <?php echo isset($limit) ? $limit : 20; ?>;
            params.push('limit=' + currentLimit);

            window.location.href = url + params.join('&');
        } else {
            // Preserve the current limit when clearing filters
            const currentLimit = <?php echo isset($limit) ? $limit : 20; ?>;
            window.location.href = '<?php echo BASE_URL; ?>sms/messages?page=1&limit=' + currentLimit;
        }
    });

    // Reset all filters
    document.getElementById('reset-filters').addEventListener('click', function() {
        document.getElementById('status-filter').value = '';
        document.getElementById('start-date').value = '';
        document.getElementById('end-date').value = '';
        document.getElementById('search-text').value = '';
        filterTable(''); // Reset the table filtering
        resetSearchBtn.style.display = 'none';
        // Preserve the current limit when resetting filters
        const currentLimit = <?php echo isset($limit) ? $limit : 20; ?>;
        window.location.href = '<?php echo BASE_URL; ?>sms/messages?page=1&limit=' + currentLimit;
    });

    // Initialize table filtering with any existing search text
    filterTable(searchInput.value);

    // Delete confirmation modal
    function confirmDelete(id) {
        // Create modal if it doesn't exist
        if (!document.getElementById('deleteModal')) {
            const modal = document.createElement('div');
            modal.id = 'deleteModal';
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Delete SMS Message</h3>
                        <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500">Are you sure you want to delete this SMS message? This action cannot be undone.</p>
                        </div>
                        <div class="flex justify-center mt-4 px-4 py-3">
                            <button id="cancelDelete" class="bg-gray-200 px-4 py-2 rounded-md text-gray-800 hover:bg-gray-300 mr-2 transition-colors duration-200">Cancel</button>
                            <a id="confirmDelete" href="#" class="bg-red-600 px-4 py-2 rounded-md text-white hover:bg-red-700 transition-colors duration-200">Delete</a>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        const modal = document.getElementById('deleteModal');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('cancelDelete');

        // Set the delete URL
        confirmBtn.href = '<?php echo BASE_URL; ?>sms/messages/' + id;

        // Show the modal
        modal.classList.remove('hidden');

        // Close modal when cancel is clicked
        cancelBtn.onclick = function() {
            modal.classList.add('hidden');
        };

        // Close modal when clicking outside
        modal.onclick = function(e) {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        };
    }
</script>
