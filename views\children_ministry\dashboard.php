<?php
/**
 * Children's Ministry Dashboard View
 */

// Data is ensured by controller - no need for redirect logic in view
?>

<div class="page-content-centered py-6">
    <!-- Enhanced Header Section with Theme Colors -->
    <div class="bg-white rounded-lg shadow-lg border border-gray-200 mb-8 overflow-hidden">
        <div class="bg-gradient-to-r from-primary to-primary-light px-6 py-8 text-white relative">
            <!-- Decorative Background Elements -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-4 right-4 w-20 h-20 bg-white rounded-full"></div>
                <div class="absolute bottom-4 right-16 w-12 h-12 bg-white rounded-full"></div>
                <div class="absolute top-8 right-32 w-6 h-6 bg-white rounded-full"></div>
            </div>

            <div class="flex items-center justify-between relative z-10">
                <div>
                    <h1 class="text-3xl font-bold text-white">Children's Ministry</h1>
                    <p class="text-white/90 mt-2 text-lg">Manage children, families, and attendance</p>

                </div>
                <div class="hidden md:flex items-center justify-center w-20 h-20 bg-white/20 rounded-full backdrop-blur-sm">
                    <i class="fas fa-child text-white text-3xl"></i>
                </div>
            </div>
        </div>

        <!-- Primary Action Buttons with Enhanced Design -->
        <div class="px-6 py-6 bg-gradient-to-r from-gray-50 to-secondary-light/20">
            <div class="flex flex-wrap gap-3">
                <!-- REMOVED: Manual Check In - Using QR codes exclusively -->
                <a href="<?php echo BASE_URL; ?>children-ministry/children"
                   class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-5 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <i class="fas fa-users mr-2"></i>All Children
                </a>
                <a href="<?php echo BASE_URL; ?>children-ministry/families"
                   class="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-5 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <i class="fas fa-home mr-2"></i>Families
                </a>
                <a href="<?php echo BASE_URL; ?>children-ministry/age-groups"
                   class="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 text-white px-5 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <i class="fas fa-layer-group mr-2"></i>Age Groups
                </a>
                <a href="<?php echo BASE_URL; ?>children-ministry/standalone-registration"
                   class="bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white px-5 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <i class="fas fa-user-plus mr-2"></i>Register Child
                </a>
                <a href="<?php echo BASE_URL; ?>children-ministry/qr-analytics"
                   class="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white px-5 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <i class="fas fa-chart-line mr-2"></i>QR Analytics
                </a>
                <button onclick="openRealtimeDashboard()"
                   class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-5 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <i class="fas fa-tv mr-2"></i>Real-time Dashboard
                </button>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <!-- Total Children -->
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-all duration-300">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-child text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-blue-700">Registered Children</p>
                    <p class="text-3xl font-bold text-blue-900"><?php echo number_format($stats['total_children']); ?></p>
                </div>
            </div>
        </div>

        <!-- Present Today (QR Check-ins) -->
        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-all duration-300">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-check text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-green-700">Present Today</p>
                    <p class="text-3xl font-bold text-green-900" data-checkin-count><?php echo number_format($stats['todays_attendance'] ?? count($currently_checked_in)); ?></p>
                </div>
            </div>
        </div>

        <!-- Medical Alerts -->
        <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-all duration-300">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-14 h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-heartbeat text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-red-700">Medical Alerts</p>
                    <p class="text-3xl font-bold text-red-900"><?php echo number_format($stats['medical_alerts']); ?></p>
                </div>
            </div>
        </div>

        <!-- Late Today -->
        <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl shadow-lg p-6 transform hover:scale-105 transition-all duration-300">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-14 h-14 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-yellow-700">Late Today</p>
                    <p class="text-3xl font-bold text-yellow-900" id="late-today-count">
                        <?php
                        $late_count = 0;
                        foreach ($currently_checked_in as $child) {
                            if ($child['status'] === 'late') $late_count++;
                        }
                        echo number_format($late_count);
                        ?>
                    </p>
                </div>
            </div>
        </div>
    </div>



    <!-- Enhanced Age Groups Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Colorful Age Groups Chart -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
            <div class="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-chart-pie text-white mr-2"></i>
                    Children by Age Group
                </h3>
            </div>
            <div class="p-6 bg-gradient-to-br from-white to-indigo-50">
                <div class="space-y-6">
                    <?php
                    $colors = [
                        ['from' => 'from-blue-500', 'to' => 'to-blue-600', 'bg' => 'bg-blue-100', 'text' => 'text-blue-700', 'dot' => 'bg-blue-500'],
                        ['from' => 'from-green-500', 'to' => 'to-green-600', 'bg' => 'bg-green-100', 'text' => 'text-green-700', 'dot' => 'bg-green-500'],
                        ['from' => 'from-purple-500', 'to' => 'to-purple-600', 'bg' => 'bg-purple-100', 'text' => 'text-purple-700', 'dot' => 'bg-purple-500'],
                        ['from' => 'from-orange-500', 'to' => 'to-orange-600', 'bg' => 'bg-orange-100', 'text' => 'text-orange-700', 'dot' => 'bg-orange-500'],
                        ['from' => 'from-red-500', 'to' => 'to-red-600', 'bg' => 'bg-red-100', 'text' => 'text-red-700', 'dot' => 'bg-red-500'],
                        ['from' => 'from-yellow-500', 'to' => 'to-yellow-600', 'bg' => 'bg-yellow-100', 'text' => 'text-yellow-700', 'dot' => 'bg-yellow-500']
                    ];
                    $colorIndex = 0;
                    ?>
                    <?php foreach ($stats['age_groups'] as $age_group): ?>
                        <?php
                        $color = $colors[$colorIndex % count($colors)];
                        $colorIndex++;
                        ?>
                        <div class="<?php echo $color['bg']; ?> rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 rounded-full <?php echo $color['dot']; ?> mr-3 shadow-sm"></div>
                                    <div>
                                        <span class="<?php echo $color['text']; ?> font-semibold text-base"><?php echo htmlspecialchars($age_group['name']); ?></span>
                                        <span class="text-sm text-gray-600 ml-2">(<?php echo $age_group['min_age']; ?>-<?php echo $age_group['max_age']; ?> years)</span>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="font-bold <?php echo $color['text']; ?> text-xl"><?php echo number_format($age_group['children_count']); ?></span>
                                    <div class="text-xs text-gray-500">children</div>
                                </div>
                            </div>
                            <div class="w-full bg-gray-300 rounded-full h-4 shadow-inner">
                                <?php
                                $percentage = $stats['total_children'] > 0 ? ($age_group['children_count'] / $stats['total_children']) * 100 : 0;
                                ?>
                                <div class="bg-gradient-to-r <?php echo $color['from']; ?> <?php echo $color['to']; ?> h-4 rounded-full transition-all duration-700 shadow-sm relative overflow-hidden" style="width: <?php echo $percentage; ?>%">
                                    <div class="absolute inset-0 bg-white opacity-20 animate-pulse"></div>
                                </div>
                            </div>
                            <div class="mt-2 text-right">
                                <span class="text-sm font-medium <?php echo $color['text']; ?>"><?php echo number_format($percentage, 1); ?>%</span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Real-time QR Attendance Tracking -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
            <!-- QR Attendance Statistics Header -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 border-b border-gray-100 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-chart-line text-white mr-2"></i>
                        Real-time Attendance Tracking
                    </h3>
                    <div class="flex items-center space-x-4">
                        <div class="text-white text-sm">
                            <i class="fas fa-sync-alt mr-1"></i>
                            <span id="last-update">Just now</span>
                        </div>
                        <a href="<?php echo BASE_URL; ?>children-ministry/attendance-list" class="text-white hover:text-blue-200 transition-colors mr-3" title="View Attendance List">
                            <i class="fas fa-list"></i>
                        </a>
                        <a href="<?php echo BASE_URL; ?>children-ministry/qr-analytics" class="text-white hover:text-blue-200 transition-colors mr-3" title="View Analytics">
                            <i class="fas fa-chart-line"></i>
                        </a>
                        <button onclick="refreshAttendanceData()" class="text-white hover:text-blue-200 transition-colors">
                            <i class="fas fa-refresh"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Simplified Attendance Statistics -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600" data-stat="present_count">
                            <?php
                            $present_count = 0;
                            foreach ($currently_checked_in as $child) {
                                if ($child['status'] === 'present') $present_count++;
                            }
                            echo $present_count;
                            ?>
                        </div>
                        <div class="text-sm text-gray-600 font-medium">Present</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-yellow-600" data-stat="late_count">
                            <?php
                            $late_count = 0;
                            foreach ($currently_checked_in as $child) {
                                if ($child['status'] === 'late') $late_count++;
                            }
                            echo $late_count;
                            ?>
                        </div>
                        <div class="text-sm text-gray-600 font-medium">Late</div>
                    </div>
                </div>
            </div>

            <!-- Present and Late Columns Header -->
            <div class="bg-gradient-to-r from-secondary to-secondary-light border-b border-gray-100 px-6 py-3">
                <div class="flex items-center justify-between">
                    <h4 class="text-md font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-users text-gray-700 mr-2"></i>
                        Real-time Attendance (<span data-checkin-count><?php echo count($currently_checked_in); ?></span> children)
                    </h4>
                    <div class="flex items-center space-x-4">
                        <a href="<?php echo BASE_URL; ?>children-ministry/attendance-list?date=<?php echo date('Y-m-d'); ?>"
                           class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors flex items-center">
                            <i class="fas fa-list mr-1"></i>View Full List
                        </a>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse" id="live-indicator"></div>
                            <span class="text-xs text-gray-600" id="live-status">Live Updates</span>
                            <span class="text-xs text-gray-500" id="last-update">Just now</span>
                            <div class="hidden" id="loading-indicator">
                                <i class="fas fa-spinner fa-spin text-blue-500 text-xs"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Children List Section -->

            <?php if (empty($currently_checked_in)): ?>
                <div class="p-6">
                    <div class="text-center py-8">
                        <div class="text-gray-400 mb-3">
                            <i class="fas fa-users text-4xl"></i>
                        </div>
                        <p class="text-gray-500">No children currently checked in</p>
                        <p class="text-sm text-gray-400 mt-2">Children will appear here as they check in via QR codes</p>
                    </div>
                </div>
            <?php else: ?>
                <!-- Present and Late Columns -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-0 border-b border-gray-200">
                    <!-- Present Column -->
                    <div class="border-r border-gray-200">
                        <div class="bg-green-50 px-4 py-3 border-b border-green-200">
                            <h5 class="text-sm font-semibold text-green-800 flex items-center">
                                <i class="fas fa-check text-green-600 mr-2"></i>
                                Present (<span id="present-column-count"><?php echo $present_count; ?></span>)
                            </h5>
                        </div>
                        <div class="max-h-80 overflow-y-auto" id="present-children-list">
                            <?php
                            $present_children = array_filter($currently_checked_in, function($child) {
                                $service_time = $child['service_time'] ?? '09:00:00';
                                $check_in_time = date('H:i:s', strtotime($child['check_in_time']));
                                return $check_in_time <= $service_time;
                            });
                            ?>
                            <?php if (empty($present_children)): ?>
                                <div class="p-4 text-center text-gray-500">
                                    <i class="fas fa-check-circle text-2xl mb-2"></i>
                                    <p class="text-sm">No children present yet</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($present_children as $child): ?>
                                    <div class="flex items-center justify-between p-3 border-b border-gray-100 hover:bg-green-25 transition-colors">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-full bg-gradient-to-br from-green-400 to-green-500 flex items-center justify-center mr-3 text-white text-xs font-semibold">
                                                <?php echo strtoupper(substr($child['first_name'], 0, 1) . substr($child['last_name'], 0, 1)); ?>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900 text-sm"><?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?></p>
                                                <p class="text-xs text-gray-500">Age <?php echo $child['age']; ?> • <?php echo $child['is_qr_checkin'] ? 'QR' : 'Manual'; ?></p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-xs font-medium text-green-600"><?php echo date('g:i A', strtotime($child['check_in_time'])); ?></p>
                                            <?php if ($child['security_code']): ?>
                                                <p class="text-xs font-mono text-blue-600"><?php echo $child['security_code']; ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Late Column -->
                    <div>
                        <div class="bg-yellow-50 px-4 py-3 border-b border-yellow-200">
                            <h5 class="text-sm font-semibold text-yellow-800 flex items-center">
                                <i class="fas fa-clock text-yellow-600 mr-2"></i>
                                Late (<span id="late-column-count"><?php echo $late_count; ?></span>)
                            </h5>
                        </div>
                        <div class="max-h-80 overflow-y-auto" id="late-children-list">
                            <?php
                            $late_children = array_filter($currently_checked_in, function($child) {
                                $service_time = $child['service_time'] ?? '09:00:00';
                                $check_in_time = date('H:i:s', strtotime($child['check_in_time']));
                                return $check_in_time > $service_time;
                            });
                            ?>
                            <?php if (empty($late_children)): ?>
                                <div class="p-4 text-center text-gray-500">
                                    <i class="fas fa-clock text-2xl mb-2"></i>
                                    <p class="text-sm">No late arrivals</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($late_children as $child): ?>
                                    <div class="flex items-center justify-between p-3 border-b border-gray-100 hover:bg-yellow-25 transition-colors">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-500 flex items-center justify-center mr-3 text-white text-xs font-semibold">
                                                <?php echo strtoupper(substr($child['first_name'], 0, 1) . substr($child['last_name'], 0, 1)); ?>
                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900 text-sm"><?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?></p>
                                                <p class="text-xs text-gray-500">Age <?php echo $child['age']; ?> • <?php echo $child['is_qr_checkin'] ? 'QR' : 'Manual'; ?></p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-xs font-medium text-yellow-600"><?php echo date('g:i A', strtotime($child['check_in_time'])); ?></p>
                                            <p class="text-xs text-gray-400">
                                                <?php
                                                $service_time = $child['service_time'] ?? '09:00:00';
                                                $check_in_time = strtotime($child['check_in_time']);
                                                $service_timestamp = strtotime(date('Y-m-d') . ' ' . $service_time);
                                                $late_minutes = round(($check_in_time - $service_timestamp) / 60);
                                                echo $late_minutes . ' min late';
                                                ?>
                                            </p>
                                            <?php if ($child['security_code']): ?>
                                                <p class="text-xs font-mono text-blue-600"><?php echo $child['security_code']; ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- REMOVED: Today's Check-in Activity section - Use Real-time Dashboard instead -->
    </div>




</div>

<!-- Chart.js for real-time charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Real-time attendance tracking JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize hourly check-ins chart
    initHourlyCheckinsChart();

    // Initial data load
    refreshAttendanceData();

    // Start real-time updates
    startRealTimeUpdates();

    console.log('🚀 Children Ministry Dashboard initialized with auto-refresh every 30 seconds');
});

// Initialize hourly check-ins chart
function initHourlyCheckinsChart() {
    const ctx = document.getElementById('hourlyCheckinsChart');
    if (!ctx) return;

    const hourlyData = <?php echo json_encode($qr_attendance_stats['hourly_distribution']); ?>;

    // Prepare data for 24-hour format
    const hours = Array.from({length: 24}, (_, i) => i);
    const checkinCounts = hours.map(hour => {
        const data = hourlyData.find(d => parseInt(d.hour) === hour);
        return data ? parseInt(data.checkins) : 0;
    });

    const qrCounts = hours.map(hour => {
        const data = hourlyData.find(d => parseInt(d.hour) === hour);
        return data ? parseInt(data.qr_checkins) : 0;
    });

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: hours.map(h => h + ':00'),
            datasets: [
                {
                    label: 'Total Check-ins',
                    data: checkinCounts,
                    backgroundColor: 'rgba(59, 130, 246, 0.6)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1
                },
                {
                    label: 'QR Check-ins',
                    data: qrCounts,
                    backgroundColor: 'rgba(147, 51, 234, 0.6)',
                    borderColor: 'rgba(147, 51, 234, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                title: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1
                }
            }
        }
    });
}

// Start real-time updates
function startRealTimeUpdates() {
    // Update every 30 seconds
    setInterval(refreshAttendanceData, 30000);
}

// Refresh attendance data
async function refreshAttendanceData() {
    try {
        console.log('🔄 Refreshing attendance data...', new Date().toLocaleTimeString());

        // Show loading indicator
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('hidden');
        }

        const response = await fetch('<?php echo BASE_URL; ?>children-ministry/real-time-stats');

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📊 Received data:', data);

        if (data.success) {
            updateStatistics(data.stats);
            updateCurrentlyCheckedIn(data.children);
            updateLastUpdateTime();
            console.log('✅ Attendance data refreshed - Present:', data.stats.present_count, 'Late:', data.stats.late_count);
        } else {
            console.error('❌ API returned error:', data.error);
        }
    } catch (error) {
        console.error('❌ Error refreshing attendance data:', error);
    } finally {
        // Hide loading indicator
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.add('hidden');
        }
    }
}

// Update attendance statistics
function updateStatistics(stats) {
    // Update simplified statistics in the header
    updateElement('[data-stat="present_count"]', stats.present_count || 0);
    updateElement('[data-stat="late_count"]', stats.late_count || 0);

    // Update total count in header (multiple elements)
    updateElement('[data-checkin-count]', stats.total_children || 0);

    // Update main stats cards
    updateElement('#late-today-count', stats.late_count || 0);

    // Update column counts
    updateElement('#present-column-count', stats.present_count || 0);
    updateElement('#late-column-count', stats.late_count || 0);
}

// Update currently checked in list with Present/Late columns
function updateCurrentlyCheckedIn(checkedInData) {
    if (!checkedInData) return;

    console.log('📋 Updating children lists with', checkedInData.length, 'children:', checkedInData);

    // Separate children into present and late
    const presentChildren = [];
    const lateChildren = [];

    checkedInData.forEach(child => {
        const serviceTime = child.service_time || '09:00:00';
        const checkInTime = new Date('1970-01-01T' + child.check_in_time.split(' ')[1]).getTime();
        const serviceTimestamp = new Date('1970-01-01T' + serviceTime).getTime();

        if (checkInTime <= serviceTimestamp) {
            presentChildren.push(child);
        } else {
            lateChildren.push(child);
        }
    });

    // Update counts in simplified stats
    updateElement('[data-stat="present_count"]', presentChildren.length);
    updateElement('[data-stat="late_count"]', lateChildren.length);
    updateElement('#late-today-count', lateChildren.length);

    // Update column counts
    updateElement('#present-column-count', presentChildren.length);
    updateElement('#late-column-count', lateChildren.length);

    // Update total count
    updateElement('[data-checkin-count]', checkedInData.length);

    // Update present children list
    updateChildrenColumn('present-children-list', presentChildren, 'present');

    // Update late children list
    updateChildrenColumn('late-children-list', lateChildren, 'late');
}

// Update a specific children column
function updateChildrenColumn(containerId, children, type) {
    console.log(`🔄 Updating ${type} column with ${children.length} children`);
    const container = document.getElementById(containerId);
    if (!container) {
        console.warn(`⚠️ Container not found: ${containerId}`);
        return;
    }

    if (children.length === 0) {
        const emptyIcon = type === 'present' ? 'fa-check-circle' : 'fa-clock';
        const emptyText = type === 'present' ? 'No children present yet' : 'No late arrivals';
        const colorClass = type === 'present' ? 'text-green-500' : 'text-yellow-500';

        container.innerHTML = `
            <div class="p-4 text-center text-gray-500">
                <i class="fas ${emptyIcon} text-2xl mb-2 ${colorClass}"></i>
                <p class="text-sm">${emptyText}</p>
            </div>
        `;
        return;
    }

    // Performance optimization: limit displayed children to most recent 15
    const maxDisplayChildren = 15;
    const displayChildren = children.slice(0, maxDisplayChildren);
    const hiddenCount = children.length - displayChildren.length;

    // Render the limited children list
    const childrenHtml = displayChildren.map(child => {
        const initials = (child.first_name.charAt(0) + child.last_name.charAt(0)).toUpperCase();
        const colorClass = type === 'present' ? 'from-green-400 to-green-500' : 'from-yellow-400 to-yellow-500';
        const timeColor = type === 'present' ? 'text-green-600' : 'text-yellow-600';
        const hoverClass = type === 'present' ? 'hover:bg-green-25' : 'hover:bg-yellow-25';

        let lateInfo = '';
        if (type === 'late') {
            const serviceTime = child.service_time || '09:00:00';
            const checkInTime = new Date('1970-01-01T' + child.check_in_time.split(' ')[1]);
            const serviceTimestamp = new Date('1970-01-01T' + serviceTime);
            const lateMinutes = Math.round((checkInTime - serviceTimestamp) / (1000 * 60));
            lateInfo = `<p class="text-xs text-gray-400">${lateMinutes} min late</p>`;
        }

        const securityCode = child.security_code ?
            `<p class="text-xs font-mono text-blue-600">${child.security_code}</p>` : '';

        return `
            <div class="flex items-center justify-between p-3 border-b border-gray-100 ${hoverClass} transition-colors">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-gradient-to-br ${colorClass} flex items-center justify-center mr-3 text-white text-xs font-semibold">
                        ${initials}
                    </div>
                    <div>
                        <p class="font-medium text-gray-900 text-sm">${child.first_name} ${child.last_name}</p>
                        <p class="text-xs text-gray-500">Age ${child.age} • ${child.is_qr_checkin ? 'QR' : 'Manual'}</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-xs font-medium ${timeColor}">${formatTime(child.check_in_time)}</p>
                    ${lateInfo}
                    ${securityCode}
                </div>
            </div>
        `;
    }).join('');

    // Add "show more" indicator if there are hidden children
    const showMoreHtml = hiddenCount > 0 ? `
        <div class="p-3 text-center border-t border-gray-200 bg-gray-50">
            <p class="text-xs text-gray-600">
                <i class="fas fa-plus-circle mr-1"></i>
                ${hiddenCount} more ${type} ${hiddenCount === 1 ? 'child' : 'children'}
                <span class="text-gray-400">(showing most recent ${maxDisplayChildren})</span>
            </p>
        </div>
    ` : '';

    container.innerHTML = childrenHtml + showMoreHtml;
}

// Helper function to format time
function formatTime(timeString) {
    const date = new Date(timeString);
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// Helper function to update element content
function updateElement(selector, value) {
    // Handle both ID selectors and attribute selectors
    let elements = [];

    if (selector.startsWith('#')) {
        // ID selector - single element
        const element = document.querySelector(selector);
        if (element) elements.push(element);
    } else if (selector.startsWith('[')) {
        // Attribute selector - potentially multiple elements
        elements = Array.from(document.querySelectorAll(selector));
    } else {
        // Assume it's an ID without #
        const element = document.getElementById(selector);
        if (element) elements.push(element);
    }

    if (elements.length > 0) {
        elements.forEach(element => {
            element.textContent = value;
        });
        // Only log for debugging if needed
        // console.log(`✅ Updated ${elements.length} element(s) with ${selector} to: ${value}`);
    } else {
        // Only warn for essential elements, not optional ones
        const optionalElements = ['#present-column-count', '#late-column-count'];
        if (!optionalElements.includes(selector)) {
            console.warn(`⚠️ Element not found for selector: ${selector}`);
        }
    }
}

// Update last update time
function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();

    // Update multiple possible elements
    const lastUpdateElement = document.getElementById('last-update');
    if (lastUpdateElement) {
        lastUpdateElement.textContent = timeString;
    }

    const lastUpdateSpan = document.getElementById('last-update');
    if (lastUpdateSpan) {
        lastUpdateSpan.textContent = timeString;
    }

    // Flash the live indicator
    const liveIndicator = document.getElementById('live-indicator');
    if (liveIndicator) {
        liveIndicator.classList.add('bg-blue-500');
        setTimeout(() => {
            liveIndicator.classList.remove('bg-blue-500');
            liveIndicator.classList.add('bg-green-500');
        }, 500);
    }
}

// Note: refreshAttendanceData function is already defined above

// Toggle chart section visibility
function toggleChartSection() {
    const content = document.getElementById('chart-section-content');
    const icon = document.getElementById('chart-toggle-icon');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.className = 'fas fa-chevron-up';
    } else {
        content.style.display = 'none';
        icon.className = 'fas fa-chevron-down';
    }
}

// Show notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm transition-all duration-300 ${
        type === 'success' ? 'bg-green-500' :
        type === 'error' ? 'bg-red-500' :
        'bg-blue-500'
    }`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Open real-time dashboard
function openRealtimeDashboard() {
    // Check if there's an active QR session
    const qrSession = <?php echo isset($_SESSION['qr_session']) ? json_encode($_SESSION['qr_session']) : 'null'; ?>;

    let url = `<?php echo BASE_URL; ?>children-ministry/realtime-dashboard`;

    if (qrSession && qrSession.token) {
        // Add token for enhanced features (live updates)
        url += `?token=${qrSession.token}`;
        showNotification('Opening live dashboard with real-time updates...', 'success');
    } else {
        // Open dashboard without token (shows today's data without live updates)
        showNotification('Opening dashboard with today\'s attendance data...', 'info');
    }

    // Open in new tab
    window.open(url, '_blank');
}
</script>
