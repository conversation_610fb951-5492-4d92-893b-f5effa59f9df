<?php
/**
 * Comprehensive Test Suite for Family Relationship Functionality
 * 
 * This test suite ensures that family relationship creation, management,
 * and display functionality works correctly and prevents regression.
 * 
 * Run this test after any changes to family relationship code.
 */

require_once 'bootstrap.php';
require_once 'controllers/MemberController.php';
require_once 'models/FamilyRelationship.php';

class FamilyRelationshipTest {
    private $database;
    private $conn;
    private $memberController;
    private $familyRelationship;
    private $test_results = [];
    
    public function __construct() {
        $this->database = new Database();
        $this->conn = $this->database->getConnection();
        $this->memberController = new MemberController($this->database);
        $this->familyRelationship = new FamilyRelationship($this->conn);
    }
    
    /**
     * Run all tests
     */
    public function runAllTests() {
        echo "=== FAMILY RELATIONSHIP TEST SUITE ===\n\n";
        
        $this->testRelationshipCreation();
        $this->testRelationshipValidation();
        $this->testRelationshipQueries();
        $this->testSiblingDetection();
        $this->testErrorHandling();
        $this->testDataIntegrity();
        
        $this->printSummary();
    }
    
    /**
     * Test relationship creation functionality
     */
    private function testRelationshipCreation() {
        echo "TEST SUITE 1: Relationship Creation\n";
        
        // Test 1.1: Valid relationship creation
        $this->runTest('Valid Relationship Creation', function() {
            $reflection = new ReflectionClass($this->memberController);
            $method = $reflection->getMethod('createParentChildRelationship');
            $method->setAccessible(true);
            
            // Use existing test members
            $parent_id = 595;
            $child_id = 596;
            
            // Check if already exists
            if ($this->familyRelationship->relationshipExists($parent_id, $child_id, 'parent')) {
                return true; // Already exists, which is fine
            }
            
            return $method->invoke($this->memberController, $parent_id, $child_id);
        });
        
        // Test 1.2: Duplicate prevention
        $this->runTest('Duplicate Prevention', function() {
            $reflection = new ReflectionClass($this->memberController);
            $method = $reflection->getMethod('createParentChildRelationship');
            $method->setAccessible(true);
            
            $parent_id = 595;
            $child_id = 596;
            
            // Try to create duplicate
            $method->invoke($this->memberController, $parent_id, $child_id);
            
            // Check count
            $query = "SELECT COUNT(*) as count FROM family_relationships WHERE parent_id = ? AND child_id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$parent_id, $child_id]);
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            return $count == 1; // Should be exactly 1
        });
        
        echo "\n";
    }
    
    /**
     * Test relationship validation
     */
    private function testRelationshipValidation() {
        echo "TEST SUITE 2: Relationship Validation\n";
        
        // Test 2.1: Invalid parent ID
        $this->runTest('Invalid Parent ID Rejection', function() {
            $reflection = new ReflectionClass($this->memberController);
            $method = $reflection->getMethod('createParentChildRelationship');
            $method->setAccessible(true);
            
            $result = $method->invoke($this->memberController, 99999, 596);
            return $result === false; // Should fail
        });
        
        // Test 2.2: Invalid child ID
        $this->runTest('Invalid Child ID Rejection', function() {
            $reflection = new ReflectionClass($this->memberController);
            $method = $reflection->getMethod('createParentChildRelationship');
            $method->setAccessible(true);
            
            $result = $method->invoke($this->memberController, 595, 99999);
            return $result === false; // Should fail
        });
        
        // Test 2.3: Relationship existence check
        $this->runTest('Relationship Existence Check', function() {
            $exists = $this->familyRelationship->relationshipExists(595, 596, 'parent');
            $not_exists = $this->familyRelationship->relationshipExists(99999, 99998, 'parent');
            
            return $exists && !$not_exists;
        });
        
        echo "\n";
    }
    
    /**
     * Test relationship queries
     */
    private function testRelationshipQueries() {
        echo "TEST SUITE 3: Relationship Queries\n";
        
        // Test 3.1: Get children by parent
        $this->runTest('Get Children by Parent', function() {
            $children_stmt = $this->familyRelationship->getChildrenByParent(595);
            $children = $children_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return count($children) > 0;
        });
        
        // Test 3.2: Get parents by child
        $this->runTest('Get Parents by Child', function() {
            $parents_stmt = $this->familyRelationship->getParentsByChild(596);
            $parents = $parents_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return count($parents) > 0;
        });
        
        echo "\n";
    }
    
    /**
     * Test sibling detection
     */
    private function testSiblingDetection() {
        echo "TEST SUITE 4: Sibling Detection\n";
        
        // Test 4.1: Get siblings
        $this->runTest('Sibling Detection', function() {
            $siblings_stmt = $this->familyRelationship->getSiblingsByChild(596);
            $siblings = $siblings_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Should find at least one sibling (597)
            return count($siblings) > 0;
        });
        
        // Test 4.2: Sibling symmetry
        $this->runTest('Sibling Symmetry', function() {
            $siblings_596 = $this->familyRelationship->getSiblingsByChild(596)->fetchAll(PDO::FETCH_ASSOC);
            $siblings_597 = $this->familyRelationship->getSiblingsByChild(597)->fetchAll(PDO::FETCH_ASSOC);
            
            // Both should have siblings
            return count($siblings_596) > 0 && count($siblings_597) > 0;
        });
        
        echo "\n";
    }
    
    /**
     * Test error handling
     */
    private function testErrorHandling() {
        echo "TEST SUITE 5: Error Handling\n";
        
        // Test 5.1: Graceful failure on invalid data
        $this->runTest('Graceful Error Handling', function() {
            try {
                $reflection = new ReflectionClass($this->memberController);
                $method = $reflection->getMethod('createParentChildRelationship');
                $method->setAccessible(true);
                
                // This should fail gracefully
                $result = $method->invoke($this->memberController, null, null);
                return $result === false;
            } catch (Exception $e) {
                // Exception is also acceptable
                return true;
            }
        });
        
        echo "\n";
    }
    
    /**
     * Test data integrity
     */
    private function testDataIntegrity() {
        echo "TEST SUITE 6: Data Integrity\n";
        
        // Test 6.1: No orphaned relationships
        $this->runTest('No Orphaned Relationships', function() {
            $query = "SELECT COUNT(*) as count FROM family_relationships fr 
                      LEFT JOIN members p ON fr.parent_id = p.id 
                      LEFT JOIN members c ON fr.child_id = c.id 
                      WHERE p.id IS NULL OR c.id IS NULL";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $orphaned = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            return $orphaned == 0;
        });
        
        // Test 6.2: No self-referencing relationships
        $this->runTest('No Self-Referencing Relationships', function() {
            $query = "SELECT COUNT(*) as count FROM family_relationships WHERE parent_id = child_id";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $self_refs = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            return $self_refs == 0;
        });
        
        echo "\n";
    }
    
    /**
     * Run a single test
     */
    private function runTest($test_name, $test_function) {
        try {
            $result = $test_function();
            if ($result) {
                echo "   ✅ $test_name: PASS\n";
                $this->test_results[$test_name] = 'PASS';
            } else {
                echo "   ❌ $test_name: FAIL\n";
                $this->test_results[$test_name] = 'FAIL';
            }
        } catch (Exception $e) {
            echo "   ❌ $test_name: ERROR - " . $e->getMessage() . "\n";
            $this->test_results[$test_name] = 'ERROR';
        }
    }
    
    /**
     * Print test summary
     */
    private function printSummary() {
        echo "=== TEST SUMMARY ===\n";
        
        $total = count($this->test_results);
        $passed = count(array_filter($this->test_results, function($r) { return $r === 'PASS'; }));
        $failed = count(array_filter($this->test_results, function($r) { return $r === 'FAIL'; }));
        $errors = count(array_filter($this->test_results, function($r) { return $r === 'ERROR'; }));
        
        echo "Total Tests: $total\n";
        echo "Passed: $passed\n";
        echo "Failed: $failed\n";
        echo "Errors: $errors\n\n";
        
        if ($passed === $total) {
            echo "🎉 ALL TESTS PASSED! Family relationship functionality is working correctly.\n";
        } else {
            echo "⚠️  Some tests failed. Review the issues above.\n";
        }
        
        echo "\n=== TEST SUITE COMPLETE ===\n";
    }
}

// Run the tests
$test_suite = new FamilyRelationshipTest();
$test_suite->runAllTests();
?>
