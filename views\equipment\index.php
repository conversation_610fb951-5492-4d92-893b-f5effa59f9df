<div class="container mx-auto max-w-6xl px-4 py-6">
    <!-- Header Section with Modern Design -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-1">Equipment Management</h1>
            <p class="text-sm text-gray-600">Track and manage church equipment and assets</p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mt-4 md:mt-0">
            <a href="<?php echo BASE_URL; ?>equipment/add" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center justify-center">
                <i class="fas fa-plus-circle mr-2"></i> Add Equipment
            </a>
        </div>
    </div>

    <!-- Hero Banner -->
    <div class="bg-gradient-to-r from-primary to-primary-light rounded-lg shadow-md p-6 mb-8 text-white">
        <div class="flex flex-col md:flex-row items-center justify-between">
            <div class="mb-6 md:mb-0 md:mr-6">
                <h2 class="text-2xl font-bold mb-2">Equipment Dashboard</h2>
                <p class="opacity-90 mb-4">Manage and track all church equipment and assets efficiently</p>
                <div class="flex flex-wrap gap-3">
                    <a href="<?php echo BASE_URL; ?>equipment/inventory" class="bg-white text-primary hover:bg-gray-100 py-2 px-4 rounded-md flex items-center text-sm font-medium transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-clipboard-list mr-2"></i> View Inventory
                    </a>
                    <a href="<?php echo BASE_URL; ?>equipment/maintenance" class="bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-md flex items-center text-sm font-medium transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-tools mr-2"></i> Maintenance
                    </a>
                    <a href="<?php echo BASE_URL; ?>equipment/reports" class="bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-md flex items-center text-sm font-medium transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-chart-bar mr-2"></i> Reports
                    </a>
                </div>
            </div>
            <div class="flex-shrink-0 bg-white/20 p-4 rounded-lg shadow-md">
                <div class="flex items-center space-x-3">
                    <div class="p-3 bg-white rounded-full shadow-sm">
                        <i class="fas fa-tools text-primary text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium opacity-75">Total Equipment</p>
                        <h3 class="text-2xl font-bold"><?php echo $stats['total']; ?></h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md p-4 text-white transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium opacity-75">Total Equipment</p>
                    <h3 class="text-2xl font-bold"><?php echo $stats['total']; ?></h3>
                </div>
                <div class="bg-white bg-opacity-30 rounded-full p-3 shadow-inner">
                    <i class="fas fa-tools text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md p-4 text-white transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium opacity-75">Excellent Condition</p>
                    <h3 class="text-2xl font-bold"><?php echo $stats['excellent']; ?></h3>
                </div>
                <div class="bg-white bg-opacity-30 rounded-full p-3 shadow-inner">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-blue-400 to-blue-500 rounded-lg shadow-md p-4 text-white transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium opacity-75">Good Condition</p>
                    <h3 class="text-2xl font-bold"><?php echo $stats['good']; ?></h3>
                </div>
                <div class="bg-white bg-opacity-30 rounded-full p-3 shadow-inner">
                    <i class="fas fa-thumbs-up text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg shadow-md p-4 text-white transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium opacity-75">Needs Attention</p>
                    <h3 class="text-2xl font-bold"><?php echo $stats['fair'] + $stats['poor']; ?></h3>
                </div>
                <div class="bg-white bg-opacity-30 rounded-full p-3 shadow-inner">
                    <i class="fas fa-exclamation-circle text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-md p-4 text-white transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium opacity-75">Recent Maintenance</p>
                    <h3 class="text-2xl font-bold"><?php echo isset($maintenance_stats['recent_maintenance']) ? $maintenance_stats['recent_maintenance'] : 0; ?></h3>
                </div>
                <div class="bg-white bg-opacity-30 rounded-full p-3 shadow-inner">
                    <i class="fas fa-wrench text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Equipment Inventory Preview -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300">
                <div class="p-4 border-b flex justify-between items-center bg-gradient-to-r from-gray-50 to-white">
                    <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-clipboard-list text-primary mr-2"></i> Equipment Inventory
                    </h2>
                    <a href="<?php echo BASE_URL; ?>equipment/inventory" class="text-primary hover:text-primary-dark text-sm flex items-center group transition-all duration-300">
                        View All <i class="fas fa-chevron-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Condition</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php
                            $count = 0;
                            if (!empty($equipments)) {
                                foreach ($equipments as $equipment) {
                                    if ($count >= 5) break; // Show only 5 items
                                    $count++;

                                    // Determine condition class
                                    $condition_class = '';
                                    switch ($equipment['status']) {
                                        case 'excellent':
                                            $condition_class = 'bg-green-100 text-green-800';
                                            break;
                                        case 'good':
                                            $condition_class = 'bg-blue-100 text-blue-800';
                                            break;
                                        case 'fair':
                                            $condition_class = 'bg-yellow-100 text-yellow-800';
                                            break;
                                        case 'poor':
                                            $condition_class = 'bg-red-100 text-red-800';
                                            break;
                                    }
                            ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900"><?php echo $equipment['name']; ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500"><?php echo $equipment['category']; ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $condition_class; ?>">
                                                <?php echo ucfirst($equipment['status']); ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $equipment['id']; ?>" class="text-primary hover:text-primary-dark" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo BASE_URL; ?>equipment/edit?id=<?php echo $equipment['id']; ?>" class="text-blue-600 hover:text-blue-900" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $equipment['id']; ?>)" class="text-red-600 hover:text-red-900" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                            <?php
                                }
                            } else {
                            ?>
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">
                                        <div class="flex flex-col items-center justify-center py-4">
                                            <i class="fas fa-clipboard-list text-gray-400 text-3xl mb-2"></i>
                                            <p>No equipment records found</p>
                                            <a href="<?php echo BASE_URL; ?>equipment/add" class="mt-2 text-primary hover:text-primary-dark">Add your first equipment</a>
                                        </div>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Quick Tips Section -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden h-full hover:shadow-lg transition-all duration-300">
                <div class="p-4 border-b bg-gradient-to-r from-gray-50 to-white">
                    <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i> Quick Tips
                    </h2>
                </div>
                <div class="p-4">
                    <div class="space-y-4">
                        <div class="border-l-4 border-primary pl-3 py-2 hover:bg-gray-50 transition-colors duration-300 rounded-r-md">
                            <h4 class="text-sm font-medium text-gray-900">Inventory Management</h4>
                            <p class="text-xs text-gray-600 mt-1">Use the Inventory page to view all equipment details and search by category or location.</p>
                        </div>
                        <div class="border-l-4 border-blue-500 pl-3 py-2 hover:bg-gray-50 transition-colors duration-300 rounded-r-md">
                            <h4 class="text-sm font-medium text-gray-900">Regular Maintenance</h4>
                            <p class="text-xs text-gray-600 mt-1">Schedule regular maintenance for equipment to extend their lifespan and ensure optimal performance.</p>
                        </div>
                        <div class="border-l-4 border-purple-500 pl-3 py-2 hover:bg-gray-50 transition-colors duration-300 rounded-r-md">
                            <h4 class="text-sm font-medium text-gray-900">QR Code Tracking</h4>
                            <p class="text-xs text-gray-600 mt-1">Generate QR codes for equipment to easily access their details and maintenance history.</p>
                        </div>
                        <div class="border-l-4 border-yellow-500 pl-3 py-2 hover:bg-gray-50 transition-colors duration-300 rounded-r-md">
                            <h4 class="text-sm font-medium text-gray-900">Reports & Analytics</h4>
                            <p class="text-xs text-gray-600 mt-1">View equipment reports to analyze maintenance costs and equipment distribution.</p>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100 text-center">
                        <a href="<?php echo BASE_URL; ?>equipment/reports" class="text-primary hover:text-primary-dark text-sm flex items-center justify-center group transition-all duration-300">
                            <i class="fas fa-chart-bar mr-1"></i> View Reports <i class="fas fa-arrow-right ml-1 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden overflow-y-auto h-full w-full z-50 backdrop-blur-sm transition-all duration-300">
    <div class="relative top-20 mx-auto p-6 border border-gray-200 w-96 shadow-2xl rounded-xl bg-white transform transition-all duration-300 scale-95 opacity-0">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 shadow-inner">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-xl leading-6 font-semibold text-gray-900 mt-4">Delete Equipment</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Are you sure you want to delete this equipment? This action cannot be undone.</p>
            </div>
            <div class="flex justify-center mt-4 px-4 py-3 space-x-4">
                <button id="cancelDelete" class="bg-white border border-gray-300 px-5 py-2.5 rounded-lg text-gray-700 hover:bg-gray-100 transition-all duration-300 shadow-sm hover:shadow">Cancel</button>
                <a id="confirmDelete" href="#" class="bg-gradient-to-r from-red-600 to-red-500 px-5 py-2.5 rounded-lg text-white hover:from-red-700 hover:to-red-600 shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">Delete</a>
            </div>
        </div>
    </div>
</div>

<style>
    /* Animation keyframes */
    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    /* Apply animations to elements */
    .animate-fadeInUp {
        animation: fadeInUp 0.6s ease forwards;
        opacity: 0;
    }

    .animate-pulse {
        animation: pulse 2s infinite;
    }

    /* Animation delays */
    .delay-100 { animation-delay: 0.1s; }
    .delay-200 { animation-delay: 0.2s; }
    .delay-300 { animation-delay: 0.3s; }
    .delay-400 { animation-delay: 0.4s; }
    .delay-500 { animation-delay: 0.5s; }
</style>

<script>
    // Delete confirmation with enhanced animation
    function confirmDelete(id) {
        const modal = document.getElementById('deleteModal');
        const confirmBtn = document.getElementById('confirmDelete');
        const modalContent = modal.querySelector('.relative');

        // Show modal with animation
        modal.classList.remove('hidden');
        setTimeout(() => {
            modalContent.classList.add('scale-100');
            modalContent.classList.remove('scale-95', 'opacity-0');
        }, 10);

        confirmBtn.href = '<?php echo BASE_URL; ?>equipment/' + id;

        document.getElementById('cancelDelete').addEventListener('click', function() {
            // Hide with animation
            modalContent.classList.add('scale-95', 'opacity-0');
            modalContent.classList.remove('scale-100');

            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        });

        // Close when clicking outside the modal
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                document.getElementById('cancelDelete').click();
            }
        });
    }

    // Initialize animations and interactions
    document.addEventListener('DOMContentLoaded', function() {
        // Apply animations to hero banner
        document.querySelector('.bg-gradient-to-r.from-primary').classList.add('animate-fadeInUp');

        // Apply animations to statistics cards with delays
        const statCards = document.querySelectorAll('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 > div');
        statCards.forEach((card, index) => {
            card.classList.add('animate-fadeInUp', `delay-${(index + 1) * 100}`);
        });

        // Apply animations to inventory and quick tips sections
        const sections = document.querySelectorAll('.grid.grid-cols-1.lg\\:grid-cols-3 > div');
        sections.forEach((section, index) => {
            section.classList.add('animate-fadeInUp', `delay-${(index + 5) * 100}`);
        });
    });
</script>
