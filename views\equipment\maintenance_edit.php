<div class="container mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Edit Maintenance Record</h1>
        <div class="flex space-x-2">
            <a href="<?php echo BASE_URL; ?>equipment/maintenance" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Maintenance
            </a>
            <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->maintenance->equipment_id; ?>" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-eye mr-2"></i> View Equipment
            </a>
        </div>
    </div>

    <!-- Maintenance Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <form action="<?php echo BASE_URL; ?>equipment/maintenance/update" method="POST">
            <input type="hidden" name="id" value="<?php echo $this->maintenance->id; ?>">
            <?php if (isset($_GET['redirect_to_equipment']) && $_GET['redirect_to_equipment'] == '1') : ?>
                <input type="hidden" name="redirect_to_equipment" value="1">
            <?php endif; ?>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Equipment -->
                <div>
                    <label for="equipment_id" class="block text-sm font-medium text-gray-700 mb-1">Equipment <span class="text-red-500">*</span></label>
                    <select id="equipment_id" name="equipment_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required>
                        <option value="">Select Equipment</option>
                        <?php foreach ($equipments as $equipment) : ?>
                            <option value="<?php echo $equipment['id']; ?>" <?php echo ($this->maintenance->equipment_id == $equipment['id']) ? 'selected' : ''; ?>>
                                <?php echo $equipment['name']; ?> (<?php echo ucfirst($equipment['category']); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Maintenance Date -->
                <div>
                    <label for="maintenance_date" class="block text-sm font-medium text-gray-700 mb-1">Maintenance Date <span class="text-red-500">*</span></label>
                    <input type="date" id="maintenance_date" name="maintenance_date" value="<?php echo $this->maintenance->maintenance_date; ?>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required>
                </div>

                <!-- Cost -->
                <div>
                    <label for="cost" class="block text-sm font-medium text-gray-700 mb-1">Cost (GH₵)</label>
                    <input type="number" id="cost" name="cost" value="<?php echo $this->maintenance->cost; ?>" step="0.01" min="0" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>

                <!-- Performed By -->
                <div>
                    <label for="performed_by" class="block text-sm font-medium text-gray-700 mb-1">Performed By</label>
                    <input type="text" id="performed_by" name="performed_by" value="<?php echo $this->maintenance->performed_by; ?>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description <span class="text-red-500">*</span></label>
                    <textarea id="description" name="description" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" required><?php echo $this->maintenance->description; ?></textarea>
                </div>
            </div>

            <div class="mt-8 flex justify-end">
                <?php if (isset($_GET['redirect_to_equipment']) && $_GET['redirect_to_equipment'] == '1') : ?>
                    <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $this->maintenance->equipment_id; ?>" class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-6 rounded-md mr-2">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                <?php else : ?>
                    <a href="<?php echo BASE_URL; ?>equipment/maintenance" class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-6 rounded-md mr-2">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                <?php endif; ?>
                <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-6 rounded-md">
                    <i class="fas fa-save mr-2"></i> Update Maintenance Record
                </button>
            </div>
        </form>
    </div>
</div>
