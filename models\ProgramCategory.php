<?php
/**
 * Program Category Model for Church Program & Activities Planner
 */

class ProgramCategory {
    // Database connection and table name
    private $conn;
    private $table_name = "program_categories";

    // Object properties
    public $id;
    public $name;
    public $description;
    public $color_code;
    public $icon;
    public $is_active;
    public $created_at;
    public $updated_at;

    // Error handling
    public $error;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db = null) {
        $this->conn = $db;

        // If no database connection is provided, get one from the Database class
        if ($this->conn === null) {
            require_once 'config/database.php';
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }

    /**
     * Get all categories
     *
     * @param bool $active_only
     * @return PDOStatement
     */
    public function getAll($active_only = true) {
        $query = "SELECT * FROM " . $this->table_name;
        
        if ($active_only) {
            $query .= " WHERE is_active = 1";
        }
        
        $query .= " ORDER BY name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get active categories
     *
     * @return PDOStatement
     */
    public function getActive() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE is_active = 1 ORDER BY name ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get category by ID
     *
     * @param int $id
     * @return array|false
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create new category
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (name, description, color_code, icon, is_active)
                  VALUES
                  (:name, :description, :color_code, :icon, :is_active)";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->color_code = htmlspecialchars(strip_tags($this->color_code));
        $this->icon = htmlspecialchars(strip_tags($this->icon));

        // Set defaults
        if (empty($this->color_code)) {
            $this->color_code = '#3B82F6';
        }
        if (empty($this->icon)) {
            $this->icon = 'fas fa-calendar';
        }
        if (!isset($this->is_active)) {
            $this->is_active = 1;
        }

        // Bind parameters
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':color_code', $this->color_code);
        $stmt->bindParam(':icon', $this->icon);
        $stmt->bindParam(':is_active', $this->is_active);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Update category
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET name = :name,
                      description = :description,
                      color_code = :color_code,
                      icon = :icon,
                      is_active = :is_active,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->color_code = htmlspecialchars(strip_tags($this->color_code));
        $this->icon = htmlspecialchars(strip_tags($this->icon));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':color_code', $this->color_code);
        $stmt->bindParam(':icon', $this->icon);
        $stmt->bindParam(':is_active', $this->is_active);

        return $stmt->execute();
    }

    /**
     * Get program count for category
     *
     * @param int $category_id
     * @return PDOStatement
     */
    public function getProgramCount($category_id) {
        $query = "SELECT COUNT(*) FROM church_programs WHERE category_id = :category_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Delete category
     *
     * @return bool
     */
    public function delete() {
        try {
            // Clear any previous errors
            $this->error = null;

            // Check if category is being used by any programs
            $check_query = "SELECT COUNT(*) as count FROM church_programs WHERE category_id = :id";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(':id', $this->id);
            $check_stmt->execute();
            $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

            if ($result['count'] > 0) {
                $this->error = 'Cannot delete category. It is being used by ' . $result['count'] . ' program(s).';
                return false; // Cannot delete category that's in use
            }

            $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $this->id);

            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Error deleting category. Please try again.';
                return false;
            }
        } catch (Exception $e) {
            $this->error = 'Database error: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * Get category with program count
     *
     * @return PDOStatement
     */
    public function getCategoriesWithProgramCount() {
        $query = "SELECT pc.*, 
                         COUNT(p.id) as program_count,
                         COUNT(CASE WHEN p.status = 'planned' THEN 1 END) as planned_count,
                         COUNT(CASE WHEN p.status = 'in_progress' THEN 1 END) as in_progress_count,
                         COUNT(CASE WHEN p.status = 'completed' THEN 1 END) as completed_count
                  FROM " . $this->table_name . " pc
                  LEFT JOIN church_programs p ON pc.id = p.category_id
                  WHERE pc.is_active = 1
                  GROUP BY pc.id
                  ORDER BY pc.name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Validate category data
     *
     * @return array
     */
    public function validate() {
        $errors = [];

        if (empty($this->name)) {
            $errors[] = "Category name is required";
        }

        if (!empty($this->color_code) && !preg_match('/^#[0-9A-Fa-f]{6}$/', $this->color_code)) {
            $errors[] = "Color code must be a valid hex color (e.g., #3B82F6)";
        }

        // Check for duplicate name (excluding current record if updating)
        $check_query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE name = :name";
        if (!empty($this->id)) {
            $check_query .= " AND id != :id";
        }

        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(':name', $this->name);
        if (!empty($this->id)) {
            $check_stmt->bindParam(':id', $this->id);
        }
        $check_stmt->execute();
        $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

        if ($result['count'] > 0) {
            $errors[] = "A category with this name already exists";
        }

        return $errors;
    }

    /**
     * Get available icons for categories
     *
     * @return array
     */
    public static function getAvailableIcons() {
        return [
            'fas fa-pray' => 'Prayer/Worship',
            'fas fa-users' => 'Groups/Community',
            'fas fa-child' => 'Children',
            'fas fa-bullhorn' => 'Outreach',
            'fas fa-graduation-cap' => 'Education/Training',
            'fas fa-handshake' => 'Fellowship',
            'fas fa-star' => 'Special Events',
            'fas fa-donate' => 'Fundraising',
            'fas fa-heart' => 'Community Service',
            'fas fa-cogs' => 'Administrative',
            'fas fa-music' => 'Music/Arts',
            'fas fa-calendar' => 'General Events',
            'fas fa-home' => 'Family',
            'fas fa-globe' => 'Missions',
            'fas fa-book' => 'Bible Study'
        ];
    }

    /**
     * Get default colors for categories
     *
     * @return array
     */
    public static function getDefaultColors() {
        return [
            '#8B5CF6', // Purple
            '#10B981', // Green
            '#F59E0B', // Yellow
            '#EF4444', // Red
            '#3B82F6', // Blue
            '#06B6D4', // Cyan
            '#8B5CF6', // Purple
            '#059669', // Emerald
            '#DC2626', // Red
            '#6B7280', // Gray
            '#EC4899', // Pink
            '#F97316', // Orange
            '#84CC16', // Lime
            '#6366F1', // Indigo
            '#14B8A6'  // Teal
        ];
    }

    /**
     * Toggle category status
     *
     * @param int $status
     * @return bool
     */
    public function toggleStatus($status) {
        $query = "UPDATE " . $this->table_name . " SET is_active = :status WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $this->id);
        return $stmt->execute();
    }
}
