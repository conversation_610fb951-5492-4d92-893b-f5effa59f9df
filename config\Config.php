<?php
/**
 * Configuration Manager
 * 
 * Centralized configuration management for the finance system.
 * Supports environment-specific configurations and feature flags.
 * 
 * @package Config
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

class Config
{
    /**
     * @var array Configuration data
     */
    private static $config = [];

    /**
     * @var string Current environment
     */
    private static $environment = 'production';

    /**
     * @var bool Whether configuration has been loaded
     */
    private static $loaded = false;

    /**
     * Load configuration
     * 
     * @param string|null $environment Environment to load
     * @return void
     */
    public static function load(?string $environment = null): void
    {
        if (self::$loaded) {
            return;
        }

        // Determine environment
        self::$environment = $environment ?? $_ENV['APP_ENV'] ?? 'production';

        // Load base configuration
        self::loadBaseConfig();

        // Load environment-specific configuration
        self::loadEnvironmentConfig();

        // Load feature flags
        self::loadFeatureFlags();

        self::$loaded = true;
    }

    /**
     * Get configuration value
     * 
     * @param string $key Configuration key (dot notation supported)
     * @param mixed $default Default value if key not found
     * @return mixed Configuration value
     */
    public static function get(string $key, $default = null)
    {
        if (!self::$loaded) {
            self::load();
        }

        return self::getNestedValue(self::$config, $key, $default);
    }

    /**
     * Set configuration value
     * 
     * @param string $key Configuration key (dot notation supported)
     * @param mixed $value Configuration value
     * @return void
     */
    public static function set(string $key, $value): void
    {
        if (!self::$loaded) {
            self::load();
        }

        self::setNestedValue(self::$config, $key, $value);
    }

    /**
     * Check if configuration key exists
     * 
     * @param string $key Configuration key
     * @return bool Whether key exists
     */
    public static function has(string $key): bool
    {
        if (!self::$loaded) {
            self::load();
        }

        return self::getNestedValue(self::$config, $key, '__NOT_FOUND__') !== '__NOT_FOUND__';
    }

    /**
     * Get current environment
     * 
     * @return string Current environment
     */
    public static function getEnvironment(): string
    {
        return self::$environment;
    }

    /**
     * Check if feature is enabled
     * 
     * @param string $feature Feature name
     * @return bool Whether feature is enabled
     */
    public static function isFeatureEnabled(string $feature): bool
    {
        return self::get("features.{$feature}", false);
    }

    /**
     * Get all configuration
     * 
     * @return array All configuration data
     */
    public static function all(): array
    {
        if (!self::$loaded) {
            self::load();
        }

        return self::$config;
    }

    /**
     * Load base configuration
     * 
     * @return void
     */
    private static function loadBaseConfig(): void
    {
        self::$config = [
            // Application settings
            'app' => [
                'name' => 'ICGC Finance System',
                'version' => '1.0.0',
                'timezone' => 'Africa/Accra',
                'locale' => 'en_US',
                'debug' => false
            ],

            // Database settings
            'database' => [
                'host' => $_ENV['DB_HOST'] ?? 'localhost',
                'port' => $_ENV['DB_PORT'] ?? 3306,
                'name' => $_ENV['DB_NAME'] ?? 'icgc_finance',
                'username' => $_ENV['DB_USERNAME'] ?? 'root',
                'password' => $_ENV['DB_PASSWORD'] ?? '',
                'charset' => 'utf8mb4',
                'options' => [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]
            ],

            // Security settings
            'security' => [
                'csrf_token_name' => 'csrf_token',
                'session_lifetime' => 3600, // 1 hour
                'password_min_length' => 8,
                'max_login_attempts' => 5,
                'lockout_duration' => 900, // 15 minutes
                'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
                'max_file_size' => 5242880, // 5MB
            ],

            // Logging settings
            'logging' => [
                'enabled' => true,
                'level' => 'info',
                'file' => 'logs/app.log',
                'max_files' => 30,
                'audit_enabled' => true
            ],

            // Cache settings
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 3600,
                'prefix' => 'icgc_finance_'
            ],

            // Email settings
            'email' => [
                'enabled' => false,
                'driver' => 'smtp',
                'host' => $_ENV['MAIL_HOST'] ?? '',
                'port' => $_ENV['MAIL_PORT'] ?? 587,
                'username' => $_ENV['MAIL_USERNAME'] ?? '',
                'password' => $_ENV['MAIL_PASSWORD'] ?? '',
                'encryption' => 'tls',
                'from_address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
                'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'ICGC Finance System'
            ],

            // Finance-specific settings
            'finance' => [
                'default_currency' => 'GHS',
                'currency_symbol' => 'GH₵',
                'decimal_places' => 2,
                'date_format' => 'Y-m-d',
                'datetime_format' => 'Y-m-d H:i:s',
                'pagination_limit' => 50,
                'dashboard_cache_ttl' => 300, // 5 minutes
                'export_formats' => ['csv', 'excel', 'pdf'],
                'backup_enabled' => true,
                'backup_frequency' => 'daily'
            ],

            // Feature flags
            'features' => [
                'audit_logging' => true,
                'advanced_reporting' => true,
                'email_notifications' => false,
                'api_access' => false,
                'mobile_app' => false,
                'multi_currency' => false,
                'budget_management' => false,
                'automated_backups' => true,
                'two_factor_auth' => false,
                'ldap_integration' => false
            ]
        ];
    }

    /**
     * Load environment-specific configuration
     * 
     * @return void
     */
    private static function loadEnvironmentConfig(): void
    {
        $envConfigFile = __DIR__ . "/environments/" . self::$environment . ".php";
        
        if (file_exists($envConfigFile)) {
            $envConfig = require $envConfigFile;
            self::$config = array_merge_recursive(self::$config, $envConfig);
        }
    }

    /**
     * Load feature flags from database or file
     * 
     * @return void
     */
    private static function loadFeatureFlags(): void
    {
        // Try to load feature flags from database
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            $stmt = $conn->prepare("SELECT feature_name, is_enabled FROM feature_flags WHERE is_active = 1");
            $stmt->execute();
            $flags = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            foreach ($flags as $feature => $enabled) {
                self::$config['features'][$feature] = (bool)$enabled;
            }
        } catch (Exception $e) {
            // If database is not available, use default feature flags
            error_log("Could not load feature flags from database: " . $e->getMessage());
        }
    }

    /**
     * Get nested configuration value using dot notation
     * 
     * @param array $array Configuration array
     * @param string $key Dot notation key
     * @param mixed $default Default value
     * @return mixed Configuration value
     */
    private static function getNestedValue(array $array, string $key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $array;

        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Set nested configuration value using dot notation
     * 
     * @param array &$array Configuration array
     * @param string $key Dot notation key
     * @param mixed $value Value to set
     * @return void
     */
    private static function setNestedValue(array &$array, string $key, $value): void
    {
        $keys = explode('.', $key);
        $current = &$array;

        foreach ($keys as $k) {
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }

        $current = $value;
    }
}
