<div class="container mx-auto px-4 max-w-7xl">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-green-600 to-teal-700 p-6 text-white">
            <div class="flex flex-row justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Attendance Goals</h1>
                    <p class="text-sm opacity-90 mt-1">Set and track attendance goals for your church</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Attendance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Set New Goal -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-green-50 to-teal-50 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Set New Attendance Goal</h2>
        </div>
        <div class="p-6">
            <form action="<?php echo BASE_URL; ?>attendance/goals" method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <input type="hidden" name="action" value="set_goal">
                
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Goal Name</label>
                    <input type="text" id="name" name="name" placeholder="e.g., Sunday Service Attendance Goal" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 py-2 px-3">
                </div>
                
                <div>
                    <label for="service_id" class="block text-sm font-medium text-gray-700 mb-1">Service</label>
                    <select id="service_id" name="service_id" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 py-2 px-3">
                        <option value="">Select a service</option>
                        <?php foreach ($services as $service) : ?>
                            <option value="<?php echo $service->id; ?>">
                                <?php echo $service->name; ?> (<?php echo ucfirst($service->day_of_week); ?> - <?php echo date('h:i A', strtotime($service->time)); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div>
                    <label for="target" class="block text-sm font-medium text-gray-700 mb-1">Target Attendance</label>
                    <input type="number" id="target" name="target" min="1" placeholder="e.g., 150" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 py-2 px-3">
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="start_date" name="start_date" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 py-2 px-3">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" id="end_date" name="end_date" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 py-2 px-3">
                    </div>
                </div>
                
                <div class="md:col-span-2">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-plus-circle mr-2"></i> Set Attendance Goal
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Current Goals -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Current Attendance Goals</h2>
        </div>
        <div class="p-6">
            <?php if (empty($goals)) : ?>
                <div class="text-center py-8 text-gray-500">
                    <div class="p-3 rounded-full bg-gray-100 text-gray-400 mx-auto mb-3 w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-bullseye text-xl"></i>
                    </div>
                    <p>No attendance goals have been set yet</p>
                    <p class="text-sm text-gray-400 mt-1">Set your first goal using the form above</p>
                </div>
            <?php else : ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php foreach ($goals as $goal) : 
                        $goal_progress = $progress[$goal['id']];
                        $percentage = $goal_progress['percentage'];
                        $color_class = $percentage >= 90 ? 'from-green-500 to-emerald-600' : 
                                      ($percentage >= 70 ? 'from-teal-500 to-green-600' : 
                                      ($percentage >= 50 ? 'from-yellow-500 to-amber-600' : 
                                      'from-red-500 to-pink-600'));
                        $bg_class = $percentage >= 90 ? 'from-green-50 to-emerald-50 border-green-100' : 
                                   ($percentage >= 70 ? 'from-teal-50 to-green-50 border-teal-100' : 
                                   ($percentage >= 50 ? 'from-yellow-50 to-amber-50 border-yellow-100' : 
                                   'from-red-50 to-pink-50 border-red-100'));
                    ?>
                        <div class="bg-gradient-to-br <?php echo $bg_class; ?> rounded-xl p-6 border shadow-sm hover:shadow-md transition-all duration-300">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-base font-bold text-gray-800"><?php echo $goal['name']; ?></h3>
                                <div class="text-xs bg-white px-2 py-1 rounded-full border border-gray-200 text-gray-600">
                                    <?php echo format_date($goal['start_date']); ?> - <?php echo format_date($goal['end_date']); ?>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <?php foreach ($services as $service) : ?>
                                    <?php if ($service->id == $goal['service_id']) : ?>
                                        <div class="text-sm text-gray-600 mb-2">
                                            <i class="fas fa-church text-gray-500 mr-1.5"></i>
                                            <?php echo $service->name; ?> (<?php echo ucfirst($service->day_of_week); ?>)
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                            
                            <div class="mb-4">
                                <div class="flex items-center justify-between mb-1.5">
                                    <div class="text-sm font-medium text-gray-700">Progress</div>
                                    <div class="text-sm font-bold text-gray-800"><?php echo $goal_progress['current']; ?> / <?php echo $goal['target']; ?></div>
                                </div>
                                <div class="w-full bg-white h-3 rounded-full shadow-inner overflow-hidden">
                                    <div class="h-full bg-gradient-to-r <?php echo $color_class; ?> rounded-full" style="width: <?php echo min(100, $percentage); ?>%"></div>
                                </div>
                                <div class="flex items-center justify-between mt-1.5">
                                    <div class="text-xs text-gray-500"><?php echo $percentage; ?>% complete</div>
                                    <?php if ($goal_progress['remaining'] > 0) : ?>
                                        <div class="text-xs text-gray-500"><?php echo $goal_progress['remaining']; ?> more needed</div>
                                    <?php else : ?>
                                        <div class="text-xs text-green-600 font-medium">Goal achieved!</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="flex justify-end space-x-2">
                                <button type="button" class="text-gray-600 hover:text-gray-800 text-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="text-red-600 hover:text-red-800 text-sm">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Goal Achievement Tips -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Tips for Achieving Attendance Goals</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="text-base font-semibold text-gray-800">Send Reminders</h3>
                    </div>
                    <p class="text-sm text-gray-600">
                        Use the SMS system to send reminders to members before services. Consistent communication increases attendance rates.
                    </p>
                    <div class="mt-4">
                        <a href="<?php echo BASE_URL; ?>sms/create" class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                            <span>Send SMS Reminders</span>
                            <i class="fas fa-arrow-right ml-1.5 text-xs"></i>
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-3">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <h3 class="text-base font-semibold text-gray-800">Follow Up</h3>
                    </div>
                    <p class="text-sm text-gray-600">
                        Reach out to members who have been absent for multiple services. Personal contact makes a significant difference.
                    </p>
                    <div class="mt-4">
                        <a href="<?php echo BASE_URL; ?>attendance/reminders" class="text-green-600 hover:text-green-800 text-sm font-medium flex items-center">
                            <span>Absent Member Follow-up</span>
                            <i class="fas fa-arrow-right ml-1.5 text-xs"></i>
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center mr-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="text-base font-semibold text-gray-800">Track Progress</h3>
                    </div>
                    <p class="text-sm text-gray-600">
                        Regularly monitor attendance trends and patterns. Use comparative analysis to identify what strategies are working.
                    </p>
                    <div class="mt-4">
                        <a href="<?php echo BASE_URL; ?>attendance/comparative-analysis" class="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center">
                            <span>Analyze Attendance Trends</span>
                            <i class="fas fa-arrow-right ml-1.5 text-xs"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set default dates
        const today = new Date();
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        
        // Set default start date to today
        startDateInput.valueAsDate = today;
        
        // Set default end date to 30 days from now
        const endDate = new Date();
        endDate.setDate(today.getDate() + 30);
        endDateInput.valueAsDate = endDate;
    });
</script>
