<?php
/**
 * Simple Member Search API for QR Attendance
 * Direct database query without models
 */

// Set headers for JSON response
header('Content-Type: application/json');

// CRITICAL SECURITY FIX: Restrict CORS to application domain only
// The wildcard '*' is dangerous and allows any website to access this API
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false || 
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
    // If origin is not allowed, we don't set the CORS header
    // This will cause the browser to block the request
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET');

// Include database config
require_once dirname(__DIR__) . '/config/database.php';

// Simple sanitize function
function simple_sanitize($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

try {
    // Get search parameter
    $search = isset($_GET['search']) ? simple_sanitize($_GET['search']) : '';
    
    if (empty($search) || strlen($search) < 2) {
        echo json_encode([
            'success' => false,
            'message' => 'Search term must be at least 2 characters',
            'members' => []
        ]);
        exit;
    }
    
    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Search query
    $query = "SELECT id, first_name, last_name, email, phone_number, profile_picture 
              FROM members 
              WHERE (first_name LIKE ? 
                     OR last_name LIKE ? 
                     OR phone_number LIKE ? 
                     OR email LIKE ?)
              AND member_status = 'active'
              ORDER BY first_name, last_name 
              LIMIT 20";
    
    $stmt = $conn->prepare($query);
    $searchParam = "%{$search}%";
    $stmt->execute([$searchParam, $searchParam, $searchParam, $searchParam]);
    
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format response
    echo json_encode([
        'success' => true,
        'members' => $members,
        'count' => count($members),
        'search_term' => $search
    ]);
    
} catch (Exception $e) {
    error_log("Member Search API Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Search failed: ' . $e->getMessage(),
        'members' => []
    ]);
}
?>
