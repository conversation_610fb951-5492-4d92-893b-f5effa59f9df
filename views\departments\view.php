<div class="container mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><?php echo $department_name; ?> Department</h1>
        <a href="<?php echo BASE_URL; ?>departments" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-arrow-left mr-2"></i> Back to Departments
        </a>
    </div>

    <!-- Department Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-primary">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-primary-light text-white mr-4">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Total Members</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo count($members); ?></h3>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-blue-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500 mr-4">
                    <i class="fas fa-user-tie text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Leaders</p>
                    <h3 class="text-2xl font-bold text-gray-800">
                        <?php
                            $leaders = array_filter($members, function($member) {
                                return $member['role'] === 'leader';
                            });
                            echo count($leaders);
                        ?>
                    </h3>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-yellow-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4">
                    <i class="fas fa-user-cog text-xl"></i>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Workers</p>
                    <h3 class="text-2xl font-bold text-gray-800">
                        <?php
                            $workers = array_filter($members, function($member) {
                                return $member['role'] === 'worker';
                            });
                            echo count($workers);
                        ?>
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Members -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-4 bg-gray-50 border-b flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800">Department Members</h2>
            <div class="flex space-x-2">
                <a href="<?php echo BASE_URL; ?>members/add?department=<?php echo $department; ?>" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center text-sm">
                    <i class="fas fa-user-plus mr-2"></i> Add New Member
                </a>
                <a href="<?php echo BASE_URL; ?>departments/report?department=<?php echo $department; ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md flex items-center text-sm">
                    <i class="fas fa-file-alt mr-2"></i> Generate Report
                </a>
            </div>
        </div>

        <?php if (empty($members)) : ?>
            <div class="p-6 text-center">
                <p class="text-gray-500">No members in this department</p>
                <a href="<?php echo BASE_URL; ?>members/add?department=<?php echo $department; ?>" class="inline-block mt-4 text-primary hover:underline">
                    <i class="fas fa-user-plus mr-1"></i> Add your first member
                </a>
            </div>
        <?php else : ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Membership Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($members as $member) : ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <?php if (!empty($member['profile_picture'])) : ?>
                                                <img class="h-10 w-10 rounded-full" src="<?php echo BASE_URL . 'uploads/' . $member['profile_picture']; ?>" alt="<?php echo $member['first_name']; ?>">
                                            <?php else : ?>
                                                <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 text-sm font-bold">
                                                    <?php echo strtoupper(substr($member['first_name'], 0, 1) . substr($member['last_name'], 0, 1)); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900"><?php echo $member['first_name'] . ' ' . $member['last_name']; ?></div>
                                            <div class="text-sm text-gray-500"><?php echo ucfirst($member['gender']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $member['phone_number']; ?></div>
                                    <div class="text-sm text-gray-500"><?php echo $member['email'] ?: 'No email'; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($member['role'] === 'leader') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Leader</span>
                                    <?php elseif ($member['role'] === 'worker') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Worker</span>
                                    <?php elseif ($member['role'] === 'pastor') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Pastor</span>
                                    <?php elseif ($member['role'] === 'deacon') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800">Deacon</span>
                                    <?php else : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Member</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo format_date($member['membership_date']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($member['member_status'] === 'active') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                    <?php elseif ($member['member_status'] === 'inactive') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                                    <?php else : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Transferred</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo BASE_URL; ?>members/view?id=<?php echo $member['id']; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>members/edit?id=<?php echo $member['id']; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="javascript:void(0)" onclick="confirmRemove(<?php echo $member['id']; ?>)" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-user-minus"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Remove from Department Confirmation Modal -->
<div id="removeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Remove from Department</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Are you sure you want to remove this member from the <?php echo $department_name; ?> department?</p>
            </div>
            <div class="flex justify-center mt-4 px-4 py-3">
                <button id="cancelRemove" class="bg-gray-200 px-4 py-2 rounded-md text-gray-800 hover:bg-gray-300 mr-2">Cancel</button>
                <a id="confirmRemove" href="#" class="bg-yellow-600 px-4 py-2 rounded-md text-white hover:bg-yellow-700">Remove</a>
            </div>
        </div>
    </div>
</div>

<script>
    // Remove from department confirmation
    function confirmRemove(id) {
        const modal = document.getElementById('removeModal');
        const confirmBtn = document.getElementById('confirmRemove');

        modal.classList.remove('hidden');
        confirmBtn.href = '<?php echo BASE_URL; ?>members/remove-department?id=' + id + '&department=<?php echo $department; ?>';

        document.getElementById('cancelRemove').addEventListener('click', function() {
            modal.classList.add('hidden');
        });
    }
</script>
