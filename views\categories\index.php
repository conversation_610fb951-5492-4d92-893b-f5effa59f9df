<?php
/**
 * Categories Index View
 * Display all program categories
 */
?>

<div class="max-w-7xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Program Categories</h1>
            <p class="text-gray-600 mt-1">Manage program categories for church activities</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>programs"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Programs
            </a>
            <a href="<?php echo BASE_URL; ?>categories/create"
               class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Add Category
            </a>
        </div>
    </div>

    <!-- Categories Grid -->
    <?php if (!empty($categories)): ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php foreach ($categories as $category): ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <!-- Category Header -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <?php if (!empty($category['icon'])): ?>
                                <div class="w-10 h-10 rounded-full flex items-center justify-center mr-3"
                                     style="background-color: <?php echo htmlspecialchars($category['color_code'] ?? '#6B7280'); ?>20;">
                                    <i class="<?php echo htmlspecialchars($category['icon']); ?> text-lg"
                                       style="color: <?php echo htmlspecialchars($category['color_code'] ?? '#6B7280'); ?>;"></i>
                                </div>
                            <?php else: ?>
                                <div class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-folder text-gray-500"></i>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    <?php echo $category['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                    <?php echo $category['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            </div>
                        </div>
                        
                        <!-- Actions Dropdown -->
                        <div class="relative">
                            <button type="button" 
                                    class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                                    onclick="toggleDropdown('dropdown-<?php echo $category['id']; ?>')">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div id="dropdown-<?php echo $category['id']; ?>" 
                                 class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                                <div class="py-1">
                                    <a href="<?php echo BASE_URL; ?>categories/<?php echo $category['id']; ?>" 
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-eye mr-2"></i>View Details
                                    </a>
                                    <a href="<?php echo BASE_URL; ?>categories/<?php echo $category['id']; ?>/edit" 
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-edit mr-2"></i>Edit
                                    </a>
                                    <button type="button" 
                                            onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>')"
                                            class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        <i class="fas fa-trash mr-2"></i>Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Description -->
                    <?php if (!empty($category['description'])): ?>
                        <p class="text-gray-600 text-sm mb-4">
                            <?php echo htmlspecialchars($category['description']); ?>
                        </p>
                    <?php endif; ?>

                    <!-- Category Stats -->
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>
                            <i class="fas fa-calendar mr-1"></i>
                            Created <?php echo date('M j, Y', strtotime($category['created_at'])); ?>
                        </span>
                        <?php if (isset($category['program_count'])): ?>
                            <span>
                                <i class="fas fa-list mr-1"></i>
                                <?php echo $category['program_count']; ?> programs
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-folder-open text-gray-400 text-3xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Categories Found</h3>
            <p class="text-gray-500 mb-6">Get started by creating your first program category.</p>
            <a href="<?php echo BASE_URL; ?>categories/create" 
               class="bg-emerald-500 hover:bg-emerald-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Create Category
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Category</h3>
            <p class="text-sm text-gray-500 mb-4">
                Are you sure you want to delete "<span id="categoryName"></span>"? This action cannot be undone.
            </p>
            <div class="flex justify-center space-x-3">
                <button type="button" 
                        onclick="closeDeleteModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancel
                </button>
                <button type="button" 
                        id="confirmDeleteBtn"
                        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                    Delete
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle dropdown menu
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
    
    // Close all other dropdowns
    allDropdowns.forEach(d => {
        if (d.id !== dropdownId) {
            d.classList.add('hidden');
        }
    });
    
    // Toggle current dropdown
    dropdown.classList.toggle('hidden');
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick*="toggleDropdown"]')) {
        const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
        allDropdowns.forEach(d => d.classList.add('hidden'));
    }
});

// Delete category
function deleteCategory(categoryId, categoryName) {
    document.getElementById('categoryName').textContent = categoryName;
    document.getElementById('deleteModal').classList.remove('hidden');
    
    document.getElementById('confirmDeleteBtn').onclick = function() {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo BASE_URL; ?>categories/' + categoryId;
        
        // Add method override for DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);
        
        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generate_csrf_token(); ?>';
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    };
}

// Close delete modal
function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}
</script>
