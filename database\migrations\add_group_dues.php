<?php
/**
 * Migration script to add group dues functionality
 */

require_once __DIR__ . '/../../config/database.php';

echo "<h1>Adding Group Dues Management Tables</h1>";

try {
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Creating Group Dues Settings Table</h2>";
    
    // Group Dues Settings Table
    $sql_dues_settings = "CREATE TABLE IF NOT EXISTS `group_dues_settings` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `group_id` INT NOT NULL,
        `dues_amount` DECIMAL(10,2) NOT NULL,
        `dues_frequency` ENUM('weekly', 'monthly', 'quarterly', 'yearly') DEFAULT 'monthly',
        `due_date` INT DEFAULT 1 COMMENT 'Day of month/week when dues are due',
        `currency` VARCHAR(10) DEFAULT 'GHS',
        `is_active` BOOLEAN DEFAULT TRUE,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (`group_id`) REFERENCES `groups`(`group_id`) ON DELETE CASCADE,
        UNIQUE KEY `unique_group_dues` (`group_id`)
    )";
    
    $stmt = $conn->prepare($sql_dues_settings);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Group dues settings table created successfully</p>";
    
    echo "<h2>Creating Dues Payments Table</h2>";
    
    // Dues Payments Table
    $sql_dues_payments = "CREATE TABLE IF NOT EXISTS `group_dues_payments` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `group_id` INT NOT NULL,
        `member_id` INT NOT NULL,
        `payment_amount` DECIMAL(10,2) NOT NULL,
        `payment_date` DATE NOT NULL,
        `payment_period` VARCHAR(20) NOT NULL COMMENT 'e.g., 2024-01 for January 2024',
        `payment_method` ENUM('cash', 'mobile_money', 'bank_transfer', 'cheque', 'other') DEFAULT 'cash',
        `payment_reference` VARCHAR(100) NULL,
        `payment_notes` TEXT NULL,
        `recorded_by` INT NOT NULL,
        `status` ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'confirmed',
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (`group_id`) REFERENCES `groups`(`group_id`) ON DELETE CASCADE,
        FOREIGN KEY (`member_id`) REFERENCES `members`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`recorded_by`) REFERENCES `members`(`id`) ON DELETE CASCADE,
        UNIQUE KEY `unique_member_period` (`group_id`, `member_id`, `payment_period`)
    )";
    
    $stmt = $conn->prepare($sql_dues_payments);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Group dues payments table created successfully</p>";
    
    echo "<h2>Creating Dues Exemptions Table</h2>";
    
    // Dues Exemptions Table (for members who are exempt from paying dues)
    $sql_dues_exemptions = "CREATE TABLE IF NOT EXISTS `group_dues_exemptions` (
        `id` INT PRIMARY KEY AUTO_INCREMENT,
        `group_id` INT NOT NULL,
        `member_id` INT NOT NULL,
        `exemption_reason` VARCHAR(255) NOT NULL,
        `exemption_start_date` DATE NOT NULL,
        `exemption_end_date` DATE NULL,
        `is_active` BOOLEAN DEFAULT TRUE,
        `created_by` INT NOT NULL,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (`group_id`) REFERENCES `groups`(`group_id`) ON DELETE CASCADE,
        FOREIGN KEY (`member_id`) REFERENCES `members`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`created_by`) REFERENCES `members`(`id`) ON DELETE CASCADE
    )";
    
    $stmt = $conn->prepare($sql_dues_exemptions);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Group dues exemptions table created successfully</p>";
    
    echo "<h2>Migration Completed Successfully!</h2>";
    echo "<p style='color: blue;'>All tables for group dues management have been created:</p>";
    echo "<ul>";
    echo "<li>✓ group_dues_settings - For managing dues amount, frequency, and settings</li>";
    echo "<li>✓ group_dues_payments - For tracking member dues payments</li>";
    echo "<li>✓ group_dues_exemptions - For managing member exemptions</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>
