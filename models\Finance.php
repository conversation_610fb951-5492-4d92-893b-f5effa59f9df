<?php
/**
 * Finance Model
 */

class Finance {
    // Database connection and table name
    private $conn;
    private $table_name = "finances";

    // Object properties
    public $id;
    public $category;
    public $transaction_type;
    public $subcategory;
    public $amount;
    public $description;
    public $reference_number;
    public $payment_method;
    public $transaction_date;
    public $member_id;
    public $recorded_by;
    public $created_at;
    public $updated_at;

    // Error handling
    public $error;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all finances with user details
     *
     * @return PDOStatement
     */
    public function getAll() {
        $query = "SELECT f.*, u.username as recorded_by_name,
                  IF(m.id IS NULL, NULL, CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, ''))) as member_name,
                  m.id as member_id
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  LEFT JOIN members m ON f.member_id = m.id
                  ORDER BY f.transaction_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get monthly income and expense data for charts
     *
     * @param int $months Number of months to retrieve
     * @return array
     */
    public function getMonthlyData($months = 6) {
        $query = "SELECT
                    DATE_FORMAT(transaction_date, '%Y-%m') as month_year,
                    DATE_FORMAT(transaction_date, '%b %Y') as month,
                    SUM(CASE WHEN category = 'income' THEN amount ELSE 0 END) as income,
                    SUM(CASE WHEN category = 'expense' THEN amount ELSE 0 END) as expense
                  FROM " . $this->table_name . "
                  WHERE transaction_date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
                  GROUP BY month_year, month
                  ORDER BY month_year ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $months, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get monthly averages for income and expenses
     *
     * @param int $months Number of months to consider
     * @return array
     */
    public function getMonthlyAverages($months = 6) {
        $query = "SELECT
                    AVG(CASE WHEN category = 'income' THEN monthly_total ELSE 0 END) as avg_income,
                    AVG(CASE WHEN category = 'expense' THEN monthly_total ELSE 0 END) as avg_expense
                  FROM (
                    SELECT
                      category,
                      DATE_FORMAT(transaction_date, '%Y-%m') as month_year,
                      SUM(amount) as monthly_total
                    FROM " . $this->table_name . "
                    WHERE transaction_date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
                    GROUP BY category, month_year
                  ) as monthly_totals";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $months, PDO::PARAM_INT);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return [
            'avg_income' => $result['avg_income'] ?: 0,
            'avg_expense' => $result['avg_expense'] ?: 0
        ];
    }

    /**
     * Get growth rates for income and expenses
     *
     * @param int $months Number of months to consider
     * @return array
     */
    public function getGrowthRates($months = 6) {
        // We need at least 2 months of data to calculate growth
        if ($months < 2) {
            $months = 2;
        }

        // Get the first and last month's data
        $query = "SELECT
                    first_month.income as first_income,
                    first_month.expense as first_expense,
                    last_month.income as last_income,
                    last_month.expense as last_expense
                  FROM
                    (SELECT
                      SUM(CASE WHEN category = 'income' THEN amount ELSE 0 END) as income,
                      SUM(CASE WHEN category = 'expense' THEN amount ELSE 0 END) as expense
                    FROM " . $this->table_name . "
                    WHERE transaction_date >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL ? MONTH), '%Y-%m-01')
                    AND transaction_date < DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL ? - 1 MONTH), '%Y-%m-01')
                    ) as first_month,
                    (SELECT
                      SUM(CASE WHEN category = 'income' THEN amount ELSE 0 END) as income,
                      SUM(CASE WHEN category = 'expense' THEN amount ELSE 0 END) as expense
                    FROM " . $this->table_name . "
                    WHERE transaction_date >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01')
                    AND transaction_date < DATE_FORMAT(CURDATE(), '%Y-%m-01')
                    ) as last_month";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $months, PDO::PARAM_INT);
        $stmt->bindParam(2, $months, PDO::PARAM_INT);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        // Calculate growth rates
        $income_growth = 0;
        $expense_growth = 0;

        if ($result && $result['first_income'] > 0) {
            $income_growth = (($result['last_income'] - $result['first_income']) / $result['first_income']) * 100;
        }

        if ($result && $result['first_expense'] > 0) {
            $expense_growth = (($result['last_expense'] - $result['first_expense']) / $result['first_expense']) * 100;
        }

        return [
            'income_growth' => $income_growth,
            'expense_growth' => $expense_growth
        ];
    }

    /**
     * Get top income and expense categories
     *
     * @param string $year Year to retrieve data for (default: current year)
     * @return array
     */
    public function getTopCategories($year = null) {
        if (!$year) {
            $year = date('Y');
        }

        // Get top income category
        $income_query = "SELECT
                          category,
                          SUM(amount) as amount,
                          (SUM(amount) / (SELECT SUM(amount) FROM " . $this->table_name . "
                                          WHERE category != 'expense' AND YEAR(transaction_date) = ?)) * 100 as percentage
                        FROM " . $this->table_name . "
                        WHERE category != 'expense'
                        AND YEAR(transaction_date) = ?
                        GROUP BY category
                        ORDER BY amount DESC
                        LIMIT 1";

        $income_stmt = $this->conn->prepare($income_query);
        $income_stmt->bindParam(1, $year, PDO::PARAM_STR);
        $income_stmt->bindParam(2, $year, PDO::PARAM_STR);
        $income_stmt->execute();
        $top_income = $income_stmt->fetch(PDO::FETCH_ASSOC);

        // Get expense data
        $expense_query = "SELECT
                           'Expense' as category,
                           SUM(amount) as amount,
                           100 as percentage
                         FROM " . $this->table_name . "
                         WHERE category = 'expense'
                         AND YEAR(transaction_date) = ?";

        $expense_stmt = $this->conn->prepare($expense_query);
        $expense_stmt->bindParam(1, $year, PDO::PARAM_STR);
        $expense_stmt->execute();
        $top_expense = $expense_stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'top_income' => $top_income ?: ['category' => 'None', 'amount' => 0, 'percentage' => 0],
            'top_expense' => $top_expense ?: ['category' => 'None', 'amount' => 0, 'percentage' => 0]
        ];
    }

    /**
     * Get income by category for charts
     *
     * @param string $year Year to retrieve data for (default: current year)
     * @return array
     */
    public function getIncomeBySubcategory($year = null) {
        if (!$year) {
            $year = date('Y');
        }

        $query = "SELECT
                    category,
                    SUM(amount) as amount
                  FROM " . $this->table_name . "
                  WHERE category != 'expense'
                  AND YEAR(transaction_date) = ?
                  GROUP BY category
                  ORDER BY amount DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $year, PDO::PARAM_STR);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get expense categories for charts
     *
     * @param string $year Year to retrieve data for (default: current year)
     * @return array
     */
    public function getExpenseBySubcategory($year = null) {
        if (!$year) {
            $year = date('Y');
        }

        $query = "SELECT
                    'Expense' as category,
                    SUM(amount) as amount
                  FROM " . $this->table_name . "
                  WHERE category = 'expense'
                  AND YEAR(transaction_date) = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $year, PDO::PARAM_STR);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get finance by ID
     *
     * @param int $id
     * @return bool
     */
    public function getById($id) {
        $query = "SELECT f.*, u.username as recorded_by_name,
                  IF(m.id IS NULL, NULL, CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, ''))) as member_name,
                  m.id as member_id
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  LEFT JOIN members m ON f.member_id = m.id
                  WHERE f.id = :id
                  LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            $this->id = $row['id'];
            $this->category = $row['category'];
            $this->amount = $row['amount'];
            $this->description = $row['description'];
            $this->transaction_date = $row['transaction_date'];
            $this->member_id = $row['member_id'];
            $this->recorded_by = $row['recorded_by'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];

            return true;
        }

        return false;
    }

    /**
     * Validate finance data before create/update operations
     *
     * @return bool True if validation passes, false otherwise
     */
    public function validate() {
        $this->error = null;

        // Required field validation
        if (empty($this->category)) {
            $this->error = 'Category is required.';
            return false;
        }

        if (empty($this->amount)) {
            $this->error = 'Amount is required.';
            return false;
        }

        // Amount validation
        if (!is_numeric($this->amount) || $this->amount <= 0) {
            $this->error = 'Amount must be a positive number.';
            return false;
        }

        // Amount range validation (reasonable limits)
        if ($this->amount > 999999999.99) {
            $this->error = 'Amount cannot exceed 999,999,999.99.';
            return false;
        }

        if (empty($this->transaction_date)) {
            $this->error = 'Transaction date is required.';
            return false;
        }

        // Date format validation
        if (!empty($this->transaction_date)) {
            $date = DateTime::createFromFormat('Y-m-d', $this->transaction_date);
            if (!$date || $date->format('Y-m-d') !== $this->transaction_date) {
                $this->error = 'Invalid transaction date format.';
                return false;
            }

            // Check if date is not too far in the future (allow up to 1 year)
            $max_future_date = new DateTime('+1 year');
            if ($date > $max_future_date) {
                $this->error = 'Transaction date cannot be more than 1 year in the future.';
                return false;
            }

            // Check if date is not too far in the past (allow up to 10 years)
            $min_past_date = new DateTime('-10 years');
            if ($date < $min_past_date) {
                $this->error = 'Transaction date cannot be more than 10 years in the past.';
                return false;
            }
        }

        // Category validation
        $valid_categories = ['tithe', 'offering', 'donation', 'expense', 'income', 'other'];
        if (!in_array(strtolower($this->category), $valid_categories)) {
            $this->error = 'Invalid category selected.';
            return false;
        }

        // Transaction type validation (if provided)
        if (!empty($this->transaction_type)) {
            $valid_types = ['income', 'expense'];
            if (!in_array(strtolower($this->transaction_type), $valid_types)) {
                $this->error = 'Invalid transaction type.';
                return false;
            }
        }

        // Payment method validation (if provided)
        if (!empty($this->payment_method)) {
            $valid_methods = ['cash', 'check', 'bank_transfer', 'mobile_money', 'card', 'other'];
            if (!in_array(strtolower($this->payment_method), $valid_methods)) {
                $this->error = 'Invalid payment method.';
                return false;
            }
        }

        // Description length validation (if provided)
        if (!empty($this->description) && strlen($this->description) > 500) {
            $this->error = 'Description cannot exceed 500 characters.';
            return false;
        }

        // Reference number validation (if provided)
        if (!empty($this->reference_number) && strlen($this->reference_number) > 100) {
            $this->error = 'Reference number cannot exceed 100 characters.';
            return false;
        }

        return true;
    }

    /**
     * Create finance record with validation
     *
     * @return bool
     */
    public function create() {
        // CRITICAL: Validate data before creating
        if (!$this->validate()) {
            // The 'error' property is set by the validate() method
            return false;
        }
        $query = "INSERT INTO " . $this->table_name . "
                  (category, transaction_type, subcategory, amount, description, reference_number, payment_method, transaction_date, member_id, recorded_by, created_at, updated_at)
                  VALUES
                  (:category, :transaction_type, :subcategory, :amount, :description, :reference_number, :payment_method, :transaction_date, :member_id, :recorded_by, :created_at, :updated_at)";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs - but don't double sanitize if already done in controller
        $this->category = htmlspecialchars(strip_tags($this->category));
        $this->transaction_type = htmlspecialchars(strip_tags($this->transaction_type));
        $this->subcategory = $this->subcategory ? htmlspecialchars(strip_tags($this->subcategory)) : null;
        $this->amount = htmlspecialchars(strip_tags($this->amount));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->reference_number = $this->reference_number ? htmlspecialchars(strip_tags($this->reference_number)) : null;
        $this->payment_method = htmlspecialchars(strip_tags($this->payment_method));
        $this->transaction_date = htmlspecialchars(strip_tags($this->transaction_date));
        $this->created_at = htmlspecialchars(strip_tags($this->created_at));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':category', $this->category);
        $stmt->bindParam(':transaction_type', $this->transaction_type);
        $stmt->bindParam(':subcategory', $this->subcategory);
        $stmt->bindParam(':amount', $this->amount);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':reference_number', $this->reference_number);
        $stmt->bindParam(':payment_method', $this->payment_method);
        $stmt->bindParam(':transaction_date', $this->transaction_date);
        $stmt->bindParam(':member_id', $this->member_id);
        $stmt->bindParam(':recorded_by', $this->recorded_by);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        try {
            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Failed to create finance record.';
                return false;
            }
        } catch (PDOException $e) {
            $this->error = 'Database error occurred while creating finance record.';
            error_log("Finance creation failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update finance record with validation
     *
     * @return bool
     */
    public function update() {
        // CRITICAL: Validate data before updating
        if (!$this->validate()) {
            // The 'error' property is set by the validate() method
            return false;
        }
        $query = "UPDATE " . $this->table_name . "
                  SET category = :category,
                      amount = :amount,
                      description = :description,
                      transaction_date = :transaction_date,
                      member_id = :member_id,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->id = htmlspecialchars(strip_tags($this->id));
        $this->category = htmlspecialchars(strip_tags($this->category));
        $this->amount = htmlspecialchars(strip_tags($this->amount));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->transaction_date = htmlspecialchars(strip_tags($this->transaction_date));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':category', $this->category);
        $stmt->bindParam(':amount', $this->amount);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':transaction_date', $this->transaction_date);
        $stmt->bindParam(':member_id', $this->member_id);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        try {
            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Failed to update finance record.';
                return false;
            }
        } catch (PDOException $e) {
            $this->error = 'Database error occurred while updating finance record.';
            error_log("Finance update failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete finance record
     *
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameter
        $stmt->bindParam(':id', $this->id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Get finances by category
     *
     * @param string $category
     * @return PDOStatement
     */
    public function getByCategory($category) {
        $query = "SELECT f.*, u.username as recorded_by_name
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  WHERE f.category = :category
                  ORDER BY f.transaction_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':category', $category);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get finances by date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return PDOStatement
     */
    public function getByDateRange($start_date, $end_date) {
        $query = "SELECT f.*, u.username as recorded_by_name
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  WHERE f.transaction_date BETWEEN :start_date AND :end_date
                  ORDER BY f.transaction_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get finances by date range and category
     *
     * @param string $start_date
     * @param string $end_date
     * @param string $category
     * @return PDOStatement
     */
    public function getByDateRangeAndCategory($start_date, $end_date, $category) {
        $query = "SELECT f.*, u.username as recorded_by_name
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  WHERE f.transaction_date BETWEEN :start_date AND :end_date
                  AND f.category = :category
                  ORDER BY f.transaction_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->bindParam(':category', $category);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get total income (includes all income categories and custom categories)
     *
     * @return float
     */
    public function getTotalIncome() {
        try {
            // Check if we have the new schema with transaction_type column
            $hasNewSchema = $this->hasNewSchemaColumns();

            if ($hasNewSchema) {
                // New schema: use transaction_type = 'income'
                $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                          WHERE transaction_type = 'income'";
            } else {
                // Old schema: include all non-expense categories
                // Also check if custom_finance_categories table exists
                $customTableExists = $this->customCategoriesTableExists();

                if ($customTableExists) {
                    $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                              WHERE (category != 'expense'
                                     OR category IN (
                                         SELECT name FROM custom_finance_categories
                                         WHERE category_type IN ('member_payments', 'general_income')
                                         AND is_active = 1
                                     ))";
                } else {
                    // Fallback to simple logic if custom table doesn't exist
                    $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                              WHERE category != 'expense'";
                }
            }

            $stmt = $this->conn->prepare($query);
            $stmt->execute();

            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            return $row['total'] ?? 0;

        } catch (Exception $e) {
            error_log("Error in getTotalIncome: " . $e->getMessage());
            // Fallback to simple calculation
            $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                      WHERE category != 'expense'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['total'] ?? 0;
        }
    }

    /**
     * Get total income by date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return float
     */
    public function getTotalIncomeByDateRange($start_date, $end_date) {
        $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                  WHERE category != 'expense'
                  AND transaction_date BETWEEN :start_date AND :end_date";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'] ?? 0;
    }

    /**
     * Get total expenses (includes all expense categories and custom categories)
     *
     * @return float
     */
    public function getTotalExpenses() {
        try {
            // Check if we have the new schema with transaction_type column
            $hasNewSchema = $this->hasNewSchemaColumns();

            if ($hasNewSchema) {
                // New schema: use transaction_type = 'expense'
                $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                          WHERE transaction_type = 'expense'";
            } else {
                // Old schema: include expense category + custom expense categories
                // Also check if custom_finance_categories table exists
                $customTableExists = $this->customCategoriesTableExists();

                if ($customTableExists) {
                    $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                              WHERE (category = 'expense'
                                     OR category IN (
                                         SELECT name FROM custom_finance_categories
                                         WHERE category_type = 'expenses'
                                         AND is_active = 1
                                     ))";
                } else {
                    // Fallback to simple logic if custom table doesn't exist
                    $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                              WHERE category = 'expense'";
                }
            }

            $stmt = $this->conn->prepare($query);
            $stmt->execute();

            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            return $row['total'] ?? 0;

        } catch (Exception $e) {
            error_log("Error in getTotalExpenses: " . $e->getMessage());
            // Fallback to simple calculation
            $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                      WHERE category = 'expense'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['total'] ?? 0;
        }
    }

    /**
     * Check if the database has the new schema columns
     *
     * @return bool
     */
    private function hasNewSchemaColumns() {
        try {
            $query = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'transaction_type'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Check if the custom_finance_categories table exists
     *
     * @return bool
     */
    private function customCategoriesTableExists() {
        try {
            $query = "SHOW TABLES LIKE 'custom_finance_categories'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get total expenses by date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return float
     */
    public function getTotalExpensesByDateRange($start_date, $end_date) {
        $query = "SELECT SUM(amount) as total FROM " . $this->table_name . "
                  WHERE category = 'expense'
                  AND transaction_date BETWEEN :start_date AND :end_date";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'] ?? 0;
    }

    /**
     * Get recent financial transactions
     *
     * @param int $limit
     * @return array
     */
    public function getRecent($limit = 5) {
        $query = "SELECT f.*, u.username as recorded_by_name
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  ORDER BY f.transaction_date DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get income by category
     *
     * @return array
     */
    public function getIncomeByCategory() {
        $query = "SELECT category, SUM(amount) as total
                  FROM " . $this->table_name . "
                  WHERE category != 'expense'
                  GROUP BY category
                  ORDER BY total DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get tithe transactions by member ID
     *
     * @param int $member_id
     * @return array
     */
    public function getTithesByMember($member_id) {
        $query = "SELECT f.*, u.username as recorded_by_name,
                  IF(m.id IS NULL, NULL, CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, ''))) as member_name
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  LEFT JOIN members m ON f.member_id = m.id
                  WHERE f.member_id = :member_id AND f.category = 'tithe'
                  ORDER BY f.transaction_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get welfare transactions by member ID
     *
     * @param int $member_id
     * @return array
     */
    public function getWelfareByMember($member_id) {
        $query = "SELECT f.*, u.username as recorded_by_name,
                  IF(m.id IS NULL, NULL, CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, ''))) as member_name
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  LEFT JOIN members m ON f.member_id = m.id
                  WHERE f.member_id = :member_id AND f.category = 'welfare'
                  ORDER BY f.transaction_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':member_id', $member_id, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get all tithe transactions with member details
     *
     * @return array
     */
    public function getAllTithes() {
        $query = "SELECT f.*, u.username as recorded_by_name,
                  IF(m.id IS NULL, NULL, CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, ''))) as member_name,
                  m.id as member_id
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  LEFT JOIN members m ON f.member_id = m.id
                  WHERE f.category = 'tithe'
                  ORDER BY f.transaction_date DESC";

        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            return [];
        }
    }

    /**
     * Get all welfare transactions with member details
     *
     * @return array
     */
    public function getAllWelfare() {
        $query = "SELECT f.*, u.username as recorded_by_name,
                  IF(m.id IS NULL, NULL, CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, ''))) as member_name,
                  m.id as member_id
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  LEFT JOIN members m ON f.member_id = m.id
                  WHERE f.category = 'welfare'
                  ORDER BY f.transaction_date DESC";

        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            return [];
        }
    }

    /**
     * Get top tithe contributors
     *
     * @param int $limit Number of contributors to return
     * @return array
     */
    public function getTopTitheContributors($limit = 5) {
        $query = "SELECT
                    f.member_id,
                    CONCAT(m.first_name, ' ', m.last_name) as name,
                    m.profile_picture,
                    SUM(f.amount) as total_amount
                  FROM " . $this->table_name . " f
                  JOIN members m ON f.member_id = m.id
                  WHERE f.category = 'tithe' AND f.member_id IS NOT NULL
                  GROUP BY f.member_id, m.first_name, m.last_name, m.profile_picture
                  ORDER BY total_amount DESC
                  LIMIT ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get top welfare contributors
     *
     * @param int $limit Number of contributors to return
     * @return array
     */
    public function getTopWelfareContributors($limit = 5) {
        $query = "SELECT
                    f.member_id,
                    CONCAT(m.first_name, ' ', m.last_name) as name,
                    m.profile_picture,
                    SUM(f.amount) as total_amount
                  FROM " . $this->table_name . " f
                  JOIN members m ON f.member_id = m.id
                  WHERE f.category = 'welfare' AND f.member_id IS NOT NULL
                  GROUP BY f.member_id, m.first_name, m.last_name, m.profile_picture
                  ORDER BY total_amount DESC
                  LIMIT ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get top contributors for a specific category
     *
     * @param string $category The category to get contributors for
     * @param int $limit Number of contributors to return
     * @return array
     */
    public function getTopContributorsBySubcategory($category, $limit = 5) {
        $query = "SELECT
                    f.member_id,
                    CONCAT(m.first_name, ' ', m.last_name) as name,
                    m.profile_picture,
                    SUM(f.amount) as total_amount
                  FROM " . $this->table_name . " f
                  JOIN members m ON f.member_id = m.id
                  WHERE f.category = ? AND f.member_id IS NOT NULL
                  GROUP BY f.member_id, m.first_name, m.last_name, m.profile_picture
                  ORDER BY total_amount DESC
                  LIMIT ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $category, PDO::PARAM_STR);
        $stmt->bindParam(2, $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get member financial history
     *
     * @param int $member_id The member ID
     * @param int $limit Number of transactions to return
     * @return array
     */
    public function getMemberFinancialHistory($member_id, $limit = 10) {
        $query = "SELECT
                    f.*,
                    u.username as recorded_by_name
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  WHERE f.member_id = ?
                  ORDER BY f.transaction_date DESC
                  LIMIT ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $member_id, PDO::PARAM_INT);
        $stmt->bindParam(2, $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get member contribution summary
     *
     * @param int $member_id The member ID
     * @return array
     */
    public function getMemberContributionSummary($member_id) {
        $query = "SELECT
                    category,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count,
                    MIN(transaction_date) as first_contribution,
                    MAX(transaction_date) as last_contribution
                  FROM " . $this->table_name . "
                  WHERE member_id = ? AND category != 'expense'
                  GROUP BY category
                  ORDER BY total_amount DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $member_id, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get all finances with user details with pagination
     *
     * @param int $limit Number of records to return
     * @param int $offset Offset for pagination
     * @return PDOStatement
     */
    public function getAllPaginated($limit = 20, $offset = 0) {
        $query = "SELECT f.*, u.username as recorded_by_name,
                  IF(m.id IS NULL, NULL, CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, ''))) as member_name,
                  m.id as member_id
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  LEFT JOIN members m ON f.member_id = m.id
                  ORDER BY f.transaction_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get filtered finances with pagination
     *
     * @param array $filters Associative array of filters
     * @param int $limit Number of records per page
     * @param int $offset Pagination offset
     * @return PDOStatement
     */
    public function getFiltered($filters = [], $limit = 20, $offset = 0) {
        $conditions = [];
        $params = [];

        // Build WHERE clause based on filters
        if (!empty($filters['category'])) {
            $conditions[] = "f.category = :category";
            $params[':category'] = $filters['category'];
        }

        if (!empty($filters['type'])) {
            if ($filters['type'] === 'income') {
                $conditions[] = "f.category != 'expense'";
            } else if ($filters['type'] === 'expense') {
                $conditions[] = "f.category = 'expense'";
            }
        }

        if (!empty($filters['start_date'])) {
            $conditions[] = "f.transaction_date >= :start_date";
            $params[':start_date'] = $filters['start_date'];
        }

        if (!empty($filters['end_date'])) {
            $conditions[] = "f.transaction_date <= :end_date";
            $params[':end_date'] = $filters['end_date'];
        }

        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $conditions[] = "(f.description LIKE :search OR CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, '')) LIKE :search)";
            $params[':search'] = $searchTerm;
        }

        if (!empty($filters['min_amount'])) {
            $conditions[] = "f.amount >= :min_amount";
            $params[':min_amount'] = $filters['min_amount'];
        }

        if (!empty($filters['max_amount'])) {
            $conditions[] = "f.amount <= :max_amount";
            $params[':max_amount'] = $filters['max_amount'];
        }

        if (!empty($filters['member_id'])) {
            $conditions[] = "f.member_id = :member_id";
            $params[':member_id'] = $filters['member_id'];
        }

        // Build the query
        $query = "SELECT f.*, u.username as recorded_by_name,
                  IF(m.id IS NULL, NULL, CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, ''))) as member_name,
                  m.id as member_id
                  FROM " . $this->table_name . " f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  LEFT JOIN members m ON f.member_id = m.id";

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $query .= " ORDER BY f.transaction_date DESC
                    LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);

        // Bind parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get count of filtered finances
     *
     * @param array $filters Associative array of filters
     * @return int
     */
    public function getFilteredCount($filters = []) {
        $conditions = [];
        $params = [];

        // Build WHERE clause based on filters (same as getFiltered method)
        if (!empty($filters['category'])) {
            $conditions[] = "f.category = :category";
            $params[':category'] = $filters['category'];
        }

        if (!empty($filters['type'])) {
            if ($filters['type'] === 'income') {
                $conditions[] = "f.category != 'expense'";
            } else if ($filters['type'] === 'expense') {
                $conditions[] = "f.category = 'expense'";
            }
        }

        if (!empty($filters['start_date'])) {
            $conditions[] = "f.transaction_date >= :start_date";
            $params[':start_date'] = $filters['start_date'];
        }

        if (!empty($filters['end_date'])) {
            $conditions[] = "f.transaction_date <= :end_date";
            $params[':end_date'] = $filters['end_date'];
        }

        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $conditions[] = "(f.description LIKE :search OR CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, '')) LIKE :search)";
            $params[':search'] = $searchTerm;
        }

        if (!empty($filters['min_amount'])) {
            $conditions[] = "f.amount >= :min_amount";
            $params[':min_amount'] = $filters['min_amount'];
        }

        if (!empty($filters['max_amount'])) {
            $conditions[] = "f.amount <= :max_amount";
            $params[':max_amount'] = $filters['max_amount'];
        }

        if (!empty($filters['member_id'])) {
            $conditions[] = "f.member_id = :member_id";
            $params[':member_id'] = $filters['member_id'];
        }

        // Build the query
        $query = "SELECT COUNT(*) as total
                  FROM " . $this->table_name . " f
                  LEFT JOIN members m ON f.member_id = m.id";

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $stmt = $this->conn->prepare($query);

        // Bind parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return (int)$row['total'];
    }

    /**
     * Get total count of finance records
     *
     * @return int
     */
    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return (int)$row['total'];
    }

    /**
     * Archive old finance records
     *
     * @param string $cutoffDate Date before which to archive records (YYYY-MM-DD)
     * @return array Result with status and message
     */
    public function archiveOldRecords($cutoffDate) {
        // Start timing
        $startTime = microtime(true);

        // Begin transaction
        $this->conn->beginTransaction();

        try {
            // 1. Check if archive table exists, create if not
            $checkTableQuery = "SHOW TABLES LIKE 'finances_archive'";
            $checkTableStmt = $this->conn->prepare($checkTableQuery);
            $checkTableStmt->execute();

            // Store the result and close the cursor
            $archiveTableNotExists = ($checkTableStmt->rowCount() === 0);
            $checkTableStmt->closeCursor();

            if ($archiveTableNotExists) {
                // Create archive table
                $createArchiveTable = file_get_contents('database/finance_archive_table.sql');
                $this->conn->exec($createArchiveTable);
            }

            // 2. Count records to be archived
            $countQuery = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE transaction_date < :cutoff_date";
            $countStmt = $this->conn->prepare($countQuery);
            $countStmt->bindParam(':cutoff_date', $cutoffDate);
            $countStmt->execute();
            $countRow = $countStmt->fetch(PDO::FETCH_ASSOC);
            $recordsToArchive = (int)$countRow['count'];

            if ($recordsToArchive === 0) {
                // Log the maintenance operation if DatabaseStats model is available
                if (class_exists('DatabaseStats')) {
                    $dbStats = new DatabaseStats($this->conn);
                    $dbStats->logMaintenanceOperation(
                        'archiving',
                        "No financial records found to archive before {$cutoffDate}",
                        'info',
                        'finances',
                        0,
                        microtime(true) - $startTime,
                        0
                    );
                }

                return [
                    'status' => 'info',
                    'message' => 'No records found to archive before ' . $cutoffDate
                ];
            }

            // 3. Move old records to archive table
            $moveRecords = "INSERT INTO finances_archive
                            SELECT *, NOW() as archived_at FROM " . $this->table_name . "
                            WHERE transaction_date < :cutoff_date";
            $moveStmt = $this->conn->prepare($moveRecords);
            $moveStmt->bindParam(':cutoff_date', $cutoffDate);
            $moveStmt->execute();

            // 4. Delete archived records from main table
            $deleteRecords = "DELETE FROM " . $this->table_name . "
                              WHERE transaction_date < :cutoff_date";
            $deleteStmt = $this->conn->prepare($deleteRecords);
            $deleteStmt->bindParam(':cutoff_date', $cutoffDate);
            $deleteStmt->execute();

            // Commit transaction
            $this->conn->commit();

            // Calculate duration
            $duration = microtime(true) - $startTime;

            // Log the maintenance operation if DatabaseStats model is available
            if (class_exists('DatabaseStats')) {
                $dbStats = new DatabaseStats($this->conn);
                $dbStats->logMaintenanceOperation(
                    'archiving',
                    "Archived {$recordsToArchive} financial records before {$cutoffDate}",
                    'success',
                    'finances,finances_archive',
                    $recordsToArchive,
                    $duration,
                    0
                );
            }

            return [
                'status' => 'success',
                'message' => $recordsToArchive . ' records successfully archived',
                'archived_count' => $recordsToArchive
            ];
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->conn->rollBack();

            // Log the failed operation if DatabaseStats model is available
            if (class_exists('DatabaseStats')) {
                $dbStats = new DatabaseStats($this->conn);
                $dbStats->logMaintenanceOperation(
                    'archiving',
                    'Error archiving financial records: ' . $e->getMessage(),
                    'error',
                    'finances',
                    0,
                    microtime(true) - $startTime,
                    0
                );
            }

            return [
                'status' => 'error',
                'message' => 'Error archiving records: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get archived finance records with pagination
     *
     * @param array $filters Filters to apply
     * @param int $limit Records per page
     * @param int $offset Pagination offset
     * @return PDOStatement
     */
    public function getArchivedRecords($filters = [], $limit = 20, $offset = 0) {
        // Check if archive table exists
        $checkTableQuery = "SHOW TABLES LIKE 'finances_archive'";
        $checkTableStmt = $this->conn->prepare($checkTableQuery);
        $checkTableStmt->execute();

        // Store the result and close the cursor
        $archiveTableExists = ($checkTableStmt->rowCount() > 0);
        $checkTableStmt->closeCursor();

        if (!$archiveTableExists) {
            // Archive table doesn't exist, return empty result
            return null;
        }

        $conditions = [];
        $params = [];

        // Apply filters (similar to getFiltered method)
        if (!empty($filters['category'])) {
            $conditions[] = "f.category = :category";
            $params[':category'] = $filters['category'];
        }

        if (!empty($filters['start_date'])) {
            $conditions[] = "f.transaction_date >= :start_date";
            $params[':start_date'] = $filters['start_date'];
        }

        if (!empty($filters['end_date'])) {
            $conditions[] = "f.transaction_date <= :end_date";
            $params[':end_date'] = $filters['end_date'];
        }

        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $conditions[] = "(f.description LIKE :search OR CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, '')) LIKE :search)";
            $params[':search'] = $searchTerm;
        }

        // Build query
        $query = "SELECT f.*, u.username as recorded_by_name,
                  IF(m.id IS NULL, NULL, CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, ''))) as member_name,
                  m.id as member_id
                  FROM finances_archive f
                  LEFT JOIN users u ON f.recorded_by = u.id
                  LEFT JOIN members m ON f.member_id = m.id";

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $query .= " ORDER BY f.transaction_date DESC
                    LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);

        // Bind parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get count of archived finance records
     *
     * @param array $filters Filters to apply
     * @return int
     */
    public function getArchivedCount($filters = []) {
        // Check if archive table exists
        $checkTableQuery = "SHOW TABLES LIKE 'finances_archive'";
        $checkTableStmt = $this->conn->prepare($checkTableQuery);
        $checkTableStmt->execute();

        // Store the result and close the cursor
        $archiveTableExists = ($checkTableStmt->rowCount() > 0);
        $checkTableStmt->closeCursor();

        if (!$archiveTableExists) {
            // Archive table doesn't exist
            return 0;
        }

        $conditions = [];
        $params = [];

        // Apply filters (similar to getFilteredCount method)
        if (!empty($filters['category'])) {
            $conditions[] = "f.category = :category";
            $params[':category'] = $filters['category'];
        }

        if (!empty($filters['start_date'])) {
            $conditions[] = "f.transaction_date >= :start_date";
            $params[':start_date'] = $filters['start_date'];
        }

        if (!empty($filters['end_date'])) {
            $conditions[] = "f.transaction_date <= :end_date";
            $params[':end_date'] = $filters['end_date'];
        }

        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $conditions[] = "(f.description LIKE :search OR CONCAT(IFNULL(m.first_name, ''), ' ', IFNULL(m.last_name, '')) LIKE :search)";
            $params[':search'] = $searchTerm;
        }

        // Build query
        $query = "SELECT COUNT(*) as total
                  FROM finances_archive f
                  LEFT JOIN members m ON f.member_id = m.id";

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $stmt = $this->conn->prepare($query);

        // Bind parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return (int)$row['total'];
    }

    /**
     * Perform database maintenance on finance tables
     *
     * @return array Result with status and message
     */
    public function performMaintenance() {
        try {
            // Use a completely different approach - run maintenance via shell command
            // This avoids the PDO unbuffered query issue entirely

            // First, check if archive table exists
            $checkTableQuery = "SHOW TABLES LIKE 'finances_archive'";
            $checkTableStmt = $this->conn->prepare($checkTableQuery);
            $checkTableStmt->execute();
            $archiveTableExists = ($checkTableStmt->rowCount() > 0);
            $checkTableStmt->closeCursor();

            // Create maintenance SQL file
            $maintenanceSQL = "";

            // Add maintenance commands
            $maintenanceSQL .= "OPTIMIZE TABLE finances;\n";
            if ($archiveTableExists) {
                $maintenanceSQL .= "OPTIMIZE TABLE finances_archive;\n";
            }

            $maintenanceSQL .= "ANALYZE TABLE finances;\n";
            if ($archiveTableExists) {
                $maintenanceSQL .= "ANALYZE TABLE finances_archive;\n";
            }

            // Create a temporary SQL file
            $tempFile = tempnam(sys_get_temp_dir(), 'finance_maintenance_');
            file_put_contents($tempFile, $maintenanceSQL);

            // Get database credentials from the connection
            $dbHost = $this->getConnectionParam('host');
            $dbName = $this->getConnectionParam('dbname');
            $dbUser = $this->getConnectionParam('user');
            $dbPass = $this->getConnectionParam('pass');

            // Build the mysql command
            $command = sprintf(
                'mysql -h %s -u %s -p%s %s < %s 2>&1',
                escapeshellarg($dbHost),
                escapeshellarg($dbUser),
                escapeshellarg($dbPass),
                escapeshellarg($dbName),
                escapeshellarg($tempFile)
            );

            // Execute the command
            $output = [];
            $returnVar = 0;
            exec($command, $output, $returnVar);

            // Delete the temporary file
            unlink($tempFile);

            // Check if the command was successful
            if ($returnVar !== 0) {
                throw new Exception('MySQL command failed: ' . implode("\n", $output));
            }

            // Alternative approach - use direct SQL but handle result sets properly
            // This is a fallback in case the shell command approach doesn't work
            if ($returnVar !== 0) {
                // Reset connection to ensure clean state
                $this->conn = null;
                $this->conn = new PDO(
                    "mysql:host=" . $this->getConnectionParam('host') . ";dbname=" . $this->getConnectionParam('dbname'),
                    $this->getConnectionParam('user'),
                    $this->getConnectionParam('pass'),
                    [PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true]
                );
                $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Run maintenance queries with proper result handling
                $stmt = $this->conn->query("OPTIMIZE TABLE " . $this->table_name);
                $stmt->fetchAll(); // Consume the result set

                if ($archiveTableExists) {
                    $stmt = $this->conn->query("OPTIMIZE TABLE finances_archive");
                    $stmt->fetchAll(); // Consume the result set
                }

                $stmt = $this->conn->query("ANALYZE TABLE " . $this->table_name);
                $stmt->fetchAll(); // Consume the result set

                if ($archiveTableExists) {
                    $stmt = $this->conn->query("ANALYZE TABLE finances_archive");
                    $stmt->fetchAll(); // Consume the result set
                }
            }

            return [
                'status' => 'success',
                'message' => 'Database maintenance completed successfully'
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Error performing maintenance: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get a parameter from the database connection string
     *
     * @param string $param The parameter to get (host, dbname, user, pass)
     * @return string|null The parameter value or null if not found
     */
    private function getConnectionParam($param) {
        // Default values
        $defaults = [
            'host' => 'localhost',
            'dbname' => 'icgc_db',
            'user' => 'root',
            'pass' => ''
        ];

        // Try to get from environment or config
        if (defined('DB_HOST') && $param === 'host') return DB_HOST;
        if (defined('DB_NAME') && $param === 'dbname') return DB_NAME;
        if (defined('DB_USER') && $param === 'user') return DB_USER;
        if (defined('DB_PASS') && $param === 'pass') return DB_PASS;

        // Return default value
        return $defaults[$param] ?? null;
    }

    /**
     * Generate unique reference number
     */
    public function generateReferenceNumber($type = null) {
        $type = $type ?: $this->transaction_type;
        $prefix = $type === 'income' ? 'INC' : 'EXP';
        $date = date('Ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $prefix . $date . $random;
    }

    /**
     * Get expense subcategory for better reporting
     */
    public function getExpenseSubcategory($category) {
        $subcategories = [
            'utilities' => 'Operations',
            'rent' => 'Operations',
            'salaries' => 'Personnel',
            'maintenance' => 'Operations',
            'equipment' => 'Assets',
            'supplies' => 'Operations',
            'events' => 'Ministry',
            'missions' => 'Ministry',
            'charity' => 'Ministry'
        ];

        return $subcategories[$category] ?? 'General';
    }

    /**
     * Validate transaction data
     */
    public function validateTransaction($data) {
        $errors = [];

        // Required fields
        if (empty($data['transaction_type'])) {
            $errors[] = 'Transaction type is required';
        }

        if (empty($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
            $errors[] = 'Valid amount is required';
        }

        if ($data['amount'] > 1000000) {
            $errors[] = 'Amount cannot exceed GH₵ 1,000,000';
        }

        if (empty($data['transaction_date'])) {
            $errors[] = 'Transaction date is required';
        }

        // Category validation
        if ($data['transaction_type'] === 'income' && empty($data['income_category'])) {
            $errors[] = 'Income category is required';
        }

        if ($data['transaction_type'] === 'expense' && empty($data['expense_category'])) {
            $errors[] = 'Expense category is required';
        }

        // Dynamic member requirement check using configuration
        require_once 'config/finance_categories.php';

        if ($data['transaction_type'] === 'income' && !empty($data['income_category'])) {
            if (FinanceCategoriesConfig::requiresMember($data['income_category'], 'income') && empty($data['member_id'])) {
                $categoryConfig = FinanceCategoriesConfig::getCategoryConfig($data['income_category'], 'income');
                $categoryLabel = $categoryConfig ? $categoryConfig['label'] : $data['income_category'];
                $errors[] = 'Member selection is required for ' . strip_tags($categoryLabel);
            }
        }

        if ($data['transaction_type'] === 'expense' && !empty($data['expense_category'])) {
            if (FinanceCategoriesConfig::requiresMember($data['expense_category'], 'expense') && empty($data['member_id'])) {
                $categoryConfig = FinanceCategoriesConfig::getCategoryConfig($data['expense_category'], 'expense');
                $categoryLabel = $categoryConfig ? $categoryConfig['label'] : $data['expense_category'];
                $errors[] = 'Member selection is required for ' . strip_tags($categoryLabel);
            }
        }

        return $errors;
    }


}
