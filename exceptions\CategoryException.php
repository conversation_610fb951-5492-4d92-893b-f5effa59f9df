<?php
/**
 * Category Exception
 * 
 * Exception class for category-related errors.
 * 
 * @package Exceptions
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

require_once 'exceptions/BaseException.php';

class CategoryException extends BaseException
{
    /**
     * Get user-friendly error message
     * 
     * @return string User-friendly error message
     */
    public function getUserMessage(): string
    {
        $message = $this->getMessage();
        
        // Map technical messages to user-friendly ones
        if (strpos($message, 'already exists') !== false) {
            return "A category with this name already exists. Please choose a different name.";
        }
        
        if (strpos($message, 'not found') !== false) {
            return "The requested category could not be found.";
        }
        
        if (strpos($message, 'cannot be deleted') !== false) {
            return "This category cannot be deleted because it has existing transactions or is a core category.";
        }
        
        if (strpos($message, 'Validation failed') !== false) {
            return "Please check your input and try again. " . $message;
        }
        
        return "An error occurred while processing the category. Please try again.";
    }
}
