<?php
/**
 * Setting Controller
 */

require_once 'controllers/BaseRestfulController.php';

class SettingController extends BaseRestfulController {
    private $database;
    private $setting;
    private $dbStats;

    /**
     * Constructor
     */
    public function __construct() {
        // Start session if not already started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        $this->database = new Database();
        $this->setting = new Setting($this->database->getConnection());

        // Create DatabaseStats model if needed
        if (class_exists('DatabaseStats')) {
            require_once 'models/DatabaseStats.php';
            $this->dbStats = new DatabaseStats();
        }
    }

    /**
     * Display settings
     *
     * @return void
     */
    public function index() {
        // Get all settings
        $settings = $this->setting->getAllAsArray();

        // Get database statistics if DatabaseStats model is available
        if (isset($this->dbStats)) {
            $db_stats = $this->dbStats->getDatabaseStats();
            $_SESSION['db_stats'] = $db_stats;
        } elseif (isset($_SESSION['db_stats'])) {
            $db_stats = $_SESSION['db_stats'];
        } else {
            $db_stats = [
                'total_size' => '0 MB',
                'total_tables' => 0,
                'last_optimized' => 'Never',
                'tables' => [],
                'recommendations' => []
            ];
        }

        // Get users data for the users tab
        require_once 'models/User.php';
        $userModel = new User($this->database->getConnection());
        $stmt = $userModel->getAll();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate user statistics
        $totalUsers = count($users);
        $adminCount = 0;
        $staffCount = 0;
        $activeCount = 0;

        foreach ($users as $user) {
            if ($user['role'] === 'admin' || $user['role'] === 'super_admin') {
                $adminCount++;
            } elseif ($user['role'] === 'staff') {
                $staffCount++;
            }
            if ($user['status'] === 'active') {
                $activeCount++;
            }
        }

        // Set page title and active page
        $page_title = getPageTitle('Settings');
        $active_page = 'settings';

        // Logo handling is now done in the view directly

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/settings/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Show edit settings form (RESTful)
     *
     * @return void
     */
    public function edit() {
        // Check authentication and authorization
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        if ($_SESSION['role'] !== 'admin') {
            set_flash_message('Access denied. Admin privileges required.', 'danger');
            redirect('dashboard');
            return;
        }

        // For now, redirect to index with edit mode
        redirect('settings?mode=edit');
    }

    /**
     * Update settings
     *
     * @return void
     */
    public function update() {
        try {
            // Check if form is submitted
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Get form type
            $form_type = $_POST['form_type'] ?? 'branding';

            // Validate form data
            $errors = array();

            if ($form_type === 'sms_settings') {
                // Update SMS settings
                $updated = true; // Assume success initially

                // SMS API key
                if (!$this->setting->update('sms_api_key', sanitize($_POST['sms_api_key'] ?? ''))) {
                    $updated = false;
                }

                // SMS sender ID
                if (!$this->setting->update('sms_sender_id', sanitize($_POST['sms_sender_id'] ?? ''))) {
                    $updated = false;
                }

                if ($updated) {
                    // Set success message
                    set_flash_message('SMS settings updated successfully', 'success');
                } else {
                    // Set error message
                    $_SESSION['errors'] = ['Failed to update SMS settings'];
                }

                redirect('settings#sms');
            } elseif ($form_type === 'phone_settings') {
                // Update phone settings - simplified to avoid PhoneNumberUtils issues
                $default_phone_country = sanitize($_POST['default_phone_country'] ?? 'GH');

                // Simple validation - just check if it's a 2-letter code
                if (strlen($default_phone_country) !== 2 || !ctype_alpha($default_phone_country)) {
                    $default_phone_country = 'GH'; // Fallback to Ghana
                }

                // Convert to uppercase
                $default_phone_country = strtoupper($default_phone_country);

                if ($this->setting->update('default_phone_country', $default_phone_country)) {
                    set_flash_message('Phone settings updated successfully. Default country set to ' . $default_phone_country . '.', 'success');
                } else {
                    set_flash_message('Failed to update phone settings.', 'danger');
                }

                redirect('settings#phone');
            } elseif ($form_type === 'branding') {
                // Handle branding updates - simplified
                $errors = array();

                // Validate required fields
                if (empty($_POST['church_name'])) {
                    $errors[] = 'Organization name is required';
                }

                // If there are errors, redirect back with errors
                if (!empty($errors)) {
                    $_SESSION['errors'] = $errors;
                    $_SESSION['form_data'] = $_POST;
                    redirect('settings#branding');
                    exit;
                }

                $updated = true;

                // Handle logo upload
                if (isset($_FILES['logo_upload']) && $_FILES['logo_upload']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = 'uploads/logos/';

                    // Ensure upload directory exists
                    if (!is_dir($upload_dir)) {
                        if (!mkdir($upload_dir, 0755, true)) {
                            $errors[] = 'Failed to create upload directory';
                            $updated = false;
                        }
                    }

                    if (empty($errors)) {
                        $file_extension = strtolower(pathinfo($_FILES['logo_upload']['name'], PATHINFO_EXTENSION));
                        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'svg'];

                        if (in_array($file_extension, $allowed_extensions)) {
                            // Check file size (2MB max)
                            if ($_FILES['logo_upload']['size'] <= 2 * 1024 * 1024) {
                                // Simple filename
                                $new_filename = 'logo_' . time() . '.' . $file_extension;
                                $target_file = $upload_dir . $new_filename;

                                // Log upload attempt
                                error_log("Attempting to upload logo: " . $_FILES['logo_upload']['name'] . " to " . $target_file);

                                if (move_uploaded_file($_FILES['logo_upload']['tmp_name'], $target_file)) {
                                    // Verify file was created
                                    if (file_exists($target_file)) {
                                        error_log("Logo uploaded successfully: " . $target_file . " (Size: " . filesize($target_file) . " bytes)");

                                        // Delete old logo if exists
                                        $old_logo = $this->setting->getValue('church_logo');
                                        if ($old_logo && file_exists($old_logo)) {
                                            unlink($old_logo);
                                            error_log("Deleted old logo: " . $old_logo);
                                        }

                                        // Update setting with new logo path
                                        if (!$this->setting->update('church_logo', $target_file)) {
                                            $updated = false;
                                        }
                                    } else {
                                        $errors[] = 'Logo file was not created properly';
                                        $updated = false;
                                        error_log("Logo file was not created: " . $target_file);
                                    }
                                } else {
                                    $errors[] = 'Failed to upload logo file';
                                    $updated = false;
                                    error_log("Failed to move uploaded file to: " . $target_file);
                                }
                            } else {
                                $errors[] = 'Logo file size must be less than 2MB';
                                $updated = false;
                            }
                        } else {
                            $errors[] = 'Invalid logo file format. Please use JPG, PNG, GIF, or SVG.';
                            $updated = false;
                        }
                    }
                } elseif (isset($_FILES['logo_upload']) && $_FILES['logo_upload']['error'] !== UPLOAD_ERR_NO_FILE) {
                    // Handle upload errors
                    $upload_errors = [
                        UPLOAD_ERR_INI_SIZE => 'File is too large (server limit)',
                        UPLOAD_ERR_FORM_SIZE => 'File is too large (form limit)',
                        UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
                        UPLOAD_ERR_NO_TMP_DIR => 'No temporary directory',
                        UPLOAD_ERR_CANT_WRITE => 'Cannot write to disk',
                        UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
                    ];
                    $error_message = $upload_errors[$_FILES['logo_upload']['error']] ?? 'Unknown upload error';
                    $errors[] = 'Logo upload failed: ' . $error_message;
                    $updated = false;
                }

                // Update application name (simplified)
                if (!$this->setting->update('church_name', sanitize($_POST['church_name']))) {
                    $updated = false;
                }

                // Logo size settings are now handled automatically

                if (!empty($errors)) {
                    $_SESSION['errors'] = $errors;
                } elseif ($updated) {
                    set_flash_message('Branding updated successfully', 'success');
                } else {
                    $_SESSION['errors'] = ['Failed to update branding settings'];
                }

                redirect('settings#branding');
            } elseif ($form_type === 'reset_logo') {
                // Handle logo reset - simplified
                $current_logo = $this->setting->getValue('church_logo');
                if ($current_logo && file_exists($current_logo)) {
                    unlink($current_logo);
                }

                if ($this->setting->update('church_logo', '')) {
                    set_flash_message('Logo reset to default successfully', 'success');
                } else {
                    $_SESSION['errors'] = ['Failed to reset logo'];
                }

                redirect('settings#branding');
            } else {
                // Invalid form type
                $_SESSION['errors'] = ['Invalid form type'];
                redirect('settings');
            }
            } else {
                // Not a POST request, redirect to settings page
                redirect('settings');
            }
        } catch (Exception $e) {
            error_log("Settings update error: " . $e->getMessage());
            set_flash_message('Settings update failed. Please try again.', 'danger');
            redirect('settings');
        }
    }

    /**
     * Backup database
     * 
     * @return void
     */
    public function backup() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to backup the database.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        try {
            // Get database credentials
            require_once 'config/database.php';
            
            // Set backup filename
            $backupFile = 'icgc_backup_' . date('Y-m-d_H-i-s') . '.sql';
            
            // Set headers for download
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename=' . $backupFile);
            
            // Use mysqldump to create backup
            $command = sprintf(
                'mysqldump --host=%s --user=%s --password=%s %s',
                escapeshellarg(DB_HOST),
                escapeshellarg(DB_USER),
                escapeshellarg(DB_PASS),
                escapeshellarg(DB_NAME)
            );
            
            // Execute command and output directly to browser
            passthru($command);
            
            // Log the backup operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'backup',
                    'Database backup created',
                    'success',
                    'all',
                    0,
                    0,
                    0
                );
            }
            
            exit;
        } catch (Exception $e) {
            // Set error message
            $_SESSION['flash_message'] = 'Error backing up database: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            
            // Log the failed operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'backup',
                    'Error: ' . $e->getMessage(),
                    'error',
                    '',
                    0,
                    0,
                    0
                );
            }
            
            // Redirect back to settings page
            header('Location: ' . BASE_URL . 'settings#database');
            exit;
        }
    }
    
    /**
     * Restore database
     * 
     * @return void
     */
    public function restore() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to restore the database.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }

        // Redirect to settings page - restore functionality moved to MaintenanceController
        redirect('settings#database');
    }

    /**
     * Refresh database statistics
     *
     * @return void
     */
    public function refreshStats() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to access database statistics.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings');
            exit;
        }

        // Check if DatabaseStats model is available
        if (!isset($this->dbStats)) {
            $_SESSION['flash_message'] = 'Database statistics functionality is not available.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings#database');
            exit;
        }

        // Get database statistics
        $db_stats = $this->dbStats->getDatabaseStats();

        // Store in session for display
        $_SESSION['db_stats'] = $db_stats;

        // Set flash message
        $_SESSION['flash_message'] = 'Database statistics refreshed successfully.';
        $_SESSION['flash_type'] = 'success';

        // Redirect back to settings page
        redirect('settings#database/system-maintenance');
    }

    /**
     * Optimize database
     *
     * @return void
     */
    public function optimizeDatabase() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to optimize the database.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings');
            exit;
        }

        // Check if DatabaseStats model is available
        if (!isset($this->dbStats)) {
            $_SESSION['flash_message'] = 'Database optimization functionality is not available.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings#database');
            exit;
        }

        try {
            // Optimize database tables
            $result = $this->dbStats->optimizeTables();

            // Set flash message
            $_SESSION['flash_message'] = $result['message'];
            $_SESSION['flash_type'] = $result['status'];

            // Store optimization details in session
            $_SESSION['optimization_details'] = $result['details'];

            // Refresh database statistics
            $_SESSION['db_stats'] = $this->dbStats->getDatabaseStats();

        } catch (Exception $e) {
            // Set error message
            $_SESSION['flash_message'] = 'Error optimizing database: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
        }

        // Redirect back to settings page
        redirect('settings#database/system-maintenance');
    }

    /**
     * Display archiving options
     *
     * @return void
     */
    public function archiving() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to access archiving options.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings');
            exit;
        }

        // Check if DatabaseStats model is available
        if (!isset($this->dbStats)) {
            $_SESSION['flash_message'] = 'Archiving functionality is not available.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings#database');
            exit;
        }

        // Get archivable data types
        $dataTypes = $this->dbStats->getArchivableDataTypes();

        // Set page title and active page
        $page_title = 'Data Archiving - ICGC Emmanuel Temple';
        $active_page = 'settings';

        // Start output buffering
        ob_start();

        // Include the view
        require_once 'views/settings/archiving.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Process archiving request
     *
     * @return void
     */
    public function processArchiving() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to archive data.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings');
            exit;
        }

        // Check if DatabaseStats model is available
        if (!isset($this->dbStats)) {
            $_SESSION['flash_message'] = 'Archiving functionality is not available.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings#database');
            exit;
        }

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('settings/archiving');
            exit;
        }

        // Get form data
        $dataType = isset($_POST['data_type']) ? $_POST['data_type'] : '';
        $cutoffDate = isset($_POST['cutoff_date']) ? $_POST['cutoff_date'] : '';
        $confirm = isset($_POST['confirm']) ? true : false;

        // Validate form data
        if (empty($dataType) || empty($cutoffDate) || !$confirm) {
            $_SESSION['flash_message'] = 'Please fill in all required fields and confirm the archiving operation.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings/archiving');
            exit;
        }

        // Process archiving based on data type
        switch ($dataType) {
            case 'finances':
                $this->archiveFinances($cutoffDate);
                break;
            case 'attendance':
                $this->archiveAttendance($cutoffDate);
                break;
            case 'sms_messages':
                $this->archiveSmsMessages($cutoffDate);
                break;
            case 'visitors':
                $this->archiveVisitors($cutoffDate);
                break;
            case 'equipment_maintenance':
                $this->archiveEquipmentMaintenance($cutoffDate);
                break;
            default:
                $_SESSION['flash_message'] = 'Invalid data type selected.';
                $_SESSION['flash_type'] = 'danger';
                redirect('settings/archiving');
                exit;
        }

        // Redirect back to archiving page
        redirect('settings/archiving');
    }

    /**
     * Archive finances
     *
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveFinances($cutoffDate) {
        try {
            // Create Finance model
            require_once 'models/Finance.php';
            $finance = new Finance();

            // Archive old records
            $result = $finance->archiveOldRecords($cutoffDate);

            // Set flash message
            $_SESSION['flash_message'] = $result['message'];
            $_SESSION['flash_type'] = $result['status'];

        } catch (Exception $e) {
            $_SESSION['flash_message'] = 'Error archiving finances: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
        }
    }

    /**
     * Archive attendance records
     *
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveAttendance($cutoffDate) {
        try {
            // Create Attendance model
            require_once 'models/Attendance.php';
            $attendance = new Attendance();

            // Check if archive table exists, create if not
            $this->createArchiveTable('attendance', 'attendance_archive');

            // Archive old records
            $startTime = microtime(true);

            // Begin transaction
            $this->database->getConnection()->beginTransaction();

            // Move old records to archive table
            $moveQuery = "INSERT INTO attendance_archive
                SELECT *, NOW() as archived_at FROM attendance
                WHERE attendance_date < :cutoff_date";
            $moveStmt = $this->database->getConnection()->prepare($moveQuery);
            $moveStmt->bindParam(':cutoff_date', $cutoffDate);
            $moveStmt->execute();
            $archivedCount = $moveStmt->rowCount();

            // Delete archived records from main table
            $deleteQuery = "DELETE FROM attendance WHERE attendance_date < :cutoff_date";
            $deleteStmt = $this->database->getConnection()->prepare($deleteQuery);
            $deleteStmt->bindParam(':cutoff_date', $cutoffDate);
            $deleteStmt->execute();

            // Commit transaction
            $this->database->getConnection()->commit();

            // Calculate duration
            $duration = microtime(true) - $startTime;

            // Log the maintenance operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'archiving',
                    "Archived attendance records before {$cutoffDate}",
                    'success',
                    'attendance,attendance_archive',
                    $archivedCount,
                    $duration,
                    0
                );
            }

            // Set flash message
            $_SESSION['flash_message'] = "{$archivedCount} attendance records successfully archived.";
            $_SESSION['flash_type'] = 'success';

        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->database->getConnection()->inTransaction()) {
                $this->database->getConnection()->rollBack();
            }

            $_SESSION['flash_message'] = 'Error archiving attendance records: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';

            // Log the failed operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'archiving',
                    'Error archiving attendance records: ' . $e->getMessage(),
                    'error',
                    'attendance',
                    0,
                    0,
                    0
                );
            }
        }
    }

    /**
     * Archive SMS messages
     *
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveSmsMessages($cutoffDate) {
        try {
            // Create SMS model
            require_once 'models/Sms.php';
            $sms = new Sms();

            // Check if archive table exists, create if not
            $this->createArchiveTable('sms_messages', 'sms_messages_archive');

            // Archive old records
            $startTime = microtime(true);

            // Begin transaction
            $this->database->getConnection()->beginTransaction();

            // Move old records to archive table
            $moveQuery = "INSERT INTO sms_messages_archive
                SELECT *, NOW() as archived_at FROM sms_messages
                WHERE sent_at < :cutoff_date";
            $moveStmt = $this->database->getConnection()->prepare($moveQuery);
            $moveStmt->bindParam(':cutoff_date', $cutoffDate);
            $moveStmt->execute();
            $archivedCount = $moveStmt->rowCount();

            // Delete archived records from main table
            $deleteQuery = "DELETE FROM sms_messages WHERE sent_at < :cutoff_date";
            $deleteStmt = $this->database->getConnection()->prepare($deleteQuery);
            $deleteStmt->bindParam(':cutoff_date', $cutoffDate);
            $deleteStmt->execute();

            // Commit transaction
            $this->database->getConnection()->commit();

            // Calculate duration
            $duration = microtime(true) - $startTime;

            // Log the maintenance operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'archiving',
                    "Archived SMS messages before {$cutoffDate}",
                    'success',
                    'sms_messages,sms_messages_archive',
                    $archivedCount,
                    $duration,
                    0
                );
            }

            // Set flash message
            $_SESSION['flash_message'] = "{$archivedCount} SMS messages successfully archived.";
            $_SESSION['flash_type'] = 'success';

        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->database->getConnection()->inTransaction()) {
                $this->database->getConnection()->rollBack();
            }

            $_SESSION['flash_message'] = 'Error archiving SMS messages: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';

            // Log the failed operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'archiving',
                    'Error archiving SMS messages: ' . $e->getMessage(),
                    'error',
                    'sms_messages',
                    0,
                    0,
                    0
                );
            }
        }
    }

    /**
     * Archive visitor records
     *
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveVisitors($cutoffDate) {
        try {
            // Create Visitor model
            require_once 'models/Visitor.php';
            $visitor = new Visitor();

            // Check if archive table exists, create if not
            $this->createArchiveTable('visitors', 'visitors_archive');

            // Archive old records
            $startTime = microtime(true);

            // Begin transaction
            $this->database->getConnection()->beginTransaction();

            // Move old records to archive table
            $moveQuery = "INSERT INTO visitors_archive
                SELECT *, NOW() as archived_at FROM visitors
                WHERE visit_date < :cutoff_date";
            $moveStmt = $this->database->getConnection()->prepare($moveQuery);
            $moveStmt->bindParam(':cutoff_date', $cutoffDate);
            $moveStmt->execute();
            $archivedCount = $moveStmt->rowCount();

            // Delete archived records from main table
            $deleteQuery = "DELETE FROM visitors WHERE visit_date < :cutoff_date";
            $deleteStmt = $this->database->getConnection()->prepare($deleteQuery);
            $deleteStmt->bindParam(':cutoff_date', $cutoffDate);
            $deleteStmt->execute();

            // Commit transaction
            $this->database->getConnection()->commit();

            // Calculate duration
            $duration = microtime(true) - $startTime;

            // Log the maintenance operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'archiving',
                    "Archived visitor records before {$cutoffDate}",
                    'success',
                    'visitors,visitors_archive',
                    $archivedCount,
                    $duration,
                    0
                );
            }

            // Set flash message
            $_SESSION['flash_message'] = "{$archivedCount} visitor records successfully archived.";
            $_SESSION['flash_type'] = 'success';

        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->database->getConnection()->inTransaction()) {
                $this->database->getConnection()->rollBack();
            }

            $_SESSION['flash_message'] = 'Error archiving visitor records: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';

            // Log the failed operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'archiving',
                    'Error archiving visitor records: ' . $e->getMessage(),
                    'error',
                    'visitors',
                    0,
                    0,
                    0
                );
            }
        }
    }

    /**
     * Archive equipment maintenance records
     *
     * @param string $cutoffDate Cutoff date for archiving
     * @return void
     */
    private function archiveEquipmentMaintenance($cutoffDate) {
        try {
            // Create Equipment model
            require_once 'models/Equipment.php';
            $equipment = new Equipment();

            // Check if archive table exists, create if not
            $this->createArchiveTable('equipment_maintenance', 'equipment_maintenance_archive');

            // Archive old records
            $startTime = microtime(true);

            // Begin transaction
            $this->database->getConnection()->beginTransaction();

            // Move old records to archive table
            $moveQuery = "INSERT INTO equipment_maintenance_archive
                SELECT *, NOW() as archived_at FROM equipment_maintenance
                WHERE maintenance_date < :cutoff_date";
            $moveStmt = $this->database->getConnection()->prepare($moveQuery);
            $moveStmt->bindParam(':cutoff_date', $cutoffDate);
            $moveStmt->execute();
            $archivedCount = $moveStmt->rowCount();

            // Delete archived records from main table
            $deleteQuery = "DELETE FROM equipment_maintenance WHERE maintenance_date < :cutoff_date";
            $deleteStmt = $this->database->getConnection()->prepare($deleteQuery);
            $deleteStmt->bindParam(':cutoff_date', $cutoffDate);
            $deleteStmt->execute();

            // Commit transaction
            $this->database->getConnection()->commit();

            // Calculate duration
            $duration = microtime(true) - $startTime;

            // Log the maintenance operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'archiving',
                    "Archived equipment maintenance records before {$cutoffDate}",
                    'success',
                    'equipment_maintenance,equipment_maintenance_archive',
                    $archivedCount,
                    $duration,
                    0
                );
            }

            // Set flash message
            $_SESSION['flash_message'] = "{$archivedCount} equipment maintenance records successfully archived.";
            $_SESSION['flash_type'] = 'success';

        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->database->getConnection()->inTransaction()) {
                $this->database->getConnection()->rollBack();
            }

            $_SESSION['flash_message'] = 'Error archiving equipment maintenance records: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';

            // Log the failed operation
            if (isset($this->dbStats)) {
                $this->dbStats->logMaintenanceOperation(
                    'archiving',
                    'Error archiving equipment maintenance records: ' . $e->getMessage(),
                    'error',
                    'equipment_maintenance',
                    0,
                    0,
                    0
                );
            }
        }
    }

    /**
     * Create archive table if it doesn't exist
     *
     * @param string $sourceTable Source table name
     * @param string $archiveTable Archive table name
     * @return bool Success
     */
    private function createArchiveTable($sourceTable, $archiveTable) {
        try {
            // Check if archive table exists
            $checkQuery = "SHOW TABLES LIKE '{$archiveTable}'";
            $checkStmt = $this->database->getConnection()->query($checkQuery);

            if ($checkStmt && $checkStmt->rowCount() === 0) {
                // Get source table structure
                $structureQuery = "SHOW CREATE TABLE {$sourceTable}";
                $structureStmt = $this->database->getConnection()->query($structureQuery);
                $structureRow = $structureStmt->fetch(PDO::FETCH_ASSOC);

                if (isset($structureRow['Create Table'])) {
                    // Modify create table statement for archive table
                    $createTableSql = $structureRow['Create Table'];
                    $createTableSql = str_replace("CREATE TABLE `{$sourceTable}`", "CREATE TABLE `{$archiveTable}`", $createTableSql);

                    // Add archived_at column if it doesn't exist in the statement
                    if (strpos($createTableSql, 'archived_at') === false) {
                        $createTableSql = str_replace('PRIMARY KEY', 'archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, PRIMARY KEY', $createTableSql);
                    }

                    // Create archive table
                    $this->database->getConnection()->exec($createTableSql);

                    return true;
                }
            }

            return false;
        } catch (PDOException $e) {
            error_log("Error creating archive table: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Display maintenance log
     *
     * @return void
     */
    public function maintenanceLog() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to access the maintenance log.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings');
            exit;
        }

        // Check if DatabaseStats model is available
        if (!isset($this->dbStats)) {
            $_SESSION['flash_message'] = 'Maintenance log functionality is not available.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings#database');
            exit;
        }

        // Get page and limit parameters
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;

        // Calculate offset
        $offset = ($page - 1) * $limit;

        // Get maintenance log entries
        $logEntries = $this->dbStats->getMaintenanceLog($limit, $offset);
        $totalCount = $this->dbStats->getMaintenanceLogCount();

        // Calculate total pages
        $totalPages = ceil($totalCount / $limit);

        // Set page title and active page
        $page_title = 'Maintenance Log - ICGC Emmanuel Temple';
        $active_page = 'settings';

        // Start output buffering
        ob_start();

        // Include the view
        require_once 'views/settings/maintenance_log.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display maintenance scheduling page
     *
     * @return void
     */
    public function maintenanceSchedule() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to access maintenance scheduling.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings');
            exit;
        }

        // Check if DatabaseStats model is available
        if (!isset($this->dbStats)) {
            $_SESSION['flash_message'] = 'Maintenance scheduling functionality is not available.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings#database');
            exit;
        }

        // Set page title and active page
        $page_title = getPageTitle('Maintenance Scheduling');
        $active_page = 'settings';

        // Start output buffering
        ob_start();

        // Include the view
        require_once 'views/settings/maintenance_schedule.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Process maintenance schedule
     *
     * @return void
     */
    public function processSchedule() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to schedule maintenance.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings');
            exit;
        }

        // Check if DatabaseStats model is available
        if (!isset($this->dbStats)) {
            $_SESSION['flash_message'] = 'Maintenance scheduling functionality is not available.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings#database');
            exit;
        }

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('settings/maintenance-schedule');
            exit;
        }

        // Get form data
        $scheduleType = isset($_POST['schedule_type']) ? $_POST['schedule_type'] : '';
        $scheduleDay = isset($_POST['schedule_day']) ? (int)$_POST['schedule_day'] : 1;
        $scheduleTime = isset($_POST['schedule_time']) ? $_POST['schedule_time'] : '00:00';
        $maintenanceTypes = isset($_POST['maintenance_types']) ? $_POST['maintenance_types'] : [];

        // Validate form data
        if (empty($scheduleType) || empty($scheduleTime) || empty($maintenanceTypes)) {
            $_SESSION['flash_message'] = 'Please fill in all required fields.';
            $_SESSION['flash_type'] = 'danger';
            redirect('settings/maintenance-schedule');
            exit;
        }

        // Save schedule settings
        $settings = [
            'schedule_type' => $scheduleType,
            'schedule_day' => $scheduleDay,
            'schedule_time' => $scheduleTime,
            'maintenance_types' => $maintenanceTypes,
            'last_updated' => date('Y-m-d H:i:s'),
            'updated_by' => $_SESSION['user_id']
        ];

        // Save settings to database
        $this->saveMaintenanceSettings($settings);

        // Set flash message
        $_SESSION['flash_message'] = 'Maintenance schedule saved successfully.';
        $_SESSION['flash_type'] = 'success';

        // Redirect back to maintenance schedule page
        redirect('settings/maintenance-schedule');
    }

    /**
     * Save maintenance settings
     *
     * @param array $settings Settings to save
     * @return bool Success
     */
    private function saveMaintenanceSettings($settings) {
        try {
            // Convert settings to JSON
            $settingsJson = json_encode($settings);

            // Check if settings already exist
            $checkQuery = "SELECT id FROM settings WHERE setting_key = 'maintenance_schedule'";
            $checkStmt = $this->database->getConnection()->query($checkQuery);

            if ($checkStmt && $checkStmt->rowCount() > 0) {
                // Update existing settings
                $updateQuery = "UPDATE settings SET setting_value = :value WHERE setting_key = 'maintenance_schedule'";
                $updateStmt = $this->database->getConnection()->prepare($updateQuery);
                $updateStmt->bindParam(':value', $settingsJson);
                return $updateStmt->execute();
            } else {
                // Insert new settings
                $insertQuery = "INSERT INTO settings (setting_key, setting_value) VALUES ('maintenance_schedule', :value)";
                $insertStmt = $this->database->getConnection()->prepare($insertQuery);
                $insertStmt->bindParam(':value', $settingsJson);
                return $insertStmt->execute();
            }
        } catch (PDOException $e) {
            error_log("Error saving maintenance settings: " . $e->getMessage());
            return false;
        }
    }
}
