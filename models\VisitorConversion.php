<?php
/**
 * Visitor Conversion Model
 * 
 * This model handles all database operations related to visitor conversions
 */

class VisitorConversion {
    private $conn;
    private $table = 'visitor_conversions';

    // Conversion properties
    public $id;
    public $visitor_id;
    public $member_id;
    public $conversion_date;
    public $notes;
    public $created_at;
    public $updated_at;

    /**
     * Constructor
     * 
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all conversions
     * 
     * @param string $orderBy Column to order by
     * @param string $order Order direction (ASC or DESC)
     * @return PDOStatement
     */
    public function getAll($orderBy = 'conversion_date', $order = 'DESC') {
        $query = "SELECT c.*, v.first_name as visitor_first_name, v.last_name as visitor_last_name,
                  m.first_name as member_first_name, m.last_name as member_last_name
                  FROM " . $this->table . " c
                  LEFT JOIN visitors v ON c.visitor_id = v.id
                  LEFT JOIN members m ON c.member_id = m.id
                  ORDER BY " . $orderBy . " " . $order;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get conversion by ID
     * 
     * @param int $id Conversion ID
     * @return bool
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($row) {
            $this->id = $row['id'];
            $this->visitor_id = $row['visitor_id'];
            $this->member_id = $row['member_id'];
            $this->conversion_date = $row['conversion_date'];
            $this->notes = $row['notes'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            
            return true;
        }
        
        return false;
    }

    /**
     * Get conversion by visitor ID
     * 
     * @param int $visitor_id Visitor ID
     * @return bool
     */
    public function getByVisitorId($visitor_id) {
        $query = "SELECT c.*, m.first_name, m.last_name 
                  FROM " . $this->table . " c
                  LEFT JOIN members m ON c.member_id = m.id
                  WHERE c.visitor_id = :visitor_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':visitor_id', $visitor_id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($row) {
            $this->id = $row['id'];
            $this->visitor_id = $row['visitor_id'];
            $this->member_id = $row['member_id'];
            $this->conversion_date = $row['conversion_date'];
            $this->notes = $row['notes'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            
            return true;
        }
        
        return false;
    }

    /**
     * Create new conversion
     * 
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table . " 
                  (visitor_id, member_id, conversion_date, notes, created_at, updated_at) 
                  VALUES 
                  (:visitor_id, :member_id, :conversion_date, :notes, :created_at, :updated_at)";
        
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        
        // Set timestamps
        $this->created_at = date('Y-m-d H:i:s');
        $this->updated_at = date('Y-m-d H:i:s');
        
        // Bind parameters
        $stmt->bindParam(':visitor_id', $this->visitor_id);
        $stmt->bindParam(':member_id', $this->member_id);
        $stmt->bindParam(':conversion_date', $this->conversion_date);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);
        
        // Execute query
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            
            // Update visitor status to 'converted'
            $query = "UPDATE visitors SET visitor_status = 'converted', updated_at = :updated_at WHERE id = :visitor_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':updated_at', $this->updated_at);
            $stmt->bindParam(':visitor_id', $this->visitor_id);
            $stmt->execute();
            
            return true;
        }
        
        return false;
    }

    /**
     * Update conversion
     * 
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table . " 
                  SET 
                  visitor_id = :visitor_id, 
                  member_id = :member_id, 
                  conversion_date = :conversion_date, 
                  notes = :notes, 
                  updated_at = :updated_at 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        
        // Set updated timestamp
        $this->updated_at = date('Y-m-d H:i:s');
        
        // Bind parameters
        $stmt->bindParam(':visitor_id', $this->visitor_id);
        $stmt->bindParam(':member_id', $this->member_id);
        $stmt->bindParam(':conversion_date', $this->conversion_date);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);
        
        // Execute query
        if ($stmt->execute()) {
            return true;
        }
        
        return false;
    }

    /**
     * Delete conversion
     * 
     * @param int $id Conversion ID
     * @return bool
     */
    public function delete($id) {
        // Get visitor ID before deleting
        $query = "SELECT visitor_id FROM " . $this->table . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $visitor_id = $row['visitor_id'];
        
        // Delete conversion
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute()) {
            // Update visitor status back to 'in_follow_up'
            $updated_at = date('Y-m-d H:i:s');
            $query = "UPDATE visitors SET visitor_status = 'in_follow_up', updated_at = :updated_at WHERE id = :visitor_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':updated_at', $updated_at);
            $stmt->bindParam(':visitor_id', $visitor_id);
            $stmt->execute();
            
            return true;
        }
        
        return false;
    }

    /**
     * Check if visitor has been converted
     * 
     * @param int $visitor_id Visitor ID
     * @return bool
     */
    public function isConverted($visitor_id) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table . " WHERE visitor_id = :visitor_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':visitor_id', $visitor_id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'] > 0;
    }

    /**
     * Get conversion statistics
     * 
     * @return array
     */
    public function getStatistics() {
        $stats = [];
        
        // Total conversions
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Recent conversions (last 30 days)
        $query = "SELECT COUNT(*) as recent FROM " . $this->table . " 
                  WHERE conversion_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['recent'] = $stmt->fetch(PDO::FETCH_ASSOC)['recent'];
        
        // Conversion rate
        $query = "SELECT 
                  (SELECT COUNT(*) FROM " . $this->table . ") as conversions,
                  (SELECT COUNT(*) FROM visitors) as visitors";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['rate'] = $row['visitors'] > 0 ? round(($row['conversions'] / $row['visitors']) * 100, 2) : 0;
        
        return $stats;
    }
}
