<div class="container mx-auto px-4 max-w-3xl">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-green-600 to-teal-500 p-6 text-white">
            <div class="flex flex-row justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Edit Service</h1>
                    <p class="text-sm opacity-90 mt-1">Update service information</p>
                </div>
                <div class="flex">
                    <a href="<?php echo BASE_URL; ?>services" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Services
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <p class="font-bold">Please fix the following errors:</p>
            <ul class="list-disc ml-5 mt-2">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Service Form -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-green-50 to-teal-50 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-green-100 text-green-600 mr-3">
                    <i class="fas fa-church text-lg"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-800">Service Information</h2>
                    <p class="text-sm text-gray-600">Update the details for this service</p>
                </div>
            </div>
        </div>

        <div class="p-6">
            <form action="<?php echo url('services/' . $service->id); ?>" method="POST">
                <input type="hidden" name="id" value="<?php echo $service->id; ?>">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Service Name -->
                    <div class="mb-3 md:col-span-2">
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Service Name <span class="text-red-500">*</span></label>
                        <input type="text" id="name" name="name" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 px-3 py-2" value="<?php echo isset($_SESSION['form_data']['name']) ? $_SESSION['form_data']['name'] : $service->name; ?>" required>
                        <p class="text-xs text-gray-500 mt-1">Example: Sunday Morning Service, Wednesday Bible Study, etc.</p>
                    </div>

                    <!-- Day of Week -->
                    <div class="mb-3">
                        <label for="day_of_week" class="block text-sm font-medium text-gray-700 mb-1">Day of Week <span class="text-red-500">*</span></label>
                        <select id="day_of_week" name="day_of_week" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 px-3 py-2" required>
                            <option value="">Select Day</option>
                            <?php
                            $days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
                            $current_day = isset($_SESSION['form_data']['day_of_week']) ? $_SESSION['form_data']['day_of_week'] : $service->day_of_week;

                            foreach ($days as $day) {
                                $selected = ($current_day == $day) ? 'selected' : '';
                                echo "<option value=\"$day\" $selected>" . ucfirst($day) . "</option>";
                            }
                            ?>
                        </select>
                    </div>

                    <!-- Time -->
                    <div class="mb-3">
                        <label for="time" class="block text-sm font-medium text-gray-700 mb-1">Time <span class="text-red-500">*</span></label>
                        <input type="time" id="time" name="time" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 px-3 py-2" value="<?php echo isset($_SESSION['form_data']['time']) ? $_SESSION['form_data']['time'] : $service->time; ?>" required>
                    </div>

                    <!-- Description -->
                    <div class="mb-4 md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="description" name="description" rows="2" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 px-3 py-2"><?php echo isset($_SESSION['form_data']['description']) ? $_SESSION['form_data']['description'] : $service->description; ?></textarea>
                        <p class="text-xs text-gray-500 mt-1">Optional: Provide additional details about this service</p>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-200">
                        <i class="fas fa-save mr-2"></i> Update Service
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Clear form data after displaying
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>
