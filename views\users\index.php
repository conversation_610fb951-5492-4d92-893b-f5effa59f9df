<div class="container mx-auto fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white">
            <h1 class="text-3xl font-bold">User Management</h1>
            <p class="mt-2 opacity-90">Manage system users and access control</p>
        </div>
        <div class="p-6 bg-white">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Quick Stats Cards -->
                <div class="flex flex-wrap gap-4 mb-4 md:mb-0">
                    <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-primary flex items-center w-40">
                        <div class="rounded-full bg-primary-light bg-opacity-20 p-3 mr-3">
                            <i class="fas fa-users text-primary"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gray-800">
                                <?php echo count($users); ?>
                            </div>
                            <div class="text-xs text-gray-500">Total Users</div>
                        </div>
                    </div>

                    <?php
                    $adminCount = 0;
                    $staffCount = 0;
                    $superAdminCount = 0;

                    foreach ($users as $user) {
                        if ($user['role'] === 'admin') {
                            $adminCount++;
                        } elseif ($user['role'] === 'staff') {
                            $staffCount++;
                        } elseif ($user['role'] === 'super_admin') {
                            $superAdminCount++;
                        }
                    }
                    ?>

                    <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500 flex items-center w-40">
                        <div class="rounded-full bg-blue-100 p-3 mr-3">
                            <i class="fas fa-user-shield text-blue-500"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gray-800">
                                <?php echo $adminCount; ?>
                            </div>
                            <div class="text-xs text-gray-500">Admins</div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-green-500 flex items-center w-40">
                        <div class="rounded-full bg-green-100 p-3 mr-3">
                            <i class="fas fa-user-tie text-green-500"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gray-800">
                                <?php echo $staffCount; ?>
                            </div>
                            <div class="text-xs text-gray-500">Staff</div>
                        </div>
                    </div>
                </div>
                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    <a href="<?php echo BASE_URL; ?>users/add" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 shadow-md">
                        <i class="fas fa-user-plus mr-2"></i> Add New User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden card-table">
        <div class="card-header flex justify-between items-center">
            <h2 class="card-title flex items-center">
                <i class="fas fa-user-shield text-primary mr-2"></i>
                System Users
            </h2>
            <div class="relative">
                <input type="text" id="userSearch" placeholder="Search users..." class="rounded-lg border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 pl-10 py-2 text-sm">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($users)) : ?>
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No users found</td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ($users as $user) : ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-primary-light flex items-center justify-center text-white font-bold">
                                                <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900"><?php echo $user['username']; ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo $user['email']; ?></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($user['role'] === 'super_admin') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Super Admin</span>
                                    <?php elseif ($user['role'] === 'admin') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Admin</span>
                                    <?php else : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Staff</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <?php if (!isset($user['status']) || $user['status'] === 'active') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    <?php else : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            Blocked
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo format_date($user['created_at']); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <!-- Debug info -->
                                    <?php $current_user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'not set'; ?>

                                    <div class="action-buttons" style="display: flex; gap: 8px;">
                                        <!-- Edit button - always visible -->
                                        <a href="<?php echo BASE_URL; ?>users/edit?id=<?php echo $user['id']; ?>"
                                           style="display: inline-block; padding: 8px; background-color: #dbeafe; color: #2563eb; border-radius: 6px;">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <!-- Block/Unblock and Delete buttons - Only for other users -->
                                        <!-- Block/Unblock button -->
                                        <a href="<?php echo BASE_URL; ?>users/toggle-status?id=<?php echo $user['id']; ?>"
                                           style="display: inline-block; padding: 8px; background-color: #fef3c7; color: #d97706; border-radius: 6px;">
                                            <i class="fas fa-ban"></i>
                                        </a>

                                        <!-- Delete button -->
                                        <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $user['id']; ?>)"
                                           style="display: inline-block; padding: 8px; background-color: #fee2e2; color: #dc2626; border-radius: 6px;">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-lg bg-white">
        <div class="absolute top-3 right-3">
            <button id="closeModal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-xl leading-6 font-medium text-gray-900 mt-4">Delete User</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Are you sure you want to delete this user? This action cannot be undone.</p>
            </div>
            <div class="flex justify-center mt-4 px-4 py-3 space-x-4">
                <button id="cancelDelete" class="bg-gray-100 border border-gray-300 px-5 py-2 rounded-lg text-gray-800 hover:bg-gray-200 transition-colors duration-200 flex items-center">
                    <i class="fas fa-times mr-2"></i> Cancel
                </button>
                <a id="confirmDelete" href="#" class="bg-red-600 px-5 py-2 rounded-lg text-white hover:bg-red-700 transition-colors duration-200 flex items-center">
                    <i class="fas fa-trash-alt mr-2"></i> Delete
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Delete confirmation
    function confirmDelete(id) {
        console.log('Confirm delete called for ID:', id); // Debug log
        const modal = document.getElementById('deleteModal');
        const confirmBtn = document.getElementById('confirmDelete');

        if (!modal || !confirmBtn) {
            console.error('Delete modal elements not found');
            return;
        }

        modal.classList.remove('hidden');
        confirmBtn.href = '<?php echo BASE_URL; ?>users/delete?id=' + id;

        // Remove any existing event listeners to prevent duplicates
        const cancelBtn = document.getElementById('cancelDelete');
        const closeBtn = document.getElementById('closeModal');

        if (cancelBtn) {
            const newCancelBtn = cancelBtn.cloneNode(true);
            cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);
            newCancelBtn.addEventListener('click', function() {
                modal.classList.add('hidden');
            });
        }

        if (closeBtn) {
            const newCloseBtn = closeBtn.cloneNode(true);
            closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);
            newCloseBtn.addEventListener('click', function() {
                modal.classList.add('hidden');
            });
        }
    }

    // User search functionality and action icons initialization
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('userSearch');
        const tableRows = document.querySelectorAll('tbody tr');

        // Initialize search functionality
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchTerm = this.value.toLowerCase();

                tableRows.forEach(row => {
                    const username = row.querySelector('td:first-child').textContent.toLowerCase();
                    const email = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                    const role = row.querySelector('td:nth-child(3)').textContent.toLowerCase();

                    if (username.includes(searchTerm) || email.includes(searchTerm) || role.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // Ensure action icons are visible
        const actionIcons = document.querySelectorAll('.flex.space-x-2 a');
        actionIcons.forEach(icon => {
            icon.style.display = 'inline-flex';
            icon.style.visibility = 'visible';
            icon.style.opacity = '1';
            console.log('Action icon initialized:', icon.title);
        });
    });
</script>

<!-- Add fade-in animation CSS -->
<style>
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Hover effect for table rows */
    tbody tr {
        transition: all 0.2s ease-in-out;
    }

    tbody tr:hover {
        background-color: rgba(243, 244, 246, 0.5);
    }

    /* Ensure action icons are always visible */
    .flex.space-x-2 a {
        display: inline-flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Ensure proper spacing between action icons */
    .flex.space-x-2 {
        display: flex !important;
        gap: 0.5rem !important;
    }
</style>
