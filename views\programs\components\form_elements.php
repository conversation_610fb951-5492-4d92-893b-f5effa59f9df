<?php
/**
 * Shared Form Elements for Programs
 * Common form fields used across create/edit forms
 */

// Form field helper function
function render_form_field($type, $name, $label, $options = []) {
    $required = $options['required'] ?? false;
    $value = $options['value'] ?? '';
    $placeholder = $options['placeholder'] ?? '';
    $help_text = $options['help_text'] ?? '';
    $class = $options['class'] ?? 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200';
    $rows = $options['rows'] ?? 4;
    $select_options = $options['options'] ?? [];
    $selected = $options['selected'] ?? '';
    
    $required_mark = $required ? '<span class="text-red-500">*</span>' : '';
    
    echo '<div>';
    echo '<label for="' . $name . '" class="block text-sm font-medium text-gray-700 mb-2">';
    echo $label . ' ' . $required_mark;
    echo '</label>';
    
    switch ($type) {
        case 'text':
        case 'email':
        case 'tel':
        case 'url':
        case 'number':
            echo '<input type="' . $type . '" id="' . $name . '" name="' . $name . '"';
            if ($required) echo ' required';
            if ($value) echo ' value="' . htmlspecialchars($value) . '"';
            if ($placeholder) echo ' placeholder="' . htmlspecialchars($placeholder) . '"';
            echo ' class="' . $class . '">';
            break;
            
        case 'textarea':
            echo '<textarea id="' . $name . '" name="' . $name . '" rows="' . $rows . '"';
            if ($required) echo ' required';
            if ($placeholder) echo ' placeholder="' . htmlspecialchars($placeholder) . '"';
            echo ' class="' . $class . '">';
            echo htmlspecialchars($value);
            echo '</textarea>';
            break;
            
        case 'select':
            echo '<select id="' . $name . '" name="' . $name . '"';
            if ($required) echo ' required';
            echo ' class="' . $class . '">';
            if ($placeholder) {
                echo '<option value="">' . htmlspecialchars($placeholder) . '</option>';
            }
            foreach ($select_options as $option_value => $option_text) {
                $selected_attr = ($selected == $option_value) ? ' selected' : '';
                echo '<option value="' . htmlspecialchars($option_value) . '"' . $selected_attr . '>';
                echo htmlspecialchars($option_text);
                echo '</option>';
            }
            echo '</select>';
            break;
            
        case 'date':
        case 'time':
        case 'datetime-local':
            echo '<input type="' . $type . '" id="' . $name . '" name="' . $name . '"';
            if ($required) echo ' required';
            if ($value) echo ' value="' . htmlspecialchars($value) . '"';
            echo ' class="' . $class . '">';
            break;
            
        case 'checkbox':
            echo '<label class="flex items-center">';
            echo '<input type="checkbox" id="' . $name . '" name="' . $name . '" value="1"';
            if ($value) echo ' checked';
            echo ' class="rounded border-gray-300 text-primary focus:ring-primary">';
            echo '<span class="ml-2 text-sm text-gray-700">' . $placeholder . '</span>';
            echo '</label>';
            break;
    }
    
    if ($help_text) {
        echo '<p class="text-xs text-gray-500 mt-1">' . htmlspecialchars($help_text) . '</p>';
    }
    
    echo '</div>';
}

// Section header helper
function render_section_header($title, $subtitle = '', $icon = 'fas fa-info-circle') {
    echo '<div class="border-b border-gray-200 pb-4">';
    echo '<h3 class="text-lg font-semibold text-gray-800 flex items-center">';
    echo '<i class="' . $icon . ' mr-2 text-primary"></i>';
    echo $title;
    echo '</h3>';
    if ($subtitle) {
        echo '<p class="text-gray-600 text-sm mt-1">' . htmlspecialchars($subtitle) . '</p>';
    }
    echo '</div>';
}

// Status badge helper
function render_status_badge($status) {
    $status_styles = [
        'planned' => 'bg-blue-100 text-blue-800 border-blue-200',
        'in_progress' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
        'completed' => 'bg-green-100 text-green-800 border-green-200',
        'cancelled' => 'bg-red-100 text-red-800 border-red-200',
        'postponed' => 'bg-gray-100 text-gray-800 border-gray-200'
    ];
    $status_style = $status_styles[$status] ?? 'bg-gray-100 text-gray-800 border-gray-200';
    
    echo '<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ' . $status_style . '">';
    echo ucfirst(str_replace('_', ' ', $status));
    echo '</span>';
}

// Action buttons helper
function render_action_buttons($buttons) {
    echo '<div class="flex flex-wrap gap-3 justify-center md:justify-start">';
    foreach ($buttons as $button) {
        $style = $button['style'] ?? 'bg-gray-100 hover:bg-gray-200 text-gray-700';
        echo '<a href="' . $button['url'] . '" class="inline-flex items-center px-4 py-2 ' . $style . ' text-sm font-medium rounded-lg transition-colors">';
        if (isset($button['icon'])) {
            echo '<i class="' . $button['icon'] . ' mr-2"></i>';
        }
        echo $button['text'];
        echo '</a>';
    }
    echo '</div>';
}
?>
