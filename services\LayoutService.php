<?php
/**
 * Layout Service
 * 
 * Handles layout-related data preparation and business logic
 * that was previously embedded in the view layer.
 */

class LayoutService {
    private $setting;
    private $database;
    
    public function __construct() {
        try {
            // Check if required files exist before including
            if (file_exists('models/Setting.php')) {
                require_once 'models/Setting.php';
            }
            if (file_exists('config/database.php')) {
                require_once 'config/database.php';
            }

            // Only initialize if classes are available
            if (class_exists('Database') && class_exists('Setting')) {
                $this->database = new Database();
                $this->setting = new Setting($this->database->getConnection());
            }
        } catch (Exception $e) {
            // Silently fail - fallback will be used
            $this->database = null;
            $this->setting = null;
        }
    }
    
    /**
     * Get layout data for the main template
     * 
     * @return array Layout data including logo, church name, navigation, etc.
     */
    public function getLayoutData() {
        try {
            // Check if database connection is available
            if ($this->setting && $this->database) {
                $layout_settings = $this->setting->getAllAsArray();

                return [
                    'logo_src' => $this->getLogoSource($layout_settings),
                    'church_name' => $layout_settings['church_name'] ?? 'ICGC Emmanuel Temple',
                    'display_name' => $layout_settings['church_name'] ?? 'ICGC Emmanuel Temple',
                    'navigation_items' => $this->getNavigationItems(),
                    'has_custom_logo' => $this->hasCustomLogo($layout_settings)
                ];
            } else {
                throw new Exception('Database not available');
            }

        } catch (Exception $e) {
            // Fallback data in case of errors
            $logoUrl = function_exists('url') ? url('assets/images/icgc.png') : '/icgc/assets/images/icgc.png';

            return [
                'logo_src' => $logoUrl,
                'church_name' => 'ICGC Emmanuel Temple',
                'display_name' => 'ICGC Emmanuel Temple',
                'navigation_items' => $this->getNavigationItems(),
                'has_custom_logo' => false
            ];
        }
    }
    
    /**
     * Get the logo source URL
     * 
     * @param array $settings Layout settings
     * @return string Logo URL
     */
    private function getLogoSource($settings) {
        $logo_keys_to_try = ['church_logo'];
        $default_logo = function_exists('url') ? url('assets/images/icgc.png') : '/icgc/assets/images/icgc.png';

        foreach ($logo_keys_to_try as $logo_key) {
            $current_logo = $settings[$logo_key] ?? '';

            if (!empty($current_logo)) {
                $logo_file_path = $current_logo;
                if (!file_exists($logo_file_path) && !str_starts_with($current_logo, '/')) {
                    $logo_file_path = ltrim($current_logo, '/');
                }

                if (file_exists($logo_file_path)) {
                    $logoUrl = function_exists('url') ? url(ltrim($current_logo, '/')) : '/icgc/' . ltrim($current_logo, '/');
                    return $logoUrl . '?v=' . filemtime($logo_file_path);
                }
            }
        }

        return $default_logo;
    }
    
    /**
     * Check if a custom logo is configured
     * 
     * @param array $settings Layout settings
     * @return bool
     */
    private function hasCustomLogo($settings) {
        $logo_keys_to_try = ['church_logo'];
        
        foreach ($logo_keys_to_try as $logo_key) {
            $current_logo = $settings[$logo_key] ?? '';
            
            if (!empty($current_logo)) {
                $logo_file_path = $current_logo;
                if (!file_exists($logo_file_path) && !str_starts_with($current_logo, '/')) {
                    $logo_file_path = ltrim($current_logo, '/');
                }
                
                if (file_exists($logo_file_path)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Get navigation items configuration
     * 
     * @return array Navigation items
     */
    private function getNavigationItems() {
        return [
            ['id' => 'dashboard', 'icon' => 'fa-tachometer-alt', 'label' => 'Dashboard'],
            ['id' => 'members', 'icon' => 'fa-users', 'label' => 'Members'],
            ['id' => 'birthdays', 'icon' => 'fa-birthday-cake', 'label' => 'Birthdays'],
            ['id' => 'groups', 'icon' => 'fa-layer-group', 'label' => 'Groups'],
            ['separator' => true],
            ['id' => 'children_ministry', 'url_slug' => 'children-ministry', 'icon' => 'fa-child', 'label' => "Children's Ministry"],
            ['id' => 'visitors', 'icon' => 'fa-user-plus', 'label' => 'Visitors'],
            ['id' => 'programs', 'icon' => 'fa-calendar-alt', 'label' => 'Programs'],
            ['id' => 'attendance', 'icon' => 'fa-clipboard-check', 'label' => 'Attendance'],
            ['id' => 'equipment', 'icon' => 'fa-tools', 'label' => 'Equipment'],
            ['separator' => true],
            ['id' => 'finances', 'icon' => 'fa-money-bill-wave', 'label' => 'Finance'],
            ['id' => 'welfare', 'icon' => 'fa-hand-holding-heart', 'label' => 'Welfare'],
            ['id' => 'sms', 'icon' => 'fa-sms', 'label' => 'SMS Broadcast'],
            ['id' => 'settings', 'icon' => 'fa-cogs', 'label' => 'Settings'],
        ];
    }
    
    /**
     * Render a navigation link
     * 
     * @param array $item Navigation item
     * @param string $active_page Currently active page
     * @return string HTML for navigation link
     */
    public function renderNavLink($item, $active_page = '') {
        if (isset($item['separator'])) {
            return '<li class="px-4 py-1"><div class="border-t border-white border-opacity-20"></div></li>';
        }
        
        $url_slug = $item['url_slug'] ?? $item['id'];
        $url = function_exists('url') ? url($url_slug) : "/icgc/$url_slug";
        $is_active = ($active_page === $item['id']);
        $active_class = $is_active ? 'bg-black bg-opacity-20 text-secondary font-semibold' : '';
        
        return <<<HTML
        <li>
            <a href="{$url}" class="flex items-center px-4 py-2 text-white hover:bg-white hover:bg-opacity-10 transition-all duration-200 {$active_class}">
                <i class="fas {$item['icon']} w-4 mr-3 text-base"></i>
                <span class="text-base">{$item['label']}</span>
            </a>
        </li>
HTML;
    }
    
    /**
     * Get asset URL with proper versioning
     * 
     * @param string $asset_path Path to asset file
     * @return string Asset URL with version parameter
     */
    public function getAssetUrl($asset_path) {
        $full_path = $asset_path;

        if (file_exists($full_path)) {
            $baseUrl = function_exists('url') ? url($asset_path) : '/icgc/' . $asset_path;
            return $baseUrl . '?v=' . filemtime($full_path);
        }

        return function_exists('url') ? url($asset_path) : '/icgc/' . $asset_path;
    }
}
