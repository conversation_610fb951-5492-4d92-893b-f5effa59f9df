<?php
// Helper function for time ago
if (!function_exists('time_ago')) {
    function time_ago($datetime) {
        if (!$datetime) return 'Never';
        
        $time = strtotime($datetime);
        $now = time();
        $diff = $now - $time;

        if ($diff < 60) {
            return 'Just now';
        } elseif ($diff < 3600) {
            $mins = floor($diff / 60);
            return $mins . ' minute' . ($mins > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 2592000) {
            $weeks = floor($diff / 604800);
            return $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 31536000) {
            $months = floor($diff / 2592000);
            return $months . ' month' . ($months > 1 ? 's' : '') . ' ago';
        } else {
            $years = floor($diff / 31536000);
            return $years . ' year' . ($years > 1 ? 's' : '') . ' ago';
        }
    }
}
?>

<div class="container mx-auto">
    <!-- Enhanced Header with Gradient Background -->
    <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl shadow-xl overflow-hidden mb-8">
        <div class="relative p-8">
            <!-- Background Pattern -->
            <div class="absolute inset-0 bg-black bg-opacity-10"></div>
            <div class="absolute top-0 right-0 w-32 h-32 transform translate-x-16 -translate-y-16">
                <div class="w-full h-full bg-white bg-opacity-10 rounded-full"></div>
            </div>

            <div class="relative z-10 flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div class="flex items-center space-x-6 mb-6 lg:mb-0">
                    <!-- Back Button -->
                    <a href="<?php echo BASE_URL; ?>members/view/<?php echo $member['id']; ?>"
                       class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-xl flex items-center transition-all duration-200 backdrop-blur-sm border border-white border-opacity-20 hover:border-opacity-40">
                        <i class="fas fa-arrow-left"></i>
                    </a>

                    <!-- Member Info -->
                    <div class="text-white">
                        <h1 class="text-3xl font-bold mb-2 flex items-center">
                            <i class="fas fa-calendar-check mr-3"></i>
                            Attendance History
                        </h1>
                        <div class="text-white text-opacity-90 mb-2">
                            <div class="text-xl font-semibold">
                                <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                            </div>
                            <div class="flex items-center space-x-4 text-sm">
                                <span class="flex items-center">
                                    <i class="fas fa-building mr-2"></i>
                                    <?php echo ucfirst(str_replace('_', ' ', $member['department'])); ?>
                                </span>
                                <span class="flex items-center">
                                    <i class="fas fa-phone mr-2"></i>
                                    <?php echo htmlspecialchars($member['phone_number']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    <a href="<?php echo BASE_URL; ?>attendance/bulk?member_id=<?php echo $member['id']; ?>"
                       class="bg-white text-primary hover:bg-gray-50 py-3 px-6 rounded-xl flex items-center transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <i class="fas fa-plus mr-2"></i> Mark Attendance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Records Card -->
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 mb-1">Total Records</p>
                    <p class="text-3xl font-bold text-gray-900"><?php echo $attendance_stats['overall']['total']; ?></p>
                    <p class="text-xs text-gray-500 mt-1">All time</p>
                </div>
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-clipboard-list text-white text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Present Card -->
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 mb-1">Present</p>
                    <p class="text-3xl font-bold text-green-600"><?php echo $attendance_stats['overall']['present']; ?></p>
                    <p class="text-xs text-green-500 mt-1">Total present</p>
                </div>
                <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-check-circle text-white text-xl"></i>
                </div>
            </div>
        </div>

        <!-- This Month Card -->
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 mb-1">This Month</p>
                    <p class="text-3xl font-bold text-blue-600"><?php echo $attendance_stats['this_month']['total']; ?></p>
                    <p class="text-xs text-blue-500 mt-1"><?php echo date('F Y'); ?></p>
                </div>
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-calendar-alt text-white text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Attendance Rate Card -->
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 mb-1">Attendance Rate</p>
                    <p class="text-3xl font-bold text-primary"><?php echo $attendance_stats['overall']['rate']; ?>%</p>
                    <p class="text-xs text-primary mt-1">Overall rate</p>
                </div>
                <div class="w-14 h-14 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-chart-line text-white text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filters -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8 hover:shadow-xl transition-shadow duration-300">
        <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                <i class="fas fa-filter text-white text-sm"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800">Filter Records</h3>
        </div>
        <form method="GET" action="<?php echo BASE_URL; ?>attendance/member" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <input type="hidden" name="id" value="<?php echo $member['id']; ?>">
            
            <div>
                <label for="service" class="block text-sm font-medium text-gray-700 mb-1">Service</label>
                <select id="service" name="service" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="">All Services</option>
                    <?php foreach ($services as $service): ?>
                        <option value="<?php echo $service->id; ?>" 
                                <?php echo (isset($service_filter) && $service_filter == $service->id) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($service->name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="">All Statuses</option>
                    <option value="present" <?php echo (isset($status_filter) && $status_filter === 'present') ? 'selected' : ''; ?>>Present</option>
                    <option value="absent" <?php echo (isset($status_filter) && $status_filter === 'absent') ? 'selected' : ''; ?>>Absent</option>
                    <option value="late" <?php echo (isset($status_filter) && $status_filter === 'late') ? 'selected' : ''; ?>>Late</option>
                </select>
            </div>

            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input type="date" id="date_from" name="date_from" 
                       value="<?php echo htmlspecialchars($date_from ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>

            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input type="date" id="date_to" name="date_to" 
                       value="<?php echo htmlspecialchars($date_to ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>

            <div class="flex items-end">
                <button type="submit" 
                        class="w-full bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors flex items-center justify-center">
                    <i class="fas fa-search mr-2"></i>
                    Filter
                </button>
            </div>

            <div class="flex items-end">
                <a href="<?php echo BASE_URL; ?>attendance/member?id=<?php echo $member['id']; ?>" 
                   class="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors flex items-center justify-center">
                    <i class="fas fa-times mr-2"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Enhanced Attendance Records -->
    <?php if (!empty($attendance_records)): ?>
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div class="bg-gradient-to-r from-primary/10 to-secondary/10 p-6 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-800 flex items-center">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-table text-white text-sm"></i>
                    </div>
                    Attendance Records
                    <span class="ml-3 px-3 py-1 bg-primary text-white text-sm rounded-full">
                        <?php echo count($attendance_records); ?> records
                    </span>
                </h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">
                                <i class="fas fa-calendar mr-2"></i>Date
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">
                                <i class="fas fa-church mr-2"></i>Service
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">
                                <i class="fas fa-check-circle mr-2"></i>Status
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">
                                <i class="fas fa-clock mr-2"></i>Recorded
                            </th>
                            <th class="px-6 py-4 text-center text-xs font-bold text-gray-600 uppercase tracking-wider">
                                <i class="fas fa-cog mr-2"></i>Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($attendance_records as $record): ?>
                            <tr class="hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-200 group">
                                <td class="px-6 py-5 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors">
                                            <i class="fas fa-calendar-day text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-bold text-gray-900">
                                                <?php echo date('M j, Y', strtotime($record['attendance_date'])); ?>
                                            </div>
                                            <div class="text-xs text-gray-500 font-medium">
                                                <?php echo ucfirst($record['day_of_week']); ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-5 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200 transition-colors">
                                            <i class="fas fa-church text-purple-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($record['service_name']); ?>
                                            </div>
                                            <div class="text-xs text-gray-500 font-medium">
                                                <?php echo date('g:i A', strtotime($record['time'])); ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-5 whitespace-nowrap">
                                    <?php
                                    $status_config = [
                                        'present' => ['bg' => 'bg-green-100', 'text' => 'text-green-800', 'icon' => 'fa-check-circle', 'border' => 'border-green-200'],
                                        'absent' => ['bg' => 'bg-red-100', 'text' => 'text-red-800', 'icon' => 'fa-times-circle', 'border' => 'border-red-200'],
                                        'late' => ['bg' => 'bg-yellow-100', 'text' => 'text-yellow-800', 'icon' => 'fa-clock', 'border' => 'border-yellow-200']
                                    ];
                                    $config = $status_config[$record['status']] ?? ['bg' => 'bg-gray-100', 'text' => 'text-gray-800', 'icon' => 'fa-question', 'border' => 'border-gray-200'];
                                    ?>
                                    <span class="inline-flex items-center px-3 py-1.5 rounded-xl text-xs font-bold border <?php echo $config['bg'] . ' ' . $config['text'] . ' ' . $config['border']; ?>">
                                        <i class="fas <?php echo $config['icon']; ?> mr-1.5"></i>
                                        <?php echo ucfirst($record['status']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-5 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-gray-200 transition-colors">
                                            <i class="fas fa-history text-gray-600 text-sm"></i>
                                        </div>
                                        <div class="text-sm text-gray-600 font-medium">
                                            <?php echo time_ago($record['attendance_date']); ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-5 whitespace-nowrap text-center">
                                    <button onclick="confirmDelete(<?php echo $record['id']; ?>, '<?php echo htmlspecialchars($record['service_name']); ?>', '<?php echo date('M j, Y', strtotime($record['attendance_date'])); ?>')"
                                            class="inline-flex items-center px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 hover:text-red-800 rounded-lg transition-all duration-200 text-sm font-medium border border-red-200 hover:border-red-300 group/btn">
                                        <i class="fas fa-trash-alt mr-2 group-hover/btn:animate-pulse"></i>
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="flex items-center justify-between mt-6">
                <div class="text-sm text-gray-700">
                    Showing <?php echo (($page - 1) * $limit) + 1; ?> to <?php echo min($page * $limit, $total_records); ?> of <?php echo $total_records; ?> results
                </div>
                <div class="flex items-center space-x-2">
                    <?php if ($page > 1): ?>
                        <a href="?id=<?php echo $member['id']; ?>&page=<?php echo $page - 1; ?><?php echo $service_filter ? '&service=' . urlencode($service_filter) : ''; ?><?php echo $status_filter ? '&status=' . urlencode($status_filter) : ''; ?><?php echo $date_from ? '&date_from=' . urlencode($date_from) : ''; ?><?php echo $date_to ? '&date_to=' . urlencode($date_to) : ''; ?>" 
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            Previous
                        </a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?id=<?php echo $member['id']; ?>&page=<?php echo $i; ?><?php echo $service_filter ? '&service=' . urlencode($service_filter) : ''; ?><?php echo $status_filter ? '&status=' . urlencode($status_filter) : ''; ?><?php echo $date_from ? '&date_from=' . urlencode($date_from) : ''; ?><?php echo $date_to ? '&date_to=' . urlencode($date_to) : ''; ?>" 
                           class="px-3 py-2 text-sm <?php echo $i === $page ? 'bg-primary text-white' : 'bg-white border border-gray-300 hover:bg-gray-50'; ?> rounded-lg transition-colors">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?id=<?php echo $member['id']; ?>&page=<?php echo $page + 1; ?><?php echo $service_filter ? '&service=' . urlencode($service_filter) : ''; ?><?php echo $status_filter ? '&status=' . urlencode($status_filter) : ''; ?><?php echo $date_from ? '&date_from=' . urlencode($date_from) : ''; ?><?php echo $date_to ? '&date_to=' . urlencode($date_to) : ''; ?>" 
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            Next
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

    <?php else: ?>
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <div class="max-w-md mx-auto">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-calendar-times text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Attendance Records</h3>
                <p class="text-gray-600 mb-4">
                    <?php if (isset($service_filter) || isset($status_filter) || isset($date_from) || isset($date_to)): ?>
                        No attendance records found matching your filter criteria.
                    <?php else: ?>
                        This member has no attendance records yet.
                    <?php endif; ?>
                </p>
                <div class="flex justify-center space-x-3">
                    <?php if (isset($service_filter) || isset($status_filter) || isset($date_from) || isset($date_to)): ?>
                        <a href="<?php echo BASE_URL; ?>attendance/member?id=<?php echo $member['id']; ?>" 
                           class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                            <i class="fas fa-times mr-2"></i>
                            Clear Filters
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo BASE_URL; ?>attendance/bulk?member_id=<?php echo $member['id']; ?>" 
                       class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Add Attendance Record
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-32 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-2xl bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-4 border-b">
                <h3 class="text-lg font-bold text-gray-900 flex items-center">
                    <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                    </div>
                    Confirm Delete
                </h3>
                <button onclick="closeDeleteModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="mt-4">
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-trash-alt text-red-600"></i>
                        </div>
                        <div>
                            <h4 class="text-red-800 font-medium">Delete Attendance Record</h4>
                            <p class="text-red-600 text-sm">This action cannot be undone.</p>
                        </div>
                    </div>
                </div>

                <div class="space-y-2 text-sm text-gray-600">
                    <p><strong>Service:</strong> <span id="deleteService"></span></p>
                    <p><strong>Date:</strong> <span id="deleteDate"></span></p>
                    <p><strong>Member:</strong> <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?></p>
                </div>

                <p class="mt-4 text-sm text-gray-700">
                    Are you sure you want to delete this attendance record? This action cannot be undone.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t">
                <button onclick="closeDeleteModal()"
                        class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg transition-colors">
                    Cancel
                </button>
                <button onclick="deleteRecord()"
                        class="px-6 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors">
                    <i class="fas fa-trash-alt mr-2"></i>Delete Record
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let deleteRecordId = null;

function confirmDelete(recordId, serviceName, date) {
    deleteRecordId = recordId;
    document.getElementById('deleteService').textContent = serviceName;
    document.getElementById('deleteDate').textContent = date;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    deleteRecordId = null;
}

function deleteRecord() {
    if (!deleteRecordId) return;

    // Show loading state
    const deleteBtn = document.querySelector('#deleteModal button[onclick="deleteRecord()"]');
    const originalText = deleteBtn.innerHTML;
    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Deleting...';
    deleteBtn.disabled = true;

    // Send delete request
    fetch('<?php echo BASE_URL; ?>attendance/delete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${deleteRecordId}&member_id=<?php echo $member['id']; ?>`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            alert('Attendance record deleted successfully!');
            // Reload the page to refresh the data
            window.location.reload();
        } else {
            // Show error message
            alert('Error deleting record: ' + (data.message || 'Unknown error'));
            // Reset button
            deleteBtn.innerHTML = originalText;
            deleteBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error deleting record. Please try again.');
        // Reset button
        deleteBtn.innerHTML = originalText;
        deleteBtn.disabled = false;
    });
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

// Add loading states for pagination and filters
document.addEventListener('DOMContentLoaded', function() {
    // Add loading state to pagination links
    const paginationLinks = document.querySelectorAll('a[href*="page="]');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
        });
    });

    // Add loading state to filter form
    const filterForm = document.querySelector('form[action*="attendance/member"]');
    if (filterForm) {
        filterForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Filtering...';
            submitBtn.disabled = true;
        });
    }
});
</script>
