<?php
/**
 * Children's Ministry QR Analytics Dashboard
 */
?>

<div class="container mx-auto px-4 py-6 max-w-7xl">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Children's Ministry QR Analytics</h1>
                <p class="text-gray-600">Comprehensive QR attendance analytics for children's ministry</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
                <a href="<?php echo BASE_URL; ?>children-ministry" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <button onclick="exportAnalytics()" 
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center">
                    <i class="fas fa-download mr-2"></i>Export Report
                </button>
            </div>
        </div>
    </div>

    <!-- Period Selection -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 overflow-hidden">
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h3 class="text-lg font-semibold text-white flex items-center">
                <i class="fas fa-calendar-alt text-white mr-2"></i>
                Analytics Period: <?php echo htmlspecialchars($analytics_data['period_info']['display_name']); ?>
            </h3>
        </div>
        <div class="p-6">
            <form method="GET" action="<?php echo BASE_URL; ?>children-ministry/qr-analytics" class="flex flex-wrap gap-4 items-end">
                <div>
                    <label for="period" class="block text-sm font-medium text-gray-700 mb-1">Period Type</label>
                    <select id="period" name="period" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="daily" <?php echo ($_GET['period'] ?? 'monthly') === 'daily' ? 'selected' : ''; ?>>Daily</option>
                        <option value="weekly" <?php echo ($_GET['period'] ?? 'monthly') === 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                        <option value="monthly" <?php echo ($_GET['period'] ?? 'monthly') === 'monthly' ? 'selected' : ''; ?>>Monthly</option>
                        <option value="yearly" <?php echo ($_GET['period'] ?? 'monthly') === 'yearly' ? 'selected' : ''; ?>>Yearly</option>
                    </select>
                </div>
                <div>
                    <label for="value" class="block text-sm font-medium text-gray-700 mb-1">Period Value</label>
                    <input type="text" id="value" name="value" 
                           value="<?php echo htmlspecialchars($_GET['value'] ?? date('Y-m')); ?>"
                           placeholder="e.g., 2025-07 for monthly"
                           class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition-colors">
                    <i class="fas fa-search mr-2"></i>Update Analytics
                </button>
            </form>
        </div>
    </div>

    <!-- Overview Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Check-ins -->
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg p-6 border border-blue-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-blue-700">Total Check-ins</p>
                    <p class="text-3xl font-bold text-blue-900"><?php echo number_format($analytics_data['overview']['total_checkins'] ?? 0); ?></p>
                </div>
            </div>
        </div>

        <!-- QR Check-ins -->
        <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl shadow-lg p-6 border border-purple-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-qrcode text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-purple-700">QR Check-ins</p>
                    <p class="text-3xl font-bold text-purple-900"><?php echo number_format($analytics_data['overview']['qr_checkins'] ?? 0); ?></p>
                </div>
            </div>
        </div>

        <!-- Unique Children -->
        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl shadow-lg p-6 border border-green-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-child text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-green-700">Unique Children</p>
                    <p class="text-3xl font-bold text-green-900"><?php echo number_format($analytics_data['overview']['unique_children'] ?? 0); ?></p>
                </div>
            </div>
        </div>

        <!-- QR Adoption Rate -->
        <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl shadow-lg p-6 border border-orange-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-percentage text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-orange-700">QR Adoption</p>
                    <p class="text-3xl font-bold text-orange-900"><?php echo $analytics_data['overview']['qr_percentage'] ?? 0; ?>%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 mb-8 overflow-hidden">
        <div class="bg-gradient-to-r from-green-600 to-green-700 px-6 py-4">
            <h3 class="text-lg font-semibold text-white flex items-center">
                <i class="fas fa-bolt text-white mr-2"></i>
                Quick Actions
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="<?php echo BASE_URL; ?>children-ministry/attendance-list"
                   class="bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-4 text-center transition-colors group">
                    <i class="fas fa-list text-blue-600 text-2xl mb-2 group-hover:scale-110 transition-transform"></i>
                    <div class="text-blue-800 font-medium">View Attendance List</div>
                    <div class="text-blue-600 text-sm">See detailed attendance by name</div>
                </a>
                <a href="<?php echo BASE_URL; ?>children-ministry/attendance-list?status=absent"
                   class="bg-red-50 hover:bg-red-100 border border-red-200 rounded-lg p-4 text-center transition-colors group">
                    <i class="fas fa-user-times text-red-600 text-2xl mb-2 group-hover:scale-110 transition-transform"></i>
                    <div class="text-red-800 font-medium">View Absent Children</div>
                    <div class="text-red-600 text-sm">Track who didn't attend</div>
                </a>
                <a href="<?php echo BASE_URL; ?>children-ministry/attendance-list?date=<?php echo date('Y-m-d'); ?>"
                   class="bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg p-4 text-center transition-colors group">
                    <i class="fas fa-calendar-day text-green-600 text-2xl mb-2 group-hover:scale-110 transition-transform"></i>
                    <div class="text-green-800 font-medium">Today's Attendance</div>
                    <div class="text-green-600 text-sm">Current day attendance</div>
                </a>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Daily Trends Chart -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-chart-line text-white mr-2"></i>
                    Daily Check-in Trends
                </h3>
            </div>
            <div class="p-6">
                <div class="h-80">
                    <canvas id="trendsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Hourly Distribution Chart -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-clock text-white mr-2"></i>
                    Hourly Distribution
                </h3>
            </div>
            <div class="p-6">
                <div class="h-80">
                    <canvas id="hourlyChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Age Group Analysis -->
    <?php if (!empty($analytics_data['age_group_analysis'])): ?>
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden mb-8">
        <div class="bg-gradient-to-r from-pink-600 to-rose-600 px-6 py-4">
            <h3 class="text-lg font-semibold text-white flex items-center">
                <i class="fas fa-users-cog text-white mr-2"></i>
                Age Group Analysis
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Age Group Chart -->
                <div class="h-80">
                    <canvas id="ageGroupChart"></canvas>
                </div>

                <!-- Age Group Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Age Group</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">QR</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Rate</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php foreach ($analytics_data['age_group_analysis'] as $group): ?>
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">
                                        <?php echo htmlspecialchars($group['age_group'] ?: 'No Group'); ?>
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($group['checkins']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($group['qr_checkins']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-900">
                                        <?php
                                        $rate = $group['checkins'] > 0 ? round(($group['qr_checkins'] / $group['checkins']) * 100, 1) : 0;
                                        echo $rate . '%';
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Service Performance -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div class="bg-gradient-to-r from-teal-600 to-cyan-600 px-6 py-4">
            <h3 class="text-lg font-semibold text-white flex items-center">
                <i class="fas fa-church text-white mr-2"></i>
                Service Performance
            </h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Service</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total Check-ins</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">QR Check-ins</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Unique Children</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">QR Rate</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <?php foreach ($analytics_data['service_performance'] as $service): ?>
                            <tr>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($service['service_name']); ?>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900"><?php echo number_format($service['checkins']); ?></td>
                                <td class="px-6 py-4 text-sm text-gray-900"><?php echo number_format($service['qr_checkins']); ?></td>
                                <td class="px-6 py-4 text-sm text-gray-900"><?php echo number_format($service['unique_children']); ?></td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                        <?php echo $service['qr_percentage'] >= 50 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                        <?php echo $service['qr_percentage']; ?>%
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Analytics data from PHP
const analyticsData = {
    trends: <?php echo json_encode($analytics_data['trends']); ?>,
    hourlyDistribution: <?php echo json_encode($analytics_data['hourly_distribution']); ?>,
    ageGroupAnalysis: <?php echo json_encode($analytics_data['age_group_analysis']); ?>
};

document.addEventListener('DOMContentLoaded', function() {
    initializeTrendsChart();
    initializeHourlyChart();

    // Only initialize age group chart if data exists
    if (analyticsData.ageGroupAnalysis && analyticsData.ageGroupAnalysis.length > 0) {
        initializeAgeGroupChart();
    }
});

// Initialize daily trends chart
function initializeTrendsChart() {
    const ctx = document.getElementById('trendsChart');
    if (!ctx) return;

    const dates = analyticsData.trends.map(item => item.date);
    const totalCheckins = analyticsData.trends.map(item => parseInt(item.total_checkins));
    const qrCheckins = analyticsData.trends.map(item => parseInt(item.qr_checkins));

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates.map(date => new Date(date).toLocaleDateString()),
            datasets: [
                {
                    label: 'Total Check-ins',
                    data: totalCheckins,
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'QR Check-ins',
                    data: qrCheckins,
                    borderColor: 'rgb(147, 51, 234)',
                    backgroundColor: 'rgba(147, 51, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white'
                }
            }
        }
    });
}

// Initialize hourly distribution chart
function initializeHourlyChart() {
    const ctx = document.getElementById('hourlyChart');
    if (!ctx) return;

    // Prepare data for 24-hour format
    const hours = Array.from({length: 24}, (_, i) => i);
    const checkinCounts = hours.map(hour => {
        const data = analyticsData.hourlyDistribution.find(d => parseInt(d.hour) === hour);
        return data ? parseInt(data.checkins) : 0;
    });

    const qrCounts = hours.map(hour => {
        const data = analyticsData.hourlyDistribution.find(d => parseInt(d.hour) === hour);
        return data ? parseInt(data.qr_checkins) : 0;
    });

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: hours.map(h => h + ':00'),
            datasets: [
                {
                    label: 'Total Check-ins',
                    data: checkinCounts,
                    backgroundColor: 'rgba(34, 197, 94, 0.6)',
                    borderColor: 'rgba(34, 197, 94, 1)',
                    borderWidth: 1
                },
                {
                    label: 'QR Check-ins',
                    data: qrCounts,
                    backgroundColor: 'rgba(147, 51, 234, 0.6)',
                    borderColor: 'rgba(147, 51, 234, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// Initialize age group chart
function initializeAgeGroupChart() {
    const ctx = document.getElementById('ageGroupChart');
    if (!ctx) return;

    const ageGroups = analyticsData.ageGroupAnalysis.map(item => item.age_group || 'No Group');
    const checkins = analyticsData.ageGroupAnalysis.map(item => parseInt(item.checkins));
    const qrCheckins = analyticsData.ageGroupAnalysis.map(item => parseInt(item.qr_checkins));

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ageGroups,
            datasets: [{
                data: checkins,
                backgroundColor: [
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(147, 51, 234, 0.8)',
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(249, 115, 22, 0.8)',
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(168, 85, 247, 0.8)'
                ],
                borderColor: [
                    'rgba(59, 130, 246, 1)',
                    'rgba(147, 51, 234, 1)',
                    'rgba(34, 197, 94, 1)',
                    'rgba(249, 115, 22, 1)',
                    'rgba(239, 68, 68, 1)',
                    'rgba(168, 85, 247, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
}

// Export analytics function
function exportAnalytics() {
    const period = new URLSearchParams(window.location.search).get('period') || 'monthly';
    const value = new URLSearchParams(window.location.search).get('value') || '<?php echo date('Y-m'); ?>';

    // Create export URL
    const exportUrl = `<?php echo BASE_URL; ?>children-ministry/export-qr-analytics?period=${period}&value=${value}&format=pdf`;

    // Open in new window
    window.open(exportUrl, '_blank');
}

// Period type change handler
document.getElementById('period').addEventListener('change', function() {
    const periodType = this.value;
    const valueInput = document.getElementById('value');

    // Update placeholder based on period type
    switch(periodType) {
        case 'daily':
            valueInput.placeholder = 'e.g., 2025-07-15';
            valueInput.value = '<?php echo date('Y-m-d'); ?>';
            break;
        case 'weekly':
            valueInput.placeholder = 'e.g., 2025-07-15 (any date in week)';
            valueInput.value = '<?php echo date('Y-m-d'); ?>';
            break;
        case 'monthly':
            valueInput.placeholder = 'e.g., 2025-07';
            valueInput.value = '<?php echo date('Y-m'); ?>';
            break;
        case 'yearly':
            valueInput.placeholder = 'e.g., 2025';
            valueInput.value = '<?php echo date('Y'); ?>';
            break;
    }
});
</script>
