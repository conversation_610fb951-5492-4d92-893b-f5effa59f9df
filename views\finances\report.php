<div class="container mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Finance Report</h1>
        <a href="<?php echo BASE_URL; ?>finance" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-arrow-left mr-2"></i> Back to Finances
        </a>
    </div>

    <!-- Report Form -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Generate Report</h2>
        <form action="<?php echo BASE_URL; ?>finance/filter" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Category -->
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select id="category" name="category" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="">All Categories</option>
                    <option value="tithe">Tithe</option>
                    <option value="offering">Offering</option>
                    <option value="project_offering">Project Offering</option>
                    <option value="pladge">Pledge</option>
                    <option value="seed">Seed</option>
                    <option value="Pastors_appreciation">Pastor's Appreciation</option>
                    <option value="welfare">Welfare</option>
                    <option value="children_service_offering">Children Service Offering</option>
                    <option value="donation">Donation</option>
                    <option value="others">Others</option>
                    <option value="expense">Expense</option>
                </select>
            </div>

            <!-- Start Date -->
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input type="date" id="start_date" name="start_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
            </div>

            <!-- End Date -->
            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input type="date" id="end_date" name="end_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
            </div>

            <div class="md:col-span-3 flex justify-end space-x-3">
                <button type="reset" class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md">
                    <i class="fas fa-redo mr-2"></i> Reset
                </button>
                <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md">
                    <i class="fas fa-search mr-2"></i> Generate Report
                </button>
            </div>
        </form>
    </div>

    <!-- Report Options -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Income Report -->
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-green-500 hover:shadow-lg transition-shadow duration-300">
            <div class="flex items-center mb-4">
                <div class="p-3 rounded-full bg-green-100 text-green-500 mr-4">
                    <i class="fas fa-chart-line text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800">Income Report</h3>
            </div>
            <p class="text-gray-600 mb-4">View detailed report of all income transactions.</p>
            <a href="<?php echo BASE_URL; ?>finance/filter?category=tithe&category=offering&category=project_offering&category=pladge&category=seed&category=Pastors_appreciation&category=welfare&category=children_service_offering&category=donation&category=others" class="text-primary hover:text-primary-dark font-medium flex items-center">
                <span>Generate Report</span>
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>

        <!-- Expense Report -->
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-red-500 hover:shadow-lg transition-shadow duration-300">
            <div class="flex items-center mb-4">
                <div class="p-3 rounded-full bg-red-100 text-red-500 mr-4">
                    <i class="fas fa-file-invoice-dollar text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800">Expense Report</h3>
            </div>
            <p class="text-gray-600 mb-4">View detailed report of all expense transactions.</p>
            <a href="<?php echo BASE_URL; ?>finance/filter?category=expense" class="text-primary hover:text-primary-dark font-medium flex items-center">
                <span>Generate Report</span>
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>

        <!-- Monthly Summary -->
        <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-blue-500 hover:shadow-lg transition-shadow duration-300">
            <div class="flex items-center mb-4">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500 mr-4">
                    <i class="fas fa-calendar-alt text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800">Monthly Summary</h3>
            </div>
            <p class="text-gray-600 mb-4">View monthly summary of income and expenses.</p>
            <a href="<?php echo BASE_URL; ?>finance/filter?start_date=<?php echo date('Y-m-01'); ?>&end_date=<?php echo date('Y-m-t'); ?>" class="text-primary hover:text-primary-dark font-medium flex items-center">
                <span>Generate Report</span>
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
    </div>

    <!-- Export Options -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Export Options</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- PDF Export -->
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-primary transition-colors duration-300">
                <div class="flex items-center mb-2">
                    <div class="p-2 rounded-full bg-red-100 text-red-500 mr-3">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <h4 class="font-medium text-gray-800">Export as PDF</h4>
                </div>
                <p class="text-sm text-gray-600 mb-3">Download a PDF version of the financial report.</p>
                <button class="text-primary hover:text-primary-dark text-sm font-medium">Generate PDF</button>
            </div>

            <!-- Excel Export -->
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-primary transition-colors duration-300">
                <div class="flex items-center mb-2">
                    <div class="p-2 rounded-full bg-green-100 text-green-500 mr-3">
                        <i class="fas fa-file-excel"></i>
                    </div>
                    <h4 class="font-medium text-gray-800">Export as Excel</h4>
                </div>
                <p class="text-sm text-gray-600 mb-3">Download an Excel spreadsheet of the financial data.</p>
                <button class="text-primary hover:text-primary-dark text-sm font-medium">Generate Excel</button>
            </div>

            <!-- Print Report -->
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-primary transition-colors duration-300">
                <div class="flex items-center mb-2">
                    <div class="p-2 rounded-full bg-blue-100 text-blue-500 mr-3">
                        <i class="fas fa-print"></i>
                    </div>
                    <h4 class="font-medium text-gray-800">Print Report</h4>
                </div>
                <p class="text-sm text-gray-600 mb-3">Print a physical copy of the financial report.</p>
                <button class="text-primary hover:text-primary-dark text-sm font-medium">Print Report</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set default dates if not already set
        if (!document.getElementById('start_date').value) {
            // Set to first day of current month
            const firstDay = new Date();
            firstDay.setDate(1);
            document.getElementById('start_date').value = firstDay.toISOString().split('T')[0];
        }

        if (!document.getElementById('end_date').value) {
            // Set to current date
            const today = new Date();
            document.getElementById('end_date').value = today.toISOString().split('T')[0];
        }
    });
</script>
