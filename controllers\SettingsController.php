<?php
/**
 * Settings Controller
 * 
 * Handles settings and system configuration
 */
class SettingsController {
    private $dbStats;
    private $user;

    /**
     * Constructor
     */
    public function __construct() {
        // Create DatabaseStats model
        require_once 'models/DatabaseStats.php';
        $this->dbStats = new DatabaseStats();

        // Create User model for users tab
        require_once 'models/User.php';
        require_once 'config/database.php';
        $database = new Database();
        $this->user = new User($database->getConnection());
    }
    
    /**
     * Display settings page
     * 
     * @return void
     */
    public function index() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to access settings.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'dashboard');
            exit;
        }
        
        // Get database statistics if not already in session
        if (!isset($_SESSION['db_stats'])) {
            $_SESSION['db_stats'] = $this->dbStats->getDatabaseStats();
        }

        // Get users data for the users tab
        $usersStmt = $this->user->getAll();
        $users = $usersStmt->fetchAll(PDO::FETCH_OBJ);

        // Calculate user statistics
        $totalUsers = count($users);
        $adminCount = 0;
        $staffCount = 0;
        $activeCount = 0;

        foreach ($users as $user) {
            if ($user->role === 'admin' || $user->role === 'super_admin') {
                $adminCount++;
            } elseif ($user->role === 'staff') {
                $staffCount++;
            }
            if ($user->status === 'active') {
                $activeCount++;
            }
        }

        // Set variables for the view
        $db_stats = $_SESSION['db_stats'];

        // Set page title and active page
        $page_title = 'Settings - ICGC Emmanuel Temple';
        $active_page = 'settings';

        // Include the view
        require_once 'views/settings/index.php';
    }
    
    /**
     * Backup database
     * 
     * @return void
     */
    public function backup() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to backup the database.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        try {
            // Get database credentials
            require_once 'config/config.php';
            
            // Set backup filename
            $backupFile = 'icgc_backup_' . date('Y-m-d_H-i-s') . '.sql';
            
            // Set headers for download
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename=' . $backupFile);
            
            // Use mysqldump to create backup
            $command = sprintf(
                'mysqldump --host=%s --user=%s --password=%s %s',
                escapeshellarg(DB_HOST),
                escapeshellarg(DB_USER),
                escapeshellarg(DB_PASS),
                escapeshellarg(DB_NAME)
            );
            
            // Execute command and output directly to browser
            passthru($command);
            
            // Log the backup operation
            $this->dbStats->logMaintenanceOperation(
                'backup',
                'Database backup created',
                'success',
                'all',
                0,
                0,
                0
            );
            
            exit;
        } catch (Exception $e) {
            // Set error message
            $_SESSION['flash_message'] = 'Error backing up database: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            
            // Log the failed operation
            $this->dbStats->logMaintenanceOperation(
                'backup',
                'Error: ' . $e->getMessage(),
                'error',
                '',
                0,
                0,
                0
            );
            
            // Redirect back to settings page
            header('Location: ' . BASE_URL . 'settings#database');
            exit;
        }
    }
    
    /**
     * Restore database
     * 
     * @return void
     */
    public function restore() {
        // Check if user has admin privileges
        if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'super_admin') {
            $_SESSION['flash_message'] = 'You do not have permission to restore the database.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings');
            exit;
        }
        
        // Check if file was uploaded
        if (!isset($_FILES['backup_file']) || $_FILES['backup_file']['error'] !== UPLOAD_ERR_OK) {
            $_SESSION['flash_message'] = 'Please select a valid backup file.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings#database');
            exit;
        }
        
        // Check file type
        $fileType = pathinfo($_FILES['backup_file']['name'], PATHINFO_EXTENSION);
        if ($fileType !== 'sql') {
            $_SESSION['flash_message'] = 'Only SQL files are allowed.';
            $_SESSION['flash_type'] = 'danger';
            header('Location: ' . BASE_URL . 'settings#database');
            exit;
        }
        
        try {
            // Get database credentials
            require_once 'config/config.php';
            
            // Move uploaded file to temporary location
            $tempFile = tempnam(sys_get_temp_dir(), 'icgc_restore_');
            move_uploaded_file($_FILES['backup_file']['tmp_name'], $tempFile);
            
            // Use mysql to restore backup
            $command = sprintf(
                'mysql --host=%s --user=%s --password=%s %s < %s',
                escapeshellarg(DB_HOST),
                escapeshellarg(DB_USER),
                escapeshellarg(DB_PASS),
                escapeshellarg(DB_NAME),
                escapeshellarg($tempFile)
            );
            
            // Execute command
            exec($command, $output, $returnVar);
            
            // Delete temporary file
            unlink($tempFile);
            
            // Check if command was successful
            if ($returnVar !== 0) {
                throw new Exception('Error executing restore command: ' . implode("\n", $output));
            }
            
            // Log the restore operation
            $this->dbStats->logMaintenanceOperation(
                'restore',
                'Database restored from backup',
                'success',
                'all',
                0,
                0,
                0
            );
            
            // Set success message
            $_SESSION['flash_message'] = 'Database restored successfully.';
            $_SESSION['flash_type'] = 'success';
            
        } catch (Exception $e) {
            // Set error message
            $_SESSION['flash_message'] = 'Error restoring database: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
            
            // Log the failed operation
            $this->dbStats->logMaintenanceOperation(
                'restore',
                'Error: ' . $e->getMessage(),
                'error',
                '',
                0,
                0,
                0
            );
        }
        
        // Redirect back to settings page
        header('Location: ' . BASE_URL . 'settings#database');
        exit;
    }
    
    /**
     * Refresh database statistics
     * 
     * @return void
     */
    public function refreshStats() {
        // Create MaintenanceController and call refreshStats method
        require_once 'controllers/MaintenanceController.php';
        $maintenanceController = new MaintenanceController();
        $maintenanceController->refreshStats();
    }
    
    /**
     * Optimize database
     * 
     * @return void
     */
    public function optimizeDatabase() {
        // Create MaintenanceController and call optimizeDatabase method
        require_once 'controllers/MaintenanceController.php';
        $maintenanceController = new MaintenanceController();
        $maintenanceController->optimizeDatabase();
    }
    
    /**
     * Display archiving options
     * 
     * @return void
     */
    public function archiving() {
        // Create MaintenanceController and call archiving method
        require_once 'controllers/MaintenanceController.php';
        $maintenanceController = new MaintenanceController();
        $maintenanceController->archiving();
    }
    
    /**
     * Process archiving request
     * 
     * @return void
     */
    public function processArchiving() {
        // Create MaintenanceController and call processArchiving method
        require_once 'controllers/MaintenanceController.php';
        $maintenanceController = new MaintenanceController();
        $maintenanceController->processArchiving();
    }
    
    /**
     * Display maintenance log
     * 
     * @return void
     */
    public function maintenanceLog() {
        // Create MaintenanceController and call maintenanceLog method
        require_once 'controllers/MaintenanceController.php';
        $maintenanceController = new MaintenanceController();
        $maintenanceController->maintenanceLog();
    }
    
    /**
     * Display maintenance scheduling page
     * 
     * @return void
     */
    public function maintenanceSchedule() {
        // Create MaintenanceController and call maintenanceSchedule method
        require_once 'controllers/MaintenanceController.php';
        $maintenanceController = new MaintenanceController();
        $maintenanceController->maintenanceSchedule();
    }
    
    /**
     * Process maintenance schedule
     * 
     * @return void
     */
    public function processSchedule() {
        // Create MaintenanceController and call processSchedule method
        require_once 'controllers/MaintenanceController.php';
        $maintenanceController = new MaintenanceController();
        $maintenanceController->processSchedule();
    }
}
?>
