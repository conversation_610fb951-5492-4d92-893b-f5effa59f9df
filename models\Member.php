<?php
/**
 * Member Model
 */

class Member {
    // Database connection and table name
    private $conn;
    private $table_name = "members";

    // Object properties
    public $id;
    public $first_name;
    public $last_name;
    public $email;
    public $phone_number;
    public $date_of_birth;
    public $gender;
    public $marital_status;
    public $location;
    public $emergency_contact_name;
    public $emergency_contact_phone;
    public $baptism_status;
    public $department;
    public $role;
    public $membership_date;
    public $occupation;
    public $school;
    public $member_status;
    public $profile_picture;

    // Error handling
    public $error;
    public $registration_type;
    public $parent_consent_required;
    public $consent_on_file;
    public $registration_source;
    public $address;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db = null) {
        $this->conn = $db;

        // If no database connection is provided, get one from the Database class
        if ($this->conn === null) {
            require_once 'config/database.php';
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }

    /**
     * Populate object properties from array
     *
     * @param array $row
     */
    protected function populateFromArray($row) {
        $this->id = $row['id'];
        $this->first_name = $row['first_name'];
        $this->last_name = $row['last_name'];
        $this->email = $row['email'];
        $this->phone_number = $row['phone_number'];
        $this->date_of_birth = $row['date_of_birth'];
        $this->gender = $row['gender'];
        $this->marital_status = $row['marital_status'];
        $this->location = $row['location'];
        $this->emergency_contact_name = $row['emergency_contact_name'];
        $this->emergency_contact_phone = $row['emergency_contact_phone'];
        $this->baptism_status = $row['baptism_status'];
        $this->department = $row['department'];
        $this->role = $row['role'];
        $this->membership_date = $row['membership_date'];
        $this->occupation = $row['occupation'];
        $this->school = $row['school'] ?? '';
        $this->member_status = $row['member_status'];
        $this->profile_picture = $row['profile_picture'];
        $this->created_at = $row['created_at'];
        $this->updated_at = $row['updated_at'];
    }

    /**
     * Get all members with optimized query (deprecated - use pagination instead)
     *
     * @return PDOStatement
     */
    public function getAll() {
        $query = "SELECT id, first_name, last_name, email, phone_number, date_of_birth,
                         gender, marital_status, location, department, role, member_status,
                         TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) as age,
                         created_at, updated_at
                  FROM " . $this->table_name . "
                  WHERE member_status = 'active'
                  ORDER BY created_at DESC, last_name, first_name
                  LIMIT 100"; // Limit for safety
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get only adult members (18+ years) for group membership
     *
     * @param int $limit Number of records to return (default: 20)
     * @param int $offset Number of records to skip (default: 0)
     * @param string $search Search term for name, email, or phone
     * @param string $gender Filter by gender
     * @param array $excludeIds Array of member IDs to exclude (e.g., current group members)
     * @return PDOStatement
     */
    public function getAdultsOnly($limit = 20, $offset = 0, $search = '', $gender = '', $excludeIds = []) {
        $whereConditions = [
            "member_status = 'active'",
            "(date_of_birth IS NULL OR TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) >= 18)"
        ];

        $params = [];

        // Add search condition
        if (!empty($search)) {
            $whereConditions[] = "(CONCAT(first_name, ' ', last_name) LIKE :search
                                 OR email LIKE :search
                                 OR phone_number LIKE :search)";
            $params['search'] = '%' . $search . '%';
        }

        // Add gender filter
        if (!empty($gender)) {
            $whereConditions[] = "gender = :gender";
            $params['gender'] = $gender;
        }

        // Exclude specific member IDs using named parameters
        if (!empty($excludeIds)) {
            $excludePlaceholders = [];
            foreach ($excludeIds as $index => $id) {
                $paramName = 'exclude_' . $index;
                $excludePlaceholders[] = ':' . $paramName;
                $params[$paramName] = (int)$id;
            }
            $whereConditions[] = "id NOT IN (" . implode(',', $excludePlaceholders) . ")";
        }

        $query = "SELECT id, first_name, last_name, email, phone_number, date_of_birth,
                         gender, marital_status, location, department, role, member_status,
                         TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) as age,
                         created_at, updated_at
                  FROM " . $this->table_name . "
                  WHERE " . implode(' AND ', $whereConditions) . "
                  ORDER BY last_name, first_name
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);

        // Bind all parameters (search, filter, and exclude IDs)
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }

        // Bind pagination parameters
        $stmt->bindValue(':limit', (int)$limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', (int)$offset, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt;
    }

    /**
     * Count adult members for pagination
     *
     * @param string $search Search term for name, email, or phone
     * @param string $gender Filter by gender
     * @param array $excludeIds Array of member IDs to exclude (e.g., current group members)
     * @return int
     */
    public function countAdultsOnly($search = '', $gender = '', $excludeIds = []) {
        $whereConditions = [
            "member_status = 'active'",
            "(date_of_birth IS NULL OR TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) >= 18)"
        ];

        $params = [];

        // Add search condition
        if (!empty($search)) {
            $whereConditions[] = "(CONCAT(first_name, ' ', last_name) LIKE :search
                                 OR email LIKE :search
                                 OR phone_number LIKE :search)";
            $params['search'] = '%' . $search . '%';
        }

        // Add gender filter
        if (!empty($gender)) {
            $whereConditions[] = "gender = :gender";
            $params['gender'] = $gender;
        }

        // Exclude specific member IDs using named parameters
        if (!empty($excludeIds)) {
            $excludePlaceholders = [];
            foreach ($excludeIds as $index => $id) {
                $paramName = 'exclude_' . $index;
                $excludePlaceholders[] = ':' . $paramName;
                $params[$paramName] = (int)$id;
            }
            $whereConditions[] = "id NOT IN (" . implode(',', $excludePlaceholders) . ")";
        }

        $query = "SELECT COUNT(*) as total
                  FROM " . $this->table_name . "
                  WHERE " . implode(' AND ', $whereConditions);

        $stmt = $this->conn->prepare($query);

        // Bind all parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['total'];
    }

    /**
     * Create database indexes for better search performance
     *
     * @return bool
     */
    public function createSearchIndexes() {
        try {
            $indexes = [
                "CREATE INDEX IF NOT EXISTS idx_members_name ON members (last_name, first_name)",
                "CREATE INDEX IF NOT EXISTS idx_members_email ON members (email)",
                "CREATE INDEX IF NOT EXISTS idx_members_phone ON members (phone_number)",
                "CREATE INDEX IF NOT EXISTS idx_members_status ON members (member_status)",
                "CREATE INDEX IF NOT EXISTS idx_members_department ON members (department)",
                "CREATE INDEX IF NOT EXISTS idx_members_search ON members (first_name, last_name, email, phone_number)",
                "CREATE INDEX IF NOT EXISTS idx_members_created ON members (created_at)"
            ];

            foreach ($indexes as $index) {
                $this->conn->exec($index);
            }

            return true;
        } catch (Exception $e) {
            error_log("Index creation error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get member by ID
     *
     * @param int $id
     * @return bool
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 0,1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row) {
            $this->populateFromArray($row);
            return true;
        }
        return false;
    }

    /**
     * Get filtered members with pagination
     *
     * @param array $filters
     * @param int $limit
     * @param int $offset
     * @param string $orderBy
     * @return PDOStatement
     */
    public function getFiltered($filters = [], $limit = 100, $offset = 0, $orderBy = 'created_at DESC') {
        $query = "SELECT * FROM " . $this->table_name;

        $whereClause = [];
        $params = [];

        // Add custom filters
        $this->addCustomFilters($filters, $whereClause, $params);

        // Add WHERE clause if there are conditions
        if (!empty($whereClause)) {
            $query .= " WHERE " . implode(" AND ", $whereClause);
        }

        $query .= " ORDER BY " . $orderBy . " LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        // Bind pagination parameters
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt;
    }

    /**
     * Get filtered count
     *
     * @param array $filters
     * @return int
     */
    public function getFilteredCount($filters = []) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name;

        $whereClause = [];
        $params = [];

        // Add custom filters
        $this->addCustomFilters($filters, $whereClause, $params);

        // Add WHERE clause if there are conditions
        if (!empty($whereClause)) {
            $query .= " WHERE " . implode(" AND ", $whereClause);
        }

        $stmt = $this->conn->prepare($query);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }

    /**
     * Get total count of members
     *
     * @return int
     */
    public function getCount() {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }

    // Removed duplicate getCountByGender method - already exists later in file

    // Removed duplicate methods - they already exist later in the file

    /**
     * Add custom filters for Member-specific filtering
     *
     * @param array $filters
     * @param array $whereClause
     * @param array $params
     */
    protected function addCustomFilters($filters, &$whereClause, &$params) {
        // Add search filter
        if (isset($filters['search']) && !empty($filters['search'])) {
            $whereClause[] = "(first_name LIKE :search OR last_name LIKE :search OR email LIKE :search OR phone_number LIKE :search)";
            $params[':search'] = "%{$filters['search']}%";
        }

        // Add department filter
        if (isset($filters['department']) && !empty($filters['department'])) {
            $whereClause[] = "department = :department";
            $params[':department'] = $filters['department'];
        }

        // Add role filter
        if (isset($filters['role']) && !empty($filters['role'])) {
            $whereClause[] = "role = :role";
            $params[':role'] = $filters['role'];
        }

        // Add status filter
        if (isset($filters['status']) && !empty($filters['status'])) {
            $whereClause[] = "member_status = :status";
            $params[':status'] = $filters['status'];
        }
    }

    /**
     * Validate member data before create/update operations
     *
     * @return bool True if validation passes, false otherwise
     */
    public function validate() {
        $this->error = null;

        // Required field validation
        if (empty($this->first_name)) {
            $this->error = 'First name is required.';
            return false;
        }

        if (empty($this->last_name)) {
            $this->error = 'Last name is required.';
            return false;
        }

        // Phone number validation - not required for children
        $is_child = $this->isChildMember();
        if (empty($this->phone_number) && !$is_child) {
            $this->error = 'Phone number is required.';
            return false;
        }

        if (empty($this->gender)) {
            $this->error = 'Gender is required.';
            return false;
        }

        if (empty($this->member_status)) {
            $this->error = 'Member status is required.';
            return false;
        }

        // Name format validation
        if (!preg_match('/^[a-zA-Z\s\'-]+$/', $this->first_name)) {
            $this->error = 'First name contains invalid characters.';
            return false;
        }

        if (!preg_match('/^[a-zA-Z\s\'-]+$/', $this->last_name)) {
            $this->error = 'Last name contains invalid characters.';
            return false;
        }

        // Email validation (if provided)
        if (!empty($this->email) && !filter_var($this->email, FILTER_VALIDATE_EMAIL)) {
            $this->error = 'Invalid email format.';
            return false;
        }

        // Date of birth validation (if provided)
        if (!empty($this->date_of_birth) && $this->date_of_birth !== '0000-00-00') {
            $date = DateTime::createFromFormat('Y-m-d', $this->date_of_birth);
            if (!$date || $date->format('Y-m-d') !== $this->date_of_birth) {
                $this->error = 'Invalid date of birth format.';
                return false;
            }

            // Check if date is not in the future
            if ($date > new DateTime()) {
                $this->error = 'Date of birth cannot be in the future.';
                return false;
            }
        }

        // Phone number format validation - skip for children with empty phone
        if (!empty($this->phone_number) && !preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $this->phone_number)) {
            $this->error = 'Invalid phone number format.';
            return false;
        }

        // Gender validation
        $valid_genders = ['male', 'female', 'other'];
        if (!in_array(strtolower($this->gender), $valid_genders)) {
            $this->error = 'Invalid gender value.';
            return false;
        }

        // Member status validation
        $valid_statuses = ['active', 'inactive', 'suspended', 'pending'];
        if (!in_array(strtolower($this->member_status), $valid_statuses)) {
            $this->error = 'Invalid member status.';
            return false;
        }

        // Email uniqueness check (if email is provided)
        if (!empty($this->email)) {
            $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email";
            // If this is an update, exclude the current member's ID from the check
            if (!empty($this->id)) {
                $query .= " AND id != :id";
            }
            $query .= " LIMIT 1";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':email', $this->email);
            if (!empty($this->id)) {
                $stmt->bindParam(':id', $this->id);
            }
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                $this->error = 'This email address is already in use by another member.';
                return false;
            }
        }

        return true;
    }

    /**
     * Create member with validation
     *
     * @return bool
     */
    public function create() {
        // Define constant to prevent database queries in PhoneNumberUtils during creation
        if (!defined('MEMBER_UPDATE_IN_PROGRESS')) {
            define('MEMBER_UPDATE_IN_PROGRESS', true);
        }

        // Validate data before creating
        if (!$this->validate()) {
            return false;
        }
        $query = "INSERT INTO " . $this->table_name . "
                SET
                    first_name = :first_name,
                    last_name = :last_name,
                    email = :email,
                    phone_number = :phone_number,
                    date_of_birth = :date_of_birth,
                    gender = :gender,
                    marital_status = :marital_status,
                    location = :location,
                    emergency_contact_name = :emergency_contact_name,
                    emergency_contact_phone = :emergency_contact_phone,
                    baptism_status = :baptism_status,
                    department = :department,
                    role = :role,
                    membership_date = :membership_date,
                    occupation = :occupation,
                    school = :school,
                    member_status = :member_status,
                    profile_picture = :profile_picture,
                    created_at = :created_at,
                    updated_at = :updated_at";

        $stmt = $this->conn->prepare($query);

        // Sanitize and bind data
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->email = htmlspecialchars(strip_tags($this->email));

        // Handle empty email - convert to NULL to avoid unique constraint issues
        if (empty($this->email)) {
            $this->email = null;
        }
        $this->phone_number = htmlspecialchars(strip_tags($this->phone_number));
        $this->date_of_birth = htmlspecialchars(strip_tags($this->date_of_birth));

        // Handle invalid date values - convert '0000-00-00' to NULL
        if ($this->date_of_birth === '0000-00-00' || empty($this->date_of_birth)) {
            $this->date_of_birth = null;
        }
        $this->gender = htmlspecialchars(strip_tags($this->gender));
        $this->marital_status = htmlspecialchars(strip_tags($this->marital_status));
        $this->location = htmlspecialchars(strip_tags($this->location));
        $this->emergency_contact_name = htmlspecialchars(strip_tags($this->emergency_contact_name));
        $this->emergency_contact_phone = htmlspecialchars(strip_tags($this->emergency_contact_phone));
        $this->baptism_status = htmlspecialchars(strip_tags($this->baptism_status));
        $this->department = htmlspecialchars(strip_tags($this->department));
        $this->role = htmlspecialchars(strip_tags($this->role));
        $this->membership_date = htmlspecialchars(strip_tags($this->membership_date));
        $this->occupation = htmlspecialchars(strip_tags($this->occupation));
        $this->school = htmlspecialchars(strip_tags($this->school));
        $this->member_status = htmlspecialchars(strip_tags($this->member_status));
        // Profile picture is handled separately
        $this->created_at = date('Y-m-d H:i:s');
        $this->updated_at = date('Y-m-d H:i:s');

        $stmt->bindParam(":first_name", $this->first_name);
        $stmt->bindParam(":last_name", $this->last_name);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":phone_number", $this->phone_number);
        $stmt->bindParam(":date_of_birth", $this->date_of_birth);
        $stmt->bindParam(":gender", $this->gender);
        $stmt->bindParam(":marital_status", $this->marital_status);
        $stmt->bindParam(":location", $this->location);
        $stmt->bindParam(":emergency_contact_name", $this->emergency_contact_name);
        $stmt->bindParam(":emergency_contact_phone", $this->emergency_contact_phone);
        $stmt->bindParam(":baptism_status", $this->baptism_status);
        $stmt->bindParam(":department", $this->department);
        $stmt->bindParam(":role", $this->role);
        $stmt->bindParam(":membership_date", $this->membership_date);
        $stmt->bindParam(":occupation", $this->occupation);
        $stmt->bindParam(":school", $this->school);
        $stmt->bindParam(":member_status", $this->member_status);
        $stmt->bindParam(":profile_picture", $this->profile_picture);
        $stmt->bindParam(":created_at", $this->created_at);
        $stmt->bindParam(":updated_at", $this->updated_at);

        // Execute query
        try {
            if ($stmt->execute()) {
                $this->id = $this->conn->lastInsertId();

                // Update normalized phone numbers after creation (non-critical operation)
                try {
                    $this->updateNormalizedPhoneNumbers();
                } catch (Exception $e) {
                    // Log the error but don't fail the main creation
                    error_log("Warning: Failed to update normalized phone numbers for new member ID {$this->id}: " . $e->getMessage());
                }

                return true;
            } else {
                $this->error = 'Failed to create member record.';
                return false;
            }
        } catch (PDOException $e) {
            // Check for duplicate email error
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'email') !== false) {
                $this->error = 'Email address already exists.';
            } else {
                $this->error = 'Database error occurred while creating member.';
                error_log("Member creation failed: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Check if this member is a child (under 18 or in children department)
     *
     * @return bool
     */
    private function isChildMember() {
        // Check if member is in children department
        if ($this->department === 'children') {
            return true;
        }

        // Check if member is under 18 years old
        if (!empty($this->date_of_birth) && $this->date_of_birth !== '0000-00-00') {
            try {
                $birth_date = new DateTime($this->date_of_birth);
                $today = new DateTime();
                $age = $today->diff($birth_date)->y;
                return $age < 18;
            } catch (Exception $e) {
                // If date parsing fails, fall back to department check
                return false;
            }
        }

        return false;
    }

    /**
     * Update normalized phone numbers for this member
     *
     * @return bool
     */
    private function updateNormalizedPhoneNumbers() {
        try {
            require_once 'utils/PhoneNumberUtils.php';

            $phone_normalized = '';
            $emergency_normalized = '';

            // Normalize phone numbers using current country setting
            if (!empty($this->phone_number)) {
                $phone_normalized = PhoneNumberUtils::normalize($this->phone_number);
            }

            if (!empty($this->emergency_contact_phone)) {
                $emergency_normalized = PhoneNumberUtils::normalize($this->emergency_contact_phone);
            }

            // Update normalized fields with proper error handling
            $query = "UPDATE " . $this->table_name . "
                      SET phone_number_normalized = ?,
                          emergency_contact_phone_normalized = ?
                      WHERE id = ?";

            $stmt = $this->conn->prepare($query);

            // Execute with proper error handling
            $result = $stmt->execute([$phone_normalized, $emergency_normalized, $this->id]);

            if (!$result) {
                error_log("Failed to update normalized phone numbers for member ID: " . $this->id);
                return false;
            }

            return true;

        } catch (PDOException $e) {
            error_log("PDO Error updating normalized phone numbers: " . $e->getMessage());
            // Don't fail the main update if phone normalization fails
            return true;
        } catch (Exception $e) {
            error_log("Error updating normalized phone numbers: " . $e->getMessage());
            // Don't fail the main update if phone normalization fails
            return true;
        }
    }

    /**
     * Update member with validation
     *
     * @return bool
     */
    public function update() {
        // Define constant to prevent database queries in PhoneNumberUtils during update
        if (!defined('MEMBER_UPDATE_IN_PROGRESS')) {
            define('MEMBER_UPDATE_IN_PROGRESS', true);
        }

        // CRITICAL: Validate data before updating to ensure data integrity
        if (!$this->validate()) {
            // The 'error' property is set by the validate() method
            return false;
        }
        $query = "UPDATE " . $this->table_name . "
                SET
                    first_name = :first_name,
                    last_name = :last_name,
                    email = :email,
                    phone_number = :phone_number,
                    date_of_birth = :date_of_birth,
                    gender = :gender,
                    marital_status = :marital_status,
                    location = :location,
                    emergency_contact_name = :emergency_contact_name,
                    emergency_contact_phone = :emergency_contact_phone,
                    baptism_status = :baptism_status,
                    department = :department,
                    role = :role,
                    membership_date = :membership_date,
                    occupation = :occupation,
                    school = :school,
                    member_status = :member_status,
                    updated_at = :updated_at
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize and bind data
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->email = htmlspecialchars(strip_tags($this->email));

        // Handle empty email - convert to NULL to avoid unique constraint issues
        if (empty($this->email)) {
            $this->email = null;
        }
        $this->phone_number = htmlspecialchars(strip_tags($this->phone_number));
        $this->date_of_birth = htmlspecialchars(strip_tags($this->date_of_birth));

        // Handle invalid date values - convert '0000-00-00' to NULL
        if ($this->date_of_birth === '0000-00-00' || empty($this->date_of_birth)) {
            $this->date_of_birth = null;
        }
        $this->gender = htmlspecialchars(strip_tags($this->gender));
        $this->marital_status = htmlspecialchars(strip_tags($this->marital_status));
        $this->location = htmlspecialchars(strip_tags($this->location));
        $this->emergency_contact_name = htmlspecialchars(strip_tags($this->emergency_contact_name));
        $this->emergency_contact_phone = htmlspecialchars(strip_tags($this->emergency_contact_phone));
        $this->baptism_status = htmlspecialchars(strip_tags($this->baptism_status));
        $this->department = htmlspecialchars(strip_tags($this->department));
        $this->role = htmlspecialchars(strip_tags($this->role));
        $this->membership_date = htmlspecialchars(strip_tags($this->membership_date));
        $this->occupation = htmlspecialchars(strip_tags($this->occupation));
        $this->member_status = htmlspecialchars(strip_tags($this->member_status));
        $this->updated_at = date('Y-m-d H:i:s');
        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":first_name", $this->first_name);
        $stmt->bindParam(":last_name", $this->last_name);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":phone_number", $this->phone_number);
        $stmt->bindParam(":date_of_birth", $this->date_of_birth);
        $stmt->bindParam(":gender", $this->gender);
        $stmt->bindParam(":marital_status", $this->marital_status);
        $stmt->bindParam(":location", $this->location);
        $stmt->bindParam(":emergency_contact_name", $this->emergency_contact_name);
        $stmt->bindParam(":emergency_contact_phone", $this->emergency_contact_phone);
        $stmt->bindParam(":baptism_status", $this->baptism_status);
        $stmt->bindParam(":department", $this->department);
        $stmt->bindParam(":role", $this->role);
        $stmt->bindParam(":membership_date", $this->membership_date);
        $stmt->bindParam(":occupation", $this->occupation);
        $stmt->bindParam(":school", $this->school);
        $stmt->bindParam(":member_status", $this->member_status);
        $stmt->bindParam(":updated_at", $this->updated_at);
        $stmt->bindParam(":id", $this->id);

        // Execute query
        try {
            if ($stmt->execute()) {
                // Update normalized phone numbers after update (non-critical operation)
                try {
                    $this->updateNormalizedPhoneNumbers();
                } catch (Exception $e) {
                    // Log the error but don't fail the main update
                    error_log("Warning: Failed to update normalized phone numbers for member ID {$this->id}: " . $e->getMessage());
                }
                return true;
            } else {
                $this->error = 'Failed to update member record in the database.';
                return false;
            }
        } catch (PDOException $e) {
            // This is a fallback in case the validate() check fails for some reason (race condition)
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'email') !== false) {
                $this->error = 'Email address already exists.';
            } else {
                $this->error = 'Database error occurred while updating member.';
                error_log("Member update failed: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Update profile picture
     *
     * @return bool
     */
    public function updateProfilePicture() {
        $query = "UPDATE " . $this->table_name . "
                SET
                    profile_picture = :profile_picture,
                    updated_at = :updated_at
                WHERE
                    id = :id";

        $stmt = $this->conn->prepare($query);

        // Bind data
        $this->updated_at = date('Y-m-d H:i:s');
        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":profile_picture", $this->profile_picture);
        $stmt->bindParam(":updated_at", $this->updated_at);
        $stmt->bindParam(":id", $this->id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Delete member
     *
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);

        // Sanitize and bind ID
        $this->id = htmlspecialchars(strip_tags($this->id));
        $stmt->bindParam(":id", $this->id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Search members
     *
     * @param string $keywords
     * @return array Array of member objects
     */
    public function search($keywords) {
        // Add search conditions
        $searchConditions = [
            "first_name LIKE :keywords",
            "last_name LIKE :keywords",
            "email LIKE :keywords",
            "phone_number LIKE :keywords",
            "location LIKE :keywords",
            "department LIKE :keywords",
            "role LIKE :keywords",
            "occupation LIKE :keywords"
        ];

        $query = "SELECT * FROM " . $this->table_name . "
                WHERE (" . implode(" OR ", $searchConditions) . ")
                ORDER BY created_at DESC";

        $stmt = $this->conn->prepare($query);

        // Sanitize and bind search keywords
        $keywords = htmlspecialchars(strip_tags($keywords));
        $keywords = "%{$keywords}%";
        $stmt->bindValue(':keywords', $keywords);

        // Execute query
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }

    /**
     * Get members by department
     *
     * @param string $department
     * @return array Array of member objects
     */
    public function getByDepartment($department) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE department = :department ORDER BY created_at DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindValue(':department', $department);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }

    /**
     * Get members by role
     *
     * @param string $role
     * @return array Array of member objects
     */
    public function getByRole($role) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE role = :role ORDER BY created_at DESC";
        $stmt = $this->conn->prepare($query);

        // Bind role
        $stmt->bindParam(":role", $role);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }

    /**
     * Get members by status
     *
     * @param string $status
     * @return array Array of member objects
     */
    public function getByStatus($status) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE member_status = :status ORDER BY created_at DESC";
        $stmt = $this->conn->prepare($query);

        // Bind status
        $stmt->bindParam(":status", $status);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }

    // getCount() method implemented above

    /**
     * Get members with birthdays in current month
     *
     * @return array Array of member objects
     */
    public function getBirthdaysThisMonth() {
        $query = "SELECT *,
                  TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) AS age,
                  CONCAT(MONTH(date_of_birth), '-', DAY(date_of_birth)) as birthday_date
                  FROM " . $this->table_name . "
                  WHERE MONTH(date_of_birth) = MONTH(CURRENT_DATE())
                  ORDER BY
                      DAY(date_of_birth) ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get members with birthdays today
     *
     * @return array Array of member objects
     */
    public function getBirthdaysToday() {
        $query = "SELECT *,
                  TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) AS age,
                  CONCAT(MONTH(date_of_birth), '-', DAY(date_of_birth)) as birthday_date
                  FROM " . $this->table_name . "
                  WHERE MONTH(date_of_birth) = MONTH(CURRENT_DATE()) AND
                      DAY(date_of_birth) = DAY(CURRENT_DATE())
                  ORDER BY
                      first_name ASC, last_name ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get members with birthdays this week
     *
     * @return array Array of member objects
     */
    public function getBirthdaysThisWeek() {
        // Get start and end of current week
        $start_of_week = date('Y-m-d', strtotime('monday this week'));
        $end_of_week = date('Y-m-d', strtotime('sunday this week'));

        $query = "SELECT *,
                  TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) AS age,
                  CONCAT(MONTH(date_of_birth), '-', DAY(date_of_birth)) as birthday_date,
                  DATE(CONCAT(YEAR(CURDATE()), '-', MONTH(date_of_birth), '-', DAY(date_of_birth))) as birthday_this_year
                  FROM " . $this->table_name . "
                  WHERE date_of_birth IS NOT NULL
                  AND DATE(CONCAT(YEAR(CURDATE()), '-', MONTH(date_of_birth), '-', DAY(date_of_birth))) BETWEEN :start_week AND :end_week
                  ORDER BY
                      MONTH(date_of_birth), DAY(date_of_birth)";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_week', $start_of_week);
        $stmt->bindParam(':end_week', $end_of_week);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get all members with their birthdays
     *
     * @return array Array of member objects
     */
    public function getAllBirthdays() {
        $query = "SELECT *,
                  TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) AS age,
                  CONCAT(MONTH(date_of_birth), '-', DAY(date_of_birth)) as birthday_date
                  FROM " . $this->table_name . "
                  WHERE
                      date_of_birth IS NOT NULL AND date_of_birth != ''
                  ORDER BY
                      MONTH(date_of_birth), DAY(date_of_birth)";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }


    /**
     * Get count of members by gender
     *
     * @param string $gender
     * @return int
     */
    public function getCountByGender($gender) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE gender = :gender";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':gender', $gender);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'] ?? 0;
    }

    /**
     * Get count of members with phone numbers
     *
     * @return int
     */
    public function getCountWithPhoneNumbers() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE phone_number IS NOT NULL AND phone_number != ''";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'] ?? 0;
    }

    /**
     * Get count of members by status
     *
     * @param string $status
     * @return int
     */
    public function getCountByStatus($status) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE member_status = :status";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'] ?? 0;
    }

    /**
     * Get count of members since a specific date
     *
     * @param string $date
     * @return int
     */
    public function getCountSince($date) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE membership_date >= :date";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'] ?? 0;
    }

    /**
     * Get count of members by date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return int
     */
    public function getCountByDateRange($start_date, $end_date) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE membership_date BETWEEN :start_date AND :end_date";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'] ?? 0;
    }

    /**
     * Get members by date range
     *
     * @param string $start_date
     * @param string $end_date
     * @return PDOStatement
     */
    public function getByDateRange($start_date, $end_date) {
        $query = "SELECT * FROM " . $this->table_name . "
                  WHERE membership_date BETWEEN :start_date AND :end_date
                  ORDER BY membership_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get members by date range and department
     *
     * @param string $start_date
     * @param string $end_date
     * @param string $department
     * @return PDOStatement
     */
    public function getByDateRangeAndDepartment($start_date, $end_date, $department) {
        $query = "SELECT * FROM " . $this->table_name . "
                  WHERE membership_date BETWEEN :start_date AND :end_date
                  AND department = :department
                  ORDER BY membership_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->bindParam(':department', $department);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get count of members by department
     *
     * @return array
     */
    public function getCountByDepartment() {
        $query = "SELECT department, COUNT(*) as count FROM " . $this->table_name . "
                  GROUP BY department
                  ORDER BY count DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get members with their attendance status for a specific date and service
     *
     * @param string $date Attendance date
     * @param int $service_id Service ID
     * @param array $filters Associative array of filters (search, department, status)
     * @param int $limit Number of records to return
     * @param int $offset Offset for pagination
     * @return array
     */
    public function getMembersWithAttendanceStatus($date, $service_id, $filters = [], $limit = 100, $offset = 0) {
        // Start building the query
        $query = "SELECT m.*, a.status as attendance_status
                  FROM " . $this->table_name . " m
                  LEFT JOIN attendance a ON m.id = a.member_id AND a.attendance_date = :date AND a.service_id = :service_id";

        // Add WHERE clause if there are filters
        $whereClause = [];
        $params = [':date' => $date, ':service_id' => $service_id];

        // Add search filter
        if (isset($filters['search']) && !empty($filters['search'])) {
            $whereClause[] = "(m.first_name LIKE :search OR m.last_name LIKE :search OR m.email LIKE :search OR m.phone_number LIKE :search)";
            $params[':search'] = "%{$filters['search']}%";
        }

        // Add department filter
        if (isset($filters['department']) && !empty($filters['department'])) {
            $whereClause[] = "m.department = :department";
            $params[':department'] = $filters['department'];
        }

        // Add status filter
        if (isset($filters['status']) && !empty($filters['status'])) {
            $whereClause[] = "m.member_status = :status";
            $params[':status'] = $filters['status'];
        }

        // Combine WHERE clauses if any
        if (!empty($whereClause)) {
            $query .= " WHERE " . implode(" AND ", $whereClause);
        }

        // Add ORDER BY clause
        $query .= " ORDER BY m.first_name ASC, m.last_name ASC";

        // Add LIMIT and OFFSET for pagination
        $query .= " LIMIT :limit OFFSET :offset";
        $params[':limit'] = (int)$limit;
        $params[':offset'] = (int)$offset;

        // Prepare and execute the query
        $stmt = $this->conn->prepare($query);

        // Bind parameters
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value, PDO::PARAM_STR);
            }
        }

        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get member by phone number
     *
     * @param string $phoneNumber
     * @return object|false Member object or false if not found
     */
    public function getByPhoneNumber($phoneNumber) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE phone_number = :phone_number LIMIT 0,1";
        $stmt = $this->conn->prepare($query);

        // Bind phone number
        $stmt->bindParam(":phone_number", $phoneNumber);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_OBJ);
    }

    /**
     * Get member by email address
     *
     * @param string $email
     * @return object|false Member object or false if not found
     */
    public function getByEmail($email) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE email = :email LIMIT 0,1";
        $stmt = $this->conn->prepare($query);

        // Bind email
        $stmt->bindParam(":email", $email);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_OBJ);
    }
}
