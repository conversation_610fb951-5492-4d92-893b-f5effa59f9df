<?php
/**
 * API endpoint to get SMS status counts
 * Used for real-time updates of the SMS status cards
 */

// Include necessary files
require_once '../config/database.php';
require_once '../models/Sms.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Create database connection
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get all SMS messages with a direct query to bypass any caching
    $query = "SELECT * FROM sms_messages ORDER BY sent_date DESC";
    $stmt = $conn->query($query);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Count messages by status
    $total = count($messages);
    
    // Count sent messages (including partial)
    $sent = array_filter($messages, function($msg) {
        return $msg['status'] === 'sent' || $msg['status'] === 'partial';
    });
    $sent_count = count($sent);
    
    // Count pending messages
    $pending = array_filter($messages, function($msg) {
        return $msg['status'] === 'pending';
    });
    $pending_count = count($pending);
    
    // Count partial messages
    $partial = array_filter($messages, function($msg) {
        return $msg['status'] === 'partial';
    });
    $partial_count = count($partial);
    
    // Count failed messages
    $failed = array_filter($messages, function($msg) {
        return $msg['status'] === 'failed';
    });
    $failed_count = count($failed);
    
    // Return the counts as JSON
    echo json_encode([
        'success' => true,
        'total' => $total,
        'sent' => $sent_count,
        'pending' => $pending_count,
        'partial' => $partial_count,
        'failed' => $failed_count,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    // Return error
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching SMS status counts: ' . $e->getMessage()
    ]);
}
?>
