<div class="container mx-auto max-w-6xl">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-1">SMS Details</h1>
            <div class="text-sm text-gray-600 flex items-center">
                <i class="fas fa-calendar-alt mr-2 text-primary"></i>
                <span>Sent on: <?php echo date('F j, Y, g:i a', strtotime($message['sent_date'])); ?></span>
            </div>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mt-4 md:mt-0">
            <a href="<?php echo BASE_URL; ?>sms/messages" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center justify-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Messages
            </a>
            <button type="button" onclick="confirmDelete(<?php echo $message['id']; ?>)" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md flex items-center justify-center">
                <i class="fas fa-trash-alt mr-2"></i> Delete
            </button>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div class="p-6">
            <!-- Message Status -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <span class="text-gray-700 font-medium mr-2">Status:</span>
                    <?php if ($message['status'] == 'sent') : ?>
                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i> Sent
                        </span>
                    <?php elseif ($message['status'] == 'partial') : ?>
                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">
                            <i class="fas fa-exclamation-triangle mr-1"></i> Partial
                        </span>
                    <?php elseif ($message['status'] == 'pending') : ?>
                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            <i class="fas fa-clock mr-1"></i> Pending
                        </span>
                    <?php else : ?>
                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                            <i class="fas fa-exclamation-circle mr-1"></i> Failed
                        </span>
                    <?php endif; ?>
                </div>
                <div class="text-sm text-gray-600">
                    <span>Sent by: <strong><?php echo $message['sent_by_name'] ?? 'System'; ?></strong></span>
                </div>
            </div>

            <!-- Message Content -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Message Content</h3>
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <p class="text-gray-700 whitespace-pre-line"><?php echo htmlspecialchars($message['message']); ?></p>
                </div>
            </div>

            <!-- Recipients -->
            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-2">Recipients (<?php echo count($recipient_details); ?>)</h3>
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <?php if (empty($recipient_details)) : ?>
                        <p class="text-gray-500 italic">No recipients found</p>
                    <?php else : ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <?php foreach ($recipient_details as $recipient) : ?>
                                <div class="flex items-center p-3 bg-white rounded-md shadow-sm border border-gray-100">
                                    <?php if ($recipient['type'] == 'member') : ?>
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary-light flex items-center justify-center text-primary font-bold">
                                            <?php echo strtoupper(substr($recipient['display'], 0, 1)); ?>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($recipient['display']); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo htmlspecialchars($recipient['phone']); ?></p>
                                        </div>
                                    <?php else : ?>
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($recipient['display']); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo $recipient['type'] == 'phone' ? 'Direct Number' : 'Unknown Recipient'; ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<script>
    function confirmDelete(id) {
        // Create modal if it doesn't exist
        if (!document.getElementById('deleteModal')) {
            const modal = document.createElement('div');
            modal.id = 'deleteModal';
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Delete SMS Message</h3>
                        <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500">Are you sure you want to delete this SMS message? This action cannot be undone.</p>
                        </div>
                        <div class="flex justify-center mt-4 px-4 py-3">
                            <button id="cancelDelete" class="bg-gray-200 px-4 py-2 rounded-md text-gray-800 hover:bg-gray-300 mr-2 transition-colors duration-200">Cancel</button>
                            <a id="confirmDelete" href="#" class="bg-red-600 px-4 py-2 rounded-md text-white hover:bg-red-700 transition-colors duration-200">Delete</a>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        const modal = document.getElementById('deleteModal');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('cancelDelete');

        // Set the delete URL
        confirmBtn.href = '<?php echo BASE_URL; ?>sms/messages/' + id;

        // Show the modal
        modal.classList.remove('hidden');

        // Close modal when cancel is clicked
        cancelBtn.onclick = function() {
            modal.classList.add('hidden');
        };

        // Close modal when clicking outside
        modal.onclick = function(e) {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        };
    }
</script>
