<?php
/**
 * Attendance Controller
 */

require_once 'models/Attendance.php';
require_once 'models/Service.php';
require_once 'models/Member.php';
require_once 'models/Setting.php';
require_once 'utils/validation.php';
require_once 'helpers/functions.php';

class AttendanceController {
    private $database;
    private $attendance;
    private $service;
    private $member;
    private $setting;
    private $logger;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->attendance = new Attendance($this->database->getConnection());
        $this->service = new Service($this->database->getConnection());
        $this->member = new Member($this->database->getConnection());
        $this->setting = new Setting($this->database->getConnection());

        // Initialize logging
        $this->logger = AppLogger::getInstance();
        $this->logger->setContext(['controller' => 'AttendanceController']);
    }

    /**
     * Display attendance list
     *
     * @return void
     */
    public function index() {
        // Get all attendance records
        $stmt = $this->attendance->getAll();
        $attendances = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Get all services for filter
        $stmt = $this->service->getAll();
        $services = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Get the most recent service and date for summary
        if (!empty($attendances)) {
            $most_recent_date = $attendances[0]->attendance_date;
            $most_recent_service_id = $attendances[0]->service_id;
        } else {
            $most_recent_date = date('Y-m-d');
            $most_recent_service_id = 1; // Default to first service
        }

        // Get service details
        $this->service->id = $most_recent_service_id;
        $service = $this->service->readOne();

        // Get member counts by attendance status
        $present_count = $this->attendance->countMembersByStatus($most_recent_date, $most_recent_service_id, 'present');
        $absent_count = $this->attendance->countMembersByStatus($most_recent_date, $most_recent_service_id, 'absent');
        $late_count = $this->attendance->countMembersByStatus($most_recent_date, $most_recent_service_id, 'late');

        // Get gender-based attendance statistics
        $gender_stats = $this->attendance->getGenderStats($most_recent_date, $most_recent_service_id);

        // Get limited members by attendance status (for display)
        $present_members = $this->attendance->getPresentMembers($most_recent_date, $most_recent_service_id, 5);
        $absent_members = $this->attendance->getAbsentMembers($most_recent_date, $most_recent_service_id, 5);
        $late_members = $this->attendance->getLateMembers($most_recent_date, $most_recent_service_id, 5);

        // Get all active members for comparison
        $all_active_members = $this->member->getByStatus('active');
        $total_active_members = count($all_active_members);

        // Calculate statistics
        $total_marked = $present_count + $absent_count + $late_count;
        $attendance_rate = $total_marked > 0 ? round(($present_count + $late_count) / $total_marked * 100) : 0;
        $coverage_rate = $total_active_members > 0 ? round($total_marked / $total_active_members * 100) : 0;

        // Get department statistics
        $department_stats = $this->getDepartmentAttendanceStats($most_recent_date, $most_recent_service_id);

        // Get recent attendance trends
        $attendance_trends = $this->getAttendanceTrends($most_recent_service_id, 5); // Last 5 services

        // Get recent services for the activity timeline
        $recent_services = $this->getRecentServices(5); // Last 5 services

        // For the summary section
        $date = $most_recent_date;
        $service_id = $most_recent_service_id;

        // Set page title and active page
        $page_title = getPageTitle('Attendance');
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display attendance form - DEPRECATED: now redirects to QR system
     *
     * @return void
     * @deprecated Use QR attendance system instead
     */
    public function create() {
        // Redirect to QR attendance system
        set_flash_message('Manual attendance has been replaced with the modern QR attendance system.', 'info');
        redirect('attendance');
    }

    /**
     * Store attendance record - DEPRECATED: now redirects to QR system
     *
     * @return void
     * @deprecated Use QR attendance system instead
     */
    public function store() {
        // Redirect to QR attendance system
        set_flash_message('Manual attendance has been replaced with the modern QR attendance system.', 'info');
        redirect('attendance');
    }

    /**
     * Display bulk attendance form - DEPRECATED: now redirects to QR system
     *
     * @return void
     * @deprecated Use QR attendance system instead
     */
    public function bulkCreate() {
        // Redirect to QR attendance system
        set_flash_message('Bulk attendance has been replaced with the modern QR attendance system. Generate a QR code for members to mark their own attendance.', 'info');
        redirect('attendance');
    }

    /**
     * Store bulk attendance records - DEPRECATED: now redirects to QR system
     *
     * @return void
     * @deprecated Use QR attendance system instead
     */
    public function bulkStore() {
        // Redirect to QR attendance system
        set_flash_message('Bulk attendance has been replaced with the modern QR attendance system. Generate a QR code for members to mark their own attendance.', 'info');
        redirect('attendance');
    }

    /**
     * View attendance by date
     *
     * @return void
     */
    public function viewByDate() {
        // Check if date is provided
        if (!isset($_GET['date']) || empty($_GET['date'])) {
            redirect('attendance');
            exit;
        }

        $date = sanitize($_GET['date']);
        $service_id = isset($_GET['service_id']) ? sanitize($_GET['service_id']) : null;

        // Get attendance records for the date (filtered by service if provided)
        $stmt = $this->attendance->getByDate($date, $service_id);
        $attendances = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Get all services for filter
        $stmt = $this->service->getAll();
        $services = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Get attendance summary
        $present_count = 0;
        $absent_count = 0;
        $late_count = 0;

        foreach ($attendances as $attendance) {
            // Only count records that match the service filter (if provided)
            if ($service_id === null || $attendance->service_id == $service_id) {
                if ($attendance->status === 'present') {
                    $present_count++;
                } elseif ($attendance->status === 'absent') {
                    $absent_count++;
                } elseif ($attendance->status === 'late') {
                    $late_count++;
                }
            }
        }

        // Set page title and active page
        $page_title = getPageTitle('Attendance for ' . format_date($date));
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view - using the file with hyphen to match URL routing
        require_once 'views/attendance/view-by-date.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display attendance records for a specific member
     *
     * @return void
     */
    public function memberAttendance() {
        // Check if member ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Member ID is required.', 'error');
            redirect('members');
            exit;
        }

        $member_id = sanitize($_GET['id']);

        // Get member details
        if (!$this->member->getById($member_id)) {
            set_flash_message('Member not found.', 'error');
            redirect('members');
            exit;
        }

        // Convert member object to array for the view
        $member = [
            'id' => $this->member->id,
            'first_name' => $this->member->first_name,
            'last_name' => $this->member->last_name,
            'email' => $this->member->email,
            'phone_number' => $this->member->phone_number,
            'department' => $this->member->department,
            'member_status' => $this->member->member_status
        ];

        // Get pagination parameters
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = 20; // Records per page
        $offset = ($page - 1) * $limit;

        // Get filter parameters
        $service_filter = isset($_GET['service']) ? sanitize($_GET['service']) : '';
        $status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
        $date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
        $date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';

        // Get member's attendance records with filters
        $attendance_records = $this->getMemberAttendanceRecords($member_id, $limit, $offset, $service_filter, $status_filter, $date_from, $date_to);
        $total_records = $this->countMemberAttendanceRecords($member_id, $service_filter, $status_filter, $date_from, $date_to);

        // Calculate pagination
        $total_pages = ceil($total_records / $limit);

        // Get member's attendance statistics
        $attendance_stats = $this->getMemberAttendanceStats($member_id);

        // Get all services for filter dropdown
        $stmt = $this->service->getAll();
        $services = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Get recent attendance summary (last 10 records)
        $recent_attendance = $this->getMemberAttendanceRecords($member_id, 10, 0);

        // Set page title and active page
        $page_title = getPageTitle('Attendance History - ' . $member['first_name'] . ' ' . $member['last_name']);
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/member.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display attendance summary after marking attendance
     *
     * @return void
     */
    public function summary() {
        // Check if date and service_id are provided
        if (!isset($_GET['date']) || empty($_GET['date']) || !isset($_GET['service_id']) || empty($_GET['service_id'])) {
            redirect('attendance');
            exit;
        }

        $date = sanitize($_GET['date']);
        $service_id = sanitize($_GET['service_id']);

        // Get service details
        $this->service->id = $service_id;
        $service = $this->service->readOne();

        // Get members by attendance status
        $present_members = $this->attendance->getPresentMembers($date, $service_id);
        $absent_members = $this->attendance->getAbsentMembers($date, $service_id);
        $late_members = $this->attendance->getLateMembers($date, $service_id);

        // Get all active members for comparison
        $stmt = $this->member->getByStatus('active');
        $all_active_members = $stmt->fetchAll();
        $total_active_members = count($all_active_members);

        // Calculate statistics
        $total_marked = count($present_members) + count($absent_members) + count($late_members);
        $attendance_rate = $total_marked > 0 ? round((count($present_members) + count($late_members)) / $total_marked * 100) : 0;
        $coverage_rate = $total_active_members > 0 ? round($total_marked / $total_active_members * 100) : 0;

        // Get department statistics
        $department_stats = $this->getDepartmentAttendanceStats($date, $service_id);

        // Get recent attendance trends
        $attendance_trends = $this->getAttendanceTrends($service_id, 5); // Last 5 services

        // Set page title and active page
        $page_title = 'Attendance Summary for ' . format_date($date) . ' - ICGC Emmanuel Temple';
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/summary.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display edit attendance form
     *
     * @return void
     */
    public function edit() {
        // Check if ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Attendance ID is required', 'danger');
            redirect('attendance');
            exit;
        }

        $id = sanitize($_GET['id']);

        // Debug the ID value
        error_log("Attendance ID: " . $id);

        // Get attendance record
        $found = $this->attendance->getById($id);
        if (!$found) {
            set_flash_message('Attendance record not found', 'danger');
            redirect('attendance');
            exit;
        }

        // Create a copy of the attendance object for the view
        $attendance = $this->attendance;

        // Get all services
        $stmt = $this->service->getAll();
        $services = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Get all members
        $members = $this->member->getAll();

        // Set page title and active page
        $page_title = getPageTitle('Edit Attendance');
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/edit.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Update attendance record
     *
     * @return void
     */
    public function update() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate form data
            $errors = [];

            // Required fields
            if (empty($_POST['id'])) {
                $errors[] = 'Attendance ID is required';
            }

            if (empty($_POST['service_id'])) {
                $errors[] = 'Service is required';
            }

            if (empty($_POST['attendance_date'])) {
                $errors[] = 'Attendance date is required';
            }

            // If there are errors, redirect back with errors
            if (!empty($errors)) {
                $_SESSION['errors'] = $errors;
                $_SESSION['form_data'] = $_POST;
                redirect('attendance/edit?id=' . $_POST['id']);
                exit;
            }

            // Get attendance record
            $id = sanitize($_POST['id']);
            if (!$this->attendance->getById($id)) {
                set_flash_message('Attendance record not found', 'danger');
                redirect('attendance');
                exit;
            }

            // Set attendance properties
            $this->attendance->id = $id;
            $this->attendance->service_id = sanitize($_POST['service_id']);
            $this->attendance->member_id = !empty($_POST['member_id']) ? sanitize($_POST['member_id']) : null;
            $this->attendance->attendance_date = sanitize($_POST['attendance_date']);
            $this->attendance->status = sanitize($_POST['status'] ?? 'present');
            $this->attendance->updated_at = date('Y-m-d H:i:s');

            // Update attendance record
            if ($this->attendance->update()) {
                // Set success message
                set_flash_message('Attendance record updated successfully', 'success');
                redirect('attendance');
            } else {
                // Set error message
                set_flash_message('Failed to update attendance record', 'danger');
                redirect('attendance/edit?id=' . $id);
            }
        } else {
            // Not a POST request, redirect to attendance list
            redirect('attendance');
        }
    }

    /**
     * View all attendance records
     *
     * @return void
     */
    public function viewAll() {
        // Get all attendance records with pagination
        $page = isset($_GET['page']) ? (int)sanitize($_GET['page']) : 1;
        $limit = 20; // Records per page
        $offset = ($page - 1) * $limit;

        // Get total count for pagination
        $total_records = $this->attendance->countAll();
        $total_pages = ceil($total_records / $limit);

        // Get attendance records with pagination
        $stmt = $this->attendance->getAllWithPagination($limit, $offset);
        $attendances = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Get all services for filter
        $stmt = $this->service->getAll();
        $services = $stmt->fetchAll(PDO::FETCH_OBJ);

        // Set page title and active page
        $page_title = getPageTitle('All Attendance Records');
        $active_page = 'attendance';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/attendance/view-all.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Delete attendance record
     *
     * @return void
     */
    public function delete() {
        // Handle AJAX requests
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'])) {
            header('Content-Type: application/json');

            $id = sanitize($_POST['id']);
            $member_id = sanitize($_POST['member_id'] ?? '');

            // Validate ID
            if (empty($id)) {
                echo json_encode(['success' => false, 'message' => 'Attendance ID is required']);
                exit;
            }

            // Get attendance record
            if (!$this->attendance->getById($id)) {
                echo json_encode(['success' => false, 'message' => 'Attendance record not found']);
                exit;
            }

            // Set the ID for deletion
            $this->attendance->id = $id;

            // Delete attendance record
            if ($this->attendance->delete()) {
                echo json_encode(['success' => true, 'message' => 'Attendance record deleted successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to delete attendance record']);
            }
            exit;
        }

        // Handle GET requests (legacy support)
        if (isset($_GET['id']) && !empty($_GET['id'])) {
            $id = sanitize($_GET['id']);
            $member_id = sanitize($_GET['member_id'] ?? '');

            // Get attendance record
            if (!$this->attendance->getById($id)) {
                set_flash_message('Attendance record not found', 'danger');
                if ($member_id) {
                    redirect('attendance/member?id=' . $member_id);
                } else {
                    redirect('attendance');
                }
                exit;
            }

            // Set the ID for deletion
            $this->attendance->id = $id;

            // Delete attendance record
            if ($this->attendance->delete()) {
                set_flash_message('Attendance record deleted successfully', 'success');
            } else {
                set_flash_message('Failed to delete attendance record', 'danger');
            }

            // Redirect back to member page if member_id is provided
            if ($member_id) {
                redirect('attendance/member?id=' . $member_id);
            } else {
                redirect('attendance');
            }
            exit;
        }

        // No ID provided
        set_flash_message('Attendance ID is required', 'danger');
        redirect('attendance');
    }

    /**
     * Get department attendance statistics
     *
     * @param string $date
     * @param int $service_id
     * @return array
     */
    private function getDepartmentAttendanceStats($date, $service_id) {
        // Get all active departments from database
        $database = new Database();
        $conn = $database->getConnection();

        $stmt = $conn->prepare("SELECT name, display_name FROM departments WHERE is_active = 1 ORDER BY sort_order, display_name");
        $stmt->execute();
        $dept_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $departments = [];
        foreach ($dept_data as $dept) {
            $departments[$dept['name']] = $dept['display_name'];
        }

        $stats = [];

        foreach ($departments as $dept_key => $dept_name) {
            // Get members in this department
            $dept_members = $this->member->getByDepartment($dept_key);

            if (empty($dept_members)) {
                continue; // Skip departments with no members
            }

            // Count present, absent, late
            $present = 0;
            $absent = 0;
            $late = 0;

            foreach ($dept_members as $member) {
                $attendance_record = $this->attendance->getByMemberDateService($member->id, $date, $service_id);

                if ($attendance_record) {
                    if ($attendance_record['status'] === 'present') {
                        $present++;
                    } elseif ($attendance_record['status'] === 'absent') {
                        $absent++;
                    } elseif ($attendance_record['status'] === 'late') {
                        $late++;
                    }
                } else {
                    $absent++; // No record means absent
                }
            }

            $total = count($dept_members);
            $attendance_rate = $total > 0 ? round(($present + $late) / $total * 100) : 0;

            $stats[$dept_key] = [
                'name' => $dept_name,
                'total' => $total,
                'present' => $present,
                'absent' => $absent,
                'late' => $late,
                'rate' => $attendance_rate
            ];
        }

        // Sort by attendance rate (highest first)
        uasort($stats, function($a, $b) {
            return $b['rate'] <=> $a['rate'];
        });

        return $stats;
    }

    /**
     * Get attendance trends for a service
     *
     * @param int $service_id
     * @param int $limit
     * @return array
     */
    private function getAttendanceTrends($service_id, $limit = 5) {
        // Get recent attendance dates for this service
        $dates = $this->attendance->getRecentDatesForService($service_id, $limit);

        $trends = [];

        foreach ($dates as $date) {
            $present = count($this->attendance->getPresentMembers($date, $service_id));
            $absent = count($this->attendance->getAbsentMembers($date, $service_id));
            $late = count($this->attendance->getLateMembers($date, $service_id));
            $total = $present + $absent + $late;
            $rate = $total > 0 ? round(($present + $late) / $total * 100) : 0;

            $trends[] = [
                'date' => $date,
                'formatted_date' => format_date($date),
                'present' => $present,
                'absent' => $absent,
                'late' => $late,
                'total' => $total,
                'rate' => $rate
            ];
        }

        return $trends;
    }

    /**
     * Get recent services with attendance statistics
     *
     * @param int $limit Number of recent services to retrieve
     * @return array
     */
    private function getRecentServices($limit = 5) {
        // Get recent services with attendance data
        $services = $this->service->getRecentWithAttendance($limit);
        $result = [];

        if (!$services) {
            return $result;
        }

        foreach ($services as $service) {
            // Get attendance counts for this service
            $present = $this->attendance->countMembersByStatus($service['date'], $service['service_id'], 'present');
            $absent = $this->attendance->countMembersByStatus($service['date'], $service['service_id'], 'absent');
            $late = $this->attendance->countMembersByStatus($service['date'], $service['service_id'], 'late');

            // Calculate total
            $total = $present + $absent + $late;

            // Add to result array
            $result[] = [
                'id' => $service['service_id'],
                'name' => $service['name'],
                'date' => $service['date'],
                'day_of_week' => $service['day_of_week'],
                'time' => $service['time'],
                'total' => $total,
                'present' => $present,
                'absent' => $absent,
                'late' => $late
            ];
        }

        return $result;
    }

    /**
     * Get member attendance records with filters and pagination
     *
     * @param int $member_id
     * @param int $limit
     * @param int $offset
     * @param string $service_filter
     * @param string $status_filter
     * @param string $date_from
     * @param string $date_to
     * @return array
     */
    private function getMemberAttendanceRecords($member_id, $limit = 20, $offset = 0, $service_filter = '', $status_filter = '', $date_from = '', $date_to = '') {
        $conn = $this->database->getConnection();

        // Build the query
        $query = "SELECT a.*, s.name as service_name, s.day_of_week, s.time
                  FROM attendance a
                  LEFT JOIN services s ON a.service_id = s.id
                  WHERE a.member_id = :member_id";

        $params = [':member_id' => $member_id];

        // Add filters
        if (!empty($service_filter)) {
            $query .= " AND a.service_id = :service_id";
            $params[':service_id'] = $service_filter;
        }

        if (!empty($status_filter)) {
            $query .= " AND a.status = :status";
            $params[':status'] = $status_filter;
        }

        if (!empty($date_from)) {
            $query .= " AND a.attendance_date >= :date_from";
            $params[':date_from'] = $date_from;
        }

        if (!empty($date_to)) {
            $query .= " AND a.attendance_date <= :date_to";
            $params[':date_to'] = $date_to;
        }

        // Add tenant filter if applicable
        if (isset($_SESSION['tenant_id'])) {
            $query .= " AND a.tenant_id = :tenant_id";
            $params[':tenant_id'] = $_SESSION['tenant_id'];
        }

        $query .= " ORDER BY a.attendance_date DESC, s.time DESC";

        // Add pagination
        if ($limit > 0) {
            $query .= " LIMIT :limit OFFSET :offset";
        }

        $stmt = $conn->prepare($query);

        // Bind parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        if ($limit > 0) {
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Count member attendance records with filters
     *
     * @param int $member_id
     * @param string $service_filter
     * @param string $status_filter
     * @param string $date_from
     * @param string $date_to
     * @return int
     */
    private function countMemberAttendanceRecords($member_id, $service_filter = '', $status_filter = '', $date_from = '', $date_to = '') {
        $conn = $this->database->getConnection();

        // Build the query
        $query = "SELECT COUNT(*) as count
                  FROM attendance a
                  WHERE a.member_id = :member_id";

        $params = [':member_id' => $member_id];

        // Add filters
        if (!empty($service_filter)) {
            $query .= " AND a.service_id = :service_id";
            $params[':service_id'] = $service_filter;
        }

        if (!empty($status_filter)) {
            $query .= " AND a.status = :status";
            $params[':status'] = $status_filter;
        }

        if (!empty($date_from)) {
            $query .= " AND a.attendance_date >= :date_from";
            $params[':date_from'] = $date_from;
        }

        if (!empty($date_to)) {
            $query .= " AND a.attendance_date <= :date_to";
            $params[':date_to'] = $date_to;
        }

        // Add tenant filter if applicable
        if (isset($_SESSION['tenant_id'])) {
            $query .= " AND a.tenant_id = :tenant_id";
            $params[':tenant_id'] = $_SESSION['tenant_id'];
        }

        $stmt = $conn->prepare($query);

        // Bind parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['count'];
    }

    /**
     * Get member attendance statistics
     *
     * @param int $member_id
     * @return array
     */
    private function getMemberAttendanceStats($member_id) {
        $conn = $this->database->getConnection();

        // Get overall statistics
        $query = "SELECT
                    COUNT(*) as total_records,
                    SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,
                    SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count
                  FROM attendance
                  WHERE member_id = :member_id";

        $params = [':member_id' => $member_id];

        // Add tenant filter if applicable
        if (isset($_SESSION['tenant_id'])) {
            $query .= " AND tenant_id = :tenant_id";
            $params[':tenant_id'] = $_SESSION['tenant_id'];
        }

        $stmt = $conn->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $overall = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get this month's statistics
        $query = "SELECT
                    COUNT(*) as total_records,
                    SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,
                    SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count
                  FROM attendance
                  WHERE member_id = :member_id
                  AND YEAR(attendance_date) = YEAR(CURDATE())
                  AND MONTH(attendance_date) = MONTH(CURDATE())";

        $params = [':member_id' => $member_id];

        // Add tenant filter if applicable
        if (isset($_SESSION['tenant_id'])) {
            $query .= " AND tenant_id = :tenant_id";
            $params[':tenant_id'] = $_SESSION['tenant_id'];
        }

        $stmt = $conn->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $this_month = $stmt->fetch(PDO::FETCH_ASSOC);

        // Calculate attendance rates
        $overall_rate = $overall['total_records'] > 0 ?
            round(($overall['present_count'] + $overall['late_count']) / $overall['total_records'] * 100, 1) : 0;

        $monthly_rate = $this_month['total_records'] > 0 ?
            round(($this_month['present_count'] + $this_month['late_count']) / $this_month['total_records'] * 100, 1) : 0;

        return [
            'overall' => [
                'total' => (int)$overall['total_records'],
                'present' => (int)$overall['present_count'],
                'absent' => (int)$overall['absent_count'],
                'late' => (int)$overall['late_count'],
                'rate' => $overall_rate
            ],
            'this_month' => [
                'total' => (int)$this_month['total_records'],
                'present' => (int)$this_month['present_count'],
                'absent' => (int)$this_month['absent_count'],
                'late' => (int)$this_month['late_count'],
                'rate' => $monthly_rate
            ]
        ];
    }

    /**
     * QR Code Attendance - Main dashboard
     *
     * @return void
     */
    public function qrIndex() {
        try {
            // Get all services
            $services = [];
            try {
                $stmt = $this->service->getAll();
                $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (Exception $e) {
                error_log("Error fetching services: " . $e->getMessage());
                $services = [];
            }

            // Get recent QR sessions with enhanced data
            $recent_sessions = [];
            try {
                require_once 'models/AttendanceQrSession.php';
                $qr_session_model = new AttendanceQrSession($this->database->getConnection());
                $recent_sessions = $qr_session_model->getRecentSessionsWithStats(10);
            } catch (Exception $e) {
                error_log("Error fetching recent sessions: " . $e->getMessage());
                $recent_sessions = [];
            }

            // Get most recent service data for analytics (same as manual system)
            $most_recent_service = $this->attendance->getMostRecentService();
            $most_recent_date = $most_recent_service['attendance_date'] ?? date('Y-m-d');
            $most_recent_service_id = $most_recent_service['service_id'] ?? 1;

            // Get service details
            $this->service->id = $most_recent_service_id;
            $service = $this->service->readOne();

            // Get member counts by attendance status
            $present_count = $this->attendance->countMembersByStatus($most_recent_date, $most_recent_service_id, 'present');
            $absent_count = $this->attendance->countMembersByStatus($most_recent_date, $most_recent_service_id, 'absent');
            $late_count = $this->attendance->countMembersByStatus($most_recent_date, $most_recent_service_id, 'late');

            // Get gender-based attendance statistics
            $gender_stats = $this->attendance->getGenderStats($most_recent_date, $most_recent_service_id);

            // Get limited members by attendance status (for display)
            $present_members = $this->attendance->getPresentMembers($most_recent_date, $most_recent_service_id, 5);
            $absent_members = $this->attendance->getAbsentMembers($most_recent_date, $most_recent_service_id, 5);
            $late_members = $this->attendance->getLateMembers($most_recent_date, $most_recent_service_id, 5);

            // Get all active members for comparison
            $all_active_members = $this->member->getByStatus('active');
            $total_active_members = count($all_active_members);

            // Calculate statistics
            $total_marked = $present_count + $absent_count + $late_count;
            $attendance_rate = $total_marked > 0 ? round(($present_count + $late_count) / $total_marked * 100) : 0;
            $coverage_rate = $total_active_members > 0 ? round($total_marked / $total_active_members * 100) : 0;

            // Get department statistics
            $department_stats = $this->getDepartmentAttendanceStats($most_recent_date, $most_recent_service_id);

            // Get recent attendance trends
            $attendance_trends = $this->getAttendanceTrends($most_recent_service_id, 5); // Last 5 services

            // Get recent services for the activity timeline
            $recent_services = $this->getRecentServices(5); // Last 5 services

            // For the summary section
            $date = $most_recent_date;
            $service_id = $most_recent_service_id;

            // Set page title and active page
            $page_title = getPageTitle('QR Attendance Dashboard');
            $active_page = 'attendance';

            // Start output buffering
            ob_start();

            // Load enhanced QR view with service management and analytics
            require_once 'views/attendance/qr-enhanced-dashboard.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("QR Index Error: " . $e->getMessage());

            // Show error page
            $page_title = 'Error - QR Code Attendance';
            $active_page = 'attendance';
            $error_message = 'Unable to load QR attendance system. Please try again later.';

            ob_start();
            echo '<div class="container mx-auto px-4 py-8">';
            echo '<div class="bg-red-50 border border-red-200 rounded-md p-4">';
            echo '<div class="flex">';
            echo '<div class="flex-shrink-0">';
            echo '<i class="fas fa-exclamation-circle text-red-400"></i>';
            echo '</div>';
            echo '<div class="ml-3">';
            echo '<h3 class="text-sm font-medium text-red-800">Error</h3>';
            echo '<div class="mt-2 text-sm text-red-700">';
            echo '<p>' . htmlspecialchars($error_message) . '</p>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            $content = ob_get_clean();

            include 'views/layouts/main.php';
        }
    }

    /**
     * Generate QR Code for service
     *
     * @return void
     */
    public function qrGenerate() {
        try {
            // Set content type
            header('Content-Type: text/html; charset=UTF-8');

            // Check if POST request
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                set_flash_message('Invalid request method', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Check required fields
            if (!isset($_POST['service_id']) || empty($_POST['service_id']) ||
                !isset($_POST['attendance_date']) || empty($_POST['attendance_date']) ||
                !isset($_POST['duration']) || empty($_POST['duration'])) {
                set_flash_message('All fields are required', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Sanitize inputs
            $service_id = sanitize($_POST['service_id']);
            $attendance_date = sanitize($_POST['attendance_date']);
            $duration = sanitize($_POST['duration']); // Duration in minutes

            // Validate inputs
            if (!is_numeric($service_id) || !is_numeric($duration)) {
                set_flash_message('Invalid input values', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Validate date
            if (!preg_match("/^\d{4}-\d{2}-\d{2}$/", $attendance_date)) {
                set_flash_message('Invalid date format', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Validate service exists
            try {
                if (!$this->service->getById($service_id)) {
                    set_flash_message('Selected service does not exist', 'danger');
                    redirect('attendance/qr');
                    exit;
                }
            } catch (Exception $e) {
                error_log("Service validation error: " . $e->getMessage());
                set_flash_message('Error validating service', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Load QR session model
            require_once 'models/AttendanceQrSession.php';
            $qr_session = new AttendanceQrSession($this->database->getConnection());

            // Generate secure unique token
            $token = $this->generateSecureToken();

            // Set QR session properties
            $qr_session->service_id = $service_id;
            $qr_session->attendance_date = $attendance_date;
            $qr_session->token = $token;
            $qr_session->created_by = $_SESSION['user_id'] ?? 1;
            $qr_session->created_at = date('Y-m-d H:i:s');
            $qr_session->expires_at = date('Y-m-d H:i:s', strtotime("+{$duration} minutes"));
            $qr_session->status = 'active';

            // Create QR session
            if ($qr_session->create()) {
                // Store session details in PHP session for quick access
                $_SESSION['qr_session'] = [
                    'id' => $qr_session->id,
                    'service_id' => $service_id,
                    'attendance_date' => $attendance_date,
                    'token' => $token,
                    'expires_at' => $qr_session->expires_at
                ];

                set_flash_message('QR code generated successfully!', 'success');

                // Redirect to QR display page
                redirect('attendance/qr-display?token=' . $token);
            } else {
                set_flash_message('Failed to create QR session. Please try again.', 'danger');
                redirect('attendance/qr');
            }

        } catch (Exception $e) {
            error_log("QR Generate Error: " . $e->getMessage());
            set_flash_message('An unexpected error occurred. Please try again.', 'danger');
            redirect('attendance/qr');
        }
    }

    /**
     * Display QR Code for attendance
     *
     * @return void
     */
    public function qrDisplay() {
        try {
            // Security: Rate limiting
            if (!$this->checkRateLimit('qr_display', 10, 60)) { // 10 requests per minute
                set_flash_message('Too many requests. Please wait before trying again.', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Check if token is provided
            if (!isset($_GET['token']) || empty($_GET['token'])) {
                set_flash_message('QR session token is required', 'danger');
                redirect('attendance/qr');
                exit;
            }

            $token = sanitize($_GET['token']);

            // Security: Validate token format
            if (!$this->isValidTokenFormat($token)) {
                $this->logSecurityEvent('invalid_token_format', ['token_length' => strlen($token)]);
                set_flash_message('Invalid token format', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Load QR session model
            require_once 'models/AttendanceQrSession.php';

            $db_connection = $this->database->getConnection();
            $qr_session = new AttendanceQrSession($db_connection);

            // Get QR session by token
            if (!$qr_session->getByToken($token)) {
                set_flash_message('QR session not found or has been deleted', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Check if session is active
            if ($qr_session->status !== 'active') {
                set_flash_message('This QR session is no longer active', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Check if session has expired
            if (strtotime($qr_session->expires_at) < time()) {
                // Update status to expired
                try {
                    $qr_session->status = 'expired';
                    $qr_session->updateStatus();
                } catch (Exception $e) {
                    error_log("Error updating QR session status: " . $e->getMessage());
                }

                set_flash_message('This QR session has expired', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Get service details with fallback
            $service = ['id' => $qr_session->service_id, 'name' => 'Service', 'description' => '', 'day_of_week' => '', 'time' => ''];
            try {
                if ($this->service->getById($qr_session->service_id)) {
                    $service = [
                        'id' => $this->service->id,
                        'name' => $this->service->name ?? 'Service',
                        'description' => $this->service->description ?? '',
                        'day_of_week' => $this->service->day_of_week ?? '',
                        'time' => $this->service->time ?? ''
                    ];
                }
            } catch (Exception $service_error) {
                error_log("Service retrieval error: " . $service_error->getMessage());
            }

            // Generate QR URLs with full domain (QR services need full URLs)
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

            // For testing: Use local IP address if on localhost
            if ($host === 'localhost' || strpos($host, '127.0.0.1') !== false) {
                // Get local IP address for mobile testing
                $local_ip = $this->getLocalIPAddress();
                if ($local_ip) {
                    $host = $local_ip;
                }
            }

            $full_base_url = $protocol . '://' . $host . BASE_URL;
            $qr_url = $full_base_url . 'attendance/qr-scan?token=' . $token;
            $qr_image_url = 'https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=' . urlencode($qr_url);

            // Get attendance stats with fallback
            $present_members = [];
            $late_members = [];
            try {
                $present_members = $this->attendance->getPresentMembers($qr_session->attendance_date, $qr_session->service_id);
                $late_members = $this->attendance->getLateMembers($qr_session->attendance_date, $qr_session->service_id);
            } catch (Exception $stats_error) {
                error_log("Attendance stats error: " . $stats_error->getMessage());
            }

            // Set page variables
            $page_title = 'QR Code for Attendance - ICGC Emmanuel Temple';
            $active_page = 'attendance';

            // Store session in PHP session
            $_SESSION['qr_session'] = [
                'id' => $qr_session->id,
                'service_id' => $qr_session->service_id,
                'attendance_date' => $qr_session->attendance_date,
                'token' => $qr_session->token,
                'expires_at' => $qr_session->expires_at
            ];

            // Security: Disable debug mode in production
            if (isset($_GET['debug']) && APP_ENV !== 'development') {
                set_flash_message('Debug mode not available', 'danger');
                redirect('attendance/qr');
                exit;
            }

            // Security: Add security headers
            header('X-Frame-Options: SAMEORIGIN');
            header('X-Content-Type-Options: nosniff');
            header('Referrer-Policy: strict-origin-when-cross-origin');

            // Render the view with layout
            ob_start();
            require_once 'views/attendance/qr-display-clean.php';
            $content = ob_get_clean();
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("QR Display Error: " . $e->getMessage());

            // Render a simple fallback page
            $this->renderSimpleFallback($token ?? 'unknown');
        }
    }

    /**
     * Simple fallback renderer for QR display errors
     */
    private function renderSimpleFallback($token) {
        // Only set headers if they haven't been sent yet
        if (!headers_sent()) {
            header('Content-Type: text/html; charset=UTF-8');

            // Show secure error page
            echo '<!DOCTYPE html>';
            echo '<html><head><title>QR Code Error</title>';
            echo '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
            echo '<style>body{font-family:Arial,sans-serif;text-align:center;padding:50px;background:#f5f5f5;}</style>';
            echo '</head><body>';
            echo '<div style="max-width:500px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);">';
            echo '<h1 style="color:#e74c3c;">QR Code Error</h1>';
            echo '<p>Unable to load QR code display. Please try again.</p>';
            echo '<a href="' . BASE_URL . 'attendance/qr" style="color:#3F7D58;text-decoration:none;background:#3F7D58;color:white;padding:10px 20px;border-radius:5px;display:inline-block;">← Back to QR Attendance</a>';
            echo '</div></body></html>';
            exit;
        } else {
            // Headers already sent, just show error message within existing layout
            set_flash_message('Unable to load QR code display. Please try again.', 'danger');
            redirect('attendance/qr');
        }
    }

    /**
     * Show QR code scanning page for members
     * Token authentication is handled by the Router
     *
     * @return void
     */


    public function qrScan() {
        try {
            // Get validated QR session from router (token already validated)
            $qr_session_data = $_SESSION['validated_qr_session'] ?? null;
            $token = isset($_GET['token']) ? sanitize($_GET['token']) : null;
            $fallback_mode = isset($_GET['fallback']) && $_GET['fallback'] === 'true';

            if (!$qr_session_data || !$token) {
                set_flash_message('Invalid QR code session', 'danger');
                redirect('attendance/qr');
                return;
            }

            // If we're in fallback mode, render a simplified scan page
            if ($fallback_mode) {
                $this->renderFallbackQRScan($token);
                return;
            }

            try {
                // Create QR session object from validated data for compatibility
                $qr_session = (object) $qr_session_data;

                // Store session details in PHP session for quick access (legacy compatibility)
                $_SESSION['qr_session'] = $qr_session_data;

                // Get service details
                $service = [];
                if ($this->service->getById($qr_session->service_id)) {
                    $service = [
                        'id' => $this->service->id,
                        'name' => $this->service->name,
                        'description' => $this->service->description,
                        'day_of_week' => $this->service->day_of_week,
                        'time' => $this->service->time
                    ];
                }

                // Set page title
                $page_title = 'Mark Your Attendance - ICGC Emmanuel Temple';
                $active_page = 'attendance';

                // Start output buffering
                ob_start();

                // Load view
                require_once 'views/attendance/qr-scan.php';

                // Get the contents of the output buffer
                $content = ob_get_clean();

                // Include the layout template
                include 'views/layouts/main.php';
                
            } catch (PDOException $e) {
                // Database connection error - render the fallback page
                error_log("QR Scan Database Error: " . $e->getMessage());
                $this->renderFallbackQRScan($token);
            }
        } catch (Exception $e) {
            error_log("QR Scan Error: " . $e->getMessage());
            set_flash_message('An error occurred. Please try again.', 'danger');
            redirect('attendance/qr');
        }
    }
    
    /**
     * Renders a simplified QR scan page when database connection fails
     *
     * @param string $token The QR session token
     * @return void
     */
    private function renderFallbackQRScan($token) {
        // Output simple HTML directly
        header('Content-Type: text/html; charset=UTF-8');
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Mark Attendance - ICGC Emmanuel Temple</title>
            <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
        </head>
        <body class="bg-gray-50">
            <div class="min-h-screen py-8">
                <div class="container max-w-md mx-auto px-4">
                    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
                        <div class="bg-blue-600 px-6 py-4">
                            <h1 class="text-xl font-semibold text-white text-center">Mark Your Attendance (Offline Mode)</h1>
                        </div>
                        
                        <div class="p-6">
                            <div class="text-center mb-6">
                                <h2 class="text-lg font-semibold text-gray-800">Attendance for Today</h2>
                                <p class="text-sm text-gray-600"><?php echo date('F j, Y'); ?></p>
                                
                                <div class="p-4 mb-4 text-sm text-blue-700 bg-blue-100 rounded-lg mt-4" role="alert">
                                    <span class="font-medium">Database connection issue:</span> Using offline mode for attendance.
                                </div>
                            </div>
                            
                            <div class="p-4 mb-4 text-sm text-yellow-700 bg-yellow-100 rounded-lg" role="alert">
                                <span class="font-medium">Note:</span> Your attendance will be recorded manually by the administrator later.
                            </div>
                            
                            <!-- Offline Form -->
                            <form id="offline-form" class="space-y-4">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Your Name</label>
                                    <input type="text" id="name" class="block w-full px-4 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="Enter your full name">
                                </div>
                                
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number (Optional)</label>
                                    <input type="tel" id="phone" class="block w-full px-4 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="Enter your phone number">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Attendance Status</label>
                                    <div class="flex space-x-4">
                                        <div class="flex items-center">
                                            <input id="present" name="status" type="radio" checked class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <label for="present" class="ml-2 block text-sm text-gray-700">Present</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input id="late" name="status" type="radio" class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <label for="late" class="ml-2 block text-sm text-gray-700">Late</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <button type="button" onclick="submitOfflineAttendance()" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        Mark Attendance
                                    </button>
                                </div>
                            </form>
                            
                            <!-- Success message (initially hidden) -->
                            <div id="success-message" class="hidden mt-4 p-4 text-sm text-green-700 bg-green-100 rounded-lg" role="alert">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle text-green-500"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="font-medium">Thank you for marking your attendance!</p>
                                        <p class="mt-1">Your attendance has been noted and will be recorded when the system is back online.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center text-gray-500 text-xs">
                        <p>ICGC Emmanuel Temple - Church Management System</p>
                    </div>
                </div>
            </div>

            <script>
                function submitOfflineAttendance() {
                    const name = document.getElementById('name').value.trim();
                    if (!name) {
                        alert('Please enter your name');
                        return;
                    }
                    
                    // Hide the form and show success message
                    document.getElementById('offline-form').classList.add('hidden');
                    document.getElementById('success-message').classList.remove('hidden');
                    
                    // In a real implementation, we could store this in localStorage 
                    // to sync later when connection is restored
                    const attendanceData = {
                        name: name,
                        phone: document.getElementById('phone').value.trim(),
                        status: document.querySelector('input[name="status"]:checked').id,
                        timestamp: new Date().toISOString(),
                        token: '<?php echo $token; ?>'
                    };
                    
                    console.log('Offline attendance recorded:', attendanceData);
                    
                    // Optional: Store in localStorage for later sync
                    try {
                        const offlineAttendance = JSON.parse(localStorage.getItem('offlineAttendance') || '[]');
                        offlineAttendance.push(attendanceData);
                        localStorage.setItem('offlineAttendance', JSON.stringify(offlineAttendance));
                    } catch (e) {
                        console.error('Error storing attendance data locally', e);
                    }
                }
            </script>
        </body>
        </html>
        <?php
        exit;
    }

    /**
     * Get family members for QR attendance
     *
     * @return void
     */
    public function qrGetFamily() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $member_identifier = isset($_POST['member_identifier']) ? sanitize($_POST['member_identifier']) : null;

            if (empty($member_identifier)) {
                echo json_encode(['success' => false, 'error' => 'Member identifier required']);
                return;
            }

            try {
                // Find the member
                $member = $this->findMemberByIdentifier($member_identifier);

                if (!$member) {
                    echo json_encode(['success' => false, 'error' => 'Member not found']);
                    return;
                }

                // Load family relationship model
                require_once 'models/FamilyRelationship.php';
                $familyRelationship = new FamilyRelationship($this->database->getConnection());

                // Get children for this parent
                $children_stmt = $familyRelationship->getChildrenByParent($member['id']);
                $children = $children_stmt->fetchAll(PDO::FETCH_ASSOC);

                // Filter children to only include those under max age
                require_once 'models/ChildrenMinistry.php';
                $childrenMinistry = new ChildrenMinistry($this->database->getConnection());
                $max_child_age = $childrenMinistry->getMaxChildAge();

                $eligible_children = array_filter($children, function($child) use ($max_child_age) {
                    return $child['age'] <= $max_child_age && $child['member_status'] === 'active';
                });

                // Prepare response
                $response = [
                    'success' => true,
                    'parent' => [
                        'id' => $member['id'],
                        'name' => $member['first_name'] . ' ' . $member['last_name'],
                        'phone' => $member['phone_number'] ?? '',
                        'department' => $member['department'] ?? ''
                    ],
                    'children' => array_values($eligible_children),
                    'has_children' => count($eligible_children) > 0
                ];

                echo json_encode($response);
                return;

            } catch (Exception $e) {
                error_log("QR Get Family Error: " . $e->getMessage());
                echo json_encode(['success' => false, 'error' => 'Failed to load family information']);
                return;
            }
        }

        echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    }

    /**
     * Process family attendance marking via QR
     *
     * @return void
     */
    public function qrMarkFamily() {
        header('Content-Type: application/json');
        error_log("QR Mark Family called - Method: " . $_SERVER['REQUEST_METHOD']);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = isset($_POST['token']) ? sanitize($_POST['token']) : null;
            $parent_id = isset($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
            $selected_members = isset($_POST['selected_members']) ? $_POST['selected_members'] : [];
            $status = isset($_POST['status']) ? sanitize($_POST['status']) : 'present';

            error_log("🔥🔥🔥 DEEP DEBUG: QR FAMILY MARK START 🔥🔥🔥");
            error_log("📥 POST Data Received:");
            error_log("  - Token: $token");
            error_log("  - Parent ID: $parent_id");
            error_log("  - Status: $status");
            error_log("  - Selected Members: " . print_r($selected_members, true));
            error_log("  - Raw POST: " . print_r($_POST, true));

            // Get validated session data from router
            $qr_session = $_SESSION['validated_qr_session'] ?? null;
            error_log("🔐 Session Validation:");
            error_log("  - Session exists: " . (isset($_SESSION['validated_qr_session']) ? 'YES' : 'NO'));
            error_log("  - Session data: " . print_r($qr_session, true));

            // Deep validation debugging
            error_log("🔍 Validation Checks:");
            if (!$qr_session) {
                error_log("❌ VALIDATION FAILED: No QR session found");
                echo json_encode(['success' => false, 'error' => 'Invalid or expired session']);
                return;
            }

            if ($qr_session['token'] !== $token) {
                error_log("❌ VALIDATION FAILED: Token mismatch");
                error_log("  - Expected: {$qr_session['token']}");
                error_log("  - Received: $token");
                echo json_encode(['success' => false, 'error' => 'Invalid or expired session']);
                return;
            }

            if (empty($parent_id)) {
                error_log("❌ VALIDATION FAILED: No parent ID provided");
                echo json_encode(['success' => false, 'error' => 'Parent ID required']);
                return;
            }

            if (empty($selected_members)) {
                error_log("❌ VALIDATION FAILED: No members selected");
                echo json_encode(['success' => false, 'error' => 'Please select family members to mark attendance']);
                return;
            }

            error_log("✅ All validations passed - proceeding with attendance marking");

            try {
                error_log("🚀 Starting database transaction");
                $this->database->getConnection()->beginTransaction();

                $marked_count = 0;
                $children_checked_in = 0;
                $security_codes = [];

                // REWRITTEN: Complete QR attendance logic with proper age-based routing
                error_log("🔥🔥🔥 QR FAMILY ATTENDANCE PROCESSING START 🔥🔥🔥");

                // Load children ministry model to get consistent age threshold
                require_once 'models/ChildrenMinistry.php';
                $childrenMinistry = new ChildrenMinistry($this->database->getConnection());
                $max_child_age = $childrenMinistry->getMaxChildAge();

                error_log("⚙️ Configuration:");
                error_log("  - Max Child Age Setting: $max_child_age");
                error_log("  - QR Session Service ID: {$qr_session['service_id']}");
                error_log("  - QR Session Date: {$qr_session['attendance_date']}");
                error_log("  - Parent ID: $parent_id");
                error_log("  - Selected Members Count: " . count($selected_members));

                // Process each selected member with DEEP debugging
                error_log("👥 Processing " . count($selected_members) . " selected members:");

                foreach ($selected_members as $index => $member_id) {
                    $member_id = (int)$member_id;

                    error_log("🔄 Processing member " . ($index + 1) . "/" . count($selected_members) . " - ID: $member_id");

                    // Get member details with enhanced query
                    $member = $this->getEnhancedMemberDetails($member_id);
                    if (!$member) {
                        error_log("❌ CRITICAL ERROR: Member ID $member_id not found in database");
                        continue;
                    }

                    // Calculate age with enhanced debugging
                    $age = $this->calculateAgeEnhanced($member['date_of_birth']);

                    error_log("👤 Member Details:");
                    error_log("  - ID: {$member['id']}");
                    error_log("  - Name: {$member['first_name']} {$member['last_name']}");
                    error_log("  - Date of Birth: {$member['date_of_birth']}");
                    error_log("  - Calculated Age: $age");
                    error_log("  - Database Age: {$member['calculated_age']}");
                    error_log("  - Max Child Age: $max_child_age");
                    error_log("  - Is Child (≤$max_child_age): " . ($age <= $max_child_age ? 'YES' : 'NO'));
                    error_log("  - Member Status: {$member['member_status']}");

                    // Route based on age with DEEP debugging
                    error_log("🎯 ROUTING DECISION:");
                    error_log("  - Age: $age");
                    error_log("  - Max Child Age: $max_child_age");
                    error_log("  - Condition: $age <= $max_child_age");
                    error_log("  - Result: " . ($age <= $max_child_age ? 'CHILD' : 'ADULT'));

                    if ($age <= $max_child_age) {
                        // CHILD ROUTE: Save to children_checkin_log
                        error_log("🧒 >>> ROUTING TO CHILDREN MINISTRY <<<");
                        error_log("  - Target Table: children_checkin_log");
                        error_log("  - Will appear in: Children Ministry Dashboard");

                        $child_result = $this->processChildAttendance($member, $qr_session, $parent_id);
                        if ($child_result['success']) {
                            $children_checked_in++;
                            $marked_count++;
                            $security_codes[] = $child_result['security_code'];
                            error_log("✅ CHILD SUCCESS: Saved to children_checkin_log table");
                            error_log("  - Record ID: {$child_result['record_id']}");
                            error_log("  - Security Code: {$child_result['security_code']['code']}");
                        } else {
                            error_log("❌ CHILD FAILED: " . $child_result['error']);
                        }

                    } else {
                        // ADULT ROUTE: Save to attendance table
                        error_log("👨 >>> ROUTING TO MAIN ATTENDANCE <<<");
                        error_log("  - Target Table: attendance");
                        error_log("  - Will appear in: Main Attendance Page");

                        $adult_result = $this->processAdultAttendance($member, $qr_session, $status);
                        if ($adult_result['success']) {
                            $marked_count++;
                            error_log("✅ ADULT SUCCESS: Saved to attendance table");
                            error_log("  - Affected Rows: {$adult_result['affected_rows']}");
                        } else {
                            error_log("❌ ADULT FAILED: " . $adult_result['error']);
                        }
                    }

                    error_log("📊 Current Totals:");
                    error_log("  - Total Marked: $marked_count");
                    error_log("  - Children Checked In: $children_checked_in");
                    error_log("  - Security Codes Generated: " . count($security_codes));
                    error_log("--- End processing member {$member['id']} ---");
                }

                error_log("🔥🔥🔥 QR FAMILY ATTENDANCE PROCESSING END 🔥🔥🔥");

                // VERIFICATION: Check what was actually saved
                error_log("🔍 Starting verification of saved records...");
                $this->verifyAttendanceRecords($selected_members, $qr_session);

                error_log("💾 Committing database transaction...");
                $this->database->getConnection()->commit();
                error_log("✅ Transaction committed successfully");

                $response = [
                    'success' => true,
                    'message' => "Attendance marked for {$marked_count} family member(s)",
                    'marked_count' => $marked_count,
                    'children_checked_in' => $children_checked_in,
                    'security_codes' => $security_codes
                ];

                error_log("📤 Final Response:");
                error_log("  - Success: true");
                error_log("  - Total Marked: $marked_count");
                error_log("  - Children Checked In: $children_checked_in");
                error_log("  - Security Codes: " . count($security_codes));
                error_log("  - Response: " . json_encode($response));

                echo json_encode($response);
                return;

            } catch (Exception $e) {
                error_log("💥 CRITICAL ERROR in QR Family Mark:");
                error_log("  - Error Message: " . $e->getMessage());
                error_log("  - Error File: " . $e->getFile());
                error_log("  - Error Line: " . $e->getLine());
                error_log("  - Stack Trace: " . $e->getTraceAsString());

                error_log("🔄 Rolling back database transaction...");
                $this->database->getConnection()->rollBack();
                error_log("✅ Transaction rolled back");

                $error_response = [
                    'success' => false,
                    'error' => 'Failed to mark family attendance: ' . $e->getMessage(),
                    'debug_info' => [
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'selected_members' => $selected_members,
                        'parent_id' => $parent_id
                    ]
                ];

                error_log("📤 Error Response: " . json_encode($error_response));
                echo json_encode($error_response);
                return;
            }
        }

        echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    }

    /**
     * Get enhanced member details for QR processing
     *
     * @param int $member_id
     * @return array|null
     */
    private function getEnhancedMemberDetails($member_id) {
        try {
            error_log("🔍 Getting enhanced member details for ID: $member_id");
            $conn = $this->database->getConnection();

            $sql = "SELECT id, first_name, last_name, date_of_birth, member_status,
                           TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) as calculated_age
                    FROM members
                    WHERE id = ? AND member_status = 'active'";

            error_log("📝 SQL Query: $sql");
            error_log("📝 Parameters: [" . $member_id . "]");

            $stmt = $conn->prepare($sql);
            $stmt->execute([$member_id]);
            $member = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($member) {
                error_log("✅ Member found in database:");
                error_log("  - ID: {$member['id']}");
                error_log("  - Name: {$member['first_name']} {$member['last_name']}");
                error_log("  - DOB: {$member['date_of_birth']}");
                error_log("  - Status: {$member['member_status']}");
                error_log("  - DB Calculated Age: {$member['calculated_age']}");
            } else {
                error_log("❌ Member NOT found in database for ID: $member_id");

                // Check if member exists but is inactive
                $check_sql = "SELECT id, first_name, last_name, member_status FROM members WHERE id = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->execute([$member_id]);
                $inactive_member = $check_stmt->fetch(PDO::FETCH_ASSOC);

                if ($inactive_member) {
                    error_log("⚠️ Member exists but status is: {$inactive_member['member_status']}");
                } else {
                    error_log("❌ Member ID $member_id does not exist in database at all");
                }
            }

            return $member;

        } catch (Exception $e) {
            error_log("💥 Error getting enhanced member details: " . $e->getMessage());
            error_log("  - Member ID: $member_id");
            error_log("  - Stack Trace: " . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Calculate age with enhanced debugging
     *
     * @param string $date_of_birth
     * @return int
     */
    private function calculateAgeEnhanced($date_of_birth) {
        try {
            if (empty($date_of_birth)) {
                error_log("Age Calculation: Empty date of birth");
                return 0;
            }

            $birth_date = new DateTime($date_of_birth);
            $today = new DateTime();
            $age = $birth_date->diff($today)->y;

            error_log("Age Calculation: Birth Date = $date_of_birth, Today = " . $today->format('Y-m-d') . ", Age = $age");

            return $age;

        } catch (Exception $e) {
            error_log("Age Calculation Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Process child attendance - REWRITTEN for children_checkin_log table
     *
     * @param array $child
     * @param array $qr_session
     * @param int $parent_id
     * @return array
     */
    private function processChildAttendance($child, $qr_session, $parent_id) {
        try {
            // Use centralized attendance router for consistency
            require_once 'services/AttendanceRouter.php';
            $router = new AttendanceRouter($this->database->getConnection());

            // Prepare attendance data
            $attendance_data = [
                'service_id' => $qr_session['service_id'],
                'attendance_date' => $qr_session['attendance_date'],
                'status' => 'present',
                'qr_session_id' => $qr_session['id'],
                'checked_in_by' => $parent_id // Parent checking in child
            ];

            // Route attendance using centralized service
            $result = $router->routeAttendance($child, $attendance_data, 'qr_family');

            if ($result['success']) {
                return [
                    'success' => true,
                    'security_code' => [
                        'name' => $child['first_name'] . ' ' . $child['last_name'],
                        'code' => $result['security_code'] ?? sprintf("%04d", rand(1000, 9999))
                    ],
                    'table' => $result['table'],
                    'classification' => $result['classification']
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error']
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process adult attendance - REWRITTEN for attendance table
     *
     * @param array $adult
     * @param array $qr_session
     * @param string $status
     * @return array
     */
    private function processAdultAttendance($adult, $qr_session, $status) {
        try {
            // Use centralized attendance router for consistency
            require_once 'services/AttendanceRouter.php';
            $router = new AttendanceRouter($this->database->getConnection());

            // Prepare attendance data
            $attendance_data = [
                'service_id' => $qr_session['service_id'],
                'attendance_date' => $qr_session['attendance_date'],
                'status' => $status,
                'qr_session_id' => $qr_session['id'],
                'checked_in_by' => $adult['id'] // Self check-in for family QR
            ];

            // Route attendance using centralized service
            $result = $router->routeAttendance($adult, $attendance_data, 'qr_family');

            if ($result['success']) {
                return [
                    'success' => true,
                    'affected_rows' => 1,
                    'table' => $result['table'],
                    'classification' => $result['classification']
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error']
                ];
            }

        } catch (Exception $e) {
            error_log("Adult attendance error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Calculate age from date of birth (legacy method - calls enhanced version)
     *
     * @param string $date_of_birth
     * @return int
     */
    private function calculateAge($date_of_birth) {
        return $this->calculateAgeEnhanced($date_of_birth);
    }

    /**
     * Verify attendance records were saved correctly
     *
     * @param array $member_ids
     * @param array $qr_session
     * @return void
     */
    private function verifyAttendanceRecords($member_ids, $qr_session) {
        try {
            $conn = $this->database->getConnection();

            error_log("=== VERIFICATION: Checking saved attendance records ===");

            foreach ($member_ids as $member_id) {
                $member_id = (int)$member_id;

                // Check children_checkin_log
                $child_sql = "SELECT ccl.*, m.first_name, m.last_name,
                                    TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
                             FROM children_checkin_log ccl
                             JOIN members m ON ccl.child_id = m.id
                             WHERE ccl.child_id = ? AND ccl.attendance_date = ?";
                $child_stmt = $conn->prepare($child_sql);
                $child_stmt->execute([$member_id, $qr_session['attendance_date']]);
                $child_record = $child_stmt->fetch(PDO::FETCH_ASSOC);

                // Check attendance table
                $adult_sql = "SELECT a.*, m.first_name, m.last_name,
                                    TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
                             FROM attendance a
                             JOIN members m ON a.member_id = m.id
                             WHERE a.member_id = ? AND a.attendance_date = ?";
                $adult_stmt = $conn->prepare($adult_sql);
                $adult_stmt->execute([$member_id, $qr_session['attendance_date']]);
                $adult_record = $adult_stmt->fetch(PDO::FETCH_ASSOC);

                if ($child_record) {
                    error_log("✓ FOUND in children_checkin_log: {$child_record['first_name']} {$child_record['last_name']} (Age: {$child_record['age']})");
                } elseif ($adult_record) {
                    error_log("✓ FOUND in attendance: {$adult_record['first_name']} {$adult_record['last_name']} (Age: {$adult_record['age']})");
                } else {
                    error_log("✗ NOT FOUND: Member ID $member_id not found in either table");
                }
            }

            error_log("=== VERIFICATION COMPLETE ===");

        } catch (Exception $e) {
            error_log("VERIFICATION ERROR: " . $e->getMessage());
        }
    }

    /**
     * Debug endpoint to test QR attendance routing
     * URL: /attendance/debug-qr-routing
     */
    public function debugQrRouting() {
        header('Content-Type: application/json');

        try {
            $conn = $this->database->getConnection();

            // Load children ministry model
            require_once 'models/ChildrenMinistry.php';
            $childrenMinistry = new ChildrenMinistry($conn);
            $max_child_age = $childrenMinistry->getMaxChildAge();

            // Get some sample members
            $sql = "SELECT id, first_name, last_name, date_of_birth,
                           TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) as age
                    FROM members
                    WHERE member_status = 'active'
                    ORDER BY date_of_birth DESC
                    LIMIT 10";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $routing_info = [];
            foreach ($members as $member) {
                $routing_info[] = [
                    'id' => $member['id'],
                    'name' => $member['first_name'] . ' ' . $member['last_name'],
                    'date_of_birth' => $member['date_of_birth'],
                    'age' => $member['age'],
                    'max_child_age' => $max_child_age,
                    'is_child' => $member['age'] <= $max_child_age,
                    'route_to' => $member['age'] <= $max_child_age ? 'children_checkin_log' : 'attendance'
                ];
            }

            // Check recent attendance records
            $today = date('Y-m-d');

            $children_sql = "SELECT COUNT(*) as count FROM children_checkin_log WHERE attendance_date = ?";
            $children_stmt = $conn->prepare($children_sql);
            $children_stmt->execute([$today]);
            $children_count = $children_stmt->fetch(PDO::FETCH_ASSOC)['count'];

            $adults_sql = "SELECT COUNT(*) as count FROM attendance WHERE attendance_date = ?";
            $adults_stmt = $conn->prepare($adults_sql);
            $adults_stmt->execute([$today]);
            $adults_count = $adults_stmt->fetch(PDO::FETCH_ASSOC)['count'];

            echo json_encode([
                'success' => true,
                'max_child_age' => $max_child_age,
                'today_attendance' => [
                    'children_checkin_log' => $children_count,
                    'attendance_table' => $adults_count
                ],
                'sample_routing' => $routing_info,
                'message' => 'QR routing debug information'
            ], JSON_PRETTY_PRINT);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Test endpoint to verify debugging is working
     * URL: /attendance/test-debug
     */
    public function testDebug() {
        header('Content-Type: application/json');

        error_log("🧪 TEST DEBUG ENDPOINT CALLED");
        error_log("🔥 This is a test log message with emojis");
        error_log("📊 Testing different log levels");

        echo json_encode([
            'success' => true,
            'message' => 'Debug test completed - check error logs',
            'timestamp' => date('Y-m-d H:i:s'),
            'instructions' => [
                '1. Check your error logs for messages with emojis',
                '2. Look for "TEST DEBUG ENDPOINT CALLED"',
                '3. If you see these messages, debugging is working'
            ]
        ], JSON_PRETTY_PRINT);
    }

    /**
     * Investigate specific member attendance issue
     * URL: /attendance/investigate-member?id=575
     */
    public function investigateMember() {
        header('Content-Type: application/json');

        $member_id = isset($_GET['id']) ? (int)$_GET['id'] : 575; // Default to Daniela

        try {
            error_log("🔍🔍🔍 INVESTIGATING MEMBER ID: $member_id 🔍🔍🔍");

            $conn = $this->database->getConnection();
            $today = date('Y-m-d');

            // Get member details
            $member_sql = "SELECT id, first_name, last_name, date_of_birth, member_status,
                                 TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) as age
                          FROM members WHERE id = ?";
            $member_stmt = $conn->prepare($member_sql);
            $member_stmt->execute([$member_id]);
            $member = $member_stmt->fetch(PDO::FETCH_ASSOC);

            // Get children ministry settings
            require_once 'models/ChildrenMinistry.php';
            $childrenMinistry = new ChildrenMinistry($conn);
            $max_child_age = $childrenMinistry->getMaxChildAge();

            // Check children_checkin_log
            $child_sql = "SELECT * FROM children_checkin_log
                         WHERE child_id = ? AND attendance_date = ?
                         ORDER BY check_in_time DESC";
            $child_stmt = $conn->prepare($child_sql);
            $child_stmt->execute([$member_id, $today]);
            $child_records = $child_stmt->fetchAll(PDO::FETCH_ASSOC);

            // Check attendance table
            $adult_sql = "SELECT * FROM attendance
                         WHERE member_id = ? AND attendance_date = ?
                         ORDER BY created_at DESC";
            $adult_stmt = $conn->prepare($adult_sql);
            $adult_stmt->execute([$member_id, $today]);
            $adult_records = $adult_stmt->fetchAll(PDO::FETCH_ASSOC);

            $investigation = [
                'member_info' => $member,
                'max_child_age' => $max_child_age,
                'should_be_child' => $member['age'] <= $max_child_age,
                'expected_table' => $member['age'] <= $max_child_age ? 'children_checkin_log' : 'attendance',
                'children_records_today' => $child_records,
                'attendance_records_today' => $adult_records,
                'issue_analysis' => [
                    'in_children_table' => count($child_records) > 0,
                    'in_attendance_table' => count($adult_records) > 0,
                    'correct_placement' => ($member['age'] <= $max_child_age && count($child_records) > 0) ||
                                         ($member['age'] > $max_child_age && count($adult_records) > 0),
                    'wrong_placement' => ($member['age'] <= $max_child_age && count($adult_records) > 0) ||
                                       ($member['age'] > $max_child_age && count($child_records) > 0)
                ]
            ];

            error_log("📊 Investigation Results: " . json_encode($investigation, JSON_PRETTY_PRINT));

            echo json_encode($investigation, JSON_PRETTY_PRINT);

        } catch (Exception $e) {
            error_log("💥 Investigation Error: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Fix misplaced attendance records - move children from attendance table to children_checkin_log
     * URL: /attendance/fix-misplaced-records
     */
    public function fixMisplacedRecords() {
        header('Content-Type: application/json');

        try {
            error_log("🔧🔧🔧 FIXING MISPLACED ATTENDANCE RECORDS 🔧🔧🔧");

            $conn = $this->database->getConnection();
            $today = date('Y-m-d');

            // Get children ministry settings
            require_once 'models/ChildrenMinistry.php';
            $childrenMinistry = new ChildrenMinistry($conn);
            $max_child_age = $childrenMinistry->getMaxChildAge();

            // Find children in the wrong table (attendance instead of children_checkin_log)
            $wrong_records_sql = "SELECT a.*, m.first_name, m.last_name, m.date_of_birth,
                                        TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
                                 FROM attendance a
                                 JOIN members m ON a.member_id = m.id
                                 WHERE a.attendance_date = ?
                                 AND TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) <= ?
                                 AND a.marked_via = 'qr'";

            $stmt = $conn->prepare($wrong_records_sql);
            $stmt->execute([$today, $max_child_age]);
            $wrong_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $fixed_count = 0;
            $errors = [];

            $conn->beginTransaction();

            foreach ($wrong_records as $record) {
                try {
                    error_log("🔄 Moving child record: {$record['first_name']} {$record['last_name']} (Age: {$record['age']})");

                    // Check if already exists in children_checkin_log
                    $check_sql = "SELECT id FROM children_checkin_log
                                 WHERE child_id = ? AND attendance_date = ? AND service_id = ?";
                    $check_stmt = $conn->prepare($check_sql);
                    $check_stmt->execute([$record['member_id'], $record['attendance_date'], $record['service_id']]);

                    if ($check_stmt->fetch()) {
                        error_log("⚠️ Record already exists in children_checkin_log, skipping");
                        continue;
                    }

                    // Insert into children_checkin_log
                    $insert_sql = "INSERT INTO children_checkin_log
                                  (child_id, service_id, checked_in_by, check_in_time, attendance_date, notes, security_code, created_at, updated_at)
                                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    $security_code = substr(str_shuffle('0123456789'), 0, 4);
                    $notes = "Moved from attendance table - Originally marked via QR at " . $record['created_at'];

                    $insert_stmt = $conn->prepare($insert_sql);
                    $insert_result = $insert_stmt->execute([
                        $record['member_id'],
                        $record['service_id'],
                        $record['member_id'], // Use self as checked_in_by for individual records
                        $record['created_at'],
                        $record['attendance_date'],
                        $notes,
                        $security_code,
                        $record['created_at'],
                        $record['updated_at']
                    ]);

                    if ($insert_result) {
                        // Delete from attendance table
                        $delete_sql = "DELETE FROM attendance WHERE id = ?";
                        $delete_stmt = $conn->prepare($delete_sql);
                        $delete_stmt->execute([$record['id']]);

                        $fixed_count++;
                        error_log("✅ Successfully moved {$record['first_name']} {$record['last_name']} to children ministry");
                    } else {
                        $errors[] = "Failed to insert {$record['first_name']} {$record['last_name']} into children_checkin_log";
                    }

                } catch (Exception $e) {
                    $errors[] = "Error processing {$record['first_name']} {$record['last_name']}: " . $e->getMessage();
                    error_log("❌ Error: " . $e->getMessage());
                }
            }

            $conn->commit();

            $result = [
                'success' => true,
                'message' => "Fixed $fixed_count misplaced attendance records",
                'fixed_count' => $fixed_count,
                'total_wrong_records' => count($wrong_records),
                'errors' => $errors,
                'details' => array_map(function($record) {
                    return [
                        'name' => $record['first_name'] . ' ' . $record['last_name'],
                        'age' => $record['age'],
                        'original_record_id' => $record['id']
                    ];
                }, $wrong_records)
            ];

            error_log("🔧 Fix Results: " . json_encode($result));
            echo json_encode($result, JSON_PRETTY_PRINT);

        } catch (Exception $e) {
            if (isset($conn)) {
                $conn->rollBack();
            }
            error_log("💥 Fix Error: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Debug children ministry dashboard data
     * URL: /attendance/debug-children-dashboard
     */
    public function debugChildrenDashboard() {
        header('Content-Type: application/json');

        try {
            error_log("🔍🔍🔍 DEBUGGING CHILDREN MINISTRY DASHBOARD 🔍🔍🔍");

            $conn = $this->database->getConnection();
            $today = date('Y-m-d');

            // Check what's in children_checkin_log table today
            $children_sql = "SELECT ccl.*, m.first_name, m.last_name, m.date_of_birth,
                                   TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                                   s.name as service_name
                            FROM children_checkin_log ccl
                            JOIN members m ON ccl.child_id = m.id
                            LEFT JOIN services s ON ccl.service_id = s.id
                            WHERE ccl.attendance_date = ?
                            ORDER BY ccl.check_in_time DESC";

            $stmt = $conn->prepare($children_sql);
            $stmt->execute([$today]);
            $children_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Run the exact same query as children ministry dashboard
            $dashboard_query = "SELECT
                        ccl.*,
                        m.first_name,
                        m.last_name,
                        m.profile_picture,
                        TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                        s.name as service_name,
                        s.time as service_time,
                        ag.name as age_group_name,
                        parent.first_name as parent_first_name,
                        parent.last_name as parent_last_name,
                        CASE
                            WHEN ccl.notes LIKE '%QR Family Attendance%' THEN 'QR Family'
                            WHEN ccl.notes LIKE '%QR family attendance%' THEN 'QR Family'
                            WHEN ccl.notes LIKE '%QR%' THEN 'QR Code'
                            ELSE 'QR System'
                        END as check_in_method,
                        CASE
                            WHEN ccl.notes LIKE '%QR%' THEN 1
                            ELSE 0
                        END as is_qr_checkin,
                        TIMESTAMPDIFF(MINUTE, ccl.check_in_time, NOW()) as minutes_checked_in
                      FROM children_checkin_log ccl
                      JOIN members m ON ccl.child_id = m.id
                      JOIN services s ON ccl.service_id = s.id
                      LEFT JOIN age_groups ag ON m.age_group_id = ag.id
                      LEFT JOIN members parent ON ccl.checked_in_by = parent.id
                      WHERE ccl.attendance_date = CURDATE()
                      ORDER BY ccl.check_in_time DESC";

            $dashboard_stmt = $conn->prepare($dashboard_query);
            $dashboard_stmt->execute();
            $dashboard_records = $dashboard_stmt->fetchAll(PDO::FETCH_ASSOC);

            // Check if there are any issues with the joins
            $simple_count_sql = "SELECT COUNT(*) as count FROM children_checkin_log WHERE attendance_date = ?";
            $count_stmt = $conn->prepare($simple_count_sql);
            $count_stmt->execute([$today]);
            $simple_count = $count_stmt->fetch(PDO::FETCH_ASSOC)['count'];

            $debug_info = [
                'today_date' => $today,
                'simple_count_in_table' => $simple_count,
                'children_records_raw' => $children_records,
                'dashboard_query_results' => $dashboard_records,
                'dashboard_count' => count($dashboard_records),
                'raw_count' => count($children_records),
                'query_comparison' => [
                    'raw_query_works' => count($children_records) > 0,
                    'dashboard_query_works' => count($dashboard_records) > 0,
                    'counts_match' => count($children_records) === count($dashboard_records)
                ]
            ];

            error_log("📊 Children Dashboard Debug: " . json_encode($debug_info, JSON_PRETTY_PRINT));

            echo json_encode($debug_info, JSON_PRETTY_PRINT);

        } catch (Exception $e) {
            error_log("💥 Debug Error: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Quick check for specific member in children table
     * URL: /attendance/check-daniela
     */
    public function checkDaniela() {
        header('Content-Type: application/json');

        try {
            $conn = $this->database->getConnection();
            $today = date('Y-m-d');

            // Check Daniela specifically
            $sql = "SELECT ccl.*, m.first_name, m.last_name, s.name as service_name
                   FROM children_checkin_log ccl
                   JOIN members m ON ccl.child_id = m.id
                   LEFT JOIN services s ON ccl.service_id = s.id
                   WHERE ccl.child_id = 575 AND ccl.attendance_date = ?";

            $stmt = $conn->prepare($sql);
            $stmt->execute([$today]);
            $daniela_record = $stmt->fetch(PDO::FETCH_ASSOC);

            // Also check if she's still in attendance table
            $attendance_sql = "SELECT * FROM attendance WHERE member_id = 575 AND attendance_date = ?";
            $att_stmt = $conn->prepare($attendance_sql);
            $att_stmt->execute([$today]);
            $attendance_record = $att_stmt->fetch(PDO::FETCH_ASSOC);

            echo json_encode([
                'daniela_in_children_table' => $daniela_record ? true : false,
                'daniela_in_attendance_table' => $attendance_record ? true : false,
                'children_record' => $daniela_record,
                'attendance_record' => $attendance_record,
                'today_date' => $today
            ], JSON_PRETTY_PRINT);

        } catch (Exception $e) {
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    /**
     * Process self-service attendance marking
     * Token authentication is handled by the Router
     *
     * @return void
     */
    public function qrMark() {
        header('Content-Type: application/json');

        try {
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $token = isset($_POST['token']) ? sanitize($_POST['token']) : null;
                $member_identifier = isset($_POST['member_identifier']) ? sanitize($_POST['member_identifier']) : null;
                $status = isset($_POST['status']) ? sanitize($_POST['status']) : 'present';

            // Get validated session data from router
            $qr_session = $_SESSION['validated_qr_session'] ?? null;

            if (!$qr_session || $qr_session['token'] !== $token) {
                echo json_encode(['success' => false, 'error' => 'Invalid or expired session']);
                return;
            }

            if (empty($member_identifier)) {
                echo json_encode(['success' => false, 'error' => 'Please select a member']);
                return;
            }

            // Find member by ID, name, or phone
            $member = $this->findMemberByIdentifier($member_identifier);

            if (!$member) {
                echo json_encode(['success' => false, 'error' => 'Member not found']);
                return;
            }

            // ROBUST: Use centralized attendance router with fallback
            try {
                require_once 'services/AttendanceRouter.php';
                $router = new AttendanceRouter($this->database->getConnection());

                // Prepare attendance data
                $attendance_data = [
                    'service_id' => $qr_session['service_id'],
                    'attendance_date' => $qr_session['attendance_date'],
                    'status' => $status,
                    'qr_session_id' => $qr_session['id'],
                    'checked_in_by' => $member['id'] // Self check-in for individual QR
                ];

                // Route attendance using centralized service
                $result = $router->routeAttendance($member, $attendance_data, 'qr_individual');

                if ($result['success']) {
                    $response = [
                        'success' => true,
                        'message' => $result['message'],
                        'member' => $member['first_name'] . ' ' . $member['last_name'],
                        'status' => $status,
                        'routed_to' => $result['table']
                    ];

                    // Add security code if it's a child
                    if (isset($result['security_code'])) {
                        $response['security_code'] = $result['security_code'];
                    }

                    echo json_encode($response);
                    return; // Exit function after successful routing
                } else {
                    echo json_encode(['success' => false, 'error' => $result['error']]);
                    return; // Exit function after router error
                }

            } catch (Exception $router_error) {
                // FALLBACK: If router fails, use simple main attendance table
                $this->logger->warning("AttendanceRouter failed, using fallback", [
                    'error' => $router_error->getMessage(),
                    'member_id' => $member['id'],
                    'qr_session_id' => $qr_session['id']
                ]);

                // FALLBACK: Simple attendance marking (original logic)
                $this->attendance->service_id = $qr_session['service_id'];
                $this->attendance->member_id = $member['id'];
                $this->attendance->attendance_date = $qr_session['attendance_date'];
                $this->attendance->status = $status;
                $this->attendance->qr_session_id = $qr_session['id'];
                $this->attendance->marked_via = 'qr_fallback';
                $this->attendance->created_at = date('Y-m-d H:i:s');
                $this->attendance->updated_at = date('Y-m-d H:i:s');

                // Check if attendance already exists
                $existing = $this->attendance->getByMemberDateService($member['id'], $qr_session['attendance_date'], $qr_session['service_id']);

                if ($existing) {
                    // Update existing record
                    $this->attendance->id = $existing['id'];
                    if ($this->attendance->update()) {
                        echo json_encode([
                            'success' => true,
                            'message' => 'Attendance updated successfully (fallback)',
                            'member' => $member['first_name'] . ' ' . $member['last_name'],
                            'status' => $status
                        ]);
                    } else {
                        echo json_encode(['success' => false, 'error' => 'Failed to update attendance']);
                    }
                } else {
                    // Create new record
                    if ($this->attendance->create()) {
                        echo json_encode([
                            'success' => true,
                            'message' => 'Attendance marked successfully (fallback)',
                            'member' => $member['first_name'] . ' ' . $member['last_name'],
                            'status' => $status
                        ]);
                    } else {
                        echo json_encode(['success' => false, 'error' => 'Failed to mark attendance']);
                    }
                }
            }
            return;
        } else {
            echo json_encode(['success' => false, 'error' => 'Invalid request method']);
        }
        } catch (Exception $e) {
            error_log("💥 QR MARK ERROR: " . $e->getMessage());
            error_log("  - File: " . $e->getFile());
            error_log("  - Line: " . $e->getLine());
            error_log("  - Stack Trace: " . $e->getTraceAsString());

            echo json_encode([
                'success' => false,
                'error' => 'An error occurred while processing your request. Please try again.'
            ]);
        }
    }

    /**
     * Find member by various identifiers
     *
     * @param string $identifier
     * @return array|null
     */
    private function findMemberByIdentifier($identifier) {
        // Try to find by ID first
        if (is_numeric($identifier)) {
            if ($this->member->getById($identifier)) {
                return [
                    'id' => $this->member->id,
                    'first_name' => $this->member->first_name,
                    'last_name' => $this->member->last_name,
                    'email' => $this->member->email,
                    'phone_number' => $this->member->phone_number,
                    'profile_picture' => $this->member->profile_picture,
                    'date_of_birth' => $this->member->date_of_birth, // CRITICAL: Include date_of_birth for age-based routing
                    'member_status' => $this->member->member_status
                ];
            }
        }

        // Try to find by phone number
        $query = "SELECT * FROM " . $this->member->table_name . "
                  WHERE phone = :identifier OR phone_2 = :identifier LIMIT 1";
        $stmt = $this->member->conn->prepare($query);
        $stmt->bindParam(':identifier', $identifier);
        $stmt->execute();
        $member = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($member) {
            return $member;
        }

        // Try to find by name (first name + last name)
        $name_parts = explode(' ', $identifier, 2);
        if (count($name_parts) >= 2) {
            $first_name = $name_parts[0];
            $last_name = $name_parts[1];

            $query = "SELECT * FROM " . $this->member->table_name . "
                      WHERE first_name LIKE :first_name AND last_name LIKE :last_name LIMIT 1";
            $stmt = $this->member->conn->prepare($query);
            $first_name_param = '%' . $first_name . '%';
            $last_name_param = '%' . $last_name . '%';
            $stmt->bindParam(':first_name', $first_name_param);
            $stmt->bindParam(':last_name', $last_name_param);
            $stmt->execute();
            $member = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($member) {
                return $member;
            }
        }

        return null;
    }

    /**
     * Full-page real-time attendance dashboard
     * Token authentication is handled by the Router
     *
     * @return void
     */
    public function attendanceStats() {
        try {
            // Get validated session data from router
            $qr_session_data = $_SESSION['validated_qr_session'] ?? null;
            $token = isset($_GET['token']) ? sanitize($_GET['token']) : null;

            if (!$qr_session_data || !$token) {
                set_flash_message('No active QR session found', 'danger');
                redirect('attendance/qr');
                return;
            }

            // Create QR session object from validated data for compatibility
            $qr_session = (object) $qr_session_data;

            // Set page variables
            $page_title = 'Real-time Attendance Dashboard - ICGC Emmanuel Temple';
            $active_page = 'attendance';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/attendance/attendance-stats.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("Attendance Stats Error: " . $e->getMessage());
            set_flash_message('Unable to load attendance dashboard. Please try again.', 'danger');
            redirect('attendance/qr');
        }
    }

    /**
     * Manage QR session (close, extend, view details)
     *
     * @return void
     */
    public function qrSessionManage() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            set_flash_message('Invalid request method', 'danger');
            redirect('attendance/qr');
            return;
        }

        $action = sanitize($_POST['action'] ?? '');
        $session_id = sanitize($_POST['session_id'] ?? '');

        if (empty($action) || empty($session_id)) {
            echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
            return;
        }

        try {
            require_once 'models/AttendanceQrSession.php';
            $qr_session = new AttendanceQrSession($this->database->getConnection());

            // Get session by ID
            if (!$qr_session->getById($session_id)) {
                echo json_encode(['success' => false, 'error' => 'QR session not found']);
                return;
            }

            switch ($action) {
                case 'close':
                    if ($qr_session->closeSession()) {
                        echo json_encode(['success' => true, 'message' => 'QR session closed successfully']);
                    } else {
                        echo json_encode(['success' => false, 'error' => 'Failed to close QR session']);
                    }
                    break;

                case 'extend':
                    $additional_minutes = intval($_POST['additional_minutes'] ?? 30);
                    if ($qr_session->extendSession($additional_minutes)) {
                        echo json_encode([
                            'success' => true,
                            'message' => "QR session extended by {$additional_minutes} minutes",
                            'new_expiry' => $qr_session->expires_at
                        ]);
                    } else {
                        echo json_encode(['success' => false, 'error' => 'Failed to extend QR session']);
                    }
                    break;

                case 'get_stats':
                    $stats = $qr_session->getSessionStats();
                    $attendance_records = $qr_session->getAttendanceRecords();
                    echo json_encode([
                        'success' => true,
                        'stats' => $stats,
                        'attendance_records' => $attendance_records
                    ]);
                    break;

                case 'delete':
                    if ($qr_session->deleteSession()) {
                        echo json_encode(['success' => true, 'message' => 'QR session deleted successfully']);
                    } else {
                        echo json_encode(['success' => false, 'error' => 'Failed to delete QR session']);
                    }
                    break;

                default:
                    echo json_encode(['success' => false, 'error' => 'Invalid action']);
                    break;
            }

        } catch (Exception $e) {
            error_log("QR Session Management Error: " . $e->getMessage());
            echo json_encode(['success' => false, 'error' => 'An error occurred while managing the QR session']);
        }
    }

    /**
     * Manage services (add/delete) for QR attendance
     *
     * @return void
     */
    public function serviceManage() {
        // Set content type for JSON response
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => 'Invalid request method']);
            return;
        }

        $action = sanitize($_POST['action'] ?? '');

        if (empty($action)) {
            echo json_encode(['success' => false, 'error' => 'Missing action parameter']);
            return;
        }

        try {
            switch ($action) {
                case 'add_service':
                    $this->addService();
                    break;

                case 'delete_service':
                    $this->deleteService();
                    break;

                case 'archive_service':
                    $this->archiveService();
                    break;

                case 'force_delete_service':
                    $this->forceDeleteService();
                    break;

                case 'get_service':
                    $this->getService();
                    break;

                case 'edit_service':
                    $this->editService();
                    break;

                default:
                    echo json_encode(['success' => false, 'error' => 'Invalid action']);
                    break;
            }
        } catch (Exception $e) {
            error_log("Service Management Error: " . $e->getMessage());
            echo json_encode(['success' => false, 'error' => 'An error occurred while managing the service']);
        }
    }

    /**
     * Add a new service
     *
     * @return void
     */
    private function addService() {
        // Validate required fields
        $name = sanitize($_POST['name'] ?? '');
        $day_of_week = sanitize($_POST['day_of_week'] ?? '');
        $time = sanitize($_POST['time'] ?? '');

        if (empty($name)) {
            echo json_encode(['success' => false, 'error' => 'Service name is required']);
            return;
        }

        if (empty($day_of_week)) {
            echo json_encode(['success' => false, 'error' => 'Day of week is required']);
            return;
        }

        if (empty($time)) {
            echo json_encode(['success' => false, 'error' => 'Time is required']);
            return;
        }

        // Validate day of week
        $valid_days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        if (!in_array($day_of_week, $valid_days)) {
            echo json_encode(['success' => false, 'error' => 'Invalid day of week']);
            return;
        }

        // Validate time format
        if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time)) {
            echo json_encode(['success' => false, 'error' => 'Invalid time format']);
            return;
        }

        // Set service properties
        $this->service->name = $name;
        $this->service->description = sanitize($_POST['description'] ?? '');
        $this->service->day_of_week = $day_of_week;
        $this->service->time = $time;
        $this->service->created_at = date('Y-m-d H:i:s');
        $this->service->updated_at = date('Y-m-d H:i:s');

        // Create service
        if ($this->service->create()) {
            // Get the ID of the newly created service
            $service_id = $this->database->getConnection()->lastInsertId();

            echo json_encode([
                'success' => true,
                'message' => 'Service added successfully',
                'service_id' => $service_id,
                'service_name' => $name
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Failed to create service']);
        }
    }

    /**
     * Delete a service (with smart handling)
     *
     * @return void
     */
    private function deleteService() {
        $service_id = sanitize($_POST['service_id'] ?? '');

        if (empty($service_id)) {
            echo json_encode(['success' => false, 'error' => 'Service ID is required']);
            return;
        }

        // Check if service exists
        if (!$this->service->getById($service_id)) {
            echo json_encode(['success' => false, 'error' => 'Service not found']);
            return;
        }

        // Check if service is used in attendance records or QR sessions
        $usedInAttendance = $this->service->isUsedInAttendance($service_id);
        $usedInQrSessions = $this->isServiceUsedInQrSessions($service_id);

        if ($usedInAttendance || $usedInQrSessions) {
            // Instead of blocking deletion, offer archive option
            $usageDetails = [];
            if ($usedInAttendance) $usageDetails[] = 'attendance records';
            if ($usedInQrSessions) $usageDetails[] = 'QR sessions';

            echo json_encode([
                'success' => false,
                'error' => 'Cannot delete service because it is used in ' . implode(' and ', $usageDetails),
                'canArchive' => true,
                'usageDetails' => $usageDetails
            ]);
            return;
        }

        // Safe to delete - no usage found
        if ($this->service->delete($service_id)) {
            echo json_encode([
                'success' => true,
                'message' => 'Service deleted successfully'
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Failed to delete service']);
        }
    }

    /**
     * Archive a service (soft delete)
     *
     * @return void
     */
    private function archiveService() {
        $service_id = sanitize($_POST['service_id'] ?? '');

        if (empty($service_id)) {
            echo json_encode(['success' => false, 'error' => 'Service ID is required']);
            return;
        }

        // Check if service exists
        if (!$this->service->getById($service_id)) {
            echo json_encode(['success' => false, 'error' => 'Service not found']);
            return;
        }

        // Archive service
        if ($this->service->archive($service_id)) {
            echo json_encode([
                'success' => true,
                'message' => 'Service archived successfully. It will no longer appear in the dropdown but historical data is preserved.'
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Failed to archive service']);
        }
    }

    /**
     * Force delete a service (admin only)
     *
     * @return void
     */
    private function forceDeleteService() {
        $service_id = sanitize($_POST['service_id'] ?? '');
        $confirm = sanitize($_POST['confirm'] ?? '');

        if (empty($service_id)) {
            echo json_encode(['success' => false, 'error' => 'Service ID is required']);
            return;
        }

        if ($confirm !== 'FORCE_DELETE') {
            echo json_encode(['success' => false, 'error' => 'Confirmation required for force delete']);
            return;
        }

        // Check if service exists
        if (!$this->service->getById($service_id)) {
            echo json_encode(['success' => false, 'error' => 'Service not found']);
            return;
        }

        // Force delete service
        if ($this->service->delete($service_id)) {
            echo json_encode([
                'success' => true,
                'message' => 'Service force deleted successfully. Warning: This may have affected historical data.'
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Failed to force delete service']);
        }
    }

    /**
     * Check if service is used in QR sessions
     *
     * @param int $service_id
     * @return bool
     */
    private function isServiceUsedInQrSessions($service_id) {
        try {
            // Get tenant_id from session, default to 1 if not set
            $tenant_id = $_SESSION['tenant_id'] ?? 1;

            $query = "SELECT COUNT(*) as count FROM attendance_qr_sessions qr
                      JOIN services s ON qr.service_id = s.id
                      WHERE qr.service_id = :service_id AND s.tenant_id = :tenant_id";
            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->bindParam(':service_id', $service_id);
            $stmt->bindParam(':tenant_id', $tenant_id);
            $stmt->execute();

            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['count'] > 0;
        } catch (Exception $e) {
            error_log("Error checking QR session usage: " . $e->getMessage());
            return true; // Err on the side of caution
        }
    }

    /**
     * Export QR session attendance data
     *
     * @return void
     */
    public function qrSessionExport() {
        $session_id = sanitize($_GET['session_id'] ?? '');

        if (empty($session_id)) {
            set_flash_message('Session ID is required', 'danger');
            redirect('attendance/qr');
            return;
        }

        try {
            require_once 'models/AttendanceQrSession.php';
            $qr_session = new AttendanceQrSession($this->database->getConnection());

            if (!$qr_session->getById($session_id)) {
                set_flash_message('QR session not found', 'danger');
                redirect('attendance/qr');
                return;
            }

            $attendance_records = $qr_session->getAttendanceRecords();

            // Set headers for CSV download
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="qr-session-' . $session_id . '-' . date('Y-m-d') . '.csv"');

            // Create CSV output
            $output = fopen('php://output', 'w');

            // CSV headers
            fputcsv($output, [
                'Name', 'Phone', 'Department', 'Status', 'Time Marked', 'Service'
            ]);

            // CSV data
            foreach ($attendance_records as $record) {
                fputcsv($output, [
                    $record['first_name'] . ' ' . $record['last_name'],
                    $record['phone_number'] ?? 'N/A',
                    $record['department'] ?? 'N/A',
                    ucfirst($record['status']),
                    date('Y-m-d H:i:s', strtotime($record['created_at'])),
                    $record['service_name']
                ]);
            }

            fclose($output);
            exit;

        } catch (Exception $e) {
            error_log("QR Session Export Error: " . $e->getMessage());
            set_flash_message('An error occurred while exporting session data', 'danger');
            redirect('attendance/qr');
        }
    }

    /**
     * Get real-time attendance stats for QR code
     *
     * @return void
     */
    public function qrStats() {
        // Security: Rate limiting for stats endpoint
        if (!$this->checkRateLimit('qr_stats', 30, 60)) { // 30 requests per minute
            echo json_encode(['success' => false, 'error' => 'Rate limit exceeded']);
            return;
        }

        // Security: Add headers
        header('Content-Type: application/json');
        header('X-Content-Type-Options: nosniff');

        // Check if token is provided
        if (!isset($_GET['token']) || empty($_GET['token'])) {
            echo json_encode(['success' => false, 'error' => 'QR session token is required']);
            return;
        }

        $token = sanitize($_GET['token']);

        // Security: Validate token format
        if (!$this->isValidTokenFormat($token)) {
            $this->logSecurityEvent('invalid_stats_token', ['token_length' => strlen($token)]);
            echo json_encode(['success' => false, 'error' => 'Invalid token format']);
            return;
        }

        // Load QR session model
        require_once 'models/AttendanceQrSession.php';
        $qr_session = new AttendanceQrSession($this->database->getConnection());

        // Get QR session by token
        if (!$qr_session->getByToken($token)) {
            echo json_encode(['success' => false, 'error' => 'Invalid QR session token']);
            return;
        }

        // Get attendance stats
        $present_members = $this->attendance->getPresentMembers($qr_session->attendance_date, $qr_session->service_id);
        $late_members = $this->attendance->getLateMembers($qr_session->attendance_date, $qr_session->service_id);

        // Combine and sort by most recent
        $all_members = array_merge($present_members, $late_members);
        usort($all_members, function($a, $b) {
            return strtotime($b['updated_at']) - strtotime($a['updated_at']);
        });

        // Show only the last 10
        $all_members = array_slice($all_members, 0, 10);

        // Generate HTML for stats
        ob_start();
        ?>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="bg-green-50 rounded-md p-4 border border-green-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-800">Present</p>
                        <h3 class="text-2xl font-bold text-green-600"><?php echo count($present_members); ?></h3>
                    </div>
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-user-check text-xl text-green-500"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-50 rounded-md p-4 border border-yellow-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-yellow-800">Late</p>
                        <h3 class="text-2xl font-bold text-yellow-600"><?php echo count($late_members); ?></h3>
                    </div>
                    <div class="p-3 rounded-full bg-yellow-100">
                        <i class="fas fa-clock text-xl text-yellow-500"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <h3 class="text-md font-medium mb-2 text-gray-700">Recent Activity</h3>
        <div class="overflow-hidden rounded-md border border-gray-200 max-h-60 overflow-y-auto" id="recent-activity">
            <?php if (empty($all_members)): ?>
                <div class="px-4 py-3 bg-gray-50 text-sm text-gray-500 text-center">
                    No attendance records yet. Members will appear here as they mark attendance.
                </div>
            <?php else: ?>
                <ul class="divide-y divide-gray-200" id="activity-list">
                    <?php foreach ($all_members as $member): 
                        $status_class = $member['status'] == 'present' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
                        $status_icon = $member['status'] == 'present' ? 'fas fa-check' : 'fas fa-clock';
                    ?>
                        <li class="px-4 py-3 flex items-center justify-between hover:bg-gray-50">
                            <div class="flex items-center">
                                <?php if (!empty($member['profile_picture'])): ?>
                                    <img src="<?php echo BASE_URL . 'uploads/profile_pictures/' . $member['profile_picture']; ?>" alt="Profile" class="h-8 w-8 rounded-full mr-2">
                                <?php else: ?>
                                    <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                                        <i class="fas fa-user text-gray-400"></i>
                                    </div>
                                <?php endif; ?>
                                <span class="text-sm font-medium text-gray-700"><?php echo $member['first_name'] . ' ' . $member['last_name']; ?></span>
                            </div>
                            <div class="flex items-center">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_class; ?>">
                                    <i class="<?php echo $status_icon; ?> mr-1"></i> <?php echo ucfirst($member['status']); ?>
                                </span>
                                <span class="ml-2 text-xs text-gray-500"><?php echo time_ago($member['updated_at']); ?></span>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
        <?php
        $html = ob_get_clean();

        // Return JSON response
        echo json_encode([
            'success' => true,
            'stats' => [
                'present_count' => count($present_members),
                'late_count' => count($late_members),
                'total_count' => count($present_members) + count($late_members)
            ],
            'html' => $html
        ]);
    }

    /**
     * Check if current user has specific permission
     *
     * @param string $permission
     * @return boolean
     */
    private function hasPermission($permission) {
        // Basic permission check - can be enhanced based on your user role system
        if (!isset($_SESSION['user_id'])) {
            return false;
        }

        // For now, allow all authenticated users to view attendance
        // You can enhance this based on your role/permission system
        return true;
    }

    /**
     * Generate secure token with additional entropy
     *
     * @return string
     */
    private function generateSecureToken() {
        // Use multiple sources of entropy
        $entropy = [
            random_bytes(16),
            microtime(true),
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $_SERVER['REMOTE_ADDR'] ?? '',
            session_id()
        ];

        return hash('sha256', implode('|', $entropy));
    }

    /**
     * Get local IP address for mobile testing
     *
     * @return string|null
     */
    private function getLocalIPAddress() {
        // Try to get the local IP address from various sources
        $local_ips = [];

        // Method 1: Check common network interfaces
        if (function_exists('exec')) {
            // Windows
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                exec('ipconfig', $output);
                foreach ($output as $line) {
                    if (strpos($line, 'IPv4 Address') !== false) {
                        preg_match('/(\d+\.\d+\.\d+\.\d+)/', $line, $matches);
                        if (isset($matches[1]) && $this->isValidLocalIP($matches[1])) {
                            $local_ips[] = $matches[1];
                        }
                    }
                }
            } else {
                // Linux/Mac
                exec('hostname -I', $output);
                if (!empty($output[0])) {
                    $ips = explode(' ', trim($output[0]));
                    foreach ($ips as $ip) {
                        if ($this->isValidLocalIP($ip)) {
                            $local_ips[] = $ip;
                        }
                    }
                }
            }
        }

        // Method 2: Use $_SERVER variables
        $server_ips = [
            $_SERVER['SERVER_ADDR'] ?? null,
            $_SERVER['LOCAL_ADDR'] ?? null,
        ];

        foreach ($server_ips as $ip) {
            if ($ip && $this->isValidLocalIP($ip)) {
                $local_ips[] = $ip;
            }
        }

        // Return the best local IP, prioritizing common WiFi ranges
        $wifi_ips = [];
        $other_ips = [];

        foreach ($local_ips as $ip) {
            // Common WiFi router ranges (prioritize these)
            if (strpos($ip, '192.168.1.') === 0 ||
                strpos($ip, '192.168.0.') === 0 ||
                strpos($ip, '192.168.2.') === 0 ||
                strpos($ip, '10.0.') === 0) {
                $wifi_ips[] = $ip;
            } else {
                $other_ips[] = $ip;
            }
        }

        // Return WiFi IP first, then any other valid IP
        if (!empty($wifi_ips)) {
            return $wifi_ips[0];
        }

        return !empty($other_ips) ? $other_ips[0] : null;
    }

    /**
     * Check if IP is a valid local network IP
     *
     * @param string $ip
     * @return bool
     */
    private function isValidLocalIP($ip) {
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return false;
        }

        // Exclude loopback and invalid ranges
        if (strpos($ip, '127.') === 0 || strpos($ip, '169.254.') === 0) {
            return false;
        }

        // Exclude common virtual machine networks
        if (strpos($ip, '192.168.56.') === 0 ||  // VirtualBox
            strpos($ip, '192.168.122.') === 0 || // KVM/QEMU
            strpos($ip, '172.17.') === 0) {      // Docker
            return false;
        }

        // Include common local network ranges
        return (strpos($ip, '192.168.') === 0 ||
                strpos($ip, '10.') === 0 ||
                (strpos($ip, '172.') === 0 &&
                 intval(explode('.', $ip)[1]) >= 16 &&
                 intval(explode('.', $ip)[1]) <= 31));
    }

    /**
     * Validate token format and structure
     *
     * @param string $token
     * @return boolean
     */
    private function isValidTokenFormat($token) {
        // Check if token is 32 or 64 character hex string
        return preg_match('/^[a-f0-9]{32,64}$/', $token);
    }

    /**
     * Simple rate limiting check
     *
     * @param string $action
     * @param int $limit
     * @param int $window_seconds
     * @return boolean
     */
    private function checkRateLimit($action, $limit, $window_seconds) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $key = "rate_limit_{$action}_{$ip}";

        // Initialize session rate limiting if not exists
        if (!isset($_SESSION['rate_limits'])) {
            $_SESSION['rate_limits'] = [];
        }

        $now = time();

        // Clean old entries
        if (isset($_SESSION['rate_limits'][$key])) {
            $_SESSION['rate_limits'][$key] = array_filter(
                $_SESSION['rate_limits'][$key],
                function($timestamp) use ($now, $window_seconds) {
                    return ($now - $timestamp) < $window_seconds;
                }
            );
        } else {
            $_SESSION['rate_limits'][$key] = [];
        }

        // Check if limit exceeded
        if (count($_SESSION['rate_limits'][$key]) >= $limit) {
            return false;
        }

        // Add current request
        $_SESSION['rate_limits'][$key][] = $now;

        return true;
    }

    /**
     * Log security events
     *
     * @param string $event
     * @param array $details
     * @return void
     */
    private function logSecurityEvent($event, $details = []) {
        $log_data = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'user_id' => $_SESSION['user_id'] ?? null,
            'details' => $details
        ];

        error_log("SECURITY_EVENT: " . json_encode($log_data));
    }

    /**
     * Get service details for editing
     *
     * @return void
     */
    private function getService() {
        $service_id = sanitize($_POST['service_id'] ?? '');

        if (empty($service_id)) {
            echo json_encode(['success' => false, 'error' => 'Service ID is required']);
            return;
        }

        // Get service details
        if ($this->service->getById($service_id)) {
            echo json_encode([
                'success' => true,
                'service' => [
                    'id' => $this->service->id,
                    'name' => $this->service->name,
                    'description' => $this->service->description,
                    'day_of_week' => $this->service->day_of_week,
                    'time' => $this->service->time
                ]
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Service not found']);
        }
    }

    /**
     * Edit a service
     *
     * @return void
     */
    private function editService() {
        // Validate required fields
        $service_id = sanitize($_POST['service_id'] ?? '');
        $name = sanitize($_POST['name'] ?? '');
        $day_of_week = sanitize($_POST['day_of_week'] ?? '');
        $time = sanitize($_POST['time'] ?? '');

        if (empty($service_id)) {
            echo json_encode(['success' => false, 'error' => 'Service ID is required']);
            return;
        }

        if (empty($name)) {
            echo json_encode(['success' => false, 'error' => 'Service name is required']);
            return;
        }

        if (empty($day_of_week)) {
            echo json_encode(['success' => false, 'error' => 'Day of week is required']);
            return;
        }

        if (empty($time)) {
            echo json_encode(['success' => false, 'error' => 'Time is required']);
            return;
        }

        // Validate day of week
        $valid_days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        if (!in_array($day_of_week, $valid_days)) {
            echo json_encode(['success' => false, 'error' => 'Invalid day of week']);
            return;
        }

        // Validate time format
        if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time)) {
            echo json_encode(['success' => false, 'error' => 'Invalid time format']);
            return;
        }

        // Check if service exists
        if (!$this->service->getById($service_id)) {
            echo json_encode(['success' => false, 'error' => 'Service not found']);
            return;
        }

        // Update service properties
        $this->service->name = $name;
        $this->service->description = sanitize($_POST['description'] ?? '');
        $this->service->day_of_week = $day_of_week;
        $this->service->time = $time;
        $this->service->updated_at = date('Y-m-d H:i:s');

        // Update service
        if ($this->service->update()) {
            echo json_encode([
                'success' => true,
                'message' => 'Service updated successfully',
                'service_id' => $service_id,
                'service_name' => $name
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Failed to update service']);
        }
    }

    /**
     * Display detailed analytics for a QR session
     *
     * @return void
     */
    public function qrSessionDetails() {
        // Check if session ID is provided
        if (!isset($_GET['session_id']) || empty($_GET['session_id'])) {
            set_flash_message('Session ID is required', 'danger');
            redirect('attendance/qr');
            return;
        }

        $session_id = sanitize($_GET['session_id']);

        try {
            require_once 'models/AttendanceQrSession.php';
            $qr_session = new AttendanceQrSession($this->database->getConnection());

            // Get detailed session analytics
            $session_details = $qr_session->getSessionDetailedAnalytics($session_id);

            if (!$session_details) {
                set_flash_message('QR session not found', 'danger');
                redirect('attendance/qr');
                return;
            }

            // Check and update session status if expired
            $session = &$session_details['session'];
            if ($session['status'] === 'active' && strtotime($session['expires_at']) < time()) {
                // Update status to expired in database
                try {
                    $qr_session->id = $session_id;
                    $qr_session->status = 'expired';
                    $qr_session->updateStatus();

                    // Update the session data for display
                    $session['status'] = 'expired';
                } catch (Exception $e) {
                    error_log("Error updating QR session status: " . $e->getMessage());
                }
            }

            // Set page title and active page
            $page_title = 'QR Session Details - ' . $session_details['session']['service_name'] . ' - ICGC Emmanuel Temple';
            $active_page = 'attendance';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/attendance/qr-session-details.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("QR Session Details Error: " . $e->getMessage());
            error_log("QR Session Details Stack Trace: " . $e->getTraceAsString());
            set_flash_message('An error occurred while loading session details: ' . $e->getMessage(), 'danger');
            redirect('attendance/qr');
        }
    }

    /**
     * Display QR sessions archive with filtering
     *
     * @return void
     */
    public function qrSessionsArchive() {
        try {
            require_once 'models/AttendanceQrSession.php';
            $qr_session = new AttendanceQrSession($this->database->getConnection());

            // Get filter parameters
            $status = isset($_GET['status']) ? sanitize($_GET['status']) : null;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;

            // Validate status filter
            $valid_statuses = ['active', 'expired', 'closed', 'archived'];
            if ($status && !in_array($status, $valid_statuses)) {
                $status = null;
            }

            // Get sessions with detailed analytics
            $sessions = $qr_session->getSessionsWithDetailedAnalytics($status, $limit);

            // Get summary statistics
            $summary_stats = $this->getQrSessionsSummaryStats();

            // Set page title and active page
            $page_title = 'QR Sessions Archive - ICGC Emmanuel Temple';
            $active_page = 'attendance';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/attendance/qr-sessions-archive.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("QR Sessions Archive Error: " . $e->getMessage());
            set_flash_message('An error occurred while loading sessions archive', 'danger');
            redirect('attendance/qr');
        }
    }

    /**
     * Get summary statistics for QR sessions
     *
     * @return array
     */
    private function getQrSessionsSummaryStats() {
        try {
            $query = "SELECT
                        COUNT(*) as total_sessions,
                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_sessions,
                        SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_sessions,
                        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_sessions,
                        SUM(CASE WHEN status = 'archived' THEN 1 ELSE 0 END) as archived_sessions,
                        SUM(attendance_count) as total_qr_attendance,
                        AVG(attendance_count) as avg_attendance_per_session,
                        MAX(attendance_count) as max_attendance_session,
                        COUNT(CASE WHEN attendance_date >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as sessions_last_30_days
                      FROM attendance_qr_sessions";

            $stmt = $this->database->getConnection()->prepare($query);
            $stmt->execute();

            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error getting QR sessions summary stats: " . $e->getMessage());
            return [
                'total_sessions' => 0,
                'active_sessions' => 0,
                'expired_sessions' => 0,
                'closed_sessions' => 0,
                'archived_sessions' => 0,
                'total_qr_attendance' => 0,
                'avg_attendance_per_session' => 0,
                'max_attendance_session' => 0,
                'sessions_last_30_days' => 0
            ];
        }
    }

    /**
     * Display comprehensive QR analytics dashboard - COMPLETELY REWRITTEN
     *
     * @return void
     */
    public function qrAnalyticsDashboard() {
        try {
            // Simple sanitize function
            $sanitize = function($data) {
                return htmlspecialchars(strip_tags(trim($data ?? '')), ENT_QUOTES, 'UTF-8');
            };

            // Get parameters safely
            $period_type = isset($_GET['period']) ? $sanitize($_GET['period']) : 'monthly';
            $period_value = isset($_GET['value']) ? $sanitize($_GET['value']) : date('Y-m');

            // Validate period type
            $valid_periods = ['weekly', 'monthly', 'quarterly', 'yearly', 'rolling'];
            if (!in_array($period_type, $valid_periods)) {
                $period_type = 'monthly';
            }

            // Auto-detect period type from value format
            if (strpos($period_value, 'Q') !== false) {
                $period_type = 'quarterly';
            } elseif (strpos($period_value, 'W') !== false) {
                $period_type = 'weekly';
            } elseif (preg_match('/^\d{4}-\d{2}$/', $period_value)) {
                $period_type = 'monthly';
            } elseif (preg_match('/^\d{4}$/', $period_value)) {
                $period_type = 'yearly';
            }

            // Set defaults if needed
            if ($period_type === 'quarterly' && !preg_match('/^\d{4}-Q[1-4]$/', $period_value)) {
                $current_quarter = ceil(date('n') / 3);
                $period_value = date('Y') . '-Q' . $current_quarter;
            }

            // Calculate date range using simple logic
            $date_range = $this->calculateSimpleDateRange($period_type, $period_value);

            // Get simple analytics data
            $analytics_data = $this->getSimpleQrAnalytics($date_range);

            // Get quarterly performance data
            $quarterly_performance = $this->getSimpleQuarterlyData(date('Y'));

            // Get enhanced analytics data
            $member_engagement = $this->getMemberEngagementData($date_range);
            $gender_distribution = $this->getGenderDistributionData($date_range);
            $department_distribution = $this->getDepartmentDistributionData($date_range);

            // Prepare data for view
            $current_period = [
                'type' => $period_type,
                'value' => $period_value
            ];

            $overview = $analytics_data['overview'] ?? [];

            // Add the new data to analytics_data array
            $analytics_data['member_engagement'] = $member_engagement;
            $analytics_data['gender_distribution'] = $gender_distribution;
            $analytics_data['department_distribution'] = $department_distribution;

            // Set page title and active page
            $page_title = 'QR Analytics Dashboard - ICGC Emmanuel Temple';
            $active_page = 'attendance';

            // Start output buffering
            ob_start();

            // Load unified view
            require_once 'views/attendance/qr-analytics-unified.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("QR Analytics Dashboard Error: " . $e->getMessage());
            error_log("QR Analytics Dashboard Stack Trace: " . $e->getTraceAsString());
            set_flash_message('An error occurred while loading analytics dashboard: ' . $e->getMessage(), 'danger');
            redirect('attendance/qr');
        }
    }

    /**
     * Get comprehensive analytics data for QR system
     *
     * @param int $timeframe_days
     * @return array
     */
    private function getQrAnalyticsData($timeframe_days = 30) {
        try {
            $conn = $this->database->getConnection();

            // 1. Overview Statistics
            $overview_query = "SELECT
                                COUNT(*) as total_sessions,
                                SUM(attendance_count) as total_qr_attendance,
                                AVG(attendance_count) as avg_attendance_per_session,
                                MAX(attendance_count) as max_attendance_session,
                                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_sessions,
                                SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_sessions,
                                SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_sessions,
                                SUM(CASE WHEN status = 'archived' THEN 1 ELSE 0 END) as archived_sessions
                              FROM attendance_qr_sessions
                              WHERE attendance_date >= DATE_SUB(NOW(), INTERVAL :timeframe DAY)";

            $stmt = $conn->prepare($overview_query);
            $stmt->bindParam(':timeframe', $timeframe_days, PDO::PARAM_INT);
            $stmt->execute();
            $overview = $stmt->fetch(PDO::FETCH_ASSOC);

            // Ensure we have default values
            if (!$overview) {
                $overview = [
                    'total_sessions' => 0,
                    'total_qr_attendance' => 0,
                    'avg_attendance_per_session' => 0,
                    'max_attendance_session' => 0,
                    'active_sessions' => 0,
                    'expired_sessions' => 0,
                    'closed_sessions' => 0,
                    'archived_sessions' => 0
                ];
            }

            // Ensure all numeric fields are properly set
            $overview['total_sessions'] = (int)($overview['total_sessions'] ?? 0);
            $overview['total_qr_attendance'] = (int)($overview['total_qr_attendance'] ?? 0);
            $overview['avg_attendance_per_session'] = (float)($overview['avg_attendance_per_session'] ?? 0);
            $overview['max_attendance_session'] = (int)($overview['max_attendance_session'] ?? 0);
            $overview['active_sessions'] = (int)($overview['active_sessions'] ?? 0);
            $overview['expired_sessions'] = (int)($overview['expired_sessions'] ?? 0);
            $overview['closed_sessions'] = (int)($overview['closed_sessions'] ?? 0);
            $overview['archived_sessions'] = (int)($overview['archived_sessions'] ?? 0);

            // Calculate QR adoption rate
            $adoption_query = "SELECT
                                (SELECT SUM(attendance_count) FROM attendance_qr_sessions
                                 WHERE attendance_date >= DATE_SUB(NOW(), INTERVAL :timeframe DAY)) as qr_attendance,
                                (SELECT COUNT(*) FROM attendance
                                 WHERE attendance_date >= DATE_SUB(NOW(), INTERVAL :timeframe DAY)) as total_attendance";

            $stmt = $conn->prepare($adoption_query);
            $stmt->bindParam(':timeframe', $timeframe_days, PDO::PARAM_INT);
            $stmt->execute();
            $adoption_data = $stmt->fetch(PDO::FETCH_ASSOC);

            // Safely calculate adoption rate
            $qr_attendance = (int)($adoption_data['qr_attendance'] ?? 0);
            $total_attendance = (int)($adoption_data['total_attendance'] ?? 0);
            $overview['qr_adoption_rate'] = $total_attendance > 0 ? ($qr_attendance / $total_attendance) * 100 : 0;

            // Add additional overview metrics
            $overview['sessions_growth'] = 0; // Placeholder - would need previous month data to calculate
            $overview['avg_response_time'] = 5.2; // Placeholder - would need attendance timing data to calculate

            // 2. Trends Data for Charts
            $trends_query = "SELECT
                               DATE(attendance_date) as date,
                               COUNT(*) as sessions,
                               SUM(attendance_count) as qr_attendance,
                               AVG(attendance_count) as avg_per_session
                             FROM attendance_qr_sessions
                             WHERE attendance_date >= DATE_SUB(NOW(), INTERVAL :timeframe DAY)
                             GROUP BY DATE(attendance_date)
                             ORDER BY date ASC";

            $stmt = $conn->prepare($trends_query);
            $stmt->bindParam(':timeframe', $timeframe_days, PDO::PARAM_INT);
            $stmt->execute();
            $trends_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Process trends data to add missing fields
            foreach ($trends_data as &$trend) {
                $trend['attendance'] = $trend['qr_attendance']; // Map for view compatibility
                $trend['peak_hour'] = null; // Would need additional query to calculate
                $trend['efficiency'] = 75; // Placeholder - would need session duration data to calculate
            }

            // 3. Usage Patterns (Hourly Distribution)
            $patterns_query = "SELECT
                                HOUR(a.created_at) as hour,
                                COUNT(*) as attendance_count
                              FROM attendance a
                              JOIN attendance_qr_sessions qs ON a.qr_session_id = qs.id
                              WHERE a.attendance_date >= DATE_SUB(NOW(), INTERVAL :timeframe DAY)
                              AND a.marked_via = 'qr'
                              GROUP BY HOUR(a.created_at)
                              ORDER BY hour";

            $stmt = $conn->prepare($patterns_query);
            $stmt->bindParam(':timeframe', $timeframe_days, PDO::PARAM_INT);
            $stmt->execute();
            $hourly_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Convert to 24-hour array
            $hourly_distribution = array_fill(0, 24, 0);
            foreach ($hourly_data as $hour_data) {
                $hourly_distribution[$hour_data['hour']] = (int)$hour_data['attendance_count'];
            }

            // 4. Service Distribution
            $service_query = "SELECT
                                s.name,
                                COUNT(qs.id) as session_count,
                                SUM(qs.attendance_count) as total_attendance
                              FROM attendance_qr_sessions qs
                              JOIN services s ON qs.service_id = s.id
                              WHERE qs.attendance_date >= DATE_SUB(NOW(), INTERVAL :timeframe DAY)
                              GROUP BY s.id, s.name
                              ORDER BY session_count DESC";

            $stmt = $conn->prepare($service_query);
            $stmt->bindParam(':timeframe', $timeframe_days, PDO::PARAM_INT);
            $stmt->execute();
            $service_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate percentages
            $total_sessions = array_sum(array_column($service_data, 'session_count'));
            foreach ($service_data as &$service) {
                $service['percentage'] = $total_sessions > 0 ? ($service['session_count'] / $total_sessions) * 100 : 0;
                // Ensure all required keys exist
                $service['count'] = $service['session_count']; // For backward compatibility
            }

            // 5. Performance Metrics
            $performance_query = "SELECT
                                    AVG(TIMESTAMPDIFF(MINUTE, created_at, expires_at)) as avg_session_duration,
                                    AVG(TIMESTAMPDIFF(MINUTE, created_at, COALESCE(last_used_at, expires_at))) as avg_active_duration,
                                    AVG(CASE WHEN TIMESTAMPDIFF(HOUR, created_at, COALESCE(last_used_at, expires_at)) > 0
                                         THEN attendance_count / TIMESTAMPDIFF(HOUR, created_at, COALESCE(last_used_at, expires_at))
                                         ELSE 0 END) as attendance_per_hour
                                  FROM attendance_qr_sessions
                                  WHERE attendance_date >= DATE_SUB(NOW(), INTERVAL :timeframe DAY)
                                  AND attendance_count > 0";

            $stmt = $conn->prepare($performance_query);
            $stmt->bindParam(':timeframe', $timeframe_days, PDO::PARAM_INT);
            $stmt->execute();
            $performance_metrics = $stmt->fetch(PDO::FETCH_ASSOC);

            // Ensure we have default values for performance metrics
            if (!$performance_metrics) {
                $performance_metrics = [
                    'avg_session_duration' => 0,
                    'avg_active_duration' => 0,
                    'attendance_per_hour' => 0
                ];
            }

            // Calculate efficiency rate
            $performance_metrics['efficiency_rate'] = $performance_metrics['avg_session_duration'] > 0 ?
                ($performance_metrics['avg_active_duration'] / $performance_metrics['avg_session_duration']) * 100 : 0;

            // Format chart data
            $chart_data = [
                'labels' => array_column($trends_data, 'date'),
                'qr_attendance' => array_column($trends_data, 'qr_attendance'),
                'total_attendance' => [] // Would need additional query for total attendance
            ];

            return [
                'overview' => $overview,
                'trends' => [
                    'chart_data' => $chart_data,
                    'daily_metrics' => $trends_data
                ],
                'usage_patterns' => [
                    'hourly_distribution' => $hourly_distribution,
                    'service_distribution' => $service_data
                ],
                'performance_metrics' => $performance_metrics
            ];

        } catch (Exception $e) {
            error_log("Error getting QR analytics data: " . $e->getMessage());
            return [
                'overview' => [],
                'trends' => [],
                'usage_patterns' => [],
                'performance_metrics' => []
            ];
        }
    }

    /**
     * Get enhanced analytics data with period support
     */
    private function getEnhancedQrAnalyticsData($period_type, $period_value, $comparison_period = null, $date_range = null) {
        try {
            $conn = $this->database->getConnection();

            // Calculate date ranges based on period type
            if ($date_range) {
                $comparison_ranges = $comparison_period ? $this->calculateDateRanges($period_type, $comparison_period) : null;
                $date_ranges = [
                    'current' => $date_range,
                    'comparison' => $comparison_ranges ? $comparison_ranges['current'] : null
                ];
            } else {
                $date_ranges = $this->calculateDateRanges($period_type, $period_value, $comparison_period);
            }

            // Get overview metrics for current period
            $overview = $this->getOverviewMetrics($date_ranges['current']);

            // Get comparison metrics if comparison period is specified
            $comparison_overview = null;
            if ($comparison_period && isset($date_ranges['comparison'])) {
                $comparison_overview = $this->getOverviewMetrics($date_ranges['comparison']);
                $overview['comparison'] = $this->calculateGrowthMetrics($overview, $comparison_overview);
            }

            // Get trends data
            $trends = $this->getTrendsData($date_ranges['current'], $period_type);

            // Get usage patterns
            $usage_patterns = $this->getUsagePatterns($date_ranges['current']);

            // Get performance metrics
            $performance_metrics = $this->getPerformanceMetrics($date_ranges['current']);

            // Get quarterly performance data
            $quarterly_performance = $this->getQuarterlyPerformance();

            // Get member engagement data
            $member_engagement = $this->getMemberEngagementData($date_ranges['current']);

            // Get gender distribution data
            $gender_distribution = $this->getGenderDistributionData($date_ranges['current']);

            // Get department distribution data
            $department_distribution = $this->getDepartmentDistributionData($date_ranges['current']);

            return [
                'overview' => $overview,
                'trends' => $trends,
                'usage_patterns' => $usage_patterns,
                'performance_metrics' => $performance_metrics,
                'member_engagement' => $member_engagement,
                'gender_distribution' => $gender_distribution,
                'department_distribution' => $department_distribution,
                'quarterly_performance' => $quarterly_performance,
                'date_ranges' => $date_ranges,
                'period_info' => [
                    'type' => $period_type,
                    'value' => $period_value,
                    'display_name' => $this->getPeriodDisplayName($period_type, $period_value)
                ],
                'current_period' => [
                    'type' => $period_type,
                    'value' => $period_value
                ]
            ];

        } catch (Exception $e) {
            error_log("Error getting enhanced QR analytics data: " . $e->getMessage());
            return $this->getDefaultAnalyticsStructure();
        }
    }

    /**
     * Calculate date ranges based on period type
     */
    private function calculateDateRanges($period_type, $period_value, $comparison_period = null) {
        $ranges = [];

        switch ($period_type) {
            case 'weekly':
                // Format: 2024-W01
                $year = substr($period_value, 0, 4);
                $week = substr($period_value, 6);
                $start_date = date('Y-m-d', strtotime($year . 'W' . str_pad($week, 2, '0', STR_PAD_LEFT)));
                $end_date = date('Y-m-d', strtotime($start_date . ' +6 days'));
                break;

            case 'monthly':
                // Format: 2024-01
                $start_date = $period_value . '-01';
                $end_date = date('Y-m-t', strtotime($start_date));
                break;

            case 'quarterly':
                // Format: 2024-Q1
                $year = substr($period_value, 0, 4);
                $quarter = substr($period_value, 6);
                $quarter_months = [
                    'Q1' => ['01', '03'],
                    'Q2' => ['04', '06'],
                    'Q3' => ['07', '09'],
                    'Q4' => ['10', '12']
                ];
                $start_date = $year . '-' . $quarter_months[$quarter][0] . '-01';
                $end_date = date('Y-m-t', strtotime($year . '-' . $quarter_months[$quarter][1] . '-01'));
                break;

            case 'yearly':
                // Format: 2024
                $start_date = $period_value . '-01-01';
                $end_date = $period_value . '-12-31';
                break;

            case 'rolling':
                // Format: 30 (days)
                $days = (int)$period_value;
                $end_date = date('Y-m-d');
                $start_date = date('Y-m-d', strtotime("-{$days} days"));
                break;

            default:
                // Default to current month
                $start_date = date('Y-m-01');
                $end_date = date('Y-m-t');
        }

        $ranges['current'] = ['start' => $start_date, 'end' => $end_date];

        // Debug logging
        error_log("Date Range Calculation - Period Type: $period_type, Period Value: $period_value");
        error_log("Calculated Date Range - Start: $start_date, End: $end_date");

        // Calculate comparison period if specified
        if ($comparison_period) {
            $ranges['comparison'] = $this->calculateComparisonRange($period_type, $comparison_period);
        }

        return $ranges;
    }

    /**
     * Get member engagement analytics
     */
    private function getMemberEngagementAnalytics($date_range) {
        try {
            $conn = $this->database->getConnection();

            // Member retention analysis
            $retention_query = "
                SELECT
                    m.id,
                    m.first_name,
                    m.last_name,
                    COUNT(a.id) as attendance_count,
                    MIN(a.attendance_date) as first_attendance,
                    MAX(a.attendance_date) as last_attendance,
                    DATEDIFF(MAX(a.attendance_date), MIN(a.attendance_date)) as engagement_span
                FROM members m
                LEFT JOIN attendance a ON m.id = a.member_id
                WHERE a.attendance_date BETWEEN :start_date AND :end_date
                GROUP BY m.id
                ORDER BY attendance_count DESC
            ";

            $stmt = $conn->prepare($retention_query);
            $stmt->bindParam(':start_date', $date_range['start']);
            $stmt->bindParam(':end_date', $date_range['end']);
            $stmt->execute();
            $retention_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate engagement scores
            $engagement_scores = $this->calculateEngagementScores($retention_data, $date_range);

            return [
                'retention_data' => $retention_data,
                'engagement_scores' => $engagement_scores,
                'summary' => $this->getMemberEngagementSummary($retention_data)
            ];

        } catch (Exception $e) {
            error_log("Error getting member engagement analytics: " . $e->getMessage());
            return ['retention_data' => [], 'engagement_scores' => [], 'summary' => []];
        }
    }

    /**
     * Get service performance analytics
     */
    private function getServicePerformanceAnalytics($date_range) {
        try {
            $conn = $this->database->getConnection();

            $service_query = "
                SELECT
                    s.id,
                    s.name,
                    s.time,
                    s.description,
                    COUNT(DISTINCT qs.id) as qr_sessions,
                    COUNT(DISTINCT CASE WHEN a.marked_via = 'qr' THEN a.id END) as qr_attendance_count,
                    COUNT(DISTINCT a.id) as total_attendance_count,
                    COUNT(DISTINCT a.member_id) as unique_members,
                    AVG(CASE WHEN a.marked_via = 'qr' THEN 1 ELSE 0 END) * 100 as qr_adoption_rate,
                    MAX(daily_counts.daily_attendance) as peak_attendance,
                    MIN(daily_counts.daily_attendance) as min_attendance
                FROM services s
                LEFT JOIN attendance_qr_sessions qs ON s.id = qs.service_id
                    AND qs.attendance_date BETWEEN :start_date AND :end_date
                LEFT JOIN attendance a ON s.id = a.service_id
                    AND a.attendance_date BETWEEN :start_date AND :end_date
                LEFT JOIN (
                    SELECT
                        service_id,
                        attendance_date,
                        COUNT(*) as daily_attendance
                    FROM attendance
                    WHERE attendance_date BETWEEN :start_date2 AND :end_date2
                    GROUP BY service_id, attendance_date
                ) daily_counts ON s.id = daily_counts.service_id
                GROUP BY s.id, s.name, s.time, s.description
                HAVING total_attendance_count > 0
                ORDER BY qr_attendance_count DESC
            ";

            $stmt = $conn->prepare($service_query);
            $stmt->bindParam(':start_date', $date_range['start']);
            $stmt->bindParam(':end_date', $date_range['end']);
            $stmt->bindParam(':start_date2', $date_range['start']);
            $stmt->bindParam(':end_date2', $date_range['end']);
            $stmt->execute();

            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate additional metrics
            foreach ($services as &$service) {
                $service['qr_attendance_count'] = (int)($service['qr_attendance_count'] ?? 0);
                $service['total_attendance_count'] = (int)($service['total_attendance_count'] ?? 0);
                $service['qr_adoption_rate'] = round((float)($service['qr_adoption_rate'] ?? 0), 1);
                $service['avg_attendance'] = $service['total_attendance_count'] > 0 ?
                    round($service['total_attendance_count'] / max(1, $service['qr_sessions']), 1) : 0;
                $service['performance_score'] = $this->calculateServicePerformanceScore($service);
            }

            return $services;

        } catch (Exception $e) {
            error_log("Error getting service performance analytics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Calculate service performance score
     */
    private function calculateServicePerformanceScore($service) {
        $attendance_score = min(100, ($service['total_attendance_count'] / 50) * 40); // Max 40 points for attendance
        $adoption_score = ($service['qr_adoption_rate'] / 100) * 30; // Max 30 points for QR adoption
        $consistency_score = 30; // Base consistency score, could be enhanced with variance calculation

        return round($attendance_score + $adoption_score + $consistency_score, 1);
    }

    /**
     * Get department insights
     */
    private function getDepartmentInsights($date_range) {
        try {
            $conn = $this->database->getConnection();

            $dept_query = "
                SELECT
                    m.department,
                    COUNT(DISTINCT m.id) as total_members,
                    COUNT(DISTINCT a.member_id) as active_members,
                    COUNT(a.id) as total_attendance,
                    AVG(CASE WHEN a.id IS NOT NULL THEN 1 ELSE 0 END) * 100 as attendance_rate
                FROM members m
                LEFT JOIN attendance a ON m.id = a.member_id
                    AND a.attendance_date BETWEEN :start_date AND :end_date
                WHERE m.department IS NOT NULL AND m.department != ''
                GROUP BY m.department
                ORDER BY attendance_rate DESC
            ";

            $stmt = $conn->prepare($dept_query);
            $stmt->bindParam(':start_date', $date_range['start']);
            $stmt->bindParam(':end_date', $date_range['end']);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error getting department insights: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get attendance forecast
     */
    private function getAttendanceForecast($period_type) {
        try {
            $conn = $this->database->getConnection();

            // Get historical data for forecasting
            $forecast_query = "
                SELECT
                    DATE(qs.attendance_date) as date,
                    SUM(qs.attendance_count) as attendance
                FROM attendance_qr_sessions qs
                WHERE qs.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)
                GROUP BY DATE(qs.attendance_date)
                ORDER BY date
            ";

            $stmt = $conn->prepare($forecast_query);
            $stmt->execute();
            $historical_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Simple linear regression for forecasting
            $forecast = $this->calculateLinearForecast($historical_data);

            return [
                'historical_data' => $historical_data,
                'forecast_data' => $forecast,
                'trend_direction' => $this->getTrendDirection($historical_data)
            ];

        } catch (Exception $e) {
            error_log("Error getting attendance forecast: " . $e->getMessage());
            return ['historical_data' => [], 'forecast_data' => [], 'trend_direction' => 'stable'];
        }
    }

    /**
     * Get default analytics structure
     */
    private function getDefaultAnalyticsStructure() {
        return [
            'overview' => [
                'total_sessions' => 0,
                'total_qr_attendance' => 0,
                'qr_adoption_rate' => 0,
                'avg_response_time' => 0,
                'sessions_growth' => 0
            ],
            'trends' => [
                'daily_metrics' => [],
                'chart_data' => ['labels' => [], 'qr_attendance' => [], 'total_attendance' => []]
            ],
            'usage_patterns' => [
                'hourly_distribution' => array_fill(0, 24, 0),
                'service_distribution' => []
            ],
            'performance_metrics' => [],
            'member_engagement' => ['retention_data' => [], 'engagement_scores' => [], 'summary' => []],
            'service_performance' => [],
            'department_insights' => [],
            'forecasting' => ['historical_data' => [], 'forecast_data' => [], 'trend_direction' => 'stable']
        ];
    }

    /**
     * Calculate growth metrics between periods
     */
    private function calculateGrowthMetrics($current, $previous) {
        $growth = [];

        foreach ($current as $key => $value) {
            if (is_numeric($value) && isset($previous[$key]) && $previous[$key] > 0) {
                $growth[$key] = (($value - $previous[$key]) / $previous[$key]) * 100;
            } else {
                $growth[$key] = 0;
            }
        }

        return $growth;
    }

    /**
     * Get period display name
     */
    private function getPeriodDisplayName($period_type, $period_value) {
        switch ($period_type) {
            case 'weekly':
                return 'Week ' . substr($period_value, 6) . ', ' . substr($period_value, 0, 4);
            case 'monthly':
                return date('F Y', strtotime($period_value . '-01'));
            case 'quarterly':
                $year = substr($period_value, 0, 4);
                $quarter = substr($period_value, 6);
                return $quarter . ' ' . $year;
            case 'yearly':
                return $period_value;
            case 'rolling':
                return 'Last ' . $period_value . ' days';
            default:
                return 'Current Period';
        }
    }

    /**
     * Calculate comparison range
     */
    private function calculateComparisonRange($period_type, $comparison_period) {
        // Implementation for comparison period calculation
        return ['start' => date('Y-m-01', strtotime('-1 month')), 'end' => date('Y-m-t', strtotime('-1 month'))];
    }

    /**
     * Get overview metrics for a date range
     */
    private function getOverviewMetrics($date_range) {
        try {
            $conn = $this->database->getConnection();

            // Get QR sessions count
            $sessions_query = "
                SELECT COUNT(DISTINCT id) as total_sessions
                FROM attendance_qr_sessions
                WHERE attendance_date BETWEEN :start_date AND :end_date
            ";

            $stmt = $conn->prepare($sessions_query);
            $stmt->bindParam(':start_date', $date_range['start']);
            $stmt->bindParam(':end_date', $date_range['end']);
            $stmt->execute();
            $sessions_result = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get actual QR attendance count (from attendance records marked via QR)
            $qr_attendance_query = "
                SELECT COUNT(*) as qr_attendance_count,
                       COUNT(DISTINCT member_id) as unique_qr_members
                FROM attendance
                WHERE marked_via = 'qr'
                AND attendance_date BETWEEN :start_date AND :end_date
            ";

            $stmt = $conn->prepare($qr_attendance_query);
            $stmt->bindParam(':start_date', $date_range['start']);
            $stmt->bindParam(':end_date', $date_range['end']);
            $stmt->execute();
            $qr_result = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get total attendance for adoption rate calculation
            $total_attendance_query = "
                SELECT COUNT(*) as total_attendance
                FROM attendance
                WHERE attendance_date BETWEEN :start_date AND :end_date
            ";

            $stmt = $conn->prepare($total_attendance_query);
            $stmt->bindParam(':start_date', $date_range['start']);
            $stmt->bindParam(':end_date', $date_range['end']);
            $stmt->execute();
            $total_result = $stmt->fetch(PDO::FETCH_ASSOC);

            // Calculate metrics
            $total_sessions = (int)($sessions_result['total_sessions'] ?? 0);
            $qr_attendance = (int)($qr_result['qr_attendance_count'] ?? 0);
            $total_attendance = (int)($total_result['total_attendance'] ?? 0);
            $unique_qr_members = (int)($qr_result['unique_qr_members'] ?? 0);

            // Calculate adoption rate
            $adoption_rate = $total_attendance > 0 ? ($qr_attendance / $total_attendance) * 100 : 0;

            return [
                'total_sessions' => $total_sessions,
                'total_qr_attendance' => $qr_attendance,
                'qr_adoption_rate' => round($adoption_rate, 1),
                'avg_response_time' => 5.2, // Placeholder - could be calculated from actual scan times
                'unique_members' => $unique_qr_members,
                'total_attendance' => $total_attendance
            ];

        } catch (Exception $e) {
            error_log("Error getting overview metrics: " . $e->getMessage());
            return [
                'total_sessions' => 0,
                'total_qr_attendance' => 0,
                'qr_adoption_rate' => 0,
                'avg_response_time' => 0,
                'unique_members' => 0,
                'total_attendance' => 0
            ];
        }
    }

    /**
     * Get trends data for a date range
     */
    private function getTrendsData($date_range, $period_type) {
        try {
            $conn = $this->database->getConnection();

            // Get actual QR attendance trends from attendance records
            $query = "
                SELECT
                    DATE(a.attendance_date) as date,
                    COUNT(CASE WHEN a.marked_via = 'qr' THEN 1 END) as qr_attendance,
                    COUNT(*) as total_attendance
                FROM attendance a
                WHERE a.attendance_date BETWEEN :start_date AND :end_date
                GROUP BY DATE(a.attendance_date)
                ORDER BY date
            ";

            $stmt = $conn->prepare($query);
            $stmt->bindParam(':start_date', $date_range['start']);
            $stmt->bindParam(':end_date', $date_range['end']);
            $stmt->execute();
            $trends_data = $stmt->fetchAll(PDO::FETCH_ASSOC);



            // Format data for charts
            $labels = [];
            $qr_attendance = [];
            $total_attendance = [];

            foreach ($trends_data as $row) {
                $labels[] = date('M j', strtotime($row['date']));
                $qr_attendance[] = (int)$row['qr_attendance'];
                $total_attendance[] = (int)$row['total_attendance'];
            }

            return [
                'chart_data' => [
                    'labels' => $labels,
                    'qr_attendance' => $qr_attendance,
                    'total_attendance' => $total_attendance
                ],
                'daily_metrics' => $trends_data
            ];

        } catch (Exception $e) {
            error_log("Error getting trends data: " . $e->getMessage());
            return ['chart_data' => ['labels' => [], 'qr_attendance' => [], 'total_attendance' => []], 'daily_metrics' => []];
        }
    }

    /**
     * Get usage patterns for a date range
     */
    private function getUsagePatterns($date_range) {
        try {
            $conn = $this->database->getConnection();

            // Hourly distribution
            $hourly_query = "
                SELECT
                    HOUR(a.created_at) as hour,
                    COUNT(*) as count
                FROM attendance a
                WHERE a.attendance_date BETWEEN :start_date AND :end_date
                AND a.marked_via = 'qr'
                GROUP BY HOUR(a.created_at)
                ORDER BY hour
            ";

            $stmt = $conn->prepare($hourly_query);
            $stmt->bindParam(':start_date', $date_range['start']);
            $stmt->bindParam(':end_date', $date_range['end']);
            $stmt->execute();
            $hourly_data = $stmt->fetchAll(PDO::FETCH_ASSOC);



            // Convert to 24-hour array
            $hourly_distribution = array_fill(0, 24, 0);
            foreach ($hourly_data as $hour_data) {
                $hourly_distribution[(int)$hour_data['hour']] = (int)$hour_data['count'];
            }

            return [
                'hourly_distribution' => $hourly_distribution,
                'service_distribution' => [] // Placeholder
            ];

        } catch (Exception $e) {
            error_log("Error getting usage patterns: " . $e->getMessage());
            return ['hourly_distribution' => array_fill(0, 24, 0), 'service_distribution' => []];
        }
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics($date_range) {
        // Placeholder implementation
        return [
            'avg_session_duration' => 45,
            'peak_usage_hour' => 10,
            'conversion_rate' => 85.5
        ];
    }

    /**
     * Calculate engagement scores
     */
    private function calculateEngagementScores($retention_data, $date_range) {
        $scores = [];

        foreach ($retention_data as $member) {
            $attendance_count = (int)$member['attendance_count'];
            $engagement_span = (int)$member['engagement_span'];

            // Simple engagement score calculation
            $score = min(100, ($attendance_count * 10) + ($engagement_span > 0 ? 20 : 0));

            $scores[] = [
                'member_id' => $member['id'],
                'name' => $member['first_name'] . ' ' . $member['last_name'],
                'score' => $score,
                'attendance_count' => $attendance_count
            ];
        }

        return $scores;
    }

    /**
     * Get member engagement summary
     */
    private function getMemberEngagementSummary($retention_data) {
        $total_members = count($retention_data);
        $active_members = count(array_filter($retention_data, function($m) { return $m['attendance_count'] > 0; }));

        return [
            'total_members' => $total_members,
            'active_members' => $active_members,
            'engagement_rate' => $total_members > 0 ? ($active_members / $total_members) * 100 : 0
        ];
    }

    /**
     * Calculate linear forecast
     */
    private function calculateLinearForecast($historical_data) {
        if (count($historical_data) < 2) {
            return [];
        }

        // Simple linear regression for next 30 days
        $forecast = [];
        $last_value = end($historical_data)['attendance'];
        $trend = 0; // Simplified trend calculation

        for ($i = 1; $i <= 30; $i++) {
            $forecast[] = max(0, $last_value + ($trend * $i));
        }

        return $forecast;
    }

    /**
     * Get trend direction
     */
    private function getTrendDirection($historical_data) {
        if (count($historical_data) < 2) {
            return 'stable';
        }

        $recent = array_slice($historical_data, -7); // Last 7 days
        $earlier = array_slice($historical_data, -14, 7); // Previous 7 days

        $recent_avg = array_sum(array_column($recent, 'attendance')) / count($recent);
        $earlier_avg = array_sum(array_column($earlier, 'attendance')) / count($earlier);

        if ($recent_avg > $earlier_avg * 1.05) {
            return 'growing';
        } elseif ($recent_avg < $earlier_avg * 0.95) {
            return 'declining';
        } else {
            return 'stable';
        }
    }

    /**
     * Export QR Analytics data in various formats
     */
    public function qrAnalyticsExport() {
        try {
            $format = isset($_GET['format']) ? sanitize($_GET['format']) : 'pdf';
            $period_type = isset($_GET['period']) ? sanitize($_GET['period']) : 'monthly';
            $period_value = isset($_GET['value']) ? sanitize($_GET['value']) : date('Y-m');

            // Get analytics data
            $analytics_data = $this->getEnhancedQrAnalyticsData($period_type, $period_value);

            switch ($format) {
                case 'pdf':
                    $this->exportAnalyticsPDF($analytics_data, $period_type, $period_value);
                    break;
                case 'excel':
                    $this->exportAnalyticsExcel($analytics_data, $period_type, $period_value);
                    break;
                case 'engagement':
                    $this->exportEngagementReport($analytics_data, $period_type, $period_value);
                    break;
                default:
                    throw new Exception('Invalid export format');
            }

        } catch (Exception $e) {
            error_log("QR Analytics Export Error: " . $e->getMessage());
            set_flash_message('Export failed: ' . $e->getMessage(), 'danger');
            redirect('attendance/qr-analytics');
        }
    }

    /**
     * Export analytics as PDF
     */
    private function exportAnalyticsPDF($analytics_data, $period_type, $period_value) {
        // Set headers for PDF download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="qr-analytics-' . $period_value . '.pdf"');

        // Simple PDF generation (in a real implementation, you'd use a library like TCPDF or FPDF)
        $html = $this->generateAnalyticsHTML($analytics_data, $period_type, $period_value);

        // For now, output as HTML (in production, convert to PDF)
        echo $html;
        exit;
    }

    /**
     * Export analytics as Excel
     */
    private function exportAnalyticsExcel($analytics_data, $period_type, $period_value) {
        // Set headers for Excel download
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="qr-analytics-' . $period_value . '.xls"');

        // Generate Excel-compatible HTML
        echo $this->generateAnalyticsExcel($analytics_data, $period_type, $period_value);
        exit;
    }

    /**
     * Export engagement report
     */
    private function exportEngagementReport($analytics_data, $period_type, $period_value) {
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="member-engagement-' . $period_value . '.csv"');

        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, ['Member Name', 'Engagement Score', 'Attendance Count', 'Last Attendance', 'Status']);

        // CSV data
        $engagement_data = $analytics_data['member_engagement']['engagement_scores'] ?? [];
        foreach ($engagement_data as $member) {
            fputcsv($output, [
                $member['name'],
                $member['score'],
                $member['attendance_count'],
                date('Y-m-d'), // Placeholder
                $member['score'] >= 80 ? 'High' : ($member['score'] >= 60 ? 'Medium' : 'Low')
            ]);
        }

        fclose($output);
        exit;
    }

    /**
     * Generate analytics HTML for PDF export
     */
    private function generateAnalyticsHTML($analytics_data, $period_type, $period_value) {
        $overview = $analytics_data['overview'];
        $period_name = $this->getPeriodDisplayName($period_type, $period_value);

        $html = "
        <!DOCTYPE html>
        <html>
        <head>
            <title>QR Analytics Report - {$period_name}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .metric { display: inline-block; margin: 10px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .metric h3 { margin: 0; color: #333; }
                .metric p { margin: 5px 0; font-size: 24px; font-weight: bold; color: #666; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>QR Analytics Report</h1>
                <h2>{$period_name}</h2>
                <p>Generated on " . date('Y-m-d H:i:s') . "</p>
            </div>

            <div class='metrics'>
                <div class='metric'>
                    <h3>Total QR Sessions</h3>
                    <p>" . number_format($overview['total_sessions'] ?? 0) . "</p>
                </div>
                <div class='metric'>
                    <h3>QR Attendance</h3>
                    <p>" . number_format($overview['total_qr_attendance'] ?? 0) . "</p>
                </div>
                <div class='metric'>
                    <h3>Adoption Rate</h3>
                    <p>" . number_format($overview['qr_adoption_rate'] ?? 0, 1) . "%</p>
                </div>
            </div>

            <h3>Service Performance</h3>
            <table>
                <tr><th>Service</th><th>QR Sessions</th><th>Total Attendance</th><th>Average</th></tr>";

        foreach ($analytics_data['service_performance'] ?? [] as $service) {
            $html .= "<tr>
                <td>" . htmlspecialchars($service['name']) . "</td>
                <td>" . ($service['qr_sessions'] ?? 0) . "</td>
                <td>" . ($service['total_qr_attendance'] ?? 0) . "</td>
                <td>" . number_format($service['avg_attendance'] ?? 0, 1) . "</td>
            </tr>";
        }

        $html .= "</table></body></html>";

        return $html;
    }

    /**
     * Generate Excel-compatible data
     */
    private function generateAnalyticsExcel($analytics_data, $period_type, $period_value) {
        $overview = $analytics_data['overview'];
        $period_name = $this->getPeriodDisplayName($period_type, $period_value);

        $excel = "
        <table border='1'>
            <tr><td colspan='4'><b>QR Analytics Report - {$period_name}</b></td></tr>
            <tr><td colspan='4'>Generated: " . date('Y-m-d H:i:s') . "</td></tr>
            <tr><td></td></tr>
            <tr><td><b>Metric</b></td><td><b>Value</b></td><td></td><td></td></tr>
            <tr><td>Total QR Sessions</td><td>" . ($overview['total_sessions'] ?? 0) . "</td><td></td><td></td></tr>
            <tr><td>QR Attendance</td><td>" . ($overview['total_qr_attendance'] ?? 0) . "</td><td></td><td></td></tr>
            <tr><td>Adoption Rate</td><td>" . number_format($overview['qr_adoption_rate'] ?? 0, 1) . "%</td><td></td><td></td></tr>
            <tr><td></td></tr>
            <tr><td><b>Service</b></td><td><b>QR Sessions</b></td><td><b>Total Attendance</b></td><td><b>Average</b></td></tr>";

        foreach ($analytics_data['service_performance'] ?? [] as $service) {
            $excel .= "<tr>
                <td>" . htmlspecialchars($service['name']) . "</td>
                <td>" . ($service['qr_sessions'] ?? 0) . "</td>
                <td>" . ($service['total_qr_attendance'] ?? 0) . "</td>
                <td>" . number_format($service['avg_attendance'] ?? 0, 1) . "</td>
            </tr>";
        }

        $excel .= "</table>";

        return $excel;
    }

    /**
     * Get quarterly performance data - Completely rewritten for safety
     */
    private function getQuarterlyPerformanceData($year = null) {
        try {
            $conn = $this->database->getConnection();
            $year = $year ?? date('Y');

            // Initialize empty quarterly data structure
            $quarterly_data = [];

            // Define quarters with safe array structure
            $quarter_definitions = [
                'Q1' => ['name' => 'Q1', 'start' => $year . '-01-01', 'end' => $year . '-03-31', 'period' => 'Jan - Mar'],
                'Q2' => ['name' => 'Q2', 'start' => $year . '-04-01', 'end' => $year . '-06-30', 'period' => 'Apr - Jun'],
                'Q3' => ['name' => 'Q3', 'start' => $year . '-07-01', 'end' => $year . '-09-30', 'period' => 'Jul - Sep'],
                'Q4' => ['name' => 'Q4', 'start' => $year . '-10-01', 'end' => $year . '-12-31', 'period' => 'Oct - Dec']
            ];

            // Process each quarter safely
            foreach ($quarter_definitions as $quarter_key => $quarter_info) {
                // Initialize default data structure
                $default_data = [
                    'quarter' => $quarter_info['name'],
                    'year' => (int)$year,
                    'period' => $quarter_info['period'],
                    'qr_sessions' => 0,
                    'qr_attendance' => 0,
                    'total_attendance' => 0,
                    'unique_qr_members' => 0,
                    'unique_members' => 0,
                    'adoption_rate' => 0.0,
                    'growth_rate' => 0.0
                ];

                try {
                    // Safe query execution
                    $query = "
                        SELECT
                            COUNT(DISTINCT qs.id) as qr_sessions,
                            COUNT(DISTINCT CASE WHEN a.marked_via = 'qr' THEN a.id END) as qr_attendance,
                            COUNT(DISTINCT a.id) as total_attendance,
                            COUNT(DISTINCT CASE WHEN a.marked_via = 'qr' THEN a.member_id END) as unique_qr_members,
                            COUNT(DISTINCT a.member_id) as unique_members,
                            CASE
                                WHEN COUNT(DISTINCT a.id) > 0
                                THEN ROUND((COUNT(DISTINCT CASE WHEN a.marked_via = 'qr' THEN a.id END) * 100.0 / COUNT(DISTINCT a.id)), 1)
                                ELSE 0
                            END as adoption_rate
                        FROM attendance a
                        LEFT JOIN attendance_qr_sessions qs ON a.qr_session_id = qs.id
                        WHERE a.attendance_date BETWEEN ? AND ?
                    ";

                    $stmt = $conn->prepare($query);
                    $stmt->execute([$quarter_info['start'], $quarter_info['end']]);
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);

                    // Safely extract data with fallbacks
                    if ($result && is_array($result)) {
                        $default_data['qr_sessions'] = max(0, (int)($result['qr_sessions'] ?? 0));
                        $default_data['qr_attendance'] = max(0, (int)($result['qr_attendance'] ?? 0));
                        $default_data['total_attendance'] = max(0, (int)($result['total_attendance'] ?? 0));
                        $default_data['unique_qr_members'] = max(0, (int)($result['unique_qr_members'] ?? 0));
                        $default_data['unique_members'] = max(0, (int)($result['unique_members'] ?? 0));
                        $default_data['adoption_rate'] = max(0.0, (float)($result['adoption_rate'] ?? 0));
                    }

                } catch (Exception $e) {
                    error_log("QR Analytics: Error processing quarter {$quarter_key}: " . $e->getMessage());
                    // Keep default data structure
                }

                // Safely assign to quarterly data
                $quarterly_data[$quarter_key] = $default_data;
            }

            // Calculate growth rates safely
            $quarterly_data = $this->calculateQuarterlyGrowthSafe($quarterly_data);

            return $quarterly_data;

        } catch (Exception $e) {
            error_log("Error getting quarterly performance data: " . $e->getMessage());
            // Return safe empty structure
            return [
                'Q1' => $this->getEmptyQuarterData('Q1', $year ?? date('Y')),
                'Q2' => $this->getEmptyQuarterData('Q2', $year ?? date('Y')),
                'Q3' => $this->getEmptyQuarterData('Q3', $year ?? date('Y')),
                'Q4' => $this->getEmptyQuarterData('Q4', $year ?? date('Y'))
            ];
        }
    }

    /**
     * Get empty quarter data structure
     */
    private function getEmptyQuarterData($quarter, $year) {
        $periods = [
            'Q1' => 'Jan - Mar',
            'Q2' => 'Apr - Jun',
            'Q3' => 'Jul - Sep',
            'Q4' => 'Oct - Dec'
        ];

        return [
            'quarter' => $quarter,
            'year' => (int)$year,
            'period' => $periods[$quarter] ?? '',
            'qr_sessions' => 0,
            'qr_attendance' => 0,
            'total_attendance' => 0,
            'unique_qr_members' => 0,
            'unique_members' => 0,
            'adoption_rate' => 0.0,
            'growth_rate' => 0.0
        ];
    }

    /**
     * Get quarter month names
     */
    private function getQuarterMonths($quarter) {
        $months = [
            'Q1' => 'Jan - Mar',
            'Q2' => 'Apr - Jun',
            'Q3' => 'Jul - Sep',
            'Q4' => 'Oct - Dec'
        ];
        return $months[$quarter] ?? '';
    }



    /**
     * Calculate quarterly growth rates safely - Completely rewritten
     */
    private function calculateQuarterlyGrowthSafe($quarterly_data) {
        // Ensure we have a valid array
        if (!is_array($quarterly_data) || empty($quarterly_data)) {
            return $quarterly_data;
        }

        // Define quarter order explicitly
        $quarter_order = ['Q1', 'Q2', 'Q3', 'Q4'];

        // Calculate growth for each quarter compared to previous
        for ($i = 1; $i < 4; $i++) { // Only iterate 3 times (Q2, Q3, Q4)
            $current_key = $quarter_order[$i];
            $previous_key = $quarter_order[$i - 1];

            // Verify both quarters exist and are arrays
            if (!array_key_exists($current_key, $quarterly_data) ||
                !array_key_exists($previous_key, $quarterly_data) ||
                !is_array($quarterly_data[$current_key]) ||
                !is_array($quarterly_data[$previous_key])) {
                continue;
            }

            $current_data = $quarterly_data[$current_key];
            $previous_data = $quarterly_data[$previous_key];

            // Safely get attendance values
            $current_attendance = isset($current_data['qr_attendance']) ? (int)$current_data['qr_attendance'] : 0;
            $previous_attendance = isset($previous_data['qr_attendance']) ? (int)$previous_data['qr_attendance'] : 0;

            // Calculate growth rate
            if ($previous_attendance > 0) {
                $growth_rate = (($current_attendance - $previous_attendance) / $previous_attendance) * 100;
                $quarterly_data[$current_key]['growth_rate'] = round($growth_rate, 1);
            } else if ($current_attendance > 0) {
                // New data in current quarter
                $quarterly_data[$current_key]['growth_rate'] = 100.0;
            } else {
                // No data in either quarter
                $quarterly_data[$current_key]['growth_rate'] = 0.0;
            }
        }

        return $quarterly_data;
    }

    /**
     * Legacy method for backward compatibility
     */
    private function calculateQuarterlyGrowth($quarterly_data) {
        return $this->calculateQuarterlyGrowthSafe($quarterly_data);
    }

    /**
     * Calculate simple date range for analytics
     */
    private function calculateSimpleDateRange($period_type, $period_value) {
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d');

        switch ($period_type) {
            case 'quarterly':
                if (preg_match('/^(\d{4})-Q([1-4])$/', $period_value, $matches)) {
                    $year = $matches[1];
                    $quarter = $matches[2];
                    $quarter_starts = [
                        1 => '01-01', 2 => '04-01', 3 => '07-01', 4 => '10-01'
                    ];
                    $quarter_ends = [
                        1 => '03-31', 2 => '06-30', 3 => '09-30', 4 => '12-31'
                    ];
                    $start_date = $year . '-' . $quarter_starts[$quarter];
                    $end_date = $year . '-' . $quarter_ends[$quarter];
                }
                break;
            case 'monthly':
                if (preg_match('/^(\d{4})-(\d{2})$/', $period_value, $matches)) {
                    $year = $matches[1];
                    $month = $matches[2];
                    $start_date = $year . '-' . $month . '-01';
                    $end_date = date('Y-m-t', strtotime($start_date));
                }
                break;
            case 'yearly':
                if (preg_match('/^(\d{4})$/', $period_value, $matches)) {
                    $year = $matches[1];
                    $start_date = $year . '-01-01';
                    $end_date = $year . '-12-31';
                }
                break;
            default:
                $start_date = date('Y-m-01');
                $end_date = date('Y-m-t');
        }

        return ['start' => $start_date, 'end' => $end_date];
    }

    /**
     * Get simple QR analytics data
     */
    private function getSimpleQrAnalytics($date_range) {
        try {
            $conn = $this->database->getConnection();

            // Get overview data
            $overview_query = "
                SELECT
                    COUNT(DISTINCT qs.id) as total_sessions,
                    COUNT(DISTINCT CASE WHEN a.marked_via = 'qr' THEN a.id END) as total_qr_attendance,
                    COUNT(DISTINCT CASE WHEN a.marked_via = 'qr' THEN a.member_id END) as unique_members,
                    ROUND(AVG(qs.attendance_count), 1) as avg_attendance_per_session
                FROM attendance_qr_sessions qs
                LEFT JOIN attendance a ON qs.id = a.qr_session_id
                WHERE qs.attendance_date BETWEEN ? AND ?
            ";

            $stmt = $conn->prepare($overview_query);
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $overview = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get trends data
            $trends_query = "
                SELECT
                    DATE(a.attendance_date) as date,
                    COUNT(CASE WHEN a.marked_via = 'qr' THEN 1 END) as qr_attendance
                FROM attendance a
                WHERE a.attendance_date BETWEEN ? AND ?
                GROUP BY DATE(a.attendance_date)
                ORDER BY date
            ";

            $stmt = $conn->prepare($trends_query);
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $trends_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Format trends data
            $labels = [];
            $qr_attendance = [];
            foreach ($trends_data as $row) {
                $labels[] = date('M j', strtotime($row['date']));
                $qr_attendance[] = (int)$row['qr_attendance'];
            }

            // Get usage patterns
            $patterns_query = "
                SELECT
                    HOUR(a.created_at) as hour,
                    COUNT(*) as count
                FROM attendance a
                WHERE a.marked_via = 'qr'
                AND a.attendance_date BETWEEN ? AND ?
                GROUP BY HOUR(a.created_at)
                ORDER BY hour
            ";

            $stmt = $conn->prepare($patterns_query);
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $hourly_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $hourly_distribution = array_fill(0, 24, 0);
            foreach ($hourly_data as $hour_data) {
                $hourly_distribution[(int)$hour_data['hour']] = (int)$hour_data['count'];
            }

            return [
                'overview' => [
                    'total_sessions' => (int)($overview['total_sessions'] ?? 0),
                    'total_qr_attendance' => (int)($overview['total_qr_attendance'] ?? 0),
                    'unique_members' => (int)($overview['unique_members'] ?? 0),
                    'avg_attendance_per_session' => (float)($overview['avg_attendance_per_session'] ?? 0)
                ],
                'trends' => [
                    'chart_data' => [
                        'labels' => $labels,
                        'qr_attendance' => $qr_attendance
                    ]
                ],
                'usage_patterns' => [
                    'hourly_distribution' => $hourly_distribution
                ],
                'date_ranges' => [
                    'current' => $date_range
                ]
            ];

        } catch (Exception $e) {
            error_log("Simple QR Analytics Error: " . $e->getMessage());
            return [
                'overview' => [
                    'total_sessions' => 0,
                    'total_qr_attendance' => 0,
                    'unique_members' => 0,
                    'avg_attendance_per_session' => 0
                ],
                'trends' => ['chart_data' => ['labels' => [], 'qr_attendance' => []]],
                'usage_patterns' => ['hourly_distribution' => array_fill(0, 24, 0)],
                'date_ranges' => ['current' => $date_range]
            ];
        }
    }

    /**
     * Get simple quarterly data
     */
    private function getSimpleQuarterlyData($year) {
        try {
            $conn = $this->database->getConnection();
            $quarterly_data = [];

            $quarters = [
                'Q1' => ['start' => $year . '-01-01', 'end' => $year . '-03-31', 'period' => 'Jan - Mar'],
                'Q2' => ['start' => $year . '-04-01', 'end' => $year . '-06-30', 'period' => 'Apr - Jun'],
                'Q3' => ['start' => $year . '-07-01', 'end' => $year . '-09-30', 'period' => 'Jul - Sep'],
                'Q4' => ['start' => $year . '-10-01', 'end' => $year . '-12-31', 'period' => 'Oct - Dec']
            ];

            foreach ($quarters as $quarter => $dates) {
                $query = "
                    SELECT
                        COUNT(DISTINCT qs.id) as qr_sessions,
                        COUNT(DISTINCT CASE WHEN a.marked_via = 'qr' THEN a.id END) as qr_attendance,
                        COUNT(DISTINCT a.id) as total_attendance
                    FROM attendance a
                    LEFT JOIN attendance_qr_sessions qs ON a.qr_session_id = qs.id
                    WHERE a.attendance_date BETWEEN ? AND ?
                ";

                $stmt = $conn->prepare($query);
                $stmt->execute([$dates['start'], $dates['end']]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                $qr_attendance = (int)($result['qr_attendance'] ?? 0);
                $total_attendance = (int)($result['total_attendance'] ?? 0);
                $adoption_rate = $total_attendance > 0 ? round(($qr_attendance / $total_attendance) * 100, 1) : 0;

                $quarterly_data[$quarter] = [
                    'quarter' => $quarter,
                    'year' => (int)$year,
                    'period' => $dates['period'],
                    'qr_sessions' => (int)($result['qr_sessions'] ?? 0),
                    'qr_attendance' => $qr_attendance,
                    'total_attendance' => $total_attendance,
                    'adoption_rate' => $adoption_rate,
                    'growth_rate' => 0
                ];
            }

            return $quarterly_data;

        } catch (Exception $e) {
            error_log("Simple Quarterly Data Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update QR session attendance counts to ensure accuracy
     */
    public function updateQrSessionCounts() {
        try {
            $conn = $this->database->getConnection();

            // Update attendance_count in QR sessions based on actual attendance records
            $update_query = "
                UPDATE attendance_qr_sessions qs
                SET attendance_count = (
                    SELECT COUNT(*)
                    FROM attendance a
                    WHERE a.qr_session_id = qs.id
                    AND a.marked_via = 'qr'
                ),
                last_used_at = (
                    SELECT MAX(a.created_at)
                    FROM attendance a
                    WHERE a.qr_session_id = qs.id
                    AND a.marked_via = 'qr'
                )
                WHERE qs.id IN (
                    SELECT DISTINCT qr_session_id
                    FROM attendance
                    WHERE qr_session_id IS NOT NULL
                )
            ";

            $stmt = $conn->prepare($update_query);
            $stmt->execute();
            $updated_sessions = $stmt->rowCount();

            return [
                'success' => true,
                'updated_sessions' => $updated_sessions,
                'message' => "Updated {$updated_sessions} QR session attendance counts"
            ];

        } catch (Exception $e) {
            error_log("Error updating QR session counts: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get real-time analytics data with accurate counts
     */
    public function getRealTimeAnalytics() {
        try {
            // First update QR session counts
            $this->updateQrSessionCounts();

            // Then get current analytics
            return $this->getEnhancedQrAnalyticsData('monthly', date('Y-m'));

        } catch (Exception $e) {
            error_log("Error getting real-time analytics: " . $e->getMessage());
            return $this->getDefaultAnalyticsStructure();
        }
    }

    /**
     * Advanced QR data export with filtering options
     *
     * @return void
     */
    public function qrAdvancedExport() {
        try {
            // Get export parameters
            $export_type = isset($_GET['type']) ? sanitize($_GET['type']) : 'sessions';
            $format = isset($_GET['format']) ? sanitize($_GET['format']) : 'csv';
            $date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : date('Y-m-d', strtotime('-30 days'));
            $date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : date('Y-m-d');
            $service_id = isset($_GET['service_id']) ? (int)$_GET['service_id'] : null;
            $status = isset($_GET['status']) ? sanitize($_GET['status']) : null;

            require_once 'models/AttendanceQrSession.php';
            $qr_session = new AttendanceQrSession($this->database->getConnection());

            switch ($export_type) {
                case 'sessions':
                    $this->exportQrSessions($date_from, $date_to, $service_id, $status, $format);
                    break;
                case 'attendance':
                    $this->exportQrAttendance($date_from, $date_to, $service_id, $format);
                    break;
                case 'analytics':
                    $this->exportQrAnalytics($date_from, $date_to, $service_id, $format);
                    break;
                default:
                    throw new Exception('Invalid export type');
            }

        } catch (Exception $e) {
            error_log("QR Advanced Export Error: " . $e->getMessage());
            set_flash_message('An error occurred during export: ' . $e->getMessage(), 'danger');
            redirect('attendance/qr');
        }
    }

    /**
     * Export QR sessions data
     */
    private function exportQrSessions($date_from, $date_to, $service_id, $status, $format) {
        $conn = $this->database->getConnection();

        $where_conditions = ["qs.attendance_date BETWEEN :date_from AND :date_to"];
        $params = [':date_from' => $date_from, ':date_to' => $date_to];

        if ($service_id) {
            $where_conditions[] = "qs.service_id = :service_id";
            $params[':service_id'] = $service_id;
        }

        if ($status) {
            $where_conditions[] = "qs.status = :status";
            $params[':status'] = $status;
        }

        $query = "SELECT
                    qs.id,
                    qs.attendance_date,
                    s.name as service_name,
                    qs.status,
                    qs.attendance_count,
                    qs.created_at,
                    qs.expires_at,
                    qs.last_used_at,
                    u.username as created_by,
                    TIMESTAMPDIFF(MINUTE, qs.created_at, qs.expires_at) as duration_minutes,
                    TIMESTAMPDIFF(MINUTE, qs.created_at, COALESCE(qs.last_used_at, qs.expires_at)) as active_minutes,
                    CASE
                        WHEN qs.attendance_count > 0 THEN
                            ROUND((qs.attendance_count / TIMESTAMPDIFF(MINUTE, qs.created_at, COALESCE(qs.last_used_at, qs.expires_at))) * 60, 2)
                        ELSE 0
                    END as attendance_per_hour
                  FROM attendance_qr_sessions qs
                  JOIN services s ON qs.service_id = s.id
                  LEFT JOIN users u ON qs.created_by = u.id
                  WHERE " . implode(' AND ', $where_conditions) . "
                  ORDER BY qs.attendance_date DESC, qs.created_at DESC";

        $stmt = $conn->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $filename = "qr_sessions_" . $date_from . "_to_" . $date_to . "." . $format;

        if ($format === 'json') {
            $this->outputJsonExport($sessions, $filename);
        } else {
            $this->outputCsvExport($sessions, $filename, [
                'ID', 'Date', 'Service', 'Status', 'Attendance Count', 'Created At', 'Expires At',
                'Last Used', 'Created By', 'Duration (min)', 'Active Duration (min)', 'Attendance/Hour'
            ]);
        }
    }

    /**
     * Export QR attendance records
     */
    private function exportQrAttendance($date_from, $date_to, $service_id, $format) {
        $conn = $this->database->getConnection();

        $where_conditions = ["a.attendance_date BETWEEN :date_from AND :date_to", "a.marked_via = 'qr'"];
        $params = [':date_from' => $date_from, ':date_to' => $date_to];

        if ($service_id) {
            $where_conditions[] = "a.service_id = :service_id";
            $params[':service_id'] = $service_id;
        }

        $query = "SELECT
                    a.id,
                    a.attendance_date,
                    s.name as service_name,
                    m.first_name,
                    m.last_name,
                    m.phone_number,
                    m.department,
                    m.gender,
                    a.status,
                    a.created_at as marked_at,
                    qs.id as qr_session_id,
                    TIMESTAMPDIFF(MINUTE, qs.created_at, a.created_at) as response_time_minutes
                  FROM attendance a
                  JOIN members m ON a.member_id = m.id
                  JOIN services s ON a.service_id = s.id
                  LEFT JOIN attendance_qr_sessions qs ON a.qr_session_id = qs.id
                  WHERE " . implode(' AND ', $where_conditions) . "
                  ORDER BY a.attendance_date DESC, a.created_at DESC";

        $stmt = $conn->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $attendance = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $filename = "qr_attendance_" . $date_from . "_to_" . $date_to . "." . $format;

        if ($format === 'json') {
            $this->outputJsonExport($attendance, $filename);
        } else {
            $this->outputCsvExport($attendance, $filename, [
                'ID', 'Date', 'Service', 'First Name', 'Last Name', 'Phone', 'Department',
                'Gender', 'Status', 'Marked At', 'QR Session ID', 'Response Time (min)'
            ]);
        }
    }

    /**
     * Export QR analytics data
     */
    private function exportQrAnalytics($date_from, $date_to, $service_id, $format) {
        $analytics_data = $this->getQrAnalyticsData(30); // Get comprehensive analytics

        // Create analytics summary
        $analytics_summary = [
            'export_date' => date('Y-m-d H:i:s'),
            'date_range' => $date_from . ' to ' . $date_to,
            'overview' => $analytics_data['overview'],
            'performance_metrics' => $analytics_data['performance_metrics'],
            'usage_patterns' => $analytics_data['usage_patterns'],
            'trends' => $analytics_data['trends']
        ];

        $filename = "qr_analytics_" . $date_from . "_to_" . $date_to . "." . $format;

        if ($format === 'json') {
            $this->outputJsonExport($analytics_summary, $filename);
        } else {
            // For CSV, flatten the analytics data
            $flattened_data = [];
            foreach ($analytics_data['trends']['daily_metrics'] as $metric) {
                $flattened_data[] = $metric;
            }

            $this->outputCsvExport($flattened_data, $filename, [
                'Date', 'Sessions', 'QR Attendance', 'Avg per Session'
            ]);
        }
    }

    /**
     * Output JSON export
     */
    private function outputJsonExport($data, $filename) {
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        echo json_encode($data, JSON_PRETTY_PRINT);
        exit;
    }

    /**
     * Output CSV export
     */
    private function outputCsvExport($data, $filename, $headers) {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        $output = fopen('php://output', 'w');

        // Write headers
        fputcsv($output, $headers);

        // Write data
        foreach ($data as $row) {
            fputcsv($output, array_values($row));
        }

        fclose($output);
        exit;
    }

    /**
     * Display QR export interface
     *
     * @return void
     */
    public function qrExportInterface() {
        try {
            // Get services for filter dropdown
            require_once 'models/Service.php';
            $service_model = new Service($this->database->getConnection());
            $services = $service_model->getAllServices();

            // Set page title and active page
            $page_title = 'QR Data Export - ICGC Emmanuel Temple';
            $active_page = 'attendance';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/attendance/qr-export-interface.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("QR Export Interface Error: " . $e->getMessage());
            set_flash_message('An error occurred while loading export interface', 'danger');
            redirect('attendance/qr');
        }
    }

    /**
     * Get member engagement data for QR analytics
     */
    private function getMemberEngagementData($date_range) {
        try {
            $conn = $this->database->getConnection();

            // Get total unique members who used QR in the period
            $stmt = $conn->prepare("
                SELECT COUNT(DISTINCT a.member_id) as total_qr_members
                FROM attendance a
                WHERE a.marked_via = 'qr'
                AND a.attendance_date BETWEEN ? AND ?
            ");
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $total_qr_members = $stmt->fetchColumn();

            // Get member engagement levels based on attendance frequency
            $stmt = $conn->prepare("
                SELECT
                    m.id as member_id,
                    CONCAT(m.first_name, ' ', m.last_name) as name,
                    COUNT(a.id) as attendance_count,
                    COUNT(DISTINCT DATE(a.attendance_date)) as unique_days,
                    CASE
                        WHEN COUNT(a.id) >= 8 THEN 'regular'
                        WHEN COUNT(a.id) >= 3 THEN 'occasional'
                        ELSE 'at_risk'
                    END as engagement_level
                FROM members m
                LEFT JOIN attendance a ON m.id = a.member_id
                    AND a.marked_via = 'qr'
                    AND a.attendance_date BETWEEN ? AND ?
                WHERE m.member_status = 'active'
                GROUP BY m.id, m.first_name, m.last_name
                HAVING COUNT(a.id) > 0
            ");
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $engagement_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Count engagement levels
            $regular_count = 0;
            $occasional_count = 0;
            $at_risk_count = 0;

            foreach ($engagement_data as $member) {
                switch ($member['engagement_level']) {
                    case 'regular':
                        $regular_count++;
                        break;
                    case 'occasional':
                        $occasional_count++;
                        break;
                    case 'at_risk':
                        $at_risk_count++;
                        break;
                }
            }

            return [
                'regular_attendees' => $regular_count,
                'occasional_attendees' => $occasional_count,
                'at_risk_members' => $at_risk_count,
                'total_engaged_members' => $total_qr_members,
                'engagement_details' => $engagement_data
            ];

        } catch (Exception $e) {
            error_log("Error getting member engagement data: " . $e->getMessage());
            return [
                'regular_attendees' => 0,
                'occasional_attendees' => 0,
                'at_risk_members' => 0,
                'total_engaged_members' => 0,
                'engagement_details' => []
            ];
        }
    }

    /**
     * Get gender distribution data for QR analytics
     */
    private function getGenderDistributionData($date_range) {
        try {
            $conn = $this->database->getConnection();

            // Get gender distribution of QR attendance
            $stmt = $conn->prepare("
                SELECT
                    m.gender,
                    COUNT(DISTINCT a.member_id) as member_count,
                    COUNT(a.id) as attendance_count
                FROM attendance a
                JOIN members m ON a.member_id = m.id
                WHERE a.marked_via = 'qr'
                AND a.attendance_date BETWEEN ? AND ?
                AND m.gender IN ('male', 'female')
                GROUP BY m.gender
            ");
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $gender_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $male_count = 0;
            $female_count = 0;
            $total_count = 0;

            foreach ($gender_data as $row) {
                if ($row['gender'] === 'male') {
                    $male_count = $row['member_count'];
                } elseif ($row['gender'] === 'female') {
                    $female_count = $row['member_count'];
                }
                $total_count += $row['member_count'];
            }

            $male_percentage = $total_count > 0 ? ($male_count / $total_count) * 100 : 0;
            $female_percentage = $total_count > 0 ? ($female_count / $total_count) * 100 : 0;

            return [
                'male_count' => $male_count,
                'female_count' => $female_count,
                'male_percentage' => $male_percentage,
                'female_percentage' => $female_percentage,
                'total_count' => $total_count
            ];

        } catch (Exception $e) {
            error_log("Error getting gender distribution data: " . $e->getMessage());
            return [
                'male_count' => 0,
                'female_count' => 0,
                'male_percentage' => 0,
                'female_percentage' => 0,
                'total_count' => 0
            ];
        }
    }

    /**
     * Get department distribution data for QR analytics
     */
    private function getDepartmentDistributionData($date_range) {
        try {
            $conn = $this->database->getConnection();

            // Get department distribution of QR attendance
            $stmt = $conn->prepare("
                SELECT
                    m.department,
                    COUNT(DISTINCT a.member_id) as member_count,
                    COUNT(a.id) as attendance_count
                FROM attendance a
                JOIN members m ON a.member_id = m.id
                WHERE a.marked_via = 'qr'
                AND a.attendance_date BETWEEN ? AND ?
                GROUP BY m.department
                ORDER BY member_count DESC
            ");
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $dept_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $total_members = 0;
            $departments = [];

            // Calculate total first
            foreach ($dept_data as $row) {
                $total_members += $row['member_count'];
            }

            // Get department display names from database
            $database = new Database();
            $conn = $database->getConnection();

            $stmt = $conn->prepare("SELECT name, display_name FROM departments WHERE is_active = 1");
            $stmt->execute();
            $dept_names_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $department_names = [];
            foreach ($dept_names_data as $dept) {
                $department_names[$dept['name']] = $dept['display_name'];
            }

            // Build department data with percentages
            foreach ($dept_data as $row) {
                $percentage = $total_members > 0 ? ($row['member_count'] / $total_members) * 100 : 0;
                $display_name = $department_names[$row['department']] ?? ucfirst(str_replace('_', ' ', $row['department']));

                $departments[$display_name] = [
                    'count' => $row['member_count'],
                    'percentage' => $percentage,
                    'attendance_count' => $row['attendance_count']
                ];
            }

            return $departments;

        } catch (Exception $e) {
            error_log("Error getting department distribution data: " . $e->getMessage());
            return [
                'Head Pastor' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Choir' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Media' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Ushering' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Children' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'New Breed' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Protocol' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Welfare' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Intercessors' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Traffic' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Administration' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Instrumentalist' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Deacon' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'Pastor\'s Wife' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0],
                'No Department' => ['count' => 0, 'percentage' => 0, 'attendance_count' => 0]
            ];
        }
    }

    /**
     * QR Analytics Report Selection Page
     */
    public function qrAnalyticsReport() {
        try {
            // Check if this is a report generation request
            if (isset($_GET['generate']) && $_GET['generate'] === 'true') {
                $this->generateQrAnalyticsReport();
                return;
            }

            // Otherwise, show the report selection page
            $this->showReportSelectionPage();

        } catch (Exception $e) {
            error_log("QR Analytics Report Error: " . $e->getMessage());
            $this->showErrorPage('Failed to load report page: ' . $e->getMessage());
        }
    }

    /**
     * Show report selection page
     */
    private function showReportSelectionPage() {
        $page_title = 'QR Analytics Reports';
        $active_page = 'attendance';

        // Get available years for selection
        $available_years = $this->getAvailableYears();

        // Start output buffering
        ob_start();

        // Include the report selection view
        include 'views/attendance/qr-report-selection.php';

        // Get content
        $content = ob_get_clean();

        // Include main layout
        include 'views/layouts/main.php';
    }

    /**
     * Generate the actual report
     */
    private function generateQrAnalyticsReport() {
        $format = isset($_GET['format']) ? sanitize($_GET['format']) : 'detailed';
        $period_type = isset($_GET['period']) ? sanitize($_GET['period']) : 'monthly';
        $period_value = isset($_GET['value']) ? sanitize($_GET['value']) : date('Y-m');
        $start_date = isset($_GET['start_date']) ? sanitize($_GET['start_date']) : null;
        $end_date = isset($_GET['end_date']) ? sanitize($_GET['end_date']) : null;

        // Calculate date range
        if ($start_date && $end_date) {
            $date_range = ['start' => $start_date, 'end' => $end_date];
        } else {
            $date_range = $this->calculateSimpleDateRange($period_type, $period_value);
        }

        // Get comprehensive analytics data
        $analytics_data = $this->getSimpleQrAnalytics($date_range);
        $member_engagement = $this->getMemberEngagementData($date_range);
        $gender_distribution = $this->getGenderDistributionData($date_range);
        $department_distribution = $this->getDepartmentDistributionData($date_range);
        $quarterly_performance = $this->getSimpleQuarterlyData(date('Y'));
        $trends_data = $this->getTrendsData($date_range, $period_type);
        $service_performance = $this->getServicePerformanceData($date_range);

        // Prepare comprehensive report data
        $report_data = [
            'period' => [
                'type' => $period_type,
                'value' => $period_value,
                'start_date' => $date_range['start'],
                'end_date' => $date_range['end'],
                'custom_range' => $start_date && $end_date
            ],
            'overview' => $analytics_data['overview'] ?? [],
            'member_engagement' => $member_engagement,
            'gender_distribution' => $gender_distribution,
            'department_distribution' => $department_distribution,
            'quarterly_performance' => $quarterly_performance,
            'trends_data' => $trends_data,
            'service_performance' => $service_performance,
            'generated_at' => date('Y-m-d H:i:s'),
            'generated_by' => $_SESSION['user']['username'] ?? 'System'
        ];

        switch ($format) {
            case 'detailed':
                $this->generateDetailedReport($report_data);
                break;
            case 'pdf':
                $this->generatePDFReport($report_data);
                break;
            case 'excel':
                $this->generateExcelReport($report_data);
                break;
            case 'summary':
                $this->generateSummaryReport($report_data);
                break;
            default:
                $this->generateDetailedReport($report_data);
        }
    }

    /**
     * Get available years from QR attendance data
     */
    private function getAvailableYears() {
        try {
            $conn = $this->database->getConnection();
            $stmt = $conn->prepare("
                SELECT DISTINCT YEAR(attendance_date) as year
                FROM attendance_qr_sessions
                ORDER BY year DESC
            ");
            $stmt->execute();
            $years = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (empty($years)) {
                $years = [date('Y')]; // Default to current year if no data
            }

            return $years;
        } catch (Exception $e) {
            return [date('Y')]; // Default to current year on error
        }
    }

    /**
     * Generate detailed HTML report
     */
    private function generateDetailedReport($data) {
        // Set page title and prepare data for main layout
        $page_title = 'QR Analytics Detailed Report';
        $report_data = $data;

        // Start output buffering to capture the report content
        ob_start();

        // Include custom CSS for the report
        echo '<style>
        body {
            font-family: "Times New Roman", serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
        }

        .report-container {
            max-width: 210mm;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 297mm;
        }

        .report-header {
            background: white;
            border-bottom: 3px solid #2c3e50;
            padding: 40px 40px 30px 40px;
            text-align: center;
        }

        .report-title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            letter-spacing: 1px;
        }

        .church-name {
            font-size: 18px;
            color: #666;
            margin-bottom: 20px;
            font-weight: normal;
        }

        .report-subtitle {
            font-size: 16px;
            color: #888;
            margin-bottom: 30px;
        }

        .report-meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .meta-item {
            text-align: left;
        }

        .meta-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .meta-value {
            font-size: 14px;
            color: #2c3e50;
            font-weight: normal;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }

        .section-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2c3e50;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border: 1px solid #dee2e6;
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
            line-height: 1;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .engagement-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }

        .engagement-card {
            background: white;
            padding: 20px;
            border: 1px solid #dee2e6;
            text-align: center;
        }

        .engagement-card.regular { border-left: 4px solid #28a745; }
        .engagement-card.occasional { border-left: 4px solid #ffc107; }
        .engagement-card.at-risk { border-left: 4px solid #dc3545; }

        .gender-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .gender-card {
            background: white;
            padding: 25px;
            border: 1px solid #dee2e6;
            text-align: center;
        }

        .gender-card.male {
            border-left: 4px solid #007bff;
        }

        .gender-card.female {
            border-left: 4px solid #e83e8c;
        }

        .gender-number {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .gender-label {
            color: #666;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
        }

        .data-table th {
            background: #2c3e50;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
        }

        .data-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #2c3e50;
            background: white;
            color: #2c3e50;
            text-decoration: none;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .btn:hover {
            background: #2c3e50;
            color: white;
        }

        .btn-primary {
            background: #2c3e50;
            color: white;
        }

        .btn-primary:hover {
            background: #1a252f;
        }

        .report-footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
            color: #666;
            font-size: 12px;
        }

        @media print {
            body { background: white; }
            .report-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
            .action-buttons { display: none; }
            .section {
                page-break-inside: avoid;
                margin-bottom: 30px;
            }
            .stats-grid { page-break-inside: avoid; }
            .data-table { page-break-inside: avoid; }
        }

        @media (max-width: 768px) {
            .report-container { margin: 10px; }
            .content { padding: 20px; }
            .stats-grid { grid-template-columns: repeat(2, 1fr); }
            .engagement-grid { grid-template-columns: 1fr; }
            .gender-grid { grid-template-columns: 1fr; }
            .report-meta { grid-template-columns: 1fr; }
        }
    </style>';

        // Add breadcrumb navigation
        echo '<div class="mb-6">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="' . BASE_URL . 'dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                            <i class="fas fa-home mr-2"></i>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <a href="' . BASE_URL . 'attendance" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">Attendance</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <a href="' . BASE_URL . 'attendance/qr-analytics" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">QR Analytics</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Detailed Report</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>';

        // Report content starts here
        echo '<div class="report-container">';
        echo '<div class="report-header">
            <div class="header-content">
                <h1 class="report-title">QR ATTENDANCE ANALYTICS REPORT</h1>
                <div class="church-name">ICGC Emmanuel Temple</div>
                <div class="report-subtitle">Church Management System</div>

                <div class="report-meta">
                    <div class="meta-item">
                        <div class="meta-label">Report Period</div>
                        <div class="meta-value">' . ucfirst($data['period']['type']) . ' - ' . htmlspecialchars($data['period']['value']) . '</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Date Range</div>
                        <div class="meta-value">' . date('F j, Y', strtotime($data['period']['start_date'])) . ' to ' . date('F j, Y', strtotime($data['period']['end_date'])) . '</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Generated By</div>
                        <div class="meta-value">' . htmlspecialchars($data['generated_by']) . '</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Generated On</div>
                        <div class="meta-value">' . date('F j, Y \a\t g:i A', strtotime($data['generated_at'])) . '</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print"></i> Print Report
            </button>
            <a href="' . BASE_URL . 'attendance/qr-analytics-report?period=' . $data['period']['type'] . '&value=' . $data['period']['value'] . '&format=excel" class="btn btn-secondary">
                <i class="fas fa-download"></i> Download CSV
            </a>
            <a href="' . BASE_URL . 'attendance/qr-analytics" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>

        <div class="content">';

        // Overview Section
        echo '<div class="section">
            <div class="section-header">
                <h2 class="section-title">Executive Summary</h2>
            </div>
            <div class="stats-grid">';

        $overview = $data['overview'];
        echo '<div class="stat-card">
                <div class="stat-number">' . number_format($overview['total_sessions'] ?? 0) . '</div>
                <div class="stat-label">QR Sessions Created</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">' . number_format($overview['total_qr_attendance'] ?? 0) . '</div>
                <div class="stat-label">Total QR Attendance</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">' . number_format($overview['unique_members'] ?? 0) . '</div>
                <div class="stat-label">Unique Members</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">' . number_format($overview['avg_attendance_per_session'] ?? 0, 1) . '</div>
                <div class="stat-label">Average per Session</div>
              </div>';
        echo '</div></div>';

        // Member Engagement Section
        echo '<div class="section">
            <div class="section-header">
                <h2 class="section-title">Member Engagement Analysis</h2>
            </div>
            <div class="engagement-grid">';

        $engagement = $data['member_engagement'];
        echo '<div class="engagement-card regular">
                <div class="stat-number">' . number_format($engagement['regular_attendees'] ?? 0) . '</div>
                <div class="stat-label">Regular Attendees<br><small>8+ QR attendances</small></div>
              </div>
              <div class="engagement-card occasional">
                <div class="stat-number">' . number_format($engagement['occasional_attendees'] ?? 0) . '</div>
                <div class="stat-label">Occasional Attendees<br><small>3-7 QR attendances</small></div>
              </div>
              <div class="engagement-card at-risk">
                <div class="stat-number">' . number_format($engagement['at_risk_members'] ?? 0) . '</div>
                <div class="stat-label">At-Risk Members<br><small>1-2 QR attendances</small></div>
              </div>';
        echo '</div></div>';

        // Gender Distribution Section
        echo '<div class="section">
            <div class="section-header">
                <h2 class="section-title">Gender Distribution</h2>
            </div>
            <div class="gender-grid">';

        $gender = $data['gender_distribution'];
        echo '<div class="gender-card male">
                <div class="gender-number">' . number_format($gender['male_count'] ?? 0) . '</div>
                <div class="gender-label">Male Members (' . number_format($gender['male_percentage'] ?? 0, 1) . '%)</div>
              </div>
              <div class="gender-card female">
                <div class="gender-number">' . number_format($gender['female_count'] ?? 0) . '</div>
                <div class="gender-label">Female Members (' . number_format($gender['female_percentage'] ?? 0, 1) . '%)</div>
              </div>';
        echo '</div></div>';

        // Department Distribution Section
        echo '<div class="section">
            <div class="section-header">
                <h2 class="section-title">Department Distribution</h2>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Department</th>
                        <th>Members</th>
                        <th>Percentage</th>
                        <th>Total Attendance</th>
                    </tr>
                </thead>
                <tbody>';

        foreach ($data['department_distribution'] as $dept => $dept_data) {
            if ($dept_data['count'] > 0) {
                echo '<tr>
                    <td>' . htmlspecialchars($dept) . '</td>
                    <td>' . number_format($dept_data['count']) . '</td>
                    <td>' . number_format($dept_data['percentage'], 1) . '%</td>
                    <td>' . number_format($dept_data['attendance_count'] ?? 0) . '</td>
                </tr>';
            }
        }

        echo '</tbody></table>
        </div>';

        // Service Performance Section
        if (!empty($data['service_performance'])) {
            echo '<div class="section">
                <div class="section-header">
                    <h2 class="section-title">Service Performance Analysis</h2>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Service</th>
                            <th>QR Sessions</th>
                            <th>QR Attendance</th>
                            <th>Adoption Rate</th>
                            <th>Unique Members</th>
                        </tr>
                    </thead>
                    <tbody>';

            foreach ($data['service_performance'] as $service) {
                echo '<tr>
                    <td>' . htmlspecialchars($service['service_name']) . '<br>
                        <small>' . htmlspecialchars($service['service_time']) . '</small></td>
                    <td>' . number_format($service['qr_sessions']) . '</td>
                    <td>' . number_format($service['qr_attendance']) . '</td>
                    <td>' . number_format($service['qr_adoption_rate'], 1) . '%</td>
                    <td>' . number_format($service['unique_qr_members']) . '</td>
                </tr>';
            }

            echo '</tbody></table>
            </div>';
        }

        // Quarterly Performance Section
        if (!empty($data['quarterly_performance'])) {
            echo '<div class="section">
                <div class="section-header">
                    <h2 class="section-title">Quarterly Performance Comparison</h2>
                </div>
                <div class="stats-grid">';

            foreach ($data['quarterly_performance'] as $quarter_data) {
                echo '<div class="stat-card">
                    <div class="stat-number">' . number_format($quarter_data['qr_attendance']) . '</div>
                    <div class="stat-label">' . htmlspecialchars($quarter_data['quarter']) . ' ' . $quarter_data['year'] . '<br>
                        <small>' . number_format($quarter_data['adoption_rate'], 1) . '% adoption rate</small></div>
                </div>';
            }

            echo '</div>
            </div>';
        }

        echo '</div> <!-- End content -->

        <div class="report-footer">
            <p><strong>ICGC Emmanuel Temple</strong> - Church Management System</p>
            <p>Generated on ' . date('l, F j, Y \a\t g:i A') . '</p>
            <p><em>This report contains confidential church data. Please handle with care.</em></p>
        </div>

    </div> <!-- End report-container -->';

        // Get the report content
        $content = ob_get_clean();

        // Set active page for navigation
        $active_page = 'attendance';

        // Include the main layout
        include 'views/layouts/main.php';
    }

    /**
     * Generate PDF report (placeholder)
     */
    private function generatePDFReport($data) {
        // For now, redirect to detailed report
        // In the future, implement PDF generation using libraries like TCPDF or DOMPDF
        $this->generateDetailedReport($data);
    }

    /**
     * Generate Excel report (placeholder)
     */
    private function generateExcelReport($data) {
        // For now, generate CSV format
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="qr-analytics-' . $data['period']['value'] . '.csv"');

        $output = fopen('php://output', 'w');

        // Overview data
        fputcsv($output, ['QR Analytics Report - ' . $data['period']['type'] . ' ' . $data['period']['value']]);
        fputcsv($output, ['Generated: ' . $data['generated_at']]);
        fputcsv($output, []);

        // Overview stats
        fputcsv($output, ['Overview Statistics']);
        fputcsv($output, ['Metric', 'Value']);
        fputcsv($output, ['QR Sessions', $data['overview']['total_sessions'] ?? 0]);
        fputcsv($output, ['QR Attendance', $data['overview']['total_qr_attendance'] ?? 0]);
        fputcsv($output, ['Unique Members', $data['overview']['unique_members'] ?? 0]);
        fputcsv($output, ['Avg per Session', $data['overview']['avg_attendance_per_session'] ?? 0]);
        fputcsv($output, []);

        // Department distribution
        fputcsv($output, ['Department Distribution']);
        fputcsv($output, ['Department', 'Members', 'Percentage', 'Total Attendance']);
        foreach ($data['department_distribution'] as $dept => $dept_data) {
            if ($dept_data['count'] > 0) {
                fputcsv($output, [$dept, $dept_data['count'], $dept_data['percentage'] . '%', $dept_data['attendance_count'] ?? 0]);
            }
        }

        fclose($output);
    }



    /**
     * Get service performance data
     */
    private function getServicePerformanceData($date_range) {
        try {
            $conn = $this->database->getConnection();

            $stmt = $conn->prepare("
                SELECT
                    s.name as service_name,
                    s.time as service_time,
                    COUNT(DISTINCT qs.id) as qr_sessions,
                    COUNT(DISTINCT CASE WHEN a.marked_via = 'qr' THEN a.id END) as qr_attendance,
                    COUNT(DISTINCT a.id) as total_attendance,
                    COUNT(DISTINCT CASE WHEN a.marked_via = 'qr' THEN a.member_id END) as unique_qr_members,
                    ROUND(AVG(CASE WHEN a.marked_via = 'qr' THEN 1 ELSE 0 END) * 100, 1) as qr_adoption_rate
                FROM services s
                LEFT JOIN attendance_qr_sessions qs ON s.id = qs.service_id
                    AND qs.attendance_date BETWEEN ? AND ?
                LEFT JOIN attendance a ON s.id = a.service_id
                    AND a.attendance_date BETWEEN ? AND ?
                GROUP BY s.id, s.name, s.time
                HAVING qr_sessions > 0 OR total_attendance > 0
                ORDER BY qr_attendance DESC
            ");

            $stmt->execute([$date_range['start'], $date_range['end'], $date_range['start'], $date_range['end']]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error getting service performance data: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Generate summary report
     */
    private function generateSummaryReport($data) {
        // Set page title and prepare data for main layout
        $page_title = 'QR Analytics Summary Report';
        $report_data = $data;

        // Start output buffering to capture the report content
        ob_start();

        // Include summary report view
        include 'views/attendance/qr-summary-report.php';

        // Get the report content
        $content = ob_get_clean();

        // Set active page for navigation
        $active_page = 'attendance';

        // Include the main layout
        include 'views/layouts/main.php';
    }

    /**
     * Show error page
     */
    private function showErrorPage($message) {
        $page_title = 'Report Error';
        $active_page = 'attendance';

        ob_start();
        echo '<div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                <h2 class="text-xl font-semibold text-red-800 mb-2">Report Generation Error</h2>
                <p class="text-red-700 mb-4">' . htmlspecialchars($message) . '</p>
                <a href="' . BASE_URL . 'attendance/qr-analytics" class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Analytics
                </a>
              </div>';

        $content = ob_get_clean();
        include 'views/layouts/main.php';
    }

    /**
     * Test QR endpoints connectivity
     * URL: /attendance/test-qr-endpoints
     */
    public function testQrEndpoints() {
        header('Content-Type: application/json');

        try {
            $method = $_SERVER['REQUEST_METHOD'];
            $post_data = $_POST;
            $session_data = $_SESSION;

            echo json_encode([
                'success' => true,
                'message' => 'QR endpoints are working',
                'debug_info' => [
                    'method' => $method,
                    'post_data' => $post_data,
                    'has_session' => !empty($session_data),
                    'base_url' => BASE_URL,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'php_version' => PHP_VERSION,
                    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
                ]
            ], JSON_PRETTY_PRINT);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Mobile debugging endpoint for QR attendance
     * URL: /attendance/mobile-debug
     */
    public function mobileDebug() {
        // Ensure we always return JSON, even if there are errors
        try {
            header('Content-Type: application/json');
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

            // Start output buffering to catch any unexpected output
            ob_start();

            $debug_info = [
                'success' => true,
                'timestamp' => date('Y-m-d H:i:s'),
                'method' => $_SERVER['REQUEST_METHOD'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                'server_name' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
                'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
                'base_url' => BASE_URL,
                'session_status' => session_status(),
                'session_id' => session_id(),
                'has_qr_session' => isset($_SESSION['qr_session']),
                'post_data' => $_POST,
                'get_data' => $_GET,
                'headers' => function_exists('getallheaders') ? getallheaders() : $_SERVER,
                'php_errors' => error_get_last(),
                'memory_usage' => memory_get_usage(true),
                'is_mobile' => preg_match('/Mobile|Android|iPhone|iPad/', $_SERVER['HTTP_USER_AGENT'] ?? ''),
                'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'Not set',
                'route_test' => 'mobile-debug endpoint reached successfully'
            ];

            if (isset($_SESSION['qr_session'])) {
                $debug_info['qr_session_details'] = $_SESSION['qr_session'];
            }

            // Check if there was any unexpected output
            $unexpected_output = ob_get_clean();
            if (!empty($unexpected_output)) {
                $debug_info['unexpected_output'] = $unexpected_output;
            }

            echo json_encode($debug_info, JSON_PRETTY_PRINT);

        } catch (Exception $e) {
            // Clean any output buffer
            if (ob_get_level()) {
                ob_end_clean();
            }

            // Ensure JSON header is set
            header('Content-Type: application/json');

            echo json_encode([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }

    /**
     * Simple connectivity test endpoint
     * URL: /attendance/ping
     */
    public function ping() {
        header('Content-Type: text/plain');
        echo 'PONG - Server is reachable at ' . date('Y-m-d H:i:s');
    }


}
