<?php
/**
 * Comprehensive Unit Tests for PhoneNumberUtils
 * 
 * Tests all phone number normalization functionality with various scenarios
 * 
 * <AUTHOR> Management System
 * @version 1.0
 */

require_once __DIR__ . '/../utils/PhoneNumberUtils.php';

class PhoneNumberUtilsTest {
    
    private $testResults = [];
    private $totalTests = 0;
    private $passedTests = 0;
    
    /**
     * Run all tests
     */
    public function runAllTests() {
        echo "=== PHONENUMBERUTILS COMPREHENSIVE UNIT TESTS ===\n\n";
        
        $this->testBasicNormalization();
        $this->testCountryDetection();
        $this->testEdgeCases();
        $this->testInternationalNumbers();
        $this->testFallbackMechanisms();
        $this->testPerformance();
        $this->testJSONPatterns();
        
        $this->printSummary();
    }
    
    /**
     * Test basic phone number normalization
     */
    private function testBasicNormalization() {
        echo "1. BASIC NORMALIZATION TESTS\n";
        
        $testCases = [
            // Togo numbers
            ['90123456', '+22890123456', 'TG', 'Togo mobile without leading 0'],
            ['090123456', '+22890123456', 'TG', 'Togo mobile with leading 0'],
            
            // Ghana numbers
            ['0246670114', '+233246670114', 'GH', 'Ghana mobile with leading 0'],
            ['246670114', '+233246670114', 'GH', 'Ghana mobile without leading 0'],
            
            // UK numbers
            ['07578662999', '+447578662999', 'GB', 'UK mobile'],
            ['02012345678', '+442012345678', 'GB', 'UK landline'],
            
            // US numbers
            ['2125551234', '+12125551234', 'US', 'US number'],
            ['12125551234', '+12125551234', 'US', 'US number with country code'],
            
            // Nigeria numbers
            ['08012345678', '+2348012345678', 'NG', 'Nigeria mobile'],
        ];
        
        foreach ($testCases as [$input, $expected, $expectedCountry, $description]) {
            $result = PhoneNumberUtils::normalize($input);
            $detectedCountry = PhoneNumberUtils::detectCountryFromNumber($input);
            
            $passed = ($result === $expected && $detectedCountry === $expectedCountry);
            $this->recordTest($description, $passed, [
                'input' => $input,
                'expected' => $expected,
                'actual' => $result,
                'expected_country' => $expectedCountry,
                'detected_country' => $detectedCountry
            ]);
        }
        
        echo "\n";
    }
    
    /**
     * Test country detection accuracy
     */
    private function testCountryDetection() {
        echo "2. COUNTRY DETECTION TESTS\n";
        
        $testCases = [
            ['90123456', 'TG', 'Togo mobile detection'],
            ['0246670114', 'GH', 'Ghana mobile detection'],
            ['07578662999', 'GB', 'UK mobile detection'],
            ['2125551234', 'US', 'US number detection'],
            ['08012345678', 'NG', 'Nigeria mobile detection'],
            ['0712345678', 'KE', 'Kenya mobile detection'],
            ['0821234567', 'ZA', 'South Africa mobile detection'],
        ];
        
        foreach ($testCases as [$input, $expected, $description]) {
            $result = PhoneNumberUtils::detectCountryFromNumber($input);
            $passed = ($result === $expected);
            
            $this->recordTest($description, $passed, [
                'input' => $input,
                'expected' => $expected,
                'actual' => $result
            ]);
        }
        
        echo "\n";
    }
    
    /**
     * Test edge cases and error handling
     */
    private function testEdgeCases() {
        echo "3. EDGE CASES TESTS\n";
        
        $testCases = [
            ['', '', 'Empty string'],
            ['123', '+123', 'Too short number'],
            ['12345678901234567890', '+12345678901234567890', 'Too long number'],
            ['+22890123456', '+22890123456', 'Already international format'],
            ['abc123def', '+123', 'Non-numeric characters'],
            ['  090 123 456  ', '+22890123456', 'Whitespace and formatting'],
            ['(090) 123-456', '+22890123456', 'US-style formatting'],
        ];
        
        foreach ($testCases as [$input, $expected, $description]) {
            $result = PhoneNumberUtils::normalize($input);
            $passed = ($result === $expected);
            
            $this->recordTest($description, $passed, [
                'input' => $input,
                'expected' => $expected,
                'actual' => $result
            ]);
        }
        
        echo "\n";
    }
    
    /**
     * Test international number handling
     */
    private function testInternationalNumbers() {
        echo "4. INTERNATIONAL NUMBERS TESTS\n";
        
        $testCases = [
            ['+22890123456', '+22890123456', 'Togo international'],
            ['+233246670114', '+233246670114', 'Ghana international'],
            ['+447578662999', '+447578662999', 'UK international'],
            ['+12125551234', '+12125551234', 'US international'],
            ['22890123456', '+22890123456', 'Togo without + prefix'],
        ];
        
        foreach ($testCases as [$input, $expected, $description]) {
            $result = PhoneNumberUtils::normalize($input);
            $passed = ($result === $expected);
            
            $this->recordTest($description, $passed, [
                'input' => $input,
                'expected' => $expected,
                'actual' => $result
            ]);
        }
        
        echo "\n";
    }
    
    /**
     * Test fallback mechanisms
     */
    private function testFallbackMechanisms() {
        echo "5. FALLBACK MECHANISMS TESTS\n";
        
        // Test with different default countries
        $originalDefault = PhoneNumberUtils::getDefaultCountry();
        
        $testCases = [
            ['TG', '90123456', '+22890123456', 'Togo default with Togo number'],
            ['GH', '0246670114', '+233246670114', 'Ghana default with Ghana number'],
            ['GB', '07578662999', '+447578662999', 'UK default with UK number'],
            ['US', '2125551234', '+12125551234', 'US default with US number'],
        ];
        
        foreach ($testCases as [$defaultCountry, $input, $expected, $description]) {
            PhoneNumberUtils::setDefaultCountry($defaultCountry);
            $result = PhoneNumberUtils::normalize($input);
            $passed = ($result === $expected);
            
            $this->recordTest($description, $passed, [
                'default_country' => $defaultCountry,
                'input' => $input,
                'expected' => $expected,
                'actual' => $result
            ]);
        }
        
        // Restore original default
        PhoneNumberUtils::setDefaultCountry($originalDefault);
        
        echo "\n";
    }
    
    /**
     * Test performance with large datasets
     */
    private function testPerformance() {
        echo "6. PERFORMANCE TESTS\n";
        
        $testNumbers = [
            '90123456', '0246670114', '07578662999', '2125551234', '08012345678'
        ];
        
        $iterations = 1000;
        $startTime = microtime(true);
        
        for ($i = 0; $i < $iterations; $i++) {
            foreach ($testNumbers as $number) {
                PhoneNumberUtils::normalize($number);
            }
        }
        
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        $avgTime = ($totalTime / ($iterations * count($testNumbers))) * 1000; // ms
        
        $passed = ($avgTime < 1.0); // Should be under 1ms per normalization
        
        $this->recordTest('Performance benchmark', $passed, [
            'iterations' => $iterations * count($testNumbers),
            'total_time' => round($totalTime, 4) . 's',
            'avg_time_per_call' => round($avgTime, 4) . 'ms',
            'threshold' => '1.0ms'
        ]);
        
        echo "\n";
    }
    
    /**
     * Test JSON patterns functionality
     */
    private function testJSONPatterns() {
        echo "7. JSON PATTERNS TESTS\n";
        
        // Test JSON file existence and structure
        $jsonFile = __DIR__ . '/../utils/phone_patterns.json';
        $jsonExists = file_exists($jsonFile);
        
        $this->recordTest('JSON patterns file exists', $jsonExists, [
            'file' => $jsonFile,
            'exists' => $jsonExists
        ]);
        
        if ($jsonExists) {
            $jsonContent = file_get_contents($jsonFile);
            $jsonData = json_decode($jsonContent, true);
            $validStructure = ($jsonData && isset($jsonData['patterns']));
            
            $this->recordTest('JSON structure is valid', $validStructure, [
                'has_patterns' => isset($jsonData['patterns']),
                'pattern_count' => $validStructure ? count($jsonData['patterns']) : 0
            ]);
            
            // Test fallback mechanism
            if ($validStructure) {
                // Temporarily clear cache to test loading
                PhoneNumberUtils::clearCache();
                $result = PhoneNumberUtils::normalize('90123456');
                $fallbackWorks = ($result === '+22890123456');
                
                $this->recordTest('JSON patterns loading works', $fallbackWorks, [
                    'test_input' => '90123456',
                    'expected' => '+22890123456',
                    'actual' => $result
                ]);
            }
        }
        
        echo "\n";
    }
    
    /**
     * Record a test result
     */
    private function recordTest($description, $passed, $details = []) {
        $this->totalTests++;
        if ($passed) {
            $this->passedTests++;
        }
        
        $status = $passed ? '✅ PASS' : '❌ FAIL';
        echo "  $status: $description\n";
        
        if (!$passed && !empty($details)) {
            echo "    Details: " . json_encode($details, JSON_PRETTY_PRINT) . "\n";
        }
        
        $this->testResults[] = [
            'description' => $description,
            'passed' => $passed,
            'details' => $details
        ];
    }
    
    /**
     * Print test summary
     */
    private function printSummary() {
        echo "=== TEST SUMMARY ===\n\n";
        
        $failedTests = $this->totalTests - $this->passedTests;
        $successRate = round(($this->passedTests / $this->totalTests) * 100, 2);
        
        echo "Total Tests: {$this->totalTests}\n";
        echo "Passed: {$this->passedTests}\n";
        echo "Failed: $failedTests\n";
        echo "Success Rate: {$successRate}%\n\n";
        
        if ($successRate >= 95) {
            echo "🎉 EXCELLENT: Phone number system is highly reliable!\n";
        } elseif ($successRate >= 85) {
            echo "✅ GOOD: Phone number system is working well with minor issues.\n";
        } elseif ($successRate >= 70) {
            echo "⚠️ FAIR: Phone number system needs some improvements.\n";
        } else {
            echo "❌ POOR: Phone number system has significant issues.\n";
        }
        
        // Show failed tests
        if ($failedTests > 0) {
            echo "\nFAILED TESTS:\n";
            foreach ($this->testResults as $result) {
                if (!$result['passed']) {
                    echo "- {$result['description']}\n";
                }
            }
        }
    }
}

// Run tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $tester = new PhoneNumberUtilsTest();
    $tester->runAllTests();
}
?>
