<?php
/**
 * Program Controller for Church Program & Activities Planner
 */

require_once 'models/Program.php';
require_once 'models/ProgramCategory.php';
require_once 'models/MinistryDepartment.php';
require_once 'models/ProgramActivity.php';
require_once 'models/ProgramGuest.php';
require_once 'models/Member.php';
require_once 'models/Setting.php';
require_once 'utils/validation.php';

class ProgramController {
    private $database;
    private $program;
    private $category;
    private $department;
    private $activity;
    private $guest;
    private $member;
    private $setting;

    /**
     * Constructor
     */
    public function __construct() {
        try {
            $this->database = new Database();
            $db = $this->database->getConnection();

            $this->program = new Program($db);
            $this->setting = new Setting($db);
            $this->category = new ProgramCategory($db);
            $this->department = new MinistryDepartment($db);
            $this->activity = new ProgramActivity($db);
            $this->guest = new ProgramGuest($db);
            $this->member = new Member($db);
        } catch (Exception $e) {
            error_log("ProgramController constructor error: " . $e->getMessage());
            $this->setting = null;
        }
    }

    /**
     * Display programs list with filters
     *
     * @return void
     */
    public function index() {
        // Get filter parameters
        $filters = [
            'status' => $_GET['status'] ?? '',
            'category_id' => $_GET['category'] ?? '',
            'department_id' => $_GET['department'] ?? '',
            'month' => $_GET['month'] ?? '',
            'year' => $_GET['year'] ?? date('Y'),
            'search' => $_GET['search'] ?? ''
        ];

        // Get programs with filters
        $stmt = $this->program->getAll($filters);
        $programs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get categories and departments for filters
        $categories = $this->category->getAll()->fetchAll(PDO::FETCH_ASSOC);
        $departments = $this->department->getAll()->fetchAll(PDO::FETCH_ASSOC);

        // Get program statistics
        $stats = $this->program->getStatistics();

        // Set page title and active page
        $page_title = getPageTitle('Church Programs & Activities');
        $active_page = 'programs';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/programs/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display program details
     *
     * @param int $id Program ID from route parameter
     * @return void
     */
    public function show($id = null) {
        // Support both RESTful route parameter and legacy $_GET
        $program_id = $id ?? $_GET['id'] ?? null;

        // Check if ID is set
        if (!$program_id || empty($program_id)) {
            set_flash_message('Program ID is required.', 'danger');
            redirect('programs');
        }

        $program_id = (int)$program_id;

        // Get program details
        $program = $this->program->getById($program_id);
        if (!$program) {
            set_flash_message('Program not found.', 'danger');
            redirect('programs');
        }

        // Get program activities
        $activities = $this->activity->getByProgram($program_id)->fetchAll(PDO::FETCH_ASSOC);

        // Get activity statistics
        $activity_stats = $this->activity->getProgramActivityStats($program_id);

        // Get program invited guests
        $guests = $this->guest->getByProgram($program_id)->fetchAll(PDO::FETCH_ASSOC);

        // Get guest statistics
        $guest_stats = $this->guest->getProgramGuestStats($program_id);

        // Set page title and active page
        $page_title = getPageTitle($program['title'] . ' - Program Details');
        $active_page = 'programs';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/programs/show.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display create program form
     *
     * @return void
     */
    public function create() {
        // Get categories and departments for dropdowns
        $categories = $this->category->getAll()->fetchAll(PDO::FETCH_ASSOC);
        $departments = $this->department->getAll()->fetchAll(PDO::FETCH_ASSOC);
        $members = $this->member->getAll()->fetchAll(PDO::FETCH_ASSOC);

        // Set page title and active page
        $page_title = getPageTitle('Create New Program');
        $active_page = 'programs';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/programs/create.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store new program
     *
     * @return void
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('programs/create');
        }

        // Validate CSRF token
        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            set_flash_message('Invalid security token. Please try again.', 'danger');
            redirect('programs/create');
        }

        // Set program properties from POST data
        $this->program->title = $_POST['title'] ?? '';
        $this->program->description = $_POST['description'] ?? '';
        $this->program->category_id = $_POST['category_id'] ?? '';
        $this->program->department_id = $_POST['department_id'] ?? '';
        $this->program->coordinator_id = !empty($_POST['coordinator_id']) ? $_POST['coordinator_id'] : null;
        $this->program->start_date = $_POST['start_date'] ?? '';
        $this->program->end_date = $_POST['end_date'] ?? '';
        $this->program->start_time = !empty($_POST['start_time']) ? $_POST['start_time'] : null;
        $this->program->end_time = !empty($_POST['end_time']) ? $_POST['end_time'] : null;
        $this->program->location = $_POST['location'] ?? '';
        $this->program->budget_allocated = !empty($_POST['budget_allocated']) ? $_POST['budget_allocated'] : 0.00;
        $this->program->expected_attendance = !empty($_POST['expected_attendance']) ? $_POST['expected_attendance'] : 0;
        $this->program->status = $_POST['status'] ?? 'planned';
        $this->program->priority = $_POST['priority'] ?? 'medium';
        $this->program->is_recurring = isset($_POST['is_recurring']) ? 1 : 0;
        $this->program->recurrence_pattern = $_POST['recurrence_pattern'] ?? null;
        $this->program->requires_registration = isset($_POST['requires_registration']) ? 1 : 0;
        $this->program->max_participants = !empty($_POST['max_participants']) ? $_POST['max_participants'] : null;
        $this->program->notes = $_POST['notes'] ?? '';
        $this->program->created_by = $_SESSION['user_id'];

        // Validate program data
        $errors = $this->program->validate();

        if (!empty($errors)) {
            set_flash_message('Please fix the following errors: ' . implode(', ', $errors), 'danger');
            redirect('programs/create');
        }

        // Create program
        if ($this->program->create()) {
            // Handle invited guests if provided
            if (!empty($_POST['guests']) && is_array($_POST['guests'])) {
                $guests_data = $_POST['guests'];
                $this->guest->createMultiple($this->program->id, $guests_data, $_SESSION['user_id']);
            }

            set_flash_message('Program created successfully!', 'success');
            redirect('programs/show?id=' . $this->program->id);
        } else {
            set_flash_message('Error creating program. Please try again.', 'danger');
            redirect('programs/create');
        }
    }

    /**
     * Display edit program form
     *
     * @param int $id Program ID from route parameter
     * @return void
     */
    public function edit($id = null) {
        // Support both RESTful route parameter and legacy $_GET
        $program_id = $id ?? $_GET['id'] ?? null;

        // Check if ID is set
        if (!$program_id || empty($program_id)) {
            set_flash_message('Program ID is required.', 'danger');
            redirect('programs');
        }

        $program_id = (int)$program_id;

        // Get program details
        $program = $this->program->getById($program_id);
        if (!$program) {
            set_flash_message('Program not found.', 'danger');
            redirect('programs');
        }

        // Get categories and departments for dropdowns
        $categories = $this->category->getAll()->fetchAll(PDO::FETCH_ASSOC);
        $departments = $this->department->getAll()->fetchAll(PDO::FETCH_ASSOC);
        $members = $this->member->getAll()->fetchAll(PDO::FETCH_ASSOC);

        // Get existing guests for this program
        $guests = $this->guest->getByProgram($program_id)->fetchAll(PDO::FETCH_ASSOC);

        // Set page title and active page
        $page_title = getPageTitle('Edit Program: ' . $program['title']);
        $active_page = 'programs';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/programs/edit.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Update program
     *
     * @param int $id Program ID from route parameter
     * @return void
     */
    public function update($id = null) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' && $_SERVER['REQUEST_METHOD'] !== 'PUT') {
            redirect('programs');
        }

        // Validate CSRF token
        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            set_flash_message('Invalid security token. Please try again.', 'danger');
            redirect('programs');
        }

        // Support both RESTful route parameter and legacy $_POST
        $program_id = $id ?? $_POST['id'] ?? null;

        // Check if ID is set
        if (!$program_id || empty($program_id)) {
            set_flash_message('Program ID is required.', 'danger');
            redirect('programs');
        }

        $program_id = (int)$program_id;

        // Set program properties
        $this->program->id = $program_id;
        $this->program->title = $_POST['title'] ?? '';
        $this->program->description = $_POST['description'] ?? '';
        $this->program->category_id = $_POST['category_id'] ?? '';
        $this->program->department_id = $_POST['department_id'] ?? '';
        $this->program->coordinator_id = !empty($_POST['coordinator_id']) ? $_POST['coordinator_id'] : null;
        $this->program->start_date = $_POST['start_date'] ?? '';
        $this->program->end_date = $_POST['end_date'] ?? '';
        $this->program->start_time = !empty($_POST['start_time']) ? $_POST['start_time'] : null;
        $this->program->end_time = !empty($_POST['end_time']) ? $_POST['end_time'] : null;
        $this->program->location = $_POST['location'] ?? '';
        $this->program->budget_allocated = !empty($_POST['budget_allocated']) ? $_POST['budget_allocated'] : 0.00;
        $this->program->expected_attendance = !empty($_POST['expected_attendance']) ? $_POST['expected_attendance'] : 0;
        $this->program->status = $_POST['status'] ?? 'planned';
        $this->program->priority = $_POST['priority'] ?? 'medium';
        $this->program->is_recurring = isset($_POST['is_recurring']) ? 1 : 0;
        $this->program->recurrence_pattern = $_POST['recurrence_pattern'] ?? null;
        $this->program->requires_registration = isset($_POST['requires_registration']) ? 1 : 0;
        $this->program->max_participants = !empty($_POST['max_participants']) ? $_POST['max_participants'] : null;
        $this->program->notes = $_POST['notes'] ?? '';

        // Validate program data
        $errors = $this->program->validate();

        if (!empty($errors)) {
            set_flash_message('Please fix the following errors: ' . implode(', ', $errors), 'danger');
            redirect('programs/edit?id=' . $program_id);
        }

        // Update program
        if ($this->program->update()) {
            // Handle invited guests if provided
            if (!empty($_POST['guests']) && is_array($_POST['guests'])) {
                $guests_data = $_POST['guests'];
                $this->guest->updateProgramGuests($program_id, $guests_data, $_SESSION['user_id']);
            }

            set_flash_message('Program updated successfully!', 'success');
            redirect('programs/show?id=' . $program_id);
        } else {
            set_flash_message('Error updating program. Please try again.', 'danger');
            redirect('programs/edit?id=' . $program_id);
        }
    }

    /**
     * Delete program
     *
     * @param int $id Program ID from route parameter
     * @return void
     */
    public function delete($id = null) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' && $_SERVER['REQUEST_METHOD'] !== 'DELETE') {
            redirect('programs');
        }

        // Validate CSRF token
        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            set_flash_message('Invalid security token. Please try again.', 'danger');
            redirect('programs');
        }

        // Support both RESTful route parameter and legacy $_POST
        $program_id = $id ?? $_POST['id'] ?? null;

        // Check if ID is set
        if (!$program_id || empty($program_id)) {
            set_flash_message('Program ID is required.', 'danger');
            redirect('programs');
        }

        $program_id = (int)$program_id;

        // Set program ID
        $this->program->id = $program_id;

        // Delete program
        if ($this->program->delete()) {
            set_flash_message('Program deleted successfully!', 'success');
        } else {
            set_flash_message('Error deleting program. Please try again.', 'danger');
        }

        redirect('programs');
    }

    /**
     * Update program status
     *
     * @return void
     */
    public function updateStatus() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('programs');
        }

        // Validate CSRF token
        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            set_flash_message('Invalid security token. Please try again.', 'danger');
            redirect('programs');
        }

        $program_id = (int)($_POST['program_id'] ?? 0);
        $new_status = $_POST['status'] ?? '';
        $reason = $_POST['reason'] ?? '';

        if (empty($program_id) || empty($new_status)) {
            set_flash_message('Program ID and status are required.', 'danger');
            redirect('programs');
        }

        // Set program ID
        $this->program->id = $program_id;

        // Update status
        if ($this->program->updateStatus($new_status, $_SESSION['user_id'], $reason)) {
            set_flash_message('Program status updated successfully!', 'success');
        } else {
            set_flash_message('Error updating program status. Please try again.', 'danger');
        }

        redirect('programs/show?id=' . $program_id);
    }

    /**
     * Display calendar view
     *
     * @return void
     */
    public function calendar() {
        $year = $_GET['year'] ?? date('Y');
        $month = $_GET['month'] ?? date('n');

        // Get programs for the specified month
        $start_date = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));

        $programs = $this->program->getByDateRange($start_date, $end_date)->fetchAll(PDO::FETCH_ASSOC);

        // Process programs for calendar display
        $calendar_data = [];
        foreach ($programs as $program) {
            $day = (int)date('j', strtotime($program['start_date']));
            if (!isset($calendar_data[$day])) {
                $calendar_data[$day] = [];
            }
            $calendar_data[$day][] = $program;
        }

        // Get categories for legend
        $categories = $this->category->getAll()->fetchAll(PDO::FETCH_ASSOC);

        // Set page title and active page
        $page_title = getPageTitle('Program Calendar - ' . date('F Y', strtotime($start_date)));
        $active_page = 'programs';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/programs/calendar.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display annual timeline view
     *
     * @return void
     */
    public function timeline() {
        $year = $_GET['year'] ?? date('Y');

        // Get all programs for the year
        $start_date = $year . '-01-01';
        $end_date = $year . '-12-31';

        $programs = $this->program->getByDateRange($start_date, $end_date)->fetchAll(PDO::FETCH_ASSOC);

        // Group programs by month
        $timeline_data = [];
        for ($month = 1; $month <= 12; $month++) {
            $timeline_data[$month] = [
                'name' => date('F', mktime(0, 0, 0, $month, 1)),
                'programs' => []
            ];
        }

        foreach ($programs as $program) {
            $month = (int)date('n', strtotime($program['start_date']));
            $timeline_data[$month]['programs'][] = $program;
        }

        // Get categories for filtering
        $categories = $this->category->getAll()->fetchAll(PDO::FETCH_ASSOC);
        $departments = $this->department->getAll()->fetchAll(PDO::FETCH_ASSOC);

        // Set page title and active page
        $page_title = getPageTitle('Annual Program Timeline - ' . $year);
        $active_page = 'programs';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/programs/timeline.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display dashboard with program overview
     *
     * @return void
     */
    public function dashboard() {
        // Get upcoming programs
        $upcoming_programs = $this->program->getUpcoming(5)->fetchAll(PDO::FETCH_ASSOC);

        // Get program statistics
        $stats = $this->program->getStatistics();

        // Get categories with program counts
        $categories_stats = $this->category->getCategoriesWithProgramCount()->fetchAll(PDO::FETCH_ASSOC);

        // Get departments with program counts
        $departments_stats = $this->department->getDepartmentsWithStats()->fetchAll(PDO::FETCH_ASSOC);

        // Get upcoming activities
        $upcoming_activities = $this->activity->getUpcoming(5)->fetchAll(PDO::FETCH_ASSOC);

        // Get automatic status updates for dashboard
        require_once 'controllers/AutoStatusManager.php';
        $autoStatusManager = new AutoStatusManager($this->database->getConnection());
        $recent_auto_updates = $autoStatusManager->getRecentAutoUpdates(10);

        // Set page title and active page
        $page_title = getPageTitle('Program Dashboard');
        $active_page = 'programs';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/programs/dashboard.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Export programs to CSV
     *
     * @return void
     */
    public function export() {
        // Get filter parameters
        $filters = [
            'status' => $_GET['status'] ?? '',
            'category_id' => $_GET['category'] ?? '',
            'department_id' => $_GET['department'] ?? '',
            'year' => $_GET['year'] ?? date('Y')
        ];

        // Get programs with filters
        $programs = $this->program->getAll($filters)->fetchAll(PDO::FETCH_ASSOC);

        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="church_programs_' . date('Y-m-d') . '.csv"');

        // Create file pointer
        $output = fopen('php://output', 'w');

        // Add CSV headers
        fputcsv($output, [
            'Title', 'Category', 'Department', 'Coordinator', 'Start Date', 'End Date',
            'Location', 'Status', 'Priority', 'Budget Allocated', 'Expected Attendance', 'Description'
        ]);

        // Add program data
        foreach ($programs as $program) {
            fputcsv($output, [
                $program['title'],
                $program['category_name'],
                $program['department_name'],
                $program['coordinator_name'],
                $program['start_date'],
                $program['end_date'],
                $program['location'],
                $program['status'],
                $program['priority'],
                $program['budget_allocated'],
                $program['expected_attendance'],
                $program['description']
            ]);
        }

        fclose($output);
        exit;
    }
}
