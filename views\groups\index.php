<!-- Enhanced Groups Management Page -->
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="page-content-centered py-8">
        <!-- Enhanced Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Groups Management</h1>
                    <p class="text-gray-600">Manage and organize your church groups effectively</p>
                </div>
                <a href="<?php echo url('groups/add'); ?>" class="bg-gradient-to-r from-[#3F7D58] to-[#2c5a3f] hover:from-[#2c5a3f] hover:to-[#1e3d2a] text-white px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    <i class="fas fa-plus mr-2"></i>Add New Group
                </a>
            </div>
        </div>

        <?php flash('group_message'); ?>

        <!-- Enhanced Statistics Cards -->
        <!-- Statistics Cards (Visitors Page Style) -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-gradient-to-r from-[#3F7D58] to-[#2c5a3f] rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Total Groups</p>
                        <h3 class="text-2xl font-bold"><?php echo $totalGroups; ?></h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-[#D3E671] to-[#b8c95a] rounded-lg shadow-md p-4 text-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Total Members</p>
                        <h3 class="text-2xl font-bold"><?php echo $totalMembers ?? 0; ?></h3>
                    </div>
                    <div class="bg-white bg-opacity-40 rounded-full p-3">
                        <i class="fas fa-user-friends text-xl text-gray-700"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Average Size</p>
                        <h3 class="text-2xl font-bold">
                            <?php
                            $avgSize = ($totalGroups > 0) ? round($totalMembers / $totalGroups, 1) : 0;
                            echo $avgSize;
                            ?>
                        </h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-chart-bar text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Largest Group</p>
                        <?php if ($largestGroup && $largestGroupSize > 0): ?>
                        <h3 class="text-lg font-bold leading-tight"><?php echo htmlspecialchars($largestGroup->group_name); ?></h3>
                        <p class="text-xs opacity-75"><?php echo $largestGroupSize; ?> members</p>
                        <?php else: ?>
                        <h3 class="text-lg font-bold">No groups yet</h3>
                        <p class="text-xs opacity-75">0 members</p>
                        <?php endif; ?>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-crown text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Quick Filter -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h2 class="text-xl font-semibold text-gray-800">Quick Filter</h2>
                    <p class="text-sm text-gray-600">Filter groups by type for easy management</p>
                </div>
                <?php if (count($allGroupTypes ?? []) > 6): ?>
                <button id="toggleFilters" class="text-sm text-[#3F7D58] hover:text-[#2c5a3f] font-medium bg-gray-50 hover:bg-gray-100 px-3 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-cog mr-1"></i> Manage Filters
                </button>
                <?php endif; ?>
            </div>
            <div class="flex flex-wrap gap-3" id="filterContainer">
                <button class="filter-btn active px-4 py-2 rounded-full bg-gradient-to-r from-[#3F7D58] to-[#2c5a3f] text-white text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200" data-filter="all">All Groups</button>

            <?php
            // Filter group types to show only those with groups and limit display
            $activeGroupTypes = [];
            $inactiveGroupTypes = [];

            foreach ($groupTypes as $type) {
                $count = $groupsByType[$type->type_name] ?? 0;
                if ($count > 0) {
                    $activeGroupTypes[] = $type;
                } else {
                    $inactiveGroupTypes[] = $type;
                }
            }

            // Show active group types first (up to 6), then inactive ones
            $displayTypes = array_slice($activeGroupTypes, 0, 6);
            $hiddenTypes = array_merge(array_slice($activeGroupTypes, 6), $inactiveGroupTypes);
            ?>

                <?php foreach ($displayTypes as $type): ?>
                <button class="filter-btn px-4 py-2 rounded-full bg-gray-100 text-gray-700 text-sm font-medium hover:bg-gradient-to-r hover:from-[#D3E671] hover:to-[#b8c95a] hover:text-gray-800 transition-all duration-200 shadow-sm hover:shadow-md" data-filter="<?php echo $type->type_name; ?>">
                    <?php echo $type->type_name; ?>
                    <?php if (($groupsByType[$type->type_name] ?? 0) > 0): ?>
                    <span class="ml-2 bg-[#3F7D58] text-white text-xs px-2 py-1 rounded-full"><?php echo $groupsByType[$type->type_name]; ?></span>
                    <?php endif; ?>
                </button>
                <?php endforeach; ?>

            <!-- Hidden filters (initially hidden) -->
            <?php if (!empty($hiddenTypes)): ?>
            <div id="hiddenFilters" class="hidden flex flex-wrap gap-2 w-full mt-2">
                <?php foreach ($hiddenTypes as $type): ?>
                <button class="filter-btn px-3 py-1 rounded-full bg-gray-100 text-gray-600 text-sm hover:bg-[#D3E671] hover:text-gray-800 transition-colors duration-200" data-filter="<?php echo $type->type_name; ?>">
                    <?php echo $type->type_name; ?>
                    <?php if (($groupsByType[$type->type_name] ?? 0) > 0): ?>
                    <span class="ml-1 bg-gray-500 text-white text-xs px-1 rounded-full"><?php echo $groupsByType[$type->type_name]; ?></span>
                    <?php endif; ?>
                </button>
                <?php endforeach; ?>
            </div>

            <?php if (count($hiddenTypes) > 0): ?>
            <button id="showMoreFilters" class="px-3 py-1 rounded-full bg-gray-100 text-gray-600 text-sm hover:bg-gray-200 transition-colors duration-200">
                <i class="fas fa-chevron-down mr-1"></i> Show <?php echo count($hiddenTypes); ?> More
            </button>
            <button id="showLessFilters" class="hidden px-3 py-1 rounded-full bg-gray-100 text-gray-600 text-sm hover:bg-gray-200 transition-colors duration-200">
                <i class="fas fa-chevron-up mr-1"></i> Show Less
            </button>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

        <!-- Enhanced Groups Table -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="px-6 py-5 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-800">All Groups</h3>
                        <p class="text-sm text-gray-600 mt-1">Manage your church groups and their members</p>
                    </div>
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-users mr-1"></i>
                        <?php echo count($groups ?? []); ?> groups total
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gradient-to-r from-gray-100 to-gray-200">
                        <tr>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Group Name</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Members</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Parent Group</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($groups)): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-8 text-center">
                                <div class="text-gray-400">
                                    <i class="fas fa-users text-4xl mb-3"></i>
                                    <p class="text-lg font-medium">No groups found</p>
                                    <p class="text-sm">Create your first group to get started</p>
                                </div>
                            </td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($groups as $group): ?>
                            <tr class="group-row hover:bg-gray-50 transition-colors duration-200" data-type="<?php echo $group->type_name ?? 'None'; ?>">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo $group->group_name; ?></div>
                                <div class="text-sm text-gray-500"><?php echo substr($group->group_description, 0, 50) . (strlen($group->group_description) > 50 ? '...' : ''); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    <?php echo $group->type_name ?? 'None'; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $group->member_count ?? 0; ?> members
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $group->parent_group_name ?? 'None'; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $group->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                    <?php echo ucfirst($group->status); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <a href="<?php echo url('groups/members/' . $group->group_id); ?>" class="text-indigo-600 hover:text-indigo-900" title="View Members">
                                        <i class="fas fa-users"></i>
                                    </a>
                                    <a href="<?php echo url('groups/edit/' . $group->group_id); ?>" class="text-blue-600 hover:text-blue-900" title="Edit Group">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="confirmDelete(<?php echo $group->group_id; ?>, '<?php echo addslashes($group->group_name); ?>')" class="text-red-600 hover:text-red-900" title="Delete Group">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Manage Filters Modal -->
    <div id="manageFiltersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Manage Quick Filters</h3>
                    <button onclick="closeManageFiltersModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-3">Select which group types to show in the Quick Filter section:</p>

                    <div class="space-y-2 max-h-60 overflow-y-auto">
                        <label class="flex items-center">
                            <input type="checkbox" id="filter_all" checked disabled class="mr-2 text-[#3F7D58]">
                            <span class="text-sm font-medium">All Groups (Always shown)</span>
                        </label>

                        <?php foreach ($allGroupTypes as $type): ?>
                        <label class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input type="checkbox"
                                       class="filter-checkbox mr-2 text-[#3F7D58]"
                                       data-filter="<?php echo htmlspecialchars($type->type_name); ?>"
                                       <?php echo (($groupsByType[$type->type_name] ?? 0) > 0) ? 'checked' : ''; ?>>
                                <span class="text-sm"><?php echo htmlspecialchars($type->type_name); ?></span>
                            </div>
                            <span class="text-xs text-gray-500">
                                (<?php echo $groupsByType[$type->type_name] ?? 0; ?> groups)
                            </span>
                        </label>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="flex justify-between">
                    <button onclick="selectAllFilters()" class="px-3 py-1 text-sm text-[#3F7D58] hover:text-[#2c5a3f]">
                        Select All
                    </button>
                    <div class="space-x-2">
                        <button onclick="closeManageFiltersModal()" class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800">
                            Cancel
                        </button>
                        <button onclick="applyFilterSettings()" class="px-4 py-2 text-sm bg-[#3F7D58] text-white rounded hover:bg-[#2c5a3f]">
                            Apply
                        </button>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirm Delete</h3>
        <p class="text-gray-700 mb-4">Are you sure you want to delete the group "<span id="groupName"></span>"? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 transition-colors duration-200">Cancel</button>
            <form id="deleteForm" action="" method="POST">
                <?php echo csrf_field(); ?>
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">Delete</button>
            </form>
        </div>
    </div>
</div>

<script>
    // Filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const filterButtons = document.querySelectorAll('.filter-btn');
        const groupRows = document.querySelectorAll('.group-row');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');

                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active', 'bg-[#3F7D58]', 'text-white'));
                filterButtons.forEach(btn => btn.classList.add('bg-gray-200', 'text-gray-700'));
                this.classList.add('active', 'bg-[#3F7D58]', 'text-white');
                this.classList.remove('bg-gray-200', 'text-gray-700');

                // Filter rows
                groupRows.forEach(row => {
                    if (filter === 'all' || row.getAttribute('data-type') === filter) {
                        row.classList.remove('hidden');
                    } else {
                        row.classList.add('hidden');
                    }
                });
            });
        });
    });

    // Delete confirmation modal
    function confirmDelete(groupId, groupName) {
        document.getElementById('groupName').textContent = groupName;
        document.getElementById('deleteForm').action = '<?php echo url('groups/delete/'); ?>' + groupId;
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.add('flex');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
        document.getElementById('deleteModal').classList.remove('flex');
    }

    // Toggle show/hide additional filters
    const showMoreBtn = document.getElementById('showMoreFilters');
    const showLessBtn = document.getElementById('showLessFilters');
    const hiddenFilters = document.getElementById('hiddenFilters');

    if (showMoreBtn) {
        showMoreBtn.addEventListener('click', function() {
            hiddenFilters.classList.remove('hidden');
            showMoreBtn.classList.add('hidden');
            if (showLessBtn) showLessBtn.classList.remove('hidden');
        });
    }

    if (showLessBtn) {
        showLessBtn.addEventListener('click', function() {
            hiddenFilters.classList.add('hidden');
            showLessBtn.classList.add('hidden');
            if (showMoreBtn) showMoreBtn.classList.remove('hidden');
        });
    }

    // Manage filters toggle
    const toggleFiltersBtn = document.getElementById('toggleFilters');
    if (toggleFiltersBtn) {
        toggleFiltersBtn.addEventListener('click', function() {
            openManageFiltersModal();
        });
    }

    // Load saved filter preferences
    loadFilterPreferences();

    function openManageFiltersModal() {
        document.getElementById('manageFiltersModal').classList.remove('hidden');
    }

    function closeManageFiltersModal() {
        document.getElementById('manageFiltersModal').classList.add('hidden');
    }

    function selectAllFilters() {
        const checkboxes = document.querySelectorAll('.filter-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    }

    function applyFilterSettings() {
        const checkboxes = document.querySelectorAll('.filter-checkbox');
        const selectedFilters = [];

        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selectedFilters.push(checkbox.dataset.filter);
            }
        });

        // Save preferences to localStorage
        localStorage.setItem('groupFilterPreferences', JSON.stringify(selectedFilters));

        // Apply the filter settings
        updateFilterDisplay(selectedFilters);

        closeManageFiltersModal();
    }

    function loadFilterPreferences() {
        const saved = localStorage.getItem('groupFilterPreferences');
        if (saved) {
            const selectedFilters = JSON.parse(saved);
            updateFilterDisplay(selectedFilters);

            // Update checkboxes in modal
            const checkboxes = document.querySelectorAll('.filter-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectedFilters.includes(checkbox.dataset.filter);
            });
        }
    }

    function updateFilterDisplay(selectedFilters) {
        const allFilterBtns = document.querySelectorAll('.filter-btn[data-filter]:not([data-filter="all"])');

        allFilterBtns.forEach(btn => {
            const filterName = btn.dataset.filter;
            if (selectedFilters.includes(filterName)) {
                btn.style.display = 'inline-block';
            } else {
                btn.style.display = 'none';
            }
        });

        // Hide show more/less buttons if not needed
        const visibleFilters = Array.from(allFilterBtns).filter(btn => btn.style.display !== 'none');
        const showMoreBtn = document.getElementById('showMoreFilters');
        const showLessBtn = document.getElementById('showLessFilters');
        const hiddenFilters = document.getElementById('hiddenFilters');

        if (visibleFilters.length <= 6) {
            if (showMoreBtn) showMoreBtn.style.display = 'none';
            if (showLessBtn) showLessBtn.style.display = 'none';
            if (hiddenFilters) hiddenFilters.classList.remove('hidden');
        }
    }
</script>