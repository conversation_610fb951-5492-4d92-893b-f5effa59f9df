<?php
/**
 * Finance Categories Configuration
 * 
 * This file defines the income and expense categories and their properties,
 * including which categories require member tracking.
 */

class FinanceCategoriesConfig {
    
    /**
     * Income categories configuration
     * 
     * @return array
     */
    public static function getIncomeCategories() {
        return [
            'tithe' => [
                'label' => '💰 Tithe',
                'requires_member' => true,
                'description' => 'Member tithe payments',
                'icon' => 'fas fa-hand-holding-heart'
            ],
            'offering' => [
                'label' => '🙏 Offering',
                'requires_member' => false,
                'description' => 'General church offerings',
                'icon' => 'fas fa-praying-hands'
            ],
            'project_offering' => [
                'label' => '🏗️ Project Offering',
                'requires_member' => false,
                'description' => 'Offerings for specific church projects',
                'icon' => 'fas fa-building'
            ],
            'donation' => [
                'label' => '💝 Donation',
                'requires_member' => false,
                'description' => 'General donations to the church',
                'icon' => 'fas fa-gift'
            ],
            'seed' => [
                'label' => '🌱 Seed Offering',
                'requires_member' => false,
                'description' => 'Seed offerings for church growth',
                'icon' => 'fas fa-seedling'
            ],
            'pledge' => [
                'label' => '📝 Pledge',
                'requires_member' => true,
                'description' => 'Member pledge payments',
                'icon' => 'fas fa-handshake'
            ],
            'pastors_appreciation' => [
                'label' => '👨‍💼 Pastor\'s Appreciation',
                'requires_member' => false,
                'description' => 'Appreciation gifts for pastors',
                'icon' => 'fas fa-user-tie'
            ],

            'children_service_offering' => [
                'label' => '👶 Children Service',
                'requires_member' => false,
                'description' => 'Offerings from children\'s service',
                'icon' => 'fas fa-child'
            ],
            'others' => [
                'label' => '📋 Others',
                'requires_member' => false,
                'description' => 'Other income sources',
                'icon' => 'fas fa-ellipsis-h'
            ]
        ];
    }
    
    /**
     * Expense categories configuration
     * 
     * @return array
     */
    public static function getExpenseCategories() {
        $defaultCategories = [
            'utilities' => [
                'label' => '⚡ Utilities',
                'requires_member' => false,
                'description' => 'Electricity, water, internet bills',
                'icon' => 'fas fa-bolt'
            ],
            'rent' => [
                'label' => '🏠 Rent',
                'requires_member' => false,
                'description' => 'Building rent payments',
                'icon' => 'fas fa-home'
            ],
            'maintenance' => [
                'label' => '🔧 Maintenance',
                'requires_member' => false,
                'description' => 'Building and equipment maintenance',
                'icon' => 'fas fa-tools'
            ],
            'supplies' => [
                'label' => '📦 Supplies',
                'requires_member' => false,
                'description' => 'Office and church supplies',
                'icon' => 'fas fa-box'
            ],
            'salaries' => [
                'label' => '💼 Salaries',
                'requires_member' => false,
                'description' => 'Staff salary payments',
                'icon' => 'fas fa-money-check-alt'
            ],
            'events' => [
                'label' => '🎉 Events',
                'requires_member' => false,
                'description' => 'Church events and programs',
                'icon' => 'fas fa-calendar-alt'
            ],
            'missions' => [
                'label' => '🌍 Missions',
                'requires_member' => false,
                'description' => 'Mission work and outreach',
                'icon' => 'fas fa-globe'
            ],
            'charity' => [
                'label' => '❤️ Charity',
                'requires_member' => false,
                'description' => 'Charitable giving and support',
                'icon' => 'fas fa-heart'
            ],
            'equipment' => [
                'label' => '🖥️ Equipment',
                'requires_member' => false,
                'description' => 'Equipment purchases and upgrades',
                'icon' => 'fas fa-desktop'
            ],
            'other_expenses' => [
                'label' => '📋 Other Expenses',
                'requires_member' => false,
                'description' => 'Other miscellaneous expenses',
                'icon' => 'fas fa-receipt'
            ]
        ];

        // Add custom expense categories
        $customCategories = self::getCustomCategories('expenses');
        return array_merge($defaultCategories, $customCategories);
    }
    
    /**
     * Get categories that require member tracking
     *
     * @param string $type 'income' or 'expense'
     * @return array
     */
    public static function getMemberRequiredCategories($type = 'income') {
        $categories = $type === 'income' ? self::getIncomeCategories() : self::getExpenseCategories();

        return array_keys(array_filter($categories, function($category) {
            return $category['requires_member'] === true;
        }));
    }
    
    /**
     * Check if a category requires member tracking
     * 
     * @param string $category
     * @param string $type 'income' or 'expense'
     * @return bool
     */
    public static function requiresMember($category, $type = 'income') {
        $categories = $type === 'income' ? self::getIncomeCategories() : self::getExpenseCategories();
        
        return isset($categories[$category]) && $categories[$category]['requires_member'] === true;
    }
    
    /**
     * Get member-specific income categories (for Member Payments tab)
     *
     * @return array
     */
    public static function getMemberPaymentCategories() {
        $allCategories = self::getIncomeCategories();
        $memberCategories = [];

        foreach ($allCategories as $key => $category) {
            if ($category['requires_member']) {
                $memberCategories[$key] = $category;
            }
        }

        // Add custom member payment categories
        $customCategories = self::getCustomCategories('member_payments');
        $memberCategories = array_merge($memberCategories, $customCategories);

        return $memberCategories;
    }

    /**
     * Get general income categories (for General Income tab)
     *
     * @return array
     */
    public static function getGeneralIncomeCategories() {
        $allCategories = self::getIncomeCategories();
        $generalCategories = [];

        foreach ($allCategories as $key => $category) {
            if (!$category['requires_member']) {
                $generalCategories[$key] = $category;
            }
        }

        // Add custom general income categories
        $customCategories = self::getCustomCategories('general_income');
        $generalCategories = array_merge($generalCategories, $customCategories);

        return $generalCategories;
    }

    /**
     * Get categories by tab type
     *
     * @param string $tabType 'member_payments', 'general_income', or 'expenses'
     * @return array
     */
    public static function getCategoriesByTab($tabType) {
        switch ($tabType) {
            case 'member_payments':
                return self::getMemberPaymentCategories();
            case 'general_income':
                return self::getGeneralIncomeCategories();
            case 'expenses':
                return self::getExpenseCategories();
            default:
                return [];
        }
    }

    /**
     * Get category configuration
     *
     * @param string $category
     * @param string $type 'income' or 'expense'
     * @return array|null
     */
    public static function getCategoryConfig($category, $type = 'income') {
        $categories = $type === 'income' ? self::getIncomeCategories() : self::getExpenseCategories();

        return isset($categories[$category]) ? $categories[$category] : null;
    }

    /**
     * Generate JavaScript configuration for frontend
     *
     * @return string
     */
    public static function getJavaScriptConfig() {
        $incomeCategories = self::getMemberRequiredCategories('income');
        $expenseCategories = self::getMemberRequiredCategories('expense');

        return json_encode([
            'income' => $incomeCategories,
            'expense' => $expenseCategories
        ]);
    }

    /**
     * Generate tab-based JavaScript configuration
     *
     * @return string
     */
    public static function getTabBasedJavaScriptConfig() {
        return json_encode([
            'member_payments' => array_keys(self::getMemberPaymentCategories()),
            'general_income' => array_keys(self::getGeneralIncomeCategories()),
            'expenses' => array_keys(self::getExpenseCategories())
        ]);
    }

    /**
     * Get custom categories from database
     *
     * @param string $categoryType
     * @return array
     */
    public static function getCustomCategories($categoryType) {
        try {
            // Get database connection
            require_once 'config/database.php';
            require_once 'models/CustomFinanceCategory.php';

            $database = new Database();
            $db = $database->getConnection();

            $customCategory = new CustomFinanceCategory($db);
            $categories = $customCategory->getByType($categoryType, true);

            $formattedCategories = [];
            foreach ($categories as $category) {
                $formattedCategories[$category->name] = [
                    'label' => $category->label,
                    'requires_member' => (bool)$category->requires_member,
                    'description' => $category->description,
                    'icon' => $category->icon
                ];
            }

            return $formattedCategories;
        } catch (Exception $e) {
            // Return empty array if there's an error (e.g., table doesn't exist yet)
            return [];
        }
    }

    /**
     * Check if a category is custom
     *
     * @param string $categoryName
     * @param string $categoryType
     * @return bool
     */
    public static function isCustomCategory($categoryName, $categoryType) {
        $customCategories = self::getCustomCategories($categoryType);
        return isset($customCategories[$categoryName]);
    }

    /**
     * Get all available icons for categories
     *
     * @return array
     */
    public static function getAvailableIcons() {
        return [
            'fas fa-hand-holding-heart' => '💰 Money/Finance',
            'fas fa-praying-hands' => '🙏 Prayer/Worship',
            'fas fa-building' => '🏗️ Building/Construction',
            'fas fa-gift' => '💝 Gift/Donation',
            'fas fa-seedling' => '🌱 Growth/Seed',
            'fas fa-handshake' => '📝 Agreement/Pledge',
            'fas fa-user-tie' => '👨‍💼 Leadership/Pastor',
            'fas fa-hands-helping' => '🤝 Help/Welfare',
            'fas fa-child' => '👶 Children/Youth',
            'fas fa-ellipsis-h' => '📋 Other/Misc',
            'fas fa-bolt' => '⚡ Utilities/Power',
            'fas fa-home' => '🏠 Building/Rent',
            'fas fa-tools' => '🔧 Maintenance/Repair',
            'fas fa-box' => '📦 Supplies/Materials',
            'fas fa-money-check-alt' => '💼 Salary/Payment',
            'fas fa-calendar-alt' => '🎉 Events/Programs',
            'fas fa-globe' => '🌍 Missions/Outreach',
            'fas fa-heart' => '❤️ Charity/Love',
            'fas fa-desktop' => '🖥️ Equipment/Technology',
            'fas fa-receipt' => '📋 Receipt/Document',
            'fas fa-car' => '🚗 Transportation',
            'fas fa-shield-alt' => '🛡️ Insurance/Protection',
            'fas fa-microphone' => '🎤 Speaking/Conference',
            'fas fa-star' => '🎉 Special/Important',
            'fas fa-shopping-cart' => '🛒 Purchase/Shopping',
            'fas fa-users' => '👥 Group/Community',
            'fas fa-circle' => '⚪ Default/Simple'
        ];
    }
}
