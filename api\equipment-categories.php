<?php
/**
 * Equipment Categories API
 * Handles CRUD operations for equipment categories
 */

// Include bootstrap to ensure proper initialization
require_once dirname(__DIR__) . '/bootstrap.php';

// Set headers for JSON response
header('Content-Type: application/json');

// SECURE CORS Configuration
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false || 
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
}

header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');
header('Access-Control-Allow-Credentials: true');

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }

    // Initialize database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Include the EquipmentCategory model
    require_once dirname(__DIR__) . '/models/EquipmentCategory.php';
    $equipmentCategory = new EquipmentCategory($conn);
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            // Get all equipment categories
            $categories = $equipmentCategory->getAll();
            
            // If no categories exist, initialize defaults
            if (empty($categories)) {
                $equipmentCategory->initializeDefaults();
                $categories = $equipmentCategory->getAll();
            }
            
            echo json_encode([
                'success' => true,
                'data' => $categories
            ]);
            break;
            
        case 'POST':
            // Validate CSRF token
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['csrf_token']) || !validate_csrf_token($input['csrf_token'])) {
                http_response_code(403);
                echo json_encode(['error' => 'Invalid CSRF token']);
                exit;
            }
            
            // Validate required fields
            if (empty($input['name']) || empty($input['value'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Category name and value are required']);
                exit;
            }
            
            // Set category properties
            $equipmentCategory->name = trim($input['name']);
            $equipmentCategory->value = trim($input['value']);
            $equipmentCategory->description = trim($input['description'] ?? '');
            $equipmentCategory->created_by = $_SESSION['user_id'];
            
            // Validate data
            $errors = $equipmentCategory->validate();
            if (!empty($errors)) {
                http_response_code(400);
                echo json_encode(['error' => implode(', ', $errors)]);
                exit;
            }
            
            // Check if value already exists
            if ($equipmentCategory->valueExists($equipmentCategory->value)) {
                http_response_code(409);
                echo json_encode(['error' => 'Category value already exists']);
                exit;
            }
            
            // Create the category
            if ($equipmentCategory->create()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Category created successfully',
                    'data' => [
                        'id' => $equipmentCategory->id,
                        'name' => $equipmentCategory->name,
                        'value' => $equipmentCategory->value,
                        'description' => $equipmentCategory->description
                    ]
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to create category']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Equipment Categories API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
