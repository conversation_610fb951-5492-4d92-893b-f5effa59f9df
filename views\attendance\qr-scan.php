<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-0 left-0 w-full h-full" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>');"></div>
        </div>

        <div class="container max-w-md mx-auto px-4 py-4 relative">
            <div class="text-center">
                <div class="w-14 h-14 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3 backdrop-blur-sm shadow-lg">
                    <i class="fas fa-church text-xl"></i>
                </div>
                <h1 class="text-xl font-bold mb-1">ICGC Emmanuel Temple</h1>
                <p class="text-blue-100 text-xs">Digital Attendance System</p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container max-w-md mx-auto px-4 -mt-2">
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 relative">
            <!-- Service Info Header -->
            <div class="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200 relative">
                <!-- Decorative elements -->
                <div class="absolute top-0 right-0 w-20 h-20 bg-blue-100 rounded-full -mr-10 -mt-10 opacity-50"></div>
                <div class="absolute bottom-0 left-0 w-16 h-16 bg-purple-100 rounded-full -ml-8 -mb-8 opacity-30"></div>

                <div class="text-center relative">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-2 shadow-lg">
                        <i class="fas fa-calendar-check text-white text-sm"></i>
                    </div>
                    <h2 class="text-lg font-bold text-gray-800 mb-1"><?php echo $service['name']; ?></h2>
                    <p class="text-xs text-gray-600 font-medium mb-2"><?php echo format_date($qr_session->attendance_date); ?></p>
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 shadow-sm">
                        <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        Session Active
                    </div>


                </div>
            </div>

            <div class="p-5">
                
                <!-- Search Form -->
                <div class="mb-5">
                    <div class="text-center mb-4">
                        <div class="inline-flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full mb-2">
                            <i class="fas fa-search text-blue-600 text-sm"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-1">Find Your Name</h3>
                        <p class="text-sm text-gray-600">Search by your name or phone number</p>
                    </div>

                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-500 text-lg"></i>
                        </div>
                        <input type="text" id="member_search"
                               class="block w-full pl-14 pr-14 py-5 border-3 border-gray-300 rounded-xl focus:ring-3 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-lg bg-white shadow-md focus:shadow-lg hover:border-gray-400"
                               placeholder="Type your name or phone number..."
                               style="border-width: 3px;">
                        <div id="search_loading" class="absolute inset-y-0 right-0 pr-5 flex items-center hidden">
                            <div class="w-6 h-6 border-3 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Results Container -->
                <div id="search_results" class="mb-6 hidden">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border-2 border-blue-200">
                        <h3 class="text-base font-semibold text-blue-800 mb-4 flex items-center">
                            <i class="fas fa-users mr-2"></i>
                            Select your name from the list:
                        </h3>
                        <div class="bg-white rounded-lg border-2 border-blue-100 max-h-80 overflow-y-auto shadow-sm">
                            <ul class="divide-y divide-gray-200" id="members_list">
                                <!-- Member results will be added here dynamically -->
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Selected Member Form -->
                <div id="member_form" class="hidden">
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 mb-6 border-2 border-green-200">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user-check text-green-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-green-800">Member Selected</h3>
                                <div class="mt-1 text-sm text-green-700">
                                    <p id="selected_member_name" class="font-medium">Name: <span class="font-bold">-</span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Family Members Section -->
                    <div id="family_section" class="hidden mb-6">
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border-2 border-blue-200">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-users text-blue-600"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-blue-800">Family Attendance</h3>
                                    <p class="text-sm text-blue-600">Select family members to mark attendance</p>
                                </div>
                            </div>

                            <div id="family_members_list" class="space-y-3">
                                <!-- Family members will be populated here -->
                            </div>
                        </div>
                    </div>

                    <form id="attendance_form" class="space-y-4">
                        <input type="hidden" id="member_identifier" name="member_identifier" value="">
                        <input type="hidden" id="parent_id" name="parent_id" value="">
                        <input type="hidden" id="token" name="token" value="<?php echo $token; ?>">
                        
                        <div>
                            <div class="text-center mb-4">
                                <div class="inline-flex items-center justify-center w-8 h-8 bg-purple-100 rounded-full mb-2">
                                    <i class="fas fa-clipboard-check text-purple-600 text-sm"></i>
                                </div>
                                <label class="block text-lg font-semibold text-gray-800">Select Your Status</label>
                            </div>
                            <div class="grid grid-cols-1 gap-3">
                                <div class="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl p-4 flex items-center hover:shadow-lg cursor-pointer attendance-option transition-all duration-200 hover:scale-[1.02] hover:border-green-300" data-status="present">
                                    <input type="radio" id="status_present" name="status" value="present" class="h-5 w-5 text-green-600 focus:ring-green-500 border-gray-300" checked>
                                    <label for="status_present" class="ml-4 text-base font-medium text-gray-700 flex items-center cursor-pointer w-full">
                                        <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mr-4 shadow-md">
                                            <i class="fas fa-check-circle text-white text-lg"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-bold text-green-800 text-lg">Present</div>
                                            <div class="text-sm text-green-600">I'm here on time</div>
                                        </div>
                                        <div class="w-6 h-6 border-2 border-green-300 rounded-full flex items-center justify-center">
                                            <div class="w-3 h-3 bg-green-500 rounded-full opacity-0 transition-opacity duration-200"></div>
                                        </div>
                                    </label>
                                </div>
                                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-200 rounded-xl p-4 flex items-center hover:shadow-lg cursor-pointer attendance-option transition-all duration-200 hover:scale-[1.02] hover:border-yellow-300" data-status="late">
                                    <input type="radio" id="status_late" name="status" value="late" class="h-5 w-5 text-yellow-600 focus:ring-yellow-500 border-gray-300">
                                    <label for="status_late" class="ml-4 text-base font-medium text-gray-700 flex items-center cursor-pointer w-full">
                                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mr-4 shadow-md">
                                            <i class="fas fa-clock text-white text-lg"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-bold text-yellow-800 text-lg">Late</div>
                                            <div class="text-sm text-yellow-600">I arrived late</div>
                                        </div>
                                        <div class="w-6 h-6 border-2 border-yellow-300 rounded-full flex items-center justify-center">
                                            <div class="w-3 h-3 bg-yellow-500 rounded-full opacity-0 transition-opacity duration-200"></div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <button type="submit" class="w-full flex justify-center items-center py-4 px-6 border border-transparent rounded-xl shadow-lg text-lg font-bold text-white bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl active:scale-[0.98] relative overflow-hidden">
                                <!-- Button shine effect -->
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 hover:opacity-20 transition-opacity duration-300 transform -skew-x-12"></div>
                                <i class="fas fa-user-check mr-3 text-xl relative z-10"></i>
                                <span class="relative z-10">Mark My Attendance</span>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Success Message -->
                <div id="success_message" class="hidden">
                    <div class="text-center py-8 animate-fade-in">
                        <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-bounce shadow-lg">
                            <i class="fas fa-check text-white text-3xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-green-800 mb-3">Attendance Marked!</h2>
                        <p class="text-lg text-green-700 mb-6" id="success_text">Your attendance has been recorded successfully!</p>

                        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-2 border-green-200 mb-6 shadow-sm">
                            <div class="flex items-center justify-center text-green-800">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-clock text-green-600"></i>
                                </div>
                                <span class="font-medium">Recorded at: <span id="attendance_time" class="font-bold"></span></span>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <button onclick="markAnotherAttendance()" class="w-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl relative overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 hover:opacity-20 transition-opacity duration-300 transform -skew-x-12"></div>
                                <i class="fas fa-plus mr-2 relative z-10"></i>
                                <span class="relative z-10">Mark Another Attendance</span>
                            </button>
                            <p class="text-sm text-gray-600 flex items-center justify-center">
                                <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                                You can help others mark their attendance too!
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Error Message -->
                <div id="error_message" class="hidden">
                    <div class="bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200 rounded-xl p-6">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-red-800 mb-2">Oops! Something went wrong</h3>
                            <p class="text-sm text-red-600 mb-4" id="error_text">Something went wrong. Please try again.</p>
                            <button onclick="resetForm()" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                                <i class="fas fa-redo mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="text-center mt-8 py-4">
            <div class="text-sm text-gray-600 mb-2">
                <i class="fas fa-church mr-1"></i>
                ICGC Emmanuel Temple
            </div>
            <div class="text-xs text-gray-400">
                © 2025 All rights reserved. Church Management System
            </div>
        </div>
    </div>
</div>

<script>
    // Network connectivity helper
    function checkNetworkConnection() {
        return navigator.onLine;
    }



    document.addEventListener('DOMContentLoaded', function() {
        const memberSearch = document.getElementById('member_search');
        const searchResults = document.getElementById('search_results');
        const membersList = document.getElementById('members_list');
        const memberForm = document.getElementById('member_form');
        const selectedMemberName = document.getElementById('selected_member_name');
        const memberIdentifier = document.getElementById('member_identifier');
        const attendanceForm = document.getElementById('attendance_form');
        const successMessage = document.getElementById('success_message');
        const successText = document.getElementById('success_text');
        const errorMessage = document.getElementById('error_message');
        const errorText = document.getElementById('error_text');
        
        // Search for members as user types
        let searchTimeout;
        memberSearch.addEventListener('input', function() {
            const query = this.value.trim();
            
            // Clear previous timeout
            clearTimeout(searchTimeout);
            
            if (query.length < 2) {
                searchResults.classList.add('hidden');
                return;
            }
            
            // Set a small timeout to avoid too many requests
            searchTimeout = setTimeout(() => {
                fetchMembers(query);
            }, 300);
        });
        
        // Fetch members from the server
        function fetchMembers(query) {
            // Show loading state
            membersList.innerHTML = `
                <li class="px-6 py-8 text-center">
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                            <i class="fas fa-spinner fa-spin text-blue-600"></i>
                        </div>
                        <div class="text-sm font-medium text-gray-700">Searching members...</div>
                        <div class="text-xs text-gray-500 mt-1">Please wait</div>
                    </div>
                </li>
            `;
            searchResults.classList.remove('hidden');
            
            // Fetch members
            const baseUrl = "<?php echo BASE_URL; ?>";
            fetch(baseUrl + 'api/search-members.php?search=' + encodeURIComponent(query))
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.members && data.members.length > 0) {
                        // Clear previous results
                        membersList.innerHTML = '';
                        
                        // Add members to the list
                        data.members.forEach(member => {
                            const li = document.createElement('li');
                            li.className = 'px-4 py-4 hover:bg-blue-50 cursor-pointer transition-all duration-200 member-item border-b border-gray-200 last:border-b-0 hover:shadow-sm';
                            li.innerHTML = `
                                <div class="flex items-center w-full">
                                    <div class="flex items-center flex-1 mr-3">
                                        ${member.profile_picture ?
                                            `<img src="${'<?php echo BASE_URL; ?>uploads/profile_pictures/'}${member.profile_picture}" alt="Profile" class="h-12 w-12 rounded-full mr-3 border-2 border-blue-200 shadow-sm flex-shrink-0">` :
                                            `<div class="h-12 w-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center mr-3 text-white font-bold text-base shadow-sm flex-shrink-0">
                                                ${member.first_name.charAt(0)}${member.last_name.charAt(0)}
                                            </div>`
                                        }
                                        <div class="flex-1">
                                            <div class="font-semibold text-gray-800 text-base leading-tight">${member.first_name} ${member.last_name}</div>
                                            ${member.phone_number ? `<div class="text-sm text-gray-600 mt-1"><i class="fas fa-phone mr-1"></i>${member.phone_number}</div>` : ''}
                                            ${member.department ? `<div class="text-xs text-blue-600 font-medium mt-1"><i class="fas fa-building mr-1"></i>${member.department}</div>` : ''}
                                        </div>
                                    </div>
                                    <div class="flex-shrink-0 ml-2">
                                        <button class="px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                                            Select
                                        </button>
                                    </div>
                                </div>
                            `;
                            
                            // Add click event
                            li.addEventListener('click', function() {
                                selectMember(member);
                            });
                            
                            membersList.appendChild(li);
                        });
                    } else {
                        // No results
                        membersList.innerHTML = '<li class="px-4 py-3 text-center text-sm text-gray-500">No members found. Try a different search.</li>';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    membersList.innerHTML = '<li class="px-4 py-3 text-center text-sm text-red-500">Error fetching members. Please try again.</li>';
                });
        }
        
        // Select a member
        function selectMember(member) {
            // Set selected member details
            memberIdentifier.value = member.id;
            document.getElementById('parent_id').value = member.id;
            selectedMemberName.innerHTML = `Name: <span class="font-medium">${member.first_name} ${member.last_name}</span>`;

            // Hide search results and show member form
            searchResults.classList.add('hidden');
            memberForm.classList.remove('hidden');

            // Hide error message if shown
            errorMessage.classList.add('hidden');

            // Load family members
            loadFamilyMembers(member.id);
        }

        // Load family members for attendance
        function loadFamilyMembers(memberId) {
            const familySection = document.getElementById('family_section');
            const familyMembersList = document.getElementById('family_members_list');

            // Show loading state
            familyMembersList.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-blue-500"></i> Loading family members...</div>';

            fetch('<?php echo BASE_URL; ?>attendance/qr-get-family', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `member_identifier=${encodeURIComponent(memberId)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.has_children) {
                        // Show family section
                        familySection.classList.remove('hidden');

                        // Build family members list
                        let familyHtml = '';

                        // Add parent (always selected by default)
                        familyHtml += `
                            <div class="bg-white rounded-lg border-2 border-green-200 p-4">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" name="selected_members[]" value="${data.parent.id}"
                                           checked class="w-5 h-5 text-green-600 border-2 border-gray-300 rounded focus:ring-green-500">
                                    <div class="ml-3 flex-1">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-user text-green-600"></i>
                                            </div>
                                            <div>
                                                <div class="font-semibold text-gray-800">${data.parent.name}</div>
                                                <div class="text-sm text-gray-600">Parent • ${data.parent.department || 'No department'}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-green-600 font-medium">You</div>
                                </label>
                            </div>
                        `;

                        // Add children
                        data.children.forEach(child => {
                            familyHtml += `
                                <div class="bg-white rounded-lg border-2 border-blue-200 p-4">
                                    <label class="flex items-center cursor-pointer">
                                        <input type="checkbox" name="selected_members[]" value="${child.child_id}"
                                               class="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500">
                                        <div class="ml-3 flex-1">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                    <i class="fas fa-child text-blue-600"></i>
                                                </div>
                                                <div>
                                                    <div class="font-semibold text-gray-800">${child.first_name} ${child.last_name}</div>
                                                    <div class="text-sm text-gray-600">Child • Age ${child.age}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-blue-600 text-sm">
                                            <i class="fas fa-shield-alt mr-1"></i>
                                            Security code will be provided
                                        </div>
                                    </label>
                                </div>
                            `;
                        });

                        familyMembersList.innerHTML = familyHtml;

                        // Update form submission to handle family attendance
                        updateFormForFamilyAttendance();
                    } else {
                        // No children, hide family section
                        familySection.classList.add('hidden');
                    }
                } else {
                    // Error loading family, hide family section
                    familySection.classList.add('hidden');
                    console.error('Failed to load family members:', data.error);
                }
            })
            .catch(error => {
                console.error('Error loading family members:', error);
                familySection.classList.add('hidden');
            });
        }

        // Update form submission for family attendance
        function updateFormForFamilyAttendance() {
            const attendanceForm = document.getElementById('attendance_form');

            // Remove existing event listeners by cloning the form
            const newForm = attendanceForm.cloneNode(true);
            attendanceForm.parentNode.replaceChild(newForm, attendanceForm);

            // Add new event listener for family attendance
            newForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const token = document.getElementById('token').value;
                const parentId = document.getElementById('parent_id').value;
                const status = document.querySelector('input[name="status"]:checked').value;
                const selectedMembers = Array.from(document.querySelectorAll('input[name="selected_members[]"]:checked')).map(cb => cb.value);

                if (selectedMembers.length === 0) {
                    showError('Please select at least one family member to mark attendance.');
                    return;
                }

                // Check network connection before proceeding
                if (!checkNetworkConnection()) {
                    showError('No internet connection. Please check your network and try again.');
                    return;
                }

                const submitBtn = this.querySelector('button[type="submit"]');
                const originalBtnText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
                submitBtn.disabled = true;

                // Send request to mark family attendance
                const formData = new FormData();
                formData.append('token', token);
                formData.append('parent_id', parentId);
                formData.append('status', status);
                selectedMembers.forEach(memberId => {
                    formData.append('selected_members[]', memberId);
                });

                // Send family attendance request
                const familyController = new AbortController();
                const familyTimeoutId = setTimeout(() => familyController.abort(), 30000);

                fetch('<?php echo BASE_URL; ?>attendance/qr-mark-family', {
                    method: 'POST',
                    body: formData,
                    signal: familyController.signal,
                    credentials: 'same-origin',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    clearTimeout(familyTimeoutId);

                    if (!response.ok) {
                        throw new Error(`Server error (${response.status}). Please try again.`);
                    }

                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Invalid server response. Please try again.');
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Show success message with security codes
                        showFamilySuccessMessage(data);
                    } else {
                        // Reset button state
                        submitBtn.innerHTML = originalBtnText;
                        submitBtn.disabled = false;

                        // Show error message
                        showError(data.error || 'Failed to mark attendance. Please try again.');
                    }
                })
                .catch(error => {
                    clearTimeout(familyTimeoutId);
                    console.error('QR Family Mark Error:', error);

                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;

                    // Show appropriate error message
                    let errorMessage = 'Unable to mark family attendance. Please try again.';

                    if (error.name === 'AbortError') {
                        errorMessage = 'Request timed out. Please check your connection and try again.';
                    } else if (error.message.includes('Server error')) {
                        errorMessage = 'Server is busy. Please wait a moment and try again.';
                    } else if (error.message.includes('Invalid server response')) {
                        errorMessage = 'Connection issue. Please try again.';
                    } else if (error.name === 'TypeError' || error.message.includes('Failed to fetch')) {
                        errorMessage = 'Network connection problem. Please check your internet and try again.';
                    } else if (navigator.onLine === false) {
                        errorMessage = 'No internet connection. Please check your network and try again.';
                    }

                    showError(errorMessage);
                });
            });
        }

        // Show family success message with security codes
        function showFamilySuccessMessage(data) {
            const memberForm = document.getElementById('member_form');
            const successMessage = document.getElementById('success_message');
            const successText = document.getElementById('success_text');

            memberForm.classList.add('hidden');
            successMessage.classList.remove('hidden');

            let message = `Attendance marked for ${data.marked_count} family member(s)!`;

            if (data.children_checked_in > 0) {
                message += `\n\n${data.children_checked_in} children checked into children's ministry.`;

                if (data.security_codes && data.security_codes.length > 0) {
                    message += '\n\nSecurity codes for pickup:';
                    data.security_codes.forEach(code => {
                        message += `\n• ${code.name}: ${code.code}`;
                    });
                }
            }

            successText.textContent = message;

            // Set attendance time
            document.getElementById('attendance_time').textContent = new Date().toLocaleTimeString();

            // Scroll to success message
            successMessage.scrollIntoView({ behavior: 'smooth' });
        }
        
        // Make status options clickable with visual feedback
        document.querySelectorAll('.attendance-option').forEach(option => {
            option.addEventListener('click', function() {
                const status = this.dataset.status;
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;

                // Update visual feedback
                updateStatusSelection();
            });
        });

        // Update visual feedback for status selection
        function updateStatusSelection() {
            document.querySelectorAll('.attendance-option').forEach(option => {
                const radio = option.querySelector('input[type="radio"]');
                const indicator = option.querySelector('.w-3.h-3');

                if (radio.checked) {
                    indicator.style.opacity = '1';
                    option.style.transform = 'scale(1.02)';
                    option.style.borderWidth = '3px';
                } else {
                    indicator.style.opacity = '0';
                    option.style.transform = 'scale(1)';
                    option.style.borderWidth = '2px';
                }
            });
        }

        // Initialize status selection visual feedback
        updateStatusSelection();
        
        // Submit attendance form
        attendanceForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const memberId = memberIdentifier.value;
            const token = document.getElementById('token').value;
            const status = document.querySelector('input[name="status"]:checked').value;
            
            if (!memberId) {
                showError('Please select a member first.');
                return;
            }
            
            // Check network connection before proceeding
            if (!checkNetworkConnection()) {
                showError('No internet connection. Please check your network and try again.');
                return;
            }

            // Show loading state on button
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
            submitBtn.disabled = true;
            
            // Send request to mark attendance
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

            const requestBody = `token=${encodeURIComponent(token)}&member_identifier=${encodeURIComponent(memberId)}&status=${encodeURIComponent(status)}`;

            // Ensure proper URL construction for mobile
            let baseUrl = '<?php echo BASE_URL; ?>';

            // Fix relative URL issue for mobile devices
            if (baseUrl.startsWith('/') && !baseUrl.startsWith('//')) {
                // Convert relative URL to absolute URL
                baseUrl = window.location.protocol + '//' + window.location.host + baseUrl;
            }

            if (!baseUrl.endsWith('/')) {
                baseUrl += '/';
            }

            const requestUrl = baseUrl + 'attendance/qr-mark';

            fetch(requestUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Cache-Control': 'no-cache',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: requestBody,
                signal: controller.signal,
                credentials: 'same-origin'
            })
            .then(response => {
                clearTimeout(timeoutId); // Clear timeout on successful response

                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`Server error (${response.status}). Please try again.`);
                }

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Invalid server response. Please try again.');
                }

                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success message
                    memberForm.classList.add('hidden');
                    successMessage.classList.remove('hidden');
                    successText.textContent = data.message || 'Your attendance has been marked successfully!';

                    // Add member name to success message
                    document.querySelector('#success_message h2').textContent = `Attendance Marked!`;

                    // Set attendance time
                    document.getElementById('attendance_time').textContent = new Date().toLocaleTimeString();

                    // Scroll to success message
                    successMessage.scrollIntoView({ behavior: 'smooth' });
                } else {
                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;

                    // Show error message
                    showError(data.error || 'Failed to mark attendance. Please try again.');
                }
            })
            .catch(error => {
                clearTimeout(timeoutId);
                console.error('QR Mark Error:', error);

                // Reset button state
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;

                // Show appropriate error message
                let errorMessage = 'Unable to mark attendance. Please try again.';

                if (error.name === 'AbortError') {
                    errorMessage = 'Request timed out. Please check your connection and try again.';
                } else if (error.message.includes('Server error')) {
                    errorMessage = 'Server is busy. Please wait a moment and try again.';
                } else if (error.message.includes('Invalid server response')) {
                    errorMessage = 'Connection issue. Please try again.';
                } else if (error.name === 'TypeError' || error.message.includes('Failed to fetch')) {
                    errorMessage = 'Network connection problem. Please check your internet and try again.';
                } else if (navigator.onLine === false) {
                    errorMessage = 'No internet connection. Please check your network and try again.';
                }

                showError(errorMessage);
            });
        });
        
        // Show error message
        function showError(message) {
            errorText.textContent = message;
            errorMessage.classList.remove('hidden');

            // Scroll to error
            errorMessage.scrollIntoView({ behavior: 'smooth' });
        }
    });

    // Function to mark another attendance (reset form)
    function markAnotherAttendance() {
        // Hide success message
        document.getElementById('success_message').classList.add('hidden');

        // Show search section
        document.getElementById('member_search').style.display = 'block';

        // Reset search input
        document.getElementById('member_search').value = '';

        // Hide search results
        document.getElementById('search_results').classList.add('hidden');

        // Hide member form
        document.getElementById('member_form').classList.add('hidden');

        // Hide error message
        document.getElementById('error_message').classList.add('hidden');

        // Reset form
        document.getElementById('attendance_form').reset();

        // Reset status to present
        document.getElementById('status_present').checked = true;

        // Reset button state (CRITICAL FIX)
        const submitBtn = document.querySelector('#attendance_form button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-user-check mr-3 text-xl"></i> Mark My Attendance';
            submitBtn.disabled = false;
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }

        // Clear member identifier
        document.getElementById('member_identifier').value = '';

        // Focus on search input
        document.getElementById('member_search').focus();

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Function to reset form after error
    function resetForm() {
        // Hide error message
        document.getElementById('error_message').classList.add('hidden');

        // Show member form if member was selected
        const memberForm = document.getElementById('member_form');
        if (!memberForm.classList.contains('hidden')) {
            // Reset button state completely
            const submitBtn = document.querySelector('#attendance_form button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-user-check mr-3 text-xl"></i> Mark My Attendance';
                submitBtn.disabled = false;
                submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        } else {
            // Go back to search
            document.getElementById('member_search').focus();
        }
    }

    // Add loading animation to search
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('member_search');
        const searchLoading = document.getElementById('search_loading');

        let searchTimeout;
        searchInput.addEventListener('input', function() {
            // Show loading
            searchLoading.classList.remove('hidden');

            // Clear previous timeout
            clearTimeout(searchTimeout);

            // Hide loading after search completes
            searchTimeout = setTimeout(() => {
                searchLoading.classList.add('hidden');
            }, 500);
        });
    });
</script>

<style>
/* Enhanced Mobile Styles */
@media (max-width: 640px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Smooth animations */
.attendance-option {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.attendance-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.attendance-option:hover::before {
    left: 100%;
}

.attendance-option:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.attendance-option:active {
    transform: translateY(0);
}

/* Button hover effects */
button[type="submit"]:hover {
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.3);
}

/* Input focus effects */
#member_search:focus {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Loading animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Success animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

/* Fade in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Gradient text */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced focus states */
input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Member list item hover effect */
.member-item:hover {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    transform: translateX(4px);
}
</style>