/**
 * ICGC Emmanuel Temple Dashboard JavaScript
 * Enhances the dashboard with animations and interactivity
 */

document.addEventListener('DOMContentLoaded', function() {
    // Immediate: Critical UI elements
    animateStatsCards();
    initializeGenderChart();
    setupKeyboardShortcuts();

    // Deferred: Non-critical features (load after 1 second)
    setTimeout(function() {
        initializeNotifications();
        initializeMobileFeatures();
        initializeQuickActions();
    }, 1000);
});

/**
 * Animates the stats cards with a staggered fade-in effect
 */
function animateStatsCards() {
    const cards = document.querySelectorAll('.gradient-card');

    // Use requestAnimationFrame for better performance
    requestAnimationFrame(() => {
        cards.forEach((card, index) => {
            // Add initial invisible state
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

            // Stagger the animations with reduced delay
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 50 * index);
        });
    });
}

/**
 * Initializes gender chart only (other charts handled by dedicated files)
 */
function initializeGenderChart() {
    // Check if Chart.js is available
    if (typeof Chart === 'undefined') {
        return;
    }

    // Enhanced Gender Chart
    const genderCtx = document.getElementById('gender-chart');
    if (genderCtx) {
        console.log('Initializing gender chart...');
        const malePercentage = parseFloat(document.querySelector('[data-gender="male"]')?.textContent) || 0;
        const femalePercentage = parseFloat(document.querySelector('[data-gender="female"]')?.textContent) || 0;

        console.log('Gender data:', { male: malePercentage, female: femalePercentage });

        new Chart(genderCtx, {
            type: 'doughnut',
            data: {
                labels: ['Male', 'Female'],
                datasets: [{
                    data: [malePercentage, femalePercentage],
                    backgroundColor: ['#3b82f6', '#ec4899'],
                    borderColor: ['#ffffff', '#ffffff'],
                    borderWidth: 3,
                    hoverOffset: 8,
                    hoverBorderWidth: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 13,
                                weight: '500'
                            },
                            color: '#374151'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(55, 65, 81, 0.95)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#d1d5db',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                },
                cutout: '65%',
                animation: {
                    animateRotate: true,
                    duration: 1500,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    // Age and Department charts removed to prevent conflicts with dedicated chart files
            type: 'doughnut',
            data: {
                labels: Object.keys(ageData),
                datasets: [{
                    data: Object.values(ageData),
                    backgroundColor: ['#10b981', '#3b82f6', '#8b5cf6', '#f59e0b'],
                    borderColor: ['#ffffff', '#ffffff', '#ffffff', '#ffffff'],
                    borderWidth: 3,
                    hoverOffset: 6,
                    hoverBorderWidth: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#374151'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(55, 65, 81, 0.95)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#d1d5db',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                },
                cutout: '60%',
                animation: {
                    animateRotate: true,
                    duration: 1500,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // Age chart type selector
        const ageChartTypeSelect = document.getElementById('age-chart-type');
        if (ageChartTypeSelect) {
            ageChartTypeSelect.addEventListener('change', function() {
                ageChart.destroy();

                const chartType = this.value;
                const chartOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: chartType === 'bar' ? 'top' : 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(55, 65, 81, 0.95)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 1500
                    }
                };

                if (chartType === 'bar') {
                    chartOptions.scales = {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                },
                                color: '#6b7280'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#6b7280'
                            }
                        }
                    };
                } else {
                    chartOptions.cutout = chartType === 'doughnut' ? '50%' : '0%';
                }

                ageChart = new Chart(ageCtx, {
                    type: chartType,
                    data: {
                        labels: Object.keys(ageData),
                        datasets: [{
                            data: Object.values(ageData),
                            backgroundColor: ['#10b981', '#3b82f6', '#8b5cf6', '#f59e0b'],
                            borderColor: ['#ffffff', '#ffffff', '#ffffff', '#ffffff'],
                            borderWidth: 2,
                            hoverOffset: chartType !== 'bar' ? 8 : 0
                        }]
                    },
                    options: chartOptions
                });
            });
        }
    }

    // Enhanced Department Distribution Chart
    const deptCtx = document.getElementById('department-chart');
    if (deptCtx) {
        console.log('Initializing department chart...');
        const deptElements = document.querySelectorAll('[data-dept-name]');
        const deptCountElements = document.querySelectorAll('[data-dept-count]');

        const deptLabels = Array.from(deptElements).map(el => el.textContent.trim());
        const deptData = Array.from(deptCountElements).map(el => parseInt(el.textContent) || 0);

        console.log('Department data:', { labels: deptLabels, data: deptData });

        // Check if we have valid data
        if (deptLabels.length === 0 || deptData.every(val => val === 0)) {
            console.log('No department data available, showing placeholder');
            deptCtx.getContext('2d').fillText('No department data available', 50, 50);
            return;
        }

        // Generate professional colors for departments
        const deptColors = [
            '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ef4444',
            '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
        ];

        const deptBorderColors = [
            '#1d4ed8', '#059669', '#d97706', '#7c3aed', '#dc2626',
            '#0891b2', '#65a30d', '#ea580c', '#be185d', '#4f46e5'
        ];

        let deptChart = new Chart(deptCtx, {
            type: 'bar',
            data: {
                labels: deptLabels,
                datasets: [{
                    label: 'Members',
                    data: deptData,
                    backgroundColor: deptColors.slice(0, deptLabels.length).map(color => color + '30'),
                    borderColor: deptColors.slice(0, deptLabels.length),
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                    hoverBackgroundColor: deptColors.slice(0, deptLabels.length).map(color => color + '60'),
                    hoverBorderColor: deptBorderColors.slice(0, deptLabels.length),
                    hoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(55, 65, 81, 0.95)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#d1d5db',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                const total = deptData.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed.y / total) * 100).toFixed(1);
                                return context.dataset.label + ': ' + context.parsed.y + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(156, 163, 175, 0.2)',
                            borderColor: '#e5e7eb'
                        },
                        ticks: {
                            precision: 0,
                            color: '#6b7280',
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        },
                        border: {
                            color: '#e5e7eb'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280',
                            maxRotation: 45,
                            minRotation: 0,
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        },
                        border: {
                            color: '#e5e7eb'
                        }
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // Department chart type selector
        const deptChartTypeSelect = document.getElementById('dept-chart-type');
        if (deptChartTypeSelect) {
            deptChartTypeSelect.addEventListener('change', function() {
                deptChart.destroy();

                const chartType = this.value;
                const chartOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: chartType !== 'bar',
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                font: {
                                    size: 10
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(55, 65, 81, 0.95)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            callbacks: {
                                label: function(context) {
                                    const total = deptData.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 1500
                    }
                };

                if (chartType === 'bar') {
                    chartOptions.scales = {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                precision: 0,
                                color: '#6b7280'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#6b7280',
                                maxRotation: 45,
                                minRotation: 0
                            }
                        }
                    };
                } else {
                    chartOptions.cutout = chartType === 'doughnut' ? '50%' : '0%';
                }

                deptChart = new Chart(deptCtx, {
                    type: chartType,
                    data: {
                        labels: deptLabels,
                        datasets: [{
                            label: 'Members',
                            data: deptData,
                            backgroundColor: deptColors.slice(0, deptLabels.length),
                            borderColor: deptColors.slice(0, deptLabels.length).map(color => color + 'CC'),
                            borderWidth: 2,
                            borderRadius: chartType === 'bar' ? 6 : 0,
                            borderSkipped: chartType === 'bar' ? false : true,
                            hoverOffset: chartType !== 'bar' ? 10 : 0
                        }]
                    },
                    options: chartOptions
                });
            });
        }
    }

    // Chart functionality handled by dedicated script files

    // Financial overview chart functionality moved to inline script in HTML





    // Duplicate department chart code removed - using original implementation above
}

/**
 * Initialize notification system
 */
function initializeNotifications() {
    // Initialize notification center first (no API call)
    setupNotificationCenter();

    // Delay notification check to not block page load
    setTimeout(checkForNotifications, 2000);

    // Set up periodic notification checks (every 5 minutes)
    setInterval(checkForNotifications, 300000);
}

/**
 * Check for new notifications
 */
function checkForNotifications() {
    // Fetch notifications from API
    fetch('/icgc/api/dashboard_data.php?action=notifications')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const badge = document.getElementById('notification-badge');
                if (data.count > 0) {
                    badge.textContent = data.count;
                    badge.classList.remove('hidden');

                    // Show notifications
                    data.notifications.forEach((notification, index) => {
                        setTimeout(() => {
                            showNotification(notification.message, getNotificationType(notification.type), notification.type);
                        }, index * 2000); // Stagger notifications
                    });
                } else {
                    badge.classList.add('hidden');
                }
            }
        })
        .catch(error => {
            console.error('Error fetching notifications:', error);
            // Fallback to local checks
            checkBirthdayNotifications();
            checkAttendanceAlerts();
            checkFinancialAlerts();
            checkInactiveMemberAlerts();
        });
}

/**
 * Get notification type for styling
 */
function getNotificationType(type) {
    switch(type) {
        case 'birthday': return 'info';
        case 'attendance': return 'warning';
        case 'financial': return 'warning';
        default: return 'info';
    }
}

/**
 * Check for birthday notifications
 */
function checkBirthdayNotifications() {
    const birthdayElements = document.querySelectorAll('[data-birthday-today]');
    if (birthdayElements.length > 0) {
        const count = birthdayElements.length;
        showNotification(
            `🎉 ${count} member${count > 1 ? 's have' : ' has'} birthday${count > 1 ? 's' : ''} today!`,
            'info',
            'birthday'
        );
    }
}

/**
 * Check for attendance alerts
 */
function checkAttendanceAlerts() {
    // This would typically fetch data from the server
    // For now, we'll use sample logic
    const lastAttendance = document.querySelector('[data-last-attendance]');
    if (lastAttendance) {
        const attendance = parseInt(lastAttendance.textContent);
        const avgAttendance = parseInt(lastAttendance.getAttribute('data-avg-attendance') || '0');

        if (attendance < avgAttendance * 0.8) {
            showNotification(
                '⚠️ Last service attendance was significantly lower than average',
                'warning',
                'attendance'
            );
        }
    }
}

/**
 * Check for financial alerts
 */
function checkFinancialAlerts() {
    const balanceElement = document.querySelector('[data-financial-balance]');
    if (balanceElement) {
        const balance = parseFloat(balanceElement.textContent.replace(/[^\d.-]/g, ''));
        if (balance < 1000) {
            showNotification(
                '💰 Church balance is running low. Consider reviewing expenses.',
                'warning',
                'financial'
            );
        }
    }
}

/**
 * Check for inactive member alerts
 */
function checkInactiveMemberAlerts() {
    const inactiveElement = document.querySelector('[data-inactive-members]');
    if (inactiveElement) {
        const inactiveCount = parseInt(inactiveElement.textContent);
        if (inactiveCount > 10) {
            showNotification(
                `📋 ${inactiveCount} members haven't attended recently. Consider follow-up.`,
                'info',
                'members'
            );
        }
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info', category = 'general') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type} fixed top-4 right-4 z-50 transform translate-x-full transition-transform duration-300 ease-in-out`;

    const bgColor = {
        'success': 'bg-green-500',
        'warning': 'bg-yellow-500',
        'error': 'bg-red-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    const icon = {
        'success': 'fa-check-circle',
        'warning': 'fa-exclamation-triangle',
        'error': 'fa-times-circle',
        'info': 'fa-info-circle'
    }[type] || 'fa-info-circle';

    notification.innerHTML = `
        <div class="${bgColor} text-white px-6 py-4 rounded-lg shadow-lg max-w-sm">
            <div class="flex items-start">
                <i class="fas ${icon} mt-1 mr-3"></i>
                <div class="flex-1">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <button class="ml-3 text-white hover:text-gray-200 focus:outline-none" onclick="dismissNotification(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto dismiss after 8 seconds
    setTimeout(() => {
        dismissNotification(notification.querySelector('button'));
    }, 8000);

    // Add to notification center
    addToNotificationCenter(message, type, category);
}

/**
 * Dismiss notification
 */
function dismissNotification(button) {
    const notification = button.closest('.notification');
    notification.classList.add('translate-x-full');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * Setup notification center
 */
function setupNotificationCenter() {
    // Create notification center if it doesn't exist
    if (!document.getElementById('notification-center')) {
        const notificationCenter = document.createElement('div');
        notificationCenter.id = 'notification-center';
        notificationCenter.className = 'hidden fixed top-16 right-4 w-80 bg-white rounded-lg shadow-xl border z-40 max-h-96 overflow-y-auto';
        notificationCenter.innerHTML = `
            <div class="p-4 border-b">
                <h3 class="font-semibold text-gray-800">Notifications</h3>
            </div>
            <div id="notification-list" class="p-2">
                <p class="text-gray-500 text-sm p-4 text-center">No notifications</p>
            </div>
        `;
        document.body.appendChild(notificationCenter);
    }
}

/**
 * Add notification to notification center
 */
function addToNotificationCenter(message, type, category) {
    const notificationList = document.getElementById('notification-list');
    if (!notificationList) return;

    // Remove "no notifications" message
    const noNotifications = notificationList.querySelector('.text-gray-500');
    if (noNotifications) {
        noNotifications.remove();
    }

    const notificationItem = document.createElement('div');
    notificationItem.className = 'p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer';
    notificationItem.innerHTML = `
        <div class="flex items-start">
            <div class="w-2 h-2 rounded-full bg-${type === 'warning' ? 'yellow' : type === 'error' ? 'red' : 'blue'}-500 mt-2 mr-3"></div>
            <div class="flex-1">
                <p class="text-sm text-gray-800">${message}</p>
                <p class="text-xs text-gray-500 mt-1">${new Date().toLocaleTimeString()}</p>
            </div>
        </div>
    `;

    notificationList.insertBefore(notificationItem, notificationList.firstChild);

    // Limit to 10 notifications
    const notifications = notificationList.querySelectorAll('.p-3');
    if (notifications.length > 10) {
        notifications[notifications.length - 1].remove();
    }
}

/**
 * Initialize mobile features
 */
function initializeMobileFeatures() {
    // Add mobile-specific touch interactions
    addTouchInteractions();

    // Create floating action button for mobile
    createFloatingActionButton();

    // Optimize charts for mobile
    optimizeChartsForMobile();

    // Add swipe gestures for cards
    addSwipeGestures();
}

/**
 * Add touch interactions
 */
function addTouchInteractions() {
    const cards = document.querySelectorAll('.gradient-card, .card');
    cards.forEach(card => {
        card.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
        });

        card.addEventListener('touchend', function() {
            this.style.transform = '';
        });
    });
}

/**
 * Create floating action button for mobile
 */
function createFloatingActionButton() {
    if (window.innerWidth <= 768) {
        const fab = document.createElement('div');
        fab.id = 'mobile-fab';
        fab.className = 'fixed bottom-6 right-6 z-50 md:hidden';
        fab.innerHTML = `
            <button class="bg-primary text-white w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" onclick="toggleMobileMenu()">
                <i class="fas fa-plus text-xl"></i>
            </button>
            <div id="mobile-menu" class="hidden absolute bottom-16 right-0 bg-white rounded-lg shadow-xl border p-2 w-48">
                <a href="${window.BASE_URL || '/icgc/'}members/add" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 rounded flex items-center">
                    <i class="fas fa-user-plus mr-3 text-primary"></i>
                    Add Member
                </a>
                <a href="${window.BASE_URL || '/icgc/'}attendance/qr" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 rounded flex items-center">
                    <i class="fas fa-qrcode mr-3 text-primary"></i>
                    QR Attendance
                </a>
                <a href="${window.BASE_URL || '/icgc/'}finances/add" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 rounded flex items-center">
                    <i class="fas fa-plus-circle mr-3 text-primary"></i>
                    Add Transaction
                </a>
                <a href="${window.BASE_URL || '/icgc/'}sms/send" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 rounded flex items-center">
                    <i class="fas fa-sms mr-3 text-primary"></i>
                    Send SMS
                </a>
            </div>
        `;
        document.body.appendChild(fab);
    }
}

/**
 * Toggle mobile menu
 */
function toggleMobileMenu() {
    const menu = document.getElementById('mobile-menu');
    const fab = document.querySelector('#mobile-fab button i');

    if (menu.classList.contains('hidden')) {
        menu.classList.remove('hidden');
        fab.style.transform = 'rotate(45deg)';
    } else {
        menu.classList.add('hidden');
        fab.style.transform = 'rotate(0deg)';
    }
}

/**
 * Optimize charts for mobile
 */
function optimizeChartsForMobile() {
    if (window.innerWidth <= 768) {
        // Reduce chart padding and font sizes for mobile
        Chart.defaults.font.size = 10;
        Chart.defaults.plugins.legend.labels.padding = 10;
    }
}

/**
 * Add swipe gestures for cards
 */
function addSwipeGestures() {
    const cards = document.querySelectorAll('.gradient-card');
    cards.forEach(card => {
        let startX = 0;
        let startY = 0;

        card.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        card.addEventListener('touchend', function(e) {
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const diffX = startX - endX;
            const diffY = startY - endY;

            // If swipe is more horizontal than vertical and significant
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    // Swiped left - could trigger an action
                    card.style.transform = 'translateX(-10px)';
                    setTimeout(() => {
                        card.style.transform = '';
                    }, 200);
                } else {
                    // Swiped right - could trigger another action
                    card.style.transform = 'translateX(10px)';
                    setTimeout(() => {
                        card.style.transform = '';
                    }, 200);
                }
            }
        });
    });
}

/**
 * Initialize quick actions
 */
function initializeQuickActions() {
    // Enhance existing quick action buttons (keyboard shortcuts initialized separately)
    enhanceQuickActionButtons();

    // Add search functionality
    addQuickSearch();
}

/**
 * Setup keyboard shortcuts
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Only trigger if Alt key is pressed and not in an input field
        if (e.altKey && !['INPUT', 'TEXTAREA', 'SELECT'].includes(e.target.tagName)) {
            switch(e.key.toLowerCase()) {
                case 'd':
                    e.preventDefault();
                    window.location.href = (window.BASE_URL || '/icgc/') + 'dashboard';
                    break;
                case 'm':
                    e.preventDefault();
                    window.location.href = (window.BASE_URL || '/icgc/') + 'members';
                    break;
                case 'a':
                    e.preventDefault();
                    window.location.href = (window.BASE_URL || '/icgc/') + 'attendance';
                    break;
                case 'f':
                    e.preventDefault();
                    window.location.href = (window.BASE_URL || '/icgc/') + 'finances';
                    break;
                case 's':
                    e.preventDefault();
                    const searchInput = document.querySelector('#quick-search');
                    if (searchInput) {
                        searchInput.focus();
                    }
                    break;
                case 'n':
                    e.preventDefault();
                    // Context-sensitive new action
                    if (window.location.pathname.includes('members')) {
                        window.location.href = '/icgc/members/add';
                    } else {
                        window.location.href = '/icgc/members/add';
                    }
                    break;
            }
        }
    });
}

// Removed redundant function - using setupKeyboardShortcuts directly

/**
 * Enhance quick action buttons
 */
function enhanceQuickActionButtons() {
    const quickActionButtons = document.querySelectorAll('.quick-action-btn');
    quickActionButtons.forEach(button => {
        // Add loading state
        button.addEventListener('click', function() {
            const icon = this.querySelector('i');
            const originalClass = icon.className;
            icon.className = 'fas fa-spinner fa-spin';

            setTimeout(() => {
                icon.className = originalClass;
            }, 1000);
        });

        // Add hover effects
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

/**
 * Add quick search functionality
 */
function addQuickSearch() {
    // Create search overlay if it doesn't exist
    if (!document.getElementById('quick-search-overlay')) {
        const searchOverlay = document.createElement('div');
        searchOverlay.id = 'quick-search-overlay';
        searchOverlay.className = 'hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center pt-20';
        searchOverlay.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
                <div class="p-4">
                    <div class="relative">
                        <input type="text" id="quick-search" placeholder="Search members, finances, attendance..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <div id="search-results" class="mt-4 max-h-60 overflow-y-auto">
                        <p class="text-gray-500 text-sm text-center py-4">Start typing to search...</p>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 rounded-b-lg">
                    <div class="flex justify-between text-xs text-gray-500">
                        <span>Press <kbd class="bg-gray-200 px-1 rounded">Alt+S</kbd> to search</span>
                        <span>Press <kbd class="bg-gray-200 px-1 rounded">Esc</kbd> to close</span>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(searchOverlay);

        // Add event listeners
        const searchInput = document.getElementById('quick-search');
        const searchOverlayEl = document.getElementById('quick-search-overlay');

        // Close on escape or overlay click
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !searchOverlayEl.classList.contains('hidden')) {
                closeQuickSearch();
            }
        });

        searchOverlayEl.addEventListener('click', function(e) {
            if (e.target === this) {
                closeQuickSearch();
            }
        });

        // Search functionality
        searchInput.addEventListener('input', function() {
            performQuickSearch(this.value);
        });
    }
}

/**
 * Open quick search
 */
function openQuickSearch() {
    const searchOverlay = document.getElementById('quick-search-overlay');
    const searchInput = document.getElementById('quick-search');

    if (searchOverlay && searchInput) {
        searchOverlay.classList.remove('hidden');
        searchInput.focus();
    }
}

/**
 * Close quick search
 */
function closeQuickSearch() {
    const searchOverlay = document.getElementById('quick-search-overlay');
    const searchInput = document.getElementById('quick-search');

    if (searchOverlay && searchInput) {
        searchOverlay.classList.add('hidden');
        searchInput.value = '';
        document.getElementById('search-results').innerHTML = '<p class="text-gray-500 text-sm text-center py-4">Start typing to search...</p>';
    }
}

/**
 * Perform quick search
 */
function performQuickSearch(query) {
    const resultsContainer = document.getElementById('search-results');

    if (query.length < 2) {
        resultsContainer.innerHTML = '<p class="text-gray-500 text-sm text-center py-4">Start typing to search...</p>';
        return;
    }

    // Show loading
    resultsContainer.innerHTML = '<p class="text-gray-500 text-sm text-center py-4"><i class="fas fa-spinner fa-spin mr-2"></i>Searching...</p>';

    // Simulate search (replace with actual API call)
    setTimeout(() => {
        const mockResults = [
            { type: 'member', title: 'John Doe', subtitle: 'Member - Choir Department', url: '/icgc/members/1' },
            { type: 'finance', title: 'Tithe Collection', subtitle: 'GH₵ 2,500 - Dec 15, 2024', url: '/icgc/finances/1' },
            { type: 'attendance', title: 'Sunday Service', subtitle: '150 attendees - Dec 15, 2024', url: '/icgc/attendance/1' }
        ].filter(item => item.title.toLowerCase().includes(query.toLowerCase()));

        if (mockResults.length === 0) {
            resultsContainer.innerHTML = '<p class="text-gray-500 text-sm text-center py-4">No results found</p>';
        } else {
            resultsContainer.innerHTML = mockResults.map(result => `
                <a href="${result.url}" class="block p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-primary-light flex items-center justify-center mr-3">
                            <i class="fas fa-${result.type === 'member' ? 'user' : result.type === 'finance' ? 'dollar-sign' : 'clipboard-check'} text-white text-xs"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">${result.title}</p>
                            <p class="text-sm text-gray-500">${result.subtitle}</p>
                        </div>
                    </div>
                </a>
            `).join('');
        }
    }, 500);
}

/**
 * Toggle notification center
 */
function toggleNotificationCenter() {
    const notificationCenter = document.getElementById('notification-center');
    if (!notificationCenter) {
        setupNotificationCenter();
        return;
    }

    if (notificationCenter.classList.contains('hidden')) {
        notificationCenter.classList.remove('hidden');
        // Mark notifications as read
        const badge = document.getElementById('notification-badge');
        if (badge) {
            badge.classList.add('hidden');
        }
    } else {
        notificationCenter.classList.add('hidden');
    }

    // Close when clicking outside
    document.addEventListener('click', function(e) {
        if (!notificationCenter.contains(e.target) && !e.target.closest('.fa-bell')) {
            notificationCenter.classList.add('hidden');
        }
    });
}

// Department details functions removed - charts are no longer clickable

// All department interaction functions removed - charts are display-only now
