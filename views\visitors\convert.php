<div class="container mx-auto max-w-4xl">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-1">Convert to Member</h1>
            <p class="text-sm text-gray-600">Convert <?php echo $this->visitor->first_name . ' ' . $this->visitor->last_name; ?> to a church member</p>
        </div>
        <div>
            <a href="<?php echo url('visitors/' . $this->visitor->id); ?>" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Visitor
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['form_errors'])): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
            <div class="font-bold">Please fix the following errors:</div>
            <ul class="list-disc ml-5">
                <?php foreach ($_SESSION['form_errors'] as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['form_errors']); ?>
    <?php endif; ?>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-green-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700">
                        You are about to convert this visitor to a church member. The visitor's information will be used to create a new member record.
                    </p>
                </div>
            </div>
        </div>

        <form action="<?php echo url('visitors/' . $visitor->id . '/convert'); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="visitor_id" value="<?php echo $this->visitor->id; ?>">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Personal Information -->
                <div class="md:col-span-2">
                    <h2 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b">Personal Information</h2>
                </div>
                
                <!-- First Name -->
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name <span class="text-red-500">*</span></label>
                    <input type="text" name="first_name" id="first_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required value="<?php echo isset($_SESSION['form_data']['first_name']) ? $_SESSION['form_data']['first_name'] : $this->visitor->first_name; ?>">
                </div>
                
                <!-- Last Name -->
                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name <span class="text-red-500">*</span></label>
                    <input type="text" name="last_name" id="last_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required value="<?php echo isset($_SESSION['form_data']['last_name']) ? $_SESSION['form_data']['last_name'] : $this->visitor->last_name; ?>">
                </div>
                
                <!-- Phone Number -->
                <div>
                    <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-1">Phone Number <span class="text-red-500">*</span></label>
                    <input type="tel" name="phone_number" id="phone_number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required value="<?php echo isset($_SESSION['form_data']['phone_number']) ? $_SESSION['form_data']['phone_number'] : $this->visitor->phone_number; ?>">
                </div>
                
                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input type="email" name="email" id="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" value="<?php echo isset($_SESSION['form_data']['email']) ? $_SESSION['form_data']['email'] : $this->visitor->email; ?>">
                </div>
                
                <!-- Gender -->
                <div>
                    <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">Gender <span class="text-red-500">*</span></label>
                    <select name="gender" id="gender" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required>
                        <option value="">-- Select Gender --</option>
                        <option value="male" <?php echo (isset($_SESSION['form_data']['gender']) && $_SESSION['form_data']['gender'] === 'male') ? 'selected' : ''; ?>>Male</option>
                        <option value="female" <?php echo (isset($_SESSION['form_data']['gender']) && $_SESSION['form_data']['gender'] === 'female') ? 'selected' : ''; ?>>Female</option>
                    </select>
                </div>
                
                <!-- Date of Birth -->
                <div>
                    <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                    <input type="date" name="date_of_birth" id="date_of_birth" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" value="<?php echo isset($_SESSION['form_data']['date_of_birth']) ? $_SESSION['form_data']['date_of_birth'] : ''; ?>">
                </div>
                
                <!-- Marital Status -->
                <div>
                    <label for="marital_status" class="block text-sm font-medium text-gray-700 mb-1">Marital Status</label>
                    <select name="marital_status" id="marital_status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">-- Select Status --</option>
                        <option value="single" <?php echo (isset($_SESSION['form_data']['marital_status']) && $_SESSION['form_data']['marital_status'] === 'single') ? 'selected' : ''; ?>>Single</option>
                        <option value="married" <?php echo (isset($_SESSION['form_data']['marital_status']) && $_SESSION['form_data']['marital_status'] === 'married') ? 'selected' : ''; ?>>Married</option>
                        <option value="divorced" <?php echo (isset($_SESSION['form_data']['marital_status']) && $_SESSION['form_data']['marital_status'] === 'divorced') ? 'selected' : ''; ?>>Divorced</option>
                        <option value="widowed" <?php echo (isset($_SESSION['form_data']['marital_status']) && $_SESSION['form_data']['marital_status'] === 'widowed') ? 'selected' : ''; ?>>Widowed</option>
                    </select>
                </div>
                
                <!-- Location -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                    <input type="text" name="location" id="location" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" value="<?php echo isset($_SESSION['form_data']['location']) ? $_SESSION['form_data']['location'] : $this->visitor->address; ?>">
                </div>
                
                <!-- Occupation -->
                <div>
                    <label for="occupation" class="block text-sm font-medium text-gray-700 mb-1">Occupation</label>
                    <input type="text" name="occupation" id="occupation" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" value="<?php echo isset($_SESSION['form_data']['occupation']) ? $_SESSION['form_data']['occupation'] : ''; ?>">
                </div>
                
                <!-- Emergency Contact Name -->
                <div>
                    <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Name</label>
                    <input type="text" name="emergency_contact_name" id="emergency_contact_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" value="<?php echo isset($_SESSION['form_data']['emergency_contact_name']) ? $_SESSION['form_data']['emergency_contact_name'] : ''; ?>">
                </div>
                
                <!-- Emergency Contact Phone -->
                <div>
                    <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Phone</label>
                    <input type="tel" name="emergency_contact_phone" id="emergency_contact_phone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" value="<?php echo isset($_SESSION['form_data']['emergency_contact_phone']) ? $_SESSION['form_data']['emergency_contact_phone'] : ''; ?>">
                </div>
                
                <!-- Church Information -->
                <div class="md:col-span-2">
                    <h2 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b mt-4">Church Information</h2>
                </div>
                
                <!-- Baptism Status -->
                <div>
                    <label for="baptism_status" class="block text-sm font-medium text-gray-700 mb-1">Baptism Status <span class="text-red-500">*</span></label>
                    <select name="baptism_status" id="baptism_status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required>
                        <option value="">-- Select Status --</option>
                        <option value="not_baptized" <?php echo (isset($_SESSION['form_data']['baptism_status']) && $_SESSION['form_data']['baptism_status'] === 'not_baptized') ? 'selected' : ''; ?>>Not Baptized</option>
                        <option value="baptized" <?php echo (isset($_SESSION['form_data']['baptism_status']) && $_SESSION['form_data']['baptism_status'] === 'baptized') ? 'selected' : ''; ?>>Baptized</option>
                        <option value="scheduled" <?php echo (isset($_SESSION['form_data']['baptism_status']) && $_SESSION['form_data']['baptism_status'] === 'scheduled') ? 'selected' : ''; ?>>Scheduled</option>
                    </select>
                </div>
                
                <!-- Department -->
                <div>
                    <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department <span class="text-red-500">*</span></label>
                    <select name="department" id="department" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required>
                        <option value="">-- Select Department --</option>
                        <?php foreach ($departments as $dept): ?>
                            <option value="<?php echo htmlspecialchars($dept['name']); ?>" <?php echo (isset($_SESSION['form_data']['department']) && $_SESSION['form_data']['department'] === $dept['name']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($dept['display_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Role -->
                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role <span class="text-red-500">*</span></label>
                    <select name="role" id="role" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required>
                        <option value="">-- Select Role --</option>
                        <?php foreach ($roles as $role): ?>
                            <option value="<?php echo htmlspecialchars($role['name']); ?>" <?php echo (isset($_SESSION['form_data']['role']) && $_SESSION['form_data']['role'] === $role['name']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($role['display_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Membership Date -->
                <div>
                    <label for="membership_date" class="block text-sm font-medium text-gray-700 mb-1">Membership Date <span class="text-red-500">*</span></label>
                    <input type="date" name="membership_date" id="membership_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary" required value="<?php echo isset($_SESSION['form_data']['membership_date']) ? $_SESSION['form_data']['membership_date'] : date('Y-m-d'); ?>">
                </div>
                
                <!-- Conversion Notes -->
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Conversion Notes</label>
                    <textarea name="notes" id="notes" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"><?php echo isset($_SESSION['form_data']['notes']) ? $_SESSION['form_data']['notes'] : ''; ?></textarea>
                </div>
            </div>
            
            <div class="mt-6 flex justify-end">
                <button type="submit" class="bg-green-500 hover:bg-green-600 text-white py-2 px-6 rounded-md">
                    <i class="fas fa-user-check mr-2"></i> Convert to Member
                </button>
            </div>
        </form>
    </div>
</div>

<?php
// Clear form data after displaying
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>
