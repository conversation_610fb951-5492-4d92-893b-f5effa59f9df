<?php
/**
 * Shared Header Component for Programs Pages
 * Reduces code duplication across all program views
 */

// Default values
$header_title = $header_title ?? 'Programs';
$header_subtitle = $header_subtitle ?? 'Church Program & Activities Planner';
$header_icon = $header_icon ?? 'fas fa-calendar-alt';
$header_width = $header_width ?? 'container mx-auto';
$show_navigation = $show_navigation ?? true;
$navigation_buttons = $navigation_buttons ?? [];

// Default navigation buttons
$default_buttons = [
    'back' => [
        'url' => BASE_URL . 'programs',
        'text' => 'Back to Dashboard',
        'icon' => 'fas fa-arrow-left',
        'style' => 'bg-gray-100 hover:bg-gray-200 text-gray-700'
    ],
    'create' => [
        'url' => BASE_URL . 'programs/create',
        'text' => 'New Program',
        'icon' => 'fas fa-plus',
        'style' => 'bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white'
    ],
    'list' => [
        'url' => BASE_URL . 'programs/list',
        'text' => 'All Programs',
        'icon' => 'fas fa-list',
        'style' => 'bg-gradient-to-r from-secondary to-secondary-dark hover:from-secondary-dark hover:to-secondary text-gray-800'
    ],
    'dashboard' => [
        'url' => BASE_URL . 'programs/dashboard',
        'text' => 'Dashboard',
        'icon' => 'fas fa-chart-pie',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ],
    'timeline' => [
        'url' => BASE_URL . 'programs/timeline',
        'text' => 'Timeline View',
        'icon' => 'fas fa-timeline',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ],
    'calendar' => [
        'url' => BASE_URL . 'programs/calendar',
        'text' => 'Calendar',
        'icon' => 'fas fa-calendar-alt',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ]
];

// Merge custom navigation with defaults
$navigation_buttons = array_merge($default_buttons, $navigation_buttons);
?>

<div class="<?php echo $header_width; ?> fade-in">
    <!-- Enhanced Header Section -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8 border border-gray-100">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-8 text-white relative overflow-hidden">
            <div class="absolute right-0 top-0 opacity-10">
                <i class="<?php echo $header_icon; ?> text-9xl transform -rotate-12 translate-x-8 -translate-y-8"></i>
            </div>
            <div class="absolute left-0 bottom-0 opacity-5">
                <i class="fas fa-church text-8xl transform rotate-12 -translate-x-4 translate-y-4"></i>
            </div>
            <div class="relative z-10">
                <?php if (isset($program) && !empty($program['title'])): ?>
                    <!-- Program-specific header -->
                    <div class="flex items-center space-x-4 mb-4">
                        <?php if (!empty($program['color_code'])): ?>
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center" style="background-color: <?php echo $program['color_code']; ?>20;">
                                <i class="<?php echo $program['icon'] ?? 'fas fa-calendar'; ?> text-2xl" style="color: <?php echo $program['color_code']; ?>"></i>
                            </div>
                        <?php else: ?>
                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                <i class="<?php echo $header_icon; ?> text-2xl"></i>
                            </div>
                        <?php endif; ?>
                        <div>
                            <h1 class="text-4xl font-bold"><?php echo htmlspecialchars($program['title']); ?></h1>
                            <p class="text-xl opacity-90"><?php echo $header_subtitle; ?></p>
                        </div>
                    </div>
                    
                    <!-- Status Badge for program pages -->
                    <?php if (isset($program['status'])): ?>
                        <?php
                        $status_styles = [
                            'planned' => 'bg-blue-100 text-blue-800 border-blue-200',
                            'in_progress' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
                            'completed' => 'bg-green-100 text-green-800 border-green-200',
                            'cancelled' => 'bg-red-100 text-red-800 border-red-200',
                            'postponed' => 'bg-gray-100 text-gray-800 border-gray-200'
                        ];
                        $status_style = $status_styles[$program['status']] ?? 'bg-gray-100 text-gray-800 border-gray-200';
                        ?>
                        <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border <?php echo $status_style; ?>">
                            <?php echo ucfirst(str_replace('_', ' ', $program['status'])); ?>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <!-- Standard header -->
                    <h1 class="text-4xl font-bold mb-3"><?php echo $header_title; ?></h1>
                    <p class="text-xl opacity-90 max-w-3xl"><?php echo $header_subtitle; ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <?php if ($show_navigation && !empty($navigation_buttons)): ?>
            <div class="p-6 bg-white">
                <div class="flex flex-wrap gap-3 justify-center md:justify-start">
                    <?php foreach ($navigation_buttons as $key => $button): ?>
                        <?php if (isset($button['show']) && !$button['show']) continue; ?>
                        <a href="<?php echo $button['url']; ?>"
                           class="inline-flex items-center px-6 py-3 <?php echo $button['style']; ?> text-sm font-medium rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md">
                            <i class="<?php echo $button['icon']; ?> mr-2"></i> <?php echo $button['text']; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
