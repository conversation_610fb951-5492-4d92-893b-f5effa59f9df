<div class="container mx-auto px-4 py-6 max-w-4xl">
    <!-- Header with Icon -->
    <div class="flex justify-between items-center mb-8">
        <div class="flex items-center">
            <div class="bg-gradient-to-r from-primary to-primary-light p-3 rounded-full shadow-lg mr-4">
                <i class="fas fa-tools text-white text-2xl"></i>
            </div>
            <div>
                <h1 class="text-2xl font-bold text-gray-800 mb-1">Add Maintenance Record</h1>
                <p class="text-gray-600">Record maintenance activity for equipment</p>
            </div>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>equipment/maintenance" class="bg-white border border-gray-300 hover:bg-gray-100 text-gray-700 py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                <i class="fas fa-arrow-left mr-2 text-primary"></i> Back to Maintenance
            </a>
            <?php if ($selected_equipment_id) : ?>
                <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $selected_equipment_id; ?>" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                    <i class="fas fa-eye mr-2"></i> View Equipment
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-50 border-l-4 border-red-500 text-red-700 p-5 mb-8 rounded-r-md shadow-sm" role="alert">
            <div class="flex items-center mb-2">
                <i class="fas fa-exclamation-circle text-red-500 mr-2 text-xl"></i>
                <p class="font-bold text-lg">Please fix the following errors:</p>
            </div>
            <ul class="list-disc ml-8 mt-2 space-y-1">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Maintenance Form -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 transition-all duration-300 hover:shadow-xl overflow-hidden">
        <div class="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-100">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-wrench text-primary mr-2"></i> Maintenance Details
            </h2>
        </div>
        <form action="<?php echo BASE_URL; ?>equipment/maintenance/store" method="POST" class="p-6">
            <?php if ($selected_equipment_id) : ?>
                <input type="hidden" name="redirect_to_equipment" value="1">
            <?php endif; ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Equipment -->
                <div class="form-field-container" data-tooltip="Select the equipment that was maintained">
                    <div class="form-field-header">
                        <label for="equipment_id" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-blue-100 text-blue-600">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <span>Equipment</span> <span class="text-red-500 ml-1">*</span>
                        </label>
                    </div>
                    <div class="relative">
                        <select id="equipment_id" name="equipment_id" class="form-select" required>
                            <option value="">Select Equipment</option>
                            <?php foreach ($equipments as $equipment) : ?>
                                <option value="<?php echo $equipment['id']; ?>" <?php echo ($selected_equipment_id == $equipment['id']) ? 'selected' : ''; ?>>
                                    <?php echo $equipment['name']; ?> (<?php echo ucfirst($equipment['category']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-500">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                </div>

                <!-- Maintenance Date -->
                <div class="form-field-container" data-tooltip="When was the maintenance performed?">
                    <div class="form-field-header">
                        <label for="maintenance_date" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-green-100 text-green-600">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <span>Maintenance Date</span> <span class="text-red-500 ml-1">*</span>
                        </label>
                    </div>
                    <input type="date" id="maintenance_date" name="maintenance_date" value="<?php echo isset($_SESSION['form_data']['maintenance_date']) ? $_SESSION['form_data']['maintenance_date'] : date('Y-m-d'); ?>" class="form-input" required>
                </div>

                <!-- Cost -->
                <div class="form-field-container" data-tooltip="How much did the maintenance cost?">
                    <div class="form-field-header">
                        <label for="cost" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-yellow-100 text-yellow-600">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <span>Cost (GH₵)</span>
                        </label>
                    </div>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500">GH₵</span>
                        </div>
                        <input type="number" id="cost" name="cost" value="<?php echo isset($_SESSION['form_data']['cost']) ? $_SESSION['form_data']['cost'] : ''; ?>" step="0.01" min="0" class="form-input pl-12">
                    </div>
                </div>

                <!-- Performed By -->
                <div class="form-field-container" data-tooltip="Who performed the maintenance?">
                    <div class="form-field-header">
                        <label for="performed_by" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-indigo-100 text-indigo-600">
                                <i class="fas fa-user-cog"></i>
                            </div>
                            <span>Performed By</span>
                        </label>
                    </div>
                    <input type="text" id="performed_by" name="performed_by" value="<?php echo isset($_SESSION['form_data']['performed_by']) ? $_SESSION['form_data']['performed_by'] : ''; ?>" class="form-input" placeholder="e.g. John Doe, ABC Repairs">
                </div>

                <!-- Description -->
                <div class="md:col-span-2 form-field-container" data-tooltip="Describe the maintenance work performed">
                    <div class="form-field-header">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="icon-container bg-gray-100 text-gray-600">
                                <i class="fas fa-align-left"></i>
                            </div>
                            <span>Description</span> <span class="text-red-500 ml-1">*</span>
                        </label>
                    </div>
                    <textarea id="description" name="description" rows="3" class="form-textarea" placeholder="Enter details about the maintenance work performed..." required><?php echo isset($_SESSION['form_data']['description']) ? $_SESSION['form_data']['description'] : ''; ?></textarea>
                </div>
            </div>

            <div class="mt-8 flex justify-end space-x-3">
                <?php if ($selected_equipment_id) : ?>
                    <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $selected_equipment_id; ?>" class="bg-white border border-gray-300 hover:bg-gray-100 text-gray-700 py-2.5 px-6 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                <?php else : ?>
                    <a href="<?php echo BASE_URL; ?>equipment/maintenance" class="bg-white border border-gray-300 hover:bg-gray-100 text-gray-700 py-2.5 px-6 rounded-lg flex items-center transition-all duration-300 shadow-sm hover:shadow">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                <?php endif; ?>
                <button type="submit" class="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-white py-2.5 px-8 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 flex items-center">
                    <i class="fas fa-save mr-2"></i> Save Maintenance Record
                </button>
            </div>
        </form>
    </div>
</div>

<style>
    /* Form styling */
    .form-field-container {
        position: relative;
        margin-bottom: 0.5rem;
    }

    .form-field-header {
        margin-bottom: 0.5rem;
    }

    .icon-container {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    .form-input, .form-select, .form-textarea {
        width: 100%;
        padding: 0.625rem 0.75rem;
        border-width: 1px;
        border-color: #E5E7EB;
        border-radius: 0.375rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
        border-color: #4CBF26;
        box-shadow: 0 0 0 3px rgba(76, 191, 38, 0.2);
        outline: none;
    }

    .form-input:hover, .form-select:hover, .form-textarea:hover {
        border-color: #4CBF26;
    }

    /* Tooltip for form fields */
    .form-field-container::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        padding: 5px 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s ease;
        white-space: nowrap;
        z-index: 20;
    }

    .form-field-container:hover::after {
        opacity: 0.9;
        bottom: calc(100% + 5px);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add focus effects to form fields
        const formInputs = document.querySelectorAll('.form-input, .form-select, .form-textarea');

        formInputs.forEach(input => {
            input.addEventListener('focus', function() {
                const container = this.closest('.form-field-container');
                if (container) {
                    container.classList.add('focused');
                }
            });

            input.addEventListener('blur', function() {
                const container = this.closest('.form-field-container');
                if (container) {
                    container.classList.remove('focused');
                }
            });
        });

        // Highlight fields sequentially on page load
        const formFieldContainers = document.querySelectorAll('.form-field-container');
        formFieldContainers.forEach((container, index) => {
            setTimeout(() => {
                container.classList.add('highlight');
                setTimeout(() => {
                    container.classList.remove('highlight');
                }, 600);
            }, index * 150);
        });
    });
</script>