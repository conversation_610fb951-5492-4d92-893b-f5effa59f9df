<?php
/**
 * Visitor Controller
 *
 * This controller handles all visitor-related operations
 */

require_once 'models/Visitor.php';
require_once 'models/VisitorFollowUp.php';
require_once 'models/VisitorConversion.php';
require_once 'helpers/functions.php';
require_once 'models/Member.php';
require_once 'models/Setting.php';
require_once 'models/Sms.php';
require_once 'utils/validation.php';
require_once 'utils/sms_helper.php';

class VisitorController {
    private $database;
    private $visitor;
    private $followUp;
    private $conversion;
    private $member;
    private $sms;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->visitor = new Visitor($this->database->getConnection());
        $this->followUp = new VisitorFollowUp($this->database->getConnection());
        $this->conversion = new VisitorConversion($this->database->getConnection());
        $this->member = new Member($this->database->getConnection());
        $this->sms = new Sms($this->database->getConnection());
    }

    /**
     * Display visitors list with pagination and search
     *
     * @return void
     */
    public function index() {
        try {
            // Get pagination and filter parameters
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 2; // Visitors per page (temporarily set to 2 for testing pagination)
            $search = trim($_GET['search'] ?? '');
            $status = isset($_GET['status']) ? sanitize($_GET['status']) : null;

            // Calculate offset
            $offset = ($page - 1) * $limit;

            // Get visitors with pagination
            $visitors_data = $this->visitor->getWithPagination($limit, $offset, $search, $status);
            $visitors = $visitors_data['visitors'];
            $total_visitors = $visitors_data['total'];
            $total_pages = ceil($total_visitors / $limit);

            // Get visitor statistics
            $stats = $this->visitor->getStatistics();

            // Get upcoming follow-ups
            $stmt = $this->followUp->getUpcoming();
            $upcoming_followups = $stmt->fetchAll();

            // Set page title and active page
            $page_title = getPageTitle('Visitors');
            $active_page = 'visitors';

            // Pagination data
            $pagination = [
                'current_page' => $page,
                'total_pages' => $total_pages,
                'total_visitors' => $total_visitors,
                'limit' => $limit,
                'has_prev' => $page > 1,
                'has_next' => $page < $total_pages,
                'prev_page' => $page - 1,
                'next_page' => $page + 1
            ];

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/visitors/index.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("Visitors index error: " . $e->getMessage());
            set_flash_message('Unable to load visitors. Please try again.', 'danger');
            redirect('dashboard');
        }
    }

    /**
     * Display visitor details
     *
     * @param int|null $id Visitor ID (from URL parameter or $_GET)
     * @return void
     */
    public function show($id = null) {
        // Get ID from parameter or fallback to $_GET for backward compatibility
        if ($id === null) {
            $id = $_GET['id'] ?? null;
        }

        // Check if ID is set
        if (!$id || empty($id)) {
            set_flash_message('Visitor ID is required.', 'danger');
            redirect('visitors');
        }

        // Sanitize the ID
        $id = sanitize($id);
        if (!$this->visitor->getById($id)) {
            set_flash_message('Visitor not found.', 'danger');
            redirect('visitors');
        }

        // Get follow-up history
        $stmt = $this->followUp->getByVisitorId($id);
        $followups = $stmt->fetchAll();

        // Check if visitor has been converted
        $is_converted = $this->conversion->isConverted($id);
        $conversion_details = null;

        if ($is_converted) {
            $this->conversion->getByVisitorId($id);
            $conversion_details = [
                'id' => $this->conversion->id,
                'member_id' => $this->conversion->member_id,
                'conversion_date' => $this->conversion->conversion_date,
                'notes' => $this->conversion->notes
            ];

            // Get member details
            $this->member->getById($this->conversion->member_id);
            $conversion_details['member_name'] = $this->member->first_name . ' ' . $this->member->last_name;
        }

        // Set page title and active page
        $page_title = getPageTitle('Visitor Details');
        $active_page = 'visitors';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/visitors/view.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display visitor registration form
     *
     * @return void
     */
    public function create() {
        // Set page title and active page
        $page_title = getPageTitle('Register New Visitor');
        $active_page = 'visitors';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/visitors/create.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store new visitor
     *
     * @return void
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            set_flash_message('Invalid request method.', 'danger');
            redirect('visitors/create');
        }

        // Set visitor properties (model will handle all validation)
        $this->visitor->first_name = sanitize($_POST['first_name']);
        $this->visitor->last_name = sanitize($_POST['last_name']);
        $this->visitor->email = isset($_POST['email']) ? sanitize($_POST['email']) : null;
        $this->visitor->phone_number = sanitize($_POST['phone_number']);
        $this->visitor->address = isset($_POST['address']) ? sanitize($_POST['address']) : null;
        $this->visitor->first_visit_date = sanitize($_POST['first_visit_date']);
        $this->visitor->how_did_they_hear = sanitize($_POST['how_did_they_hear']);
        $this->visitor->visitor_status = 'new';
        $this->visitor->notes = isset($_POST['notes']) ? sanitize($_POST['notes']) : null;

        // Create visitor
        if ($this->visitor->create()) {
            // Get visitor's full name for welcome message
            $visitor_name = $this->visitor->first_name . ' ' . $this->visitor->last_name;

            // Set welcome message
            $welcome_message = "Visitor {$visitor_name} registered successfully. Welcome to ICGC Emmanuel Temple!";
            set_flash_message($welcome_message, 'success');

            // Optionally send SMS welcome message if phone number is provided
            if (!empty($this->visitor->phone_number)) {
                // Get settings for SMS API
                $setting = new Setting($this->database->getConnection());
                $settings = $setting->getAllAsArray();
                $api_key = $settings['sms_api_key'] ?? '';
                $sender_id = $settings['sms_sender_id'] ?? 'ICGC';

                // Check if API key is configured
                if (!empty($api_key)) {
                    // Prepare welcome SMS message
                    $sms_message = "Dear {$this->visitor->first_name}, welcome to ICGC Emmanuel Temple! Thank you for visiting us. We look forward to seeing you again soon. God bless you.";

                    // Send SMS
                    $result = send_single_sms($this->visitor->phone_number, $sms_message, $sender_id, $api_key);

                    // If primary method failed with 405 error, try alternative endpoint
                    if (!$result['status'] && isset($result['details']['http_code']) && $result['details']['http_code'] == 405) {
                        $alt_result = try_alternative_endpoint($this->visitor->phone_number, $sms_message, $sender_id, $api_key);

                        // Use alternative result if it was successful
                        if ($alt_result['status']) {
                            $result = $alt_result;
                        }
                    }

                    // Record SMS in the database
                    $this->sms->message = $sms_message;
                    $this->sms->recipients = $this->visitor->id; // Store visitor ID as recipient
                    $this->sms->sent_date = date('Y-m-d H:i:s');
                    $this->sms->status = $result['status'] ? 'sent' : 'failed';
                    $this->sms->sent_by = $_SESSION['user_id'] ?? null;
                    $this->sms->created_at = date('Y-m-d H:i:s');
                    $this->sms->updated_at = date('Y-m-d H:i:s');

                    // Create SMS record
                    $this->sms->create();

                    // If SMS was sent successfully, create a follow-up record
                    if ($result['status']) {
                        // Set follow-up properties
                        $this->followUp->visitor_id = $this->visitor->id;
                        $this->followUp->follow_up_date = date('Y-m-d');
                        $this->followUp->follow_up_type = 'sms';
                        $this->followUp->notes = "Welcome SMS sent: {$sms_message}";
                        $this->followUp->conducted_by = $_SESSION['user_id'] ?? null;

                        // Create follow-up
                        $this->followUp->create();
                    }
                }
            }

            redirect('visitors');
        } else {
            // Get detailed error message from model
            $errorMessage = $this->visitor->error ?? 'Failed to register visitor.';
            set_flash_message($errorMessage, 'danger');
            $_SESSION['form_data'] = $_POST;
            redirect('visitors/create');
        }
    }

    /**
     * Display visitor edit form
     *
     * @param int|null $id Visitor ID (from URL parameter or $_GET)
     * @return void
     */
    public function edit($id = null) {
        // Get ID from parameter or fallback to $_GET for backward compatibility
        if ($id === null) {
            $id = $_GET['id'] ?? null;
        }

        // Check if ID is set
        if (!$id || empty($id)) {
            set_flash_message('Visitor ID is required.', 'danger');
            redirect('visitors');
        }

        // Sanitize the ID
        $id = sanitize($id);
        if (!$this->visitor->getById($id)) {
            set_flash_message('Visitor not found.', 'danger');
            redirect('visitors');
        }

        // Set page title and active page
        $page_title = getPageTitle('Edit Visitor');
        $active_page = 'visitors';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/visitors/edit.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Update visitor
     *
     * @return void
     */
    public function update() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            set_flash_message('Invalid request method.', 'danger');
            redirect('visitors');
        }

        // Check if ID is set
        if (!isset($_POST['id']) || empty($_POST['id'])) {
            set_flash_message('Visitor ID is required.', 'danger');
            redirect('visitors');
        }

        // Get visitor by ID
        $id = sanitize($_POST['id']);
        if (!$this->visitor->getById($id)) {
            set_flash_message('Visitor not found.', 'danger');
            redirect('visitors');
        }

        // Set visitor properties (model will handle all validation)
        $this->visitor->first_name = sanitize($_POST['first_name']);
        $this->visitor->last_name = sanitize($_POST['last_name']);
        $this->visitor->email = isset($_POST['email']) ? sanitize($_POST['email']) : null;
        $this->visitor->phone_number = sanitize($_POST['phone_number']);
        $this->visitor->address = isset($_POST['address']) ? sanitize($_POST['address']) : null;
        $this->visitor->first_visit_date = sanitize($_POST['first_visit_date']);
        $this->visitor->how_did_they_hear = sanitize($_POST['how_did_they_hear']);
        $this->visitor->visitor_status = sanitize($_POST['visitor_status']);
        $this->visitor->notes = isset($_POST['notes']) ? sanitize($_POST['notes']) : null;

        // Update visitor (model will handle all validation)
        if ($this->visitor->update()) {
            set_flash_message('Visitor updated successfully.', 'success');
            redirect('visitors/view?id=' . $id);
        } else {
            // Get detailed error message from model
            $errorMessage = $this->visitor->error ?? 'Failed to update visitor.';
            set_flash_message($errorMessage, 'danger');
            $_SESSION['form_data'] = $_POST;
            redirect('visitors/edit?id=' . $id);
        }
    }

    /**
     * Delete visitor
     *
     * @param int|null $id Visitor ID (from URL parameter or $_GET)
     * @return void
     */
    public function delete($id = null) {
        // Get ID from parameter or fallback to $_GET for backward compatibility
        if ($id === null) {
            $id = $_GET['id'] ?? null;
        }

        // Check if ID is set
        if (!$id || empty($id)) {
            set_flash_message('Visitor ID is required.', 'danger');
            redirect('visitors');
        }

        // Sanitize and delete visitor
        $id = sanitize($id);
        if ($this->visitor->delete($id)) {
            set_flash_message('Visitor deleted successfully.', 'success');
        } else {
            set_flash_message('Failed to delete visitor.', 'danger');
        }

        redirect('visitors');
    }

    /**
     * Display follow-up form
     *
     * @param int|null $id Visitor ID (from URL parameter or $_GET)
     * @return void
     */
    public function followUp($id = null) {
        // Get ID from parameter or fallback to $_GET for backward compatibility
        if ($id === null) {
            $id = $_GET['id'] ?? null;
        }

        // Check if ID is set
        if (!$id || empty($id)) {
            set_flash_message('Visitor ID is required.', 'danger');
            redirect('visitors');
        }

        // Sanitize the ID
        $id = sanitize($id);
        if (!$this->visitor->getById($id)) {
            set_flash_message('Visitor not found.', 'danger');
            redirect('visitors');
        }

        // Set page title and active page
        $page_title = 'Add Follow-Up - ICGC Emmanuel Temple';
        $active_page = 'visitors';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/visitors/follow_up.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store new follow-up
     *
     * @return void
     */
    public function storeFollowUp() {
        // Validate form data
        $errors = [];

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            set_flash_message('Invalid request method.', 'danger');
            redirect('visitors');
        }

        // Required fields
        $required_fields = ['visitor_id', 'follow_up_date', 'follow_up_type'];
        foreach ($required_fields as $field) {
            if (!isset($_POST[$field]) || empty($_POST[$field])) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required.';
            }
        }

        // If there are errors, redirect back with errors
        if (!empty($errors)) {
            $_SESSION['form_errors'] = $errors;
            $_SESSION['form_data'] = $_POST;
            redirect('visitors/follow-up?id=' . $_POST['visitor_id']);
        }

        // Get visitor by ID
        $visitor_id = sanitize($_POST['visitor_id']);
        if (!$this->visitor->getById($visitor_id)) {
            set_flash_message('Visitor not found.', 'danger');
            redirect('visitors');
        }

        // Set follow-up properties
        $this->followUp->visitor_id = $visitor_id;
        $this->followUp->follow_up_date = sanitize($_POST['follow_up_date']);
        $this->followUp->follow_up_type = sanitize($_POST['follow_up_type']);
        $this->followUp->notes = isset($_POST['notes']) ? sanitize($_POST['notes']) : null;
        $this->followUp->conducted_by = $_SESSION['user_id'] ?? null;

        // Create follow-up
        if ($this->followUp->create()) {
            // Update visitor status to 'in_follow_up' if currently 'new'
            if ($this->visitor->visitor_status === 'new') {
                $this->visitor->updateStatus($visitor_id, 'in_follow_up');
            }

            set_flash_message('Follow-up added successfully.', 'success');
            redirect('visitors/view?id=' . $visitor_id);
        } else {
            set_flash_message('Failed to add follow-up.', 'danger');
            $_SESSION['form_data'] = $_POST;
            redirect('visitors/follow-up?id=' . $visitor_id);
        }
    }

    /**
     * Delete follow-up
     *
     * @param int|null $visitor_id Visitor ID (from URL parameter or $_GET)
     * @param int|null $followup_id Follow-up ID (from URL parameter or $_GET)
     * @return void
     */
    public function deleteFollowUp($visitor_id = null, $followup_id = null) {
        // Get IDs from parameters or fallback to $_GET for backward compatibility
        if ($visitor_id === null) {
            $visitor_id = $_GET['visitor_id'] ?? null;
        }
        if ($followup_id === null) {
            $followup_id = $_GET['id'] ?? null;
        }

        // Check if IDs are set
        if (!$visitor_id || empty($visitor_id) || !$followup_id || empty($followup_id)) {
            set_flash_message('Follow-up ID and Visitor ID are required.', 'danger');
            redirect('visitors');
        }

        // Sanitize the IDs
        $id = sanitize($followup_id);
        $visitor_id = sanitize($visitor_id);

        if ($this->followUp->delete($id)) {
            set_flash_message('Follow-up deleted successfully.', 'success');
        } else {
            set_flash_message('Failed to delete follow-up.', 'danger');
        }

        redirect('visitors/view?id=' . $visitor_id);
    }

    /**
     * Send SMS to visitor and record as follow-up
     *
     * @return void
     */
    public function sendSms() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            set_flash_message('Invalid request method.', 'danger');
            redirect('visitors');
            return;
        }

        // Validate required fields
        $required_fields = ['visitor_id', 'message'];
        $errors = [];

        foreach ($required_fields as $field) {
            if (!isset($_POST[$field]) || empty($_POST[$field])) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required.';
            }
        }

        // Get visitor ID
        $visitor_id = isset($_POST['visitor_id']) ? sanitize($_POST['visitor_id']) : null;

        // If there are errors, redirect back with errors
        if (!empty($errors)) {
            $_SESSION['form_errors'] = $errors;
            $_SESSION['form_data'] = $_POST;
            redirect('visitors/follow-up?id=' . $visitor_id);
            return;
        }

        // Get visitor by ID
        if (!$this->visitor->getById($visitor_id)) {
            set_flash_message('Visitor not found.', 'danger');
            redirect('visitors');
            return;
        }

        // Get message content
        $message = sanitize($_POST['message']);

        // Get settings for SMS API
        $setting = new Setting($this->database->getConnection());
        $settings = $setting->getAllAsArray();
        $api_key = $settings['sms_api_key'] ?? '';
        $sender_id = $settings['sms_sender_id'] ?? 'ICGC';

        // Check if API key is configured
        if (empty($api_key)) {
            set_flash_message('SMS API key is not configured. Please set it in the settings.', 'danger');
            redirect('visitors/follow-up?id=' . $visitor_id);
            return;
        }

        // Format phone number
        $phone_number = $this->visitor->phone_number;

        // Send SMS
        $result = send_single_sms($phone_number, $message, $sender_id, $api_key);

        // If primary method failed with 405 error, try alternative endpoint
        if (!$result['status'] && isset($result['details']['http_code']) && $result['details']['http_code'] == 405) {
            error_log("Primary SMS method failed with 405 error, trying alternative endpoint for {$phone_number}");
            $alt_result = try_alternative_endpoint($phone_number, $message, $sender_id, $api_key);

            // Use alternative result if it was successful
            if ($alt_result['status']) {
                $result = $alt_result;
                error_log("Alternative SMS endpoint succeeded for {$phone_number}");
            } else {
                error_log("Alternative SMS endpoint also failed for {$phone_number}: {$alt_result['message']}");
            }
        }

        // Record SMS in the database
        $this->sms->message = $message;
        $this->sms->recipients = $visitor_id; // Store visitor ID as recipient
        $this->sms->sent_date = date('Y-m-d H:i:s');
        $this->sms->status = $result['status'] ? 'sent' : 'failed';
        $this->sms->sent_by = $_SESSION['user_id'] ?? null;
        $this->sms->created_at = date('Y-m-d H:i:s');
        $this->sms->updated_at = date('Y-m-d H:i:s');

        // Create SMS record
        $sms_created = $this->sms->create();

        // Create follow-up record if SMS was sent successfully
        if ($result['status']) {
            // Set follow-up properties
            $this->followUp->visitor_id = $visitor_id;
            $this->followUp->follow_up_date = date('Y-m-d');
            $this->followUp->follow_up_type = 'sms';
            $this->followUp->notes = "SMS sent: {$message}";
            $this->followUp->conducted_by = $_SESSION['user_id'] ?? null;

            // Create follow-up
            $followup_created = $this->followUp->create();

            // Update visitor status to 'in_follow_up' if currently 'new'
            if ($this->visitor->visitor_status === 'new') {
                $this->visitor->updateStatus($visitor_id, 'in_follow_up');
            }

            // Set success message
            set_flash_message('SMS sent successfully and follow-up recorded.', 'success');
        } else {
            // Set error message
            set_flash_message('Failed to send SMS: ' . ($result['message'] ?? 'Unknown error'), 'danger');
        }

        // Redirect back to visitor view
        redirect('visitors/view?id=' . $visitor_id);
    }

    /**
     * Redirect to member registration with visitor data pre-populated
     *
     * @param int|null $id Visitor ID (from URL parameter or $_GET)
     * @return void
     */
    public function convert($id = null) {
        // Get ID from parameter or fallback to $_GET for backward compatibility
        if ($id === null) {
            $id = $_GET['id'] ?? null;
        }

        // Check if ID is set
        if (!$id || empty($id)) {
            set_flash_message('Visitor ID is required.', 'danger');
            redirect('visitors');
        }

        // Sanitize the ID
        $id = sanitize($id);
        if (!$this->visitor->getById($id)) {
            set_flash_message('Visitor not found.', 'danger');
            redirect('visitors');
        }

        // Check if visitor has already been converted
        if ($this->conversion->isConverted($id)) {
            set_flash_message('This visitor has already been converted to a member.', 'warning');
            redirect('visitors/' . $id);
        }

        // Store visitor data in session for pre-population
        $_SESSION['convert_visitor_data'] = [
            'visitor_id' => $this->visitor->id,
            'first_name' => $this->visitor->first_name,
            'last_name' => $this->visitor->last_name,
            'email' => $this->visitor->email,
            'phone_number' => $this->visitor->phone_number,
            'address' => $this->visitor->address,
            'first_visit_date' => $this->visitor->first_visit_date,
            'how_did_they_hear' => $this->visitor->how_did_they_hear,
            'notes' => $this->visitor->notes
        ];

        // Set a flash message to indicate this is a conversion
        set_flash_message('Converting visitor to member. Please complete the member registration form below.', 'info');

        // Redirect to the standard member registration page
        redirect('members/add?from_visitor=' . $id);
    }

    /**
     * Store new conversion
     *
     * @return void
     */
    public function storeConversion() {
        // Validate form data
        $errors = [];

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            set_flash_message('Invalid request method.', 'danger');
            redirect('visitors');
        }

        // Check if visitor ID is set
        if (!isset($_POST['visitor_id']) || empty($_POST['visitor_id'])) {
            set_flash_message('Visitor ID is required.', 'danger');
            redirect('visitors');
        }

        $visitor_id = sanitize($_POST['visitor_id']);

        // Check if visitor has already been converted
        if ($this->conversion->isConverted($visitor_id)) {
            set_flash_message('This visitor has already been converted to a member.', 'warning');
            redirect('visitors/view?id=' . $visitor_id);
        }

        // Get visitor by ID
        if (!$this->visitor->getById($visitor_id)) {
            set_flash_message('Visitor not found.', 'danger');
            redirect('visitors');
        }

        // Required fields for member
        $required_fields = [
            'first_name', 'last_name', 'phone_number', 'gender',
            'baptism_status', 'department', 'role', 'membership_date'
        ];

        foreach ($required_fields as $field) {
            if (!isset($_POST[$field]) || empty($_POST[$field])) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required.';
            }
        }

        // If there are errors, redirect back with errors
        if (!empty($errors)) {
            $_SESSION['form_errors'] = $errors;
            $_SESSION['form_data'] = $_POST;
            redirect('visitors/convert?id=' . $visitor_id);
        }

        // Create new member
        $member = new Member($this->database->getConnection());

        // Set member properties
        $member->first_name = sanitize($_POST['first_name']);
        $member->last_name = sanitize($_POST['last_name']);
        $member->email = isset($_POST['email']) ? sanitize($_POST['email']) : null;
        $member->phone_number = sanitize($_POST['phone_number']);
        $member->date_of_birth = isset($_POST['date_of_birth']) ? sanitize($_POST['date_of_birth']) : null;
        $member->gender = sanitize($_POST['gender']);
        $member->marital_status = isset($_POST['marital_status']) ? sanitize($_POST['marital_status']) : null;
        $member->location = isset($_POST['location']) ? sanitize($_POST['location']) : null;
        $member->emergency_contact_name = isset($_POST['emergency_contact_name']) ? sanitize($_POST['emergency_contact_name']) : null;
        $member->emergency_contact_phone = isset($_POST['emergency_contact_phone']) ? sanitize($_POST['emergency_contact_phone']) : null;
        $member->baptism_status = sanitize($_POST['baptism_status']);
        $member->department = sanitize($_POST['department']);
        $member->role = sanitize($_POST['role']);
        $member->membership_date = sanitize($_POST['membership_date']);
        $member->occupation = isset($_POST['occupation']) ? sanitize($_POST['occupation']) : null;
        $member->member_status = 'active';

        // Create member
        if ($member->create()) {
            // Create conversion record
            $this->conversion->visitor_id = $visitor_id;
            $this->conversion->member_id = $member->id;
            $this->conversion->conversion_date = date('Y-m-d');
            $this->conversion->notes = isset($_POST['notes']) ? sanitize($_POST['notes']) : null;

            if ($this->conversion->create()) {
                set_flash_message('Visitor successfully converted to member.', 'success');
                redirect('visitors/view?id=' . $visitor_id);
            } else {
                // If conversion record fails, delete the member
                $member->delete($member->id);
                set_flash_message('Failed to create conversion record.', 'danger');
                $_SESSION['form_data'] = $_POST;
                redirect('visitors/convert?id=' . $visitor_id);
            }
        } else {
            set_flash_message('Failed to create member.', 'danger');
            $_SESSION['form_data'] = $_POST;
            redirect('visitors/convert?id=' . $visitor_id);
        }
    }
}
