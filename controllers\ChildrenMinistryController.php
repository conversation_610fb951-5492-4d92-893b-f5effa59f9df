<?php
/**
 * Children's Ministry Controller
 * Handles all children's ministry related functionality
 */

// BASE_URL is now centrally managed in bootstrap.php - no need to define here

require_once 'config/database.php';
require_once 'models/ChildrenMinistry.php';
require_once 'models/Member.php';
require_once 'models/Service.php';
require_once 'models/GuardianContact.php';
require_once 'models/Setting.php';
require_once 'utils/validation.php';
require_once 'helpers/functions.php';
require_once 'utils/helpers.php';

class ChildrenMinistryController {
    private $database;
    private $childrenMinistry;
    private $member;
    private $service;
    private $setting;

    /**
     * Constructor
     */
    public function __construct() {
        try {
            $this->database = new Database();
            $this->childrenMinistry = new ChildrenMinistry($this->database->getConnection());
            $this->member = new Member($this->database->getConnection());
            $this->service = new Service($this->database->getConnection());
            $this->setting = new Setting($this->database->getConnection());
        } catch (Exception $e) {
            error_log("ChildrenMinistryController constructor error: " . $e->getMessage());
            throw new Exception("Failed to initialize Children's Ministry controller: " . $e->getMessage());
        }
    }

    /**
     * Children's Ministry Dashboard
     *
     * @return void
     */
    public function index() {
        try {
            // Get dashboard statistics
            $stats = $this->childrenMinistry->getDashboardStats();

            // Debug: Log the stats to see what we're getting
            error_log("Children Ministry Dashboard Stats: " . print_r($stats, true));

            // Get recent check-ins
            $recent_checkins = $this->childrenMinistry->getRecentCheckins(10)->fetchAll(PDO::FETCH_ASSOC);

            // Get currently checked-in children with enhanced QR tracking data
            $currently_checked_in = $this->getEnhancedCurrentlyCheckedIn();

            // Debug: Log the currently checked in count
            error_log("Currently Checked In Count: " . count($currently_checked_in));

            // Get real-time QR attendance statistics
            $qr_attendance_stats = $this->getQrAttendanceStats();

            // Get children's services
            $children_services = $this->childrenMinistry->getChildrenServices()->fetchAll(PDO::FETCH_ASSOC);

            // Set page variables
            $page_title = getPageTitle('Children\'s Ministry Dashboard');
            $active_page = 'children_ministry';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/children_ministry/dashboard.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Load layout
            require_once 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("Children's Ministry Dashboard Error: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            // Set error message and redirect to dashboard
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['flash_message'] = 'Unable to load Children\'s Ministry dashboard. Please try again.';
            $_SESSION['flash_type'] = 'danger';

            header('Location: ' . BASE_URL . 'dashboard');
            exit;
        }
    }

    /**
     * Real-time Children's Ministry Attendance Dashboard
     * Shows today's attendance data regardless of QR session status
     */
    public function realtimeDashboard() {
        try {
            // Get date parameter or default to today
            $date = isset($_GET['date']) ? sanitize($_GET['date']) : date('Y-m-d');

            // Validate date format
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                $date = date('Y-m-d');
            }

            // Get token from URL or session (optional - for enhanced features)
            $token = isset($_GET['token']) ? sanitize($_GET['token']) : null;
            $qr_session_info = null;

            // Try to get QR session info if token provided (but don't require it)
            if ($token) {
                require_once 'models/AttendanceQrSession.php';
                $qr_session = new AttendanceQrSession($this->database->getConnection());

                if ($qr_session->getByToken($token)) {
                    $qr_session_info = [
                        'id' => $qr_session->id,
                        'service_id' => $qr_session->service_id,
                        'attendance_date' => $qr_session->attendance_date,
                        'token' => $qr_session->token,
                        'status' => $qr_session->status,
                        'expires_at' => $qr_session->expires_at,
                        'is_active' => $qr_session->status === 'active' && strtotime($qr_session->expires_at) > time()
                    ];

                    // Use QR session date if available
                    $date = $qr_session->attendance_date;
                }
            }

            // Set page variables
            $page_title = 'Children\'s Ministry Attendance Dashboard - ICGC Emmanuel Temple';
            $active_page = 'children_ministry';

            // Pass data to view
            $dashboard_date = $date;
            $qr_session_data = $qr_session_info;

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/children_ministry/realtime-dashboard.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Include the layout template
            include 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("Children's Ministry Real-time Dashboard Error: " . $e->getMessage());
            set_flash_message('Unable to load children\'s ministry dashboard. Please try again.', 'danger');
            redirect('children-ministry');
        }
    }

    /**
     * API endpoint for real-time children's attendance data
     * Works with or without QR session token
     */
    public function getRealtimeAttendanceData() {
        header('Content-Type: application/json');

        try {
            // Get date parameter or default to today
            $date = isset($_GET['date']) ? sanitize($_GET['date']) : date('Y-m-d');

            // Validate date format
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                $date = date('Y-m-d');
            }

            // Get token from request (optional)
            $token = isset($_GET['token']) ? sanitize($_GET['token']) : null;
            $qr_session_info = null;

            // Try to get QR session info if token provided
            if ($token) {
                require_once 'models/AttendanceQrSession.php';
                $qr_session = new AttendanceQrSession($this->database->getConnection());

                if ($qr_session->getByToken($token)) {
                    $qr_session_info = [
                        'service_id' => $qr_session->service_id,
                        'attendance_date' => $qr_session->attendance_date,
                        'status' => $qr_session->status,
                        'is_active' => $qr_session->status === 'active' && strtotime($qr_session->expires_at) > time()
                    ];

                    // Use QR session date if available
                    $date = $qr_session->attendance_date;
                }
            }

            $conn = $this->database->getConnection();

            // Get children's attendance for the specified date
            $children_sql = "SELECT ccl.*, m.first_name, m.last_name, m.gender, m.date_of_birth,
                                   TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                                   s.name as service_name, s.time as service_time,
                                   parent.first_name as parent_first_name,
                                   parent.last_name as parent_last_name,
                                   CASE
                                       WHEN TIME(ccl.check_in_time) > TIME(s.time) THEN 'late'
                                       ELSE 'present'
                                   END as status,
                                   TIMESTAMPDIFF(MINUTE, ccl.check_in_time, NOW()) as minutes_checked_in
                            FROM children_checkin_log ccl
                            JOIN members m ON ccl.child_id = m.id
                            JOIN services s ON ccl.service_id = s.id
                            LEFT JOIN members parent ON ccl.checked_in_by = parent.id
                            WHERE ccl.attendance_date = ?
                            ORDER BY ccl.check_in_time DESC";

            $stmt = $conn->prepare($children_sql);
            $stmt->execute([$date]);
            $children = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate statistics
            $total_children = count($children);
            $present_count = count(array_filter($children, function($child) { return $child['status'] === 'present'; }));
            $late_count = count(array_filter($children, function($child) { return $child['status'] === 'late'; }));
            $male_count = count(array_filter($children, function($child) { return strtolower($child['gender']) === 'male'; }));
            $female_count = count(array_filter($children, function($child) { return strtolower($child['gender']) === 'female'; }));

            // Age group breakdown
            $age_groups = [
                'toddlers' => count(array_filter($children, function($child) { return $child['age'] <= 3; })),
                'preschool' => count(array_filter($children, function($child) { return $child['age'] >= 4 && $child['age'] <= 6; })),
                'elementary' => count(array_filter($children, function($child) { return $child['age'] >= 7 && $child['age'] <= 12; })),
                'teens' => count(array_filter($children, function($child) { return $child['age'] >= 13; }))
            ];

            echo json_encode([
                'success' => true,
                'date' => $date,
                'qr_session' => $qr_session_info,
                'stats' => [
                    'total_children' => $total_children,
                    'present_count' => $present_count,
                    'late_count' => $late_count,
                    'male_count' => $male_count,
                    'female_count' => $female_count,
                    'male_percentage' => $total_children > 0 ? round(($male_count / $total_children) * 100) : 0,
                    'female_percentage' => $total_children > 0 ? round(($female_count / $total_children) * 100) : 0,
                    'age_groups' => $age_groups
                ],
                'children' => $children,
                'last_updated' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            error_log("Real-time children's attendance data error: " . $e->getMessage());
            echo json_encode(['success' => false, 'error' => 'Failed to load data']);
        }
    }

    /**
     * Get real-time stats for the main dashboard
     */
    public function getRealTimeStats() {
        header('Content-Type: application/json');

        try {
            $conn = $this->database->getConnection();
            $today = date('Y-m-d');

            // Get today's checked-in children with service time for present/late calculation
            $children_sql = "SELECT ccl.*, m.first_name, m.last_name, m.gender, m.date_of_birth,
                                   TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                                   s.name as service_name, s.time as service_time,
                                   parent.first_name as parent_first_name,
                                   parent.last_name as parent_last_name,
                                   CASE
                                       WHEN TIME(ccl.check_in_time) > TIME(s.time) THEN 'late'
                                       ELSE 'present'
                                   END as status,
                                   TIMESTAMPDIFF(MINUTE, ccl.check_in_time, NOW()) as minutes_checked_in,
                                   CASE
                                       WHEN ccl.notes LIKE '%QR%' THEN 1
                                       ELSE 0
                                   END as is_qr_checkin
                            FROM children_checkin_log ccl
                            JOIN members m ON ccl.child_id = m.id
                            JOIN services s ON ccl.service_id = s.id
                            LEFT JOIN members parent ON ccl.checked_in_by = parent.id
                            WHERE ccl.attendance_date = ?
                            ORDER BY ccl.check_in_time DESC";

            $stmt = $conn->prepare($children_sql);
            $stmt->execute([$today]);
            $children = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate statistics
            $total_children = count($children);
            $present_count = count(array_filter($children, function($child) { return $child['status'] === 'present'; }));
            $late_count = count(array_filter($children, function($child) { return $child['status'] === 'late'; }));

            echo json_encode([
                'success' => true,
                'stats' => [
                    'total_children' => $total_children,
                    'present_count' => $present_count,
                    'late_count' => $late_count
                ],
                'children' => $children,
                'last_updated' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            error_log("Real-time stats error: " . $e->getMessage());
            echo json_encode(['success' => false, 'error' => 'Failed to load stats']);
        }
    }

    /**
     * Get enhanced currently checked-in data with QR tracking information
     *
     * @return array
     */
    private function getEnhancedCurrentlyCheckedIn() {
        try {
            $conn = $this->database->getConnection();

            $query = "SELECT
                        ccl.*,
                        m.first_name,
                        m.last_name,
                        m.profile_picture,
                        TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                        s.name as service_name,
                        s.time as service_time,
                        ag.name as age_group_name,
                        parent.first_name as parent_first_name,
                        parent.last_name as parent_last_name,
                        CASE
                            WHEN ccl.notes LIKE '%QR Family Attendance%' THEN 'QR Family'
                            WHEN ccl.notes LIKE '%QR family attendance%' THEN 'QR Family'
                            WHEN ccl.notes LIKE '%QR%' THEN 'QR Code'
                            ELSE 'QR System'
                        END as check_in_method,
                        CASE
                            WHEN ccl.notes LIKE '%QR%' THEN 1
                            ELSE 0
                        END as is_qr_checkin,
                        TIMESTAMPDIFF(MINUTE, ccl.check_in_time, NOW()) as minutes_checked_in
                      FROM children_checkin_log ccl
                      JOIN members m ON ccl.child_id = m.id
                      JOIN services s ON ccl.service_id = s.id
                      LEFT JOIN age_groups ag ON m.age_group_id = ag.id
                      LEFT JOIN members parent ON ccl.checked_in_by = parent.id
                      WHERE ccl.attendance_date = CURDATE()
                      ORDER BY ccl.check_in_time DESC";

            $stmt = $conn->prepare($query);
            $stmt->execute();

            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log("Children's Ministry Dashboard - Currently checked in count: " . count($result));

            return $result;

        } catch (Exception $e) {
            error_log("Error getting enhanced currently checked in: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get real-time QR attendance statistics for children's ministry
     *
     * @return array
     */
    private function getQrAttendanceStats() {
        try {
            $conn = $this->database->getConnection();

            // Today's QR check-ins (all attendance is now QR-based)
            $today_stats_query = "SELECT
                                    COUNT(*) as total_checkins,
                                    COUNT(*) as qr_checkins,
                                    0 as manual_checkins,
                                    COUNT(DISTINCT service_id) as active_services,
                                    AVG(TIMESTAMPDIFF(MINUTE, check_in_time, NOW())) as avg_duration_minutes
                                  FROM children_checkin_log
                                  WHERE attendance_date = CURDATE()";

            $stmt = $conn->prepare($today_stats_query);
            $stmt->execute();
            $today_stats = $stmt->fetch(PDO::FETCH_ASSOC);

            // Hourly check-in distribution for today
            $hourly_query = "SELECT
                               HOUR(check_in_time) as hour,
                               COUNT(*) as checkins,
                               SUM(CASE WHEN notes LIKE '%QR%' THEN 1 ELSE 0 END) as qr_checkins
                             FROM children_checkin_log
                             WHERE attendance_date = CURDATE()
                             GROUP BY HOUR(check_in_time)
                             ORDER BY hour";

            $stmt = $conn->prepare($hourly_query);
            $stmt->execute();
            $hourly_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Recent QR family check-ins (last 10)
            $recent_qr_query = "SELECT
                                  ccl.*,
                                  m.first_name,
                                  m.last_name,
                                  parent.first_name as parent_first_name,
                                  parent.last_name as parent_last_name,
                                  s.name as service_name
                                FROM children_checkin_log ccl
                                JOIN members m ON ccl.child_id = m.id
                                JOIN members parent ON ccl.checked_in_by = parent.id
                                JOIN services s ON ccl.service_id = s.id
                                WHERE ccl.notes LIKE '%QR%'
                                AND ccl.attendance_date = CURDATE()
                                ORDER BY ccl.check_in_time DESC
                                LIMIT 10";

            $stmt = $conn->prepare($recent_qr_query);
            $stmt->execute();
            $recent_qr_checkins = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'today_stats' => $today_stats,
                'hourly_distribution' => $hourly_distribution,
                'recent_qr_checkins' => $recent_qr_checkins,
                'qr_percentage' => $today_stats['total_checkins'] > 0 ?
                    round(($today_stats['qr_checkins'] / $today_stats['total_checkins']) * 100, 1) : 0
            ];

        } catch (Exception $e) {
            error_log("Error getting QR attendance stats: " . $e->getMessage());
            return [
                'today_stats' => ['total_checkins' => 0, 'qr_checkins' => 0, 'manual_checkins' => 0],
                'hourly_distribution' => [],
                'recent_qr_checkins' => [],
                'qr_percentage' => 100
            ];
        }
    }

    /**
     * Get real-time attendance statistics (AJAX endpoint)
     *
     * @return void
     */
    public function realTimeStats() {
        header('Content-Type: application/json');

        try {
            // Get current statistics
            $stats = $this->childrenMinistry->getDashboardStats();
            $qr_stats = $this->getQrAttendanceStats();
            $currently_checked_in = $this->getEnhancedCurrentlyCheckedIn();

            // Calculate present and late counts
            $present_count = 0;
            $late_count = 0;
            foreach ($currently_checked_in as $child) {
                if ($child['status'] === 'present') {
                    $present_count++;
                } else if ($child['status'] === 'late') {
                    $late_count++;
                }
            }

            echo json_encode([
                'success' => true,
                'stats' => [
                    'total_children' => count($currently_checked_in),
                    'present_count' => $present_count,
                    'late_count' => $late_count
                ],
                'children' => $currently_checked_in,
                'timestamp' => time()
            ]);

        } catch (Exception $e) {
            error_log("Error getting real-time stats: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'error' => 'Failed to get real-time statistics'
            ]);
        }
    }

    /**
     * Children's Ministry QR Analytics Dashboard - REWRITTEN
     *
     * @return void
     */
    public function qrAnalytics() {
        // Ensure session is started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // BASE_URL is centrally managed

        // Simple sanitize function if not available
        if (!function_exists('sanitize')) {
            function sanitize($data) {
                return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
            }
        }

        // Get period parameters
        $period_type = isset($_GET['period']) ? sanitize($_GET['period']) : 'monthly';
        $period_value = isset($_GET['value']) ? sanitize($_GET['value']) : date('Y-m');

        // Validate period parameters
        $valid_periods = ['daily', 'weekly', 'monthly', 'yearly'];
        if (!in_array($period_type, $valid_periods)) {
            $period_type = 'monthly';
        }

        // Get analytics data with fallback
        $analytics_data = $this->getSimpleQrAnalytics($period_type, $period_value);

        // Set page variables
        $page_title = 'Children\'s Ministry QR Analytics - ICGC Emmanuel Temple';
        $active_page = 'children_ministry';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/children_ministry/qr_analytics.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Load layout
        require_once 'views/layouts/main.php';
    }

    /**
     * Get simple QR analytics data for children's ministry
     *
     * @param string $period_type
     * @param string $period_value
     * @return array
     */
    private function getSimpleQrAnalytics($period_type, $period_value) {
        try {
            $conn = $this->database->getConnection();

            // Calculate simple date range
            $date_range = $this->getSimpleDateRange($period_type, $period_value);

            // 1. Basic Overview Statistics
            $overview_query = "SELECT
                                COUNT(*) as total_checkins,
                                SUM(CASE WHEN notes LIKE '%QR%' THEN 1 ELSE 0 END) as qr_checkins,
                                COUNT(DISTINCT child_id) as unique_children
                              FROM children_checkin_log
                              WHERE attendance_date BETWEEN ? AND ?";

            $stmt = $conn->prepare($overview_query);
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $overview = $stmt->fetch(PDO::FETCH_ASSOC);

            // Calculate manual checkins and percentage
            $overview['manual_checkins'] = $overview['total_checkins'] - $overview['qr_checkins'];
            $overview['qr_percentage'] = $overview['total_checkins'] > 0 ?
                round(($overview['qr_checkins'] / $overview['total_checkins']) * 100, 1) : 0;

            // 2. Simple Daily Trends (last 7 days for monthly, or period range)
            $trends_query = "SELECT
                               DATE(attendance_date) as date,
                               COUNT(*) as total_checkins,
                               SUM(CASE WHEN notes LIKE '%QR%' THEN 1 ELSE 0 END) as qr_checkins
                             FROM children_checkin_log
                             WHERE attendance_date BETWEEN ? AND ?
                             GROUP BY DATE(attendance_date)
                             ORDER BY date";

            $stmt = $conn->prepare($trends_query);
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $trends = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 3. Simple Hourly Distribution
            $hourly_query = "SELECT
                               HOUR(check_in_time) as hour,
                               COUNT(*) as checkins,
                               SUM(CASE WHEN notes LIKE '%QR%' THEN 1 ELSE 0 END) as qr_checkins
                             FROM children_checkin_log
                             WHERE attendance_date BETWEEN ? AND ?
                             GROUP BY HOUR(check_in_time)
                             ORDER BY hour";

            $stmt = $conn->prepare($hourly_query);
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $hourly_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 4. Simple Service Performance
            $service_query = "SELECT
                                s.name as service_name,
                                COUNT(*) as checkins,
                                SUM(CASE WHEN ccl.notes LIKE '%QR%' THEN 1 ELSE 0 END) as qr_checkins,
                                COUNT(DISTINCT ccl.child_id) as unique_children
                              FROM children_checkin_log ccl
                              LEFT JOIN services s ON ccl.service_id = s.id
                              WHERE ccl.attendance_date BETWEEN ? AND ?
                              GROUP BY s.id, s.name
                              ORDER BY checkins DESC";

            $stmt = $conn->prepare($service_query);
            $stmt->execute([$date_range['start'], $date_range['end']]);
            $service_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Add QR percentage to service performance
            foreach ($service_performance as &$service) {
                $service['qr_percentage'] = $service['checkins'] > 0 ?
                    round(($service['qr_checkins'] / $service['checkins']) * 100, 1) : 0;
            }

            return [
                'overview' => $overview,
                'trends' => $trends,
                'hourly_distribution' => $hourly_distribution,
                'age_group_analysis' => [], // Simplified - remove for now
                'service_performance' => $service_performance,
                'period_info' => [
                    'type' => $period_type,
                    'value' => $period_value,
                    'start_date' => $date_range['start'],
                    'end_date' => $date_range['end'],
                    'display_name' => $this->getSimplePeriodName($period_type, $period_value)
                ]
            ];

        } catch (Exception $e) {
            error_log("Error getting children QR analytics data: " . $e->getMessage());
            return [
                'overview' => ['total_checkins' => 0, 'qr_checkins' => 0, 'manual_checkins' => 0, 'unique_children' => 0, 'qr_percentage' => 0],
                'trends' => [],
                'hourly_distribution' => [],
                'age_group_analysis' => [],
                'service_performance' => [],
                'period_info' => [
                    'type' => $period_type,
                    'value' => $period_value,
                    'display_name' => 'Current Period'
                ]
            ];
        }
    }

    /**
     * Get simple date range
     *
     * @param string $period_type
     * @param string $period_value
     * @return array
     */
    private function getSimpleDateRange($period_type, $period_value) {
        switch ($period_type) {
            case 'daily':
                return [
                    'start' => $period_value,
                    'end' => $period_value
                ];

            case 'weekly':
                $date = new DateTime($period_value);
                $start = clone $date;
                $start->modify('monday this week');
                $end = clone $date;
                $end->modify('sunday this week');
                return [
                    'start' => $start->format('Y-m-d'),
                    'end' => $end->format('Y-m-d')
                ];

            case 'monthly':
                $date = new DateTime($period_value . '-01');
                return [
                    'start' => $date->format('Y-m-01'),
                    'end' => $date->format('Y-m-t')
                ];

            case 'yearly':
                return [
                    'start' => $period_value . '-01-01',
                    'end' => $period_value . '-12-31'
                ];

            default:
                // Default to current month
                return [
                    'start' => date('Y-m-01'),
                    'end' => date('Y-m-t')
                ];
        }
    }

    /**
     * Get simple period display name
     *
     * @param string $period_type
     * @param string $period_value
     * @return string
     */
    private function getSimplePeriodName($period_type, $period_value) {
        switch ($period_type) {
            case 'daily':
                return date('F j, Y', strtotime($period_value));

            case 'weekly':
                $date = new DateTime($period_value);
                $start = clone $date;
                $start->modify('monday this week');
                $end = clone $date;
                $end->modify('sunday this week');
                return $start->format('M j') . ' - ' . $end->format('M j, Y');

            case 'monthly':
                return date('F Y', strtotime($period_value . '-01'));

            case 'yearly':
                return $period_value;

            default:
                return 'Current Period';
        }
    }



    /**
     * Children's Ministry Attendance List
     *
     * @return void
     */
    public function attendanceList() {
        // Simple sanitize function if not available
        if (!function_exists('sanitize')) {
            function sanitize($data) {
                return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
            }
        }

        // Get filter parameters
        $selected_date = isset($_GET['date']) ? sanitize($_GET['date']) : date('Y-m-d');
        $selected_service_id = isset($_GET['service_id']) ? (int)$_GET['service_id'] : null;
        $selected_status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
        $per_page = isset($_GET['per_page']) ? $_GET['per_page'] : 25;
        $current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;



        // Get services for filter dropdown
        $services = $this->childrenMinistry->getChildrenServices()->fetchAll(PDO::FETCH_ASSOC);

        // Get attendance data (only children who actually attended)
        $all_attendance_list = $this->getDetailedAttendanceList($selected_date, $selected_service_id, $selected_status);

        // Calculate attendance statistics (only for children who attended)
        $attendance_stats = $this->calculateAttendanceOnlyStats($selected_date, $selected_service_id, $all_attendance_list);

        // Apply simple pagination
        $total_count = count($all_attendance_list);

        if ($per_page === 'all') {
            $attendance_list = $all_attendance_list;
            $pagination_info = [
                'total' => $total_count,
                'current_page' => 1,
                'total_pages' => 1,
                'per_page' => $total_count,
                'start' => 1,
                'end' => $total_count,
                'base_url' => '',
                'prev_url' => '',
                'next_url' => ''
            ];
        } else {
            $per_page = (int)$per_page;
            $total_pages = ceil($total_count / $per_page);
            $current_page = max(1, min($current_page, $total_pages));

            $start_index = ($current_page - 1) * $per_page;
            $attendance_list = array_slice($all_attendance_list, $start_index, $per_page);

            $start = $start_index + 1;
            $end = min($start_index + $per_page, $total_count);

            // Build URLs for pagination
            $base_params = $_GET;
            unset($base_params['page']);
            $base_url = BASE_URL . 'children-ministry/attendance-list?' . http_build_query($base_params);

            $prev_params = $_GET;
            $prev_params['page'] = $current_page - 1;
            $prev_url = BASE_URL . 'children-ministry/attendance-list?' . http_build_query($prev_params);

            $next_params = $_GET;
            $next_params['page'] = $current_page + 1;
            $next_url = BASE_URL . 'children-ministry/attendance-list?' . http_build_query($next_params);

            $pagination_info = [
                'total' => $total_count,
                'current_page' => $current_page,
                'total_pages' => $total_pages,
                'per_page' => $per_page,
                'start' => $start,
                'end' => $end,
                'base_url' => $base_url,
                'prev_url' => $prev_url,
                'next_url' => $next_url
            ];
        }

        // Set page variables
        $page_title = getPageTitle('Children\'s Attendance List');
        $active_page = 'children_ministry';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/children_ministry/attendance_list.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Load layout
        require_once 'views/layouts/main.php';
    }



    /**
     * Calculate pagination information
     *
     * @param int $total_count
     * @param int $current_page
     * @param mixed $per_page
     * @param array $get_params
     * @return array
     */
    private function calculatePagination($total_count, $current_page, $per_page, $get_params) {
        // Handle "show all" option
        if ($per_page === 'all') {
            return [
                'total' => $total_count,
                'current_page' => 1,
                'total_pages' => 1,
                'per_page' => $total_count,
                'start' => 1,
                'end' => $total_count,
                'limit' => null,
                'offset' => 0,
                'base_url' => '',
                'prev_url' => '',
                'next_url' => ''
            ];
        }

        $per_page = (int)$per_page;
        $total_pages = ceil($total_count / $per_page);
        $current_page = max(1, min($current_page, $total_pages));

        $start = (($current_page - 1) * $per_page) + 1;
        $end = min($current_page * $per_page, $total_count);
        $offset = ($current_page - 1) * $per_page;

        // Build URLs for pagination
        $base_params = $get_params;
        unset($base_params['page']);
        $base_url = BASE_URL . 'children-ministry/attendance-list?' . http_build_query($base_params);

        $prev_params = $get_params;
        $prev_params['page'] = $current_page - 1;
        $prev_url = BASE_URL . 'children-ministry/attendance-list?' . http_build_query($prev_params);

        $next_params = $get_params;
        $next_params['page'] = $current_page + 1;
        $next_url = BASE_URL . 'children-ministry/attendance-list?' . http_build_query($next_params);

        return [
            'total' => $total_count,
            'current_page' => $current_page,
            'total_pages' => $total_pages,
            'per_page' => $per_page,
            'start' => $start,
            'end' => $end,
            'limit' => $per_page,
            'offset' => $offset,
            'base_url' => $base_url,
            'prev_url' => $prev_url,
            'next_url' => $next_url
        ];
    }

    /**
     * Get detailed attendance list (only children who attended)
     *
     * @param string $date
     * @param int|null $service_id
     * @param string $status
     * @return array
     */
    private function getDetailedAttendanceList($date, $service_id = null, $status = '') {
        try {
            $conn = $this->database->getConnection();

            // Get children ministry max age
            require_once 'models/ChildrenMinistry.php';
            $childrenMinistry = new ChildrenMinistry($conn);
            $max_child_age = $childrenMinistry->getMaxChildAge();

            // Get only children who actually attended (no absent children)
            $query = "
                SELECT
                    m.id,
                    m.first_name,
                    m.last_name,
                    m.gender,
                    m.date_of_birth,
                    TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                    ccl.id as checkin_id,
                    ccl.service_id,
                    ccl.check_in_time,
                    ccl.attendance_date,
                    ccl.security_code,
                    ccl.notes,
                    s.name as service_name,
                    s.time as service_time,
                    parent.first_name as parent_first_name,
                    parent.last_name as parent_last_name,
                    CASE
                        WHEN s.time IS NULL THEN 'present'
                        WHEN TIME(ccl.check_in_time) > TIME(s.time) THEN 'late'
                        ELSE 'present'
                    END as status,
                    CASE
                        WHEN ccl.notes LIKE '%QR Family%' THEN 'QR Family'
                        WHEN ccl.notes LIKE '%QR%' THEN 'QR Code'
                        ELSE 'Manual'
                    END as check_in_method
                FROM children_checkin_log ccl
                JOIN members m ON ccl.child_id = m.id
                JOIN services s ON ccl.service_id = s.id
                LEFT JOIN members parent ON ccl.checked_in_by = parent.id
                WHERE ccl.attendance_date = ?
                AND TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) <= ?
                AND m.member_status = 'active'
            ";
            $params = [$date, $max_child_age];

            // Add service filter
            if ($service_id) {
                $query .= " AND ccl.service_id = ?";
                $params[] = $service_id;
            }

            // Add status filter
            if ($status === 'present') {
                $query .= " AND (s.time IS NULL OR TIME(ccl.check_in_time) <= TIME(s.time))";
            } elseif ($status === 'late') {
                $query .= " AND s.time IS NOT NULL AND TIME(ccl.check_in_time) > TIME(s.time)";
            }

            $query .= " ORDER BY m.last_name, m.first_name";

            $stmt = $conn->prepare($query);
            $stmt->execute($params);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error getting detailed attendance list: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Calculate attendance statistics
     *
     * @param array $attendance_list
     * @param string $date
     * @return array
     */
    private function calculateAttendanceStats($attendance_list, $date) {
        $stats = [
            'total' => count($attendance_list),
            'present' => 0,
            'late' => 0,
            'absent' => 0
        ];

        foreach ($attendance_list as $record) {
            $status = $record['status'];
            if (isset($stats[$status])) {
                $stats[$status]++;
            }
        }

        return $stats;
    }

    /**
     * Calculate attendance statistics for attendance-only view
     *
     * @param string $date
     * @param int|null $service_id
     * @param array $attendance_list
     * @return array
     */
    private function calculateAttendanceOnlyStats($date, $service_id, $attendance_list) {
        $stats = [
            'total' => count($attendance_list),
            'present' => 0,
            'late' => 0,
            'absent' => 0  // Always 0 for attendance-only view
        ];

        foreach ($attendance_list as $record) {
            $status = $record['status'];
            if ($status === 'present') {
                $stats['present']++;
            } elseif ($status === 'late') {
                $stats['late']++;
            }
        }

        return $stats;
    }

    /**
     * Export Children's Ministry QR Analytics
     *
     * @return void
     */
    public function exportQrAnalytics() {
        try {
            $period_type = isset($_GET['period']) ? sanitize($_GET['period']) : 'monthly';
            $period_value = isset($_GET['value']) ? sanitize($_GET['value']) : date('Y-m');
            $format = isset($_GET['format']) ? sanitize($_GET['format']) : 'pdf';

            // Get analytics data
            $analytics_data = $this->getChildrenQrAnalyticsData($period_type, $period_value);

            if ($format === 'csv') {
                $this->exportQrAnalyticsCSV($analytics_data);
            } else {
                $this->exportQrAnalyticsPDF($analytics_data);
            }

        } catch (Exception $e) {
            error_log("Export QR Analytics Error: " . $e->getMessage());
            set_flash_message('Failed to export analytics. Please try again.', 'danger');
            redirect('children-ministry/qr-analytics');
        }
    }

    /**
     * Export QR analytics as CSV
     *
     * @param array $analytics_data
     * @return void
     */
    private function exportQrAnalyticsCSV($analytics_data) {
        $filename = 'children_ministry_qr_analytics_' . date('Y-m-d_H-i-s') . '.csv';

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: no-cache, must-revalidate');

        $output = fopen('php://output', 'w');

        // Overview section
        fputcsv($output, ['Children\'s Ministry QR Analytics Report']);
        fputcsv($output, ['Period', $analytics_data['period_info']['display_name']]);
        fputcsv($output, ['Generated', date('Y-m-d H:i:s')]);
        fputcsv($output, []);

        // Overview statistics
        fputcsv($output, ['Overview Statistics']);
        fputcsv($output, ['Metric', 'Value']);
        fputcsv($output, ['Total Check-ins', $analytics_data['overview']['total_checkins']]);
        fputcsv($output, ['QR Check-ins', $analytics_data['overview']['qr_checkins']]);
        fputcsv($output, ['Manual Check-ins', $analytics_data['overview']['manual_checkins']]);
        fputcsv($output, ['Unique Children', $analytics_data['overview']['unique_children']]);
        fputcsv($output, ['QR Adoption Rate', $analytics_data['overview']['qr_percentage'] . '%']);
        fputcsv($output, []);

        // Daily trends
        if (!empty($analytics_data['trends'])) {
            fputcsv($output, ['Daily Trends']);
            fputcsv($output, ['Date', 'Total Check-ins', 'QR Check-ins', 'Unique Children']);
            foreach ($analytics_data['trends'] as $trend) {
                fputcsv($output, [
                    $trend['date'],
                    $trend['total_checkins'],
                    $trend['qr_checkins'],
                    $trend['unique_children']
                ]);
            }
            fputcsv($output, []);
        }

        // Service performance
        if (!empty($analytics_data['service_performance'])) {
            fputcsv($output, ['Service Performance']);
            fputcsv($output, ['Service', 'Total Check-ins', 'QR Check-ins', 'Unique Children', 'QR Rate']);
            foreach ($analytics_data['service_performance'] as $service) {
                fputcsv($output, [
                    $service['service_name'],
                    $service['checkins'],
                    $service['qr_checkins'],
                    $service['unique_children'],
                    $service['qr_percentage'] . '%'
                ]);
            }
        }

        fclose($output);
        exit;
    }

    /**
     * Export QR analytics as PDF
     *
     * @param array $analytics_data
     * @return void
     */
    private function exportQrAnalyticsPDF($analytics_data) {
        // For now, create a simple HTML report that can be printed as PDF
        $filename = 'children_ministry_qr_analytics_' . date('Y-m-d_H-i-s') . '.html';

        header('Content-Type: text/html');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        echo $this->generateAnalyticsHTMLReport($analytics_data);
        exit;
    }

    /**
     * Generate HTML report for analytics
     *
     * @param array $analytics_data
     * @return string
     */
    private function generateAnalyticsHTMLReport($analytics_data) {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Children's Ministry QR Analytics Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .stats-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-bottom: 30px; }
                .stat-card { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f5f5f5; }
                .section-title { font-size: 18px; font-weight: bold; margin: 20px 0 10px 0; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Children's Ministry QR Analytics Report</h1>
                <p>Period: <?php echo htmlspecialchars($analytics_data['period_info']['display_name']); ?></p>
                <p>Generated: <?php echo date('F j, Y \a\t g:i A'); ?></p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Total Check-ins</h3>
                    <p style="font-size: 24px; font-weight: bold;"><?php echo number_format($analytics_data['overview']['total_checkins']); ?></p>
                </div>
                <div class="stat-card">
                    <h3>QR Check-ins</h3>
                    <p style="font-size: 24px; font-weight: bold;"><?php echo number_format($analytics_data['overview']['qr_checkins']); ?></p>
                </div>
                <div class="stat-card">
                    <h3>Unique Children</h3>
                    <p style="font-size: 24px; font-weight: bold;"><?php echo number_format($analytics_data['overview']['unique_children']); ?></p>
                </div>
                <div class="stat-card">
                    <h3>QR Adoption Rate</h3>
                    <p style="font-size: 24px; font-weight: bold;"><?php echo $analytics_data['overview']['qr_percentage']; ?>%</p>
                </div>
            </div>

            <?php if (!empty($analytics_data['service_performance'])): ?>
            <div class="section-title">Service Performance</div>
            <table>
                <thead>
                    <tr>
                        <th>Service</th>
                        <th>Total Check-ins</th>
                        <th>QR Check-ins</th>
                        <th>Unique Children</th>
                        <th>QR Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($analytics_data['service_performance'] as $service): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($service['service_name']); ?></td>
                        <td><?php echo number_format($service['checkins']); ?></td>
                        <td><?php echo number_format($service['qr_checkins']); ?></td>
                        <td><?php echo number_format($service['unique_children']); ?></td>
                        <td><?php echo $service['qr_percentage']; ?>%</td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>

            <?php if (!empty($analytics_data['age_group_analysis'])): ?>
            <div class="section-title">Age Group Analysis</div>
            <table>
                <thead>
                    <tr>
                        <th>Age Group</th>
                        <th>Total Check-ins</th>
                        <th>QR Check-ins</th>
                        <th>Unique Children</th>
                        <th>QR Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($analytics_data['age_group_analysis'] as $group): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($group['age_group'] ?: 'No Group'); ?></td>
                        <td><?php echo number_format($group['checkins']); ?></td>
                        <td><?php echo number_format($group['qr_checkins']); ?></td>
                        <td><?php echo number_format($group['unique_children']); ?></td>
                        <td><?php echo $group['checkins'] > 0 ? round(($group['qr_checkins'] / $group['checkins']) * 100, 1) : 0; ?>%</td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Display all children
     *
     * @return void
     */
    public function children() {
        try {
            // Handle export request
            if (isset($_GET['export']) && $_GET['export'] === 'csv') {
                $this->exportChildren();
                return;
            }

            // Handle print request
            if (isset($_GET['print'])) {
                $this->printChildren();
                return;
            }

            // Get search and filter parameters
            $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
            $age_group = isset($_GET['age_group']) ? sanitize($_GET['age_group']) : '';
            $gender = isset($_GET['gender']) ? sanitize($_GET['gender']) : '';
            $department = isset($_GET['department']) ? sanitize($_GET['department']) : '';
            $age_min = isset($_GET['age_min']) ? (int)$_GET['age_min'] : null;
            $age_max = isset($_GET['age_max']) ? (int)$_GET['age_max'] : null;

            // Use simple database queries instead of complex model methods
            $conn = $this->database->getConnection();

            // Get children with simple query
            if (!empty($search)) {
                $stmt = $conn->prepare("
                    SELECT m.*,
                           TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                           ag.name as age_group_name,
                           ag.id as age_group_id
                    FROM members m
                    LEFT JOIN children_age_groups ag ON (
                        TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) BETWEEN ag.min_age AND ag.max_age
                        AND ag.is_active = 1
                    )
                    WHERE TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) <= 17
                    AND m.member_status = 'active'
                    AND (m.first_name LIKE :search OR m.last_name LIKE :search)
                    ORDER BY m.created_at DESC, m.last_name, m.first_name
                ");
                $searchTerm = '%' . $search . '%';
                $stmt->bindParam(':search', $searchTerm);
            } else {
                $stmt = $conn->prepare("
                    SELECT m.*,
                           TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                           ag.name as age_group_name,
                           ag.id as age_group_id
                    FROM members m
                    LEFT JOIN children_age_groups ag ON (
                        TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) BETWEEN ag.min_age AND ag.max_age
                        AND ag.is_active = 1
                    )
                    WHERE TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) <= 17
                    AND m.member_status = 'active'
                    ORDER BY m.created_at DESC, m.last_name, m.first_name
                ");
            }

            $stmt->execute();
            $children = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Apply simple filters
            if (!empty($age_group)) {
                $children = array_filter($children, function($child) use ($age_group) {
                    return $child['age_group_id'] == $age_group;
                });
            }

            if (!empty($gender)) {
                $children = array_filter($children, function($child) use ($gender) {
                    return strtolower($child['gender']) == strtolower($gender);
                });
            }

            // Get age groups
            $stmt = $conn->prepare("SELECT * FROM children_age_groups WHERE is_active = 1 ORDER BY min_age");
            $stmt->execute();
            $age_groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Set page variables
            $page_title = getPageTitle('Children Directory');
            $active_page = 'children_ministry';

            // Start output buffering
            ob_start();

            // Load view
            require_once 'views/children_ministry/children.php';

            // Get the contents of the output buffer
            $content = ob_get_clean();

            // Load layout
            require_once 'views/layouts/main.php';

        } catch (Exception $e) {
            error_log("Children's Ministry Children Page Error: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            // Display error directly for debugging
            echo "<div style='padding: 20px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; margin: 20px;'>";
            echo "<h2>Children Directory Error</h2>";
            echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
            echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
            echo "<p><a href='" . BASE_URL . "children-ministry'>Back to Dashboard</a></p>";
            echo "</div>";
            exit;
        }
    }

    /**
     * Apply filters to children array
     *
     * @param array $children
     * @param array $filters
     * @return array
     */
    private function applyFilters($children, $filters) {
        $filtered = $children;

        // Age group filter
        if (!empty($filters['age_group'])) {
            $filtered = array_filter($filtered, function($child) use ($filters) {
                $childAgeGroupId = $child['age_group_id'] ?? null;
                // Convert both to string for comparison to handle type differences
                return (string)$childAgeGroupId === (string)$filters['age_group'];
            });
        }

        // Gender filter
        if (!empty($filters['gender'])) {
            $filtered = array_filter($filtered, function($child) use ($filters) {
                return $child['gender'] === $filters['gender'];
            });
        }

        // Re-index the array to maintain proper array structure
        return array_values($filtered);
    }

    /**
     * Export children data to CSV
     *
     * @return void
     */
    private function exportChildren() {
        // Get selected IDs if provided
        $selected_ids = isset($_GET['selected_ids']) ? explode(',', $_GET['selected_ids']) : [];

        // Get all children or filtered children
        $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
        if (!empty($search)) {
            $stmt = $this->childrenMinistry->searchChildren($search);
        } else {
            $stmt = $this->childrenMinistry->getAllChildren();
        }

        $children = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Filter by selected IDs if provided
        if (!empty($selected_ids)) {
            $children = array_filter($children, function($child) use ($selected_ids) {
                return in_array($child['id'], $selected_ids);
            });
        }

        // Apply other filters
        $children = $this->applyFilters($children, [
            'age_group' => isset($_GET['age_group']) ? sanitize($_GET['age_group']) : '',
            'gender' => isset($_GET['gender']) ? sanitize($_GET['gender']) : '',
            'department' => isset($_GET['department']) ? sanitize($_GET['department']) : '',
            'age_min' => isset($_GET['age_min']) ? (int)$_GET['age_min'] : null,
            'age_max' => isset($_GET['age_max']) ? (int)$_GET['age_max'] : null
        ]);

        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="children_export_' . date('Y-m-d_H-i-s') . '.csv"');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Create CSV output
        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, [
            'ID', 'First Name', 'Last Name', 'Age', 'Gender', 'Date of Birth',
            'Age Group', 'Department', 'Emergency Contact', 'Emergency Phone',
            'School', 'Member Status', 'Created Date'
        ]);

        // CSV data
        foreach ($children as $child) {
            fputcsv($output, [
                $child['id'],
                $child['first_name'],
                $child['last_name'],
                $child['age'],
                $child['gender'],
                $child['date_of_birth'],
                $child['age_group_name'] ?? '',
                $child['department'] ?? '',
                $child['emergency_contact_name'] ?? '',
                $child['emergency_contact_phone'] ?? '',
                $child['school'] ?? '',
                $child['member_status'],
                $child['created_at']
            ]);
        }

        fclose($output);
        exit;
    }

    /**
     * Print children data
     *
     * @return void
     */
    private function printChildren() {
        // Similar to export but render a print-friendly HTML page
        // This would be implemented based on specific print requirements
        echo "Print functionality - to be implemented based on requirements";
        exit;
    }

    /**
     * Display child details
     *
     * @return void
     */
    public function viewChild() {
        // Check if child ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Child ID is required.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        $child_id = sanitize($_GET['id']);

        // Get child family information
        $child_info = $this->childrenMinistry->getChildFamilyInfo($child_id);

        if (!$child_info) {
            set_flash_message('Child not found.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        // Set page variables
        $page_title = $child_info['child']['first_name'] . ' ' . $child_info['child']['last_name'] . ' - Children\'s Ministry';
        $active_page = 'children_ministry';

        // Start output buffering
        ob_start();

        // Include the view
        include 'views/children_ministry/child_details.php';

        // Get content
        $content = ob_get_clean();

        // Include layout
        include 'views/layouts/main.php';
    }

    /**
     * Edit child form
     *
     * @return void
     */
    public function editChild() {
        // Check if child ID is provided
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Child ID is required.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        $child_id = sanitize($_GET['id']);

        // Get child information
        $child_info = $this->childrenMinistry->getChildFamilyInfo($child_id);

        if (!$child_info) {
            set_flash_message('Child not found.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        // Set page variables
        $page_title = 'Edit ' . $child_info['child']['first_name'] . ' ' . $child_info['child']['last_name'] . ' - Children\'s Ministry';
        $active_page = 'children_ministry';

        // Start output buffering
        ob_start();

        // Include the view
        include 'views/children_ministry/edit_child.php';

        // Get content
        $content = ob_get_clean();

        // Include layout
        include 'views/layouts/main.php';
    }

    /**
     * Update child information
     *
     * @return void
     */
    public function updateChild() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('children-ministry/children');
            return;
        }

        // Check if child ID is provided
        if (!isset($_POST['id']) || empty($_POST['id'])) {
            set_flash_message('Child ID is required.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        $child_id = sanitize($_POST['id']);

        // Validate input
        $validation = new Validation($_POST);
        $validation->required('first_name')
                  ->required('last_name')
                  ->required('date_of_birth')
                  ->required('gender')
                  ->required('guardian_first_name')
                  ->required('guardian_last_name')
                  ->required('guardian_phone')
                  ->required('guardian_relationship');

        if ($validation->fails()) {
            set_flash_message('Please fill in all required fields.', 'danger');
            redirect('children-ministry/edit-child?id=' . $child_id);
            return;
        }

        // Get existing child data
        $existing_child = $this->member->getById($child_id);
        if (!$existing_child) {
            set_flash_message('Child not found.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        // Update member properties (using correct field names from Member model)
        $this->member->id = $child_id;
        $this->member->first_name = sanitize($_POST['first_name']);
        $this->member->last_name = sanitize($_POST['last_name']);
        $this->member->date_of_birth = sanitize($_POST['date_of_birth']);
        $this->member->gender = sanitize($_POST['gender']);
        $this->member->phone_number = sanitize($_POST['guardian_phone'] ?? ''); // Use guardian phone
        $this->member->email = sanitize($_POST['guardian_email'] ?? ''); // Use guardian email
        $this->member->location = sanitize($_POST['address'] ?? ''); // Use guardian address
        $this->member->emergency_contact_name = sanitize($_POST['guardian_first_name'] . ' ' . $_POST['guardian_last_name']); // Use guardian as emergency contact
        $this->member->emergency_contact_phone = sanitize($_POST['guardian_phone'] ?? ''); // Use guardian phone

        // Keep existing values for fields not in the form
        $this->member->marital_status = $existing_child['marital_status'] ?? 'single'; // Default for children
        $this->member->baptism_status = $existing_child['baptism_status'] ?? 'not_baptized'; // Default for children
        $this->member->department = 'children'; // Always set to children for child records
        $this->member->role = $existing_child['role'] ?? 'member';
        $this->member->membership_date = $existing_child['membership_date'] ?? date('Y-m-d');
        $this->member->occupation = ''; // Children don't have occupation
        $this->member->school = sanitize($_POST['school'] ?? '');
        $this->member->member_status = $existing_child['member_status'] ?? 'active';
        $this->member->updated_at = date('Y-m-d H:i:s');

        // Attempt to update child
        if ($this->member->update()) {
            // Update guardian information if provided
            if (!empty($_POST['guardian_first_name']) && !empty($_POST['guardian_last_name'])) {
                $existing_parents = $child_info['parents'] ?? [];
                $existing_guardians = $child_info['guardians'] ?? [];

                // Determine primary guardian (prioritize guardians, then parents)
                $primary_guardian = null;
                $is_guardian_contact = false;

                // First check for primary guardian in guardian_contacts
                foreach ($existing_guardians as $guardian) {
                    if ($guardian['is_primary']) {
                        $primary_guardian = $guardian;
                        $is_guardian_contact = true;
                        break;
                    }
                }

                // If no primary guardian found, check parents
                if (!$primary_guardian) {
                    foreach ($existing_parents as $parent) {
                        if ($parent['is_primary']) {
                            $primary_guardian = $parent;
                            $is_guardian_contact = false;
                            break;
                        }
                    }
                }

                // If still no primary found, use first available
                if (!$primary_guardian) {
                    if (!empty($existing_guardians)) {
                        $primary_guardian = $existing_guardians[0];
                        $is_guardian_contact = true;
                    } elseif (!empty($existing_parents)) {
                        $primary_guardian = $existing_parents[0];
                        $is_guardian_contact = false;
                    }
                }

                if ($primary_guardian) {
                    if ($is_guardian_contact) {
                        // Update guardian contact record
                        $guardian_contact = new GuardianContact($this->database->getConnection());
                        $guardian_contact->id = $primary_guardian['id'];
                        if ($guardian_contact->read()) {
                            $guardian_contact->first_name = sanitize($_POST['guardian_first_name']);
                            $guardian_contact->last_name = sanitize($_POST['guardian_last_name']);
                            $guardian_contact->phone_number = sanitize($_POST['guardian_phone'] ?? '');
                            $guardian_contact->email = sanitize($_POST['guardian_email'] ?? '');
                            $guardian_contact->occupation = sanitize($_POST['guardian_occupation'] ?? '');
                            $guardian_contact->address = sanitize($_POST['guardian_address'] ?? '');
                            $guardian_contact->relationship_to_child = sanitize($_POST['guardian_relationship'] ?? '');
                            $guardian_contact->updated_at = date('Y-m-d H:i:s');
                            $guardian_contact->update();
                        }
                    } else {
                        // Update member parent record
                        $parent_member = new Member($this->database->getConnection());
                        if ($parent_member->getById($primary_guardian['id'])) {
                            $parent_member->first_name = sanitize($_POST['guardian_first_name']);
                            $parent_member->last_name = sanitize($_POST['guardian_last_name']);
                            $parent_member->phone_number = sanitize($_POST['guardian_phone'] ?? '');
                            $parent_member->email = sanitize($_POST['guardian_email'] ?? '');
                            $parent_member->occupation = sanitize($_POST['guardian_occupation'] ?? '');
                            $parent_member->location = sanitize($_POST['guardian_address'] ?? '');
                            $parent_member->updated_at = date('Y-m-d H:i:s');
                            $parent_member->update();

                            // Update relationship type if changed
                            $relationship_type = sanitize($_POST['guardian_relationship'] ?? '');
                            if ($relationship_type !== $primary_guardian['relationship_type']) {
                                $this->childrenMinistry->familyRelationship->updateRelationshipType(
                                    $primary_guardian['id'],
                                    $child_id,
                                    $relationship_type
                                );
                            }
                        }
                    }
                }
            }

            set_flash_message('Child and guardian information updated successfully.', 'success');
            redirect('children-ministry/view-child?id=' . $child_id);
        } else {
            set_flash_message('Failed to update child information. Please try again.', 'danger');
            redirect('children-ministry/edit-child?id=' . $child_id);
        }
    }

    /**
     * Fix missing department and emergency contact information for existing children
     *
     * @return void
     */
    public function fixChildrenData() {
        // Get all children
        $children = $this->childrenMinistry->getAllChildren(false)->fetchAll(PDO::FETCH_ASSOC);

        $fixed_count = 0;

        foreach ($children as $child) {
            $needs_update = false;
            $member = new Member($this->database->getConnection());

            if ($member->getById($child['id'])) {
                // Fix department if missing or empty
                if (empty($child['department']) || $child['department'] === 'None') {
                    $member->department = 'children';
                    $needs_update = true;
                }

                // Fix emergency contact if missing and child has parents
                if (empty($child['emergency_contact_name']) || empty($child['emergency_contact_phone'])) {
                    $child_info = $this->childrenMinistry->getChildFamilyInfo($child['id']);
                    if (!empty($child_info['parents'])) {
                        $primary_parent = $child_info['parents'][0];
                        $member->emergency_contact_name = $primary_parent['first_name'] . ' ' . $primary_parent['last_name'];
                        $member->emergency_contact_phone = $primary_parent['phone_number'];
                        $needs_update = true;
                    }
                }

                if ($needs_update) {
                    $member->updated_at = date('Y-m-d H:i:s');
                    if ($member->update()) {
                        $fixed_count++;
                    }
                }
            }
        }

        set_flash_message("Fixed data for {$fixed_count} children.", 'success');
        redirect('children-ministry/children');
    }

    /**
     * Delete child with comprehensive relationship cleanup
     *
     * @return void
     */
    public function deleteChild() {
        // Set JSON response headers
        header('Content-Type: application/json');

        // Check if child ID is provided
        if (!isset($_POST['child_id']) || empty($_POST['child_id'])) {
            echo json_encode(['success' => false, 'message' => 'Child ID is required.']);
            return;
        }

        $child_id = sanitize($_POST['child_id']);
        $delete_type = sanitize($_POST['delete_type'] ?? 'soft'); // 'soft' or 'hard'

        // Get child information first (outside transaction)
        $child_info = $this->childrenMinistry->getChildFamilyInfo($child_id);
        if (!$child_info) {
            echo json_encode(['success' => false, 'message' => 'Child not found.']);
            return;
        }

        $child = $child_info['child'];
        $child_name = $child['first_name'] . ' ' . $child['last_name'];

        try {
            $conn = $this->database->getConnection();

            if ($delete_type === 'soft') {
                // Soft delete - quick operation, minimal transaction time
                $conn->beginTransaction();

                $stmt = $conn->prepare("UPDATE members SET member_status = 'inactive', updated_at = NOW() WHERE id = ?");
                $stmt->execute([$child_id]);

                if ($stmt->rowCount() == 0) {
                    throw new Exception('Failed to deactivate child record - record not found.');
                }

                $conn->commit();
                echo json_encode([
                    'success' => true,
                    'message' => "$child_name has been deactivated successfully.",
                    'type' => 'soft_delete'
                ]);

            } else {
                // Hard delete - optimized transaction
                $conn->beginTransaction();

                $deletion_summary = $this->performHardDelete($child_id, $child_info, $conn);

                $conn->commit();
                echo json_encode([
                    'success' => true,
                    'message' => "$child_name has been permanently deleted.",
                    'type' => 'hard_delete',
                    'summary' => $deletion_summary
                ]);
            }

        } catch (Exception $e) {
            if (isset($conn) && $conn->inTransaction()) {
                $conn->rollback();
            }

            error_log("Error deleting child ID $child_id: " . $e->getMessage());

            // Provide user-friendly error messages
            $user_message = $e->getMessage();
            if (strpos($user_message, 'Lock wait timeout') !== false) {
                $user_message = 'The system is currently busy. Please try again in a moment.';
            } elseif (strpos($user_message, 'foreign key constraint') !== false) {
                $user_message = 'Cannot delete this child due to data dependencies. Please contact the administrator.';
            }

            echo json_encode([
                'success' => false,
                'message' => $user_message
            ]);
        }
    }

    /**
     * Perform hard delete with optimized transaction management
     *
     * @param int $child_id
     * @param array $child_info
     * @param PDO $conn
     * @return array
     */
    private function performHardDelete($child_id, $child_info, $conn) {
        $summary = [
            'family_relationships' => 0,
            'guardian_relationships' => 0,
            'orphaned_guardians' => 0,
            'medical_records' => 0,
            'checkin_records' => 0,
            'authorizations' => 0
        ];

        try {
            // Set transaction timeout for better performance (compatible with all MySQL versions)
            $conn->exec("SET SESSION innodb_lock_wait_timeout = 10");

            // 1. Delete in optimal order to minimize lock conflicts
            // Start with dependent records first (least likely to cause locks)

            // Delete check-in records (no foreign key dependencies)
            $stmt = $conn->prepare("DELETE FROM children_checkin_log WHERE child_id = ?");
            $stmt->execute([$child_id]);
            $summary['checkin_records'] = $stmt->rowCount();

            // Delete medical information (simple foreign key)
            $stmt = $conn->prepare("DELETE FROM children_medical_info WHERE child_id = ?");
            $stmt->execute([$child_id]);
            $summary['medical_records'] = $stmt->rowCount();

            // Delete child authorizations (simple foreign key)
            $stmt = $conn->prepare("DELETE FROM child_authorizations WHERE child_id = ?");
            $stmt->execute([$child_id]);
            $summary['authorizations'] = $stmt->rowCount();

            // 2. Handle guardian relationships (more complex)
            // Get guardian IDs before deleting relationships
            $stmt = $conn->prepare("SELECT guardian_contact_id FROM child_guardian_relationships WHERE child_id = ? AND guardian_contact_id IS NOT NULL");
            $stmt->execute([$child_id]);
            $guardian_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Delete child-guardian relationships
            $stmt = $conn->prepare("DELETE FROM child_guardian_relationships WHERE child_id = ?");
            $stmt->execute([$child_id]);
            $summary['guardian_relationships'] = $stmt->rowCount();

            // Check and delete orphaned guardians
            foreach ($guardian_ids as $guardian_id) {
                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM child_guardian_relationships WHERE guardian_contact_id = ?");
                $stmt->execute([$guardian_id]);
                $other_children = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

                if ($other_children == 0) {
                    $stmt = $conn->prepare("DELETE FROM guardian_contacts WHERE id = ?");
                    $stmt->execute([$guardian_id]);
                    if ($stmt->rowCount() > 0) {
                        $summary['orphaned_guardians']++;
                    }
                }
            }

            // 3. Delete family relationships (member parents)
            $stmt = $conn->prepare("DELETE FROM family_relationships WHERE child_id = ?");
            $stmt->execute([$child_id]);
            $summary['family_relationships'] = $stmt->rowCount();

            // 4. Handle profile picture deletion (file system operation)
            $stmt = $conn->prepare("SELECT profile_picture FROM members WHERE id = ?");
            $stmt->execute([$child_id]);
            $profile_picture = $stmt->fetchColumn();

            if (!empty($profile_picture) && file_exists($profile_picture)) {
                unlink($profile_picture);
            }

            // 5. Finally, delete the child member record
            $stmt = $conn->prepare("DELETE FROM members WHERE id = ?");
            $stmt->execute([$child_id]);

            if ($stmt->rowCount() == 0) {
                throw new Exception('Failed to delete child member record - record not found.');
            }

            return $summary;

        } catch (PDOException $e) {
            // Handle specific database errors
            if (strpos($e->getMessage(), 'Lock wait timeout') !== false) {
                throw new Exception('Database is busy. Please try again in a moment.');
            } elseif (strpos($e->getMessage(), 'foreign key constraint') !== false) {
                throw new Exception('Cannot delete child due to data dependencies. Please contact administrator.');
            } else {
                throw new Exception('Database error: ' . $e->getMessage());
            }
        }
    }

    /**
     * Family management page
     *
     * @return void
     */
    public function families() {
        // BASE_URL is centrally managed

        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Set page variables
        $page_title = 'Family Management - Children\'s Ministry';
        $active_page = 'children_ministry';

        // Get family data
        $family_groups = $this->getFamilyGroups();

        // Render the complete page with layout
        $this->renderFamiliesPage($page_title, $active_page, $family_groups);
    }

    /**
     * Get family groups data
     */
    private function getFamilyGroups() {
        try {
            // Get all family relationships
            $families = $this->childrenMinistry->familyRelationship->getAll()->fetchAll(PDO::FETCH_ASSOC);

            // Group by family
            $family_groups = [];
            foreach ($families as $relationship) {
                $parent_key = $relationship['parent_id'];
                if (!isset($family_groups[$parent_key])) {
                    $family_groups[$parent_key] = [
                        'parent' => [
                            'id' => $relationship['parent_id'],
                            'name' => $relationship['parent_first_name'] . ' ' . $relationship['parent_last_name']
                        ],
                        'children' => []
                    ];
                }
                $family_groups[$parent_key]['children'][] = $relationship;
            }

            return $family_groups;
        } catch (Exception $e) {
            error_log("Error getting family groups: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Render the families page using the main layout
     */
    private function renderFamiliesPage($page_title, $active_page, $family_groups) {
        // Start output buffering for the content
        ob_start();
        ?>
        <!-- Page Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Family Management</h1>
                        <p class="text-gray-600 mt-1">Manage parent-child relationships and family connections</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                            <?php echo count($family_groups); ?> Families
                        </span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                            <?php
                            $total_children = 0;
                            foreach ($family_groups as $family) {
                                $total_children += count($family['children']);
                            }
                            echo $total_children;
                            ?> Children
                        </span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="px-6 py-4 bg-gray-50">
                <div class="flex flex-wrap gap-3">
                    <a href="<?php echo BASE_URL; ?>children-ministry"
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                    <a href="<?php echo BASE_URL; ?>members/add"
                       class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-user-plus mr-2"></i>Register New Family
                    </a>

                    <button onclick="exportFamilies()"
                            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                        <i class="fas fa-download mr-2"></i>Export Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div class="flex-1 max-w-md">
                        <div class="relative">
                            <input type="text" id="searchFamilies" placeholder="Search families, parents, or children..."
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <select id="filterByDepartment" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Departments</option>
                            <option value="children">Children's Ministry</option>
                            <option value="youth">Youth Ministry</option>
                            <option value="adult">Adult Ministry</option>
                        </select>
                        <button onclick="clearFilters()" class="text-gray-500 hover:text-gray-700 text-sm">
                            <i class="fas fa-times mr-1"></i>Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Family Table -->
        <?php $this->renderFamilyTable($family_groups); ?>



        <!-- JavaScript -->
        <script src="<?php echo BASE_URL; ?>assets/js/children-ministry.js"></script>
        <script>
        // Search functionality
        document.getElementById('searchFamilies').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('.family-row');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Export functionality
        function exportFamilies() {
            window.location.href = '<?php echo BASE_URL; ?>children-ministry/export-families';
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('searchFamilies').value = '';
            document.getElementById('filterByDepartment').value = '';
            document.querySelectorAll('.family-row').forEach(row => {
                row.style.display = '';
            });
        }
        </script>

        <?php
        // Get the content
        $content = ob_get_clean();

        // Load the main layout
        require_once 'views/layouts/main.php';
    }



    /**
     * Render family table with parent and children information
     */
    private function renderFamilyTable($family_groups) {
        if (empty($family_groups)) {
            ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <div class="text-gray-400 mb-4">
                    <i class="fas fa-home text-6xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">No Family Relationships Found</h3>
                <p class="text-gray-500 mb-4">Family relationships will appear here once members are registered with family connections.</p>
            </div>
            <?php
        } else {
            // Initialize database connection once
            require_once 'config/database.php';
            $database = new Database();
            $conn = $database->getConnection();
            ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 family-table">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Parent/Guardian
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Contact Information
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Children
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Children
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Department
                                </th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($family_groups as $family): ?>
                                <?php
                                // Get parent details from database
                                $parent_member = new Member($conn);
                                $parent_member->getById($family['parent']['id']);
                                ?>
                                <tr class="family-row hover:bg-gray-50">
                                    <!-- Parent Information -->
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                    <i class="fas fa-user text-blue-600"></i>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($family['parent']['name']); ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    Parent/Guardian
                                                </div>
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Contact Information -->
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php if (!empty($parent_member->email)): ?>
                                                <div class="flex items-center mb-1">
                                                    <i class="fas fa-envelope text-gray-400 mr-2"></i>
                                                    <?php echo htmlspecialchars($parent_member->email); ?>
                                                </div>
                                            <?php endif; ?>
                                            <?php if (!empty($parent_member->phone_number)): ?>
                                                <div class="flex items-center">
                                                    <i class="fas fa-phone text-gray-400 mr-2"></i>
                                                    <?php echo htmlspecialchars($parent_member->phone_number); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>

                                    <!-- Children Information -->
                                    <td class="px-6 py-4">
                                        <div class="space-y-2">
                                            <?php foreach ($family['children'] as $child): ?>
                                                <div class="bg-gray-50 rounded-md p-2 child-card">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0 h-6 w-6">
                                                            <div class="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center">
                                                                <i class="fas fa-child text-green-600 text-xs"></i>
                                                            </div>
                                                        </div>
                                                        <div class="ml-2">
                                                            <div class="text-sm font-medium text-gray-900">
                                                                <?php echo htmlspecialchars($child['child_first_name'] . ' ' . $child['child_last_name']); ?>
                                                            </div>
                                                            <div class="text-xs text-gray-500">
                                                                <?php
                                                                $age = $child['child_age'] ?? 'Unknown age';
                                                                $gender = ucfirst($child['child_gender'] ?? 'Unknown');
                                                                $relationship = ucfirst($child['relationship_type']);
                                                                echo "$age • $gender • $relationship";
                                                                ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </td>

                                    <!-- Total Children -->
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <?php echo count($family['children']); ?> child<?php echo count($family['children']) > 1 ? 'ren' : ''; ?>
                                        </span>
                                    </td>

                                    <!-- Department -->
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?php echo ucfirst($parent_member->department ?? 'General'); ?>
                                        </span>
                                    </td>

                                    <!-- Actions -->
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                        <div class="flex justify-center space-x-2">
                                            <!-- View Family -->
                                            <a href="<?php echo BASE_URL; ?>members/view/<?php echo $family['parent']['id']; ?>"
                                               class="action-button text-blue-600 hover:text-blue-900 p-2 rounded-md border border-blue-200 hover:border-blue-300"
                                               title="View Family">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- Edit Family -->
                                            <a href="<?php echo BASE_URL; ?>members/edit?id=<?php echo $family['parent']['id']; ?>"
                                               class="action-button text-yellow-600 hover:text-yellow-900 p-2 rounded-md border border-yellow-200 hover:border-yellow-300"
                                               title="Edit Family">
                                                <i class="fas fa-edit"></i>
                                            </a>



                                            <!-- Delete Family -->
                                            <button onclick="deleteFamily(<?php echo $family['parent']['id']; ?>)"
                                                    class="action-button text-red-600 hover:text-red-900 p-2 rounded-md border border-red-200 hover:border-red-300"
                                                    title="Delete Family">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Table Footer with Pagination -->
                <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </button>
                            <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium"><?php echo count($family_groups); ?></span> families with
                                    <span class="font-medium">
                                        <?php
                                        $total_children = 0;
                                        foreach ($family_groups as $family) {
                                            $total_children += count($family['children']);
                                        }
                                        echo $total_children;
                                        ?>
                                    </span> children total
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <!-- Pagination will be implemented later -->
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
        }
    }



    /**
     * Delete a family and all its relationships
     */
    public function deleteFamily() {
        header('Content-Type: application/json');

        try {
            // Check if request is POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Invalid request method');
            }

            // Get JSON input
            $input = json_decode(file_get_contents('php://input'), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON data');
            }

            if (!isset($input['parent_id']) || empty($input['parent_id'])) {
                throw new Exception('Parent ID is required');
            }

            $parent_id = (int)$input['parent_id'];

            if ($parent_id <= 0) {
                throw new Exception('Invalid parent ID');
            }

            // Start transaction
            $conn = $this->database->getConnection();
            $conn->beginTransaction();

            try {
                // First, check if the parent exists and has relationships
                $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM family_relationships WHERE parent_id = ?");
                $check_stmt->execute([$parent_id]);
                $relationship_count = $check_stmt->fetch(PDO::FETCH_ASSOC)['count'];

                if ($relationship_count == 0) {
                    throw new Exception('No family relationships found for this parent');
                }

                // Delete all family relationships for this parent
                $stmt = $conn->prepare("DELETE FROM family_relationships WHERE parent_id = ?");
                $result = $stmt->execute([$parent_id]);

                if (!$result) {
                    throw new Exception('Failed to delete family relationships');
                }

                $deleted_count = $stmt->rowCount();

                // Commit transaction
                $conn->commit();

                // Log the deletion
                error_log("Family deleted: Parent ID $parent_id, Relationships deleted: $deleted_count");

                echo json_encode([
                    'success' => true,
                    'message' => "Family deleted successfully. Removed $deleted_count relationship(s).",
                    'deleted_relationships' => $deleted_count
                ]);

            } catch (Exception $e) {
                // Rollback transaction
                $conn->rollback();
                throw $e;
            }

        } catch (Exception $e) {
            // Log the error
            error_log("Error deleting family: " . $e->getMessage());

            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    // REMOVED: Manual check-in interface - Using QR codes exclusively

    // REMOVED: Manual check-in processing - Using QR codes exclusively

    // REMOVED: Manual family check-in processing - Using QR codes exclusively





    /**
     * Medical information management
     *
     * @return void
     */
    public function medicalInfo() {
        // Get child ID
        if (!isset($_GET['child_id']) || empty($_GET['child_id'])) {
            set_flash_message('Child ID is required.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        $child_id = sanitize($_GET['child_id']);

        // Get child information
        $child_info = $this->childrenMinistry->getChildFamilyInfo($child_id);

        if (!$child_info) {
            set_flash_message('Child not found.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        // Set page variables
        $page_title = 'Medical Information - ' . $child_info['child']['first_name'] . ' ' . $child_info['child']['last_name'];
        $active_page = 'children_ministry';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/children_ministry/medical_info.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Load layout
        require_once 'views/layouts/main.php';
    }

    /**
     * Save medical information
     *
     * @return void
     */
    public function saveMedicalInfo() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('children-ministry/children');
            return;
        }

        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
            set_flash_message('Invalid form submission.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        // Validate input
        $validation = new Validation($_POST);
        $validation->required('child_id');

        if ($validation->fails()) {
            set_flash_message('Child ID is required.', 'danger');
            redirect('children-ministry/children');
            return;
        }

        // Set medical info properties
        $this->childrenMinistry->medicalInfo->child_id = sanitize($_POST['child_id']);
        $this->childrenMinistry->medicalInfo->allergies = sanitize($_POST['allergies'] ?? '');
        $this->childrenMinistry->medicalInfo->medical_conditions = sanitize($_POST['medical_conditions'] ?? '');
        $this->childrenMinistry->medicalInfo->medications = sanitize($_POST['medications'] ?? '');
        $this->childrenMinistry->medicalInfo->special_needs = sanitize($_POST['special_needs'] ?? '');
        $this->childrenMinistry->medicalInfo->emergency_medical_contact = sanitize($_POST['emergency_medical_contact'] ?? '');
        $this->childrenMinistry->medicalInfo->emergency_medical_phone = sanitize($_POST['emergency_medical_phone'] ?? '');
        $this->childrenMinistry->medicalInfo->doctor_name = sanitize($_POST['doctor_name'] ?? '');
        $this->childrenMinistry->medicalInfo->doctor_phone = sanitize($_POST['doctor_phone'] ?? '');
        $this->childrenMinistry->medicalInfo->created_at = date('Y-m-d H:i:s');
        $this->childrenMinistry->medicalInfo->updated_at = date('Y-m-d H:i:s');

        // Attempt to save medical information
        if ($this->childrenMinistry->medicalInfo->createOrUpdate()) {
            set_flash_message('Medical information saved successfully.', 'success');
        } else {
            set_flash_message('Failed to save medical information. Please try again.', 'danger');
        }

        redirect('children-ministry/medical-info?child_id=' . $_POST['child_id']);
    }

    /**
     * Attendance reports
     *
     * @return void
     */
    public function reports() {
        // Get date range from request
        $start_date = isset($_GET['start_date']) ? sanitize($_GET['start_date']) : date('Y-m-01');
        $end_date = isset($_GET['end_date']) ? sanitize($_GET['end_date']) : date('Y-m-d');

        // Get attendance statistics
        $attendance_stats = $this->childrenMinistry->checkinLog->getAttendanceStats($start_date, $end_date);

        // Get daily attendance for the selected date (if single date)
        $daily_attendance = [];
        if ($start_date === $end_date) {
            $daily_attendance = $this->childrenMinistry->checkinLog->getDailyAttendanceReport($start_date)->fetchAll(PDO::FETCH_ASSOC);
        }

        // Set page variables
        $page_title = 'Attendance Reports - Children\'s Ministry';
        $active_page = 'children_ministry';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/children_ministry/reports.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Load layout
        require_once 'views/layouts/main.php';
    }

    /**
     * Age groups management
     *
     * @return void
     */
    public function ageGroups() {
        // Get all age groups
        $age_groups = $this->childrenMinistry->ageGroup->getChildrenCountByAgeGroup()->fetchAll(PDO::FETCH_ASSOC);

        // Set page variables
        $page_title = 'Age Groups - Children\'s Ministry';
        $active_page = 'children_ministry';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/children_ministry/age_groups.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Load layout
        require_once 'views/layouts/main.php';
    }

    /**
     * Save age group (create or update)
     *
     * @return void
     */
    public function saveAgeGroup() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('children-ministry/age-groups');
            return;
        }

        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
            set_flash_message('Invalid form submission.', 'danger');
            redirect('children-ministry/age-groups');
            return;
        }

        // Validate input
        $validation = new Validation($_POST);
        $validation->required('name')
                  ->required('min_age')
                  ->required('max_age');

        if ($validation->fails()) {
            set_flash_message('Please fill in all required fields.', 'danger');
            redirect('children-ministry/age-groups');
            return;
        }

        // Validate age range
        $min_age = (int)$_POST['min_age'];
        $max_age = (int)$_POST['max_age'];

        if ($min_age > $max_age) {
            set_flash_message('Minimum age cannot be greater than maximum age.', 'danger');
            redirect('children-ministry/age-groups');
            return;
        }

        // Check for overlapping age ranges
        $exclude_id = isset($_POST['id']) ? $_POST['id'] : null;
        if ($this->childrenMinistry->ageGroup->hasOverlappingRange($min_age, $max_age, $exclude_id)) {
            set_flash_message('Age range overlaps with an existing age group.', 'danger');
            redirect('children-ministry/age-groups');
            return;
        }

        // Set age group properties
        $this->childrenMinistry->ageGroup->name = sanitize($_POST['name']);
        $this->childrenMinistry->ageGroup->min_age = $min_age;
        $this->childrenMinistry->ageGroup->max_age = $max_age;
        $this->childrenMinistry->ageGroup->description = sanitize($_POST['description'] ?? '');
        $this->childrenMinistry->ageGroup->is_active = isset($_POST['is_active']) ? 1 : 0;
        $this->childrenMinistry->ageGroup->updated_at = date('Y-m-d H:i:s');

        // Check if this is an update or create
        if (isset($_POST['id']) && !empty($_POST['id'])) {
            // Update existing age group
            $this->childrenMinistry->ageGroup->id = sanitize($_POST['id']);

            if ($this->childrenMinistry->ageGroup->update()) {
                set_flash_message('Age group updated successfully.', 'success');
            } else {
                set_flash_message('Failed to update age group. Please try again.', 'danger');
            }
        } else {
            // Create new age group
            $this->childrenMinistry->ageGroup->created_at = date('Y-m-d H:i:s');

            if ($this->childrenMinistry->ageGroup->create()) {
                set_flash_message('Age group created successfully.', 'success');
            } else {
                set_flash_message('Failed to create age group. Please try again.', 'danger');
            }
        }

        redirect('children-ministry/age-groups');
    }

    /**
     * Get age group data for editing (AJAX)
     *
     * @return void
     */
    public function getAgeGroup($id = null) {
        // Set content type header first
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'GET' || !$id) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request - missing ID parameter']);
            exit;
        }

        $id = sanitize($id);

        try {
            $age_group = $this->childrenMinistry->ageGroup->getById($id);

            if ($age_group) {
                echo json_encode($age_group);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Age group not found with ID: ' . $id]);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
        }

        exit; // Important: prevent any additional output
    }

    /**
     * Delete age group
     *
     * @return void
     */
    public function deleteAgeGroup() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('children-ministry/age-groups');
            return;
        }

        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
            set_flash_message('Invalid form submission.', 'danger');
            redirect('children-ministry/age-groups');
            return;
        }

        if (!isset($_POST['id']) || empty($_POST['id'])) {
            set_flash_message('Age group ID is required.', 'danger');
            redirect('children-ministry/age-groups');
            return;
        }

        // Delete the age group (model will handle data integrity checks)
        $this->childrenMinistry->ageGroup->id = sanitize($_POST['id']);

        if ($this->childrenMinistry->ageGroup->delete()) {
            set_flash_message('Age group deleted successfully.', 'success');
        } else {
            // Get error message from model
            $errorMessage = $this->childrenMinistry->ageGroup->error ?? 'Failed to delete age group. Please try again.';
            set_flash_message($errorMessage, 'danger');
        }

        redirect('children-ministry/age-groups');
    }

    /**
     * Toggle age group status
     *
     * @return void
     */
    public function toggleAgeGroupStatus() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('children-ministry/age-groups');
            return;
        }

        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
            set_flash_message('Invalid form submission.', 'danger');
            redirect('children-ministry/age-groups');
            return;
        }

        if (!isset($_POST['id']) || !isset($_POST['status'])) {
            set_flash_message('Invalid request parameters.', 'danger');
            redirect('children-ministry/age-groups');
            return;
        }

        // Get the age group
        $age_group = $this->childrenMinistry->ageGroup->getById($_POST['id']);

        if (!$age_group) {
            set_flash_message('Age group not found.', 'danger');
            redirect('children-ministry/age-groups');
            return;
        }

        // Update the status
        $this->childrenMinistry->ageGroup->id = sanitize($_POST['id']);
        $this->childrenMinistry->ageGroup->name = $age_group['name'];
        $this->childrenMinistry->ageGroup->min_age = $age_group['min_age'];
        $this->childrenMinistry->ageGroup->max_age = $age_group['max_age'];
        $this->childrenMinistry->ageGroup->description = $age_group['description'];
        $this->childrenMinistry->ageGroup->is_active = $_POST['status'] === 'true' ? 1 : 0;
        $this->childrenMinistry->ageGroup->updated_at = date('Y-m-d H:i:s');

        $action = $_POST['status'] === 'true' ? 'activated' : 'deactivated';

        if ($this->childrenMinistry->ageGroup->update()) {
            set_flash_message("Age group {$action} successfully.", 'success');
        } else {
            set_flash_message("Failed to {$action} age group. Please try again.", 'danger');
        }

        redirect('children-ministry/age-groups');
    }

    /**
     * Get adult members for family relationships (API endpoint)
     *
     * @return void
     */
    public function getAdultMembers() {
        header('Content-Type: application/json');

        try {
            // Get members who are 18 or older (potential parents/guardians)
            $query = "SELECT id, first_name, last_name, phone_number, email,
                             TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) as age
                      FROM members
                      WHERE member_status = 'active'
                      AND TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) >= 18
                      ORDER BY last_name, first_name";

            $conn = $this->database->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $adults = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'data' => $adults
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }

        exit;
    }

    /**
     * Get child members for family relationships (API endpoint)
     *
     * @return void
     */
    public function getChildMembers() {
        header('Content-Type: application/json');

        try {
            // Get members who are under the configured child age limit (potential children)
            $max_child_age = $this->childrenMinistry->getMaxChildAge();

            $query = "SELECT m.id, m.first_name, m.last_name, m.date_of_birth,
                             TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                             ag.name as age_group_name
                      FROM members m
                      LEFT JOIN children_age_groups ag ON (
                          TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) BETWEEN ag.min_age AND ag.max_age
                          AND ag.is_active = 1
                      )
                      WHERE m.member_status = 'active'
                      AND TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) <= :max_child_age
                      ORDER BY m.created_at DESC, m.date_of_birth DESC";

            $conn = $this->database->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':max_child_age', $max_child_age, PDO::PARAM_INT);
            $stmt->execute();
            $children = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'data' => $children
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }

        exit;
    }

    /**
     * Get relationship data for editing (API endpoint)
     *
     * @return void
     */
    public function editRelationship() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'GET' || !isset($_GET['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            exit;
        }

        try {
            $relationship = $this->childrenMinistry->familyRelationship->getById($_GET['id']);

            if ($relationship) {
                echo json_encode($relationship);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Relationship not found']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
        }

        exit;
    }

    /**
     * Update family relationship
     *
     * @return void
     */
    public function updateRelationship() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('children-ministry/families');
            return;
        }

        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
            set_flash_message('Invalid form submission.', 'danger');
            redirect('children-ministry/families');
            return;
        }

        // Validate input
        $validation = new Validation($_POST);
        $validation->required('id')
                  ->required('parent_id')
                  ->required('child_id')
                  ->required('relationship_type');

        if ($validation->fails()) {
            set_flash_message('Please fill in all required fields.', 'danger');
            redirect('children-ministry/families');
            return;
        }

        // Get existing relationship
        $existing = $this->childrenMinistry->familyRelationship->getById($_POST['id']);
        if (!$existing) {
            set_flash_message('Relationship not found.', 'danger');
            redirect('children-ministry/families');
            return;
        }

        // Update relationship properties
        $this->childrenMinistry->familyRelationship->id = sanitize($_POST['id']);
        $this->childrenMinistry->familyRelationship->parent_id = sanitize($_POST['parent_id']);
        $this->childrenMinistry->familyRelationship->child_id = sanitize($_POST['child_id']);
        $this->childrenMinistry->familyRelationship->relationship_type = sanitize($_POST['relationship_type']);
        $this->childrenMinistry->familyRelationship->is_primary = isset($_POST['is_primary']) ? 1 : 0;
        $this->childrenMinistry->familyRelationship->can_pickup = isset($_POST['can_pickup']) ? 1 : 0;
        $this->childrenMinistry->familyRelationship->notes = sanitize($_POST['notes'] ?? '');
        $this->childrenMinistry->familyRelationship->updated_at = date('Y-m-d H:i:s');

        // Attempt to update relationship
        if ($this->childrenMinistry->familyRelationship->updateComplete()) {
            set_flash_message('Family relationship updated successfully.', 'success');
        } else {
            set_flash_message('Failed to update family relationship. Please try again.', 'danger');
        }

        redirect('children-ministry/families');
    }

    /**
     * Delete family relationship
     *
     * @return void
     */
    public function deleteRelationship() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('children-ministry/families');
            return;
        }

        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
            set_flash_message('Invalid form submission.', 'danger');
            redirect('children-ministry/families');
            return;
        }

        if (!isset($_POST['id']) || empty($_POST['id'])) {
            set_flash_message('Relationship ID is required.', 'danger');
            redirect('children-ministry/families');
            return;
        }

        // Get relationship details for confirmation
        $relationship = $this->childrenMinistry->familyRelationship->getById($_POST['id']);
        if (!$relationship) {
            set_flash_message('Relationship not found.', 'danger');
            redirect('children-ministry/families');
            return;
        }

        // Delete the relationship
        $this->childrenMinistry->familyRelationship->id = sanitize($_POST['id']);

        if ($this->childrenMinistry->familyRelationship->delete()) {
            set_flash_message('Family relationship deleted successfully.', 'success');
        } else {
            set_flash_message('Failed to delete family relationship. Please try again.', 'danger');
        }

        redirect('children-ministry/families');
    }









    /**
     * Get smart relationship suggestions based on existing data
     *
     * @return void
     */
    public function getSmartSuggestions() {
        header('Content-Type: application/json');

        // Auto-detection disabled - all family relationships are now manual only
        // This prevents automatic relationship suggestions based on surnames/addresses
        echo json_encode([
            'success' => false,
            'message' => 'Auto-detection has been disabled. Please use manual parent assignment instead.',
            'suggestions' => []
        ]);

        exit;
    }
}
