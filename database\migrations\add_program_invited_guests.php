<?php
/**
 * Migration script to add Program Invited Guests functionality
 * This creates the database table to store invited guests for church programs
 */

require_once __DIR__ . '/../../config/database.php';

echo "<h1>Adding Program Invited Guests Table</h1>";

try {
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Creating Program Invited Guests Table</h2>";
    
    // Create program_invited_guests table
    $sql_invited_guests = "CREATE TABLE IF NOT EXISTS program_invited_guests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        guest_name VARCHAR(100) NOT NULL,
        guest_role VARCHAR(100) NULL COMMENT 'Role/Title like Guest Speaker, Worship Leader, etc.',
        contact_email VARCHAR(100) NULL,
        contact_phone VARCHAR(20) NULL,
        organization VARCHAR(150) NULL COMMENT 'Guest home church or organization',
        special_notes TEXT NULL COMMENT 'Special requirements, dietary restrictions, etc.',
        invitation_status ENUM('invited', 'confirmed', 'declined', 'attended', 'no_show') DEFAULT 'invited',
        invitation_sent_date DATETIME NULL,
        response_date DATETIME NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT NULL COMMENT 'User who added this guest',
        
        -- Foreign key constraints
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        
        -- Indexes for better performance
        INDEX idx_program_id (program_id),
        INDEX idx_guest_name (guest_name),
        INDEX idx_invitation_status (invitation_status),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores invited guests for church programs'";
    
    $stmt = $conn->prepare($sql_invited_guests);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Program invited guests table created successfully</p>";
    
    // Create program_guest_communications table for tracking communications
    echo "<h2>Creating Program Guest Communications Table</h2>";
    
    $sql_guest_communications = "CREATE TABLE IF NOT EXISTS program_guest_communications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        guest_id INT NOT NULL,
        communication_type ENUM('email', 'phone', 'sms', 'letter', 'in_person') NOT NULL,
        subject VARCHAR(200) NULL,
        message TEXT NULL,
        sent_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        sent_by INT NULL COMMENT 'User who sent the communication',
        response_received BOOLEAN DEFAULT FALSE,
        response_date DATETIME NULL,
        response_notes TEXT NULL,
        
        -- Foreign key constraints
        FOREIGN KEY (guest_id) REFERENCES program_invited_guests(id) ON DELETE CASCADE,
        FOREIGN KEY (sent_by) REFERENCES users(id) ON DELETE SET NULL,
        
        -- Indexes
        INDEX idx_guest_id (guest_id),
        INDEX idx_communication_type (communication_type),
        INDEX idx_sent_date (sent_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tracks communications with invited guests'";
    
    $stmt = $conn->prepare($sql_guest_communications);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Program guest communications table created successfully</p>";
    
    // Create program_guest_requirements table for special requirements
    echo "<h2>Creating Program Guest Requirements Table</h2>";
    
    $sql_guest_requirements = "CREATE TABLE IF NOT EXISTS program_guest_requirements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        guest_id INT NOT NULL,
        requirement_type ENUM('accommodation', 'transportation', 'dietary', 'technical', 'accessibility', 'other') NOT NULL,
        requirement_description TEXT NOT NULL,
        status ENUM('requested', 'approved', 'arranged', 'completed', 'cancelled') DEFAULT 'requested',
        assigned_to INT NULL COMMENT 'User responsible for fulfilling this requirement',
        cost_estimate DECIMAL(10,2) NULL,
        actual_cost DECIMAL(10,2) NULL,
        notes TEXT NULL,
        due_date DATE NULL,
        completed_date DATETIME NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- Foreign key constraints
        FOREIGN KEY (guest_id) REFERENCES program_invited_guests(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
        
        -- Indexes
        INDEX idx_guest_id (guest_id),
        INDEX idx_requirement_type (requirement_type),
        INDEX idx_status (status),
        INDEX idx_due_date (due_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tracks special requirements for invited guests'";
    
    $stmt = $conn->prepare($sql_guest_requirements);
    $stmt->execute();
    echo "<p style='color: green;'>✓ Program guest requirements table created successfully</p>";
    
    echo "<h2>Migration Completed Successfully!</h2>";
    echo "<p style='color: blue;'>All tables for Program Invited Guests have been created:</p>";
    echo "<ul>";
    echo "<li>✓ program_invited_guests - Main table for storing guest information</li>";
    echo "<li>✓ program_guest_communications - Track all communications with guests</li>";
    echo "<li>✓ program_guest_requirements - Manage special requirements and logistics</li>";
    echo "</ul>";
    
    echo "<h3>Table Features:</h3>";
    echo "<ul>";
    echo "<li><strong>Guest Management:</strong> Complete guest information with contact details</li>";
    echo "<li><strong>Role Tracking:</strong> Track guest roles (Speaker, Worship Leader, etc.)</li>";
    echo "<li><strong>Invitation Status:</strong> Track invitation lifecycle (invited → confirmed → attended)</li>";
    echo "<li><strong>Communication Log:</strong> Record all communications with guests</li>";
    echo "<li><strong>Requirements Management:</strong> Handle accommodation, transportation, dietary needs</li>";
    echo "<li><strong>Cost Tracking:</strong> Track estimated and actual costs for guest requirements</li>";
    echo "<li><strong>Audit Trail:</strong> Full audit trail with created/updated timestamps</li>";
    echo "<li><strong>User Tracking:</strong> Track who added guests and handled communications</li>";
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Update the ProgramController to handle guest data when creating programs</li>";
    echo "<li>Create a ProgramGuest model to manage guest operations</li>";
    echo "<li>Add guest management features to the program show/edit pages</li>";
    echo "<li>Implement guest communication and requirement tracking</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}
?>
