<?php // No header needed as it's included in the layout ?>

<div class="container mx-auto px-4 py-6 max-w-7xl">
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-purple-600 to-purple-500 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-white p-2 rounded-full mr-3">
                        <i class="fas fa-history text-purple-500 text-xl"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-white">Maintenance Log</h1>
                </div>
                <a href="<?php echo BASE_URL; ?>settings#database/system-maintenance" class="bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back to System Maintenance
                </a>
            </div>
        </div>

        <div class="p-6">
            <?php if (empty($logEntries)): ?>
            <div class="text-center py-8">
                <div class="bg-purple-100 text-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-history text-2xl"></i>
                </div>
                <h2 class="text-xl font-bold text-gray-800 mb-2">No Maintenance Log Entries</h2>
                <p class="text-gray-600 max-w-lg mx-auto">
                    No maintenance operations have been logged yet. Run database optimization or archiving to create log entries.
                </p>
            </div>
            <?php else: ?>
            <!-- Log Entries Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Date & Time</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Operation</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Details</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Status</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Executed By</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Metrics</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($logEntries as $index => $entry): ?>
                        <tr class="<?php echo $index % 2 === 0 ? 'bg-white' : 'bg-gray-50'; ?> hover:bg-purple-50 transition-colors duration-150">
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo date('M d, Y', strtotime($entry['executed_at'])); ?></div>
                                <div class="text-xs text-gray-500"><?php echo date('h:i A', strtotime($entry['executed_at'])); ?></div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <?php
                                $operationIcon = '';
                                $operationClass = '';

                                switch ($entry['operation_type']) {
                                    case 'optimization':
                                        $operationIcon = 'fa-sync-alt';
                                        $operationClass = 'text-blue-600';
                                        break;
                                    case 'archiving':
                                        $operationIcon = 'fa-archive';
                                        $operationClass = 'text-amber-600';
                                        break;
                                    case 'backup':
                                        $operationIcon = 'fa-download';
                                        $operationClass = 'text-green-600';
                                        break;
                                    case 'restore':
                                        $operationIcon = 'fa-upload';
                                        $operationClass = 'text-red-600';
                                        break;
                                    default:
                                        $operationIcon = 'fa-cog';
                                        $operationClass = 'text-gray-600';
                                }
                                ?>
                                <div class="flex items-center">
                                    <div class="mr-2">
                                        <i class="fas <?php echo $operationIcon; ?> <?php echo $operationClass; ?>"></i>
                                    </div>
                                    <div>
                                        <?php echo ucfirst($entry['operation_type']); ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-sm text-gray-900 max-w-xs truncate" title="<?php echo htmlspecialchars($entry['operation_details']); ?>">
                                    <?php echo htmlspecialchars($entry['operation_details']); ?>
                                </div>
                                <?php if (!empty($entry['affected_tables'])): ?>
                                <div class="text-xs text-gray-500 mt-1">
                                    Tables: <?php echo htmlspecialchars($entry['affected_tables']); ?>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <?php if ($entry['status'] === 'success'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i> Success
                                </span>
                                <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i> Error
                                </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <?php echo !empty($entry['executed_by_name']) ? htmlspecialchars($entry['executed_by_name']) : 'System'; ?>
                                </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-xs text-gray-500">
                                    <?php if ($entry['affected_records'] > 0): ?>
                                    <div class="mb-1">Records: <?php echo number_format($entry['affected_records']); ?></div>
                                    <?php endif; ?>

                                    <?php if ($entry['duration_seconds'] > 0): ?>
                                    <div class="mb-1">Duration: <?php echo number_format($entry['duration_seconds'], 2); ?>s</div>
                                    <?php endif; ?>

                                    <?php if ($entry['size_change_mb'] > 0): ?>
                                    <div>Size Change: <?php echo number_format($entry['size_change_mb'], 2); ?> MB</div>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium"><?php echo ($page - 1) * $limit + 1; ?></span> to <span class="font-medium"><?php echo min($page * $limit, $totalCount); ?></span> of <span class="font-medium"><?php echo $totalCount; ?></span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <!-- Previous Page Link -->
                                <?php if ($page > 1): ?>
                                    <a href="<?php echo BASE_URL; ?>settings/maintenance-log?page=<?php echo $page - 1; ?>&limit=<?php echo $limit; ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </span>
                                <?php endif; ?>

                                <!-- Page Numbers -->
                                <?php
                                $startPage = max(1, $page - 2);
                                $endPage = min($totalPages, $startPage + 4);
                                if ($endPage - $startPage < 4 && $totalPages > 5) {
                                    $startPage = max(1, $endPage - 4);
                                }

                                for ($i = $startPage; $i <= $endPage; $i++): ?>
                                    <?php if ($i == $page): ?>
                                        <span class="relative inline-flex items-center px-4 py-2 border border-purple-500 bg-purple-50 text-sm font-medium text-purple-700">
                                            <?php echo $i; ?>
                                        </span>
                                    <?php else: ?>
                                        <a href="<?php echo BASE_URL; ?>settings/maintenance-log?page=<?php echo $i; ?>&limit=<?php echo $limit; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endif; ?>
                                <?php endfor; ?>

                                <!-- Next Page Link -->
                                <?php if ($page < $totalPages): ?>
                                    <a href="<?php echo BASE_URL; ?>settings/maintenance-log?page=<?php echo $page + 1; ?>&limit=<?php echo $limit; ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </span>
                                <?php endif; ?>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php // No footer needed as it's included in the layout ?>
