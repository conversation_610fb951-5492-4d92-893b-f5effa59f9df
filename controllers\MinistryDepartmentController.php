<?php
/**
 * Ministry Department Controller for Ministry Departments Management
 */

require_once 'models/MinistryDepartment.php';
require_once 'models/Member.php';
require_once 'utils/validation.php';
require_once 'controllers/BaseRestfulController.php';

class MinistryDepartmentController extends BaseRestfulController {
    private $database;
    private $department;
    private $member;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $db = $this->database->getConnection();
        $this->department = new MinistryDepartment($db);
        $this->member = new Member($db);
    }

    /**
     * Display departments list
     */
    public function index() {
        $stmt = $this->department->getAll();
        $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $page_title = 'Ministry Departments - ICGC Emmanuel Temple';
        $active_page = 'programs';

        ob_start();
        require_once 'views/ministry_departments/index.php';
        $content = ob_get_clean();
        include 'views/layouts/main.php';
    }

    /**
     * Display create department form
     */
    public function create() {
        $members = $this->member->getAll()->fetchAll(PDO::FETCH_ASSOC);

        $page_title = 'Create Ministry Department - ICGC Emmanuel Temple';
        $active_page = 'programs';

        ob_start();
        require_once 'views/ministry_departments/create.php';
        $content = ob_get_clean();
        include 'views/layouts/main.php';
    }

    /**
     * Store new department
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('ministry-departments');
        }

        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            set_flash_message('Invalid security token. Please try again.', 'danger');
            redirect('ministry-departments/create');
        }

        $this->department->name = $_POST['name'] ?? '';
        $this->department->description = $_POST['description'] ?? '';
        $this->department->head_pastor_id = !empty($_POST['head_pastor_id']) ? $_POST['head_pastor_id'] : null;
        $this->department->contact_email = $_POST['contact_email'] ?? '';
        $this->department->contact_phone = $_POST['contact_phone'] ?? '';
        $this->department->budget_allocation = !empty($_POST['budget_allocation']) ? $_POST['budget_allocation'] : 0.00;

        $errors = $this->department->validate();

        if (!empty($errors)) {
            set_flash_message('Please fix the following errors: ' . implode(', ', $errors), 'danger');
            redirect('ministry-departments/create');
        }

        if ($this->department->create()) {
            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Ministry department created successfully!']);
                exit;
            }

            set_flash_message('Ministry department created successfully!', 'success');
            redirect('ministry-departments');
        } else {
            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Error creating ministry department. Please try again.']);
                exit;
            }

            set_flash_message('Error creating ministry department. Please try again.', 'danger');
            redirect('ministry-departments/create');
        }
    }

    /**
     * Display edit department form
     */
    public function edit() {
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Department ID is required.', 'danger');
            redirect('ministry-departments');
        }

        $department_id = (int)$_GET['id'];
        $department = $this->department->getById($department_id);

        if (!$department) {
            set_flash_message('Department not found.', 'danger');
            redirect('ministry-departments');
        }

        $members = $this->member->getAll()->fetchAll(PDO::FETCH_ASSOC);

        $page_title = 'Edit Department: ' . $department['name'] . ' - ICGC Emmanuel Temple';
        $active_page = 'programs';

        ob_start();
        require_once 'views/ministry_departments/edit.php';
        $content = ob_get_clean();
        include 'views/layouts/main.php';
    }

    /**
     * Show single department (RESTful)
     *
     * @param int|null $id Department ID from route parameter
     * @return void
     */
    public function show($id = null) {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        $department_id = $this->getId($id, 'id', 'id');
        if (!$department_id) {
            $this->handleResponse(false, 'Invalid department ID', 'ministry-departments');
            return;
        }

        $department = $this->department->getById($department_id);
        if (!$department) {
            $this->handleResponse(false, 'Department not found', 'ministry-departments');
            return;
        }

        // For AJAX requests, return JSON
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'data' => $department]);
            exit;
        }

        $active_page = 'ministry-departments';
        $page_title = 'View Department';

        require_once 'views/ministry-departments/show.php';
    }

    /**
     * Update department
     */
    public function update($id = null) {
        // Support both RESTful (PUT) and legacy (POST) methods
        if (!$this->checkHttpMethod(['POST', 'PUT'])) {
            redirect('ministry-departments');
            return;
        }

        if (!$this->validateCsrf('ministry-departments')) {
            return;
        }

        // Get ID from multiple sources (supports both legacy and RESTful)
        $department_id = $this->getId($id, 'id', 'id');
        if (!$department_id) {
            $this->handleResponse(false, 'Department ID is required', 'ministry-departments');
            return;
        }

        $department_id = (int)$_POST['id'];

        // Get the existing department to preserve is_active status
        $existingDepartment = $this->department->getById($department_id);
        if (!$existingDepartment) {
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Department not found.']);
                exit;
            }
            set_flash_message('Department not found.', 'danger');
            redirect('ministry-departments');
        }

        $this->department->id = $department_id;
        $this->department->name = $_POST['name'] ?? '';
        $this->department->description = $_POST['description'] ?? '';
        $this->department->head_pastor_id = !empty($_POST['head_pastor_id']) ? $_POST['head_pastor_id'] : null;
        $this->department->contact_email = $_POST['contact_email'] ?? '';
        $this->department->contact_phone = $_POST['contact_phone'] ?? '';
        $this->department->budget_allocation = !empty($_POST['budget_allocation']) ? $_POST['budget_allocation'] : 0.00;
        // Preserve the existing is_active status
        $this->department->is_active = $existingDepartment['is_active'] ?? 1;

        $errors = $this->department->validate();

        if (!empty($errors)) {
            set_flash_message('Please fix the following errors: ' . implode(', ', $errors), 'danger');
            redirect('ministry-departments/edit?id=' . $department_id);
        }

        if ($this->department->update()) {
            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Department updated successfully!']);
                exit;
            }

            set_flash_message('Department updated successfully!', 'success');
            redirect('ministry-departments');
        } else {
            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Error updating department. Please try again.']);
                exit;
            }

            set_flash_message('Error updating department. Please try again.', 'danger');
            redirect('ministry-departments/edit?id=' . $department_id);
        }
    }

    /**
     * Delete department
     */
    public function delete($id = null) {
        // Support both RESTful (DELETE) and legacy (POST) methods
        if (!$this->checkHttpMethod(['POST', 'DELETE'])) {
            redirect('ministry-departments');
            return;
        }

        if (!$this->validateCsrf('ministry-departments')) {
            return;
        }

        // Get ID from multiple sources (supports both legacy and RESTful)
        $department_id = $this->getId($id, 'id', 'id');
        if (!$department_id) {
            $this->handleResponse(false, 'Department ID is required', 'ministry-departments');
            return;
        }

        $department_id = (int)$_POST['id'];

        $this->department->id = $department_id;

        if ($this->department->delete()) {
            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Department deleted successfully!']);
                exit;
            }

            set_flash_message('Department deleted successfully!', 'success');
        } else {
            // Get error message from model
            $errorMessage = $this->department->error ?? 'Error deleting department. Please try again.';

            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $errorMessage]);
                exit;
            }

            set_flash_message($errorMessage, 'danger');
        }

        redirect('ministry-departments');
    }

    /**
     * Toggle department status
     */
    public function toggleStatus() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('ministry-departments');
        }

        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            set_flash_message('Invalid security token. Please try again.', 'danger');
            redirect('ministry-departments');
        }

        $department_id = (int)($_POST['department_id'] ?? 0);
        $new_status = (int)($_POST['status'] ?? 0);

        if (empty($department_id)) {
            set_flash_message('Department ID is required.', 'danger');
            redirect('ministry-departments');
        }

        $this->department->id = $department_id;

        if ($this->department->toggleStatus($new_status)) {
            $status_text = $new_status ? 'activated' : 'deactivated';
            set_flash_message('Department ' . $status_text . ' successfully!', 'success');
        } else {
            set_flash_message('Error updating department status. Please try again.', 'danger');
        }

        redirect('ministry-departments');
    }

    /**
     * AJAX endpoint to get departments
     */
    public function getDepartments() {
        header('Content-Type: application/json');
        
        $stmt = $this->department->getActive();
        $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode($departments);
        exit;
    }
}
