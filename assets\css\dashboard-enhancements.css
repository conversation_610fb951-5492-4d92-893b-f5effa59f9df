/*
 * ICGC <PERSON> Temple Dashboard Enhancements
 * Enhanced features for Phase 1 implementation
 */

/* Notification Styles */
.notification {
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.notification-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.notification-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.notification-error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.notification-info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Notification Center */
#notification-center {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

#notification-center::-webkit-scrollbar {
    width: 4px;
}

#notification-center::-webkit-scrollbar-track {
    background: #f1f1f1;
}

#notification-center::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

/* Enhanced Card Animations */
.gradient-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gradient-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Mobile Enhancements */
@media (max-width: 768px) {
    .gradient-card {
        padding: 1.25rem;
        margin-bottom: 1rem;
    }
    
    .gradient-card .card-value {
        font-size: 1.75rem;
    }
    
    .gradient-card .card-title {
        font-size: 0.75rem;
    }
    
    /* Mobile-specific card layout */
    .mobile-card-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    /* Touch-friendly buttons */
    .quick-action-btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* Mobile floating action button */
    #mobile-fab {
        animation: fadeInUp 0.5s ease-out;
    }
    
    #mobile-fab button {
        transition: all 0.3s ease;
    }
    
    #mobile-fab button:active {
        transform: scale(0.95);
    }
    
    #mobile-menu {
        animation: slideUp 0.3s ease-out;
    }
}

/* Tablet Enhancements */
@media (min-width: 769px) and (max-width: 1024px) {
    .gradient-card {
        padding: 1.5rem;
    }
    
    .gradient-card .card-value {
        font-size: 2rem;
    }
}

/* Enhanced Chart Containers */
.chart-container {
    position: relative;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 2rem;
    margin-bottom: 2rem;
    border: none;
    transition: all 0.3s ease;
}

.chart-container:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.chart-container h3 {
    color: #1f2937;
    font-weight: 600;
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 0.75rem;
}

.chart-container h3 i {
    color: #3F7D58;
    font-size: 1rem;
    margin-right: 0.75rem;
}

/* Clean Chart Header Design */
.chart-container .flex.justify-between.items-center {
    border-bottom: 1px solid #f3f4f6;
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
}

/* Professional Stats Cards */
.chart-container .grid > div {
    background: #f8fafc;
    border: none;
    border-radius: 0.75rem;
    padding: 1.25rem;
    text-align: center;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin: 0.25rem;
}

.chart-container .grid > div:hover {
    background: #f1f5f9;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* Clean Typography for Stats */
.chart-container .grid > div .text-xl,
.chart-container .grid > div .text-lg,
.chart-container .grid > div .text-sm {
    font-weight: 600;
}

.chart-container .grid > div .text-xs {
    color: #6b7280;
    font-weight: 400;
}

.chart-container .chart-controls {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.chart-container .chart-controls select {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background: white;
    color: #374151;
    font-weight: 500;
    transition: all 0.2s ease;
}

.chart-container .chart-controls select:focus {
    outline: none;
    border-color: #3F7D58;
    box-shadow: 0 0 0 3px rgba(63, 125, 88, 0.1);
}

/* Chart Canvas Styling */
.chart-container canvas {
    background: transparent !important;
}

/* Clean Chart Area */
.chart-container .h-64,
.chart-container .h-80 {
    background: transparent;
    border-radius: 0.5rem;
    position: relative;
    margin: 1rem 0;
    padding: 0.5rem;
}

/* Professional Color Scheme Override */
.bg-blue-50 {
    background-color: #eff6ff !important;
    border: none !important;
}

.bg-pink-50 {
    background-color: #fdf2f8 !important;
    border: none !important;
}

.bg-green-50 {
    background-color: #f0fdf4 !important;
    border: none !important;
}

.bg-purple-50 {
    background-color: #faf5ff !important;
    border: none !important;
}

.bg-orange-50 {
    background-color: #fff7ed !important;
    border: none !important;
}

.bg-gray-50 {
    background-color: #f9fafb !important;
    border: none !important;
}

/* Quick Search Styles */
#quick-search-overlay {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

#quick-search {
    transition: all 0.2s ease;
}

#quick-search:focus {
    border-color: #3F7D58;
    box-shadow: 0 0 0 3px rgba(63, 125, 88, 0.1);
}

#search-results a {
    transition: background-color 0.2s ease;
}

#search-results a:hover {
    background-color: #f9fafb;
}

/* Keyboard Shortcut Indicators */
kbd {
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    font-family: monospace;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3F7D58;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Enhanced Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.quick-action-item {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quick-action-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: #3F7D58;
}

.quick-action-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.quick-action-item:hover::before {
    left: 100%;
}

.quick-action-item i {
    font-size: 1.5rem;
    color: #3F7D58;
    margin-bottom: 0.5rem;
    display: block;
}

.quick-action-item span {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

/* Responsive Grid Adjustments */
@media (max-width: 640px) {
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .quick-action-item {
        padding: 0.75rem;
    }
    
    .quick-action-item i {
        font-size: 1.25rem;
    }
    
    .quick-action-item span {
        font-size: 0.75rem;
    }
}

/* Enhanced Stats Cards */
.stats-card-enhanced {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3F7D58;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card-enhanced::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(63, 125, 88, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.stats-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    .chart-container {
        background: #1f2937;
        border-color: #374151;
    }
    
    .chart-container h3 {
        color: #f9fafb;
    }
    
    #notification-center {
        background: #1f2937;
        border-color: #374151;
    }
    
    #quick-search-overlay .bg-white {
        background: #1f2937;
    }
    
    #quick-search {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
}

/* Demographics Chart Enhancements */
.chart-container .grid > div {
    transition: all 0.2s ease;
    cursor: default;
}

.chart-container .grid > div:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Remove any dark backgrounds */
.chart-container,
.chart-container * {
    background-color: transparent !important;
}

.chart-container {
    background-color: white !important;
}

/* Ensure clean white background for chart areas */
.chart-container .h-64,
.chart-container .h-80 {
    background-color: white !important;
    border: none !important;
}

/* Clean grid styling */
.chart-container .grid {
    gap: 0.75rem;
}

/* Professional text colors */
.chart-container .text-blue-600 {
    color: #2563eb !important;
}

.chart-container .text-pink-600 {
    color: #db2777 !important;
}

.chart-container .text-green-600 {
    color: #059669 !important;
}

.chart-container .text-purple-600 {
    color: #7c3aed !important;
}

.chart-container .text-orange-600 {
    color: #ea580c !important;
}

.chart-container .text-primary {
    color: #3F7D58 !important;
}

/* Chart Type Selector Styling */
.chart-controls select {
    transition: all 0.2s ease;
    cursor: pointer;
}

.chart-controls select:hover {
    border-color: #3F7D58;
    box-shadow: 0 0 0 2px rgba(63, 125, 88, 0.1);
}

/* Department Stats Cards */
.chart-container .grid .border {
    transition: all 0.3s ease;
    cursor: pointer;
}

.chart-container .grid .border:hover {
    border-color: #3F7D58;
    background-color: #f8fffe;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Chart Animation Classes */
.chart-loading {
    position: relative;
}

.chart-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3F7D58;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

/* Clean Modern Design */
.chart-container .flex.justify-between.items-center h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    letter-spacing: -0.025em;
}

.chart-container .text-sm.text-gray-600 {
    color: #6b7280 !important;
    font-weight: 500;
}

/* Modern Button Styling */
.chart-controls select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
}

/* Enhanced Two-Column Layout */
.dashboard-two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

@media (max-width: 1280px) {
    .dashboard-two-column {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

/* Smooth Page Load Animation */
.chart-container {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.chart-container:nth-child(1) { animation-delay: 0.1s; }
.chart-container:nth-child(2) { animation-delay: 0.2s; }
.chart-container:nth-child(3) { animation-delay: 0.3s; }
.chart-container:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Column Spacing */
.space-y-6 > .chart-container {
    margin-bottom: 0;
}

.space-y-6 > * + * {
    margin-top: 2.5rem;
}

.space-y-8 > * + * {
    margin-top: 3rem;
}

/* Improved Visual Balance */
.xl\\:grid-cols-2 > div {
    min-height: fit-content;
}

/* Enhanced Grid Spacing */
.grid.gap-6 {
    gap: 2rem;
}

.grid.gap-3 {
    gap: 1rem;
}

.grid.gap-4 {
    gap: 1.25rem;
}

/* Enhanced Chart Container Spacing */
.chart-container + .chart-container {
    margin-top: 0;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

/* Recent Activities Styling */
.activity-item {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    margin-bottom: 0.75rem;
    padding: 1rem !important;
    border-radius: 0.75rem !important;
}

.activity-item:hover {
    transform: translateX(2px);
    border-left-color: #3F7D58;
    background-color: rgba(63, 125, 88, 0.05);
}

.activity-icon {
    transition: all 0.2s ease;
    margin-right: 1rem !important;
}

.activity-item:hover .activity-icon {
    transform: scale(1.1);
}



/* Enhanced Grid Responsiveness */
@media (max-width: 1024px) {
    .grid.grid-cols-1.lg\\:grid-cols-2 {
        grid-template-columns: 1fr;
    }
}

/* Responsive Chart Adjustments */
@media (max-width: 768px) {
    .chart-container {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .chart-container h3 {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .chart-container .grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .chart-container .grid > div {
        padding: 1rem;
    }

    .chart-controls select {
        font-size: 0.75rem;
        padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    }

    .chart-container .flex.justify-between.items-center {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .space-y-6 > * + * {
        margin-top: 1.5rem;
    }

    .space-y-8 > * + * {
        margin-top: 2rem;
    }

    .grid.grid-cols-2.md\\:grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
    }

    .grid.gap-8 {
        gap: 1.5rem;
    }
}

/* Enhanced Visual Hierarchy */
.chart-container h3 {
    position: relative;
    padding-bottom: 0.5rem;
}

.chart-container h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 2rem;
    height: 2px;
    background: linear-gradient(90deg, #3F7D58, #D3E671);
    border-radius: 1px;
}

/* Improved Loading States */
.chart-container canvas {
    transition: opacity 0.3s ease;
}

.chart-container.loading canvas {
    opacity: 0.5;
}

.chart-container.loading::before {
    content: 'Loading...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #6b7280;
    font-size: 0.875rem;
    z-index: 10;
}

/* Enhanced Section Dividers */
.space-y-6 > .chart-container:not(:first-child) {
    position: relative;
}

.space-y-6 > .chart-container:not(:first-child)::before {
    content: '';
    position: absolute;
    top: -0.75rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
}

/* Improved Hover States for Interactive Elements */
.activity-item {
    cursor: pointer;
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
}

.activity-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(63, 125, 88, 0.1), transparent);
    transition: left 0.5s ease;
}

.activity-item:hover::before {
    left: 100%;
}



/* Enhanced Typography Hierarchy */
.chart-container h3 {
    font-weight: 600;
    letter-spacing: -0.025em;
    color: #1f2937;
}

.chart-container .text-sm.text-gray-600 {
    font-weight: 500;
    color: #6b7280;
}

/* Improved Mobile Experience */
@media (max-width: 640px) {
    .chart-container {
        padding: 1.25rem;
        margin-bottom: 1.25rem;
    }

    .activity-item {
        padding: 0.875rem !important;
        margin-bottom: 0.5rem;
    }

    .space-y-6 > * + * {
        margin-top: 1.25rem;
    }

    .space-y-8 > * + * {
        margin-top: 1.5rem;
    }
}

@media (max-width: 640px) {
    .chart-container .grid.grid-cols-2 {
        grid-template-columns: 1fr;
    }

    .chart-container .grid.md\\:grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
    }

    .chart-container .grid.lg\\:grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
    }
}
