<?php
/**
 * Application Routes
 *
 * This file defines the routing for the application. The router in public/index.php
 * will use this array to dispatch requests to the correct controller and action.
 *
 * Format: [HTTP_METHOD, URI_PATTERN, CONTROLLER_CLASS, ACTION_METHOD, OPTIONS]
 * - URI_PATTERN is a regex pattern. Use capture groups for parameters.
 * - Parameters from capture groups are passed as arguments to the controller method
 * - OPTIONS is an optional array for route metadata:
 *   - ['auth' => false] makes the route public (no authentication required)
 *   - Routes without OPTIONS or with ['auth' => true] require authentication
 */

return [
    // HTTP Method, Route Pattern, Controller, Action

    // --- Authentication ---
    ['GET', '/^login$/', 'AuthController', 'loginPage', ['auth' => false]],
    ['POST', '/^auth\/login$/', 'AuthController', 'login', ['auth' => false, 'csrf' => true]],
    ['GET', '/^logout$/', 'AuthController', 'logout', ['auth' => false]],
    ['GET', '/^forgot-password$/', 'AuthController', 'forgotPasswordPage', ['auth' => false]],
    ['POST', '/^forgot-password$/', 'AuthController', 'forgotPassword', ['auth' => false, 'csrf' => true]],
    ['GET', '/^reset-password$/', 'AuthController', 'resetPasswordPage', ['auth' => false]],
    ['POST', '/^reset-password$/', 'AuthController', 'resetPassword', ['auth' => false, 'csrf' => true]],

    // --- Dashboard ---
    ['GET', '/^dashboard$/', 'DashboardController', 'index'],

    // --- Members (RESTful with Legacy Redirects) ---
    ['GET', '/^members$/', 'MemberController', 'index'],
    ['GET', '/^members\/(add|new-registration)$/', 'MemberController', 'create'],
    ['POST', '/^members\/store$/', 'MemberController', 'store', ['csrf' => true]],
    ['GET', '/^members\/(\d+)$/', 'MemberController', 'show'], // RESTful: /members/{id}
    ['GET', '/^members\/(\d+)\/edit$/', 'MemberController', 'edit'], // RESTful: /members/{id}/edit
    ['PUT', '/^members\/(\d+)$/', 'MemberController', 'update', ['csrf' => true]], // RESTful: PUT /members/{id}
    ['DELETE', '/^members\/(\d+)$/', 'MemberController', 'delete', ['role' => 'admin', 'csrf' => true]], // RESTful: DELETE /members/{id}
    ['POST', '/^members\/create-parent-for-child$/', 'MemberController', 'createParentForChild', ['role' => 'staff', 'csrf' => true]], // Create parent for standalone child
    ['GET', '/^members\/search$/', 'MemberController', 'searchMembers'],

    // Legacy Member Routes (Redirects)
    ['GET', '/^members\/view\/(\d+)$/', 'RedirectController', 'memberView'], // Legacy: /members/view/{id} → /members/{id}
    ['GET', '/^members\/view$/', 'RedirectController', 'memberViewQuery'], // Legacy: ?id= → /members/{id}
    ['GET', '/^members\/edit\/(\d+)$/', 'RedirectController', 'memberEdit'], // Legacy: /members/edit/{id} → /members/{id}/edit
    ['GET', '/^members\/edit$/', 'RedirectController', 'memberEditQuery'], // Legacy: ?id= → /members/{id}/edit
    ['POST', '/^members\/update$/', 'RedirectController', 'memberUpdate', ['csrf' => true]], // Legacy: POST /members/update → PUT /members/{id}
    ['POST', '/^members\/delete$/', 'RedirectController', 'memberDelete', ['csrf' => true]], // Legacy: POST /members/delete → DELETE /members/{id}

    // --- Attendance (Consolidated) ---
    ['GET', '/^attendance$/', 'AttendanceController', 'qrIndex'], // Main attendance page (QR-based)
    ['GET', '/^attendance\/date$/', 'AttendanceController', 'viewByDate'], // View by date
    ['GET', '/^attendance\/summary$/', 'AttendanceController', 'summary'], // Attendance summary
    ['GET', '/^attendance\/members\/(\d+)$/', 'AttendanceController', 'memberAttendance'], // Member attendance history
    ['GET', '/^attendance\/(\d+)\/edit$/', 'AttendanceController', 'edit'], // Edit attendance record
    ['PUT', '/^attendance\/(\d+)$/', 'AttendanceController', 'update', ['csrf' => true]], // Update attendance
    ['DELETE', '/^attendance\/(\d+)$/', 'AttendanceController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete attendance

    // Legacy Attendance Routes (Redirects)
    ['GET', '/^attendance\/add$/', 'RedirectController', 'attendanceAdd'], // Legacy: /attendance/add → /attendance/qr
    ['GET', '/^attendance\/bulk$/', 'RedirectController', 'attendanceAdd'], // Legacy: /attendance/bulk → /attendance/qr
    ['GET', '/^attendance\/view-by-date$/', 'RedirectController', 'attendanceViewByDate'], // Legacy redirect
    ['GET', '/^attendance\/view-all$/', 'AttendanceController', 'viewAll'], // Keep for now
    ['GET', '/^attendance\/member$/', 'RedirectController', 'attendanceMember'], // Legacy: ?id= → /attendance/members/{id}
    ['GET', '/^attendance\/edit$/', 'RedirectController', 'attendanceEdit'], // Legacy: ?id= → /attendance/{id}/edit
    ['POST', '/^attendance\/update$/', 'RedirectController', 'attendanceUpdate', ['csrf' => true]], // Legacy: POST → PUT

    // --- QR Code Attendance ---
    // Admin/Staff routes (require authentication)
    ['GET', '/^attendance\/qr$/', 'AttendanceController', 'qrIndex'],
    ['POST', '/^attendance\/qr-generate$/', 'AttendanceController', 'qrGenerate', ['csrf' => true]],
    ['GET', '/^attendance\/qr-display$/', 'AttendanceController', 'qrDisplay'],

    // Public QR routes (use token-based authentication)
    ['GET', '/^attendance\/qr-scan$/', 'AttendanceController', 'qrScan', ['token_auth' => true]],
    ['POST', '/^attendance\/qr-mark$/', 'AttendanceController', 'qrMark', ['token_auth' => true, 'csrf' => true]],
    ['POST', '/^attendance\/qr-get-family$/', 'AttendanceController', 'qrGetFamily', ['token_auth' => true, 'csrf' => true]],
    ['POST', '/^attendance\/qr-mark-family$/', 'AttendanceController', 'qrMarkFamily', ['token_auth' => true, 'csrf' => true]],
    ['GET', '/^attendance\/qr-stats$/', 'AttendanceController', 'qrStats', ['token_auth' => true]],
    ['GET', '/^attendance\/stats-dashboard$/', 'AttendanceController', 'attendanceStats', ['token_auth' => true]],
    ['GET', '/^attendance\/qr-session-manage$/', 'AttendanceController', 'qrSessionManage'],
    ['GET', '/^attendance\/qr-session-export$/', 'AttendanceController', 'qrSessionExport'],
    ['GET', '/^attendance\/qr-session-details$/', 'AttendanceController', 'qrSessionDetails'],
    ['GET', '/^attendance\/qr-sessions-archive$/', 'AttendanceController', 'qrSessionsArchive'],
    ['GET', '/^attendance\/qr-analytics$/', 'AttendanceController', 'qrAnalyticsDashboard'],
    ['GET', '/^attendance\/qr-analytics-export$/', 'AttendanceController', 'qrAnalyticsExport'],
    ['GET', '/^attendance\/qr-analytics-report$/', 'AttendanceController', 'qrAnalyticsReport'],
    ['POST', '/^attendance\/update-qr-counts$/', 'AttendanceController', 'updateQrSessionCounts', ['csrf' => true]],
    ['GET', '/^attendance\/qr-export$/', 'AttendanceController', 'qrExportInterface'],
    ['POST', '/^attendance\/qr-advanced-export$/', 'AttendanceController', 'qrAdvancedExport', ['csrf' => true]],
    ['GET', '/^attendance\/service-manage$/', 'AttendanceController', 'serviceManage'],

    // --- Services (RESTful) ---
    ['GET', '/^services$/', 'ServiceController', 'index'], // List all services
    ['GET', '/^services\/create$/', 'ServiceController', 'create', ['role' => 'admin']], // Show form to create service
    ['POST', '/^services$/', 'ServiceController', 'store', ['role' => 'admin', 'csrf' => true]], // Create service
    ['GET', '/^services\/(\d+)$/', 'ServiceController', 'show'], // Show service details
    ['GET', '/^services\/(\d+)\/edit$/', 'ServiceController', 'edit', ['role' => 'admin']], // Edit service form
    ['PUT', '/^services\/(\d+)$/', 'ServiceController', 'update', ['role' => 'admin', 'csrf' => true]], // Update service
    ['DELETE', '/^services\/(\d+)$/', 'ServiceController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete service

    // Legacy Service Routes (Redirects)
    ['GET', '/^services\/add$/', 'RedirectController', 'serviceAdd'], // Legacy: /services/add → /services/create
    ['GET', '/^services\/edit\/(\d+)$/', 'RedirectController', 'serviceEdit'], // Legacy: /services/edit/{id} → /services/{id}/edit
    ['POST', '/^services\/store$/', 'RedirectController', 'serviceStore', ['csrf' => true]], // Legacy: POST /services/store → POST /services
    ['POST', '/^services\/update$/', 'RedirectController', 'serviceUpdate', ['csrf' => true]], // Legacy: POST /services/update → PUT /services/{id}
    ['POST', '/^services\/delete$/', 'RedirectController', 'serviceDelete', ['csrf' => true]], // Legacy: POST /services/delete → DELETE /services/{id}

    // --- Finance (Admin/Staff Only) ---
    ['GET', '/^finances$/', 'FinanceController', 'index', ['role' => 'staff']],
    ['GET', '/^finances\/add$/', 'FinanceController', 'create', ['role' => 'staff']],

    // Legacy Finance Routes (without 's') - Redirect to correct URLs
    ['GET', '/^finance$/', 'RedirectController', 'financeIndex'],
    ['GET', '/^finance\/add$/', 'RedirectController', 'financeAdd'],
    ['GET', '/^finance\/categories$/', 'RedirectController', 'financeCategories'],
    ['GET', '/^finance\/edit$/', 'RedirectController', 'financeEdit'],
    ['POST', '/^finance\/store$/', 'RedirectController', 'financeStore', ['csrf' => true]],
    ['POST', '/^finance\/update$/', 'RedirectController', 'financeUpdate', ['csrf' => true]],
    ['POST', '/^finance\/delete$/', 'RedirectController', 'financeDelete', ['csrf' => true]],

    // Legacy Finance Dashboard Routes (without 's') - Redirect to correct URLs
    ['GET', '/^finance\/dashboard\/tithe$/', 'RedirectController', 'financeDashboardTithe'],
    ['GET', '/^finance\/dashboard\/pledge$/', 'RedirectController', 'financeDashboardPledge'],
    ['GET', '/^finance\/dashboard\/pastor-application$/', 'RedirectController', 'financeDashboardPastorApp'],
    ['GET', '/^finance\/dashboard\/category$/', 'RedirectController', 'financeDashboardCategory'],
    ['GET', '/^finance\/member-tithe-history$/', 'RedirectController', 'financeMemberTitheHistory'],
    // RESTful Finance Operations
    ['POST', '/^finances$/', 'FinanceController', 'store', ['role' => 'staff', 'csrf' => true]], // Create transaction (RESTful)
    ['GET', '/^finances\/(\d+)$/', 'FinanceController', 'show', ['role' => 'staff']], // Show single transaction (RESTful)
    ['GET', '/^finances\/(\d+)\/edit$/', 'FinanceController', 'edit', ['role' => 'staff']], // Show edit form (RESTful)
    ['PUT', '/^finances\/(\d+)$/', 'FinanceController', 'update', ['role' => 'staff', 'csrf' => true]], // Update transaction (RESTful)
    ['DELETE', '/^finances\/(\d+)$/', 'FinanceController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete transaction (RESTful)

    // Legacy Finance Routes (for backward compatibility)
    ['GET', '/^finances\/add-payment-type$/', 'FinanceController', 'addPaymentType', ['role' => 'admin']], // Legacy
    ['POST', '/^finances\/store$/', 'FinanceController', 'store', ['role' => 'staff', 'csrf' => true]], // Legacy
    ['GET', '/^finances\/edit$/', 'FinanceController', 'edit', ['role' => 'staff']], // Legacy (uses query params)
    ['POST', '/^finances\/update$/', 'FinanceController', 'update', ['role' => 'staff', 'csrf' => true]], // Legacy
    ['POST', '/^finances\/delete$/', 'FinanceController', 'delete', ['role' => 'admin', 'csrf' => true]], // Legacy
    ['GET', '/^finances\/report$/', 'FinanceController', 'report', ['role' => 'staff']],
    ['GET', '/^finances\/filter$/', 'FinanceController', 'filter', ['role' => 'staff']],
    ['GET', '/^finances\/tithes$/', 'FinanceController', 'tithes', ['role' => 'staff']],
    ['GET', '/^finances\/memberTithes$/', 'FinanceController', 'memberTithes', ['role' => 'staff']],
    ['GET', '/^finances\/transactions$/', 'FinanceController', 'transactions', ['role' => 'staff']],
    ['GET', '/^finances\/welfare$/', 'FinanceController', 'welfare', ['role' => 'staff']],
    ['GET', '/^finances\/memberWelfare$/', 'FinanceController', 'memberWelfare', ['role' => 'staff']],
    ['GET', '/^finances\/archived$/', 'FinanceController', 'archived', ['role' => 'admin']],
    ['POST', '/^finances\/archive-old$/', 'FinanceController', 'archiveOld', ['role' => 'admin', 'csrf' => true]],
    ['GET', '/^finances\/maintenance$/', 'FinanceController', 'maintenance', ['role' => 'admin']],
    ['GET', '/^finances\/enhanced-reports$/', 'FinanceController', 'enhancedReports', ['role' => 'staff']],
    ['GET', '/^finances\/migration-status$/', 'FinanceController', 'migrationStatus', ['role' => 'admin']],
    ['POST', '/^finances\/run-migration$/', 'FinanceController', 'runMigration', ['role' => 'admin', 'csrf' => true]],

    // --- Departments ---
    ['GET', '/^departments$/', 'DepartmentController', 'index'],
    ['GET', '/^departments\/view$/', 'DepartmentController', 'view'],

    // --- Equipment (RESTful) ---
    ['GET', '/^equipment$/', 'EquipmentController', 'index'], // List all equipment
    ['GET', '/^equipment\/create$/', 'EquipmentController', 'create', ['role' => 'staff']], // Show form to create equipment
    ['POST', '/^equipment$/', 'EquipmentController', 'store', ['role' => 'staff', 'csrf' => true]], // Create equipment
    ['GET', '/^equipment\/(\d+)$/', 'EquipmentController', 'show'], // Show equipment details
    ['GET', '/^equipment\/(\d+)\/edit$/', 'EquipmentController', 'edit', ['role' => 'staff']], // Edit equipment form
    ['PUT', '/^equipment\/(\d+)$/', 'EquipmentController', 'update', ['role' => 'staff', 'csrf' => true]], // Update equipment
    ['DELETE', '/^equipment\/(\d+)$/', 'EquipmentController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete equipment
    ['GET', '/^equipment\/maintenance$/', 'EquipmentController', 'maintenance', ['role' => 'staff']], // Maintenance dashboard
    ['GET', '/^equipment\/(\d+)\/qrcode$/', 'EquipmentController', 'generateQrCode'], // Generate QR code for equipment
    ['GET', '/^equipment\/inventory$/', 'EquipmentController', 'inventory'], // Inventory management
    ['GET', '/^equipment\/reports$/', 'EquipmentController', 'reports'], // Equipment reports

    // Legacy Equipment Routes (Redirects)
    ['GET', '/^equipment\/add$/', 'RedirectController', 'equipmentAdd'], // Legacy: /equipment/add → /equipment/create
    ['GET', '/^equipment\/view$/', 'RedirectController', 'equipmentView'], // Legacy: /equipment/view?id= → /equipment/{id}
    ['GET', '/^equipment\/edit$/', 'RedirectController', 'equipmentEdit'], // Legacy: /equipment/edit?id= → /equipment/{id}/edit
    ['POST', '/^equipment\/store$/', 'RedirectController', 'equipmentStore', ['csrf' => true]], // Legacy: POST /equipment/store → POST /equipment
    ['POST', '/^equipment\/update$/', 'RedirectController', 'equipmentUpdate', ['csrf' => true]], // Legacy: POST /equipment/update → PUT /equipment/{id}
    ['POST', '/^equipment\/delete$/', 'RedirectController', 'equipmentDelete', ['csrf' => true]], // Legacy: POST /equipment/delete → DELETE /equipment/{id}
    ['GET', '/^equipment\/qrcode$/', 'RedirectController', 'equipmentQrCode'], // Legacy: /equipment/qrcode?id= → /equipment/{id}/qrcode

    // --- SMS (RESTful) ---
    ['GET', '/^sms$/', 'SmsController', 'index'], // SMS dashboard
    ['GET', '/^sms\/create$/', 'SmsController', 'create'], // Create SMS form
    ['POST', '/^sms$/', 'SmsController', 'send', ['csrf' => true]], // Send SMS
    ['GET', '/^sms\/messages$/', 'SmsController', 'messages'], // List all messages
    ['GET', '/^sms\/messages\/(\d+)$/', 'SmsController', 'view'], // View message details
    ['DELETE', '/^sms\/messages\/(\d+)$/', 'SmsController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete message
    ['GET', '/^sms\/filter$/', 'SmsController', 'filter'], // Filter messages

    // Legacy SMS Routes (Redirects)
    ['POST', '/^sms\/send$/', 'RedirectController', 'smsSend', ['csrf' => true]], // Legacy: POST /sms/send → POST /sms
    ['GET', '/^sms\/view$/', 'RedirectController', 'smsView'], // Legacy: /sms/view?id= → /sms/messages/{id}
    ['POST', '/^sms\/delete$/', 'RedirectController', 'smsDelete', ['csrf' => true]], // Legacy: POST /sms/delete → DELETE /sms/messages/{id}

    // --- Reports ---
    ['GET', '/^reports$/', 'ReportController', 'index'],
    ['POST', '/^reports\/generate$/', 'ReportController', 'generate', ['csrf' => true]],
    ['GET', '/^reports\/members\/new$/', 'ReportController', 'newMembersReport'],
    ['GET', '/^reports\/members\/birthdays$/', 'ReportController', 'birthdaysReport'],
    ['GET', '/^reports\/members\/department$/', 'ReportController', 'departmentReport'],
    ['GET', '/^reports\/members\/status$/', 'ReportController', 'statusReport'],

    // --- Users (Admin Only) - RESTful Implementation ---
    ['GET', '/^users$/', 'UserController', 'index', ['role' => 'admin']], // List all users
    ['GET', '/^users\/create$/', 'UserController', 'create', ['role' => 'admin']], // Show create form
    ['POST', '/^users$/', 'UserController', 'store', ['role' => 'admin', 'csrf' => true]], // Create new user (RESTful)
    ['GET', '/^users\/(\d+)$/', 'UserController', 'show', ['role' => 'admin']], // Show single user (RESTful)
    ['GET', '/^users\/(\d+)\/edit$/', 'UserController', 'edit', ['role' => 'admin']], // Show edit form (RESTful)
    ['PUT', '/^users\/(\d+)$/', 'UserController', 'update', ['role' => 'admin', 'csrf' => true]], // Update user (RESTful)
    ['DELETE', '/^users\/(\d+)$/', 'UserController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete user (RESTful)
    ['PUT', '/^users\/(\d+)\/status$/', 'UserController', 'toggleStatus', ['role' => 'admin', 'csrf' => true]], // Toggle status (RESTful)

    // Legacy Users Routes (for backward compatibility)
    ['GET', '/^users\/add$/', 'RedirectController', 'usersAdd'], // Redirect to /users/create
    ['POST', '/^users\/store$/', 'UserController', 'store', ['role' => 'admin', 'csrf' => true]], // Legacy
    ['GET', '/^users\/edit$/', 'RedirectController', 'usersEdit'], // Redirect with ID handling
    ['POST', '/^users\/update$/', 'UserController', 'update', ['role' => 'admin', 'csrf' => true]], // Legacy
    ['POST', '/^users\/toggle-status$/', 'UserController', 'toggleStatus', ['role' => 'admin', 'csrf' => true]], // Legacy
    ['POST', '/^users\/delete$/', 'UserController', 'delete', ['role' => 'admin', 'csrf' => true]], // Legacy

    // --- Settings (Admin Only) - RESTful Implementation ---
    ['GET', '/^settings$/', 'SettingController', 'index', ['role' => 'admin']], // List all settings
    ['GET', '/^settings\/edit$/', 'SettingController', 'edit', ['role' => 'admin']], // Show edit form
    ['PUT', '/^settings$/', 'SettingController', 'update', ['role' => 'admin', 'csrf' => true]], // Update settings (RESTful)
    ['POST', '/^settings\/actions\/backup$/', 'SettingController', 'backup', ['role' => 'admin', 'csrf' => true]], // Backup action (RESTful)
    ['POST', '/^settings\/actions\/restore$/', 'SettingController', 'restore', ['role' => 'admin', 'csrf' => true]], // Restore action (RESTful)
    ['POST', '/^settings\/actions\/refresh-stats$/', 'SettingController', 'refreshStats', ['role' => 'admin', 'csrf' => true]], // Refresh stats (RESTful)
    ['POST', '/^settings\/actions\/optimize-database$/', 'SettingController', 'optimizeDatabase', ['role' => 'admin', 'csrf' => true]], // Optimize DB (RESTful)

    // Legacy Settings Routes (for backward compatibility)
    ['POST', '/^settings\/update$/', 'SettingController', 'update', ['role' => 'admin', 'csrf' => true]], // Legacy
    ['POST', '/^settings\/backup$/', 'SettingController', 'backup', ['role' => 'admin', 'csrf' => true]], // Legacy
    ['POST', '/^settings\/restore$/', 'SettingController', 'restore', ['role' => 'admin', 'csrf' => true]], // Legacy
    ['POST', '/^settings\/refresh-stats$/', 'SettingController', 'refreshStats', ['role' => 'admin', 'csrf' => true]], // Legacy
    ['POST', '/^settings\/optimize-database$/', 'SettingController', 'optimizeDatabase', ['role' => 'admin', 'csrf' => true]], // Legacy

    // --- Profile - RESTful Implementation ---
    ['GET', '/^profile$/', 'ProfileController', 'index'], // Show profile
    ['GET', '/^profile\/edit$/', 'ProfileController', 'edit'], // Show edit form
    ['PUT', '/^profile$/', 'ProfileController', 'updateProfile', ['csrf' => true]], // Update profile (RESTful)
    ['PUT', '/^profile\/settings$/', 'ProfileController', 'updateSettings', ['csrf' => true]], // Update settings (RESTful)
    ['DELETE', '/^profile\/logo$/', 'ProfileController', 'resetLogo', ['csrf' => true]], // Reset logo (RESTful)

    // Legacy Profile Routes (for backward compatibility)
    ['POST', '/^profile\/update$/', 'ProfileController', 'updateProfile', ['csrf' => true]], // Legacy
    ['POST', '/^profile\/update-settings$/', 'ProfileController', 'updateSettings', ['csrf' => true]], // Legacy
    ['POST', '/^profile\/reset-logo$/', 'ProfileController', 'resetLogo', ['csrf' => true]], // Legacy

    // --- Visitors (RESTful) ---
    ['GET', '/^visitors$/', 'VisitorController', 'index'], // List all visitors
    ['GET', '/^visitors\/create$/', 'VisitorController', 'create'], // Show form to create visitor
    ['POST', '/^visitors$/', 'VisitorController', 'store', ['csrf' => true]], // Create visitor
    ['GET', '/^visitors\/(\d+)$/', 'VisitorController', 'show'], // Show visitor details
    ['GET', '/^visitors\/(\d+)\/edit$/', 'VisitorController', 'edit'], // Edit visitor form
    ['PUT', '/^visitors\/(\d+)$/', 'VisitorController', 'update', ['csrf' => true]], // Update visitor
    ['DELETE', '/^visitors\/(\d+)$/', 'VisitorController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete visitor
    ['GET', '/^visitors\/(\d+)\/follow-up$/', 'VisitorController', 'followUp'], // Show follow-up form
    ['POST', '/^visitors\/(\d+)\/follow-up$/', 'VisitorController', 'storeFollowUp', ['csrf' => true]], // Create follow-up
    ['DELETE', '/^visitors\/(\d+)\/follow-up\/(\d+)$/', 'VisitorController', 'deleteFollowUp', ['csrf' => true]], // Delete follow-up
    ['GET', '/^visitors\/(\d+)\/convert$/', 'VisitorController', 'convert'], // Show conversion form
    ['POST', '/^visitors\/(\d+)\/convert$/', 'VisitorController', 'storeConversion', ['csrf' => true]], // Convert to member
    ['POST', '/^visitors\/(\d+)\/sms$/', 'VisitorController', 'sendSms', ['csrf' => true]], // Send SMS to visitor

    // Legacy Visitor Routes (Redirects)
    ['GET', '/^visitors\/view$/', 'RedirectController', 'visitorView'], // Legacy: /visitors/view?id= → /visitors/{id}
    ['GET', '/^visitors\/edit$/', 'RedirectController', 'visitorEdit'], // Legacy: /visitors/edit?id= → /visitors/{id}/edit
    ['POST', '/^visitors\/store$/', 'RedirectController', 'visitorStore', ['csrf' => true]], // Legacy: POST /visitors/store → POST /visitors
    ['POST', '/^visitors\/update$/', 'RedirectController', 'visitorUpdate', ['csrf' => true]], // Legacy: POST /visitors/update → PUT /visitors/{id}
    ['POST', '/^visitors\/delete$/', 'RedirectController', 'visitorDelete', ['csrf' => true]], // Legacy: POST /visitors/delete → DELETE /visitors/{id}
    ['GET', '/^visitors\/follow-up$/', 'RedirectController', 'visitorFollowUp'], // Legacy: /visitors/follow-up?id= → /visitors/{id}/follow-up
    ['POST', '/^visitors\/store-follow-up$/', 'RedirectController', 'visitorStoreFollowUp', ['csrf' => true]], // Legacy follow-up store
    ['POST', '/^visitors\/delete-follow-up$/', 'RedirectController', 'visitorDeleteFollowUp', ['csrf' => true]], // Legacy follow-up delete
    ['POST', '/^visitors\/convert$/', 'RedirectController', 'visitorConvert', ['csrf' => true]], // Legacy: /visitors/convert?id= → /visitors/{id}/convert
    ['POST', '/^visitors\/store-conversion$/', 'RedirectController', 'visitorStoreConversion', ['csrf' => true]], // Legacy conversion store
    ['POST', '/^visitors\/send-sms$/', 'RedirectController', 'visitorSendSms', ['csrf' => true]], // Legacy SMS send

    // --- Groups ---
    ['GET', '/^groups$/', 'GroupsController', 'index'],
    ['GET', '/^groups\/create$/', 'GroupsController', 'add'], // RESTful: /groups/create
    ['GET', '/^groups\/add$/', 'GroupsController', 'add'], // Legacy: /groups/add (same as create)
    ['POST', '/^groups$/', 'GroupsController', 'create', ['csrf' => true]], // RESTful: POST /groups
    ['GET', '/^groups\/(\d+)$/', 'GroupsController', 'show'], // RESTful: /groups/{id}
    ['GET', '/^groups\/(\d+)\/edit$/', 'GroupsController', 'edit'], // RESTful: /groups/{id}/edit
    ['GET', '/^groups\/edit\/(\d+)$/', 'GroupsController', 'edit'], // Legacy: /groups/edit/{id}
    ['PUT', '/^groups\/(\d+)$/', 'GroupsController', 'update', ['csrf' => true]], // RESTful: PUT /groups/{id}
    ['POST', '/^groups\/update\/(\d+)$/', 'GroupsController', 'update', ['csrf' => true]], // Legacy: POST /groups/update/{id}
    ['DELETE', '/^groups\/(\d+)$/', 'GroupsController', 'delete', ['role' => 'admin', 'csrf' => true]], // RESTful: DELETE /groups/{id}
    ['POST', '/^groups\/delete\/(\d+)$/', 'GroupsController', 'delete', ['role' => 'admin', 'csrf' => true]], // Legacy: POST /groups/delete/{id}
    ['GET', '/^groups\/members\/(\d+)$/', 'GroupsController', 'members'],
    ['GET', '/^groups\/add-members\/(\d+)$/', 'GroupsController', 'addMembers'],
    ['POST', '/^groups\/add-members-process\/(\d+)$/', 'GroupsController', 'addMembersProcess', ['csrf' => true]],
    ['DELETE', '/^groups\/(\d+)\/members\/(\d+)$/', 'GroupsController', 'removeMember', ['csrf' => true]], // RESTful: DELETE /groups/{id}/members/{member_id}
    ['POST', '/^groups\/remove-member\/(\d+)\/(\d+)$/', 'GroupsController', 'removeMember', ['csrf' => true]], // Legacy
    ['PUT', '/^groups\/(\d+)\/members\/(\d+)\/role$/', 'GroupsController', 'updateRole', ['csrf' => true]], // RESTful: PUT /groups/{id}/members/{member_id}/role
    ['POST', '/^groups\/update-role\/(\d+)\/(\d+)$/', 'GroupsController', 'updateRole', ['csrf' => true]], // Legacy

    // Group Schedule & Meetings
    ['GET', '/^groups\/save-schedule\/(\d+)$/', 'GroupsController', 'saveSchedule'],
    ['POST', '/^groups\/save-schedule\/(\d+)$/', 'GroupsController', 'saveSchedule', ['csrf' => true]],
    ['DELETE', '/^groups\/(\d+)\/schedule$/', 'GroupsController', 'deleteSchedule', ['csrf' => true]], // RESTful: DELETE /groups/{id}/schedule
    ['POST', '/^groups\/delete-schedule\/(\d+)$/', 'GroupsController', 'deleteSchedule', ['csrf' => true]], // Legacy: POST only
    ['GET', '/^groups\/create-meeting\/(\d+)$/', 'GroupsController', 'createMeeting'],
    ['POST', '/^groups\/create-meeting\/(\d+)$/', 'GroupsController', 'createMeeting', ['csrf' => true]],
    ['GET', '/^groups\/view-attendance\/(\d+)$/', 'GroupsController', 'viewAttendance'],

    // Group Announcements
    ['GET', '/^groups\/create-announcement\/(\d+)$/', 'GroupsController', 'createAnnouncement'],
    ['POST', '/^groups\/create-announcement\/(\d+)$/', 'GroupsController', 'createAnnouncement', ['csrf' => true]],
    ['PUT', '/^groups\/announcements\/(\d+)$/', 'GroupsController', 'updateAnnouncement', ['csrf' => true]], // RESTful: PUT /groups/announcements/{id}
    ['POST', '/^groups\/update-announcement\/(\d+)$/', 'GroupsController', 'updateAnnouncement', ['csrf' => true]], // Legacy
    ['DELETE', '/^groups\/announcements\/(\d+)$/', 'GroupsController', 'deleteAnnouncement', ['csrf' => true]], // RESTful: DELETE /groups/announcements/{id}
    ['POST', '/^groups\/delete-announcement\/(\d+)$/', 'GroupsController', 'deleteAnnouncement', ['csrf' => true]], // Legacy: POST only

    // Group Dues
    ['GET', '/^groups\/save-dues-settings\/(\d+)$/', 'GroupsController', 'saveDuesSettings'],
    ['POST', '/^groups\/save-dues-settings\/(\d+)$/', 'GroupsController', 'saveDuesSettings', ['csrf' => true]],
    ['POST', '/^groups\/record-dues-payment\/(\d+)$/', 'GroupsController', 'recordDuesPayment', ['csrf' => true]],

    // --- Birthdays ---
    ['GET', '/^birthdays$/', 'BirthdaysController', 'index'],
    ['GET', '/^birthdays\/sms$/', 'BirthdaysController', 'sms'],
    ['POST', '/^birthdays\/send-sms$/', 'BirthdaysController', 'sendSms', ['csrf' => true]],

    // --- Programs (RESTful) ---
    ['GET', '/^programs$/', 'ProgramController', 'index'], // List all programs (RESTful)
    ['GET', '/^programs\/create$/', 'ProgramController', 'create'], // Show form to create program
    ['POST', '/^programs$/', 'ProgramController', 'store', ['csrf' => true]], // Create program (RESTful)
    ['GET', '/^programs\/(\d+)$/', 'ProgramController', 'show'], // Show program details (RESTful)
    ['GET', '/^programs\/(\d+)\/edit$/', 'ProgramController', 'edit'], // Edit program form (RESTful)
    ['PUT', '/^programs\/(\d+)$/', 'ProgramController', 'update', ['csrf' => true]], // Update program (RESTful)
    ['DELETE', '/^programs\/(\d+)$/', 'ProgramController', 'delete', ['csrf' => true]], // Delete program (RESTful)

    // Program Dashboard & Special Views
    ['GET', '/^programs\/dashboard$/', 'ProgramController', 'dashboard'], // Dashboard view
    ['PUT', '/^programs\/(\d+)\/status$/', 'ProgramController', 'updateStatus', ['csrf' => true]], // RESTful: PUT /programs/{id}/status
    ['GET', '/^programs\/calendar$/', 'ProgramController', 'calendar'],
    ['GET', '/^programs\/timeline$/', 'ProgramController', 'timeline'],
    ['GET', '/^programs\/export$/', 'ProgramController', 'export'],

    // Legacy Programs Routes (for backward compatibility)
    ['GET', '/^programs\/list$/', 'RedirectController', 'programsList'], // Legacy: /programs/list → /programs
    ['POST', '/^programs\/store$/', 'RedirectController', 'programsStore', ['csrf' => true]], // Legacy: POST /programs/store → POST /programs
    ['GET', '/^programs\/show$/', 'RedirectController', 'programsShow'], // Legacy: /programs/show?id= → /programs/{id}
    ['GET', '/^programs\/edit$/', 'RedirectController', 'programsEdit'], // Legacy: /programs/edit?id= → /programs/{id}/edit
    ['POST', '/^programs\/update$/', 'RedirectController', 'programsUpdate', ['csrf' => true]], // Legacy: POST /programs/update → PUT /programs/{id}
    ['POST', '/^programs\/delete$/', 'RedirectController', 'programsDelete', ['csrf' => true]], // Legacy: POST /programs/delete → DELETE /programs/{id}
    ['POST', '/^programs\/update-status$/', 'RedirectController', 'programsUpdateStatus', ['csrf' => true]], // Legacy: POST /programs/update-status → PUT /programs/{id}/status

    // --- Categories (RESTful) ---
    ['GET', '/^categories$/', 'CategoryController', 'index'], // List all categories
    ['GET', '/^categories\/create$/', 'CategoryController', 'create'], // Show form to create category
    ['POST', '/^categories$/', 'CategoryController', 'store', ['csrf' => true]], // Create category (RESTful)
    ['GET', '/^categories\/(\d+)$/', 'CategoryController', 'show'], // Show category details (RESTful)
    ['GET', '/^categories\/(\d+)\/edit$/', 'CategoryController', 'edit'], // Edit category form (RESTful)
    ['PUT', '/^categories\/(\d+)$/', 'CategoryController', 'update', ['csrf' => true]], // Update category (RESTful)
    ['DELETE', '/^categories\/(\d+)$/', 'CategoryController', 'delete', ['csrf' => true]], // Delete category (RESTful)
    ['GET', '/^categories\/get-categories$/', 'CategoryController', 'getCategories'], // API endpoint

    // Legacy Categories Routes (for backward compatibility)
    ['POST', '/^categories\/store$/', 'RedirectController', 'categoriesStore', ['csrf' => true]], // Legacy: POST /categories/store → POST /categories
    ['GET', '/^categories\/edit$/', 'RedirectController', 'categoriesEdit'], // Legacy: /categories/edit?id= → /categories/{id}/edit
    ['POST', '/^categories\/update$/', 'RedirectController', 'categoriesUpdate', ['csrf' => true]], // Legacy: POST /categories/update → PUT /categories/{id}
    ['POST', '/^categories\/delete$/', 'RedirectController', 'categoriesDelete', ['csrf' => true]], // Legacy: POST /categories/delete → DELETE /categories/{id}

    // --- Ministry Departments - RESTful Implementation ---
    ['GET', '/^ministry-departments$/', 'MinistryDepartmentController', 'index'], // List all departments
    ['GET', '/^ministry-departments\/create$/', 'MinistryDepartmentController', 'create'], // Show create form
    ['POST', '/^ministry-departments$/', 'MinistryDepartmentController', 'store', ['csrf' => true]], // Create department (RESTful)
    ['GET', '/^ministry-departments\/(\d+)$/', 'MinistryDepartmentController', 'show'], // Show single department (RESTful)
    ['GET', '/^ministry-departments\/(\d+)\/edit$/', 'MinistryDepartmentController', 'edit'], // Show edit form (RESTful)
    ['PUT', '/^ministry-departments\/(\d+)$/', 'MinistryDepartmentController', 'update', ['csrf' => true]], // Update department (RESTful)
    ['DELETE', '/^ministry-departments\/(\d+)$/', 'MinistryDepartmentController', 'delete', ['csrf' => true]], // Delete department (RESTful)
    ['GET', '/^api\/ministry-departments$/', 'MinistryDepartmentController', 'getDepartments'], // API endpoint (RESTful)

    // Legacy Ministry Departments Routes (for backward compatibility)
    ['POST', '/^ministry-departments\/store$/', 'MinistryDepartmentController', 'store', ['csrf' => true]], // Legacy
    ['GET', '/^ministry-departments\/edit$/', 'RedirectController', 'ministryDepartmentEdit'], // Legacy redirect
    ['POST', '/^ministry-departments\/update$/', 'MinistryDepartmentController', 'update', ['csrf' => true]], // Legacy
    ['POST', '/^ministry-departments\/delete$/', 'MinistryDepartmentController', 'delete', ['csrf' => true]], // Legacy
    ['GET', '/^ministry-departments\/get-departments$/', 'MinistryDepartmentController', 'getDepartments'], // Legacy API

    // --- Children's Ministry (RESTful) ---
    ['GET', '/^children-ministry$/', 'ChildrenMinistryController', 'index'], // Children ministry dashboard
    ['GET', '/^children-ministry\/children$/', 'ChildrenMinistryController', 'children'], // List all children
    ['GET', '/^children-ministry\/children\/(\d+)$/', 'ChildrenMinistryController', 'viewChild'], // View child details
    ['GET', '/^children-ministry\/children\/(\d+)\/edit$/', 'ChildrenMinistryController', 'editChild'], // Edit child form
    ['PUT', '/^children-ministry\/children\/(\d+)$/', 'ChildrenMinistryController', 'updateChild', ['csrf' => true]], // Update child
    ['DELETE', '/^children-ministry\/children\/(\d+)$/', 'ChildrenMinistryController', 'deleteChild', ['role' => 'admin', 'csrf' => true]], // Delete child
    ['GET', '/^children-ministry\/families$/', 'ChildrenMinistryController', 'families'], // Family management
    ['POST', '/^children-ministry\/families$/', 'ChildrenMinistryController', 'createFamily', ['csrf' => true]], // Create family
    ['DELETE', '/^children-ministry\/families\/(\d+)$/', 'ChildrenMinistryController', 'deleteFamily', ['role' => 'admin', 'csrf' => true]], // Delete family
    ['DELETE', '/^children-ministry\/relationships\/(\d+)$/', 'ChildrenMinistryController', 'deleteRelationship', ['csrf' => true]], // Delete relationship
    ['GET', '/^children-ministry\/reports$/', 'ChildrenMinistryController', 'reports'], // Children ministry reports
    ['GET', '/^children-ministry\/age-groups$/', 'ChildrenMinistryController', 'ageGroups'], // Age group management
    ['POST', '/^children-ministry\/age-groups$/', 'ChildrenMinistryController', 'saveAgeGroup', ['role' => 'staff', 'csrf' => true]], // Create/update age group
    ['GET', '/^children-ministry\/age-groups\/(\d+)$/', 'ChildrenMinistryController', 'getAgeGroup'], // Get age group details
    ['DELETE', '/^children-ministry\/age-groups\/(\d+)$/', 'ChildrenMinistryController', 'deleteAgeGroup', ['role' => 'admin', 'csrf' => true]], // Delete age group
    ['PATCH', '/^children-ministry\/age-groups\/(\d+)\/toggle-status$/', 'ChildrenMinistryController', 'toggleAgeGroupStatus', ['role' => 'staff', 'csrf' => true]], // Toggle age group status

    // --- Standalone Child Registration ---
    ['GET', '/^children-ministry\/standalone-registration$/', 'StandaloneChildController', 'showRegistrationForm', ['auth' => false]], // Show standalone registration form
    ['POST', '/^children-ministry\/standalone-registration$/', 'StandaloneChildController', 'processRegistration', ['auth' => false, 'csrf' => true]], // Process standalone registration
    ['GET', '/^children-ministry\/standalone-registration-success$/', 'StandaloneChildController', 'showRegistrationSuccess', ['auth' => false]], // Show registration success page

    // Legacy Children Ministry Routes (Redirects)
    ['GET', '/^children-ministry\/view-child$/', 'RedirectController', 'childrenViewChild'], // Legacy: /children-ministry/view-child?id= → /children-ministry/children/{id}
    ['GET', '/^children-ministry\/edit-child$/', 'RedirectController', 'childrenEditChild'], // Legacy: /children-ministry/edit-child?id= → /children-ministry/children/{id}/edit
    ['POST', '/^children-ministry\/update-child$/', 'RedirectController', 'childrenUpdateChild', ['csrf' => true]], // Legacy: POST /children-ministry/update-child → PUT /children-ministry/children/{id}
    ['POST', '/^children-ministry\/delete-child$/', 'RedirectController', 'childrenDeleteChild', ['csrf' => true]], // Legacy: POST /children-ministry/delete-child → DELETE /children-ministry/children/{id}
    ['POST', '/^children-ministry\/delete-family$/', 'RedirectController', 'childrenDeleteFamily', ['csrf' => true]], // Legacy: POST /children-ministry/delete-family → DELETE /children-ministry/families/{id}
    ['POST', '/^children-ministry\/delete-relationship$/', 'RedirectController', 'childrenDeleteRelationship', ['csrf' => true]], // Legacy: POST /children-ministry/delete-relationship → DELETE /children-ministry/relationships/{id}
    ['POST', '/^children-ministry\/save-age-group$/', 'RedirectController', 'childrenSaveAgeGroup', ['csrf' => true]], // Legacy: POST /children-ministry/save-age-group → POST /children-ministry/age-groups
    ['GET', '/^children-ministry\/get-age-group$/', 'RedirectController', 'childrenGetAgeGroup'], // Legacy: /children-ministry/get-age-group?id= → /children-ministry/age-groups/{id}
    ['POST', '/^children-ministry\/delete-age-group$/', 'RedirectController', 'childrenDeleteAgeGroup', ['csrf' => true]], // Legacy: POST /children-ministry/delete-age-group → DELETE /children-ministry/age-groups/{id}
    ['POST', '/^children-ministry\/toggle-age-group-status$/', 'RedirectController', 'childrenToggleAgeGroupStatus', ['csrf' => true]], // Legacy: POST /children-ministry/toggle-age-group-status → PATCH /children-ministry/age-groups/{id}/toggle-status

    // --- Welfare - RESTful Implementation ---
    ['GET', '/^welfare$/', 'WelfareController', 'index'], // Welfare dashboard
    ['GET', '/^welfare\/create$/', 'WelfareController', 'create'], // Show create claim form (RESTful)
    ['POST', '/^welfare$/', 'WelfareController', 'store', ['csrf' => true]], // Create welfare claim (RESTful)
    ['GET', '/^welfare\/(\d+)$/', 'WelfareController', 'show'], // Show single claim (RESTful)
    ['GET', '/^welfare\/(\d+)\/edit$/', 'WelfareController', 'edit'], // Show edit form (RESTful)
    ['PUT', '/^welfare\/(\d+)$/', 'WelfareController', 'update', ['csrf' => true]], // Update claim (RESTful)
    ['DELETE', '/^welfare\/(\d+)$/', 'WelfareController', 'delete', ['csrf' => true]], // Delete claim (RESTful)
    ['GET', '/^welfare\/search$/', 'WelfareController', 'search'], // Search welfare claims
    ['GET', '/^welfare\/history$/', 'WelfareController', 'history'], // Welfare history
    ['GET', '/^welfare\/history\/(\d+)$/', 'WelfareController', 'memberHistory'], // Member welfare history (RESTful)
    ['GET', '/^welfare\/categories$/', 'WelfareController', 'categories'], // Welfare categories
    ['GET', '/^welfare\/reports$/', 'WelfareController', 'reports'], // Welfare reports
    ['GET', '/^welfare\/payments-this-month$/', 'WelfareController', 'paymentsThisMonth'], // Members who paid this month
    ['GET', '/^welfare\/claims-this-month$/', 'WelfareController', 'claimsThisMonth'], // Members who claimed this month

    // Legacy Welfare Routes (for backward compatibility)
    ['GET', '/^welfare\/add$/', 'WelfareController', 'add'], // Legacy: direct to add method (no redirect)
    ['POST', '/^welfare\/add$/', 'WelfareController', 'add', ['csrf' => true]], // Legacy: process add form
    ['GET', '/^welfare\/claim$/', 'WelfareController', 'claimForm'], // Show claim form
    ['POST', '/^welfare\/claim$/', 'WelfareController', 'claim', ['csrf' => true]], // Process claim (legacy method)

    // --- Finance Categories (RESTful) ---
    ['GET', '/^finances\/categories$/', 'SimpleCustomFinanceCategoryController', 'index'], // List all
    ['GET', '/^finances\/categories\/create$/', 'SimpleCustomFinanceCategoryController', 'create'], // Show create form
    ['POST', '/^finances\/categories$/', 'SimpleCustomFinanceCategoryController', 'store', ['csrf' => true]], // Create (RESTful)
    ['GET', '/^finances\/categories\/(\d+)$/', 'SimpleCustomFinanceCategoryController', 'show'], // Show single (RESTful)
    ['GET', '/^finances\/categories\/(\d+)\/edit$/', 'SimpleCustomFinanceCategoryController', 'edit'], // Edit form (RESTful)
    ['PUT', '/^finances\/categories\/(\d+)$/', 'SimpleCustomFinanceCategoryController', 'update', ['csrf' => true]], // Update (RESTful)
    ['DELETE', '/^finances\/categories\/(\d+)$/', 'SimpleCustomFinanceCategoryController', 'delete', ['csrf' => true]], // Delete (RESTful)
    ['PUT', '/^finances\/categories\/(\d+)\/status$/', 'SimpleCustomFinanceCategoryController', 'toggleStatus', ['csrf' => true]], // Toggle status (RESTful)

    // Legacy Finance Categories Routes (for backward compatibility)
    ['POST', '/^finances\/categories\/store$/', 'SimpleCustomFinanceCategoryController', 'store', ['csrf' => true]], // Legacy
    ['POST', '/^finances\/categories\/update$/', 'SimpleCustomFinanceCategoryController', 'update', ['csrf' => true]], // Legacy
    ['POST', '/^finances\/categories\/delete$/', 'SimpleCustomFinanceCategoryController', 'delete', ['csrf' => true]], // Legacy
    ['GET', '/^finances\/categories\/get$/', 'SimpleCustomFinanceCategoryController', 'getCategory'], // Legacy API
    ['POST', '/^finances\/categories\/toggle-status$/', 'SimpleCustomFinanceCategoryController', 'toggleStatus', ['csrf' => true]], // Legacy

    // --- Finance Dashboards ---
    ['GET', '/^finances\/dashboard\/tithe$/', 'FinanceDashboardController', 'titheDashboard'],
    ['GET', '/^finances\/dashboard\/pledge$/', 'FinanceDashboardController', 'pledgeDashboard'],
    ['GET', '/^finances\/dashboard\/pastor-application$/', 'FinanceDashboardController', 'pastorApplicationDashboard'],
    ['GET', '/^finances\/dashboard\/category$/', 'FinanceDashboardController', 'categoryDashboard'],
    ['GET', '/^finances\/member-tithe-history$/', 'FinanceDashboardController', 'memberTitheHistory'],

    // --- Tithe Members ---
    ['GET', '/^finances\/tithe\/members$/', 'TitheMemberController', 'index'],
    ['GET', '/^finances\/tithe\/members\/history$/', 'TitheMemberController', 'getMemberHistory'],
    ['GET', '/^finances\/tithe\/members\/export$/', 'TitheMemberController', 'export'],

    // --- API Endpoints (Legacy) ---
    ['POST', '/^members\/create-parent-for-child$/', 'api/create_parent_for_child.php', null],

    // --- Equipment Categories API ---
    ['GET', '/^api\/equipment-categories$/', 'api/equipment-categories.php', null],
    ['POST', '/^api\/equipment-categories$/', 'api/equipment-categories.php', null, ['csrf' => true]],

    // --- Security Test (Admin Only) ---
    ['GET', '/^security-test$/', 'test_security_web.php', null, ['role' => 'admin']],

    // Default route (must be last)
    ['GET', '/^$/', 'DashboardController', 'index'], // Empty request goes to dashboard
];
