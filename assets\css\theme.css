
/* IC<PERSON> Custom Theme Colors */
:root {
    --primary: #3F7D58;
    --primary-dark: #2c5a3f;
    --secondary: #D3E671;
    --secondary-dark: #b8c95c;
    --background: #FFFFFF;
    --text: #333333;
}

/* Primary button styles */
.bg-primary {
    background-color: var(--primary) !important;
}

.hover\:bg-primary:hover {
    background-color: var(--primary) !important;
}

.bg-primary-dark {
    background-color: var(--primary-dark) !important;
}

.hover\:bg-primary-dark:hover {
    background-color: var(--primary-dark) !important;
}

/* Secondary color styles */
.bg-secondary {
    background-color: var(--secondary) !important;
}

.hover\:bg-secondary:hover {
    background-color: var(--secondary) !important;
}

/* Text colors */
.text-primary {
    color: var(--primary) !important;
}

.hover\:text-primary:hover {
    color: var(--primary) !important;
}

/* Border colors */
.border-primary {
    border-color: var(--primary) !important;
}

.hover\:border-primary:hover {
    border-color: var(--primary) !important;
}

/* Main content background */
body {
    background-color: var(--background) !important;
}

main {
    background-color: var(--background) !important;
}

/* Ensure white backgrounds */
.bg-gray-50, .bg-gray-100, .bg-gray-200, .bg-gray-300 {
    background-color: white !important;
}

.card, .dashboard-card, .stats-card {
    background-color: white !important;
}

/* Tables with white background */
table, th, td {
    background-color: white !important;
}

/* Forms with white background */
.form-control, .form-select, .form-input {
    background-color: white !important;
}

/* Sidebar navigation styles removed - using pure Tailwind CSS */
