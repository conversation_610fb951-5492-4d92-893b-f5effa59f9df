<?php
/**
 * API endpoint to check if an email address is already registered
 * Used for duplicate detection during registration
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/Member.php';
require_once __DIR__ . '/../utils/helpers.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['email']) || empty($input['email'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Email address is required']);
        exit;
    }
    
    $email = sanitize($input['email']);
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid email format']);
        exit;
    }
    
    // Initialize database and member model
    $database = new Database();
    $member = new Member($database->getConnection());
    
    // Check if member exists with this email
    $existingMember = $member->getByEmail($email);
    
    if ($existingMember) {
        echo json_encode([
            'exists' => true,
            'member' => [
                'id' => $existingMember->id,
                'first_name' => $existingMember->first_name,
                'last_name' => $existingMember->last_name,
                'email' => $existingMember->email
            ]
        ]);
    } else {
        echo json_encode(['exists' => false]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
    error_log('Check duplicate email API error: ' . $e->getMessage());
}
?>
