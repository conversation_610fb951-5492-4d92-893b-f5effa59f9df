<?php
/**
 * Profile Controller
 * Handles user profile and admin settings
 */

require_once 'models/User.php';
require_once 'models/Setting.php';
require_once 'controllers/BaseRestfulController.php';

class ProfileController extends BaseRestfulController {
    private $database;
    private $user;
    private $setting;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->user = new User($this->database->getConnection());
        $this->setting = new Setting($this->database->getConnection());
    }

    /**
     * Display profile page
     *
     * @return void
     */
    public function index() {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
            return;
        }

        // Get current user
        $user_id = $_SESSION['user_id'];
        if (!$this->user->getById($user_id)) {
            set_flash_message('User not found', 'danger');
            redirect('dashboard');
            return;
        }

        // Store user data for the view
        $current_user = $this->user;

        // Get current settings
        $settings = $this->setting->getAll();
        $settings_array = [];
        while ($row = $settings->fetch(PDO::FETCH_ASSOC)) {
            $settings_array[$row['setting_key']] = $row['setting_value'];
        }

        // Set page title and active page
        $page_title = 'Profile & Settings - ICGC Emmanuel Temple';
        $active_page = 'profile';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/profile/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Show edit profile form (RESTful)
     *
     * @return void
     */
    public function edit() {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        // For now, redirect to profile with edit mode
        redirect('profile?mode=edit');
    }

    /**
     * Update user profile
     *
     * @return void
     */
    public function updateProfile() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $user_id = $_SESSION['user_id'];
            
            // Validate inputs
            $errors = [];
            
            if (empty($_POST['username'])) {
                $errors[] = 'Username is required';
            }
            
            if (empty($_POST['email'])) {
                $errors[] = 'Email is required';
            } elseif (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Invalid email format';
            }

            // Check if username/email already exists for other users
            $existing_user = $this->user->getByUsername($_POST['username']);
            if ($existing_user && $existing_user['id'] != $user_id) {
                $errors[] = 'Username already exists';
            }

            // Create a temporary user object to check email
            $temp_user = new User($this->database->getConnection());
            if ($temp_user->getByEmail($_POST['email'])) {
                if ($temp_user->id != $user_id) {
                    $errors[] = 'Email already exists';
                }
            }

            if (!empty($errors)) {
                $_SESSION['errors'] = $errors;
                redirect('profile');
                return;
            }

            // Get current user data
            $this->user->getById($user_id);

            // Update user data
            $this->user->username = sanitize($_POST['username']);
            $this->user->email = sanitize($_POST['email']);
            $this->user->full_name = sanitize($_POST['full_name'] ?? '');

            // Handle password update
            if (!empty($_POST['new_password'])) {
                if (strlen($_POST['new_password']) < 6) {
                    $_SESSION['errors'] = ['Password must be at least 6 characters long'];
                    redirect('profile');
                    return;
                }
                
                if ($_POST['new_password'] !== $_POST['confirm_password']) {
                    $_SESSION['errors'] = ['Passwords do not match'];
                    redirect('profile');
                    return;
                }
                
                $this->user->password = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
            }

            // Handle profile picture upload
            if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = 'uploads/profile_pictures/';
                
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                $file_extension = strtolower(pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                
                if (in_array($file_extension, $allowed_extensions)) {
                    $new_filename = 'user_' . $user_id . '_' . time() . '.' . $file_extension;
                    $target_file = $upload_dir . $new_filename;
                    
                    if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $target_file)) {
                        // Delete old profile picture if exists
                        if (!empty($this->user->profile_picture) && file_exists($this->user->profile_picture)) {
                            unlink($this->user->profile_picture);
                        }
                        $this->user->profile_picture = $target_file;
                    }
                }
            }

            // Update user
            if ($this->user->update()) {
                set_flash_message('Profile updated successfully', 'success');
            } else {
                set_flash_message('Failed to update profile', 'danger');
            }

            redirect('profile');
        }
    }

    /**
     * Update admin settings (logo, church name, etc.)
     *
     * @return void
     */
    public function updateSettings() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Check if user is admin
            if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                set_flash_message('Access denied. Admin privileges required.', 'danger');
                redirect('profile');
                return;
            }

            $errors = [];

            // Handle logo upload
            if (isset($_FILES['church_logo']) && $_FILES['church_logo']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = 'uploads/logos/';
                
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                $file_extension = strtolower(pathinfo($_FILES['church_logo']['name'], PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'svg'];
                
                if (in_array($file_extension, $allowed_extensions)) {
                    $new_filename = 'logo_' . time() . '.' . $file_extension;
                    $target_file = $upload_dir . $new_filename;
                    
                    if (move_uploaded_file($_FILES['church_logo']['tmp_name'], $target_file)) {
                        // Delete old logo if exists
                        $old_logo = $this->setting->getValue('church_logo');
                        if ($old_logo && file_exists($old_logo)) {
                            unlink($old_logo);
                        }
                        
                        $this->setting->updateSetting('church_logo', $target_file);
                    } else {
                        $errors[] = 'Failed to upload logo';
                    }
                } else {
                    $errors[] = 'Invalid logo file format. Please use JPG, PNG, GIF, or SVG.';
                }
            }

            // Update other settings
            $settings_to_update = [
                'church_name' => $_POST['church_name'] ?? '',
                'church_address' => $_POST['church_address'] ?? '',
                'church_phone' => $_POST['church_phone'] ?? '',
                'church_email' => $_POST['church_email'] ?? '',
                'church_website' => $_POST['church_website'] ?? '',
                'app_name' => $_POST['app_name'] ?? '',
                'app_description' => $_POST['app_description'] ?? '',
                'primary_color' => $_POST['primary_color'] ?? '#3F7D58',
                'secondary_color' => $_POST['secondary_color'] ?? '#D3E671',
                'timezone' => $_POST['timezone'] ?? 'UTC',
                'date_format' => $_POST['date_format'] ?? 'Y-m-d',
                'time_format' => $_POST['time_format'] ?? 'H:i'
            ];

            foreach ($settings_to_update as $key => $value) {
                if (!empty($value)) {
                    $this->setting->updateSetting($key, sanitize($value));
                }
            }

            if (empty($errors)) {
                set_flash_message('Settings updated successfully', 'success');
            } else {
                $_SESSION['errors'] = $errors;
            }

            redirect('profile');
        }
    }

    /**
     * Reset logo to default
     *
     * @return void
     */
    public function resetLogo() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Check if user is admin
            if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
                set_flash_message('Access denied. Admin privileges required.', 'danger');
                redirect('profile');
                return;
            }

            // Delete current logo file
            $current_logo = $this->setting->getValue('church_logo');
            if ($current_logo && file_exists($current_logo)) {
                unlink($current_logo);
            }

            // Reset to default
            $this->setting->updateSetting('church_logo', '');
            
            set_flash_message('Logo reset to default successfully', 'success');
            redirect('profile');
        }
    }
}
?>
