<?php
/**
 * Tithe Dashboard View
 * Comprehensive tithe tracking and analytics
 */

// Ensure we have the required data
$analytics = $analytics ?? [];
$memberTracking = $memberTracking ?? [];
$trends = $trends ?? [];
$recentTithes = $recentTithes ?? [];
?>

<div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-green-600 to-green-700 text-white py-8 px-6 rounded-b-3xl shadow-xl mb-8">
        <div class="max-w-7xl mx-auto">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <button onclick="history.back()" class="text-green-100 hover:text-white mr-3 transition-colors bg-transparent border-none cursor-pointer">
                            <i class="fas fa-arrow-left text-lg"></i>
                        </button>
                        <h1 class="text-3xl font-bold">💰 Tithe Dashboard</h1>
                    </div>
                    <p class="text-green-100 text-lg">Comprehensive tithe tracking and member analytics</p>
                </div>
                <div class="flex gap-3">
                    <a href="<?php echo BASE_URL; ?>finance/add" class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-plus mr-2"></i>
                        <span class="hidden sm:inline">Record Tithe</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>finance/enhanced-reports" class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-chart-bar mr-2"></i>
                        <span class="hidden sm:inline">Reports</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 pb-8">
        <!-- Analytics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- This Month Total -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-green-400 to-green-500 text-white">
                        <i class="fas fa-hand-holding-usd text-xl"></i>
                    </div>
                    <?php if (isset($analytics['amount_growth']) && $analytics['amount_growth'] != 0): ?>
                        <span class="text-sm font-medium px-2 py-1 rounded-full <?php echo $analytics['amount_growth'] > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                            <i class="fas fa-<?php echo $analytics['amount_growth'] > 0 ? 'arrow-up' : 'arrow-down'; ?> mr-1"></i>
                            <?php echo abs($analytics['amount_growth']); ?>%
                        </span>
                    <?php endif; ?>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    GH₵ <?php echo number_format($analytics['current_month']['total_amount'] ?? 0, 2); ?>
                </h3>
                <p class="text-gray-600 text-sm">This Month's Tithes</p>
                <p class="text-xs text-gray-500 mt-1">
                    <?php echo $analytics['current_month']['total_transactions'] ?? 0; ?> transactions
                </p>
            </div>

            <!-- Average Tithe -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 text-white">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    GH₵ <?php echo number_format($analytics['current_month']['average_amount'] ?? 0, 2); ?>
                </h3>
                <p class="text-gray-600 text-sm">Average Tithe Amount</p>
                <p class="text-xs text-gray-500 mt-1">This month</p>
            </div>

            <!-- Active Contributors -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-purple-400 to-purple-500 text-white">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    <?php echo $analytics['current_month']['unique_contributors'] ?? 0; ?>
                </h3>
                <p class="text-gray-600 text-sm">Active Contributors</p>
                <p class="text-xs text-gray-500 mt-1">This month</p>
            </div>

            <!-- Year to Date -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-orange-400 to-orange-500 text-white">
                        <i class="fas fa-calendar-alt text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    GH₵ <?php echo number_format($analytics['ytd']['ytd_amount'] ?? 0, 2); ?>
                </h3>
                <p class="text-gray-600 text-sm">Year to Date</p>
                <p class="text-xs text-gray-500 mt-1">
                    <?php echo $analytics['ytd']['ytd_transactions'] ?? 0; ?> transactions
                </p>
            </div>
        </div>

        <!-- Charts and Tables Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Trends Chart -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">Monthly Trends</h3>
                    <div class="flex items-center space-x-2">
                        <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                        <span class="text-sm text-gray-600">Tithe Amount</span>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="titheTrendsChart"></canvas>
                </div>
            </div>

            <!-- Top Contributors -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">Top Contributors</h3>
                    <a href="#member-tracking" class="text-green-600 hover:text-green-700 text-sm font-medium">
                        View All <i class="fas fa-chevron-right ml-1"></i>
                    </a>
                </div>
                <div class="space-y-4">
                    <?php if (!empty($memberTracking)): ?>
                        <?php foreach (array_slice($memberTracking, 0, 5) as $index => $member): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                                        <?php echo $index + 1; ?>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">
                                            <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?php echo $member['total_payments']; ?> payments
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-green-600">
                                        GH₵ <?php echo number_format($member['total_amount'], 2); ?>
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        Avg: GH₵ <?php echo number_format($member['average_amount'], 2); ?>
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-users text-gray-300 text-4xl mb-3"></i>
                            <p class="text-sm">No tithe contributors found</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Member Tracking Overview -->
        <div class="bg-white rounded-xl shadow-md border border-gray-100 mb-8">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">Member Tithe Tracking</h3>
                        <p class="text-gray-600 text-sm">Comprehensive member management and payment history</p>
                    </div>
                    <a href="<?php echo BASE_URL; ?>finance/tithe/members"
                       class="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center shadow-md hover:shadow-lg">
                        <i class="fas fa-users mr-2"></i>
                        <span>View All Members</span>
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Quick Stats Preview -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-600 mb-1">
                            <?php echo count($memberTracking); ?>
                        </div>
                        <div class="text-sm text-green-700">Active Contributors</div>
                    </div>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600 mb-1">
                            <?php
                            $topContributor = !empty($memberTracking) ? $memberTracking[0] : null;
                            echo $topContributor ? 'GH₵ ' . number_format($topContributor['total_amount'], 0) : '0';
                            ?>
                        </div>
                        <div class="text-sm text-blue-700">Highest Contributor</div>
                    </div>
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-purple-600 mb-1">
                            <?php
                            $avgAmount = !empty($memberTracking) ? array_sum(array_column($memberTracking, 'total_amount')) / count($memberTracking) : 0;
                            echo 'GH₵ ' . number_format($avgAmount, 0);
                            ?>
                        </div>
                        <div class="text-sm text-purple-700">Average per Member</div>
                    </div>
                </div>

                <!-- Top 5 Contributors Preview -->
                <?php if (!empty($memberTracking)): ?>
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">Top Contributors (Preview)</h4>
                        <div class="space-y-2">
                            <?php foreach (array_slice($memberTracking, 0, 5) as $index => $member): ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                                            <?php echo $index + 1; ?>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-800">
                                                <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                                            </p>
                                            <p class="text-xs text-gray-500">
                                                <?php echo $member['total_payments']; ?> payments
                                            </p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-green-600">
                                            GH₵ <?php echo number_format($member['total_amount'], 2); ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="mt-4 text-center">
                            <a href="<?php echo BASE_URL; ?>finance/tithe/members"
                               class="text-green-600 hover:text-green-700 font-medium text-sm">
                                View all <?php echo count($memberTracking); ?> members →
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-users text-gray-300 text-4xl mb-3"></i>
                        <p class="text-sm">No tithe contributors found</p>
                        <a href="<?php echo BASE_URL; ?>finance/add" class="mt-3 inline-block bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-2 px-4 rounded-lg text-sm shadow-md hover:shadow-lg transition-all duration-300">
                            <i class="fas fa-plus-circle mr-2"></i> Record First Tithe
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Additional Styles for Member History -->
<style>
.member-card {
    transition: all 0.3s ease;
}

.member-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rotate-180 {
    transform: rotate(180deg);
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

#memberSearch:focus {
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.member-card .grid > div {
    transition: all 0.2s ease;
}

.member-card .grid > div:hover {
    transform: scale(1.05);
}

/* Smooth transitions for history toggle */
#history-content-* {
    transition: all 0.3s ease;
}

/* Custom scrollbar for member list */
#membersList {
    max-height: 800px;
    overflow-y: auto;
}

#membersList::-webkit-scrollbar {
    width: 6px;
}

#membersList::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#membersList::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#membersList::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<!-- Chart.js Script -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tithe Trends Chart
    const trendsCtx = document.getElementById('titheTrendsChart').getContext('2d');
    const trendsData = <?php echo json_encode($trends); ?>;
    
    new Chart(trendsCtx, {
        type: 'line',
        data: {
            labels: trendsData.map(item => item.month_label),
            datasets: [{
                label: 'Tithe Amount (GH₵)',
                data: trendsData.map(item => parseFloat(item.total_amount)),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'GH₵ ' + value.toLocaleString();
                        }
                    }
                }
            },
            elements: {
                point: {
                    radius: 6,
                    hoverRadius: 8
                }
            }
        }
    });




});
</script>
