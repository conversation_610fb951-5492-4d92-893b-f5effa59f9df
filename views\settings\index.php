<div class="container mx-auto max-w-6xl px-4 fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border-2 border-gray-100">
        <div class="bg-gradient-to-r from-primary to-secondary-light p-6 text-white">
            <h1 class="text-3xl font-bold">System Settings</h1>
            <p class="mt-2 opacity-90">Configure your church management system</p>
        </div>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-r-lg shadow-sm" role="alert">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i>
                </div>
                <div>
                    <p class="font-bold">Please fix the following errors:</p>
                    <ul class="list-disc ml-5 mt-2">
                        <?php foreach ($_SESSION['errors'] as $error) : ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Flash Message -->
    <?php if (isset($_SESSION['flash_message'])) : ?>
        <?php
        // Handle both array and string formats for backward compatibility
        if (is_array($_SESSION['flash_message'])) {
            $message = $_SESSION['flash_message']['message'];
            $type = $_SESSION['flash_message']['type'] ?? 'info';
        } else {
            $message = $_SESSION['flash_message'];
            $type = 'success';
        }

        // Set CSS classes based on message type
        $css_classes = [
            'success' => 'bg-green-100 border-l-4 border-green-500 text-green-700',
            'danger' => 'bg-red-100 border-l-4 border-red-500 text-red-700',
            'warning' => 'bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700',
            'info' => 'bg-blue-100 border-l-4 border-blue-500 text-blue-700'
        ];

        $icon_classes = [
            'success' => 'fas fa-check-circle text-green-500',
            'danger' => 'fas fa-exclamation-circle text-red-500',
            'warning' => 'fas fa-exclamation-triangle text-yellow-500',
            'info' => 'fas fa-info-circle text-blue-500'
        ];

        $css_class = $css_classes[$type] ?? $css_classes['info'];
        $icon_class = $icon_classes[$type] ?? $icon_classes['info'];
        ?>
        <div class="<?php echo $css_class; ?> p-4 mb-6 rounded-r-lg shadow-sm" role="alert">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="<?php echo $icon_class; ?> text-xl mr-3"></i>
                </div>
                <div>
                    <p class="font-bold"><?php echo htmlspecialchars($message); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Settings Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="settingsTabs" role="tablist">
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg hover:text-primary hover:border-primary active" id="branding-tab" data-tabs-target="#branding" type="button" role="tab" aria-controls="branding" aria-selected="true">
                        <i class="fas fa-palette mr-2"></i> Branding
                    </button>
                </li>

                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary hover:border-primary" id="sms-tab" data-tabs-target="#sms" type="button" role="tab" aria-controls="sms" aria-selected="false">
                        <i class="fas fa-sms mr-2"></i> SMS Settings
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary hover:border-primary" id="phone-tab" data-tabs-target="#phone" type="button" role="tab" aria-controls="phone" aria-selected="false">
                        <i class="fas fa-phone mr-2"></i> Phone Settings
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary hover:border-primary" id="users-tab" data-tabs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="false">
                        <i class="fas fa-user-shield mr-2"></i> User Management
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary hover:border-primary" id="database-tab" data-tabs-target="#database" type="button" role="tab" aria-controls="database" aria-selected="false">
                        <i class="fas fa-database mr-2"></i> Database Management
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary hover:border-primary" id="reports-tab" data-tabs-target="#reports" type="button" role="tab" aria-controls="reports" aria-selected="false">
                        <i class="fas fa-chart-bar mr-2"></i> Church Reports
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="bg-white rounded-2xl shadow-lg overflow-hidden card-form border-2 border-gray-100 relative">
        <!-- Decorative elements -->
        <div class="absolute top-0 right-0 w-32 h-32 bg-primary bg-opacity-5 rounded-full -mr-10 -mt-10"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-secondary bg-opacity-10 rounded-full -ml-8 -mb-8"></div>
        <div class="absolute top-20 left-0 w-16 h-16 bg-blue-500 bg-opacity-5 rounded-full -ml-8"></div>
        <div class="absolute bottom-20 right-0 w-16 h-16 bg-purple-500 bg-opacity-5 rounded-full -mr-8"></div>
        <div class="p-8 relative z-10">
            <div id="settingsTabContent">
                <!-- Branding Tab Content -->
                <div class="block" id="branding" role="tabpanel" aria-labelledby="branding-tab">
                    <form action="<?php echo BASE_URL; ?>settings/update" method="POST" enctype="multipart/form-data" id="brandingForm">
                        <input type="hidden" name="form_type" value="branding">
                        <div class="mb-6">
                            <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                                <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                    <i class="fas fa-palette text-primary"></i>
                                </span>
                                Application Branding
                            </h2>

                            <!-- Simple Information -->
                            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle text-blue-600 mr-3"></i>
                                    <div>
                                        <h4 class="font-semibold text-blue-800">Customize Your Application</h4>
                                        <p class="text-sm text-blue-700">Change the application name and logo to match your organization's branding.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Logo Section -->
                            <div class="mb-6 p-6 bg-gray-50 rounded-lg border">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-image text-primary mr-2"></i>
                                    Application Logo
                                </h3>

                                <div class="flex items-center space-x-6">
                                    <!-- Current Logo Display -->
                                    <div class="flex-shrink-0">
                                        <div class="w-24 h-24 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
                                            <?php
                                            // Simplified logo handling - use church_logo as primary
                                            $current_logo = $settings['church_logo'] ?? '';
                                            if (!empty($current_logo) && file_exists($current_logo)) :
                                            ?>
                                                <img src="<?php echo BASE_URL . $current_logo; ?>" alt="Current Logo" class="w-full h-full object-contain">
                                            <?php else : ?>
                                                <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                                                    <i class="fas fa-image text-2xl text-gray-400"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-2 text-center">Current Logo</p>
                                    </div>

                                    <!-- Logo Upload Controls -->
                                    <div class="flex-1">
                                        <label for="logo_upload" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Upload New Logo
                                        </label>
                                        <input type="file" id="logo_upload" name="logo_upload" accept="image/*"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                               onchange="previewLogo(this)">
                                        <p class="text-xs text-gray-500 mt-1">
                                            Supported formats: JPG, PNG, GIF. Max size: 2MB. Recommended: 200x200px
                                        </p>

                                        <!-- Logo Preview -->
                                        <div id="logoPreview" class="hidden mt-3">
                                            <p class="text-sm font-semibold text-gray-700 mb-2">Preview:</p>
                                            <div class="w-16 h-16 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
                                                <img id="logoPreviewImg" src="" alt="Logo Preview" class="w-full h-full object-contain">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>



                            <!-- Application Name -->
                            <div class="mb-6 p-6 bg-gray-50 rounded-lg border">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-tag text-primary mr-2"></i>
                                    Application Name
                                </h3>

                                <div class="form-group">
                                    <label for="church_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                        Organization Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="church_name" name="church_name"
                                           value="<?php echo htmlspecialchars($settings['church_name'] ?? 'ICGC Emmanuel Temple'); ?>"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                                           required placeholder="Enter your organization name">
                                    <p class="text-xs text-gray-500 mt-1">This name appears throughout the application interface</p>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="flex justify-center mt-6">
                                <button type="submit" class="bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg">
                                    <i class="fas fa-save mr-2"></i>
                                    Save Branding Settings
                                </button>
                            </div>
                        </div>
                    </form>
                </div>



                <!-- SMS Settings Tab Content -->
                <div class="hidden" id="sms" role="tabpanel" aria-labelledby="sms-tab">
                    <form action="<?php echo BASE_URL; ?>settings/update" method="POST" id="smsForm">
                        <input type="hidden" name="form_type" value="sms_settings">
                        <div class="mb-6">
                            <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                                <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                    <i class="fas fa-sms text-primary"></i>
                                </span>
                                SMS Settings
                                <span class="ml-3 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                    Tenant-Specific
                                </span>
                            </h2>

                            <!-- SMS Configuration Info -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-500 text-lg"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-blue-800">SMS Configuration</h3>
                                        <p class="text-sm text-blue-700 mt-1">
                                            Configure your SMS API credentials and sender ID for sending SMS messages to members.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- SMS API Key -->
                                <div class="form-group mb-2">
                                    <label for="sms_api_key" class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                        <span class="bg-blue-100 text-blue-600 p-2 rounded-full mr-2 shadow-sm">
                                            <i class="fas fa-key"></i>
                                        </span>
                                        SMS API Key
                                    </label>
                                    <div class="relative group">
                                        <div class="absolute inset-y-0 left-0 w-10 flex items-center justify-center pointer-events-none bg-gray-50 border-r-2 border-gray-400 rounded-l-lg transition-colors group-hover:bg-blue-100">
                                            <i class="fas fa-key text-blue-600"></i>
                                        </div>
                                        <input type="text" id="sms_api_key" name="sms_api_key" value="<?php echo isset($_SESSION['form_data']['sms_api_key']) ? $_SESSION['form_data']['sms_api_key'] : ($settings['sms_api_key'] ?? ''); ?>" placeholder="Enter your Arkesel SMS API key"
                                            class="w-full rounded-lg border-2 border-gray-400 pl-12 py-3 bg-gray-50 shadow-sm
                                            hover:border-blue-400 hover:bg-white focus:bg-white focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-30
                                            transition-all duration-200">
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1 ml-1">Your organization's API key for Arkesel SMS service.</p>
                                </div>

                                <!-- SMS Sender ID -->
                                <div class="form-group mb-2">
                                    <label for="sms_sender_id" class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                        <span class="bg-green-100 text-green-600 p-2 rounded-full mr-2 shadow-sm">
                                            <i class="fas fa-id-card"></i>
                                        </span>
                                        SMS Sender ID
                                    </label>
                                    <div class="relative group">
                                        <div class="absolute inset-y-0 left-0 w-10 flex items-center justify-center pointer-events-none bg-gray-50 border-r-2 border-gray-400 rounded-l-lg transition-colors group-hover:bg-green-100">
                                            <i class="fas fa-id-card text-green-600"></i>
                                        </div>
                                        <input type="text" id="sms_sender_id" name="sms_sender_id" value="<?php echo isset($_SESSION['form_data']['sms_sender_id']) ? $_SESSION['form_data']['sms_sender_id'] : ($settings['sms_sender_id'] ?? 'ICGC_ET'); ?>"
                                            class="w-full rounded-lg border-2 border-gray-400 pl-12 py-3 bg-gray-50 shadow-sm
                                            hover:border-green-400 hover:bg-white focus:bg-white focus:border-green-400 focus:ring-2 focus:ring-green-400 focus:ring-opacity-30
                                            transition-all duration-200">
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1 ml-1">Your organization's sender ID for outgoing SMS messages (e.g., ICGC_ET, CHURCH_NAME)</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex justify-center">
                            <button type="submit" class="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white py-3 px-8 rounded-full flex items-center transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                                <i class="fas fa-save mr-2"></i> Save SMS Settings
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Phone Settings Tab Content -->
                <div class="hidden" id="phone" role="tabpanel" aria-labelledby="phone-tab">
                    <form action="<?php echo BASE_URL; ?>settings/update" method="POST" id="phoneForm">
                        <input type="hidden" name="form_type" value="phone_settings">
                        <div class="mb-6">
                            <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                                <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                    <i class="fas fa-phone text-primary"></i>
                                </span>
                                Phone Number Settings
                                <span class="ml-3 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                    Global Configuration
                                </span>
                            </h2>

                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-blue-800 mb-2">About Phone Number Normalization</h4>
                                        <p class="text-blue-700 text-sm">
                                            This setting controls how local phone numbers (without country codes) are automatically
                                            converted to international format. For example, if set to UK, "07578662999" becomes "+447578662999".
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Default Country Setting -->
                            <div class="mb-6">
                                <label for="default_phone_country" class="block text-sm font-medium text-gray-700 mb-2">
                                    Default Country for Phone Numbers <span class="text-red-500">*</span>
                                </label>
                                <select id="default_phone_country" name="default_phone_country" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                                    <?php
                                    // Get current setting
                                    $current_country = 'GH'; // Default
                                    try {
                                        require_once 'config/database.php';
                                        $database = new Database();
                                        $conn = $database->getConnection();
                                        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
                                        $stmt->execute(['default_phone_country']);
                                        $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                        if ($result) {
                                            $current_country = $result['setting_value'];
                                        }
                                    } catch (Exception $e) {
                                        // Use default
                                    }

                                    // Get all supported countries from PhoneNumberUtils
                                    require_once 'utils/PhoneNumberUtils.php';
                                    $countries = PhoneNumberUtils::getSupportedCountries();

                                    foreach ($countries as $code => $name) {
                                        $selected = ($code === $current_country) ? 'selected' : '';
                                        echo "<option value=\"$code\" $selected>$name</option>";
                                    }
                                    ?>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">
                                    Choose the country where your church is located. This affects how phone numbers without country codes are processed.
                                </p>
                            </div>

                            <!-- Examples Section -->
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <h4 class="font-medium text-yellow-800 mb-3 flex items-center">
                                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                    Examples by Country
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <strong class="text-yellow-800">Ghana (GH):</strong>
                                        <ul class="text-yellow-700 mt-1 space-y-1">
                                            <li>• "0246670114" → "+233246670114"</li>
                                            <li>• "0501234567" → "+233501234567"</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <strong class="text-yellow-800">United Kingdom (GB):</strong>
                                        <ul class="text-yellow-700 mt-1 space-y-1">
                                            <li>• "07578662999" → "+447578662999"</li>
                                            <li>• "02012345678" → "+442012345678"</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex justify-center">
                            <button type="submit" class="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white py-3 px-8 rounded-full flex items-center transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                                <i class="fas fa-save mr-2"></i> Save Phone Settings
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Users Tab Content -->
                <div class="hidden" id="users" role="tabpanel" aria-labelledby="users-tab">
                    <div class="mb-6">
                        <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                            <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                <i class="fas fa-user-shield text-primary"></i>
                            </span>
                            User Management
                        </h2>

                        <!-- User Statistics -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-primary">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">Total Users</p>
                                        <p class="text-2xl font-bold text-gray-800"><?php echo $totalUsers; ?></p>
                                    </div>
                                    <div class="bg-primary bg-opacity-10 p-3 rounded-full">
                                        <i class="fas fa-users text-primary"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-red-500">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">Administrators</p>
                                        <p class="text-2xl font-bold text-gray-800"><?php echo $adminCount; ?></p>
                                    </div>
                                    <div class="bg-red-100 p-3 rounded-full">
                                        <i class="fas fa-user-shield text-red-500"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-green-500">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">Staff</p>
                                        <p class="text-2xl font-bold text-gray-800"><?php echo $staffCount; ?></p>
                                    </div>
                                    <div class="bg-green-100 p-3 rounded-full">
                                        <i class="fas fa-user-tie text-green-500"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">Active Users</p>
                                        <p class="text-2xl font-bold text-gray-800"><?php echo $activeCount; ?></p>
                                    </div>
                                    <div class="bg-blue-100 p-3 rounded-full">
                                        <i class="fas fa-user-check text-blue-500"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Add User Button -->
                        <div class="mb-6 flex justify-end">
                            <a href="<?php echo BASE_URL; ?>users/add" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300 transform hover:scale-105 shadow-md">
                                <i class="fas fa-user-plus mr-2"></i> Add New User
                            </a>
                        </div>

                        <!-- Users Table -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden">
                            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <i class="fas fa-user-shield text-primary mr-2"></i>
                                    System Users
                                </h3>
                                <div class="relative">
                                    <input type="text" id="userSearchInSettings" placeholder="Search users..." class="rounded-lg border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 pl-10 py-2 text-sm">
                                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                </div>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200" id="usersTableBody">
                                        <?php if (empty($users)) : ?>
                                            <tr>
                                                <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No users found</td>
                                            </tr>
                                        <?php else : ?>
                                            <?php foreach ($users as $user) : ?>
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="flex items-center">
                                                            <div class="flex-shrink-0 h-10 w-10">
                                                                <div class="h-10 w-10 rounded-full bg-primary-light flex items-center justify-center text-white font-bold">
                                                                    <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                                                </div>
                                                            </div>
                                                            <div class="ml-4">
                                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['username']); ?></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo htmlspecialchars($user['email']); ?></td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                            <?php echo $user['role'] === 'super_admin' ? 'bg-red-100 text-red-800' :
                                                                      ($user['role'] === 'admin' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'); ?>">
                                                            <?php echo ucfirst(str_replace('_', ' ', $user['role'])); ?>
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                            <?php echo $user['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                            <?php echo ucfirst($user['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <?php echo (isset($user['last_login']) && $user['last_login']) ? date('M j, Y g:i A', strtotime($user['last_login'])) : 'Never'; ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <div class="flex space-x-2">
                                                            <a href="<?php echo BASE_URL; ?>users/edit?id=<?php echo $user['id']; ?>" class="text-indigo-600 hover:text-indigo-900">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <?php if ($user['id'] != $_SESSION['user_id']) : ?>
                                                                <button onclick="confirmUserDelete(<?php echo $user['id']; ?>)" class="text-red-600 hover:text-red-900">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Database Management Tab Content -->
                <div class="hidden" id="database" role="tabpanel" aria-labelledby="database-tab">
                    <!-- Database Management Tabs -->
                    <div class="mb-6">
                        <div class="border-b border-gray-200 mb-6">
                            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="databaseTabs" role="tablist">
                                <li class="mr-2" role="presentation">
                                    <button class="inline-block p-4 border-b-2 rounded-t-lg hover:text-primary hover:border-primary active" id="backup-restore-tab" data-tabs-target="#backup-restore" type="button" role="tab" aria-controls="backup-restore" aria-selected="true">
                                        <i class="fas fa-save mr-2"></i> Backup & Restore
                                    </button>
                                </li>
                                <li class="mr-2" role="presentation">
                                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-primary hover:border-primary" id="system-maintenance-tab" data-tabs-target="#system-maintenance" type="button" role="tab" aria-controls="system-maintenance" aria-selected="false">
                                        <i class="fas fa-tools mr-2"></i> System Maintenance
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div id="databaseTabContent">
                        <!-- Backup & Restore Tab Content -->
                        <div class="block" id="backup-restore" role="tabpanel" aria-labelledby="backup-restore-tab">
                            <div class="mb-6">
                                <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                                    <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                        <i class="fas fa-database text-primary"></i>
                                    </span>
                                    Backup & Restore
                                </h2>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                                    <!-- Backup Database -->
                                    <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-blue-500 hover:shadow-lg transition-all duration-300">
                                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                            <span class="bg-blue-100 text-blue-600 p-2 rounded-full mr-2 shadow-sm">
                                                <i class="fas fa-download"></i>
                                            </span>
                                            Backup Database
                                        </h3>
                                        <p class="text-gray-600 mb-6">Create a backup of your database. This will download an SQL file containing all your data.</p>
                                        <form action="<?php echo BASE_URL; ?>settings/backup" method="POST">
                                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                                <i class="fas fa-download mr-2"></i> Create Backup
                                            </button>
                                        </form>
                                    </div>

                                    <!-- Restore Database -->
                                    <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-green-500 hover:shadow-lg transition-all duration-300">
                                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                            <span class="bg-green-100 text-green-600 p-2 rounded-full mr-2 shadow-sm">
                                                <i class="fas fa-upload"></i>
                                            </span>
                                            Restore Database
                                        </h3>
                                        <p class="text-gray-600 mb-6">Restore your database from a backup file. This will replace your current data.</p>
                                        <form action="<?php echo BASE_URL; ?>settings/restore" method="POST" enctype="multipart/form-data">
                                            <div class="mb-4">
                                                <label for="backup_file" class="block text-sm font-medium text-gray-700 mb-2">Select Backup File</label>
                                                <input type="file" id="backup_file" name="backup_file" accept=".sql" class="w-full border-2 border-gray-300 rounded-lg p-2 focus:outline-none focus:border-green-500">
                                            </div>
                                            <button type="submit" class="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                                <i class="fas fa-upload mr-2"></i> Restore Backup
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Maintenance Tab Content -->
                        <div class="hidden" id="system-maintenance" role="tabpanel" aria-labelledby="system-maintenance-tab">
                            <div class="mb-6">
                                <h2 class="text-xl font-semibold text-primary mb-6 flex items-center">
                                    <span class="bg-primary bg-opacity-10 p-2 rounded-full mr-3">
                                        <i class="fas fa-tools text-primary"></i>
                                    </span>
                                    System Maintenance
                                </h2>

                                <!-- Database Statistics -->
                                <div class="bg-white rounded-xl shadow-md p-6 mb-8 border border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                        <span class="bg-purple-100 text-purple-600 p-2 rounded-full mr-2 shadow-sm">
                                            <i class="fas fa-chart-pie"></i>
                                        </span>
                                        Database Statistics
                                    </h3>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <p class="text-sm text-gray-500">Total Size</p>
                                                    <p class="text-2xl font-bold text-gray-800"><?php echo isset($db_stats) && isset($db_stats['total_size']) ? $db_stats['total_size'] : '0 MB'; ?></p>
                                                </div>
                                                <div class="bg-blue-100 p-3 rounded-full">
                                                    <i class="fas fa-database text-blue-600"></i>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <p class="text-sm text-gray-500">Total Tables</p>
                                                    <p class="text-2xl font-bold text-gray-800"><?php echo isset($db_stats) && isset($db_stats['total_tables']) ? $db_stats['total_tables'] : '0'; ?></p>
                                                </div>
                                                <div class="bg-green-100 p-3 rounded-full">
                                                    <i class="fas fa-table text-green-600"></i>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <p class="text-sm text-gray-500">Last Optimized</p>
                                                    <p class="text-2xl font-bold text-gray-800"><?php echo isset($db_stats) && isset($db_stats['last_optimized']) ? $db_stats['last_optimized'] : 'Never'; ?></p>
                                                </div>
                                                <div class="bg-amber-100 p-3 rounded-full">
                                                    <i class="fas fa-clock text-amber-600"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (isset($db_stats) && isset($db_stats['recommendations']) && !empty($db_stats['recommendations'])): ?>
                                    <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4 rounded-r-md">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-info-circle text-blue-500"></i>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-blue-800">Recommendations</h3>
                                                <div class="mt-2 text-sm text-blue-700">
                                                    <ul class="list-disc pl-5 space-y-1">
                                                        <?php foreach ($db_stats['recommendations'] as $recommendation): ?>
                                                            <li><?php echo $recommendation; ?></li>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="text-right">
                                        <a href="<?php echo BASE_URL; ?>settings/refresh-stats" class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-sync-alt mr-1"></i> Refresh Statistics
                                        </a>
                                    </div>
                                </div>

                                <!-- Maintenance Actions -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Database Optimization -->
                                    <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-indigo-500 hover:shadow-lg transition-all duration-300">
                                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                            <span class="bg-indigo-100 text-indigo-600 p-2 rounded-full mr-2 shadow-sm">
                                                <i class="fas fa-database"></i>
                                            </span>
                                            Database Optimization
                                        </h3>
                                        <p class="text-gray-600 mb-6">Optimize database tables to improve performance and reduce storage space. This is recommended to run periodically.</p>
                                        <div class="flex space-x-2">
                                            <a href="<?php echo BASE_URL; ?>settings/optimize-database" class="flex-1 bg-indigo-500 hover:bg-indigo-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                                <i class="fas fa-sync-alt mr-2"></i> Run Optimization
                                            </a>
                                            <a href="<?php echo BASE_URL; ?>direct_maintenance.php" class="bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                                <i class="fas fa-bolt"></i>
                                            </a>
                                        </div>
                                    </div>

                                    <!-- Data Archiving -->
                                    <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-amber-500 hover:shadow-lg transition-all duration-300">
                                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                            <span class="bg-amber-100 text-amber-600 p-2 rounded-full mr-2 shadow-sm">
                                                <i class="fas fa-archive"></i>
                                            </span>
                                            Data Archiving
                                        </h3>
                                        <p class="text-gray-600 mb-6">Archive old data to improve system performance while keeping it accessible when needed.</p>
                                        <a href="<?php echo BASE_URL; ?>settings/archiving" class="w-full bg-amber-500 hover:bg-amber-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                            <i class="fas fa-archive mr-2"></i> Manage Archives
                                        </a>
                                    </div>

                                    <!-- Maintenance Scheduling -->
                                    <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-blue-500 hover:shadow-lg transition-all duration-300">
                                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                            <span class="bg-blue-100 text-blue-600 p-2 rounded-full mr-2 shadow-sm">
                                                <i class="fas fa-calendar-alt"></i>
                                            </span>
                                            Maintenance Scheduling
                                        </h3>
                                        <p class="text-gray-600 mb-6">Schedule automatic maintenance tasks to keep your system running smoothly.</p>
                                        <a href="<?php echo BASE_URL; ?>settings/maintenance-schedule" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                            <i class="fas fa-calendar-alt mr-2"></i> Schedule Maintenance
                                        </a>
                                    </div>

                                    <!-- Maintenance Log -->
                                    <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-purple-500 hover:shadow-lg transition-all duration-300">
                                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                            <span class="bg-purple-100 text-purple-600 p-2 rounded-full mr-2 shadow-sm">
                                                <i class="fas fa-history"></i>
                                            </span>
                                            Maintenance Log
                                        </h3>
                                        <p class="text-gray-600 mb-6">View a history of all maintenance activities performed on your system.</p>
                                        <a href="<?php echo BASE_URL; ?>settings/maintenance-log" class="w-full bg-purple-500 hover:bg-purple-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                            <i class="fas fa-history mr-2"></i> View Log
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Church Reports Tab Content -->
                <div class="hidden" id="reports" role="tabpanel" aria-labelledby="reports-tab">
                    <div class="p-6">
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold text-gray-800 mb-2 flex items-center">
                                <span class="bg-blue-100 text-blue-600 p-2 rounded-full mr-3 shadow-sm">
                                    <i class="fas fa-chart-bar"></i>
                                </span>
                                Church Reports
                            </h3>
                            <p class="text-gray-600">Generate and view comprehensive reports about your church activities and data.</p>
                        </div>

                        <!-- Reports Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- Membership Reports -->
                            <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-green-500 hover:shadow-lg transition-all duration-300">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <span class="bg-green-100 text-green-600 p-2 rounded-full mr-2 shadow-sm">
                                        <i class="fas fa-users"></i>
                                    </span>
                                    Membership Reports
                                </h4>
                                <p class="text-gray-600 mb-6">View detailed reports on church membership, demographics, and growth statistics.</p>
                                <a href="<?php echo BASE_URL; ?>reports/membership" class="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                    <i class="fas fa-chart-line mr-2"></i> View Reports
                                </a>
                            </div>

                            <!-- Attendance Reports -->
                            <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-blue-500 hover:shadow-lg transition-all duration-300">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <span class="bg-blue-100 text-blue-600 p-2 rounded-full mr-2 shadow-sm">
                                        <i class="fas fa-clipboard-check"></i>
                                    </span>
                                    Attendance Reports
                                </h4>
                                <p class="text-gray-600 mb-6">Track attendance patterns, trends, and generate attendance summaries.</p>
                                <a href="<?php echo BASE_URL; ?>reports/attendance" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                    <i class="fas fa-chart-bar mr-2"></i> View Reports
                                </a>
                            </div>

                            <!-- Financial Reports -->
                            <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-yellow-500 hover:shadow-lg transition-all duration-300">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <span class="bg-yellow-100 text-yellow-600 p-2 rounded-full mr-2 shadow-sm">
                                        <i class="fas fa-dollar-sign"></i>
                                    </span>
                                    Financial Reports
                                </h4>
                                <p class="text-gray-600 mb-6">Generate financial summaries, offering reports, and budget analysis.</p>
                                <a href="<?php echo BASE_URL; ?>reports/financial" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                    <i class="fas fa-chart-pie mr-2"></i> View Reports
                                </a>
                            </div>

                            <!-- Ministry Reports -->
                            <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-purple-500 hover:shadow-lg transition-all duration-300">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <span class="bg-purple-100 text-purple-600 p-2 rounded-full mr-2 shadow-sm">
                                        <i class="fas fa-hands-helping"></i>
                                    </span>
                                    Ministry Reports
                                </h4>
                                <p class="text-gray-600 mb-6">Track ministry activities, group participation, and program effectiveness.</p>
                                <a href="<?php echo BASE_URL; ?>reports/ministry" class="w-full bg-purple-500 hover:bg-purple-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                    <i class="fas fa-chart-area mr-2"></i> View Reports
                                </a>
                            </div>

                            <!-- Visitor Reports -->
                            <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-indigo-500 hover:shadow-lg transition-all duration-300">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <span class="bg-indigo-100 text-indigo-600 p-2 rounded-full mr-2 shadow-sm">
                                        <i class="fas fa-user-plus"></i>
                                    </span>
                                    Visitor Reports
                                </h4>
                                <p class="text-gray-600 mb-6">Analyze visitor data, follow-up statistics, and conversion rates.</p>
                                <a href="<?php echo BASE_URL; ?>reports/visitors" class="w-full bg-indigo-500 hover:bg-indigo-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                    <i class="fas fa-user-chart mr-2"></i> View Reports
                                </a>
                            </div>

                            <!-- Custom Reports -->
                            <div class="bg-white rounded-xl shadow-md p-6 border-l-4 border-red-500 hover:shadow-lg transition-all duration-300">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <span class="bg-red-100 text-red-600 p-2 rounded-full mr-2 shadow-sm">
                                        <i class="fas fa-cogs"></i>
                                    </span>
                                    Custom Reports
                                </h4>
                                <p class="text-gray-600 mb-6">Create custom reports with specific criteria and data combinations.</p>
                                <a href="<?php echo BASE_URL; ?>reports/custom" class="w-full bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                                    <i class="fas fa-tools mr-2"></i> Create Report
                                </a>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-8 bg-gray-50 rounded-xl p-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <span class="bg-gray-100 text-gray-600 p-2 rounded-full mr-2 shadow-sm">
                                    <i class="fas fa-bolt"></i>
                                </span>
                                Quick Actions
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <a href="<?php echo BASE_URL; ?>reports" class="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-center transition-all duration-300 shadow-sm hover:shadow-md">
                                    <i class="fas fa-chart-bar text-blue-500 mr-2"></i>
                                    <span class="text-gray-700 font-medium">All Reports</span>
                                </a>
                                <a href="<?php echo BASE_URL; ?>reports/export" class="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-center transition-all duration-300 shadow-sm hover:shadow-md">
                                    <i class="fas fa-download text-green-500 mr-2"></i>
                                    <span class="text-gray-700 font-medium">Export Data</span>
                                </a>
                                <a href="<?php echo BASE_URL; ?>reports/schedule" class="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-center transition-all duration-300 shadow-sm hover:shadow-md">
                                    <i class="fas fa-calendar-alt text-purple-500 mr-2"></i>
                                    <span class="text-gray-700 font-medium">Schedule Reports</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Clear form data
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}

// Clear flash message
if (isset($_SESSION['flash_message'])) {
    unset($_SESSION['flash_message']);
}
?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Main tabs functionality
        function setupTabs(tabsSelector, contentSelector) {
            const tabs = document.querySelectorAll(tabsSelector);
            const tabContents = document.querySelectorAll(contentSelector);

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Deactivate all tabs in this group
                    tabs.forEach(t => {
                        t.classList.remove('text-primary', 'border-primary', 'active');
                        t.classList.add('border-transparent');
                        t.setAttribute('aria-selected', 'false');
                    });

                    // Hide all tab contents in this group
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });

                    // Activate clicked tab
                    this.classList.add('text-primary', 'border-primary', 'active');
                    this.classList.remove('border-transparent');
                    this.setAttribute('aria-selected', 'true');

                    // Show corresponding content
                    const target = this.getAttribute('data-tabs-target');
                    document.querySelector(target).classList.remove('hidden');
                });
            });

            // Activate first tab by default if none are active
            if (tabs.length > 0 && !Array.from(tabs).some(tab => tab.classList.contains('active'))) {
                tabs[0].click();
            }
        }

        // Setup main tabs
        setupTabs('#settingsTabs [role="tab"]', '#settingsTabContent > [role="tabpanel"]');

        // Setup database tabs
        setupTabs('#databaseTabs [role="tab"]', '#databaseTabContent > [role="tabpanel"]');

        // Check if there's a hash in the URL to activate specific tab
        const hash = window.location.hash;
        const currentPath = window.location.pathname;

        // If coming from users page, activate users tab
        if (currentPath.includes('/users') || hash === '#users') {
            const usersTab = document.querySelector('#users-tab');
            if (usersTab) {
                usersTab.click();
                return;
            }
        }

        if (hash) {
            // Check if it's a main tab
            const mainTab = document.querySelector(`#settingsTabs [data-tabs-target="${hash}"]`);
            if (mainTab) {
                mainTab.click();
            } else {
                // Check if it's a nested tab
                const nestedTabMatch = hash.match(/#([^/]+)\/(.+)/);
                if (nestedTabMatch) {
                    const mainTabId = `#${nestedTabMatch[1]}`;
                    const nestedTabId = `#${nestedTabMatch[2]}`;

                    // First activate the main tab
                    const mainTab = document.querySelector(`#settingsTabs [data-tabs-target="${mainTabId}"]`);
                    if (mainTab) {
                        mainTab.click();

                        // Then activate the nested tab
                        setTimeout(() => {
                            const nestedTab = document.querySelector(`[data-tabs-target="${nestedTabId}"]`);
                            if (nestedTab) nestedTab.click();
                        }, 100);
                    }
                }
            }
        } else {
            // Activate first main tab by default
            const firstMainTab = document.querySelector('#settingsTabs [role="tab"]');
            if (firstMainTab) firstMainTab.click();
        }

        // Form animations
        const formGroups = document.querySelectorAll('.form-group');
        formGroups.forEach((group, index) => {
            group.style.opacity = '0';
            group.style.transform = 'translateY(20px)';
            setTimeout(() => {
                group.style.transition = 'all 0.5s ease';
                group.style.opacity = '1';
                group.style.transform = 'translateY(0)';
            }, 100 + (index * 50));
        });

        // Decorative elements animation
        const decorativeElements = document.querySelectorAll('.card-form .absolute');
        decorativeElements.forEach((el, index) => {
            if (index > 1) {
                el.style.animation = `float ${6 + index}s ease-in-out infinite`;
                el.style.animationDelay = `${index * 0.5}s`;
            }
        });

        // Add click handler for database tab to ensure nested tabs work
        document.querySelector('#database-tab').addEventListener('click', function() {
            // Ensure database tabs are initialized after the database tab is shown
            setTimeout(() => {
                setupTabs('#databaseTabs [role="tab"]', '#databaseTabContent > [role="tabpanel"]');
            }, 100);
        });

        // User search functionality
        const userSearchInput = document.getElementById('userSearchInSettings');
        if (userSearchInput) {
            userSearchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const tableRows = document.querySelectorAll('#usersTableBody tr');

                tableRows.forEach(row => {
                    const username = row.querySelector('td:first-child .text-sm')?.textContent.toLowerCase() || '';
                    const email = row.querySelector('td:nth-child(2)')?.textContent.toLowerCase() || '';
                    const role = row.querySelector('td:nth-child(3)')?.textContent.toLowerCase() || '';

                    if (username.includes(searchTerm) || email.includes(searchTerm) || role.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }
    });

    // User delete confirmation function
    function confirmUserDelete(userId) {
        if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            window.location.href = '<?php echo BASE_URL; ?>users/delete?id=' + userId;
        }
    }

    // Simplified logo preview functionality
    function previewLogo(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('logoPreview');
                const previewImg = document.getElementById('logoPreviewImg');

                if (preview && previewImg) {
                    previewImg.src = e.target.result;
                    preview.classList.remove('hidden');
                }
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    // Logo size adjustment functions
    function updateLogoPreview(type) {
        const heightSlider = document.getElementById(type + '_logo_height');
        const widthSlider = document.getElementById(type + '_logo_width');
        const heightValue = document.getElementById(type + '_height_value');
        const widthValue = document.getElementById(type + '_width_value');
        const maintainAspect = document.getElementById(type + '_maintain_aspect');
        const preview = document.getElementById(type + 'LogoPreview');
        const sizeInfo = document.getElementById(type + 'SizeInfo');

        if (heightSlider && widthSlider && heightValue && widthValue) {
            const height = heightSlider.value;
            const width = widthSlider.value;

            // Update display values
            heightValue.textContent = height + 'px';
            widthValue.textContent = width + 'px';

            // Update preview
            if (preview) {
                preview.style.height = height + 'px';
                preview.style.width = width + 'px';
            }

            // Update size info
            if (sizeInfo) {
                sizeInfo.textContent = width + 'x' + height + 'px';
            }

            // Handle aspect ratio
            if (maintainAspect && maintainAspect.checked) {
                // Calculate aspect ratio based on current values
                const aspectRatio = width / height;

                // If height changed, adjust width
                if (event.target === heightSlider) {
                    const newWidth = Math.round(height * aspectRatio);
                    widthSlider.value = Math.min(Math.max(newWidth, widthSlider.min), widthSlider.max);
                    widthValue.textContent = widthSlider.value + 'px';
                    if (preview) preview.style.width = widthSlider.value + 'px';
                }
                // If width changed, adjust height
                else if (event.target === widthSlider) {
                    const newHeight = Math.round(width / aspectRatio);
                    heightSlider.value = Math.min(Math.max(newHeight, heightSlider.min), heightSlider.max);
                    heightValue.textContent = heightSlider.value + 'px';
                    if (preview) preview.style.height = heightSlider.value + 'px';
                }

                // Update size info again if aspect ratio was maintained
                if (sizeInfo) {
                    sizeInfo.textContent = widthSlider.value + 'x' + heightSlider.value + 'px';
                }
            }

            // Update login preview (uses sidebar size * 1.5)
            if (type === 'sidebar') {
                const loginPreview = document.getElementById('loginLogoPreview');
                if (loginPreview) {
                    loginPreview.style.height = (height * 1.5) + 'px';
                    loginPreview.style.width = 'auto';
                }
            }
        }
    }

    function toggleAspectRatio(type) {
        const maintainAspect = document.getElementById(type + '_maintain_aspect');
        const heightSlider = document.getElementById(type + '_logo_height');
        const widthSlider = document.getElementById(type + '_logo_width');

        if (maintainAspect && maintainAspect.checked && heightSlider && widthSlider) {
            // When enabling aspect ratio, make width match height
            widthSlider.value = heightSlider.value;
            updateLogoPreview(type);
        }
    }

    function applyPreset(size) {
        let sidebarSize, headerSize;

        switch(size) {
            case 'small':
                sidebarSize = 60;
                headerSize = 30;
                break;
            case 'medium':
                sidebarSize = 80;
                headerSize = 40;
                break;
            case 'large':
                sidebarSize = 100;
                headerSize = 50;
                break;
        }

        // Apply to sidebar
        const sidebarHeight = document.getElementById('sidebar_logo_height');
        const sidebarWidth = document.getElementById('sidebar_logo_width');
        if (sidebarHeight && sidebarWidth) {
            sidebarHeight.value = sidebarSize;
            sidebarWidth.value = sidebarSize;
            updateLogoPreview('sidebar');
        }

        // Apply to header
        const headerHeight = document.getElementById('header_logo_height');
        const headerWidth = document.getElementById('header_logo_width');
        if (headerHeight && headerWidth) {
            headerHeight.value = headerSize;
            headerWidth.value = headerSize;
            updateLogoPreview('header');
        }
    }

    function resetToDefault() {
        // Reset sidebar to 80x80
        const sidebarHeight = document.getElementById('sidebar_logo_height');
        const sidebarWidth = document.getElementById('sidebar_logo_width');
        if (sidebarHeight && sidebarWidth) {
            sidebarHeight.value = 80;
            sidebarWidth.value = 80;
            updateLogoPreview('sidebar');
        }

        // Reset header to 40x40
        const headerHeight = document.getElementById('header_logo_height');
        const headerWidth = document.getElementById('header_logo_width');
        if (headerHeight && headerWidth) {
            headerHeight.value = 40;
            headerWidth.value = 40;
            updateLogoPreview('header');
        }

        // Reset aspect ratio checkboxes
        const sidebarAspect = document.getElementById('sidebar_maintain_aspect');
        const headerAspect = document.getElementById('header_maintain_aspect');
        if (sidebarAspect) sidebarAspect.checked = true;
        if (headerAspect) headerAspect.checked = true;
    }

    // Reset logo function
    function resetLogo() {
        if (confirm('Are you sure you want to reset the logo to default?')) {
            // Create a form to submit the reset request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?php echo BASE_URL; ?>settings/update';

            const formType = document.createElement('input');
            formType.type = 'hidden';
            formType.name = 'form_type';
            formType.value = 'reset_logo';

            form.appendChild(formType);
            document.body.appendChild(form);
            form.submit();
        }
    }

    // Real-time preview updates
    document.addEventListener('DOMContentLoaded', function() {
        const navPreviewText = document.getElementById('navPreviewText');
        const headerPreviewText = document.getElementById('headerPreviewText');

        // Check if user is super admin
        const isSuperAdmin = <?php echo (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'super_admin') ? 'true' : 'false'; ?>;

        if (isSuperAdmin) {
            // Super Admin: Update navigation preview when company name changes
            const companyNameInput = document.getElementById('company_name');
            if (companyNameInput && navPreviewText) {
                companyNameInput.addEventListener('input', function() {
                    navPreviewText.textContent = this.value || 'ICGC International';
                });
            }

            // Super Admin: Update header preview when platform name changes
            const platformNameInput = document.getElementById('platform_name');
            if (platformNameInput && headerPreviewText) {
                platformNameInput.addEventListener('input', function() {
                    headerPreviewText.textContent = this.value || 'Church Management Platform';
                });
            }
        } else {
            // Tenant Admin: Update navigation preview when church name changes
            const churchNameInput = document.getElementById('church_name');
            if (churchNameInput && navPreviewText) {
                churchNameInput.addEventListener('input', function() {
                    navPreviewText.textContent = this.value || 'ICGC Emmanuel Temple';
                });
            }

            // Tenant Admin: Update header preview when church name changes
            if (churchNameInput && headerPreviewText) {
                churchNameInput.addEventListener('input', function() {
                    headerPreviewText.textContent = this.value || 'ICGC Emmanuel Temple';
                });
            }
        }
    });
</script>

<style>
    /* Fade-in animation */
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Custom range slider styles */
    input[type="range"] {
        -webkit-appearance: none;
        appearance: none;
        background: transparent;
        cursor: pointer;
    }

    input[type="range"]::-webkit-slider-track {
        background: #e5e7eb;
        height: 8px;
        border-radius: 4px;
    }

    input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        background: #3F7D58;
        height: 20px;
        width: 20px;
        border-radius: 50%;
        border: 2px solid #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    input[type="range"]::-webkit-slider-thumb:hover {
        background: #2c5a3f;
        transform: scale(1.1);
        transition: all 0.2s ease;
    }

    input[type="range"]::-moz-range-track {
        background: #e5e7eb;
        height: 8px;
        border-radius: 4px;
        border: none;
    }

    input[type="range"]::-moz-range-thumb {
        background: #3F7D58;
        height: 20px;
        width: 20px;
        border-radius: 50%;
        border: 2px solid #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        cursor: pointer;
    }

    input[type="range"]::-moz-range-thumb:hover {
        background: #2c5a3f;
        transform: scale(1.1);
    }

    /* Logo preview animations */
    .logo-preview-container img {
        transition: all 0.3s ease;
    }

    .logo-preview-container img:hover {
        transform: scale(1.05);
    }

    /* Form field focus effect */
    .form-group input:focus, .form-group select:focus {
        border-color: var(--primary);
        border-width: 2px;
        box-shadow: 0 0 0 2px rgba(63, 125, 88, 0.2);
        background-color: white;
    }

    /* Form group hover effect */
    .form-group:hover label {
        color: var(--primary);
        transition: all 0.3s ease;
    }

    /* Cute form styling */
    .card-form {
        transition: all 0.3s ease;
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%233f7d58' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
    }

    .card-form:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }

    /* Animation for decorative elements */
    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0px); }
    }

    /* Tab styling */
    #settingsTabs button.active {
        color: var(--primary);
        border-color: var(--primary);
        font-weight: 600;
    }

    /* File input styling */
    input[type="file"] {
        position: relative;
        cursor: pointer;
    }

    input[type="file"]::before {
        content: 'Browse';
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        background: var(--primary);
        color: white;
        padding: 0 15px;
        display: flex;
        align-items: center;
        border-top-right-radius: 0.5rem;
        border-bottom-right-radius: 0.5rem;
    }
</style>
