<?php
/**
 * Command Line Migration Runner for Church Program Planner
 * 
 * Run this from command line: php run_migration_cli.php
 * Or access via browser: http://localhost/icgc/database/migrations/run_migration_cli.php
 */

// Set database configuration - MODIFY THESE VALUES
$host = 'localhost';
$dbname = 'icgc_finance';  // Change this to your database name
$username = 'root';        // Change this to your database username
$password = '';            // Change this to your database password

echo "=== Church Program & Activities Planner Migration ===\n";
echo "Connecting to database: $dbname@$host\n";

try {
    // Create database connection
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Database connection successful\n";
    echo "Creating tables...\n\n";
    
    // 1. Program Categories Table
    $sql_categories = "CREATE TABLE IF NOT EXISTS program_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        color_code VARCHAR(7) DEFAULT '#3B82F6',
        icon VARCHAR(50) DEFAULT 'fas fa-calendar',
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $stmt = $conn->prepare($sql_categories);
    $stmt->execute();
    echo "✓ Program categories table created\n";
    
    // 2. Ministry Departments Table
    $sql_departments = "CREATE TABLE IF NOT EXISTS ministry_departments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        head_pastor_id INT NULL,
        contact_email VARCHAR(100),
        contact_phone VARCHAR(20),
        budget_allocation DECIMAL(10,2) DEFAULT 0.00,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (head_pastor_id) REFERENCES members(id) ON DELETE SET NULL
    )";
    
    $stmt = $conn->prepare($sql_departments);
    $stmt->execute();
    echo "✓ Ministry departments table created\n";
    
    // 3. Church Programs Table
    $sql_programs = "CREATE TABLE IF NOT EXISTS church_programs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        category_id INT NOT NULL,
        department_id INT NOT NULL,
        coordinator_id INT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        start_time TIME NULL,
        end_time TIME NULL,
        location VARCHAR(200),
        budget_allocated DECIMAL(10,2) DEFAULT 0.00,
        budget_spent DECIMAL(10,2) DEFAULT 0.00,
        expected_attendance INT DEFAULT 0,
        actual_attendance INT DEFAULT 0,
        status ENUM('planned', 'in_progress', 'completed', 'cancelled', 'postponed') DEFAULT 'planned',
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        is_recurring BOOLEAN DEFAULT FALSE,
        recurrence_pattern VARCHAR(50) NULL,
        requires_registration BOOLEAN DEFAULT FALSE,
        max_participants INT NULL,
        notes TEXT,
        created_by INT NOT NULL,
        approved_by INT NULL,
        approved_at DATETIME NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES program_categories(id) ON DELETE RESTRICT,
        FOREIGN KEY (department_id) REFERENCES ministry_departments(id) ON DELETE RESTRICT,
        FOREIGN KEY (coordinator_id) REFERENCES members(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
        FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_start_date (start_date),
        INDEX idx_status (status),
        INDEX idx_department (department_id),
        INDEX idx_category (category_id)
    )";
    
    $stmt = $conn->prepare($sql_programs);
    $stmt->execute();
    echo "✓ Church programs table created\n";
    
    // 4. Program Activities Table
    $sql_activities = "CREATE TABLE IF NOT EXISTS program_activities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        assigned_to INT NULL,
        start_datetime DATETIME NOT NULL,
        end_datetime DATETIME NOT NULL,
        location VARCHAR(200),
        status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
        priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
        estimated_cost DECIMAL(10,2) DEFAULT 0.00,
        actual_cost DECIMAL(10,2) DEFAULT 0.00,
        notes TEXT,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_to) REFERENCES members(id) ON DELETE SET NULL,
        INDEX idx_program (program_id),
        INDEX idx_start_datetime (start_datetime),
        INDEX idx_status (status)
    )";
    
    $stmt = $conn->prepare($sql_activities);
    $stmt->execute();
    echo "✓ Program activities table created\n";
    
    // 5. Program Participants Table
    $sql_participants = "CREATE TABLE IF NOT EXISTS program_participants (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        member_id INT NOT NULL,
        role ENUM('participant', 'volunteer', 'coordinator', 'speaker') DEFAULT 'participant',
        registration_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        attendance_status ENUM('registered', 'attended', 'absent', 'cancelled') DEFAULT 'registered',
        notes TEXT,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        UNIQUE KEY unique_program_member (program_id, member_id),
        INDEX idx_program (program_id),
        INDEX idx_member (member_id),
        INDEX idx_role (role)
    )";
    
    $stmt = $conn->prepare($sql_participants);
    $stmt->execute();
    echo "✓ Program participants table created\n";
    
    // 6. Program Resources Table
    $sql_resources = "CREATE TABLE IF NOT EXISTS program_resources (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        resource_name VARCHAR(200) NOT NULL,
        resource_type ENUM('equipment', 'material', 'venue', 'transport', 'catering', 'other') NOT NULL,
        quantity_needed INT DEFAULT 1,
        quantity_secured INT DEFAULT 0,
        estimated_cost DECIMAL(10,2) DEFAULT 0.00,
        actual_cost DECIMAL(10,2) DEFAULT 0.00,
        supplier_contact VARCHAR(200),
        status ENUM('needed', 'ordered', 'secured', 'delivered') DEFAULT 'needed',
        notes TEXT,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        INDEX idx_program (program_id),
        INDEX idx_type (resource_type),
        INDEX idx_status (status)
    )";
    
    $stmt = $conn->prepare($sql_resources);
    $stmt->execute();
    echo "✓ Program resources table created\n";
    
    // 7. Program Reminders Table
    $sql_reminders = "CREATE TABLE IF NOT EXISTS program_reminders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        reminder_type ENUM('email', 'sms', 'notification') NOT NULL,
        recipient_type ENUM('coordinator', 'participants', 'department', 'all') NOT NULL,
        message TEXT NOT NULL,
        send_datetime DATETIME NOT NULL,
        status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
        sent_at DATETIME NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        INDEX idx_program (program_id),
        INDEX idx_send_datetime (send_datetime),
        INDEX idx_status (status)
    )";
    
    $stmt = $conn->prepare($sql_reminders);
    $stmt->execute();
    echo "✓ Program reminders table created\n";
    
    // 8. Program Status History Table
    $sql_status_history = "CREATE TABLE IF NOT EXISTS program_status_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        program_id INT NOT NULL,
        old_status VARCHAR(50),
        new_status VARCHAR(50) NOT NULL,
        changed_by INT NOT NULL,
        change_reason TEXT,
        changed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (program_id) REFERENCES church_programs(id) ON DELETE CASCADE,
        FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE RESTRICT,
        INDEX idx_program (program_id),
        INDEX idx_changed_at (changed_at)
    )";
    
    $stmt = $conn->prepare($sql_status_history);
    $stmt->execute();
    echo "✓ Program status history table created\n";
    
    echo "\n=== Inserting Default Data ===\n";
    
    // Insert default categories
    $default_categories = [
        ['name' => 'Worship Services', 'description' => 'Regular worship services and special services', 'color_code' => '#8B5CF6', 'icon' => 'fas fa-pray'],
        ['name' => 'Youth Programs', 'description' => 'Programs and activities for youth ministry', 'color_code' => '#10B981', 'icon' => 'fas fa-users'],
        ['name' => 'Children Ministry', 'description' => 'Programs for children and Sunday school', 'color_code' => '#F59E0B', 'icon' => 'fas fa-child'],
        ['name' => 'Outreach & Evangelism', 'description' => 'Community outreach and evangelistic programs', 'color_code' => '#EF4444', 'icon' => 'fas fa-bullhorn'],
        ['name' => 'Training & Education', 'description' => 'Bible studies, seminars, and training programs', 'color_code' => '#3B82F6', 'icon' => 'fas fa-graduation-cap'],
        ['name' => 'Fellowship & Social', 'description' => 'Fellowship events and social gatherings', 'color_code' => '#06B6D4', 'icon' => 'fas fa-handshake'],
        ['name' => 'Special Events', 'description' => 'Conferences, revivals, and special occasions', 'color_code' => '#8B5CF6', 'icon' => 'fas fa-star'],
        ['name' => 'Fundraising', 'description' => 'Fundraising events and financial campaigns', 'color_code' => '#059669', 'icon' => 'fas fa-donate'],
        ['name' => 'Community Service', 'description' => 'Community service and charity programs', 'color_code' => '#DC2626', 'icon' => 'fas fa-heart'],
        ['name' => 'Administrative', 'description' => 'Administrative meetings and church business', 'color_code' => '#6B7280', 'icon' => 'fas fa-cogs']
    ];
    
    $insert_category = "INSERT IGNORE INTO program_categories (name, description, color_code, icon) VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($insert_category);
    
    foreach ($default_categories as $category) {
        $stmt->execute([$category['name'], $category['description'], $category['color_code'], $category['icon']]);
    }
    echo "✓ Default program categories inserted\n";
    
    // Insert default departments
    $default_departments = [
        ['name' => 'Head Pastor Office', 'description' => 'Senior pastoral leadership and oversight'],
        ['name' => 'Choir Ministry', 'description' => 'Music and worship ministry'],
        ['name' => 'Media Ministry', 'description' => 'Audio, video, and technical support'],
        ['name' => 'Ushering Ministry', 'description' => 'Welcome and hospitality services'],
        ['name' => 'Children Ministry', 'description' => 'Programs for children and families'],
        ['name' => 'Youth Ministry (New Breed)', 'description' => 'Youth programs and activities'],
        ['name' => 'Protocol Ministry', 'description' => 'Event coordination and protocol'],
        ['name' => 'Welfare Ministry', 'description' => 'Member care and welfare services'],
        ['name' => 'Intercessors Ministry', 'description' => 'Prayer and intercession ministry'],
        ['name' => 'Traffic Ministry', 'description' => 'Parking and traffic coordination'],
        ['name' => 'Administration', 'description' => 'Church administration and management'],
        ['name' => 'Instrumentalist Ministry', 'description' => 'Musical instruments and accompaniment'],
        ['name' => 'Deacons Board', 'description' => 'Deacon ministry and church governance']
    ];
    
    $insert_department = "INSERT IGNORE INTO ministry_departments (name, description) VALUES (?, ?)";
    $stmt = $conn->prepare($insert_department);
    
    foreach ($default_departments as $department) {
        $stmt->execute([$department['name'], $department['description']]);
    }
    echo "✓ Default ministry departments inserted\n";
    
    echo "\n🎉 MIGRATION COMPLETED SUCCESSFULLY! 🎉\n";
    echo "\nAll tables created:\n";
    echo "✓ program_categories\n";
    echo "✓ ministry_departments\n";
    echo "✓ church_programs\n";
    echo "✓ program_activities\n";
    echo "✓ program_participants\n";
    echo "✓ program_resources\n";
    echo "✓ program_reminders\n";
    echo "✓ program_status_history\n";
    echo "\nYou can now access the Program Planner at: http://localhost/icgc/programs\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\nPlease check your database credentials and try again.\n";
}
?>
