<?php
/**
 * QR Stats API
 * Simple endpoint for QR attendance stats without rate limiting
 */

// Set headers for JSON response
header('Content-Type: application/json');

// CRITICAL SECURITY FIX: Restrict CORS to application domain only
// The wildcard '*' is dangerous and allows any website to access this API
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false || 
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
    // If origin is not allowed, we don't set the CORS header
    // This will cause the browser to block the request
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET');

// Include bootstrap to ensure proper initialization
require_once dirname(__DIR__) . '/bootstrap.php';

try {
    // Check if token is provided
    if (!isset($_GET['token']) || empty($_GET['token'])) {
        echo json_encode(['success' => false, 'error' => 'QR session token is required']);
        exit;
    }

    $token = sanitize($_GET['token']);

    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();

    // Get QR session by token
    $query = "SELECT * FROM attendance_qr_sessions WHERE token = :token LIMIT 1";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':token', $token);
    $stmt->execute();
    $qr_session = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$qr_session) {
        echo json_encode(['success' => false, 'error' => 'Invalid QR session token']);
        exit;
    }

    // Get attendance stats
    $attendance_date = $qr_session['attendance_date'];
    $service_id = $qr_session['service_id'];

    // Get present members
    $present_query = "SELECT m.first_name, m.last_name, a.created_at, a.status
                      FROM attendance a
                      JOIN members m ON a.member_id = m.id
                      WHERE a.attendance_date = :date
                      AND a.service_id = :service_id
                      AND a.status = 'present'
                      ORDER BY a.created_at DESC
                      LIMIT 10";

    $stmt = $conn->prepare($present_query);
    $stmt->bindParam(':date', $attendance_date);
    $stmt->bindParam(':service_id', $service_id);
    $stmt->execute();
    $present_members = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get late members
    $late_query = "SELECT m.first_name, m.last_name, a.created_at, a.status
                   FROM attendance a
                   JOIN members m ON a.member_id = m.id
                   WHERE a.attendance_date = :date
                   AND a.service_id = :service_id
                   AND a.status = 'late'
                   ORDER BY a.created_at DESC
                   LIMIT 10";

    $stmt = $conn->prepare($late_query);
    $stmt->bindParam(':date', $attendance_date);
    $stmt->bindParam(':service_id', $service_id);
    $stmt->execute();
    $late_members = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Combine and sort by most recent
    $all_members = array_merge($present_members, $late_members);
    usort($all_members, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

    // Show only the last 10
    $all_members = array_slice($all_members, 0, 10);

    // Generate HTML for stats
    ob_start();
    ?>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div class="bg-green-50 rounded-md p-4 border border-green-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-green-600">Present</p>
                    <p class="text-2xl font-bold text-green-900"><?php echo count($present_members); ?></p>
                </div>
                <div class="p-2 bg-green-100 rounded-full">
                    <i class="fas fa-check text-green-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-yellow-50 rounded-md p-4 border border-yellow-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-yellow-600">Late</p>
                    <p class="text-2xl font-bold text-yellow-900"><?php echo count($late_members); ?></p>
                </div>
                <div class="p-2 bg-yellow-100 rounded-full">
                    <i class="fas fa-clock text-yellow-600"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="flex justify-between items-center mb-2">
        <h3 class="text-md font-medium text-gray-700">Recent Activity</h3>
        <a href="<?php echo BASE_URL; ?>attendance/stats-dashboard?token=<?php echo $token; ?>"
           class="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center">
            <i class="fas fa-list mr-1"></i>
            Recent Attendance
        </a>
    </div>

    <div class="overflow-hidden rounded-md border border-gray-200">
        <div class="px-4 py-6 bg-gray-50 text-sm text-gray-600 text-center">
            <i class="fas fa-users text-gray-400 text-2xl mb-3"></i>
            <p class="mb-3">
                <?php if (!empty($all_members)): ?>
                    <strong><?php echo count($all_members); ?> members</strong> have marked attendance.
                <?php else: ?>
                    No attendance records yet.
                <?php endif; ?>
            </p>
            <p class="text-xs text-gray-500 mb-4">
                For detailed member list and comprehensive statistics, use the full dashboard.
            </p>
            <a href="<?php echo BASE_URL; ?>attendance/stats-dashboard?token=<?php echo $token; ?>"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-external-link-alt mr-2"></i>
                View Full Dashboard
            </a>
        </div>
    </div>
    <?php
    $html = ob_get_clean();

    // Return JSON response
    echo json_encode([
        'success' => true,
        'stats' => [
            'present_count' => count($present_members),
            'late_count' => count($late_members),
            'total_count' => count($present_members) + count($late_members)
        ],
        'html' => $html
    ]);

} catch (Exception $e) {
    error_log("QR Stats API Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load stats: ' . $e->getMessage()
    ]);
}
?>
