# Assets Directory

This directory contains all static assets for the ICGC Emmanuel Temple Church Management System.

## Directory Structure

- **css/**: Contains all CSS stylesheets
  - `style.css`: Main stylesheet with custom variables and utility classes

- **js/**: Contains all JavaScript files
  - `main.js`: Main JavaScript file with common functionality

- **images/**: Contains all images used in the application
  - Logos, icons, and other graphical elements
  - Placeholder images for testing

- **fonts/**: Contains custom fonts (if any)

## Usage Guidelines

1. **CSS**:
   - Use the predefined color variables for consistency
   - Follow the naming conventions established in `style.css`
   - Add new styles to the appropriate section in the stylesheet

2. **JavaScript**:
   - Keep code modular and organized
   - Document functions with JSDoc comments
   - Use event delegation where appropriate

3. **Images**:
   - Optimize images before adding them to the repository
   - Use descriptive filenames
   - Consider using SVG for icons when possible

4. **Fonts**:
   - Include both WOFF and WOFF2 formats for best browser compatibility
   - Document any licensing requirements

## Color Scheme

The application uses a vibrant green color palette:

- Primary: `#4CBF26` (Darker green)
- Primary Light: `#7ED321` (Medium green)
- Primary Dark: `#3A9D1E` (Darker shade of primary)
- Secondary: `#7ED321` (Medium green)
- Secondary Light: `#B8E986` (Light lime green)
- Secondary Dark: `#5EB31C` (Darker shade of secondary)

The sidebar uses a gradient background with these three colors:
- Left: `#4CBF26` (Darker green)
- Middle: `#7ED321` (Medium green)
- Right: `#B8E986` (Light lime green)

These colors are defined as CSS variables in `style.css` and as Tailwind theme extensions.
