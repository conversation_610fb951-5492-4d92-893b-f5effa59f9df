<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php
    // Get church name from settings for dynamic page title
    require_once 'models/Setting.php';
    require_once 'config/database.php';
    try {
        $database = new Database();
        $setting = new Setting($database->getConnection());
        $layout_settings = $setting->getAllAsArray();
        $church_name = $layout_settings['church_name'] ?? 'ICGC Emmanuel Temple';
    } catch (Exception $e) {
        $church_name = 'ICGC Emmanuel Temple';
    }
    ?>
    <title>404 - Page Not Found | <?php echo htmlspecialchars($church_name); ?></title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <div class="flex-1 flex items-center justify-center">
        <div class="text-center px-4">
            <h1 class="text-6xl font-bold text-indigo-600 mb-4">404</h1>
            <h2 class="text-3xl font-semibold text-gray-800 mb-6">Page Not Found</h2>
            <p class="text-gray-600 mb-8">The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.</p>
            <a href="<?php echo BASE_URL; ?>" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-home mr-2"></i> Go to Homepage
            </a>
        </div>
    </div>
    
    <footer class="bg-white text-gray-600 py-4 text-center shadow-inner">
        <p>&copy; <?php echo date('Y'); ?> ICGC Emmanuel Temple. All rights reserved.</p>
    </footer>
</body>
</html>
