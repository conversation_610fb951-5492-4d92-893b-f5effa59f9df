<?php
/**
 * API Response Utility
 * 
 * Provides standardized methods for API responses in JSON format
 */

/**
 * Send a JSON response with appropriate headers
 * 
 * @param mixed $data Response data
 * @param int $status_code HTTP status code
 * @param string $message Optional message
 * @return void
 */
function api_response($data, $status_code = 200, $message = '') {
    // Set content type header
    header('Content-Type: application/json');
    
    // Set HTTP response code
    http_response_code($status_code);
    
    // Prepare response array
    $response = [
        'status' => $status_code < 400 ? 'success' : 'error',
        'code' => $status_code
    ];
    
    // Add message if provided
    if (!empty($message)) {
        $response['message'] = $message;
    }
    
    // Add data if not null
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    // Output JSON response
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Send a successful JSON response
 * 
 * @param mixed $data Response data
 * @param string $message Success message
 * @param int $status_code HTTP status code (default 200)
 * @return void
 */
function api_success($data = null, $message = 'Operation successful', $status_code = 200) {
    api_response($data, $status_code, $message);
}

/**
 * Send a created (201) JSON response
 * 
 * @param mixed $data Created resource data
 * @param string $message Success message
 * @return void
 */
function api_created($data = null, $message = 'Resource created successfully') {
    api_response($data, 201, $message);
}

/**
 * Send an error JSON response
 * 
 * @param string $message Error message
 * @param int $status_code HTTP status code (default 400)
 * @param mixed $errors Additional error details
 * @return void
 */
function api_error($message = 'An error occurred', $status_code = 400, $errors = null) {
    $data = null;
    if ($errors !== null) {
        $data = ['errors' => $errors];
    }
    api_response($data, $status_code, $message);
}

/**
 * Send a not found (404) JSON response
 * 
 * @param string $message Not found message
 * @return void
 */
function api_not_found($message = 'Resource not found') {
    api_error($message, 404);
}

/**
 * Send an unauthorized (401) JSON response
 * 
 * @param string $message Unauthorized message
 * @return void
 */
function api_unauthorized($message = 'Unauthorized access') {
    api_error($message, 401);
}

/**
 * Send a forbidden (403) JSON response
 * 
 * @param string $message Forbidden message
 * @return void
 */
function api_forbidden($message = 'Access forbidden') {
    api_error($message, 403);
}

/**
 * Send a validation error (422) JSON response
 * 
 * @param array $errors Validation errors
 * @param string $message Validation error message
 * @return void
 */
function api_validation_error($errors, $message = 'Validation failed') {
    api_error($message, 422, $errors);
}

/**
 * Send a server error (500) JSON response
 * 
 * @param string $message Server error message
 * @param mixed $debug_info Debug information (only included in development)
 * @return void
 */
function api_server_error($message = 'Internal server error', $debug_info = null) {
    $data = null;
    
    // Include debug info only in development environment
    if (defined('APP_DEBUG') && APP_DEBUG && $debug_info !== null) {
        $data = ['debug' => $debug_info];
    }
    
    api_response($data, 500, $message);
}

/**
 * Check API key against database
 * 
 * @return bool True if valid API key
 */
function api_check_key() {
    // Get API key from header or query parameter
    $api_key = isset($_SERVER['HTTP_X_API_KEY']) ? $_SERVER['HTTP_X_API_KEY'] : null;
    
    if (empty($api_key) && isset($_GET['api_key'])) {
        $api_key = $_GET['api_key'];
    }
    
    if (empty($api_key)) {
        api_unauthorized('API key is required');
        return false;
    }
    
    // Validate API key against database
    $db = db_connect();
    $stmt = $db->prepare("SELECT * FROM api_keys WHERE api_key = ? AND status = 'active'");
    $stmt->execute([$api_key]);
    $key = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$key) {
        api_unauthorized('Invalid API key');
        return false;
    }
    
    // Update last used timestamp
    $update = $db->prepare("UPDATE api_keys SET last_used = NOW() WHERE id = ?");
    $update->execute([$key['id']]);
    
    return true;
} 