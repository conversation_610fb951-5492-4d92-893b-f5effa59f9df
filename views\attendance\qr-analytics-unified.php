<?php
/**
 * Unified QR Analytics Dashboard - Single Page Layout
 */
?>

<!-- QR Analytics Dashboard - Compact Professional Layout -->
<div class="w-full space-y-3">

<!-- Debug Info (Remove in production) -->
<?php if (isset($_GET['debug'])): ?>
<div class="bg-yellow-100 border border-yellow-300 rounded p-4 mb-4">
    <h4 class="font-bold">Debug Info:</h4>
    <p><strong>Member Engagement Data:</strong></p>
    <pre><?php print_r($analytics_data['member_engagement'] ?? 'Not set'); ?></pre>
    <p><strong>Gender Distribution Data:</strong></p>
    <pre><?php print_r($analytics_data['gender_distribution'] ?? 'Not set'); ?></pre>
    <p><strong>Department Distribution Data:</strong></p>
    <pre><?php print_r($analytics_data['department_distribution'] ?? 'Not set'); ?></pre>
</div>
<?php endif; ?>

    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-primary to-primary-dark text-white rounded-xl p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold mb-2">QR Analytics Dashboard</h1>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white border border-white/30">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <?php echo ucfirst(htmlspecialchars($current_period['type'])); ?> Analysis
                    </span>
                    <span class="text-green-200">•</span>
                    <span class="text-lg font-semibold">
                        <?php echo htmlspecialchars($current_period['value']); ?>
                    </span>
                </div>
            </div>
            <div class="flex space-x-3">
                <button onclick="exportAnalytics()" class="inline-flex items-center px-4 py-2.5 bg-white/20 text-white font-medium rounded-lg hover:bg-white/30 transition-colors duration-200">
                    <i class="fas fa-download mr-2"></i>
                    Export
                </button>
                <a href="<?php echo BASE_URL; ?>attendance/qr" class="inline-flex items-center px-5 py-2.5 bg-secondary text-primary font-semibold rounded-lg hover:bg-secondary-dark transition-colors duration-200">
                    <i class="fas fa-qrcode mr-2"></i>
                    Generate QR
                </a>
            </div>
        </div>
    </div>

    <!-- Period Selector -->
    <div class="bg-white border border-gray-100 rounded-xl p-5">
        <form method="GET" action="<?php echo BASE_URL; ?>attendance/qr-analytics" class="space-y-4">
            <div class="flex flex-wrap items-end gap-4">
                <div class="flex-1 min-w-48">
                    <label for="period" class="block text-sm font-semibold text-gray-700 mb-2">Period Type</label>
                    <select name="period" id="period" class="w-full px-3 py-2.5 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white text-sm shadow-sm" onchange="handlePeriodChange()">
                        <option value="weekly" <?php echo $current_period['type'] === 'weekly' ? 'selected' : ''; ?>>Weekly Analysis</option>
                        <option value="monthly" <?php echo $current_period['type'] === 'monthly' ? 'selected' : ''; ?>>Monthly Analysis</option>
                        <option value="quarterly" <?php echo $current_period['type'] === 'quarterly' ? 'selected' : ''; ?>>Quarterly Analysis</option>
                        <option value="yearly" <?php echo $current_period['type'] === 'yearly' ? 'selected' : ''; ?>>Yearly Analysis</option>
                    </select>
                </div>

                <div class="flex-1 min-w-48">
                    <label for="value" class="block text-sm font-semibold text-gray-700 mb-2">Period Value</label>
                    <input type="text" name="value" id="value" class="w-full px-3 py-2.5 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white text-sm shadow-sm"
                           value="<?php echo htmlspecialchars($current_period['value']); ?>"
                           placeholder="e.g., 2025-Q3">
                </div>

                <div>
                    <button type="submit" class="px-5 py-2.5 bg-primary text-white font-medium rounded-lg hover:bg-primary-dark transition-colors duration-200 text-sm">
                        <i class="fas fa-sync-alt mr-2"></i>Update
                    </button>
                </div>
            </div>

            <!-- Format Hint -->
            <p class="text-xs text-gray-500" id="formatHint">
                <i class="fas fa-info-circle mr-1"></i>
                Format:
                <span class="font-mono bg-gray-100 px-2 py-0.5 rounded">
                    <?php
                    switch($current_period['type']) {
                        case 'weekly': echo 'YYYY-WNN (e.g., 2025-W01)'; break;
                        case 'monthly': echo 'YYYY-MM (e.g., 2025-07)'; break;
                        case 'quarterly': echo 'YYYY-QN (e.g., 2025-Q3)'; break;
                        case 'yearly': echo 'YYYY (e.g., 2025)'; break;
                        default: echo 'depends on period type';
                    }
                    ?>
                </span>
            </p>

            <!-- Quick Access Navigation -->
            <div class="pt-3 border-t border-gray-100">
                <div class="flex flex-wrap gap-2">
                    <button onclick="goToCurrentMonth()" type="button"
                            class="px-3 py-1.5 text-xs font-medium bg-gray-50 text-gray-700 border border-gray-300 rounded-md hover:bg-primary hover:text-white transition-colors duration-200">
                        <i class="fas fa-calendar mr-1"></i>Current Month
                    </button>
                    <button onclick="goToCurrentQuarter()" type="button"
                            class="px-3 py-1.5 text-xs font-medium bg-gray-50 text-gray-700 border border-gray-300 rounded-md hover:bg-primary hover:text-white transition-colors duration-200">
                        <i class="fas fa-calendar-quarter mr-1"></i>Current Quarter
                    </button>
                    <button onclick="goToCurrentYear()" type="button"
                            class="px-3 py-1.5 text-xs font-medium bg-gray-50 text-gray-700 border border-gray-300 rounded-md hover:bg-primary hover:text-white transition-colors duration-200">
                        <i class="fas fa-calendar-year mr-1"></i>Current Year
                    </button>
                    <button onclick="goToQ3Data()" type="button"
                            class="px-3 py-1.5 text-xs font-medium bg-secondary text-primary border border-secondary-dark rounded-md hover:bg-secondary-dark transition-colors duration-200">
                        <i class="fas fa-star mr-1"></i>Q3 2025
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Member Patterns Button -->
    <div class="mb-6">
        <a href="<?php echo BASE_URL; ?>attendance/patterns" class="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm">
            <i class="fas fa-users-cog mr-2"></i>
            Member Patterns
        </a>
    </div>

    <!-- Overview Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- QR Sessions -->
        <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
            <div class="flex items-center justify-between mb-3">
                <div class="p-2 bg-primary/10 rounded-lg">
                    <i class="fas fa-qrcode text-lg text-primary"></i>
                </div>
                <span class="text-xs font-medium bg-gray-100 text-gray-600 px-2 py-1 rounded-full">SESSIONS</span>
            </div>
            <div class="text-2xl font-bold mb-1 text-gray-900">
                <?php echo number_format($overview['total_sessions'] ?? 0); ?>
            </div>
            <div class="text-sm text-gray-600">QR Sessions Created</div>
        </div>

        <!-- QR Attendance -->
        <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
            <div class="flex items-center justify-between mb-3">
                <div class="p-2 bg-primary/10 rounded-lg">
                    <i class="fas fa-users text-lg text-primary"></i>
                </div>
                <span class="text-xs font-medium bg-gray-100 text-gray-600 px-2 py-1 rounded-full">ATTENDANCE</span>
            </div>
            <div class="text-2xl font-bold mb-1 text-gray-900">
                <?php echo number_format($overview['total_qr_attendance'] ?? 0); ?>
            </div>
            <div class="text-sm text-gray-600">Total QR Attendance</div>
        </div>

        <!-- Unique Members -->
        <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
            <div class="flex items-center justify-between mb-3">
                <div class="p-2 bg-primary/10 rounded-lg">
                    <i class="fas fa-user-check text-lg text-primary"></i>
                </div>
                <span class="text-xs font-medium bg-gray-100 text-gray-600 px-2 py-1 rounded-full">MEMBERS</span>
            </div>
            <div class="text-2xl font-bold mb-1 text-gray-900">
                <?php echo number_format($overview['unique_members'] ?? 0); ?>
            </div>
            <div class="text-sm text-gray-600">Unique Members</div>
        </div>

        <!-- Average per Session -->
        <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
            <div class="flex items-center justify-between mb-3">
                <div class="p-2 bg-secondary/20 rounded-lg">
                    <i class="fas fa-chart-line text-lg text-primary"></i>
                </div>
                <span class="text-xs font-medium bg-gray-100 text-gray-600 px-2 py-1 rounded-full">AVERAGE</span>
            </div>
            <div class="text-2xl font-bold mb-1 text-gray-900">
                <?php echo number_format($overview['avg_attendance_per_session'] ?? 0, 1); ?>
            </div>
            <div class="text-sm text-gray-600">Avg per Session</div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- Attendance Trends -->
        <div class="bg-white border border-gray-100 rounded-xl p-5">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Attendance Trends</h3>
                    <p class="text-sm text-gray-500">QR attendance over time</p>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span class="text-xs text-gray-600">QR Data</span>
                </div>
            </div>
            <div class="h-64 bg-gray-50 rounded-lg p-3">
                <canvas id="trendsChart" class="w-full h-full"></canvas>
            </div>
        </div>

        <!-- Usage Patterns -->
        <div class="bg-white border border-gray-100 rounded-xl p-5">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Usage Patterns</h3>
                    <p class="text-sm text-gray-500">Time distribution</p>
                </div>
                <div class="text-xs text-gray-500">
                    <i class="fas fa-clock mr-1"></i>Hourly
                </div>
            </div>
            <div class="h-64 bg-gray-50 rounded-lg p-3">
                <canvas id="patternsChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Member Engagement Analysis -->
    <div class="bg-white border border-gray-100 rounded-xl p-5">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-lg font-semibold text-gray-900">Member Engagement Analysis</h3>
            <div class="flex space-x-2">
                <select id="engagementPeriod" class="text-xs border border-gray-300 rounded px-2 py-1">
                    <option value="30">Last 30 days</option>
                    <option value="60">Last 60 days</option>
                    <option value="90">Last 90 days</option>
                </select>
                <button onclick="refreshEngagement()" class="text-xs bg-primary text-white px-3 py-1 rounded hover:bg-primary-dark">
                    <i class="fas fa-sync-alt mr-1"></i>Refresh
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-5">
            <!-- Regular Attendees -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <div class="p-2 bg-green-500 rounded-lg">
                        <i class="fas fa-medal text-white text-sm"></i>
                    </div>
                    <span class="text-xs font-medium bg-green-200 text-green-700 px-2 py-1 rounded-full">REGULAR</span>
                </div>
                <div class="text-xl font-bold text-green-700 mb-1">
                    <?php
                    $regular_count = $analytics_data['member_engagement']['regular_attendees'] ?? 0;
                    echo number_format($regular_count);
                    ?>
                </div>
                <div class="text-sm text-green-600">Regular Attendees</div>
                <div class="text-xs text-green-500 mt-1">8+ QR attendances</div>
            </div>

            <!-- Occasional Attendees -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <div class="p-2 bg-yellow-500 rounded-lg">
                        <i class="fas fa-clock text-white text-sm"></i>
                    </div>
                    <span class="text-xs font-medium bg-yellow-200 text-yellow-700 px-2 py-1 rounded-full">OCCASIONAL</span>
                </div>
                <div class="text-xl font-bold text-yellow-700 mb-1">
                    <?php
                    $occasional_count = $analytics_data['member_engagement']['occasional_attendees'] ?? 0;
                    echo number_format($occasional_count);
                    ?>
                </div>
                <div class="text-sm text-yellow-600">Occasional Attendees</div>
                <div class="text-xs text-yellow-500 mt-1">3-7 QR attendances</div>
            </div>

            <!-- At-Risk Members -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <div class="p-2 bg-red-500 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                    </div>
                    <span class="text-xs font-medium bg-red-200 text-red-700 px-2 py-1 rounded-full">AT-RISK</span>
                </div>
                <div class="text-xl font-bold text-red-700 mb-1">
                    <?php
                    $at_risk_count = $analytics_data['member_engagement']['at_risk_members'] ?? 0;
                    echo number_format($at_risk_count);
                    ?>
                </div>
                <div class="text-sm text-red-600">At-Risk Members</div>
                <div class="text-xs text-red-500 mt-1">1-2 QR attendances</div>
            </div>
        </div>

    </div>

    <!-- Gender Distribution -->
    <div class="bg-white border border-gray-100 rounded-xl p-5">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-lg font-semibold text-gray-900">Gender Distribution</h3>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                <i class="fas fa-users mr-1"></i>Demographics
            </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Gender Stats -->
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-500 rounded-lg mr-3">
                            <i class="fas fa-male text-white"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-blue-700">
                                <?php echo number_format($analytics_data['gender_distribution']['male_count'] ?? 0); ?>
                            </div>
                            <div class="text-sm text-blue-600">Male Attendees</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-semibold text-blue-700">
                            <?php echo number_format($analytics_data['gender_distribution']['male_percentage'] ?? 0, 1); ?>%
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between p-4 bg-pink-50 border border-pink-200 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-2 bg-pink-500 rounded-lg mr-3">
                            <i class="fas fa-female text-white"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-pink-700">
                                <?php echo number_format($analytics_data['gender_distribution']['female_count'] ?? 0); ?>
                            </div>
                            <div class="text-sm text-pink-600">Female Attendees</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-semibold text-pink-700">
                            <?php echo number_format($analytics_data['gender_distribution']['female_percentage'] ?? 0, 1); ?>%
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gender Chart -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-semibold text-gray-700 mb-3">Gender Distribution Chart</h4>
                <div class="h-48">
                    <canvas id="genderChart" class="w-full h-full"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Distribution -->
    <div class="bg-white border border-gray-100 rounded-xl p-5">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-lg font-semibold text-gray-900">Department Distribution</h3>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                <i class="fas fa-sitemap mr-1"></i>Departments
            </span>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Department Stats -->
            <div class="space-y-3">
                <?php
                $departments = $analytics_data['department_distribution'] ?? [];
                $color_classes = [
                    'bg-blue-50', 'bg-green-50', 'bg-yellow-50', 'bg-purple-50', 'bg-indigo-50',
                    'bg-pink-50', 'bg-red-50', 'bg-orange-50', 'bg-teal-50', 'bg-cyan-50',
                    'bg-lime-50', 'bg-emerald-50', 'bg-violet-50', 'bg-fuchsia-50', 'bg-gray-50'
                ];
                $border_classes = [
                    'border-blue-200', 'border-green-200', 'border-yellow-200', 'border-purple-200', 'border-indigo-200',
                    'border-pink-200', 'border-red-200', 'border-orange-200', 'border-teal-200', 'border-cyan-200',
                    'border-lime-200', 'border-emerald-200', 'border-violet-200', 'border-fuchsia-200', 'border-gray-200'
                ];
                $icon_classes = [
                    'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500', 'bg-indigo-500',
                    'bg-pink-500', 'bg-red-500', 'bg-orange-500', 'bg-teal-500', 'bg-cyan-500',
                    'bg-lime-500', 'bg-emerald-500', 'bg-violet-500', 'bg-fuchsia-500', 'bg-gray-500'
                ];
                $text_classes = [
                    'text-blue-700', 'text-green-700', 'text-yellow-700', 'text-purple-700', 'text-indigo-700',
                    'text-pink-700', 'text-red-700', 'text-orange-700', 'text-teal-700', 'text-cyan-700',
                    'text-lime-700', 'text-emerald-700', 'text-violet-700', 'text-fuchsia-700', 'text-gray-700'
                ];

                $index = 0;
                foreach ($departments as $dept => $data):
                    $color_index = $index % count($color_classes);
                    $bg_class = $color_classes[$color_index];
                    $border_class = $border_classes[$color_index];
                    $icon_class = $icon_classes[$color_index];
                    $text_class = $text_classes[$color_index];
                    $index++;
                ?>
                <div class="flex items-center justify-between p-3 <?php echo $bg_class; ?> border <?php echo $border_class; ?> rounded-lg">
                    <div class="flex items-center">
                        <div class="p-2 <?php echo $icon_class; ?> rounded-lg mr-3">
                            <i class="fas fa-users text-white text-sm"></i>
                        </div>
                        <div>
                            <div class="text-sm font-semibold <?php echo $text_class; ?>"><?php echo htmlspecialchars($dept); ?></div>
                            <div class="text-xs text-gray-600"><?php echo number_format($data['count']); ?> members</div>
                        </div>
                    </div>
                    <div class="text-sm font-bold <?php echo $text_class; ?>">
                        <?php echo number_format($data['percentage'], 1); ?>%
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Department Chart -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-semibold text-gray-700 mb-3">Department Distribution Chart</h4>
                <div class="h-64">
                    <canvas id="departmentChart" class="w-full h-full"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Center -->
    <div class="bg-white border border-gray-100 rounded-xl p-5">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                <i class="fas fa-tools mr-1"></i>Analytics Tools
            </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Detailed Report -->
            <button onclick="generateDetailedReport()" class="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors duration-200">
                <div class="p-3 bg-green-500 rounded-full mb-3">
                    <i class="fas fa-file-alt text-white"></i>
                </div>
                <span class="text-sm font-medium text-green-700">Detailed Report</span>
                <span class="text-xs text-green-600 mt-1">Full Analytics</span>
            </button>

            <!-- Member Insights -->
            <a href="<?php echo BASE_URL; ?>attendance/patterns" class="flex flex-col items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors duration-200">
                <div class="p-3 bg-purple-500 rounded-full mb-3">
                    <i class="fas fa-users-cog text-white"></i>
                </div>
                <span class="text-sm font-medium text-purple-700">Member Patterns</span>
                <span class="text-xs text-purple-600 mt-1">Attendance Insights</span>
            </a>
        </div>
    </div>

    <!-- Quarterly Performance -->
    <?php if (!empty($quarterly_performance)): ?>
    <div class="bg-white border border-gray-100 rounded-xl p-5">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-lg font-semibold text-gray-900">Quarterly Performance</h3>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                <i class="fas fa-calendar-alt mr-1"></i>Historical Data
            </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <?php foreach ($quarterly_performance as $quarter => $data): ?>
                <?php if (($data['qr_attendance'] ?? 0) > 0 || ($data['total_attendance'] ?? 0) > 0): ?>
                <div class="relative bg-white border border-gray-200 rounded-lg p-4 hover:border-primary hover:shadow-sm transition-all duration-200">
                    <!-- Quarter Header -->
                    <div class="text-center mb-4">
                        <div class="inline-flex items-center justify-center w-12 h-12 bg-primary rounded-full mb-2">
                            <span class="text-white font-bold text-sm"><?php echo htmlspecialchars($quarter); ?></span>
                        </div>
                        <h4 class="text-lg font-bold text-gray-900"><?php echo htmlspecialchars($data['year']); ?></h4>
                        <p class="text-xs text-gray-600"><?php echo htmlspecialchars($data['period']); ?></p>
                    </div>

                    <!-- Compact Metrics -->
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">QR Attendance</span>
                            <span class="font-semibold text-primary"><?php echo number_format($data['qr_attendance']); ?></span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">Total</span>
                            <span class="font-semibold text-gray-900"><?php echo number_format($data['total_attendance']); ?></span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">Rate</span>
                            <span class="font-semibold text-primary-dark"><?php echo number_format($data['adoption_rate'], 1); ?>%</span>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <a href="<?php echo BASE_URL; ?>attendance/qr-analytics?period=quarterly&value=<?php echo $data['year']; ?>-<?php echo $quarter; ?>"
                       class="w-full inline-flex items-center justify-center px-3 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-primary-dark transition-colors duration-200">
                        <i class="fas fa-eye mr-1"></i>View
                    </a>
                </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

</div>

<script>
// Analytics data from PHP
const analyticsData = <?php echo json_encode($analytics_data ?? []); ?>;

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initTrendsChart();
    initPatternsChart();
    initGenderChart();
    initDepartmentChart();
});

// Handle period type change
function handlePeriodChange() {
    updatePeriodValue();
    // Auto-update without confirmation dialog
}

// Update period value based on type
function updatePeriodValue() {
    const periodType = document.getElementById('period').value;
    const valueInput = document.getElementById('value');
    const formatHint = document.getElementById('formatHint');
    const currentYear = new Date().getFullYear();
    const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
    const currentQuarter = Math.ceil((new Date().getMonth() + 1) / 3);
    const currentWeek = getWeekNumber(new Date());

    // Update placeholder, format hint, and suggest appropriate value
    switch(periodType) {
        case 'weekly':
            const weekValue = currentYear + '-W' + String(currentWeek).padStart(2, '0');
            valueInput.placeholder = weekValue;
            formatHint.innerHTML = 'Format: <span class="font-mono">YYYY-WNN (e.g., ' + weekValue + ')</span>';
            // If current value doesn't match weekly format, suggest current week
            if (!valueInput.value.match(/^\d{4}-W\d{1,2}$/)) {
                valueInput.value = weekValue;
            }
            break;
        case 'monthly':
            const monthValue = currentYear + '-' + currentMonth;
            valueInput.placeholder = monthValue;
            formatHint.innerHTML = 'Format: <span class="font-mono">YYYY-MM (e.g., ' + monthValue + ')</span>';
            // If current value doesn't match monthly format, suggest current month
            if (!valueInput.value.match(/^\d{4}-\d{2}$/)) {
                valueInput.value = monthValue;
            }
            break;
        case 'quarterly':
            const quarterValue = currentYear + '-Q' + currentQuarter;
            valueInput.placeholder = quarterValue;
            formatHint.innerHTML = 'Format: <span class="font-mono">YYYY-QN (e.g., ' + quarterValue + ')</span>';
            // If current value doesn't match quarterly format, suggest current quarter
            if (!valueInput.value.match(/^\d{4}-Q[1-4]$/)) {
                valueInput.value = quarterValue;
            }
            break;
        case 'yearly':
            const yearValue = currentYear.toString();
            valueInput.placeholder = yearValue;
            formatHint.innerHTML = 'Format: <span class="font-mono">YYYY (e.g., ' + yearValue + ')</span>';
            // If current value doesn't match yearly format, suggest current year
            if (!valueInput.value.match(/^\d{4}$/)) {
                valueInput.value = yearValue;
            }
            break;
    }
}

// Get week number of the year
function getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(),0,1));
    return Math.ceil((((d - yearStart) / 86400000) + 1)/7);
}

// Quick navigation functions
function goToCurrentMonth() {
    const currentYear = new Date().getFullYear();
    const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
    window.location.href = '<?php echo BASE_URL; ?>attendance/qr-analytics?period=monthly&value=' + currentYear + '-' + currentMonth;
}

function goToCurrentQuarter() {
    const currentYear = new Date().getFullYear();
    const currentQuarter = Math.ceil((new Date().getMonth() + 1) / 3);
    window.location.href = '<?php echo BASE_URL; ?>attendance/qr-analytics?period=quarterly&value=' + currentYear + '-Q' + currentQuarter;
}

function goToCurrentYear() {
    const currentYear = new Date().getFullYear();
    window.location.href = '<?php echo BASE_URL; ?>attendance/qr-analytics?period=yearly&value=' + currentYear;
}

function goToQ3Data() {
    window.location.href = '<?php echo BASE_URL; ?>attendance/qr-analytics?period=quarterly&value=2025-Q3';
}

// Initialize gender chart
function initGenderChart() {
    const ctx = document.getElementById('genderChart').getContext('2d');
    const genderData = analyticsData.gender_distribution || {};

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Male', 'Female'],
            datasets: [{
                data: [
                    genderData.male_count || 0,
                    genderData.female_count || 0
                ],
                backgroundColor: [
                    '#3B82F6', // Blue for male
                    '#EC4899'  // Pink for female
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Initialize department chart
function initDepartmentChart() {
    const ctx = document.getElementById('departmentChart').getContext('2d');
    const departmentData = analyticsData.department_distribution || {};

    const labels = Object.keys(departmentData);
    const data = labels.map(dept => departmentData[dept]?.count || 0);

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Members',
                data: data,
                backgroundColor: [
                    '#3B82F6', // Blue
                    '#10B981', // Green
                    '#F59E0B', // Yellow
                    '#8B5CF6', // Purple
                    '#6366F1', // Indigo
                    '#6B7280'  // Gray
                ],
                borderWidth: 1,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// Export analytics data
function exportAnalytics() {
    const periodType = document.getElementById('period').value;
    const periodValue = document.getElementById('value').value;

    const url = `<?php echo BASE_URL; ?>attendance/qr-analytics-export?period=${periodType}&value=${periodValue}&format=comprehensive`;
    window.open(url, '_blank');
}

// Refresh engagement data
function refreshEngagement() {
    const period = document.getElementById('engagementPeriod').value;
    const currentPeriodType = document.getElementById('period').value;
    const currentPeriodValue = document.getElementById('value').value;

    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Loading...';
    button.disabled = true;

    // Simulate refresh (in real implementation, this would fetch new data)
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;

        // Reload page with engagement period parameter
        const url = new URL(window.location);
        url.searchParams.set('engagement_period', period);
        window.location.href = url.toString();
    }, 1000);
}

// Compare with previous period
function comparePeriods() {
    const currentPeriod = document.getElementById('value').value;
    const periodType = document.getElementById('period').value;

    // Calculate previous period
    let previousPeriod;
    if (periodType === 'monthly') {
        const [year, month] = currentPeriod.split('-');
        const prevMonth = parseInt(month) - 1;
        if (prevMonth === 0) {
            previousPeriod = `${parseInt(year) - 1}-12`;
        } else {
            previousPeriod = `${year}-${String(prevMonth).padStart(2, '0')}`;
        }
    } else if (periodType === 'quarterly') {
        const [year, quarter] = currentPeriod.split('-Q');
        const prevQuarter = parseInt(quarter) - 1;
        if (prevQuarter === 0) {
            previousPeriod = `${parseInt(year) - 1}-Q4`;
        } else {
            previousPeriod = `${year}-Q${prevQuarter}`;
        }
    }

    if (previousPeriod) {
        const url = `<?php echo url('attendance/qr-analytics-compare'); ?>?current=${currentPeriod}&previous=${previousPeriod}&type=${periodType}`;
        window.open(url, '_blank');
    }
}

// Generate detailed report
function generateDetailedReport() {
    const periodType = document.getElementById('period').value;
    const periodValue = document.getElementById('value').value;

    const url = `<?php echo url('attendance/qr-analytics-report'); ?>?period=${periodType}&value=${periodValue}&format=detailed`;
    window.open(url, '_blank');
}

// Initialize trends chart
function initTrendsChart() {
    const ctx = document.getElementById('trendsChart').getContext('2d');
    const trendsData = analyticsData.trends?.chart_data || {};
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: trendsData.labels || [],
            datasets: [{
                label: 'QR Attendance',
                data: trendsData.qr_attendance || [],
                borderColor: '#3B82F6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Initialize patterns chart
function initPatternsChart() {
    const ctx = document.getElementById('patternsChart').getContext('2d');
    const hourlyData = analyticsData.usage_patterns?.hourly_distribution || Array(24).fill(0);
    
    const labels = [];
    const data = [];
    for (let i = 0; i < 24; i++) {
        if (hourlyData[i] > 0) {
            labels.push(i + ':00');
            data.push(hourlyData[i]);
        }
    }
    
    if (data.length === 0) {
        ctx.canvas.parentElement.innerHTML = '<p class="text-center text-gray-500">No usage pattern data available</p>';
        return;
    }
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
                    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6B7280'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}
</script>
