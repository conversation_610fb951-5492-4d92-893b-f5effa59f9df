<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Edit Group: <?php echo $group->group_name; ?></h1>
        <a href="<?php echo BASE_URL; ?>groups" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-arrow-left mr-2"></i> Back to Groups
        </a>
    </div>

    <?php flash('group_message'); ?>

    <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto">
        <form action="<?php echo BASE_URL; ?>groups/update/<?php echo $group->group_id; ?>" method="POST">
            <div class="mb-4">
                <label for="group_name" class="block text-gray-700 text-sm font-bold mb-2">Group Name *</label>
                <input type="text" name="group_name" id="group_name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" value="<?php echo $group->group_name; ?>" required>
            </div>

            <div class="mb-4">
                <label for="group_description" class="block text-gray-700 text-sm font-bold mb-2">Description</label>
                <textarea name="group_description" id="group_description" rows="4" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><?php echo $group->group_description; ?></textarea>
            </div>

            <div class="mb-4">
                <label for="group_type_id" class="block text-gray-700 text-sm font-bold mb-2">Group Type</label>
                <select name="group_type_id" id="group_type_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" onchange="toggleNewTypeInputEdit()">
                    <option value="">Select Group Type</option>
                    <?php foreach ($groupTypes as $type): ?>
                    <option value="<?php echo $type->group_type_id; ?>" <?php echo ($group->group_type_id == $type->group_type_id) ? 'selected' : ''; ?>><?php echo $type->type_name; ?></option>
                    <?php endforeach; ?>
                    <option value="new">+ Create New Type</option>
                </select>

                <!-- New Group Type Input (hidden by default) -->
                <div id="newTypeContainer" class="mt-3 hidden">
                    <label for="new_group_type" class="block text-gray-700 text-sm font-bold mb-2">New Group Type Name *</label>
                    <input type="text" name="new_group_type" id="new_group_type" placeholder="Enter new group type name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <p class="text-xs text-gray-500 mt-1">This will create a new group type that can be used for future groups.</p>
                </div>
            </div>

            <div class="mb-4">
                <label for="parent_group_id" class="block text-gray-700 text-sm font-bold mb-2">Parent Group (Optional)</label>
                <select name="parent_group_id" id="parent_group_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">None</option>
                    <?php foreach ($parentGroups as $parent): ?>
                    <option value="<?php echo $parent->group_id; ?>" <?php echo ($group->parent_group_id == $parent->group_id) ? 'selected' : ''; ?>><?php echo $parent->group_name; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="mb-4">
                <label for="status" class="block text-gray-700 text-sm font-bold mb-2">Status</label>
                <select name="status" id="status" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="active" <?php echo ($group->status == 'active') ? 'selected' : ''; ?>>Active</option>
                    <option value="inactive" <?php echo ($group->status == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                </select>
            </div>

            <div class="flex items-center justify-end">
                <button type="submit" class="bg-[#3F7D58] hover:bg-[#2c5a3f] text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200">
                    Update Group
                </button>
            </div>
        </form>
    </div>
</div>

<script src="<?php echo BASE_URL; ?>assets/js/groups-common.js"></script>