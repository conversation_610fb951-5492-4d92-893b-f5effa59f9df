<?php
/**
 * Utility to sync ENUM constraints with departments and roles tables
 * This should be run whenever new departments or roles are added
 */

require_once dirname(__DIR__) . '/config/database.php';

function syncEnumConstraints() {
    $database = new Database();
    $conn = $database->getConnection();
    
    try {
        // Get all active department names
        $stmt = $conn->prepare("SELECT name FROM departments WHERE is_active = 1 ORDER BY name");
        $stmt->execute();
        $departments = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Get all active role names
        $stmt = $conn->prepare("SELECT name FROM roles WHERE is_active = 1 ORDER BY name");
        $stmt->execute();
        $roles = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Build department ENUM string
        $dept_enum_values = "'" . implode("','", $departments) . "'";
        
        // Build role ENUM string
        $role_enum_values = "'" . implode("','", $roles) . "'";
        
        // Update department ENUM
        $dept_sql = "ALTER TABLE members MODIFY COLUMN department ENUM($dept_enum_values) DEFAULT 'none'";
        $stmt = $conn->prepare($dept_sql);
        $dept_result = $stmt->execute();
        
        // Update role ENUM
        $role_sql = "ALTER TABLE members MODIFY COLUMN role ENUM($role_enum_values) DEFAULT 'member'";
        $stmt = $conn->prepare($role_sql);
        $role_result = $stmt->execute();
        
        return [
            'success' => $dept_result && $role_result,
            'departments_synced' => count($departments),
            'roles_synced' => count($roles),
            'message' => 'ENUM constraints synced successfully'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// If called directly, run the sync
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    echo "=== SYNCING ENUM CONSTRAINTS ===\n";
    
    $result = syncEnumConstraints();
    
    if ($result['success']) {
        echo "✅ " . $result['message'] . "\n";
        echo "   Departments synced: " . $result['departments_synced'] . "\n";
        echo "   Roles synced: " . $result['roles_synced'] . "\n";
    } else {
        echo "❌ Error: " . $result['error'] . "\n";
    }
}
?>
