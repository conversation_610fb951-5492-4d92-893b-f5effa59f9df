<?php
/**
 * Child Details View
 */

// Extract child information (controller ensures this data exists)
$child = $child_info['child'] ?? null;
$parents = $child_info['parents'] ?? []; // Church member parents
$guardians = $child_info['guardians'] ?? []; // Non-member guardians
$all_guardians = $child_info['all_guardians'] ?? []; // Combined list
$medical_info = $child_info['medical_info'] ?? null;
$recent_attendance = $child_info['recent_attendance'] ?? [];

// Calculate age
$age = 0;
if ($child && $child['date_of_birth']) {
    $birthDate = new DateTime($child['date_of_birth']);
    $today = new DateTime();
    $age = $today->diff($birthDate)->y;
}
?>

<div class="max-w-7xl mx-auto px-4 py-8">
    <!-- Success/Error Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span><?php echo htmlspecialchars($_SESSION['success_message']); ?></span>
            </div>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span><?php echo htmlspecialchars($_SESSION['error_message']); ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>

    <!-- Page Header -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-child text-2xl text-blue-600"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">
                        <?php echo htmlspecialchars($child['first_name'] . ' ' . $child['last_name']); ?>
                    </h1>
                    <p class="text-gray-600">Age: <?php echo $age; ?> years old</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo BASE_URL; ?>children-ministry/children" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Children
                </a>
                <a href="<?php echo BASE_URL; ?>children-ministry/edit-child?id=<?php echo $child['id']; ?>" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Child
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div class="bg-blue-50 px-6 py-4 border-b border-blue-200">
                    <h3 class="text-lg font-semibold text-blue-900">Basic Information</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($child['first_name']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($child['last_name']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                            <p class="text-gray-900"><?php echo date('F j, Y', strtotime($child['date_of_birth'])); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                            <p class="text-gray-900 capitalize"><?php echo htmlspecialchars($child['gender']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Age</label>
                            <p class="text-gray-900"><?php echo $age; ?> years old</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Member Status</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $child['member_status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                <?php echo ucfirst($child['member_status']); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family Relationships -->
            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div class="bg-green-50 px-6 py-4 border-b border-green-200">
                    <h3 class="text-lg font-semibold text-green-900">Family Relationships</h3>
                </div>
                <div class="p-6">
                    <?php if (!empty($all_guardians)): ?>
                        <div class="space-y-4">
                            <?php foreach ($all_guardians as $guardian): ?>
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium text-gray-900">
                                                <?php echo htmlspecialchars($guardian['first_name'] . ' ' . $guardian['last_name']); ?>
                                            </h4>
                                            <p class="text-sm text-gray-600 capitalize">
                                                <?php echo str_replace('_', ' ', $guardian['relationship_type']); ?>
                                            </p>
                                            <?php if (isset($guardian['guardian_contact_id'])): ?>
                                                <span class="inline-block bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium mt-1">Non-member Guardian</span>
                                            <?php else: ?>
                                                <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium mt-1">Church Member</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <?php if ($guardian['is_primary']): ?>
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">Primary</span>
                                            <?php endif; ?>
                                            <?php if ($guardian['can_pickup']): ?>
                                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">Can Pickup</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="mt-2 space-y-1">
                                        <?php if (!empty($guardian['phone_number'])): ?>
                                            <p class="text-sm text-gray-600">
                                                <i class="fas fa-phone mr-1"></i>
                                                <?php echo htmlspecialchars($guardian['phone_number']); ?>
                                            </p>
                                        <?php endif; ?>
                                        <?php if (!empty($guardian['email'])): ?>
                                            <p class="text-sm text-gray-600">
                                                <i class="fas fa-envelope mr-1"></i>
                                                <?php echo htmlspecialchars($guardian['email']); ?>
                                            </p>
                                        <?php endif; ?>
                                        <?php if (!empty($guardian['address'])): ?>
                                            <p class="text-sm text-gray-600">
                                                <i class="fas fa-map-marker-alt mr-1"></i>
                                                <?php echo htmlspecialchars($guardian['address']); ?>
                                            </p>
                                        <?php endif; ?>
                                        <?php if (!empty($guardian['occupation'])): ?>
                                            <p class="text-sm text-gray-600">
                                                <i class="fas fa-briefcase mr-1"></i>
                                                <?php echo htmlspecialchars($guardian['occupation']); ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <div class="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <i class="fas fa-users text-2xl text-gray-400"></i>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">No Family Relationships</h4>
                            <p class="text-gray-500 mb-4">No parent or guardian relationships have been added yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-8">
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div class="bg-purple-50 px-6 py-4 border-b border-purple-200">
                    <h3 class="text-lg font-semibold text-purple-900">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="<?php echo BASE_URL; ?>children-ministry/medical-info?child_id=<?php echo $child['id']; ?>" 
                       class="w-full inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fas fa-heartbeat mr-2 text-red-500"></i>
                        Medical Information
                    </a>

                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div class="bg-yellow-50 px-6 py-4 border-b border-yellow-200">
                    <h3 class="text-lg font-semibold text-yellow-900">Recent Activity</h3>
                </div>
                <div class="p-6">
                    <?php if (!empty($recent_attendance)): ?>
                        <div class="space-y-3">
                            <?php foreach (array_slice($recent_attendance, 0, 5) as $attendance): ?>
                                <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($attendance['event_name']); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?php echo date('M j, Y', strtotime($attendance['check_in_time'])); ?>
                                        </p>
                                    </div>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                        Present
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 text-sm">No recent activity</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
