<?php
/**
 * Service Details View
 */
?>

<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Service Details</h1>
            <p class="text-gray-600 mt-1">View service information and settings</p>
        </div>
        <div class="flex space-x-2">
            <a href="<?php echo url('services'); ?>" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Services
            </a>
            <?php if (has_permission('admin')): ?>
            <a href="<?php echo url('services/' . $service->id . '/edit'); ?>" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-edit mr-2"></i> Edit Service
            </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Service Information Card -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                <i class="fas fa-church text-primary mr-3"></i>
                <?php echo htmlspecialchars($service->name); ?>
            </h2>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Service Name</dt>
                            <dd class="text-sm text-gray-900"><?php echo htmlspecialchars($service->name); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Description</dt>
                            <dd class="text-sm text-gray-900">
                                <?php echo !empty($service->description) ? htmlspecialchars($service->description) : '<span class="text-gray-400 italic">No description provided</span>'; ?>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="text-sm">
                                <?php if ($service->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i> Active
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i> Inactive
                                    </span>
                                <?php endif; ?>
                            </dd>
                        </div>
                    </dl>
                </div>

                <!-- Schedule Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Day of Week</dt>
                            <dd class="text-sm text-gray-900">
                                <?php 
                                $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                                echo $days[$service->day_of_week] ?? 'Unknown';
                                ?>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Time</dt>
                            <dd class="text-sm text-gray-900">
                                <?php echo date('g:i A', strtotime($service->time)); ?>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Duration</dt>
                            <dd class="text-sm text-gray-900">
                                <?php echo $service->duration ?? 'Not specified'; ?> minutes
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Additional Information -->
            <?php if (!empty($service->location) || !empty($service->notes)): ?>
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php if (!empty($service->location)): ?>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Location</dt>
                        <dd class="text-sm text-gray-900 mt-1"><?php echo htmlspecialchars($service->location); ?></dd>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($service->notes)): ?>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Notes</dt>
                        <dd class="text-sm text-gray-900 mt-1"><?php echo nl2br(htmlspecialchars($service->notes)); ?></dd>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Metadata -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Metadata</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created</dt>
                        <dd class="text-sm text-gray-900">
                            <?php echo isset($service->created_at) ? date('M j, Y g:i A', strtotime($service->created_at)) : 'Unknown'; ?>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="text-sm text-gray-900">
                            <?php echo isset($service->updated_at) ? date('M j, Y g:i A', strtotime($service->updated_at)) : 'Unknown'; ?>
                        </dd>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <?php if (has_permission('admin')): ?>
    <div class="mt-6 flex justify-end space-x-3">
        <a href="<?php echo url('services/' . $service->id . '/edit'); ?>" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-edit mr-2"></i> Edit Service
        </a>
        <button onclick="confirmDelete(<?php echo $service->id; ?>)" class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-trash mr-2"></i> Delete Service
        </button>
    </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-2">Delete Service</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete this service? This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirm-delete" class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-600">
                    Delete
                </button>
                <button id="cancel-delete" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    const modal = document.getElementById('delete-modal');
    const confirmBtn = document.getElementById('confirm-delete');
    
    modal.classList.remove('hidden');
    confirmBtn.onclick = function() {
        // Create form for DELETE request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo url('services/'); ?>' + id;
        
        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfToken);
        
        // Add method override for DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);
        
        document.body.appendChild(form);
        form.submit();
    };
    
    document.getElementById('cancel-delete').onclick = function() {
        modal.classList.add('hidden');
    };
}
</script>
