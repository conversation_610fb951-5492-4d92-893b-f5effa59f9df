<div class="container mx-auto px-4 max-w-7xl">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-red-600 to-pink-700 p-6 text-white">
            <div class="flex flex-row justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Absent Member Reminders</h1>
                    <p class="text-sm opacity-90 mt-1">Send reminders to members who have been absent for multiple services</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Attendance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- SMS Balance & Settings Info -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <!-- SMS Balance -->
        <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">SMS Balance</h3>
                </div>
            </div>
            <div class="text-2xl font-bold text-blue-600 mb-2">
                <?php echo isset($sms_balance) ? number_format($sms_balance) : 'N/A'; ?>
            </div>
            <p class="text-sm text-gray-600">Available SMS credits</p>
            <?php if (!isset($sms_settings_configured) || !$sms_settings_configured): ?>
                <div class="mt-3">
                    <a href="<?php echo BASE_URL; ?>settings" class="text-red-600 hover:text-red-800 text-sm font-medium">
                        <i class="fas fa-cog mr-1"></i>Configure SMS
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Members to Follow Up -->
        <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full bg-red-100 text-red-600 flex items-center justify-center mr-3">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">Need Follow-up</h3>
                </div>
            </div>
            <div class="text-2xl font-bold text-red-600 mb-2">
                <?php echo count($absent_members); ?>
            </div>
            <p class="text-sm text-gray-600">Members with extended absences</p>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-3">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">Quick Actions</h3>
                </div>
            </div>
            <div class="space-y-2">
                <button onclick="selectAllMembers()" class="w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-check-double mr-2"></i>Select All
                </button>
                <button onclick="previewMessage()" class="w-full bg-purple-100 hover:bg-purple-200 text-purple-700 py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-eye mr-2"></i>Preview SMS
                </button>
            </div>
        </div>
    </div>

    <!-- Absent Members -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-red-50 to-pink-50 p-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-lg font-semibold text-gray-800">Members with Extended Absences</h2>
                    <p class="text-sm text-gray-600">Members who have been absent for 3 or more consecutive QR services</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="text-sm text-gray-600">
                        <span class="font-medium"><?php echo count($absent_members); ?></span> members need follow-up
                    </div>
                    <?php if (!empty($absent_members)): ?>
                        <button onclick="exportAbsentMembers()" class="bg-white hover:bg-gray-50 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium border border-gray-300 transition-colors">
                            <i class="fas fa-download mr-2"></i>Export List
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="p-6">
            <?php if (empty($absent_members)) : ?>
                <div class="text-center py-8">
                    <div class="p-3 rounded-full bg-green-100 text-green-600 mx-auto mb-4 w-16 h-16 flex items-center justify-center">
                        <i class="fas fa-user-check text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Members Need Follow-up</h3>
                    <div class="text-gray-500 space-y-2 max-w-md mx-auto">
                        <p>This could mean:</p>
                        <ul class="text-sm text-left space-y-1">
                            <li>• All QR-active members are attending regularly</li>
                            <li>• No QR attendance sessions have been created yet</li>
                            <li>• Members haven't started using the QR attendance system</li>
                        </ul>
                        <?php if (isset($dashboard_stats)): ?>
                            <div class="mt-4 p-4 bg-blue-50 rounded-lg text-sm">
                                <div class="font-medium text-blue-800 mb-2">System Status:</div>
                                <div class="text-blue-700 space-y-1 text-left">
                                    <div>Total Active Members: <span class="font-medium"><?php echo $dashboard_stats['total_members']; ?></span></div>
                                    <div>Members with Phone Numbers: <span class="font-medium"><?php echo $dashboard_stats['members_with_phones']; ?></span></div>
                                    <div>QR-Active Members: <span class="font-medium"><?php echo $dashboard_stats['qr_active_members']; ?></span></div>
                                    <div>Recent QR Sessions (30 days): <span class="font-medium"><?php echo $dashboard_stats['recent_qr_sessions']; ?></span></div>
                                    <div>Total QR Attendance Records: <span class="font-medium"><?php echo $dashboard_stats['total_qr_attendance']; ?></span></div>
                                    <?php if ($dashboard_stats['last_qr_session_date']): ?>
                                        <div>Last QR Session: <span class="font-medium"><?php echo date('M j, Y', strtotime($dashboard_stats['last_qr_session_date'])); ?></span></div>
                                    <?php else: ?>
                                        <div class="text-orange-600">No QR sessions found</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="mt-4">
                            <a href="<?php echo BASE_URL; ?>attendance/qr" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-qrcode mr-2"></i>
                                Create QR Session
                            </a>
                        </div>
                    </div>
                </div>
            <?php else : ?>
                <form action="<?php echo BASE_URL; ?>attendance/reminders" method="POST">
                    <input type="hidden" name="action" value="send_reminders">
                    
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="select-all" class="rounded border-gray-300 text-red-600 focus:ring-red-500 h-4 w-4">
                                <label for="select-all" class="ml-2 text-sm font-medium text-gray-700">Select All</label>
                            </div>
                            <div class="text-sm text-gray-600">
                                <span class="font-medium" id="selected-count">0</span> of <?php echo count($absent_members); ?> members selected
                            </div>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead>
                                    <tr>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10"></th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Consecutive Absences</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Last Attendance</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Risk Level</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($absent_members as $member) :
                                        // Calculate risk level based on consecutive absences
                                        $consecutive = $member['consecutive_absences'];
                                        if ($consecutive >= 8) {
                                            $risk_level = 'Critical';
                                            $risk_color = 'bg-red-100 text-red-800';
                                            $risk_icon = 'fa-exclamation-triangle';
                                        } elseif ($consecutive >= 5) {
                                            $risk_level = 'High';
                                            $risk_color = 'bg-orange-100 text-orange-800';
                                            $risk_icon = 'fa-exclamation';
                                        } else {
                                            $risk_level = 'Medium';
                                            $risk_color = 'bg-yellow-100 text-yellow-800';
                                            $risk_icon = 'fa-info-circle';
                                        }

                                        // Check if phone number is valid for SMS
                                        $has_phone = !empty($member['phone']) && strlen($member['phone']) >= 10;
                                    ?>
                                        <tr class="hover:bg-gray-50" data-member-id="<?php echo $member['member_id']; ?>" data-member-name="<?php echo htmlspecialchars($member['name']); ?>" data-member-phone="<?php echo htmlspecialchars($member['phone']); ?>">
                                            <td class="px-4 py-3 whitespace-nowrap">
                                                <input type="checkbox" name="member_ids[]" value="<?php echo $member['member_id']; ?>" class="member-checkbox rounded border-gray-300 text-red-600 focus:ring-red-500 h-4 w-4" <?php echo $has_phone ? '' : 'disabled title="No valid phone number"'; ?>>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10 rounded-full <?php echo $has_phone ? 'bg-green-100' : 'bg-gray-100'; ?> flex items-center justify-center">
                                                        <i class="fas fa-user <?php echo $has_phone ? 'text-green-600' : 'text-gray-400'; ?>"></i>
                                                    </div>
                                                    <div class="ml-3">
                                                        <div class="text-sm font-medium text-gray-900"><?php echo $member['name']; ?></div>
                                                        <div class="text-xs text-gray-500">ID: <?php echo $member['member_id']; ?></div>
                                                        <?php if (!$has_phone): ?>
                                                            <div class="text-xs text-red-500">
                                                                <i class="fas fa-exclamation-triangle mr-1"></i>No valid phone
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-center">
                                                <?php if ($has_phone): ?>
                                                    <div class="text-gray-900 font-medium"><?php echo $member['phone']; ?></div>
                                                    <div class="text-xs text-green-600">
                                                        <i class="fas fa-check-circle mr-1"></i>SMS Ready
                                                    </div>
                                                <?php else: ?>
                                                    <div class="text-gray-400"><?php echo $member['phone'] ?: 'Not provided'; ?></div>
                                                    <div class="text-xs text-red-500">
                                                        <i class="fas fa-times-circle mr-1"></i>Invalid
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center">
                                                <?php echo $member['department'] ?? 'Not assigned'; ?>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-center">
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    <?php echo $member['consecutive_absences']; ?> services
                                                </span>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center">
                                                <?php echo $member['last_attendance'] !== 'Never' ? format_date($member['last_attendance']) : 'Never'; ?>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-center">
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $risk_color; ?>">
                                                    <i class="fas <?php echo $risk_icon; ?> mr-1"></i><?php echo $risk_level; ?>
                                                </span>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-center">
                                                <div class="flex items-center justify-center space-x-2">
                                                    <a href="<?php echo BASE_URL; ?>members/view/<?php echo $member['member_id']; ?>" class="text-blue-600 hover:text-blue-900" title="View Member">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($has_phone): ?>
                                                        <button onclick="sendIndividualSMS(<?php echo $member['member_id']; ?>)" class="text-green-600 hover:text-green-900" title="Send Individual SMS">
                                                            <i class="fas fa-sms"></i>
                                                        </button>
                                                        <a href="tel:<?php echo $member['phone']; ?>" class="text-purple-600 hover:text-purple-900" title="Call Member">
                                                            <i class="fas fa-phone"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-gray-400" title="No phone number available">
                                                            <i class="fas fa-phone-slash"></i>
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-5 border border-gray-200">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-base font-semibold text-gray-800">SMS Reminder Message</h3>
                            <div class="flex items-center space-x-2">
                                <button type="button" onclick="useTemplate('caring')" class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full hover:bg-blue-200 transition-colors">
                                    Caring
                                </button>
                                <button type="button" onclick="useTemplate('encouraging')" class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full hover:bg-green-200 transition-colors">
                                    Encouraging
                                </button>
                                <button type="button" onclick="useTemplate('invitation')" class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full hover:bg-purple-200 transition-colors">
                                    Invitation
                                </button>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message Content</label>
                            <textarea id="message" name="message" rows="5" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-red-500 focus:ring focus:ring-red-500 focus:ring-opacity-50 py-2 px-3" placeholder="Enter your personalized message here...">Hello {name}, we've missed you at church! We hope to see you this Sunday at our service. Your presence makes a difference in our community. God bless!</textarea>
                            <div class="flex justify-between mt-1">
                                <div class="text-xs text-gray-500">
                                    <span id="character-count">0</span>/160 characters per SMS
                                </div>
                                <div class="text-xs text-gray-500">
                                    <span id="message-count">1</span> SMS message(s)
                                </div>
                            </div>
                            <div class="mt-2 text-xs text-blue-600">
                                <i class="fas fa-info-circle mr-1"></i>
                                Use {name} to personalize with member's name
                            </div>
                        </div>

                        <!-- SMS Cost Calculation -->
                        <div class="bg-blue-50 rounded-lg p-4 mb-4 border border-blue-200">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <i class="fas fa-calculator text-blue-600 mr-2"></i>
                                    <span class="text-sm font-medium text-blue-800">SMS Cost Estimate</span>
                                </div>
                                <div class="text-sm text-blue-600">
                                    <span id="selected-members-count">0</span> members selected
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-blue-700">Messages to send:</span>
                                    <span class="font-medium text-blue-900" id="total-messages">0</span>
                                </div>
                                <div>
                                    <span class="text-blue-700">Estimated cost:</span>
                                    <span class="font-medium text-blue-900" id="estimated-cost">0 credits</span>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center">
                            <div class="flex items-center space-x-3">
                                <button type="button" onclick="previewMessage()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                                    <i class="fas fa-eye mr-2"></i> Preview
                                </button>
                                <button type="button" onclick="testMessage()" class="bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                                    <i class="fas fa-vial mr-2"></i> Test SMS
                                </button>
                            </div>
                            <button type="submit" id="send-reminders-btn" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200" disabled>
                                <i class="fas fa-paper-plane mr-2"></i> Send SMS Reminders
                            </button>
                        </div>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <!-- Reminder History -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Reminder History</h2>
        </div>
        <div class="p-6">
            <?php if (empty($reminder_history)) : ?>
                <div class="text-center py-8 text-gray-500">
                    <div class="p-3 rounded-full bg-gray-100 text-gray-400 mx-auto mb-3 w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-history text-xl"></i>
                    </div>
                    <p>No reminders have been sent yet</p>
                </div>
            <?php else : ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Sent Date</th>
                                <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($reminder_history as $reminder) : ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?php echo $reminder['member_name']; ?></div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="text-sm text-gray-500 max-w-md truncate"><?php echo $reminder['message']; ?></div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <?php echo date('M d, Y h:i A', strtotime($reminder['sent_at'])); ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-center">
                                        <?php if ($reminder['status'] === 'delivered') : ?>
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Delivered
                                            </span>
                                        <?php elseif ($reminder['status'] === 'failed') : ?>
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Failed
                                            </span>
                                        <?php else : ?>
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Pending
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Best Practices -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Best Practices for Absence Follow-up</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3">
                            <i class="fas fa-comment-dots"></i>
                        </div>
                        <h3 class="text-base font-semibold text-gray-800">Message Tips</h3>
                    </div>
                    <ul class="text-sm text-gray-600 space-y-2 list-disc list-inside">
                        <li>Keep messages warm and inviting, not accusatory</li>
                        <li>Include specific details about upcoming services</li>
                        <li>Personalize messages when possible</li>
                        <li>Express that they are missed and valued</li>
                        <li>Offer assistance if they need help attending</li>
                    </ul>
                </div>

                <div class="bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-3">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h3 class="text-base font-semibold text-gray-800">Follow-up Strategy</h3>
                    </div>
                    <ul class="text-sm text-gray-600 space-y-2 list-disc list-inside">
                        <li>Send reminders 2-3 days before the service</li>
                        <li>Follow up with a phone call for members absent 5+ times</li>
                        <li>Track which members respond to reminders</li>
                        <li>Consider creating a visitation team for persistent absences</li>
                        <li>Celebrate when members return after extended absences</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Individual SMS Modal -->
<div id="individual-sms-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
        <div class="bg-gradient-to-r from-green-600 to-teal-600 p-4 text-white rounded-t-xl">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-bold">Send Individual SMS</h3>
                <button onclick="closeIndividualSMSModal()" class="text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="p-6">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Member:</label>
                <div id="individual-member-info" class="bg-gray-100 rounded-lg p-3 text-sm">
                    <!-- Member info will be populated here -->
                </div>
            </div>
            <div class="mb-4">
                <label for="individual-message" class="block text-sm font-medium text-gray-700 mb-2">Message:</label>
                <textarea id="individual-message" rows="4" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 py-2 px-3" placeholder="Enter your message..."></textarea>
                <div class="flex justify-between mt-1 text-xs text-gray-500">
                    <span><span id="individual-char-count">0</span>/160 characters</span>
                    <span><span id="individual-sms-count">1</span> SMS</span>
                </div>
            </div>
            <div class="flex justify-end space-x-3">
                <button onclick="closeIndividualSMSModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg">
                    Cancel
                </button>
                <button onclick="sendIndividualSMSNow()" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg">
                    <i class="fas fa-paper-plane mr-2"></i>Send SMS
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllCheckbox = document.getElementById('select-all');
        const memberCheckboxes = document.querySelectorAll('.member-checkbox');
        const selectedCountElement = document.getElementById('selected-count');
        const messageTextarea = document.getElementById('message');
        const characterCountElement = document.getElementById('character-count');
        const messageCountElement = document.getElementById('message-count');
        const sendButton = document.getElementById('send-reminders-btn');
        const selectedMembersCountElement = document.getElementById('selected-members-count');
        const totalMessagesElement = document.getElementById('total-messages');
        const estimatedCostElement = document.getElementById('estimated-cost');

        // Message templates
        const templates = {
            caring: "Hello {name}, we've noticed you haven't been to church recently and wanted to check on you. We miss your presence and hope everything is well. You're always welcome back anytime. God bless!",
            encouraging: "Hi {name}! We miss seeing you at church. Your presence makes our community stronger. We hope to see you this Sunday - there's always a seat waiting for you. Blessings!",
            invitation: "Hello {name}, we have an exciting service this Sunday and would love to see you there! We miss you and hope you can join us. Service starts at [TIME]. Looking forward to seeing you!"
        };

        // Select All functionality
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;

            memberCheckboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = isChecked;
                }
            });

            updateCounts();
        });

        // Individual checkbox change
        memberCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateCounts();

                // Update select all checkbox
                const enabledCheckboxes = Array.from(memberCheckboxes).filter(cb => !cb.disabled);
                selectAllCheckbox.checked = enabledCheckboxes.every(cb => cb.checked);
                selectAllCheckbox.indeterminate = !selectAllCheckbox.checked && enabledCheckboxes.some(cb => cb.checked);
            });
        });

        // Message input handling
        messageTextarea.addEventListener('input', function() {
            updateMessageCounts();
            updateCosts();
        });

        // Initialize counts
        updateCounts();
        updateMessageCounts();

        function updateCounts() {
            const selectedCount = document.querySelectorAll('.member-checkbox:checked').length;
            selectedCountElement.textContent = selectedCount;
            selectedMembersCountElement.textContent = selectedCount;

            // Enable/disable send button
            sendButton.disabled = selectedCount === 0;

            updateCosts();
        }

        function updateMessageCounts() {
            const characterCount = messageTextarea.value.length;
            characterCountElement.textContent = characterCount;

            // Calculate message count (1 message = 160 characters)
            const messageCount = Math.max(1, Math.ceil(characterCount / 160));
            messageCountElement.textContent = messageCount;
        }

        function updateCosts() {
            const selectedCount = document.querySelectorAll('.member-checkbox:checked').length;
            const messageCount = Math.max(1, Math.ceil(messageTextarea.value.length / 160));
            const totalMessages = selectedCount * messageCount;

            totalMessagesElement.textContent = totalMessages;
            estimatedCostElement.textContent = totalMessages + ' credits';
        }

        // Global functions for buttons
        window.selectAllMembers = function() {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.dispatchEvent(new Event('change'));
        };

        window.useTemplate = function(templateType) {
            if (templates[templateType]) {
                messageTextarea.value = templates[templateType];
                updateMessageCounts();
                updateCosts();
            }
        };

        window.previewMessage = function() {
            const selectedMembers = Array.from(document.querySelectorAll('.member-checkbox:checked'))
                .map(cb => {
                    const row = cb.closest('tr');
                    return {
                        name: row.dataset.memberName,
                        phone: row.dataset.memberPhone
                    };
                });

            if (selectedMembers.length === 0) {
                alert('Please select at least one member to preview the message.');
                return;
            }

            const message = messageTextarea.value;
            const sampleMember = selectedMembers[0];
            const personalizedMessage = message.replace(/{name}/g, sampleMember.name);

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-4 text-white rounded-t-xl">
                        <h3 class="text-lg font-bold">SMS Preview</h3>
                    </div>
                    <div class="p-6">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Sample message for ${sampleMember.name}:</label>
                            <div class="bg-gray-100 rounded-lg p-3 text-sm">
                                ${personalizedMessage}
                            </div>
                        </div>
                        <div class="text-sm text-gray-600 mb-4">
                            <div class="flex justify-between">
                                <span>Characters: ${personalizedMessage.length}</span>
                                <span>SMS parts: ${Math.max(1, Math.ceil(personalizedMessage.length / 160))}</span>
                            </div>
                            <div class="flex justify-between mt-1">
                                <span>Recipients: ${selectedMembers.length}</span>
                                <span>Total SMS: ${selectedMembers.length * Math.max(1, Math.ceil(personalizedMessage.length / 160))}</span>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button onclick="this.closest('.fixed').remove()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        window.testMessage = function() {
            const message = messageTextarea.value;
            if (!message.trim()) {
                alert('Please enter a message to test.');
                return;
            }

            const testPhone = prompt('Enter your phone number to receive a test SMS:');
            if (!testPhone) return;

            // Here you would make an AJAX call to send a test SMS
            alert('Test SMS functionality would be implemented here. Message would be sent to: ' + testPhone);
        };

        window.sendIndividualSMS = function(memberId) {
            const row = document.querySelector(`tr[data-member-id="${memberId}"]`);
            const memberName = row.dataset.memberName;
            const memberPhone = row.dataset.memberPhone;

            // Populate modal
            document.getElementById('individual-member-info').innerHTML = `
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-3">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="font-medium">${memberName}</div>
                        <div class="text-gray-500">${memberPhone}</div>
                    </div>
                </div>
            `;

            // Set default message
            const defaultMessage = `Hello ${memberName}, we've missed you at church! We hope to see you this Sunday at our service. Your presence makes a difference. God bless!`;
            document.getElementById('individual-message').value = defaultMessage;

            // Update character count
            updateIndividualMessageCount();

            // Store member data for sending
            window.currentIndividualSMS = { memberId, memberName, memberPhone };

            // Show modal
            document.getElementById('individual-sms-modal').classList.remove('hidden');
        };

        window.closeIndividualSMSModal = function() {
            document.getElementById('individual-sms-modal').classList.add('hidden');
        };

        window.sendIndividualSMSNow = function() {
            const message = document.getElementById('individual-message').value;
            if (!message.trim()) {
                alert('Please enter a message.');
                return;
            }

            const { memberId, memberName, memberPhone } = window.currentIndividualSMS;

            // Here you would make an AJAX call to send individual SMS
            if (confirm(`Send SMS to ${memberName} at ${memberPhone}?`)) {
                // Simulate sending SMS
                alert(`SMS sent successfully to ${memberName}!`);
                closeIndividualSMSModal();
            }
        };

        function updateIndividualMessageCount() {
            const message = document.getElementById('individual-message').value;
            const charCount = message.length;
            const smsCount = Math.max(1, Math.ceil(charCount / 160));

            document.getElementById('individual-char-count').textContent = charCount;
            document.getElementById('individual-sms-count').textContent = smsCount;
        }

        // Add event listener for individual message textarea
        document.getElementById('individual-message').addEventListener('input', updateIndividualMessageCount);

        window.exportAbsentMembers = function() {
            // Create CSV content
            const headers = ['Name', 'Phone', 'Department', 'Consecutive Absences', 'Last Attendance', 'Risk Level'];
            const rows = Array.from(document.querySelectorAll('tbody tr')).map(row => {
                const cells = row.querySelectorAll('td');
                return [
                    row.dataset.memberName,
                    row.dataset.memberPhone,
                    cells[3].textContent.trim(),
                    cells[4].textContent.trim(),
                    cells[5].textContent.trim(),
                    cells[6].textContent.trim()
                ];
            });

            const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'absent_members_' + new Date().toISOString().split('T')[0] + '.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        };
    });
</script>
