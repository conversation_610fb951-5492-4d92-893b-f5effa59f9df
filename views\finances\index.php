<div class="max-w-7xl mx-auto">
    <style>

        /* Chart container */
        .chart-container {
            position: relative;
            height: 180px;
            width: 100%;
        }

        /* Card animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease forwards;
        }

        /* Progress bar animation */
        @keyframes growWidth {
            from { width: 0; }
            to { width: var(--target-width); }
        }

        .animate-progress {
            animation: growWidth 1s ease-out forwards;
        }

        /* Hover effects */
        .stat-card {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* Subtle background patterns */
        .bg-pattern-dots {
            background-image: radial-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .bg-pattern-lines {
            background-image: linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px);
            background-size: 100% 20px;
        }
    </style>

    <!-- Clean Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Financial Dashboard</h1>
            <p class="text-gray-600">Manage church finances and track transactions</p>
        </div>
        <div class="flex flex-wrap gap-3 mt-4 sm:mt-0">
            <a href="<?php echo BASE_URL; ?>finance/add" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2 text-sm"></i>
                Add Transaction
            </a>
            <a href="<?php echo BASE_URL; ?>finance/categories" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center">
                <i class="fas fa-cog mr-2 text-sm"></i>
                Categories
            </a>
            <a href="<?php echo BASE_URL; ?>finance/enhanced-reports" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center">
                <i class="fas fa-chart-bar mr-2 text-sm"></i>
                Reports
            </a>
        </div>
    </div>



    <!-- Financial Summary -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Income Card -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-green-100 to-green-200 border border-green-300 rounded-lg p-8 hover:shadow-lg transition-all duration-300 animate-fadeIn" style="animation-delay: 0.1s; min-height: 150px;">
                <div class="flex items-center justify-between h-full">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-arrow-up text-white text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-green-700 uppercase tracking-wide">Total Income</p>
                            <p class="text-3xl font-bold text-green-800">GH₵ <?php echo number_format($total_income, 2); ?></p>
                        </div>
                    </div>
                </div>
                <div class="absolute top-0 right-0 w-24 h-24 bg-green-300 opacity-40 rounded-full -mr-12 -mt-12"></div>
            </div>

            <!-- Expenses Card -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-red-100 to-red-200 border border-red-300 rounded-lg p-8 hover:shadow-lg transition-all duration-300 animate-fadeIn" style="animation-delay: 0.2s; min-height: 150px;">
                <div class="flex items-center justify-between h-full">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-red-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-arrow-down text-white text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-red-700 uppercase tracking-wide">Total Expenses</p>
                            <p class="text-3xl font-bold text-red-800">GH₵ <?php echo number_format($total_expense, 2); ?></p>
                        </div>
                    </div>
                </div>
                <div class="absolute top-0 right-0 w-24 h-24 bg-red-300 opacity-40 rounded-full -mr-12 -mt-12"></div>
            </div>

            <!-- Balance Card -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-primary/20 to-primary/40 border border-primary/50 rounded-lg p-8 hover:shadow-lg transition-all duration-300 animate-fadeIn" style="animation-delay: 0.3s; min-height: 150px;">
                <div class="flex items-center justify-between h-full">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-primary rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-wallet text-white text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-primary-dark uppercase tracking-wide">Current Balance</p>
                            <p class="text-3xl font-bold text-primary-dark">GH₵ <?php echo number_format($balance, 2); ?></p>
                        </div>
                    </div>
                </div>
                <div class="absolute top-0 right-0 w-24 h-24 bg-primary/30 opacity-50 rounded-full -mr-12 -mt-12"></div>
            </div>
        </div>
    </div>

    <!-- Category Dashboards -->
    <?php if (!empty($memberPaymentCategories) || !empty($generalIncomeCategories) || !empty($expenseCategories)): ?>
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Category Dashboards</h3>
            <a href="<?php echo BASE_URL; ?>finance/categories" class="text-sm text-gray-600 hover:text-gray-800 flex items-center">
                <i class="fas fa-cog mr-1"></i>
                Manage
            </a>
        </div>


        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            <!-- Static Dashboards (Always Show) -->
            <a href="<?php echo BASE_URL; ?>finance/dashboard/tithe" class="group bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg p-3 transition-all duration-200 hover:shadow-sm">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-chart-line text-white text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-green-800 text-sm">Tithe</h4>
                        <p class="text-green-600 text-xs">Dashboard</p>
                    </div>
                </div>
            </a>

            <a href="<?php echo BASE_URL; ?>finance/dashboard/pledge" class="group bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-3 transition-all duration-200 hover:shadow-sm">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-handshake text-white text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-blue-800 text-sm">Pledge</h4>
                        <p class="text-blue-600 text-xs">Dashboard</p>
                    </div>
                </div>
            </a>

            <?php
            // Show core category dashboards from database
            $coreCategoryDashboards = [
                // Member Payment Categories
                [
                    'name' => 'tithe',
                    'label' => 'Tithe',
                    'icon' => 'fas fa-hand-holding-heart',
                    'colors' => ['bg' => 'purple-50', 'hover' => 'purple-100', 'border' => 'purple-200', 'icon' => 'purple-500', 'text' => 'purple-800'],
                    'url' => 'finance/dashboard/tithe'
                ],
                [
                    'name' => 'pledge',
                    'label' => 'Pledge',
                    'icon' => 'fas fa-handshake',
                    'colors' => ['bg' => 'purple-50', 'hover' => 'purple-100', 'border' => 'purple-200', 'icon' => 'purple-500', 'text' => 'purple-800'],
                    'url' => 'finance/dashboard/pledge'
                ],
                // General Income Categories
                [
                    'name' => 'offering',
                    'label' => 'Offering',
                    'icon' => 'fas fa-praying-hands',
                    'colors' => ['bg' => 'emerald-50', 'hover' => 'emerald-100', 'border' => 'emerald-200', 'icon' => 'emerald-500', 'text' => 'emerald-800'],
                    'url' => 'finance/dashboard/category?category=offering&type=income'
                ],
                [
                    'name' => 'project_offering',
                    'label' => 'Project Offering',
                    'icon' => 'fas fa-building',
                    'colors' => ['bg' => 'emerald-50', 'hover' => 'emerald-100', 'border' => 'emerald-200', 'icon' => 'emerald-500', 'text' => 'emerald-800'],
                    'url' => 'finance/dashboard/category?category=project_offering&type=income'
                ],
                // Expense Categories
                [
                    'name' => 'utilities',
                    'label' => 'Utilities',
                    'icon' => 'fas fa-bolt',
                    'colors' => ['bg' => 'red-50', 'hover' => 'red-100', 'border' => 'red-200', 'icon' => 'red-500', 'text' => 'red-800'],
                    'url' => 'finance/dashboard/category?category=utilities&type=expense'
                ],
                [
                    'name' => 'events',
                    'label' => 'Events',
                    'icon' => 'fas fa-calendar-alt',
                    'colors' => ['bg' => 'red-50', 'hover' => 'red-100', 'border' => 'red-200', 'icon' => 'red-500', 'text' => 'red-800'],
                    'url' => 'finance/dashboard/category?category=events&type=expense'
                ]
            ];

            // Display core category dashboards
            foreach ($coreCategoryDashboards as $category):
                $color = $category['colors'];
            ?>
                <a href="<?php echo BASE_URL . $category['url']; ?>" class="group bg-<?php echo $color['bg']; ?> hover:bg-<?php echo $color['hover']; ?> border border-<?php echo $color['border']; ?> rounded-lg p-3 transition-all duration-200 hover:shadow-sm">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-<?php echo $color['icon']; ?> rounded-lg flex items-center justify-center mr-3">
                            <i class="<?php echo $category['icon']; ?> text-white text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-<?php echo $color['text']; ?> text-sm"><?php echo $category['label']; ?></h4>
                            <p class="text-<?php echo $color['text']; ?> text-xs opacity-70">Dashboard</p>
                        </div>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Financial Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
        <!-- Income vs Expenses Chart -->
        <div class="bg-white rounded-xl shadow-sm p-3 border border-gray-100">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-semibold text-gray-800">Income vs Expenses</h3>
                <span class="text-xs text-gray-500">Last 6 months</span>
            </div>
            <div class="chart-container" style="height: 180px;">
                <canvas id="incomeExpensesChart"></canvas>
            </div>
        </div>

        <!-- Monthly Giving Trends Chart -->
        <div class="bg-white rounded-xl shadow-sm p-3 border border-gray-100">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-semibold text-gray-800">Monthly Giving Trends</h3>
                <span class="text-xs text-gray-500">Last 12 months</span>
            </div>
            <div class="chart-container" style="height: 180px;">
                <canvas id="monthlyGivingChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Financial Insights and Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8 animate-fadeIn" style="animation-delay: 0.5s;">


        <!-- Top Tithe Contributors -->
        <div class="bg-white rounded-xl shadow-md p-4 border border-gray-100 hover:shadow-lg transition-all duration-300 overflow-hidden relative">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="p-2.5 rounded-full bg-gradient-to-r from-primary to-primary-light text-white mr-3 shadow-sm">
                        <i class="fas fa-hand-holding-usd text-sm"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800">Top Tithe Contributors</h3>
                </div>
                <a href="<?php echo BASE_URL; ?>finance/dashboard/tithe" class="text-xs text-primary hover:text-primary-dark transition-colors duration-300 flex items-center">
                    View All <i class="fas fa-chevron-right ml-1"></i>
                </a>
            </div>

            <?php if (empty($top_tithe_contributors)): ?>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-hand-holding-usd text-gray-300 text-4xl mb-3"></i>
                    <p class="text-sm">No tithe records found</p>
                </div>
            <?php else: ?>
                <div class="space-y-3">
                    <?php foreach ($top_tithe_contributors as $index => $contributor): ?>
                        <div class="flex items-center p-2 rounded-lg hover:bg-gray-50 transition-colors duration-300">
                            <div class="relative mr-3">
                                <?php if (!empty($contributor['profile_picture'])): ?>
                                    <img src="<?php echo BASE_URL . $contributor['profile_picture']; ?>" alt="<?php echo htmlspecialchars($contributor['name']); ?>" class="w-10 h-10 rounded-full object-cover border-2 border-primary">
                                <?php else: ?>
                                    <div class="w-10 h-10 rounded-full bg-primary-light flex items-center justify-center text-white font-bold text-sm border-2 border-primary">
                                        <?php echo strtoupper(substr($contributor['name'], 0, 1)); ?>
                                    </div>
                                <?php endif; ?>
                                <div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-primary to-primary-light rounded-full flex items-center justify-center text-white text-xs shadow-sm">
                                    <?php echo $index + 1; ?>
                                </div>
                            </div>
                            <div class="flex-grow min-w-0">
                                <a href="<?php echo BASE_URL; ?>members/view?id=<?php echo $contributor['member_id']; ?>" class="text-sm font-medium text-gray-800 hover:text-primary transition-colors duration-300 truncate block">
                                    <?php echo htmlspecialchars($contributor['name']); ?>
                                </a>
                                <div class="flex items-center justify-between text-xs">
                                    <span class="text-gray-500">Total Tithes</span>
                                    <span class="font-bold text-primary">GH₵ <?php echo number_format($contributor['total_amount'], 2); ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Transactions section removed as requested -->

<!-- Chart.js Script -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize charts if they exist
        if (document.getElementById('incomeExpensesChart')) {
            // Income vs Expenses Chart
            const incomeExpensesCtx = document.getElementById('incomeExpensesChart').getContext('2d');

            // Get data from PHP
            const monthlyData = <?php echo json_encode($monthly_data ?? []); ?>;

            // Prepare data for chart
            const labels = monthlyData.map(item => item.month);
            const incomeData = monthlyData.map(item => parseFloat(item.income));
            const expenseData = monthlyData.map(item => parseFloat(item.expense));

            new Chart(incomeExpensesCtx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Income',
                            data: incomeData,
                            backgroundColor: 'rgba(59, 209, 111, 0.7)',
                            borderColor: 'rgba(59, 209, 111, 1)',
                            borderWidth: 1,
                            borderRadius: 4,
                            barPercentage: 0.6,
                            categoryPercentage: 0.7
                        },
                        {
                            label: 'Expenses',
                            data: expenseData,
                            backgroundColor: 'rgba(239, 68, 68, 0.7)',
                            borderColor: 'rgba(239, 68, 68, 1)',
                            borderWidth: 1,
                            borderRadius: 4,
                            barPercentage: 0.6,
                            categoryPercentage: 0.7
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 5,
                                font: {
                                    size: 10
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#111827',
                            bodyColor: '#4B5563',
                            borderColor: '#E5E7EB',
                            borderWidth: 1,
                            padding: 10,
                            boxPadding: 5,
                            usePointStyle: true,
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': GH₵ ' + context.raw.toFixed(2);
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(229, 231, 235, 0.5)'
                            },
                            ticks: {
                                font: {
                                    size: 10
                                },
                                callback: function(value) {
                                    return 'GH₵ ' + value;
                                }
                            }
                        }
                    }
                }
            });
        }

        if (document.getElementById('monthlyGivingChart')) {
            // Monthly Giving Trends Chart
            const monthlyGivingCtx = document.getElementById('monthlyGivingChart').getContext('2d');

            // Get actual monthly giving data from PHP
            const monthlyGivingData = <?php
            // Get real monthly data for the last 12 months
            $monthlyData = [];
            for ($i = 11; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-$i months"));
                $monthName = date('M', strtotime("-$i months"));

                // Query actual data from database
                $titheQuery = "SELECT COALESCE(SUM(amount), 0) as total FROM finances WHERE category = 'tithe' AND DATE_FORMAT(transaction_date, '%Y-%m') = '$month'";
                $offeringQuery = "SELECT COALESCE(SUM(amount), 0) as total FROM finances WHERE category = 'offering' AND DATE_FORMAT(transaction_date, '%Y-%m') = '$month'";
                $pledgeQuery = "SELECT COALESCE(SUM(amount), 0) as total FROM finances WHERE category = 'pledge' AND DATE_FORMAT(transaction_date, '%Y-%m') = '$month'";

                try {
                    $titheStmt = $this->database->getConnection()->prepare($titheQuery);
                    $titheStmt->execute();
                    $titheAmount = $titheStmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

                    $offeringStmt = $this->database->getConnection()->prepare($offeringQuery);
                    $offeringStmt->execute();
                    $offeringAmount = $offeringStmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

                    $pledgeStmt = $this->database->getConnection()->prepare($pledgeQuery);
                    $pledgeStmt->execute();
                    $pledgeAmount = $pledgeStmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
                } catch (Exception $e) {
                    $titheAmount = 0;
                    $offeringAmount = 0;
                    $pledgeAmount = 0;
                }

                $monthlyData[] = [
                    'month' => $monthName,
                    'tithe' => (float)$titheAmount,
                    'offering' => (float)$offeringAmount,
                    'pledge' => (float)$pledgeAmount
                ];
            }
            echo json_encode($monthlyData);
            ?>;

            const labels = monthlyGivingData.map(item => item.month);
            const titheData = monthlyGivingData.map(item => item.tithe);
            const offeringData = monthlyGivingData.map(item => item.offering);
            const pledgeData = monthlyGivingData.map(item => item.pledge);

            new Chart(monthlyGivingCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Tithe',
                            data: titheData,
                            borderColor: 'rgba(34, 197, 94, 1)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'Offering',
                            data: offeringData,
                            borderColor: 'rgba(59, 130, 246, 1)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'Pledge',
                            data: pledgeData,
                            borderColor: 'rgba(168, 85, 247, 1)',
                            backgroundColor: 'rgba(168, 85, 247, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: GH₵ ${context.parsed.y.toLocaleString()}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'GH₵ ' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        // Add cute hover effects to icons
        const icons = document.querySelectorAll('.p-2\\.5.rounded-full');
        icons.forEach(icon => {
            icon.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1) rotate(5deg)';
            });
            icon.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) rotate(0deg)';
            });
        });

        // Show/hide delete confirmation modal
        window.showDeleteModal = function(id) {
            const modal = document.getElementById('deleteModal');
            const confirmButton = document.getElementById('confirmDelete');

            // Set the delete URL
            confirmButton.href = '<?php echo BASE_URL; ?>finance/delete?id=' + id;

            // Show the modal
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.classList.add('opacity-100');
                document.getElementById('modalContent').classList.add('scale-100');
            }, 50);
        };
    });
</script>

<div id="deleteModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 transition-opacity duration-300 opacity-0">
    <div class="relative top-20 mx-auto p-6 border w-96 shadow-xl rounded-xl bg-white transform scale-95 transition-all duration-300" id="modalContent">
        <div class="absolute top-3 right-3">
            <button id="closeModal" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4 animate-pulse">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-xl leading-6 font-bold text-gray-900">Delete Financial Record</h3>
            <div class="mt-4 px-2 py-3">
                <p class="text-gray-600">Are you sure you want to delete this financial record? This action cannot be undone.</p>
            </div>
            <div class="flex justify-center mt-6 space-x-3">
                <button id="cancelDelete" class="bg-white border border-gray-300 px-5 py-2.5 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300">
                    <i class="fas fa-times mr-2"></i> Cancel
                </button>
                <a id="confirmDelete" href="#" class="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 px-5 py-2.5 rounded-lg text-white transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <i class="fas fa-trash-alt mr-2"></i> Delete
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Accessibility Help Text -->
<div id="inactive-category-help" class="sr-only">
    This category is inactive but can still be accessed to view historical transaction data. To reactivate this category, visit the Category Management page.
</div>

<!-- Enhanced CSS for Inactive Categories -->
<style>
.category-inactive {
    position: relative;
    transition: all 0.3s ease;
}

.category-inactive::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(156, 163, 175, 0.1) 10px,
        rgba(156, 163, 175, 0.1) 20px
    );
    border-radius: 0.5rem;
    pointer-events: none;
    z-index: 1;
}

.category-inactive .font-semibold {
    color: #6b7280 !important;
}

.category-inactive .text-purple-600,
.category-inactive .text-emerald-600,
.category-inactive .text-red-600 {
    color: #9ca3af !important;
}

.category-inactive .text-purple-800,
.category-inactive .text-emerald-800,
.category-inactive .text-red-800 {
    color: #6b7280 !important;
}

.category-inactive:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-inactive:hover::before {
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(156, 163, 175, 0.15) 10px,
        rgba(156, 163, 175, 0.15) 20px
    );
}

/* Tooltip for inactive categories */
.category-inactive::after {
    content: 'This category is inactive but can still be accessed for viewing historical data';
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: #374151;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.category-inactive:hover::after {
    opacity: 1;
    visibility: visible;
    bottom: -35px;
}

/* Arrow for tooltip */
.category-inactive::before {
    content: '';
    position: absolute;
    bottom: -32px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #374151;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 11;
}

.category-inactive:hover::before {
    opacity: 1;
    visibility: visible;
    bottom: -27px;
}

/* Enhanced Performance: Smooth transitions with modern effects */
.group {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.group:hover {
    transform: translateY(-4px);
}

/* Modern card hover effects */
.group:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    animation: shimmer 0.6s ease-out;
    pointer-events: none;
    border-radius: inherit;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced button effects */
.group:hover .fas.fa-arrow-right {
    animation: bounce-right 0.6s ease-in-out;
}

@keyframes bounce-right {
    0%, 20%, 50%, 80%, 100% { transform: translateX(0); }
    40% { transform: translateX(4px); }
    60% { transform: translateX(2px); }
}

/* Loading state for category cards */
.category-loading {
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Background grid pattern for header */
.bg-grid-pattern {
    background-image:
        linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Enhanced gradient text */
.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

/* Improved shadow effects */
.hover\:shadow-purple-100\/50:hover {
    box-shadow: 0 10px 25px -3px rgba(196, 181, 253, 0.5), 0 4px 6px -2px rgba(196, 181, 253, 0.05);
}

.hover\:shadow-emerald-100\/50:hover {
    box-shadow: 0 10px 25px -3px rgba(167, 243, 208, 0.5), 0 4px 6px -2px rgba(167, 243, 208, 0.05);
}

.hover\:shadow-red-100\/50:hover {
    box-shadow: 0 10px 25px -3px rgba(254, 226, 226, 0.5), 0 4px 6px -2px rgba(254, 226, 226, 0.05);
}
</style>

<!-- Performance Metrics (Development) -->
<?php if (defined('SHOW_PERFORMANCE_METRICS') && SHOW_PERFORMANCE_METRICS): ?>
<div class="fixed bottom-4 right-4 bg-gray-800 text-white text-xs p-2 rounded shadow-lg opacity-75">
    <div>Categories loaded: <?php echo (isset($categoryStats) ? array_sum(array_column($categoryStats, 'total')) : 0); ?></div>
    <div>Load time: <?php echo number_format((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2); ?>ms</div>
</div>
<?php endif; ?>

<style>
    /* Custom styles for the finance page */
    /* Line clamp for description text */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Tooltip styles */
    .group/tooltip {
        position: relative;
    }

    /* Animated gradient background for buttons */
    .bg-gradient-to-r {
        background-size: 200% 200%;
        transition: all 0.3s ease;
    }

    .bg-gradient-to-r:hover {
        background-position: right center;
    }

    /* Subtle hover effect for cards */
    .rounded-xl {
        transition: all 0.3s ease;
    }

    /* Custom scrollbar */
    .overflow-x-auto::-webkit-scrollbar {
        height: 8px;
    }

    .overflow-x-auto::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .overflow-x-auto::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 10px;
    }

    .overflow-x-auto::-webkit-scrollbar-thumb:hover {
        background: #9ca3af;
    }

    /* Table row hover effect */
    tbody tr {
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
    }

    tbody tr:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border-left: 3px solid #3BD16F;
    }

    /* Table header styles */
    th {
        position: sticky;
        top: 0;
        z-index: 10;
    }

    /* Alternating row colors */
    tbody tr:nth-child(even) {
        background-color: rgba(249, 250, 251, 0.8);
    }

    /* Action buttons are now always visible */

    /* Filter styles */
    .filter-group {
        position: relative;
        transition: all 0.3s ease;
    }

    .filter-group:hover {
        transform: translateY(-2px);
    }

    .filter-label {
        display: flex;
        align-items: center;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .filter-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 6px;
        margin-right: 0.5rem;
        transition: all 0.3s ease;
    }

    .filter-group:hover .filter-icon {
        transform: scale(1.1);
    }

    .filter-input {
        width: 100%;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        padding: 0.75rem 1rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        background-color: #fff;
    }

    .filter-input:focus {
        border-color: #4CBF26;
        box-shadow: 0 0 0 3px rgba(76, 191, 38, 0.2);
        outline: none;
    }

    .filter-input:hover {
        border-color: #4CBF26;
    }

    .filter-clear {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 18px;
        height: 18px;
        background-color: #e5e7eb;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6b7280;
        font-size: 10px;
        cursor: pointer;
        opacity: 0;
        transition: all 0.2s ease;
        z-index: 5;
    }

    .filter-clear:hover {
        background-color: #d1d5db;
        color: #4b5563;
    }

    .filter-input:not(:placeholder-shown) ~ .filter-clear,
    .filter-input:not([value=""]) ~ .filter-clear,
    select.filter-input:not([value=""]) ~ .filter-clear {
        opacity: 1;
    }

    /* Active filter tag styles */
    .filter-tag {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        background-color: #f3f4f6;
        border: 1px solid #e5e7eb;
        border-radius: 9999px;
        font-size: 0.75rem;
        color: #4b5563;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        transition: all 0.2s ease;
    }

    .filter-tag:hover {
        background-color: #e5e7eb;
    }

    .filter-tag-close {
        margin-left: 0.5rem;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #d1d5db;
        color: #4b5563;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .filter-tag-close:hover {
        background-color: #9ca3af;
        color: #ffffff;
    }

    /* Animation for filter changes */
    @keyframes pulse-border {
        0% { border-color: #4CBF26; }
        50% { border-color: #74E39A; }
        100% { border-color: #4CBF26; }
    }

    .filter-pulse {
        animation: pulse-border 1.5s ease-in-out;
    }

    /* Hide clear button for date inputs in some browsers */
    input[type="date"]::-webkit-clear-button,
    input[type="date"]::-webkit-inner-spin-button,
    input[type="date"]::-webkit-calendar-picker-indicator {
        display: none;
        -webkit-appearance: none;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get all filter elements
        const searchInput = document.getElementById('table-search');
        const typeFilter = document.getElementById('type-filter');
        const categoryFilter = document.getElementById('category-filter');
        const startDateFilter = document.getElementById('start-date');
        const endDateFilter = document.getElementById('end-date');
        const resetFiltersButton = document.getElementById('reset-filters');
        const filterCountElement = document.getElementById('filter-count');
        const activeFiltersContainer = document.getElementById('active-filters');

        // Get all clear filter buttons
        const clearButtons = document.querySelectorAll('.filter-clear');

        // Store original table data for filtering
        const tableRows = document.querySelectorAll('tbody tr');
        const originalTableData = [];

        // Extract and store data from each row for faster filtering
        tableRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const rowData = {
                element: row,
                date: cells[0] ? cells[0].textContent.trim().toLowerCase() : '',
                category: cells[1] ? cells[1].textContent.trim().toLowerCase() : '',
                amount: cells[2] ? cells[2].textContent.trim().toLowerCase() : '',
                description: cells[3] ? cells[3].textContent.trim().toLowerCase() : '',
                recordedBy: cells[4] ? cells[4].textContent.trim().toLowerCase() : '',
                fullText: row.textContent.trim().toLowerCase(),
                // Determine if it's income or expense based on the category or amount indicator
                type: cells[2] && cells[2].querySelector('.text-red-600') ? 'expense' : 'income'
            };
            originalTableData.push(rowData);
        });

        // Function to apply all filters
        function applyFilters() {
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const type = typeFilter ? typeFilter.value.toLowerCase() : '';
            const category = categoryFilter ? categoryFilter.value.toLowerCase() : '';
            const startDate = startDateFilter ? startDateFilter.value : '';
            const endDate = endDateFilter ? endDateFilter.value : '';

            let visibleCount = 0;
            let activeFilterCount = 0;

            // Clear active filters display
            if (activeFiltersContainer) {
                activeFiltersContainer.innerHTML = '';
            }

            // Count active filters and create filter tags
            if (searchTerm) {
                activeFilterCount++;
                createFilterTag('search', 'Search: ' + searchTerm);
            }

            if (type) {
                activeFilterCount++;
                createFilterTag('type', 'Type: ' + (type === 'income' ? 'Income' : 'Expense'));
            }

            if (category) {
                activeFilterCount++;
                createFilterTag('category', 'Category: ' + category.charAt(0).toUpperCase() + category.slice(1));
            }

            if (startDate) {
                activeFilterCount++;
                createFilterTag('start-date', 'From: ' + formatDate(startDate));
            }

            if (endDate) {
                activeFilterCount++;
                createFilterTag('end-date', 'To: ' + formatDate(endDate));
            }

            // Update filter count display
            if (filterCountElement) {
                filterCountElement.textContent = activeFilterCount + (activeFilterCount === 1 ? ' filter applied' : ' filters applied');
            }

            // Apply filters to each row
            originalTableData.forEach(rowData => {
                let showRow = true;

                // Search filter
                if (searchTerm && !rowData.fullText.includes(searchTerm)) {
                    showRow = false;
                }

                // Type filter
                if (type && rowData.type !== type) {
                    showRow = false;
                }

                // Category filter
                if (category && !rowData.category.includes(category)) {
                    showRow = false;
                }

                // Date range filter
                if (startDate || endDate) {
                    // Extract date from the row data
                    const dateMatch = rowData.date.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/);
                    if (dateMatch) {
                        const [_, day, month, year] = dateMatch;
                        const rowDateObj = new Date(`${year}-${month}-${day}`);

                        if (startDate) {
                            const startDateObj = new Date(startDate);
                            if (rowDateObj < startDateObj) {
                                showRow = false;
                            }
                        }

                        if (endDate) {
                            const endDateObj = new Date(endDate);
                            if (rowDateObj > endDateObj) {
                                showRow = false;
                            }
                        }
                    }
                }

                // Show or hide the row
                rowData.element.style.display = showRow ? '' : 'none';
                if (showRow) visibleCount++;
            });

            // Update the count of visible transactions
            const countElement = document.querySelector('.text-sm.text-gray-600 span.font-bold');
            if (countElement) {
                countElement.textContent = visibleCount;

                // Add pulse animation to the count
                countElement.classList.add('filter-pulse');
                setTimeout(() => {
                    countElement.classList.remove('filter-pulse');
                }, 1500);
            }

            // Highlight the table if filters are active
            const tableContainer = document.querySelector('.bg-white.rounded-xl.shadow-lg.overflow-hidden');
            if (tableContainer) {
                if (activeFilterCount > 0) {
                    tableContainer.classList.add('border-primary');
                } else {
                    tableContainer.classList.remove('border-primary');
                }
            }
        }

        // Function to create filter tag
        function createFilterTag(filterType, text) {
            if (!activeFiltersContainer) return;

            const tag = document.createElement('div');
            tag.className = 'filter-tag';
            tag.innerHTML = `
                <span>${text}</span>
                <div class="filter-tag-close" data-filter="${filterType}">
                    <i class="fas fa-times"></i>
                </div>
            `;

            // Add click event to remove this filter
            const closeButton = tag.querySelector('.filter-tag-close');
            if (closeButton) {
                closeButton.addEventListener('click', function() {
                    clearFilter(this.dataset.filter);
                });
            }

            activeFiltersContainer.appendChild(tag);
        }

        // Function to clear a specific filter
        function clearFilter(filterType) {
            switch(filterType) {
                case 'search':
                    if (searchInput) searchInput.value = '';
                    break;
                case 'type':
                    if (typeFilter) typeFilter.value = '';
                    break;
                case 'category':
                    if (categoryFilter) categoryFilter.value = '';
                    break;
                case 'start-date':
                    if (startDateFilter) startDateFilter.value = '';
                    break;
                case 'end-date':
                    if (endDateFilter) endDateFilter.value = '';
                    break;
            }

            // Re-apply filters
            applyFilters();
        }

        // Function to reset all filters
        function resetAllFilters() {
            if (searchInput) searchInput.value = '';
            if (typeFilter) typeFilter.value = '';
            if (categoryFilter) categoryFilter.value = '';
            if (startDateFilter) startDateFilter.value = '';
            if (endDateFilter) endDateFilter.value = '';

            // Clear active filters display
            if (activeFiltersContainer) {
                activeFiltersContainer.innerHTML = '';
            }

            // Update filter count
            if (filterCountElement) {
                filterCountElement.textContent = '0 filters applied';
            }

            // Show all rows
            originalTableData.forEach(rowData => {
                rowData.element.style.display = '';
            });

            // Update the count of visible transactions
            const countElement = document.querySelector('.text-sm.text-gray-600 span.font-bold');
            if (countElement) {
                countElement.textContent = originalTableData.length;
            }

            // Remove highlight from table
            const tableContainer = document.querySelector('.bg-white.rounded-xl.shadow-lg.overflow-hidden');
            if (tableContainer) {
                tableContainer.classList.remove('border-primary');
            }
        }

        // Helper function to format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' });
        }

        // Add event listeners to all filter inputs for real-time filtering
        if (searchInput) {
            searchInput.addEventListener('input', applyFilters);
        }

        if (typeFilter) {
            typeFilter.addEventListener('change', applyFilters);
        }

        if (categoryFilter) {
            categoryFilter.addEventListener('change', applyFilters);
        }

        if (startDateFilter) {
            startDateFilter.addEventListener('change', applyFilters);
        }

        if (endDateFilter) {
            endDateFilter.addEventListener('change', applyFilters);
        }

        // Add event listeners to clear buttons
        clearButtons.forEach(button => {
            button.addEventListener('click', function() {
                clearFilter(this.dataset.filter);
            });
        });

        // Add event listener to reset button
        if (resetFiltersButton) {
            resetFiltersButton.addEventListener('click', resetAllFilters);
        }

        // Add hover effect to table rows
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('shadow-md');
            });

            row.addEventListener('mouseleave', function() {
                this.classList.remove('shadow-md');
            });
        });
    });
</script>

<script>
    // Delete confirmation functionality
    window.confirmDelete = function(id) {
        const modal = document.getElementById('deleteModal');
        const modalContent = document.getElementById('modalContent');
        const confirmBtn = document.getElementById('confirmDelete');

        modal.classList.remove('hidden');
        setTimeout(() => {
            modal.classList.add('opacity-100');
            modalContent.classList.add('scale-100');
        }, 10);

        confirmBtn.href = '<?php echo BASE_URL; ?>finance/delete?id=' + id;

        const closeModal = function() {
            modal.classList.remove('opacity-100');
            modalContent.classList.remove('scale-100');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        };

        document.getElementById('cancelDelete').addEventListener('click', closeModal);
        document.getElementById('closeModal').addEventListener('click', closeModal);

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                closeModal();
            }
        });
    };
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation to summary cards
        const animateValue = (element, start, end, duration) => {
            let startTimestamp = null;
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                const value = Math.floor(progress * (end - start) + start);
                element.innerHTML = 'GH₵ ' + value.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            window.requestAnimationFrame(step);
        };

        // Apply animations with a slight delay for visual effect
        setTimeout(() => {
            const summaryCards = document.querySelectorAll('.grid-cols-3 > div');
            summaryCards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('animate-fadeIn');
                }, index * 200);
            });
        }, 300);
    });
</script>

</div> <!-- Close main container -->
