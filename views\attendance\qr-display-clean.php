<?php
/**
 * QR Code Display for Attendance - Clean Version
 * This version should work without any JavaScript printing issues
 */

// Set default values if not provided
$service = $service ?? ['name' => 'Service', 'id' => 1];
$qr_session = $qr_session ?? (object)['attendance_date' => date('Y-m-d'), 'expires_at' => date('Y-m-d H:i:s', strtotime('+30 minutes'))];
$token = $token ?? ($_GET['token'] ?? 'default-token');

// Ensure BASE_URL is defined
if (!defined('BASE_URL')) {
    define('BASE_URL', '/icgc/');
}

// Generate full URL for QR code (QR services need full URLs)
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';

// For testing: Use local IP address if on localhost
if ($host === 'localhost' || strpos($host, '127.0.0.1') !== false) {
    // Try to get local IP address for mobile testing
    $local_ip = null;
    if (function_exists('exec') && strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        exec('ipconfig', $output);
        foreach ($output as $line) {
            if (strpos($line, 'IPv4 Address') !== false && (strpos($line, '192.168.') !== false || strpos($line, '172.') !== false)) {
                preg_match('/(\d+\.\d+\.\d+\.\d+)/', $line, $matches);
                if (isset($matches[1])) {
                    $local_ip = $matches[1];
                    break;
                }
            }
        }
    }
    if ($local_ip) {
        $host = $local_ip;
    }
}

$full_base_url = $protocol . '://' . $host . BASE_URL;
$qr_url = $qr_url ?? ($full_base_url . 'attendance/qr-scan?token=' . $token);

// Use Google Charts QR service as primary (more reliable)
$qr_image_url = $qr_image_url ?? ('https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=' . urlencode($qr_url));

// Security: Only show debug in development environment
if (isset($_GET['debug']) && defined('APP_ENV') && APP_ENV === 'development') {
    echo "<div style='background: #fff3cd; padding: 10px; margin: 10px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<h3 style='color: #856404;'>Debug Information (Development Only):</h3>";
    echo "<p><strong>BASE_URL:</strong> " . htmlspecialchars(BASE_URL) . "</p>";
    echo "<p><strong>Token:</strong> " . htmlspecialchars(substr($token, 0, 8)) . "..." . "</p>";
    echo "<p><strong>QR URL:</strong> " . htmlspecialchars($qr_url) . "</p>";
    echo "</div>";
}
$present_members = $present_members ?? [];
$late_members = $late_members ?? [];

// Helper functions
if (!function_exists('format_date')) {
    function format_date($date, $format = 'F j, Y') {
        return date($format, strtotime($date));
    }
}

if (!function_exists('time_ago')) {
    function time_ago($datetime) {
        $time = time() - strtotime($datetime);
        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . ' minutes ago';
        if ($time < 86400) return floor($time/3600) . ' hours ago';
        return floor($time/86400) . ' days ago';
    }
}
?>
<div class="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-primary px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-semibold text-white">QR Code for Attendance</h1>
                <a href="<?php echo BASE_URL; ?>attendance/qr" class="text-white bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded-md text-sm flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </a>
            </div>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- QR Code Section -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-8 transition-all duration-300 hover:shadow-xl flex flex-col items-center justify-center">
                    <div class="mb-4 text-center">
                        <h2 class="text-lg font-semibold text-gray-800"><?php echo htmlspecialchars($service['name']); ?></h2>
                        <p class="text-sm text-gray-600"><?php echo format_date($qr_session->attendance_date); ?></p>
                        
                        <div class="mt-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <span class="animate-pulse mr-1.5 relative flex h-2 w-2">
                                <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                                <span class="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                            </span>
                            Active
                        </div>
                    </div>
                    
                    <div class="relative mb-6 p-4 bg-white rounded-xl border-2 border-primary-100 shadow-lg">
                        <div class="absolute -top-3 -left-3 w-6 h-6 bg-primary rounded-full"></div>
                        <div class="absolute -top-3 -right-3 w-6 h-6 bg-primary rounded-full"></div>
                        <div class="absolute -bottom-3 -left-3 w-6 h-6 bg-primary rounded-full"></div>
                        <div class="absolute -bottom-3 -right-3 w-6 h-6 bg-primary rounded-full"></div>
                        
                        <!-- QR code image -->
                        <img id="qrCodeImage" src="<?php echo htmlspecialchars($qr_image_url); ?>"
                             alt="QR Code" class="mx-auto w-64 h-64"
                             onerror="handleQRError(this)">

                        <!-- Error message if QR code fails to load -->
                        <div id="qr-error" style="display: none;" class="text-center p-4 bg-red-50 border border-red-200 rounded-md">
                            <p class="text-red-600 mb-2">QR Code failed to load from external services</p>
                            <div class="space-x-2">
                                <button onclick="retryQRCode()" class="px-3 py-1 bg-blue-600 text-white rounded text-sm">Retry External Services</button>
                                <button onclick="generateQRCodeClientSide('<?php echo htmlspecialchars($qr_url, ENT_QUOTES, 'UTF-8'); ?>')" class="px-3 py-1 bg-green-600 text-white rounded text-sm">Generate Offline</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4 space-y-2">
                        <p class="text-sm text-gray-600">
                            <i class="fas fa-clock mr-1 text-primary"></i> 
                            Expires: 
                            <span class="font-medium" id="expiryTime">
                                <?php echo date('h:i A', strtotime($qr_session->expires_at)); ?>
                            </span>
                            (<span id="countdown"></span>)
                        </p>
                        
                        <div class="text-center mt-4">
                            <p class="text-sm text-gray-600 mb-3">
                                <i class="fas fa-info-circle mr-1"></i>
                                Print or download this QR code to share with members
                            </p>
                            <div class="flex justify-center space-x-3">
                                <button onclick="printQRCode()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200" title="Print QR code with instructions">
                                    <i class="fas fa-print mr-2"></i> Print QR Code
                                </button>

                                <button onclick="downloadQRCode()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200" title="Download QR code as PNG image">
                                    <i class="fas fa-download mr-2"></i> Download PNG
                                </button>

                                <button onclick="refreshQRStats()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200" title="Refresh attendance statistics">
                                    <i class="fas fa-sync-alt mr-2"></i> Refresh Stats
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Instructions and Stats Section -->
                <div class="space-y-6">
                    <!-- Instructions -->
                    <div class="bg-blue-50 rounded-xl shadow-md p-6 border border-blue-100">
                        <h2 class="text-lg font-semibold mb-3 text-gray-800">How to Use</h2>
                        
                        <div class="space-y-3">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                    <span class="text-xs font-bold">1</span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-700">Display this QR code on a screen where members can see it.</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                    <span class="text-xs font-bold">2</span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-700">Members scan the QR code with their smartphone camera.</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                    <span class="text-xs font-bold">3</span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-700">They'll be taken to a page where they can search for their name.</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 rounded-full bg-blue-500 text-white flex items-center justify-center">
                                    <span class="text-xs font-bold">4</span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-700">After finding themselves, they mark themselves present or late.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Real-time Stats -->
                    <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                        <h2 class="text-lg font-semibold mb-3 text-gray-800">Real-time Attendance Stats</h2>
                        
                        <div id="stats-container">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div class="bg-green-50 rounded-md p-4 border border-green-100">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm font-medium text-green-800">Present</p>
                                            <h3 class="text-2xl font-bold text-green-600"><?php echo count($present_members); ?></h3>
                                        </div>
                                        <div class="p-3 rounded-full bg-green-100">
                                            <i class="fas fa-user-check text-xl text-green-500"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-yellow-50 rounded-md p-4 border border-yellow-100">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm font-medium text-yellow-800">Late</p>
                                            <h3 class="text-2xl font-bold text-yellow-600"><?php echo count($late_members); ?></h3>
                                        </div>
                                        <div class="p-3 rounded-full bg-yellow-100">
                                            <i class="fas fa-clock text-xl text-yellow-500"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Recent Activity -->
                            <div class="flex justify-between items-center mb-2">
                                <h3 class="text-md font-medium text-gray-700">Recent Activity</h3>
                                <a href="<?php echo BASE_URL; ?>attendance/stats-dashboard?token=<?php echo htmlspecialchars($token, ENT_QUOTES, 'UTF-8'); ?>"
                                   class="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center">
                                    <i class="fas fa-list mr-1"></i>
                                    Recent Attendance
                                </a>
                            </div>
                            <div class="overflow-hidden rounded-md border border-gray-200 max-h-60 overflow-y-auto">
                                <div class="px-4 py-3 bg-gray-50 text-sm text-gray-500 text-center">
                                    No attendance records yet. Members will appear here as they mark attendance.
                                    <br><br>
                                    <a href="<?php echo BASE_URL; ?>attendance/stats-dashboard?token=<?php echo htmlspecialchars($token, ENT_QUOTES, 'UTF-8'); ?>"
                                       class="text-blue-600 hover:text-blue-800 font-medium">
                                        View Full Dashboard →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
(function() {
    'use strict';

    // Set up countdown timer
    function updateCountdown() {
        const expiryTime = new Date("<?php echo $qr_session->expires_at; ?>").getTime();
        const now = new Date().getTime();
        const timeLeft = expiryTime - now;

        const countdownElement = document.getElementById('countdown');
        if (!countdownElement) return;

        if (timeLeft <= 0) {
            countdownElement.innerHTML = "Expired";
            return;
        }

        const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

        let countdownText = "";
        if (hours > 0) countdownText += hours + "h ";
        countdownText += minutes + "m " + seconds + "s";

        countdownElement.innerHTML = countdownText;
    }

    // Print QR code function with enhanced styling
    window.printQRCode = function() {
        const serviceName = "<?php echo htmlspecialchars($service['name'], ENT_QUOTES, 'UTF-8'); ?>";
        const serviceDate = "<?php echo format_date($qr_session->attendance_date); ?>";
        const serviceTime = "<?php echo isset($service['time']) ? date('g:i A', strtotime($service['time'])) : ''; ?>";
        const expiryTime = "<?php echo date('g:i A', strtotime($qr_session->expires_at)); ?>";
        const qrUrl = "<?php echo $qr_url; ?>";

        // Show loading state
        const printBtn = event.target.closest('button');
        const originalText = printBtn.innerHTML;
        printBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Preparing...';
        printBtn.disabled = true;

        // Function to restore button state
        const restoreButton = () => {
            printBtn.innerHTML = originalText;
            printBtn.disabled = false;
        };

        // Generate QR code for printing
        generateQRForPrint(serviceName, serviceDate, serviceTime, expiryTime, qrUrl, restoreButton);
    };

    // Generate QR code specifically for printing
    function generateQRForPrint(serviceName, serviceDate, serviceTime, expiryTime, qrUrl, callback) {
        // Load QR code library if not already loaded
        if (typeof QRCode === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js';
            script.onload = function() {
                createPrintWindow(serviceName, serviceDate, serviceTime, expiryTime, qrUrl, callback);
            };
            script.onerror = function() {
                // Fallback to external QR service
                let qrImageUrl = "<?php echo htmlspecialchars($qr_image_url, ENT_QUOTES, 'UTF-8'); ?>";
                if (window.clientSideQRUrl) {
                    qrImageUrl = window.clientSideQRUrl;
                }
                createPrintWindowWithImage(serviceName, serviceDate, serviceTime, expiryTime, qrImageUrl, callback);
            };
            document.head.appendChild(script);
        } else {
            createPrintWindow(serviceName, serviceDate, serviceTime, expiryTime, qrUrl, callback);
        }
    }

    // Create print window with generated QR code
    function createPrintWindow(serviceName, serviceDate, serviceTime, expiryTime, qrUrl, callback) {
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 400;

        QRCode.toCanvas(canvas, qrUrl, {
            width: 400,
            margin: 4,
            color: {
                dark: '#000000',
                light: '#ffffff'
            }
        }, function(error) {
            if (error) {
                console.error('Error generating QR code for print:', error);
                // Fallback to external QR service
                let qrImageUrl = "<?php echo htmlspecialchars($qr_image_url, ENT_QUOTES, 'UTF-8'); ?>";
                if (window.clientSideQRUrl) {
                    qrImageUrl = window.clientSideQRUrl;
                }
                createPrintWindowWithImage(serviceName, serviceDate, serviceTime, expiryTime, qrImageUrl, callback);
            } else {
                const qrDataUrl = canvas.toDataURL('image/png');
                createPrintWindowWithImage(serviceName, serviceDate, serviceTime, expiryTime, qrDataUrl, callback);
            }
        });
    }

    // Create print window with QR image
    function createPrintWindowWithImage(serviceName, serviceDate, serviceTime, expiryTime, qrImageUrl, callback) {
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
            alert('Please allow pop-ups to print the QR code');
            callback();
            return;
        }

        const currentDate = new Date().toLocaleDateString();
        const currentTime = new Date().toLocaleTimeString();

        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>QR Code - ${serviceName}</title>
                <style>
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 30px;
                        text-align: center;
                        background: white;
                        color: #333;
                    }
                    .header {
                        margin-bottom: 30px;
                        border-bottom: 3px solid #3F7D58;
                        padding-bottom: 20px;
                    }
                    .church-name {
                        color: #3F7D58;
                        font-size: 28px;
                        font-weight: bold;
                        margin: 0 0 10px 0;
                    }
                    .service-title {
                        color: #2563eb;
                        font-size: 24px;
                        font-weight: 600;
                        margin: 0 0 5px 0;
                    }
                    .service-details {
                        color: #666;
                        font-size: 16px;
                        margin: 0;
                    }
                    .qr-container {
                        border: 3px solid #3F7D58;
                        border-radius: 15px;
                        padding: 30px;
                        display: inline-block;
                        margin: 30px 0;
                        background: #f8fffe;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                    }
                    .qr-code {
                        width: 350px;
                        height: 350px;
                        border-radius: 8px;
                    }
                    .instructions {
                        margin: 30px 0;
                        padding: 20px;
                        background: #f0f9ff;
                        border: 1px solid #bae6fd;
                        border-radius: 10px;
                        max-width: 500px;
                        margin-left: auto;
                        margin-right: auto;
                    }
                    .instructions h3 {
                        color: #0369a1;
                        margin: 0 0 15px 0;
                        font-size: 18px;
                    }
                    .instructions ol {
                        text-align: left;
                        color: #0c4a6e;
                        line-height: 1.6;
                    }
                    .footer {
                        margin-top: 30px;
                        padding-top: 20px;
                        border-top: 2px solid #e5e7eb;
                        color: #666;
                        font-size: 14px;
                    }
                    .expiry-notice {
                        background: #fef3c7;
                        border: 1px solid #f59e0b;
                        border-radius: 8px;
                        padding: 15px;
                        margin: 20px 0;
                        color: #92400e;
                        font-weight: 500;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 class="church-name">ICGC Emmanuel Temple</h1>
                    <h2 class="service-title">${serviceName}</h2>
                    <p class="service-details">
                        ${serviceDate}${serviceTime ? ' • ' + serviceTime : ''}
                    </p>
                </div>

                <div class="qr-container">
                    <img src="${qrImageUrl}" alt="QR Code for ${serviceName}" class="qr-code"
                         onload="setTimeout(() => { window.print(); setTimeout(() => window.close(), 1000); }, 500);"
                         onerror="document.body.innerHTML='<h2>Error loading QR code. Please try again.</h2>';">
                </div>

                <div class="instructions">
                    <h3><i class="fas fa-mobile-alt"></i> How to Mark Attendance</h3>
                    <ol>
                        <li>Open your phone's camera app</li>
                        <li>Point the camera at this QR code</li>
                        <li>Tap the notification that appears</li>
                        <li>Select your name and mark attendance</li>
                    </ol>
                </div>

                ${expiryTime ? `
                <div class="expiry-notice">
                    <strong>⏰ QR Code expires at ${expiryTime}</strong><br>
                    Please scan before this time to mark your attendance.
                </div>
                ` : ''}

                <div class="footer">
                    <p>Generated on ${currentDate} at ${currentTime}</p>
                    <p>For technical support, contact the church administration</p>
                </div>
            </body>
            </html>
        `);

        printWindow.document.close();
        callback();
    }

    // Download QR code function with CORS handling
    window.downloadQRCode = function() {
        const serviceName = "<?php echo htmlspecialchars($service['name'], ENT_QUOTES, 'UTF-8'); ?>";
        const serviceDate = "<?php echo date('Y-m-d', strtotime($qr_session->attendance_date)); ?>";
        const fileName = "QR_Code_" + serviceName.replace(/\s+/g, '_') + "_" + serviceDate + ".png";

        // Show loading state
        const downloadBtn = event.target.closest('button');
        const originalText = downloadBtn.innerHTML;
        downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Downloading...';
        downloadBtn.disabled = true;

        // Function to restore button state
        const restoreButton = () => {
            downloadBtn.innerHTML = originalText;
            downloadBtn.disabled = false;
        };

        // Try to use client-side generated QR first
        if (window.clientSideQRUrl) {
            try {
                const link = document.createElement('a');
                link.href = window.clientSideQRUrl;
                link.download = fileName;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                restoreButton();
                showNotification('QR code downloaded successfully!', 'success');
                return;
            } catch (error) {
                console.log('Client-side download failed, trying server method');
            }
        }

        // Fallback: Generate QR code on canvas and download
        generateQRForDownload(fileName, restoreButton);
    };

    // Generate QR code specifically for download
    function generateQRForDownload(fileName, callback) {
        const qrUrl = "<?php echo $qr_url; ?>";

        // Load QR code library if not already loaded
        if (typeof QRCode === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js';
            script.onload = function() {
                generateQRCanvas(qrUrl, fileName, callback);
            };
            script.onerror = function() {
                alert('Failed to load QR code library. Please try again.');
                callback();
            };
            document.head.appendChild(script);
        } else {
            generateQRCanvas(qrUrl, fileName, callback);
        }
    }

    // Generate QR code on canvas for download
    function generateQRCanvas(url, fileName, callback) {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;

        QRCode.toCanvas(canvas, url, {
            width: 512,
            margin: 4,
            color: {
                dark: '#000000',
                light: '#ffffff'
            }
        }, function(error) {
            if (error) {
                console.error('Error generating QR code for download:', error);
                alert('Failed to generate QR code. Please try again.');
            } else {
                // Convert canvas to blob and download
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = fileName;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    // Show success notification
                    showNotification('QR code downloaded successfully!', 'success');
                }, 'image/png');
            }
            callback();
        });
    }

    // QR code error handling with automatic fallback
    let qrServiceIndex = 0;
    const qrServices = [
        'https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=',
        'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data='
    ];

    window.handleQRError = function(img) {
        const qrError = document.getElementById('qr-error');
        const qrUrl = "<?php echo $qr_url; ?>"; // Use unencoded URL for client-side generation

        // Try next service automatically
        qrServiceIndex++;
        if (qrServiceIndex < qrServices.length) {
            img.onerror = function() { window.handleQRError(img); };
            img.src = qrServices[qrServiceIndex] + encodeURIComponent(qrUrl) + '&timestamp=' + Date.now();
        } else {
            // All external services failed, try client-side generation
            generateQRCodeClientSide(qrUrl);
        }
    };

    // Generate QR code using client-side library
    window.generateQRCodeClientSide = function(url) {
        const qrContainer = document.querySelector('.relative.mb-6.p-4');
        const qrImage = document.getElementById('qrCodeImage');
        const qrError = document.getElementById('qr-error');

        // Hide the failed image and error
        if (qrImage) qrImage.style.display = 'none';
        if (qrError) qrError.style.display = 'none';

        // Show loading message
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'qr-loading';
        loadingDiv.className = 'text-center p-4 bg-blue-50 border border-blue-200 rounded-md';
        loadingDiv.innerHTML = '<p class="text-blue-600"><i class="fas fa-spinner fa-spin mr-2"></i>Generating QR code offline...</p>';
        if (qrContainer) {
            qrContainer.appendChild(loadingDiv);
        }

        // Load QR code library dynamically
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js';
        script.onload = function() {
            // Create a canvas element
            const canvas = document.createElement('canvas');
            canvas.id = 'qrCodeCanvas';
            canvas.className = 'mx-auto';
            canvas.width = 256;
            canvas.height = 256;

            // Insert canvas after the image
            if (qrImage && qrImage.parentNode) {
                qrImage.parentNode.insertBefore(canvas, qrImage.nextSibling);
            } else if (qrContainer) {
                qrContainer.appendChild(canvas);
            }

            // Generate QR code
            QRCode.toCanvas(canvas, url, {
                width: 256,
                margin: 4,
                color: {
                    dark: '#000000',
                    light: '#ffffff'
                }
            }, function(error) {
                const loadingDiv = document.getElementById('qr-loading');
                if (loadingDiv) loadingDiv.remove();

                if (error) {
                    // Show error message as last resort
                    if (qrError) {
                        qrError.style.display = 'block';
                    }
                } else {
                    // Update global qrImageUrl for print/download functions
                    window.clientSideQRUrl = canvas.toDataURL();

                    // Show success message briefly
                    const successDiv = document.createElement('div');
                    successDiv.className = 'text-center p-2 bg-green-50 border border-green-200 rounded-md mt-2';
                    successDiv.innerHTML = '<p class="text-green-600 text-sm"><i class="fas fa-check mr-1"></i>QR code generated offline successfully!</p>';
                    if (qrContainer) {
                        qrContainer.appendChild(successDiv);
                        setTimeout(() => successDiv.remove(), 3000);
                    }
                }
            });
        };

        script.onerror = function() {
            console.error('Failed to load QR code library');
            const loadingDiv = document.getElementById('qr-loading');
            if (loadingDiv) loadingDiv.remove();

            // Show error as last resort
            if (qrError) {
                qrError.style.display = 'block';
            }
        };

        document.head.appendChild(script);
    };

    window.retryQRCode = function() {
        const qrImage = document.getElementById('qrCodeImage');
        const qrError = document.getElementById('qr-error');
        const qrCanvas = document.getElementById('qrCodeCanvas');
        const qrUrl = "<?php echo $qr_url; ?>";

        // Remove any existing canvas
        if (qrCanvas) {
            qrCanvas.remove();
        }

        if (qrImage && qrError) {
            qrServiceIndex = 0; // Reset to first service
            qrError.style.display = 'none';
            qrImage.style.display = 'block';
            qrImage.onerror = function() { window.handleQRError(qrImage); };
            qrImage.src = qrServices[qrServiceIndex] + encodeURIComponent(qrUrl) + '&timestamp=' + Date.now();
        }
    };

    // Refresh stats function
    window.refreshQRStats = function() {
        const statsContainer = document.getElementById('stats-container');
        const baseUrl = "<?php echo BASE_URL; ?>";
        const token = "<?php echo htmlspecialchars($token, ENT_QUOTES, 'UTF-8'); ?>";

        if (!statsContainer) {
            console.error('Stats container not found');
            return;
        }

        statsContainer.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin mr-2"></i>Updating stats...</div>';

        fetch(baseUrl + 'api/qr-stats.php?token=' + token)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    statsContainer.innerHTML = data.html;
                } else {
                    statsContainer.innerHTML = '<div class="text-center py-4 text-red-600"><i class="fas fa-exclamation-triangle mr-2"></i>Failed to update stats</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                statsContainer.innerHTML = '<div class="text-center py-4 text-red-600"><i class="fas fa-wifi mr-2"></i>Network error</div>';
            });
    };

    // Show notification function
    window.showNotification = function(message, type = 'info') {
        // Remove any existing notifications
        const existingNotification = document.querySelector('.qr-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `qr-notification fixed top-4 right-4 z-50 px-4 py-3 rounded-md shadow-lg transition-all duration-300 transform translate-x-full`;

        // Set colors based on type
        if (type === 'success') {
            notification.className += ' bg-green-100 border border-green-400 text-green-700';
            notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
        } else if (type === 'error') {
            notification.className += ' bg-red-100 border border-red-400 text-red-700';
            notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
        } else {
            notification.className += ' bg-blue-100 border border-blue-400 text-blue-700';
            notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
        }

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            updateCountdown();
            setInterval(updateCountdown, 1000);
            setTimeout(window.refreshQRStats, 1000);
            setInterval(window.refreshQRStats, 30000);
        });
    } else {
        updateCountdown();
        setInterval(updateCountdown, 1000);
        setTimeout(window.refreshQRStats, 1000);
        setInterval(window.refreshQRStats, 30000);
    }
})();
</script>
