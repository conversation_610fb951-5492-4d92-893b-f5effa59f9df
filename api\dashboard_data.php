<?php
/**
 * Dashboard Data API
 *
 * Provides data for the dashboard view with caching for improved performance
 */

// Include bootstrap to ensure proper initialization
require_once dirname(__DIR__) . '/bootstrap.php';

// Set JSON header
header('Content-Type: application/json');

// Start session if not already started
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

try {
    // Get requested action
    $action = isset($_GET['action']) ? $_GET['action'] : 'default';

    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();

    // Process based on action
    switch ($action) {
        case 'financial_overview':
            $period = isset($_GET['period']) ? $_GET['period'] : '6months';
            $data = get_financial_overview_data($conn, $period);
            break;

        case 'attendance_trends':
            $period = isset($_GET['period']) ? $_GET['period'] : 'monthly';
            $data = get_attendance_trends_data($conn, $period);
            break;

        case 'notifications':
            $data = get_notifications_data($conn);
            break;

        default:
            // Default dashboard includes all data types
            $data = [
                'members' => get_members_dashboard_data($conn),
                'attendance' => get_attendance_dashboard_data($conn),
                'finance' => get_finance_dashboard_data($conn),
            ];
            break;
    }

    // Return success response with proper structure
    $response = [
        'success' => true,
        'labels' => $data['labels'] ?? null,
        'data' => $data['data'] ?? null,
        'income' => $data['income'] ?? null,
        'expenses' => $data['expenses'] ?? null
    ];

    echo json_encode($response);

} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Get financial overview data for charts
 *
 * @param PDO $conn Database connection
 * @param string $period Period for data (3months, 6months, yearly)
 * @return array Financial overview data
 */
function get_financial_overview_data($conn, $period) {
    // Determine date range based on period
    switch ($period) {
        case '3months':
            $months = 3;
            $date_format = '%Y-%m';
            $label_format = 'M Y';
            break;
        case 'yearly':
            $months = 12;
            $date_format = '%Y-%m';
            $label_format = 'M Y';
            break;
        case '6months':
        default:
            $months = 6;
            $date_format = '%Y-%m';
            $label_format = 'M Y';
            break;
    }

    // No tenant filtering needed for single tenant system

    // Get income data
    $income_query = "SELECT DATE_FORMAT(transaction_date, '$date_format') as period,
                            SUM(amount) as total
                     FROM finances
                     WHERE category IN ('tithe', 'offering', 'donation', 'fundraising', 'other_income')
                     AND transaction_date >= DATE_SUB(CURDATE(), INTERVAL $months MONTH)
                     GROUP BY DATE_FORMAT(transaction_date, '$date_format')
                     ORDER BY period ASC";

    $stmt = $conn->prepare($income_query);
    $stmt->execute();
    $income_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get expenses data
    $expenses_query = "SELECT DATE_FORMAT(transaction_date, '$date_format') as period,
                              SUM(amount) as total
                       FROM finances
                       WHERE category IN ('utilities', 'maintenance', 'supplies', 'salaries', 'other_expense')
                       AND transaction_date >= DATE_SUB(CURDATE(), INTERVAL $months MONTH)
                       GROUP BY DATE_FORMAT(transaction_date, '$date_format')
                       ORDER BY period ASC";

    $stmt = $conn->prepare($expenses_query);
    $stmt->execute();
    $expenses_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Create labels for the last N months
    $labels = [];
    $income = [];
    $expenses = [];

    for ($i = $months - 1; $i >= 0; $i--) {
        $date = new DateTime();
        $date->sub(new DateInterval("P{$i}M"));
        $period_key = $date->format('Y-m');
        $label = $date->format($label_format);

        $labels[] = $label;

        // Find income for this period
        $income_amount = 0;
        foreach ($income_data as $income_row) {
            if ($income_row['period'] === $period_key) {
                $income_amount = (float)$income_row['total'];
                break;
            }
        }
        $income[] = $income_amount;

        // Find expenses for this period
        $expenses_amount = 0;
        foreach ($expenses_data as $expense_row) {
            if ($expense_row['period'] === $period_key) {
                $expenses_amount = (float)$expense_row['total'];
                break;
            }
        }
        $expenses[] = $expenses_amount;
    }

    return [
        'success' => true,
        'labels' => $labels,
        'income' => $income,
        'expenses' => $expenses
    ];
}

/**
 * Get attendance trends data for charts
 *
 * @param PDO $conn Database connection
 * @param string $period Period for data (weekly, monthly, yearly)
 * @return array Attendance trends data
 */
function get_attendance_trends_data($conn, $period) {
    // No tenant filtering needed for single tenant system

    // Determine query based on period
    switch ($period) {
        case 'weekly':
            // Last 8 weeks
            $query = "SELECT DATE_FORMAT(a.attendance_date, '%Y-%u') as period,
                             CONCAT('Week ', WEEK(a.attendance_date)) as label,
                             COUNT(DISTINCT a.member_id) as attendance_count
                      FROM attendance a
                      WHERE a.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 8 WEEK)
                      AND a.status IN ('present', 'late')
                      GROUP BY DATE_FORMAT(a.attendance_date, '%Y-%u')
                      ORDER BY period ASC";
            break;

        case 'yearly':
            // Last 3 years
            $query = "SELECT YEAR(a.attendance_date) as period,
                             YEAR(a.attendance_date) as label,
                             COUNT(DISTINCT a.member_id) as attendance_count
                      FROM attendance a
                      WHERE a.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)
                      AND a.status IN ('present', 'late')
                      GROUP BY YEAR(a.attendance_date)
                      ORDER BY period ASC";
            break;

        case 'monthly':
        default:
            // Last 6 months
            $query = "SELECT DATE_FORMAT(a.attendance_date, '%Y-%m') as period,
                             DATE_FORMAT(a.attendance_date, '%M %Y') as label,
                             COUNT(DISTINCT a.member_id) as attendance_count
                      FROM attendance a
                      WHERE a.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                      AND a.status IN ('present', 'late')
                      GROUP BY DATE_FORMAT(a.attendance_date, '%Y-%m')
                      ORDER BY period ASC";
            break;
    }

    $stmt = $conn->prepare($query);
    $stmt->execute();
    $attendance_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $labels = [];
    $data = [];

    foreach ($attendance_data as $row) {
        $labels[] = $row['label'];
        $data[] = (int)$row['attendance_count'];
    }

    return [
        'success' => true,
        'labels' => $labels,
        'data' => $data
    ];
}

/**
 * Get notifications data
 *
 * @param PDO $conn Database connection
 * @return array Notifications data
 */
function get_notifications_data($conn) {
    $notifications = [];
    $count = 0;

    // Check for birthdays today
    $birthday_query = "SELECT COUNT(*) as count FROM members
                       WHERE DATE_FORMAT(date_of_birth, '%m-%d') = DATE_FORMAT(CURDATE(), '%m-%d')
                       AND member_status = 'active'";

    $stmt = $conn->prepare($birthday_query);
    $stmt->execute();
    $birthday_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($birthday_count > 0) {
        $notifications[] = [
            'type' => 'birthday',
            'message' => "$birthday_count member" . ($birthday_count > 1 ? 's have' : ' has') . " birthday" . ($birthday_count > 1 ? 's' : '') . " today!"
        ];
        $count++;
    }

    return [
        'notifications' => $notifications,
        'count' => $count
    ];
}

/**
 * Get members dashboard data
 *
 * @param PDO $db Database connection
 * @return array Members dashboard data
 */
function get_members_dashboard_data($db) {
    // No tenant filtering needed for single tenant system

    // Total members
    $stmt = $db->query("SELECT COUNT(*) as total FROM members WHERE member_status = 'active'");
    $total_members = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // New members this month
    $stmt = $db->query("SELECT COUNT(*) as total FROM members WHERE member_status = 'active' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
    $new_members = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Members by gender
    $stmt = $db->query("SELECT gender, COUNT(*) as count FROM members WHERE member_status = 'active' GROUP BY gender");
    $members_by_gender = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Members by department
    $stmt = $db->query("SELECT department, COUNT(*) as count FROM members WHERE member_status = 'active' GROUP BY department ORDER BY count DESC");
    $members_by_department = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'total_members' => $total_members,
        'new_members' => $new_members,
        'members_by_gender' => $members_by_gender,
        'members_by_department' => $members_by_department
    ];
}

/**
 * Get attendance dashboard data
 *
 * @param PDO $db Database connection
 * @return array Attendance dashboard data
 */
function get_attendance_dashboard_data($db) {
    // No tenant filtering needed for single tenant system

    // Last service attendance
    $stmt = $db->query("
        SELECT attendance_date, service_id, COUNT(*) as total_attendance
        FROM attendance
        WHERE status IN ('present', 'late')
        GROUP BY attendance_date, service_id
        ORDER BY attendance_date DESC
        LIMIT 1
    ");
    $last_service = $stmt->fetch(PDO::FETCH_ASSOC);

    // Attendance trend for last 6 services
    $stmt = $db->query("
        SELECT attendance_date, service_id, COUNT(*) as total_attendance
        FROM attendance
        WHERE status IN ('present', 'late')
        GROUP BY attendance_date, service_id
        ORDER BY attendance_date DESC
        LIMIT 6
    ");
    $attendance_trend = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'last_service' => $last_service,
        'attendance_trend' => $attendance_trend
    ];
}

/**
 * Get finance dashboard data
 *
 * @param PDO $db Database connection
 * @return array Finance dashboard data
 */
function get_finance_dashboard_data($db) {
    // Single tenant - no filtering needed

    // Total income this month
    $stmt = $db->query("
        SELECT SUM(amount) as total
        FROM finances
        WHERE category IN ('tithe', 'offering', 'donation', 'fundraising', 'other_income')
        AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
        AND YEAR(transaction_date) = YEAR(CURRENT_DATE())
    ");
    $income_this_month = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?: 0;

    // Total expenses this month
    $stmt = $db->query("
        SELECT SUM(amount) as total
        FROM finances
        WHERE category IN ('utilities', 'maintenance', 'supplies', 'salaries', 'other_expense')
        AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
        AND YEAR(transaction_date) = YEAR(CURRENT_DATE())
    ");
    $expenses_this_month = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?: 0;

    // Income by category
    $stmt = $db->query("
        SELECT category, SUM(amount) as total
        FROM finances
        WHERE category IN ('tithe', 'offering', 'donation', 'fundraising', 'other_income')
        AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
        AND YEAR(transaction_date) = YEAR(CURRENT_DATE())
        GROUP BY category
    ");
    $income_by_category = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Expenses by category
    $stmt = $db->query("
        SELECT category, SUM(amount) as total
        FROM finances
        WHERE category IN ('utilities', 'maintenance', 'supplies', 'salaries', 'other_expense')
        AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
        AND YEAR(transaction_date) = YEAR(CURRENT_DATE())
        GROUP BY category
    ");
    $expenses_by_category = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'income_this_month' => $income_this_month,
        'expenses_this_month' => $expenses_this_month,
        'income_by_category' => $income_by_category,
        'expenses_by_category' => $expenses_by_category,
        'net_balance' => $income_this_month - $expenses_this_month
    ];
}
?>
