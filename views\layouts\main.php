<?php
/**
 * Main Layout Template
 *
 * Emergency fix: Simplified layout that works reliably
 */

// Ensure session is started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Simple fallback approach for immediate functionality
try {
    // Try to use LayoutService if available
    if (file_exists('services/LayoutService.php')) {
        require_once 'services/LayoutService.php';
        $layoutService = new LayoutService();
        $layoutData = $layoutService->getLayoutData();

        $logo_src = $layoutData['logo_src'];
        $display_name = $layoutData['display_name'];
        $church_name = $layoutData['church_name'];
        $nav_items = $layoutData['navigation_items'];
    } else {
        throw new Exception('LayoutService not available');
    }
} catch (Exception $e) {
    // Fallback to basic functionality
    $logo_src = (function_exists('url') ? url('assets/images/icgc.png') : '/icgc/assets/images/icgc.png');
    $display_name = 'ICGC Emmanuel Temple';
    $church_name = 'ICGC Emmanuel Temple';

    // Basic navigation items
    $nav_items = [
        ['id' => 'dashboard', 'icon' => 'fa-tachometer-alt', 'label' => 'Dashboard'],
        ['id' => 'members', 'icon' => 'fa-users', 'label' => 'Members'],
        ['id' => 'birthdays', 'icon' => 'fa-birthday-cake', 'label' => 'Birthdays'],
        ['id' => 'groups', 'icon' => 'fa-layer-group', 'label' => 'Groups'],
        ['separator' => true],
        ['id' => 'children_ministry', 'url_slug' => 'children-ministry', 'icon' => 'fa-child', 'label' => "Children's Ministry"],
        ['id' => 'visitors', 'icon' => 'fa-user-plus', 'label' => 'Visitors'],
        ['id' => 'programs', 'icon' => 'fa-calendar-alt', 'label' => 'Programs'],
        ['id' => 'attendance', 'icon' => 'fa-clipboard-check', 'label' => 'Attendance'],
        ['id' => 'equipment', 'icon' => 'fa-tools', 'label' => 'Equipment'],
        ['separator' => true],
        ['id' => 'finances', 'icon' => 'fa-money-bill-wave', 'label' => 'Finance'],
        ['id' => 'welfare', 'icon' => 'fa-hand-holding-heart', 'label' => 'Welfare'],
        ['id' => 'sms', 'icon' => 'fa-sms', 'label' => 'SMS Broadcast'],
        ['id' => 'settings', 'icon' => 'fa-cogs', 'label' => 'Settings'],
    ];
}

// Simple navigation rendering function
function renderNavLink($item, $active_page = '') {
    if (isset($item['separator'])) {
        return '<li class="px-4 py-1"><div class="border-t border-white border-opacity-20"></div></li>';
    }

    $url_slug = $item['url_slug'] ?? $item['id'];
    $url = function_exists('url') ? url($url_slug) : "/icgc/$url_slug";
    $is_active = ($active_page === $item['id']);
    $active_class = $is_active ? 'bg-black bg-opacity-20 text-secondary font-semibold' : '';

    return <<<HTML
    <li>
        <a href="{$url}" class="flex items-center px-4 py-2 text-white hover:bg-white hover:bg-opacity-10 transition-all duration-200 {$active_class}">
            <i class="fas {$item['icon']} w-4 mr-3 text-base"></i>
            <span class="text-base">{$item['label']}</span>
        </a>
    </li>
HTML;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title><?php echo $page_title ?? $display_name; ?></title>
    <link rel="icon" href="<?php echo $logo_src; ?>" type="image/png">
    <meta name="description" content="<?php echo $display_name; ?> Church Management System">
    <meta name="author" content="<?php echo $display_name; ?>">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Make BASE_URL available to JavaScript
        window.BASE_URL = '<?php echo url(""); ?>';

        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#3F7D58',
                        'primary-light': '#5a9e76',
                        'primary-dark': '#2c5a3f',
                        'secondary': '#D3E671',
                        'secondary-light': '#e1ef9a',
                        'secondary-dark': '#b8c95c',
                    }
                }
            }
        }
    </script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js for dashboard charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- EXACT COLORS YOU SPECIFIED - LEFT TO RIGHT -->
    <style>
        /* Force sidebar gradient with your exact colors - left to right */
        aside#sidebar,
        #sidebar {
            background: linear-gradient(to right, #3b7c5e, #d4ea63) !important;
            background-color: #3b7c5e !important;
        }

        /* Override any Tailwind classes */
        #sidebar.fixed,
        #sidebar[class*="bg-"] {
            background: linear-gradient(to right, #3b7c5e, #d4ea63) !important;
        }
    </style>




</head>
<body class="bg-white m-0 p-0">
    <div class="relative">
        <!-- Sidebar starts at the very top -->

        <!-- Modern Professional Sidebar -->
        <aside id="sidebar" class="fixed top-0 left-0 w-64 h-screen shadow-2xl transform -translate-x-full md:translate-x-0 transition-all duration-300 ease-in-out z-50 overflow-hidden" style="background: linear-gradient(to right, #3b7c5e, #d4ea63) !important;">

            <!-- Logo Section -->
            <div class="relative px-4 py-3 border-b border-white border-opacity-20">
                <div class="flex flex-col items-center">
                    <div class="mb-2">
                        <img src="<?php echo $logo_src; ?>" alt="<?php echo $display_name; ?> Logo"
                             class="drop-shadow-lg object-contain rounded-full"
                             style="height: 50px; width: 50px;">
                    </div>
                    <h2 class="text-white text-xs font-semibold text-center tracking-wide"><?php echo $display_name; ?></h2>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="relative py-2">
                <ul class="space-y-0">
                    <?php
                    // Loop through the navigation items array to render the menu
                    foreach ($nav_items as $item) {
                        if (isset($layoutService)) {
                            echo $layoutService->renderNavLink($item, $active_page ?? '');
                        } else {
                            echo renderNavLink($item, $active_page ?? '');
                        }
                    }
                    ?>
                </ul>
            </nav>
        </aside>

        <!-- Main Content - Adjusted with left margin to accommodate fixed sidebar -->
        <div class="ml-0 md:ml-64 min-h-screen">
            <!-- Header -->
            <header class="bg-white text-gray-800 shadow-sm mb-0 sticky top-0 z-10">
                <div class="px-6 py-3 flex justify-between items-center">
                    <div class="flex items-center">
                        <button id="sidebar-toggle" class="mr-2 md:hidden text-gray-800 focus:outline-none">
                            <i class="fas fa-bars"></i>
                        </button>
                        <!-- Header text removed -->
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <button id="user-menu-button" class="flex items-center space-x-2 focus:outline-none">
                                <span class="hidden md:inline-block text-gray-800"><?php echo current_user()['name'] ?? 'Admin'; ?></span>
                                <i class="fas fa-user-circle text-xl text-gray-800"></i>
                            </button>
                            <div id="user-menu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden z-10">
                                <a href="<?php echo url('profile'); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-[#D3E671]">Profile</a>
                                <a href="<?php echo url('settings'); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-[#D3E671]">Settings</a>
                                <a href="<?php echo url('logout'); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-[#D3E671]">Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <main class="p-6">
            <?php
            // Include the reusable flash message partial
            include_once __DIR__ . '/../partials/_flash_messages.php';
            ?>

            <?php echo $content; ?>
            </main>

            <!-- Footer -->
            <footer class="bg-white text-gray-600 py-4 text-center shadow-inner">
                <p>&copy; <?php echo date('Y'); ?> All rights reserved. <span class="text-primary">Church Management System</span></p>
            </footer>
        </div>
    </div>

    <!-- ICGC Chart Management System - MUST LOAD FIRST -->
    <script src="<?php
        if (isset($layoutService)) {
            echo $layoutService->getAssetUrl('assets/js/charts-simple.js');
        } else {
            echo (function_exists('url') ? url('assets/js/charts-simple.js') : '/icgc/assets/js/charts-simple.js');
        }
    ?>"></script>

    <!-- JavaScript -->
    <script src="<?php
        if (isset($layoutService)) {
            echo $layoutService->getAssetUrl('assets/js/main.js');
        } else {
            echo (function_exists('url') ? url('assets/js/main.js') : '/icgc/assets/js/main.js');
        }
    ?>"></script>
</body>
</html>
