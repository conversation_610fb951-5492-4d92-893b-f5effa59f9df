<?php
/**
 * API endpoint for department management
 */

header('Content-Type: application/json');

// CRITICAL SECURITY FIX: Restrict CORS to application domain only
// The wildcard '*' is dangerous and allows any website to access this API
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false || 
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
    // If origin is not allowed, we don't set the CORS header
    // This will cause the browser to block the request
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../helpers/functions.php';

// Start session for CSRF protection
session_start();

$database = new Database();
$conn = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            // Get all departments
            $stmt = $conn->prepare("SELECT * FROM departments WHERE is_active = 1 ORDER BY sort_order, display_name");
            $stmt->execute();
            $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'data' => $departments
            ]);
            break;
            
        case 'POST':
            // Create new department
            if (!isset($input['name']) || !isset($input['display_name'])) {
                throw new Exception('Name and display name are required');
            }
            
            $name = sanitize($input['name']);
            $display_name = sanitize($input['display_name']);
            $description = sanitize($input['description'] ?? '');
            $sort_order = intval($input['sort_order'] ?? 999);
            
            // Check if department already exists
            $stmt = $conn->prepare("SELECT id FROM departments WHERE name = ? OR display_name = ?");
            $stmt->execute([$name, $display_name]);
            if ($stmt->rowCount() > 0) {
                throw new Exception('Department with this name already exists');
            }
            
            $stmt = $conn->prepare("INSERT INTO departments (name, display_name, description, sort_order) VALUES (?, ?, ?, ?)");
            $stmt->execute([$name, $display_name, $description, $sort_order]);

            $department_id = $conn->lastInsertId();

            // Automatically update ENUM constraints to include new department
            try {
                $stmt = $conn->prepare("SELECT name FROM departments WHERE is_active = 1 ORDER BY name");
                $stmt->execute();
                $all_departments = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $dept_enum_values = "'" . implode("','", $all_departments) . "'";
                $enum_sql = "ALTER TABLE members MODIFY COLUMN department ENUM($dept_enum_values) DEFAULT 'none'";
                $conn->prepare($enum_sql)->execute();
            } catch (Exception $e) {
                error_log("Failed to update department ENUM: " . $e->getMessage());
            }

            // Get the created department
            $stmt = $conn->prepare("SELECT * FROM departments WHERE id = ?");
            $stmt->execute([$department_id]);
            $department = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'message' => 'Department created successfully',
                'data' => $department
            ]);
            break;
            
        case 'PUT':
            // Update department
            if (!isset($input['id'])) {
                throw new Exception('Department ID is required');
            }
            
            $id = intval($input['id']);
            $display_name = sanitize($input['display_name']);
            $description = sanitize($input['description'] ?? '');
            $sort_order = intval($input['sort_order'] ?? 0);
            
            // Check if department exists
            $stmt = $conn->prepare("SELECT id FROM departments WHERE id = ?");
            $stmt->execute([$id]);
            if ($stmt->rowCount() === 0) {
                throw new Exception('Department not found');
            }
            
            $stmt = $conn->prepare("UPDATE departments SET display_name = ?, description = ?, sort_order = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$display_name, $description, $sort_order, $id]);
            
            // Get the updated department
            $stmt = $conn->prepare("SELECT * FROM departments WHERE id = ?");
            $stmt->execute([$id]);
            $department = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'message' => 'Department updated successfully',
                'data' => $department
            ]);
            break;
            
        case 'DELETE':
            // Delete department (soft delete by setting is_active = 0)
            if (!isset($input['id'])) {
                throw new Exception('Department ID is required');
            }
            
            $id = intval($input['id']);
            
            // Check if department exists
            $stmt = $conn->prepare("SELECT name FROM departments WHERE id = ?");
            $stmt->execute([$id]);
            $department = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$department) {
                throw new Exception('Department not found');
            }
            
            // Don't allow deletion of 'none' department
            if ($department['name'] === 'none') {
                throw new Exception('Cannot delete the default "No Department" option');
            }
            
            // Check if any members are using this department
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM members WHERE department = ?");
            $stmt->execute([$department['name']]);
            $member_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($member_count > 0) {
                throw new Exception("Cannot delete department. $member_count members are currently assigned to this department.");
            }
            
            // Soft delete the department
            $stmt = $conn->prepare("UPDATE departments SET is_active = 0, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$id]);

            // Automatically update ENUM constraints to remove deactivated department
            try {
                $stmt = $conn->prepare("SELECT name FROM departments WHERE is_active = 1 ORDER BY name");
                $stmt->execute();
                $all_departments = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $dept_enum_values = "'" . implode("','", $all_departments) . "'";
                $enum_sql = "ALTER TABLE members MODIFY COLUMN department ENUM($dept_enum_values) DEFAULT 'none'";
                $conn->prepare($enum_sql)->execute();
            } catch (Exception $e) {
                error_log("Failed to update department ENUM after deletion: " . $e->getMessage());
            }

            echo json_encode([
                'success' => true,
                'message' => 'Department deleted successfully'
            ]);
            break;
            
        default:
            throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
