<?php // No header needed as it's included in the layout ?>

<div class="container mx-auto px-4 py-6 max-w-7xl">
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-amber-600 to-amber-500 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-white p-2 rounded-full mr-3">
                        <i class="fas fa-archive text-amber-500 text-xl"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-white">Data Archiving</h1>
                </div>
                <a href="<?php echo BASE_URL; ?>settings#database/system-maintenance" class="bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back to System Maintenance
                </a>
            </div>
        </div>

        <div class="p-6">
            <div class="bg-amber-50 border-l-4 border-amber-500 p-4 mb-6 rounded-r-md">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-amber-500"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-amber-800">About Data Archiving</h3>
                        <div class="mt-2 text-sm text-amber-700">
                            <p>Archiving moves older data to separate tables, which helps improve system performance while keeping the data accessible when needed.</p>
                            <ul class="list-disc pl-5 space-y-1 mt-2">
                                <li>Archived data remains accessible through dedicated archive views</li>
                                <li>The archiving process is transaction-safe and can be reversed if needed</li>
                                <li>We recommend archiving data that is at least 1 year old</li>
                                <li>Regular archiving helps maintain optimal system performance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Types Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <?php foreach ($dataTypes as $key => $dataType): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-300">
                    <div class="border-b border-gray-200 bg-gray-50 px-4 py-3">
                        <h3 class="font-semibold text-gray-800 flex items-center">
                            <?php
                            $iconClass = '';
                            switch ($key) {
                                case 'finances':
                                    $iconClass = 'fa-money-bill-wave text-green-600';
                                    break;
                                case 'attendance':
                                    $iconClass = 'fa-users text-blue-600';
                                    break;
                                case 'sms_messages':
                                    $iconClass = 'fa-sms text-purple-600';
                                    break;
                                case 'visitors':
                                    $iconClass = 'fa-user-friends text-orange-600';
                                    break;
                                case 'equipment_maintenance':
                                    $iconClass = 'fa-tools text-gray-600';
                                    break;
                                default:
                                    $iconClass = 'fa-database text-primary';
                            }
                            ?>
                            <i class="fas <?php echo $iconClass; ?> mr-2"></i>
                            <?php echo $dataType['name']; ?>
                        </h3>
                    </div>
                    <div class="p-4">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Total Records:</span>
                                <span class="font-semibold text-gray-800"><?php echo number_format($dataType['count']); ?></span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Archivable Records:</span>
                                <span class="font-semibold text-amber-600"><?php echo number_format($dataType['archivable_count']); ?></span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Archive Table:</span>
                                <?php if ($dataType['has_archive_table']): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i> Created
                                </span>
                                <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-times-circle mr-1"></i> Not Created
                                </span>
                                <?php endif; ?>
                            </div>
                            <?php if ($dataType['oldest_record']): ?>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Oldest Record:</span>
                                <span class="text-sm text-gray-800"><?php echo date('M d, Y', strtotime($dataType['oldest_record'])); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <?php if ($dataType['count'] > 0 && $dataType['archivable_count'] > 0): ?>
                            <button type="button"
                                class="w-full bg-amber-500 hover:bg-amber-600 text-white py-2 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-sm hover:shadow-md"
                                onclick="showArchiveModal('<?php echo $key; ?>', '<?php echo $dataType['name']; ?>', <?php echo $dataType['archivable_count']; ?>)">
                                <i class="fas fa-archive mr-2"></i> Archive Old Records
                            </button>
                            <?php else: ?>
                            <button type="button" disabled
                                class="w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-lg flex items-center justify-center cursor-not-allowed">
                                <i class="fas fa-archive mr-2"></i> No Records to Archive
                            </button>
                            <?php endif; ?>

                            <?php if ($dataType['has_archive_table']): ?>
                            <a href="<?php echo BASE_URL . $key; ?>/archived" class="mt-2 w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg flex items-center justify-center transition-all duration-300 border border-gray-300">
                                <i class="fas fa-search mr-2"></i> View Archived Records
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!-- Archive Modal -->
<div id="archiveModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-xl max-w-md w-full mx-4 overflow-hidden">
        <div class="bg-amber-500 px-6 py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <i class="fas fa-archive mr-2"></i>
                    <span id="modalTitle">Archive Records</span>
                </h3>
                <button type="button" onclick="hideArchiveModal()" class="text-white hover:text-amber-200 focus:outline-none">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <form action="<?php echo BASE_URL; ?>settings/process-archiving" method="POST" id="archiveForm" class="p-6">
            <input type="hidden" name="data_type" id="dataType" value="">

            <div class="mb-6">
                <p class="text-gray-700 mb-4">You are about to archive <span id="recordCount" class="font-semibold text-amber-600">0</span> records from <span id="dataTypeName" class="font-semibold">data type</span>.</p>

                <div class="bg-amber-50 border-l-4 border-amber-500 p-3 rounded-r-md mb-4">
                    <p class="text-sm text-amber-800">
                        <i class="fas fa-exclamation-triangle mr-1"></i> This process will move records to an archive table. The data will still be accessible but stored separately.
                    </p>
                </div>

                <div class="mb-4">
                    <label for="cutoff_date" class="block text-sm font-medium text-gray-700 mb-1">Archive Records Before</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-400"></i>
                        </div>
                        <input type="date" id="cutoff_date" name="cutoff_date" required
                            class="focus:ring-amber-500 focus:border-amber-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 rounded-md py-3"
                            value="<?php echo date('Y-m-d', strtotime('-1 year')); ?>"
                            max="<?php echo date('Y-m-d', strtotime('-1 month')); ?>">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">Date</span>
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        All records with dates before this will be archived.
                    </p>
                </div>

                <div class="bg-gray-50 p-4 rounded-md">
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="confirm" name="confirm" type="checkbox" required
                                class="focus:ring-amber-500 h-4 w-4 text-amber-600 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="confirm" class="font-medium text-gray-700">I confirm that I want to archive these records</label>
                            <p class="text-gray-500">I understand that this process will move older records to the archive.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="hideArchiveModal()" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500">
                    Cancel
                </button>
                <button type="submit" class="bg-amber-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500">
                    Archive Records
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function showArchiveModal(dataType, dataTypeName, recordCount) {
        document.getElementById('dataType').value = dataType;
        document.getElementById('dataTypeName').textContent = dataTypeName;
        document.getElementById('recordCount').textContent = recordCount.toLocaleString();
        document.getElementById('modalTitle').textContent = 'Archive ' + dataTypeName;
        document.getElementById('archiveModal').classList.remove('hidden');
    }

    function hideArchiveModal() {
        document.getElementById('archiveModal').classList.add('hidden');
        document.getElementById('archiveForm').reset();
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('archiveModal');
        const modalContent = document.querySelector('#archiveModal > div');

        if (modal && !modal.classList.contains('hidden') && !modalContent.contains(event.target)) {
            hideArchiveModal();
        }
    });

    // Prevent event propagation from modal content
    document.querySelector('#archiveModal > div').addEventListener('click', function(event) {
        event.stopPropagation();
    });
</script>

<?php // No footer needed as it's included in the layout ?>
