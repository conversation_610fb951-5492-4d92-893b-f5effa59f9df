<?php
// Get QR session token from URL or session
$token = isset($_GET['token']) ? sanitize($_GET['token']) : (isset($_SESSION['qr_session']['token']) ? $_SESSION['qr_session']['token'] : null);

if (!$token) {
    set_flash_message('No active QR session found', 'danger');
    redirect('attendance/qr');
    exit;
}

// Get session info for display
$qr_session = isset($_SESSION['qr_session']) ? $_SESSION['qr_session'] : null;
?>

<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Real-time Attendance Dashboard</h1>
                    <p class="text-sm text-gray-600 mt-1">
                        <i class="fas fa-calendar mr-1"></i>
                        <?php echo $qr_session ? format_date($qr_session['attendance_date']) : 'Loading...'; ?>
                        <span class="mx-2">•</span>
                        <i class="fas fa-clock mr-1"></i>
                        <span id="current-time"><?php echo date('g:i A'); ?></span>
                    </p>
                </div>
                <div class="flex space-x-3">
                    <button onclick="exportData()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm flex items-center">
                        <i class="fas fa-download mr-2"></i> Export
                    </button>
                    <a href="<?php echo BASE_URL; ?>attendance/qr" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i> Back to QR
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        
        <!-- Summary Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Present Card -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Present</p>
                        <p class="text-3xl font-bold text-blue-600" id="total-count">0</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <span id="percentage-present">0%</span> of expected
                        </p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Male Attendance Card -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Male</p>
                        <p class="text-3xl font-bold text-green-600" id="male-count">0</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <span id="male-percentage">0%</span> of total
                        </p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-male text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Female Attendance Card -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Female</p>
                        <p class="text-3xl font-bold text-pink-600" id="female-count">0</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <span id="female-percentage">0%</span> of total
                        </p>
                    </div>
                    <div class="p-3 bg-pink-100 rounded-full">
                        <i class="fas fa-female text-pink-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Late Arrivals Card -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Late Arrivals</p>
                        <p class="text-3xl font-bold text-yellow-600" id="late-count">0</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <span id="late-percentage">0%</span> of total
                        </p>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Bar -->
        <div class="bg-white rounded-xl shadow-md p-4 mb-6 border border-gray-200">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <label for="search-members" class="sr-only">Search members</label>
                        <div class="relative">
                            <input type="text" id="search-members" placeholder="Search members..." 
                                   class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    <select id="filter-status" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all">All Status</option>
                        <option value="present">Present</option>
                        <option value="late">Late</option>
                    </select>
                    <select id="filter-gender" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all">All Genders</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                    </select>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="flex items-center text-sm text-gray-600">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span>Auto-refresh: <span id="refresh-countdown">10</span>s</span>
                    </div>
                    <button onclick="toggleAutoRefresh()" id="auto-refresh-btn" class="text-sm text-blue-600 hover:text-blue-800">
                        <i class="fas fa-pause mr-1"></i> Pause
                    </button>
                </div>
            </div>
        </div>

        <!-- Live Member List -->
        <div class="bg-white rounded-xl shadow-md border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">Live Attendance List</h3>
                    <div class="text-sm text-gray-600">
                        <span id="showing-count">0</span> of <span id="total-members">0</span> members
                    </div>
                </div>
            </div>
            
            <!-- Loading State -->
            <div id="loading-state" class="p-8 text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading attendance data...</p>
            </div>

            <!-- Member List Container -->
            <div id="members-container" class="hidden">
                <div class="divide-y divide-gray-200 max-h-96 overflow-y-auto" id="members-list">
                    <!-- Members will be loaded here via JavaScript -->
                </div>
                
                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            Showing <span id="page-start">1</span> to <span id="page-end">10</span> of <span id="page-total">0</span> results
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="previousPage()" id="prev-btn" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50" disabled>
                                Previous
                            </button>
                            <button onclick="nextPage()" id="next-btn" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50" disabled>
                                Next
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div id="empty-state" class="hidden p-8 text-center">
                <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No attendance marked yet</h3>
                <p class="text-gray-600">Members will appear here as they check in</p>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Real-time Updates -->
<script>
let autoRefreshEnabled = true;
let refreshInterval;
let currentPage = 1;
let itemsPerPage = 20;
let allMembers = [];
let filteredMembers = [];
let refreshCountdown = 10;

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadAttendanceData();
    startAutoRefresh();
    updateClock();
    
    // Set up search and filter event listeners
    document.getElementById('search-members').addEventListener('input', filterMembers);
    document.getElementById('filter-status').addEventListener('change', filterMembers);
    document.getElementById('filter-gender').addEventListener('change', filterMembers);
});

// Update clock every second
function updateClock() {
    setInterval(() => {
        const now = new Date();
        document.getElementById('current-time').textContent = now.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    }, 1000);
}

// Load attendance data from API
async function loadAttendanceData() {
    try {
        const token = '<?php echo $token; ?>';
        const response = await fetch(`<?php echo BASE_URL; ?>api/qr-stats-enhanced.php?token=${token}`);
        const data = await response.json();
        
        if (data.success) {
            updateStatistics(data.stats);
            allMembers = data.members || [];
            filterMembers();
            showMembersContainer();
        } else {
            showError(data.error || 'Failed to load attendance data');
        }
    } catch (error) {
        console.error('Error loading attendance data:', error);
        showError('Network error. Please check your connection.');
    }
}

// Update statistics cards
function updateStatistics(stats) {
    document.getElementById('total-count').textContent = stats.total_count || 0;
    document.getElementById('male-count').textContent = stats.male_count || 0;
    document.getElementById('female-count').textContent = stats.female_count || 0;
    document.getElementById('late-count').textContent = stats.late_count || 0;
    
    const total = stats.total_count || 0;
    if (total > 0) {
        document.getElementById('male-percentage').textContent = Math.round((stats.male_count / total) * 100) + '%';
        document.getElementById('female-percentage').textContent = Math.round((stats.female_count / total) * 100) + '%';
        document.getElementById('late-percentage').textContent = Math.round((stats.late_count / total) * 100) + '%';
    }
}

// Filter members based on search and filters
function filterMembers() {
    const searchTerm = document.getElementById('search-members').value.toLowerCase();
    const statusFilter = document.getElementById('filter-status').value;
    const genderFilter = document.getElementById('filter-gender').value;
    
    filteredMembers = allMembers.filter(member => {
        const matchesSearch = member.name.toLowerCase().includes(searchTerm) || 
                            member.phone.includes(searchTerm);
        const matchesStatus = statusFilter === 'all' || member.status === statusFilter;
        const matchesGender = genderFilter === 'all' || member.gender === genderFilter;
        
        return matchesSearch && matchesStatus && matchesGender;
    });
    
    currentPage = 1;
    displayMembers();
}

// Display members with pagination
function displayMembers() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageMembers = filteredMembers.slice(startIndex, endIndex);
    
    const membersList = document.getElementById('members-list');
    membersList.innerHTML = '';
    
    if (pageMembers.length === 0) {
        showEmptyState();
        return;
    }
    
    pageMembers.forEach(member => {
        const memberElement = createMemberElement(member);
        membersList.appendChild(memberElement);
    });
    
    updatePagination();
    document.getElementById('showing-count').textContent = filteredMembers.length;
    document.getElementById('total-members').textContent = allMembers.length;
}

// Create member element
function createMemberElement(member) {
    const div = document.createElement('div');
    div.className = 'px-6 py-4 flex items-center justify-between hover:bg-gray-50';
    
    const statusClass = member.status === 'present' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
    const genderIcon = member.gender === 'male' ? 'fa-male text-blue-600' : 'fa-female text-pink-600';
    
    div.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                    <i class="fas ${genderIcon}"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-900">${member.name}</p>
                <p class="text-xs text-gray-500">${member.phone} • ${member.department}</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <span class="text-xs text-gray-500">${member.time}</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">
                ${member.status.charAt(0).toUpperCase() + member.status.slice(1)}
            </span>
        </div>
    `;
    
    return div;
}

// Show/hide different states
function showMembersContainer() {
    document.getElementById('loading-state').classList.add('hidden');
    document.getElementById('members-container').classList.remove('hidden');
    document.getElementById('empty-state').classList.add('hidden');
}

function showEmptyState() {
    document.getElementById('loading-state').classList.add('hidden');
    document.getElementById('members-container').classList.add('hidden');
    document.getElementById('empty-state').classList.remove('hidden');
}

function showError(message) {
    console.error('Dashboard Error:', message);
    // You can implement error display here
}

// Auto-refresh functionality
function startAutoRefresh() {
    refreshInterval = setInterval(() => {
        if (autoRefreshEnabled) {
            refreshCountdown--;
            document.getElementById('refresh-countdown').textContent = refreshCountdown;
            
            if (refreshCountdown <= 0) {
                loadAttendanceData();
                refreshCountdown = 10;
            }
        }
    }, 1000);
}

function toggleAutoRefresh() {
    autoRefreshEnabled = !autoRefreshEnabled;
    const btn = document.getElementById('auto-refresh-btn');
    
    if (autoRefreshEnabled) {
        btn.innerHTML = '<i class="fas fa-pause mr-1"></i> Pause';
        refreshCountdown = 10;
    } else {
        btn.innerHTML = '<i class="fas fa-play mr-1"></i> Resume';
    }
}

// Pagination functions
function updatePagination() {
    const totalPages = Math.ceil(filteredMembers.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredMembers.length);
    
    document.getElementById('page-start').textContent = startIndex + 1;
    document.getElementById('page-end').textContent = endIndex;
    document.getElementById('page-total').textContent = filteredMembers.length;
    
    document.getElementById('prev-btn').disabled = currentPage <= 1;
    document.getElementById('next-btn').disabled = currentPage >= totalPages;
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        displayMembers();
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredMembers.length / itemsPerPage);
    if (currentPage < totalPages) {
        currentPage++;
        displayMembers();
    }
}

// Export functionality
function exportData() {
    // This will be implemented to export attendance data
    alert('Export functionality will be implemented');
}
</script>
