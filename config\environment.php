<?php
/**
 * Environment Configuration Manager
 * Handles loading and managing environment-specific configurations
 */

class EnvironmentConfig {
    private static $config = [];
    private static $loaded = false;
    
    /**
     * Load environment configuration
     */
    public static function load(): void {
        if (self::$loaded) {
            return;
        }
        
        // Load .env file if it exists
        self::loadDotEnv();
        
        // Set default configuration
        self::setDefaults();
        
        // Load environment-specific overrides
        self::loadEnvironmentOverrides();
        
        // Define constants for backward compatibility
        self::defineConstants();
        
        self::$loaded = true;
    }
    
    /**
     * Get configuration value
     */
    public static function get(string $key, $default = null) {
        $keys = explode('.', $key);
        $value = self::$config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * Set configuration value
     */
    public static function set(string $key, $value): void {
        $keys = explode('.', $key);
        $config = &self::$config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }
    
    /**
     * Load .env file
     */
    private static function loadDotEnv(): void {
        $baseDir = defined('BASE_DIR') ? BASE_DIR : dirname(__DIR__);
        $envFile = $baseDir . '/.env';
        
        if (!file_exists($envFile)) {
            return;
        }
        
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue; // Skip comments
            }
            
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if (preg_match('/^"(.*)"$/', $value, $matches)) {
                    $value = $matches[1];
                } elseif (preg_match("/^'(.*)'$/", $value, $matches)) {
                    $value = $matches[1];
                }
                
                $_ENV[$key] = $value;
                putenv("$key=$value");
            }
        }
    }
    
    /**
     * Set default configuration values
     */
    private static function setDefaults(): void {
        self::$config = [
            'app' => [
                'name' => $_ENV['APP_NAME'] ?? 'Church Management System',
                'env' => $_ENV['APP_ENV'] ?? 'production',
                'debug' => filter_var($_ENV['APP_DEBUG'] ?? 'false', FILTER_VALIDATE_BOOLEAN),
                'url' => $_ENV['APP_URL'] ?? 'http://localhost',
                'timezone' => $_ENV['APP_TIMEZONE'] ?? 'Africa/Accra'
            ],
            
            'database' => [
                'host' => $_ENV['DB_HOST'] ?? 'localhost',
                'port' => (int)($_ENV['DB_PORT'] ?? 3306),
                'name' => $_ENV['DB_NAME'] ?? 'icgc_db',
                'username' => $_ENV['DB_USER'] ?? 'root',
                'password' => $_ENV['DB_PASS'] ?? '',
                'charset' => 'utf8mb4'
            ],
            
            'security' => [
                'session_lifetime' => (int)($_ENV['SESSION_LIFETIME'] ?? 3600),
                'max_login_attempts' => (int)($_ENV['MAX_LOGIN_ATTEMPTS'] ?? 5),
                'lockout_duration' => (int)($_ENV['LOCKOUT_DURATION'] ?? 900),
                'csrf_token_name' => 'csrf_token'
            ],
            
            'logging' => [
                'level' => $_ENV['LOG_LEVEL'] ?? 'info',
                'file' => $_ENV['LOG_FILE'] ?? 'logs/app.log',
                'audit_enabled' => filter_var($_ENV['AUDIT_ENABLED'] ?? 'true', FILTER_VALIDATE_BOOLEAN)
            ],
            
            'mail' => [
                'enabled' => filter_var($_ENV['MAIL_ENABLED'] ?? 'false', FILTER_VALIDATE_BOOLEAN),
                'host' => $_ENV['MAIL_HOST'] ?? '',
                'port' => (int)($_ENV['MAIL_PORT'] ?? 587),
                'username' => $_ENV['MAIL_USERNAME'] ?? '',
                'password' => $_ENV['MAIL_PASSWORD'] ?? '',
                'from_address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '',
                'from_name' => $_ENV['MAIL_FROM_NAME'] ?? ''
            ],
            
            'upload' => [
                'max_file_size' => (int)($_ENV['MAX_FILE_SIZE'] ?? 5242880),
                'allowed_types' => explode(',', $_ENV['ALLOWED_FILE_TYPES'] ?? 'jpg,jpeg,png,pdf,doc,docx')
            ],
            
            'cache' => [
                'enabled' => filter_var($_ENV['CACHE_ENABLED'] ?? 'false', FILTER_VALIDATE_BOOLEAN),
                'ttl' => (int)($_ENV['CACHE_TTL'] ?? 3600)
            ]
        ];
    }
    
    /**
     * Load environment-specific configuration overrides
     */
    private static function loadEnvironmentOverrides(): void {
        $env = self::get('app.env');
        $baseDir = defined('BASE_DIR') ? BASE_DIR : dirname(__DIR__);
        $envConfigFile = $baseDir . "/config/environments/{$env}.php";
        
        if (file_exists($envConfigFile)) {
            $envConfig = require $envConfigFile;
            self::$config = array_merge_recursive(self::$config, $envConfig);
        }
    }
    
    /**
     * Define constants for backward compatibility
     */
    private static function defineConstants(): void {
        // App constants
        if (!defined('APP_ENV')) {
            define('APP_ENV', self::get('app.env'));
        }
        if (!defined('APP_DEBUG')) {
            define('APP_DEBUG', self::get('app.debug'));
        }
        
        // Database constants
        if (!defined('DB_HOST')) {
            define('DB_HOST', self::get('database.host'));
        }
        if (!defined('DB_USER')) {
            define('DB_USER', self::get('database.username'));
        }
        if (!defined('DB_PASS')) {
            define('DB_PASS', self::get('database.password'));
        }
        if (!defined('DB_NAME')) {
            define('DB_NAME', self::get('database.name'));
        }
        
        // Logging constants
        if (!defined('LOG_LEVEL')) {
            define('LOG_LEVEL', self::get('logging.level'));
        }
    }
    
    /**
     * Get all configuration
     */
    public static function all(): array {
        return self::$config;
    }
    
    /**
     * Check if running in production
     */
    public static function isProduction(): bool {
        return self::get('app.env') === 'production';
    }
    
    /**
     * Check if debug mode is enabled
     */
    public static function isDebug(): bool {
        return self::get('app.debug', false);
    }
}
?>
