<?php
// Prevent direct access
if (!defined('BASE_URL')) {
    exit('Direct access not permitted');
}
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Edit Welfare Payment</h1>
            <p class="text-gray-600 mt-1">Update welfare payment details</p>
        </div>
        <a href="<?php echo BASE_URL; ?>welfare" 
           class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Welfare
        </a>
    </div>

    <!-- Edit Payment Form -->
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Payment Information</h2>
                <p class="text-sm text-gray-600">Update the payment details below</p>
            </div>
            
            <form method="POST" action="<?php echo BASE_URL; ?>welfare/edit-payment/<?php echo $payment['id']; ?>" class="p-6 space-y-6">
                <input type="hidden" name="payment_id" value="<?php echo $payment['id']; ?>">
                
                <!-- Member Selection -->
                <div>
                    <label for="member_id" class="block text-sm font-medium text-gray-700 mb-2">Member *</label>
                    <select name="member_id" id="member_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">Select Member</option>
                        <?php foreach ($members as $member): ?>
                            <option value="<?php echo $member['id']; ?>" 
                                    <?php echo ($member['id'] == $payment['member_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Amount -->
                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Amount (₵) *</label>
                    <input type="number" name="amount" id="amount" step="0.01" min="0" required
                           value="<?php echo htmlspecialchars($payment['amount']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                </div>

                <!-- Payment Date -->
                <div>
                    <label for="payment_date" class="block text-sm font-medium text-gray-700 mb-2">Payment Date *</label>
                    <input type="date" name="payment_date" id="payment_date" required
                           value="<?php echo htmlspecialchars($payment['payment_date']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                </div>

                <!-- Payment Method -->
                <div>
                    <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-2">Payment Method *</label>
                    <select name="payment_method" id="payment_method" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">Select Payment Method</option>
                        <option value="cash" <?php echo ($payment['payment_method'] == 'cash') ? 'selected' : ''; ?>>Cash</option>
                        <option value="mobile_money" <?php echo ($payment['payment_method'] == 'mobile_money') ? 'selected' : ''; ?>>Mobile Money</option>
                        <option value="bank_transfer" <?php echo ($payment['payment_method'] == 'bank_transfer') ? 'selected' : ''; ?>>Bank Transfer</option>
                        <option value="cheque" <?php echo ($payment['payment_method'] == 'cheque') ? 'selected' : ''; ?>>Cheque</option>
                    </select>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea name="notes" id="notes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                              placeholder="Additional notes about this payment..."><?php echo htmlspecialchars($payment['notes'] ?? ''); ?></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="<?php echo BASE_URL; ?>welfare" 
                       class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-emerald-500 hover:bg-emerald-600 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-save mr-2"></i>
                        Update Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set max date to today
    const paymentDateInput = document.getElementById('payment_date');
    const today = new Date().toISOString().split('T')[0];
    paymentDateInput.setAttribute('max', today);
});
</script>
