<div class="page-content-centered">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-purple-600 to-indigo-700 p-6 text-white">
            <div class="flex flex-row justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Attendance Patterns</h1>
                    <p class="text-sm opacity-90 mt-1">Analyze member attendance consistency and patterns</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="cleanupOldRecords()" class="bg-red-500 bg-opacity-20 hover:bg-opacity-30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200 border border-red-300">
                        <i class="fas fa-trash-alt mr-2"></i> Remove Old Records
                    </button>
                    <a href="<?php echo BASE_URL; ?>attendance" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Attendance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Consistency Statistics -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Attendance Consistency Overview</h2>
            <p class="text-sm text-gray-600">Based on the last 3 months of attendance data</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
                <?php
                $pattern_colors = [
                    'Very Regular' => ['bg' => 'bg-green-100', 'text' => 'text-green-800', 'icon' => 'fa-medal', 'icon-bg' => 'bg-green-200', 'icon-color' => 'text-green-600'],
                    'Regular' => ['bg' => 'bg-teal-100', 'text' => 'text-teal-800', 'icon' => 'fa-thumbs-up', 'icon-bg' => 'bg-teal-200', 'icon-color' => 'text-teal-600'],
                    'Somewhat Regular' => ['bg' => 'bg-blue-100', 'text' => 'text-blue-800', 'icon' => 'fa-check', 'icon-bg' => 'bg-blue-200', 'icon-color' => 'text-blue-600'],
                    'Irregular' => ['bg' => 'bg-yellow-100', 'text' => 'text-yellow-800', 'icon' => 'fa-exclamation', 'icon-bg' => 'bg-yellow-200', 'icon-color' => 'text-yellow-600'],
                    'Very Irregular' => ['bg' => 'bg-orange-100', 'text' => 'text-orange-800', 'icon' => 'fa-exclamation-triangle', 'icon-bg' => 'bg-orange-200', 'icon-color' => 'text-orange-600'],
                    'Extended Absence' => ['bg' => 'bg-red-100', 'text' => 'text-red-800', 'icon' => 'fa-user-slash', 'icon-bg' => 'bg-red-200', 'icon-color' => 'text-red-600']
                ];

                // Define the order of patterns
                $pattern_order = [
                    'Very Regular',
                    'Regular',
                    'Somewhat Regular',
                    'Irregular',
                    'Very Irregular',
                    'Extended Absence'
                ];

                // Display patterns in the defined order
                foreach ($pattern_order as $pattern) :
                    $count = $consistency_stats[$pattern];
                    $colors = $pattern_colors[$pattern];
                ?>
                    <div class="<?php echo $colors['bg']; ?> rounded-xl p-4 border border-gray-200 shadow-sm text-center">
                        <div class="flex items-center justify-center mb-3">
                            <div class="w-10 h-10 rounded-full <?php echo $colors['icon-bg']; ?> <?php echo $colors['icon-color']; ?> flex items-center justify-center">
                                <i class="fas <?php echo $colors['icon']; ?>"></i>
                            </div>
                        </div>
                        <div class="text-2xl font-bold <?php echo $colors['text']; ?>"><?php echo $count; ?></div>
                        <div class="text-xs font-medium text-gray-600 mt-1"><?php echo $pattern; ?></div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Consistency Chart -->
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                <h3 class="text-base font-semibold text-gray-800 mb-4">Attendance Consistency Distribution</h3>
                <div class="h-12 bg-gray-200 rounded-lg overflow-hidden flex">
                    <?php
                    $total_members = array_sum($consistency_stats);
                    foreach ($pattern_order as $pattern) :
                        $count = $consistency_stats[$pattern];
                        $percentage = $total_members > 0 ? ($count / $total_members * 100) : 0;
                        $colors = $pattern_colors[$pattern];
                        if ($percentage > 0) :
                    ?>
                        <div class="<?php echo $colors['bg']; ?> h-full" style="width: <?php echo $percentage; ?>%" title="<?php echo $pattern; ?>: <?php echo $count; ?> members (<?php echo round($percentage); ?>%)"></div>
                    <?php
                        endif;
                    endforeach;
                    ?>
                </div>
                <div class="flex flex-wrap mt-3 justify-center">
                    <?php foreach ($pattern_order as $pattern) :
                        $count = $consistency_stats[$pattern];
                        $colors = $pattern_colors[$pattern];
                        $percentage = $total_members > 0 ? ($count / $total_members * 100) : 0;
                        if ($percentage > 0) :
                    ?>
                        <div class="flex items-center mr-4 mb-2">
                            <div class="w-3 h-3 rounded-sm <?php echo $colors['bg']; ?> mr-1"></div>
                            <span class="text-xs text-gray-600"><?php echo $pattern; ?> (<?php echo round($percentage); ?>%)</span>
                        </div>
                    <?php
                        endif;
                    endforeach;
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Member Attendance Patterns -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
            <div class="flex justify-between items-center mb-3">
                <div>
                    <h2 class="text-lg font-semibold text-gray-800">Member Attendance Patterns</h2>
                    <p class="text-sm text-gray-600">Detailed attendance patterns for all members (sorted by pattern priority)</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <input type="text" id="pattern-search" placeholder="Search members..." class="rounded-lg border border-gray-300 pl-9 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination Info -->
            <div class="flex justify-between items-center text-sm text-gray-600">
                <div>
                    Showing <?php echo $pagination['start_member']; ?>-<?php echo $pagination['end_member']; ?> of <?php echo $pagination['total_members']; ?> members
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                        Page <?php echo $pagination['current_page']; ?> of <?php echo $pagination['total_pages']; ?>
                    </span>
                </div>
            </div>
        </div>
        <div class="p-4">
            <!-- Pattern Priority Legend -->
            <div class="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 mb-4 border border-purple-100">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-800">
                        <i class="fas fa-sort-amount-down mr-2 text-purple-600"></i>
                        Sorting Order (Best to Needs Attention)
                    </h3>
                    <span class="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">Priority Order</span>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                    <?php
                    $legend_patterns = [
                        'Very Regular' => ['bg' => 'bg-green-100', 'text' => 'text-green-800', 'icon' => 'fa-medal', 'priority' => '1st'],
                        'Regular' => ['bg' => 'bg-teal-100', 'text' => 'text-teal-800', 'icon' => 'fa-thumbs-up', 'priority' => '2nd'],
                        'Somewhat Regular' => ['bg' => 'bg-blue-100', 'text' => 'text-blue-800', 'icon' => 'fa-check', 'priority' => '3rd'],
                        'Irregular' => ['bg' => 'bg-yellow-100', 'text' => 'text-yellow-800', 'icon' => 'fa-exclamation', 'priority' => '4th'],
                        'Very Irregular' => ['bg' => 'bg-orange-100', 'text' => 'text-orange-800', 'icon' => 'fa-exclamation-triangle', 'priority' => '5th'],
                        'Extended Absence' => ['bg' => 'bg-red-100', 'text' => 'text-red-800', 'icon' => 'fa-user-slash', 'priority' => '6th']
                    ];

                    foreach ($legend_patterns as $pattern => $colors):
                    ?>
                        <div class="flex items-center <?php echo $colors['bg']; ?> rounded-lg p-2 border border-gray-200">
                            <div class="flex items-center justify-center w-6 h-6 rounded-full bg-white mr-2">
                                <i class="fas <?php echo $colors['icon']; ?> text-xs <?php echo $colors['text']; ?>"></i>
                            </div>
                            <div>
                                <div class="text-xs font-medium <?php echo $colors['text']; ?>"><?php echo $pattern; ?></div>
                                <div class="text-xs text-gray-500"><?php echo $colors['priority']; ?></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="patterns-table">
                    <thead>
                        <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Pattern</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-green-500 uppercase tracking-wider">Present</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-red-500 uppercase tracking-wider">Absent</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-yellow-500 uppercase tracking-wider">Late</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($members_with_patterns as $member) :
                            $pattern = $member['pattern'];
                            $colors = $pattern_colors[$pattern];
                        ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                            <i class="fas fa-user text-gray-500"></i>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900"><?php echo $member['name']; ?></div>
                                            <div class="text-xs text-gray-500">ID: <?php echo $member['member_id']; ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-center">
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $colors['bg']; ?> <?php echo $colors['text']; ?>">
                                        <?php echo $pattern; ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-center">
                                    <?php
                                    $rate_color = $member['attendance_rate'] >= 70 ? 'text-green-600' : ($member['attendance_rate'] >= 50 ? 'text-yellow-600' : 'text-red-600');
                                    ?>
                                    <div class="text-sm font-medium <?php echo $rate_color; ?>"><?php echo $member['attendance_rate']; ?>%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-1.5 mx-auto mt-1">
                                        <div class="h-1.5 rounded-full <?php echo $member['attendance_rate'] >= 70 ? 'bg-green-500' : ($member['attendance_rate'] >= 50 ? 'bg-yellow-500' : 'bg-red-500'); ?>" style="width: <?php echo $member['attendance_rate']; ?>%"></div>
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-center">
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        <?php echo $member['present_count']; ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-center">
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        <?php echo $member['absent_count']; ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-center">
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        <?php echo $member['late_count']; ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center">
                                    <?php echo $member['total_services']; ?>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-center">
                                    <a href="<?php echo BASE_URL; ?>members/view/<?php echo $member['member_id']; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="text-blue-600 hover:text-blue-900 view-history" data-member-id="<?php echo $member['member_id']; ?>" data-member-name="<?php echo $member['name']; ?>">
                                        <i class="fas fa-history"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination Controls -->
            <?php if ($pagination['total_pages'] > 1): ?>
            <div class="bg-gray-50 px-4 py-3 border-t border-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <!-- Mobile pagination -->
                        <?php if ($pagination['has_prev']): ?>
                            <a href="?page=<?php echo $pagination['prev_page']; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                        <?php else: ?>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                                Previous
                            </span>
                        <?php endif; ?>

                        <?php if ($pagination['has_next']): ?>
                            <a href="?page=<?php echo $pagination['next_page']; ?>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        <?php else: ?>
                            <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                                Next
                            </span>
                        <?php endif; ?>
                    </div>

                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium"><?php echo $pagination['start_member']; ?></span> to <span class="font-medium"><?php echo $pagination['end_member']; ?></span> of <span class="font-medium"><?php echo $pagination['total_members']; ?></span> members
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <!-- Previous button -->
                                <?php if ($pagination['has_prev']): ?>
                                    <a href="?page=<?php echo $pagination['prev_page']; ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </span>
                                <?php endif; ?>

                                <!-- Page numbers -->
                                <?php
                                $start_page = max(1, $pagination['current_page'] - 2);
                                $end_page = min($pagination['total_pages'], $pagination['current_page'] + 2);

                                // Show first page if not in range
                                if ($start_page > 1): ?>
                                    <a href="?page=1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                                    <?php if ($start_page > 2): ?>
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <!-- Current page range -->
                                <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                    <?php if ($i == $pagination['current_page']): ?>
                                        <span class="relative inline-flex items-center px-4 py-2 border border-purple-500 bg-purple-50 text-sm font-medium text-purple-600">
                                            <?php echo $i; ?>
                                        </span>
                                    <?php else: ?>
                                        <a href="?page=<?php echo $i; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endif; ?>
                                <?php endfor; ?>

                                <!-- Show last page if not in range -->
                                <?php if ($end_page < $pagination['total_pages']): ?>
                                    <?php if ($end_page < $pagination['total_pages'] - 1): ?>
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                    <?php endif; ?>
                                    <a href="?page=<?php echo $pagination['total_pages']; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?php echo $pagination['total_pages']; ?></a>
                                <?php endif; ?>

                                <!-- Next button -->
                                <?php if ($pagination['has_next']): ?>
                                    <a href="?page=<?php echo $pagination['next_page']; ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </span>
                                <?php endif; ?>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recommendations -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Attendance Improvement Recommendations</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Extended Absence Members -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="p-2 rounded-full bg-red-100 text-red-600 mr-3">
                            <i class="fas fa-user-slash"></i>
                        </div>
                        <h3 class="text-base font-semibold text-gray-800">Extended Absence Members</h3>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Members who have been absent for an extended period may need follow-up.</p>
                    <a href="<?php echo BASE_URL; ?>attendance/reminders" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200 w-full justify-center">
                        <i class="fas fa-envelope mr-2"></i> Send Reminders
                    </a>
                </div>

                <!-- Irregular Attendance Members -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="p-2 rounded-full bg-yellow-100 text-yellow-600 mr-3">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3 class="text-base font-semibold text-gray-800">Irregular Attendance Members</h3>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">Members with irregular attendance patterns may benefit from engagement programs.</p>
                    <a href="#" class="bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200 w-full justify-center">
                        <i class="fas fa-users mr-2"></i> Create Engagement Program
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance History Modal -->
<div id="history-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[80vh] overflow-hidden">
        <div class="bg-gradient-to-r from-purple-600 to-indigo-700 p-4 text-white flex justify-between items-center">
            <h3 class="text-lg font-bold" id="history-modal-title">Attendance History</h3>
            <button type="button" class="text-white hover:text-gray-200" id="close-history-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-6 overflow-y-auto max-h-[calc(80vh-4rem)]" id="history-modal-content">
            <!-- Content will be loaded dynamically -->
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700 mx-auto"></div>
                <p class="mt-4 text-gray-600">Loading attendance history...</p>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('pattern-search');
        const table = document.getElementById('patterns-table');
        const rows = table.querySelectorAll('tbody tr');

        searchInput.addEventListener('keyup', function() {
            const searchTerm = searchInput.value.toLowerCase();
            let visibleCount = 0;

            rows.forEach(row => {
                const memberName = row.querySelector('td:first-child').textContent.toLowerCase();
                if (memberName.includes(searchTerm)) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Update search results info
            updateSearchInfo(searchTerm, visibleCount);
        });

        // Function to update search information
        function updateSearchInfo(searchTerm, visibleCount) {
            const existingInfo = document.getElementById('search-info');
            if (existingInfo) {
                existingInfo.remove();
            }

            if (searchTerm.length > 0) {
                const searchInfo = document.createElement('div');
                searchInfo.id = 'search-info';
                searchInfo.className = 'bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4';
                searchInfo.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-search text-blue-500 mr-2"></i>
                            <span class="text-sm text-blue-700">
                                Found ${visibleCount} member${visibleCount !== 1 ? 's' : ''} matching "${searchTerm}"
                            </span>
                        </div>
                        <button onclick="clearSearch()" class="text-blue-500 hover:text-blue-700 text-sm">
                            <i class="fas fa-times mr-1"></i>Clear
                        </button>
                    </div>
                `;

                // Insert before the table
                const tableContainer = document.querySelector('.overflow-x-auto');
                tableContainer.parentNode.insertBefore(searchInfo, tableContainer);
            }
        }

        // Function to clear search
        window.clearSearch = function() {
            searchInput.value = '';
            rows.forEach(row => {
                row.style.display = '';
            });
            const searchInfo = document.getElementById('search-info');
            if (searchInfo) {
                searchInfo.remove();
            }
        };

        // Modal functionality
        const historyModal = document.getElementById('history-modal');
        const closeHistoryModal = document.getElementById('close-history-modal');
        const historyModalTitle = document.getElementById('history-modal-title');
        const historyModalContent = document.getElementById('history-modal-content');
        const viewHistoryButtons = document.querySelectorAll('.view-history');

        viewHistoryButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const memberId = this.getAttribute('data-member-id');
                const memberName = this.getAttribute('data-member-name');

                historyModalTitle.textContent = `Attendance History - ${memberName}`;
                historyModal.classList.remove('hidden');

                // Show loading state
                historyModalContent.innerHTML = `
                    <div class="flex flex-col items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700 mx-auto"></div>
                        <p class="mt-4 text-gray-600">Loading attendance history...</p>
                    </div>
                `;

                // Fetch real attendance history via AJAX
                fetch(`<?php echo BASE_URL; ?>attendance/member-history?member_id=${memberId}`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to fetch attendance history');
                    }
                    return response.json();
                })
                .then(data => {
                    // Generate status badge HTML
                    function getStatusBadge(status) {
                        const badges = {
                            'present': '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Present</span>',
                            'absent': '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Absent</span>',
                            'late': '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Late</span>'
                        };
                        return badges[status] || '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Unknown</span>';
                    }

                    // Generate pattern description
                    function getPatternDescription(pattern, rate) {
                        const descriptions = {
                            'Very Regular': `This member has an excellent attendance pattern with ${rate}% attendance rate. They consistently attend services.`,
                            'Regular': `This member has a good attendance pattern with ${rate}% attendance rate. They attend services regularly with occasional absences.`,
                            'Somewhat Regular': `This member has a moderate attendance pattern with ${rate}% attendance rate. They attend services but with some inconsistency.`,
                            'Irregular': `This member has an irregular attendance pattern with ${rate}% attendance rate. They attend services infrequently.`,
                            'Very Irregular': `This member has a very irregular attendance pattern with ${rate}% attendance rate. They rarely attend services.`,
                            'Extended Absence': `This member has been absent for an extended period with ${rate}% attendance rate. Follow-up may be needed.`
                        };
                        return descriptions[pattern] || `This member has a ${rate}% attendance rate.`;
                    }

                    // Generate pattern icon
                    function getPatternIcon(pattern) {
                        const icons = {
                            'Very Regular': '<i class="fas fa-medal"></i>',
                            'Regular': '<i class="fas fa-thumbs-up"></i>',
                            'Somewhat Regular': '<i class="fas fa-check-circle"></i>',
                            'Irregular': '<i class="fas fa-exclamation-triangle"></i>',
                            'Very Irregular': '<i class="fas fa-times-circle"></i>',
                            'Extended Absence': '<i class="fas fa-user-times"></i>'
                        };
                        return icons[pattern] || '<i class="fas fa-question-circle"></i>';
                    }

                    // Generate pattern color
                    function getPatternColor(pattern) {
                        const colors = {
                            'Very Regular': 'bg-green-100 text-green-600',
                            'Regular': 'bg-teal-100 text-teal-600',
                            'Somewhat Regular': 'bg-blue-100 text-blue-600',
                            'Irregular': 'bg-yellow-100 text-yellow-600',
                            'Very Irregular': 'bg-red-100 text-red-600',
                            'Extended Absence': 'bg-gray-100 text-gray-600'
                        };
                        return colors[pattern] || 'bg-gray-100 text-gray-600';
                    }

                    // Build history table rows
                    let historyRows = '';
                    if (data.history && data.history.length > 0) {
                        historyRows = data.history.map(record => `
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${record.formatted_date}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${record.service_name}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-center">${getStatusBadge(record.status)}</td>
                            </tr>
                        `).join('');
                    } else {
                        historyRows = `
                            <tr>
                                <td colspan="3" class="px-4 py-8 text-center text-gray-500">
                                    <i class="fas fa-info-circle mb-2"></i><br>
                                    No attendance records found for the last 3 months
                                </td>
                            </tr>
                        `;
                    }

                    // Update modal content with real data
                    historyModalContent.innerHTML = `
                        <div class="mb-4">
                            <h4 class="text-base font-semibold text-gray-800 mb-2">Last 3 Months Attendance</h4>
                            <div class="bg-gray-100 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="text-sm font-medium text-gray-700">Attendance Rate</div>
                                    <div class="text-sm font-bold text-purple-700">${data.statistics.attendance_rate}%</div>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-purple-600 h-2.5 rounded-full" style="width: ${data.statistics.attendance_rate}%"></div>
                                </div>
                                <div class="mt-3 grid grid-cols-3 gap-4 text-center">
                                    <div>
                                        <div class="text-lg font-bold text-green-600">${data.statistics.present_count}</div>
                                        <div class="text-xs text-gray-500">Present</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-yellow-600">${data.statistics.late_count}</div>
                                        <div class="text-xs text-gray-500">Late</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-red-600">${data.statistics.absent_count}</div>
                                        <div class="text-xs text-gray-500">Absent</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                        <th class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    ${historyRows}
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6">
                            <h4 class="text-base font-semibold text-gray-800 mb-2">Attendance Pattern</h4>
                            <div class="bg-gray-100 rounded-lg p-4">
                                <div class="flex items-center mb-3">
                                    <div class="p-2 rounded-full ${getPatternColor(data.statistics.pattern)} mr-3">
                                        ${getPatternIcon(data.statistics.pattern)}
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-700">${data.statistics.pattern}</div>
                                        <div class="text-xs text-gray-500">Attends ${data.statistics.attendance_rate}% of services</div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-600">
                                    ${getPatternDescription(data.statistics.pattern, data.statistics.attendance_rate)}
                                </div>
                            </div>
                        </div>
                    `;
                })
                .catch(error => {
                    console.error('Error fetching attendance history:', error);
                    historyModalContent.innerHTML = `
                        <div class="flex flex-col items-center justify-center py-8 text-red-600">
                            <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium mb-2">Error Loading Data</h3>
                            <p class="text-sm text-gray-600 text-center">
                                Unable to load attendance history. Please try again later.
                            </p>
                        </div>
                    `;
                });
            });
        });

        closeHistoryModal.addEventListener('click', function() {
            historyModal.classList.add('hidden');
        });

        // Close modal when clicking outside
        historyModal.addEventListener('click', function(e) {
            if (e.target === historyModal) {
                historyModal.classList.add('hidden');
            }
        });
    });

    // Cleanup old attendance records function
    function cleanupOldRecords() {
        if (!confirm('Are you sure you want to remove all old manual attendance records?\n\nThis will:\n• Remove all manual attendance records\n• Keep only QR-based attendance records\n• Archive old records for backup\n\nThis action cannot be undone!')) {
            return;
        }

        // Show loading state
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Removing...';
        button.disabled = true;

        // Make AJAX request to cleanup endpoint
        fetch('<?php echo BASE_URL; ?>attendance/cleanup-old-records', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to cleanup records');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert(`Cleanup completed successfully!\n\n• ${data.records_removed} old records removed\n• ${data.records_archived} records archived\n• ${data.qr_records_remaining} QR records remaining\n\nThe page will now reload to show updated data.`);
                // Reload the page to show updated data
                window.location.reload();
            } else {
                throw new Error(data.message || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('Cleanup error:', error);
            alert('Error during cleanup: ' + error.message + '\n\nPlease try again or contact support.');
        })
        .finally(() => {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
</script>
