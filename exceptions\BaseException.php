<?php
/**
 * Base Exception
 * 
 * Base exception class for the finance system.
 * Provides common functionality for all custom exceptions.
 * 
 * @package Exceptions
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

class BaseException extends Exception
{
    /**
     * @var array Additional context data
     */
    protected $context = [];

    /**
     * @var string Error code for categorization
     */
    protected $errorCode;

    /**
     * BaseException constructor
     * 
     * @param string $message Error message
     * @param int $code Error code
     * @param Exception|null $previous Previous exception
     * @param array $context Additional context
     */
    public function __construct(string $message = "", int $code = 0, Exception $previous = null, array $context = [])
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
        $this->errorCode = static::class;
        
        // Log the exception
        $this->logException();
    }

    /**
     * Get additional context data
     * 
     * @return array Context data
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Get error code for categorization
     * 
     * @return string Error code
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    /**
     * Set additional context data
     * 
     * @param array $context Context data
     * @return void
     */
    public function setContext(array $context): void
    {
        $this->context = $context;
    }

    /**
     * Add context data
     * 
     * @param string $key Context key
     * @param mixed $value Context value
     * @return void
     */
    public function addContext(string $key, $value): void
    {
        $this->context[$key] = $value;
    }

    /**
     * Get formatted error message for users
     * 
     * @return string User-friendly error message
     */
    public function getUserMessage(): string
    {
        // Override in child classes for user-specific messages
        return "An error occurred. Please try again or contact support.";
    }

    /**
     * Get formatted error message for developers
     * 
     * @return string Developer-friendly error message
     */
    public function getDeveloperMessage(): string
    {
        return $this->getMessage();
    }

    /**
     * Convert exception to array
     * 
     * @return array Exception data
     */
    public function toArray(): array
    {
        return [
            'error_code' => $this->getErrorCode(),
            'message' => $this->getMessage(),
            'user_message' => $this->getUserMessage(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->getContext(),
            'trace' => $this->getTraceAsString()
        ];
    }

    /**
     * Convert exception to JSON
     * 
     * @return string JSON representation
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT);
    }

    /**
     * Log the exception
     * 
     * @return void
     */
    protected function logException(): void
    {
        $logData = [
            'exception_class' => get_class($this),
            'message' => $this->getMessage(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->getContext(),
            'user_id' => $_SESSION['user_id'] ?? null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        error_log('EXCEPTION: ' . json_encode($logData));
    }
}
