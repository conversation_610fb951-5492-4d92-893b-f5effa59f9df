<!-- Enhanced Background -->

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-green-50">
    <div class="container mx-auto px-4 py-8">
        <!-- Enhanced Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <div class="flex-1">
                    <div class="flex items-center gap-3 mb-3">
                        <a href="<?php echo url('groups'); ?>" class="text-green-600 hover:text-green-800 transition-colors p-2 rounded-lg hover:bg-green-50">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-800 mb-1"><?php echo htmlspecialchars($group->group_name ?? 'Unknown Group'); ?></h1>
                            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                                <span class="flex items-center gap-1 bg-green-50 px-2 py-1 rounded-full">
                                    <i class="fas fa-users text-green-600"></i>
                                    <?php echo $memberCount ?? 0; ?> Members
                                </span>
                                <?php if ($meetingSchedule): ?>
                                    <span class="flex items-center gap-1 bg-blue-50 px-2 py-1 rounded-full">
                                        <i class="fas fa-calendar text-blue-600"></i>
                                        <?php echo ucfirst($meetingSchedule->meeting_day); ?>s at <?php echo date('g:i A', strtotime($meetingSchedule->meeting_time)); ?>
                                    </span>
                                <?php endif; ?>
                                <span class="flex items-center gap-1 bg-purple-50 px-2 py-1 rounded-full">
                                    <i class="fas fa-tag text-purple-600"></i>
                                    <?php echo htmlspecialchars($group->type_name ?? 'Group'); ?>
                                </span>
                                <span class="flex items-center gap-1 bg-gray-50 px-2 py-1 rounded-full">
                                    <i class="fas fa-circle text-green-500 text-xs"></i>
                                    <?php echo ucfirst($group->status ?? 'active'); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php if ($group->group_description): ?>
                        <div class="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border-l-4 border-green-500 mt-3">
                            <p class="text-gray-700"><?php echo htmlspecialchars($group->group_description); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="flex gap-3">
                    <a href="<?php echo url('groups/add-members/' . ($group->group_id ?? 1)); ?>" class="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center">
                        <i class="fas fa-user-plus mr-2"></i>Add Members
                    </a>
                    <a href="<?php echo url('groups/edit/' . ($group->group_id ?? 1)); ?>" class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center">
                        <i class="fas fa-edit mr-2"></i>Edit Group
                    </a>
                </div>
            </div>
        </div>

    <?php if (function_exists('flash')): ?>
        <?php flash('group_message'); ?>
    <?php endif; ?>

        <!-- Compact Quick Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <!-- Members Card -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Total Members</p>
                        <h3 class="text-2xl font-bold"><?php echo $memberCount ?? 0; ?></h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Meetings Card -->
            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Total Meetings</p>
                        <h3 class="text-2xl font-bold"><?php echo isset($meetings) ? count($meetings) : 0; ?></h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-calendar-check text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Announcements Card -->
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Announcements</p>
                        <h3 class="text-2xl font-bold"><?php echo isset($announcements) ? count($announcements) : 0; ?></h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-bullhorn text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Dues Collection Card -->
            <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-md p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium opacity-75">Dues Collected</p>
                        <h3 class="text-2xl font-bold">
                            <?php if ($duesStatistics && $duesStatistics->total_collected > 0): ?>
                                <?php echo $duesStatistics->currency ?? 'GHS'; ?> <?php echo number_format($duesStatistics->total_collected, 0); ?>
                            <?php else: ?>
                                <?php echo $duesSettings->currency ?? 'GHS'; ?> 0
                            <?php endif; ?>
                        </h3>
                    </div>
                    <div class="bg-white bg-opacity-30 rounded-full p-3">
                        <i class="fas fa-money-bill-wave text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Tabs -->
        <div class="bg-white rounded-xl shadow-lg mb-8 overflow-hidden">
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                <nav class="flex flex-wrap justify-center lg:justify-start space-x-2 lg:space-x-8 px-6 py-2" aria-label="Tabs">
                    <button onclick="showTab('members')" id="members-tab" class="tab-button active bg-green-600 text-white rounded-lg px-4 py-3 text-sm font-medium transition-all duration-200 transform hover:scale-105 shadow-md">
                        <i class="fas fa-users mr-2"></i>Members
                        <span class="ml-2 bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs"><?php echo $memberCount ?? 0; ?></span>
                    </button>
                    <button onclick="showTab('schedule')" id="schedule-tab" class="tab-button bg-white hover:bg-gray-50 text-gray-600 hover:text-gray-800 rounded-lg px-4 py-3 text-sm font-medium transition-all duration-200 transform hover:scale-105 shadow-sm border border-gray-200">
                        <i class="fas fa-calendar-alt mr-2"></i>Schedule
                        <?php if ($meetingSchedule): ?>
                            <span class="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Active</span>
                        <?php endif; ?>
                    </button>
                    <button onclick="showTab('announcements')" id="announcements-tab" class="tab-button bg-white hover:bg-gray-50 text-gray-600 hover:text-gray-800 rounded-lg px-4 py-3 text-sm font-medium transition-all duration-200 transform hover:scale-105 shadow-sm border border-gray-200">
                        <i class="fas fa-bullhorn mr-2"></i>Announcements
                        <span class="ml-2 bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs"><?php echo isset($announcements) ? count($announcements) : 0; ?></span>
                    </button>
                    <button onclick="showTab('attendance')" id="attendance-tab" class="tab-button bg-white hover:bg-gray-50 text-gray-600 hover:text-gray-800 rounded-lg px-4 py-3 text-sm font-medium transition-all duration-200 transform hover:scale-105 shadow-sm border border-gray-200">
                        <i class="fas fa-check-circle mr-2"></i>Attendance
                        <span class="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"><?php echo isset($meetings) ? count($meetings) : 0; ?></span>
                    </button>
                    <button onclick="showTab('dues')" id="dues-tab" class="tab-button bg-white hover:bg-gray-50 text-gray-600 hover:text-gray-800 rounded-lg px-4 py-3 text-sm font-medium transition-all duration-200 transform hover:scale-105 shadow-sm border border-gray-200">
                        <i class="fas fa-money-bill-wave mr-2"></i>Dues
                        <?php if ($duesSettings): ?>
                            <span class="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Setup</span>
                        <?php else: ?>
                            <span class="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">None</span>
                        <?php endif; ?>
                    </button>
                </nav>
            </div>
        </div>

        <!-- Tab Contents -->
        <div id="members-content" class="tab-content">
        <!-- Enhanced Members Table -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
            <!-- Header with Search and Filters -->
            <div class="bg-gradient-to-r from-green-50 to-blue-50 p-6 border-b border-gray-200">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div>
                        <h2 class="text-xl font-bold text-gray-800 mb-1">Group Members</h2>
                        <p class="text-gray-600 text-sm">
                            Showing <span id="members-showing">0</span> of <span id="members-total"><?php echo $memberCount ?? 0; ?></span> members
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex gap-3">
                        <a href="<?php echo url('groups/add-members/' . ($group->group_id ?? 1)); ?>" class="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center text-sm">
                            <i class="fas fa-user-plus mr-2"></i>Add Members
                        </a>
                        <button onclick="exportMembers()" class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center text-sm">
                            <i class="fas fa-download mr-2"></i>Export
                        </button>
                    </div>
                </div>

                <!-- Search and Filter Controls -->
                <div class="mt-4 flex flex-col sm:flex-row gap-3">
                    <!-- Search Input -->
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" id="member-search" placeholder="Search members by name, email, or phone..."
                               value="<?php echo htmlspecialchars($currentFilters['search'] ?? ''); ?>"
                               class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm w-full">
                    </div>

                    <!-- Filters -->
                    <div class="flex gap-2">
                        <!-- Role Filter -->
                        <select id="role-filter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                            <option value="">All Roles</option>
                            <?php if (!empty($availableRoles)): ?>
                                <?php foreach ($availableRoles as $role): ?>
                                    <option value="<?php echo htmlspecialchars($role); ?>"
                                            <?php echo ($currentFilters['role'] ?? '') === $role ? 'selected' : ''; ?>>
                                        <?php echo ucfirst($role); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>

                        <!-- Status Filter -->
                        <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                            <option value="active" <?php echo ($currentFilters['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($currentFilters['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="all" <?php echo ($currentFilters['status'] ?? '') === 'all' ? 'selected' : ''; ?>>All</option>
                        </select>


                    </div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div id="members-loading" class="hidden px-6 py-8 text-center">
                <div class="inline-flex items-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mr-3"></div>
                    <span class="text-gray-600">Loading members...</span>
                </div>
            </div>
            <div id="members-table-container" class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gradient-to-r from-gray-100 to-gray-200">
                        <tr>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <i class="fas fa-user mr-2 text-green-600"></i>Member
                            </th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <i class="fas fa-envelope mr-2 text-blue-600"></i>Contact
                            </th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <i class="fas fa-tag mr-2 text-purple-600"></i>Role
                            </th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <i class="fas fa-calendar mr-2 text-orange-600"></i>Joined
                            </th>
                            <th scope="col" class="px-6 py-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <i class="fas fa-cog mr-2 text-gray-600"></i>Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody id="members-tbody" class="bg-white divide-y divide-gray-100">
                        <?php if (!empty($membersData['members'])): ?>
                            <?php foreach ($membersData['members'] as $member): ?>
                            <tr class="hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-12 w-12">
                                            <?php if (!empty($member->profile_picture)): ?>
                                                <img src="<?php echo url('uploads/members/' . $member->profile_picture); ?>" alt="<?php echo $member->first_name; ?>" class="h-12 w-12 rounded-full object-cover border-2 border-green-200 shadow-md">
                                            <?php else: ?>
                                                <div class="h-12 w-12 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center text-white font-bold text-lg shadow-md border-2 border-green-200">
                                                    <?php echo substr($member->first_name ?? 'U', 0, 1) . substr($member->last_name ?? 'N', 0, 1); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-bold text-gray-900"><?php echo htmlspecialchars(($member->first_name ?? 'Unknown') . ' ' . ($member->last_name ?? 'Name')); ?></div>
                                            <div class="text-xs text-gray-500 flex items-center gap-2">
                                                <span class="bg-gray-100 px-2 py-1 rounded-full">ID: <?php echo $member->id ?? 'N/A'; ?></span>
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full"><?php echo ucfirst($member->gender ?? 'unknown'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="space-y-1">
                                        <div class="flex items-center text-sm text-gray-900">
                                            <i class="fas fa-phone text-green-600 mr-2"></i>
                                            <?php echo htmlspecialchars($member->phone_number ?? 'N/A'); ?>
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <i class="fas fa-envelope text-blue-600 mr-2"></i>
                                            <?php echo htmlspecialchars($member->email ?? 'N/A'); ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300 shadow-sm">
                                        <i class="fas fa-user-tag mr-1"></i>
                                        <?php echo ucfirst($member->role_in_group ?? 'member'); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-calendar-plus text-orange-600 mr-2"></i>
                                        <span class="bg-orange-50 px-2 py-1 rounded-full text-orange-800 font-medium text-xs">
                                            <?php echo isset($member->joined_date) ? date('M d, Y', strtotime($member->joined_date)) : 'Unknown'; ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right">
                                    <div class="flex justify-end space-x-2">
                                        <a href="<?php echo url('members/view/' . (isset($member->id) ? $member->id : (isset($member['id']) ? $member['id'] : 1))); ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200 flex items-center" title="View Member">
                                            <i class="fas fa-eye mr-1"></i>View
                                        </a>
                                        <button onclick="confirmRemoveMember(<?php echo isset($member->id) ? $member->id : (isset($member['id']) ? $member['id'] : 1); ?>, '<?php echo addslashes((isset($member->first_name) ? $member->first_name : (isset($member['first_name']) ? $member['first_name'] : 'Unknown')) . ' ' . (isset($member->last_name) ? $member->last_name : (isset($member['last_name']) ? $member['last_name'] : 'Name'))); ?>')" class="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200 flex items-center" title="Remove from Group">
                                            <i class="fas fa-user-minus mr-1"></i>Remove
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                        <tr>
                            <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-users text-4xl text-gray-300 mb-2"></i>
                                    <p class="text-lg font-medium">No members found</p>
                                    <p class="text-sm">Try adjusting your search or filter criteria</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <?php if (!empty($membersData) && $membersData['pages'] > 1): ?>
                <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                    <div class="text-sm text-gray-700">
                        Showing <?php echo (($membersData['current_page'] - 1) * $membersData['per_page']) + 1; ?>
                        to <?php echo min($membersData['current_page'] * $membersData['per_page'], $membersData['total']); ?>
                        of <?php echo $membersData['total']; ?> results
                    </div>

                    <div class="flex items-center space-x-2">
                        <!-- Previous Button -->
                        <?php if ($membersData['has_prev']): ?>
                            <button type="button" onclick="MemberManager.loadPage(<?php echo $membersData['current_page'] - 1; ?>)"
                                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                <i class="fas fa-chevron-left mr-1"></i>Previous
                            </button>
                        <?php endif; ?>

                        <!-- Page Numbers -->
                        <?php
                        $start = max(1, $membersData['current_page'] - 2);
                        $end = min($membersData['pages'], $membersData['current_page'] + 2);
                        ?>

                        <?php if ($start > 1): ?>
                            <button type="button" onclick="MemberManager.loadPage(1)" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">1</button>
                            <?php if ($start > 2): ?>
                                <span class="px-2 py-2 text-sm text-gray-500">...</span>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php for ($i = $start; $i <= $end; $i++): ?>
                            <button type="button" onclick="MemberManager.loadPage(<?php echo $i; ?>)"
                                    class="px-3 py-2 text-sm font-medium <?php echo $i == $membersData['current_page'] ? 'text-green-600 bg-green-50 border-green-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50'; ?> border rounded-md">
                                <?php echo $i; ?>
                            </button>
                        <?php endfor; ?>

                        <?php if ($end < $membersData['pages']): ?>
                            <?php if ($end < $membersData['pages'] - 1): ?>
                                <span class="px-2 py-2 text-sm text-gray-500">...</span>
                            <?php endif; ?>
                            <button type="button" onclick="MemberManager.loadPage(<?php echo $membersData['pages']; ?>)" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"><?php echo $membersData['pages']; ?></button>
                        <?php endif; ?>

                        <!-- Next Button -->
                        <?php if ($membersData['has_next']): ?>
                            <button type="button" onclick="MemberManager.loadPage(<?php echo $membersData['current_page'] + 1; ?>)"
                                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Next<i class="fas fa-chevron-right ml-1"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        </div>

        <!-- Schedule Tab Content -->
        <div id="schedule-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-800">Meeting Schedule</h3>
                <button onclick="openScheduleModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>Set Schedule
                </button>
            </div>

            <?php if (isset($meetingSchedule) && $meetingSchedule): ?>
            <!-- Current Schedule Display -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-lg font-semibold text-blue-900 mb-2">Current Schedule</h4>
                        <div class="space-y-2">
                            <p class="text-blue-700 flex items-center">
                                <i class="fas fa-calendar mr-3 text-blue-600"></i>
                                <span class="font-medium">Every <?php echo ucfirst($meetingSchedule->meeting_day); ?></span>
                            </p>
                            <p class="text-blue-700 flex items-center">
                                <i class="fas fa-clock mr-3 text-blue-600"></i>
                                <span class="font-medium"><?php echo date('g:i A', strtotime($meetingSchedule->meeting_time)); ?></span>
                            </p>
                            <?php if (!empty($meetingSchedule->meeting_location)): ?>
                            <p class="text-blue-700 flex items-center">
                                <i class="fas fa-map-marker-alt mr-3 text-blue-600"></i>
                                <span class="font-medium"><?php echo htmlspecialchars($meetingSchedule->meeting_location); ?></span>
                            </p>
                            <?php endif; ?>
                            <?php if (!empty($meetingSchedule->description)): ?>
                            <p class="text-blue-700 flex items-center">
                                <i class="fas fa-info-circle mr-3 text-blue-600"></i>
                                <span class="font-medium"><?php echo htmlspecialchars($meetingSchedule->description); ?></span>
                            </p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-2">
                        <button onclick="openScheduleModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i>Edit
                        </button>
                        <button onclick="deleteScheduleNew()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>Delete
                        </button>
                    </div>
                </div>
            </div>

            <!-- Next Meeting Info -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <h5 class="font-semibold text-green-900 mb-2">Next Meeting</h5>
                <?php
                $nextMeeting = null;
                $today = new DateTime();
                $daysOfWeek = ['sunday' => 0, 'monday' => 1, 'tuesday' => 2, 'wednesday' => 3, 'thursday' => 4, 'friday' => 5, 'saturday' => 6];
                $targetDay = $daysOfWeek[strtolower($meetingSchedule->meeting_day)];
                $currentDay = (int)$today->format('w');

                if ($targetDay > $currentDay) {
                    $daysUntil = $targetDay - $currentDay;
                } else {
                    $daysUntil = 7 - ($currentDay - $targetDay);
                }

                $nextMeeting = clone $today;
                $nextMeeting->add(new DateInterval('P' . $daysUntil . 'D'));
                ?>
                <p class="text-green-700">
                    <i class="fas fa-calendar-day mr-2"></i>
                    <?php echo $nextMeeting->format('l, F j, Y'); ?> at <?php echo date('g:i A', strtotime($meetingSchedule->meeting_time)); ?>
                </p>
                <p class="text-sm text-green-600 mt-1">
                    <?php echo $daysUntil == 0 ? 'Today!' : ($daysUntil == 1 ? 'Tomorrow' : 'In ' . $daysUntil . ' days'); ?>
                </p>
            </div>

            <!-- Schedule Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="bg-purple-500 rounded-full p-2 mr-3">
                            <i class="fas fa-calendar-check text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm text-purple-600 font-medium">Schedule Active</p>
                            <p class="text-lg font-bold text-purple-900">
                                <?php echo isset($meetingSchedule->created_at) ?
                                    floor((time() - strtotime($meetingSchedule->created_at)) / (60 * 60 * 24 * 7)) . ' weeks' : 'Recently'; ?>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="bg-orange-500 rounded-full p-2 mr-3">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm text-orange-600 font-medium">Meeting Duration</p>
                            <p class="text-lg font-bold text-orange-900">
                                <?php echo isset($meetingSchedule->duration) ? $meetingSchedule->duration . ' mins' : '60 mins'; ?>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="bg-teal-500 rounded-full p-2 mr-3">
                            <i class="fas fa-users text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm text-teal-600 font-medium">Expected Attendees</p>
                            <p class="text-lg font-bold text-teal-900"><?php echo count($members); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <?php else: ?>
            <!-- No Schedule Set -->
            <div class="text-center py-12">
                <i class="fas fa-calendar-alt text-6xl text-gray-400 mb-6"></i>
                <h4 class="text-xl font-medium text-gray-900 mb-3">No Schedule Set</h4>
                <p class="text-gray-600 mb-6 max-w-md mx-auto">Set up a regular meeting schedule for this group to help members know when to attend and improve consistency.</p>
                <button onclick="openScheduleModal()" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200">
                    <i class="fas fa-calendar-plus mr-2"></i>Create Schedule
                </button>
            </div>
            <?php endif; ?>
            </div>
        </div>

        <!-- Announcements Tab Content -->
        <div id="announcements-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-800">Group Announcements</h3>
                <button onclick="showAnnouncementModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>New Announcement
                </button>
            </div>

            <?php if (!empty($announcements)): ?>
                <div class="space-y-4">
                    <?php foreach ($announcements as $announcement): ?>
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex-1">
                                    <div class="flex items-center gap-2 mb-2">
                                        <h4 class="text-lg font-semibold text-gray-800"><?= htmlspecialchars($announcement->title) ?></h4>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            <?php
                                            switch($announcement->announcement_type) {
                                                case 'urgent': echo 'bg-red-100 text-red-800'; break;
                                                case 'prayer_request': echo 'bg-purple-100 text-purple-800'; break;
                                                case 'event': echo 'bg-blue-100 text-blue-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800'; break;
                                            }
                                            ?>">
                                            <?= ucfirst(str_replace('_', ' ', $announcement->announcement_type)) ?>
                                        </span>
                                    </div>
                                    <p class="text-gray-600 mb-2"><?= nl2br(htmlspecialchars($announcement->content)) ?></p>
                                    <div class="text-sm text-gray-500">
                                        <span>Posted by <?= htmlspecialchars($announcement->first_name . ' ' . $announcement->last_name) ?></span>
                                        <span class="mx-2">•</span>
                                        <span><?= date('M j, Y g:i A', strtotime($announcement->created_at)) ?></span>
                                        <?php if ($announcement->expires_at): ?>
                                            <span class="mx-2">•</span>
                                            <span class="text-orange-600">Expires: <?= date('M j, Y', strtotime($announcement->expires_at)) ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="flex gap-2 ml-4">
                                    <button onclick="editAnnouncement(<?= $announcement->id ?>, '<?= htmlspecialchars($announcement->title, ENT_QUOTES) ?>', '<?= htmlspecialchars($announcement->content, ENT_QUOTES) ?>', '<?= $announcement->announcement_type ?>', '<?= $announcement->expires_at ?>')"
                                            class="text-blue-600 hover:text-blue-800 p-1">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="deleteAnnouncement(<?= $announcement->id ?>)"
                                            class="text-red-600 hover:text-red-800 p-1">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-bullhorn text-4xl text-gray-400 mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Announcements</h4>
                    <p class="text-gray-600">Share important updates with your group members</p>
                </div>
            <?php endif; ?>
            </div>
        </div>

        <!-- Attendance Tab Content -->
        <div id="attendance-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-800">Attendance Tracking</h3>
                <button onclick="showAttendanceModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>Record Attendance
                </button>
            </div>

            <!-- Attendance Statistics -->
            <?php if ($attendanceStats && $attendanceStats->total_meetings > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-calendar-check text-blue-600 text-2xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-blue-900">Total Meetings</p>
                                <p class="text-2xl font-bold text-blue-600"><?= $attendanceStats->total_meetings ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-user-check text-green-600 text-2xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-green-900">Attendance Records</p>
                                <p class="text-2xl font-bold text-green-600"><?= $attendanceStats->total_attendance_records ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-percentage text-purple-600 text-2xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-purple-900">Average Attendance</p>
                                <p class="text-2xl font-bold text-purple-600"><?= number_format($attendanceStats->average_attendance_rate, 1) ?>%</p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Meeting History -->
            <?php if (!empty($meetings)): ?>
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Recent Meetings</h4>
                    <?php foreach ($meetings as $meeting): ?>
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-2">
                                        <h5 class="text-lg font-semibold text-gray-800"><?= htmlspecialchars($meeting->meeting_topic ?: 'Group Meeting') ?></h5>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            <?php
                                            switch($meeting->status) {
                                                case 'completed': echo 'bg-green-100 text-green-800'; break;
                                                case 'scheduled': echo 'bg-blue-100 text-blue-800'; break;
                                                case 'cancelled': echo 'bg-red-100 text-red-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800'; break;
                                            }
                                            ?>">
                                            <?= ucfirst($meeting->status) ?>
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-600 mb-2">
                                        <i class="fas fa-calendar mr-2"></i><?= date('M j, Y', strtotime($meeting->meeting_date)) ?>
                                        <i class="fas fa-clock ml-4 mr-2"></i><?= date('g:i A', strtotime($meeting->meeting_time)) ?>
                                        <?php if ($meeting->meeting_location): ?>
                                            <i class="fas fa-map-marker-alt ml-4 mr-2"></i><?= htmlspecialchars($meeting->meeting_location) ?>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <span>Created by <?= htmlspecialchars($meeting->first_name . ' ' . $meeting->last_name) ?></span>
                                        <span class="mx-2">•</span>
                                        <span class="text-blue-600"><?= $meeting->total_attendance ?> attendance records</span>
                                    </div>
                                    <?php if ($meeting->meeting_notes): ?>
                                        <p class="text-sm text-gray-700 mt-2"><?= nl2br(htmlspecialchars($meeting->meeting_notes)) ?></p>
                                    <?php endif; ?>
                                </div>
                                <div class="flex gap-2 ml-4">
                                    <a href="<?php echo url('groups/view-attendance/' . $meeting->id); ?>"
                                       class="text-blue-600 hover:text-blue-800 p-1" title="View Attendance">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if ($meeting->status === 'scheduled'): ?>
                                        <button onclick="editMeeting(<?= $meeting->id ?>, '<?= htmlspecialchars($meeting->meeting_topic, ENT_QUOTES) ?>', '<?= $meeting->meeting_date ?>', '<?= $meeting->meeting_time ?>', '<?= htmlspecialchars($meeting->meeting_location, ENT_QUOTES) ?>', '<?= htmlspecialchars($meeting->meeting_notes, ENT_QUOTES) ?>')"
                                                class="text-green-600 hover:text-green-800 p-1" title="Record Attendance">
                                            <i class="fas fa-user-check"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-clipboard-list text-4xl text-gray-400 mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Meetings Yet</h4>
                    <p class="text-gray-600">Start tracking attendance by creating your first meeting</p>
                </div>
            <?php endif; ?>
            </div>
        </div>

        <!-- Dues Tab Content -->
        <div id="dues-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-800">Group Dues Management</h3>
                <div class="flex gap-2">
                    <?php if ($duesSettings): ?>
                        <button onclick="showPaymentModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>Record Payment
                        </button>
                        <button onclick="showDuesSetupModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-cog mr-2"></i>Edit Settings
                        </button>
                    <?php else: ?>
                        <button onclick="showDuesSetupModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-cog mr-2"></i>Setup Dues
                        </button>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($duesSettings): ?>
                <!-- Dues Settings Display -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h4 class="text-lg font-semibold text-gray-800 mb-3">Current Dues Settings</h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <p class="text-sm text-gray-600">Amount</p>
                            <p class="text-lg font-semibold text-green-600"><?= $duesSettings->currency ?> <?= number_format($duesSettings->dues_amount, 2) ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Frequency</p>
                            <p class="text-lg font-semibold text-gray-800"><?= ucfirst($duesSettings->dues_frequency) ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Due Date</p>
                            <p class="text-lg font-semibold text-gray-800">
                                <?php
                                function ordinal($number) {
                                    $ends = array('th','st','nd','rd','th','th','th','th','th','th');
                                    if ((($number % 100) >= 11) && (($number%100) <= 13))
                                        return $number. 'th';
                                    else
                                        return $number. $ends[$number % 10];
                                }

                                if ($duesSettings->dues_frequency === 'monthly') {
                                    echo ordinal($duesSettings->due_date) . ' of each month';
                                } else {
                                    echo 'Day ' . $duesSettings->due_date;
                                }
                                ?>
                            </p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Status</p>
                            <p class="text-lg font-semibold text-green-600">Active</p>
                        </div>
                    </div>
                </div>

                <!-- Dues Statistics -->
                <?php if ($duesStatistics): ?>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-blue-600"><?= $duesStatistics->total_members ?></div>
                            <div class="text-sm text-blue-800">Total Members</div>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-green-600"><?= $duesStatistics->paid_full_members ?></div>
                            <div class="text-sm text-green-800">Paid Full</div>
                        </div>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-yellow-600"><?= $duesStatistics->paid_partial_members ?></div>
                            <div class="text-sm text-yellow-800">Paid Partial</div>
                        </div>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-red-600"><?= $duesStatistics->total_members - $duesStatistics->paid_members - $duesStatistics->exempt_members ?></div>
                            <div class="text-sm text-red-800">Unpaid</div>
                        </div>
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-purple-600"><?= $duesStatistics->exempt_members ?></div>
                            <div class="text-sm text-purple-800">Exempt</div>
                        </div>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-yellow-600"><?= $duesStatistics->currency ?> <?= number_format($duesStatistics->total_collected, 2) ?></div>
                            <div class="text-sm text-yellow-800">Collected</div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Period Selector -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">View Period</label>
                    <select id="duesPeriodSelector" onchange="changeDuesPeriod()" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <?php
                        $currentPeriod = $_GET['period'] ?? date('Y-m');
                        for ($i = 0; $i < 12; $i++) {
                            $period = date('Y-m', strtotime("-$i months"));
                            $periodLabel = date('F Y', strtotime($period . '-01'));
                            $selected = $period === $currentPeriod ? 'selected' : '';
                            echo "<option value='$period' $selected>$periodLabel</option>";
                        }
                        ?>
                    </select>
                </div>

                <!-- Member Dues Status -->
                <?php if (!empty($memberDuesStatus)): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="py-3 px-4 border-b text-left">Member</th>
                                    <th class="py-3 px-4 border-b text-left">Status</th>
                                    <th class="py-3 px-4 border-b text-left">Amount</th>
                                    <th class="py-3 px-4 border-b text-left">Payment Date</th>
                                    <th class="py-3 px-4 border-b text-left">Method</th>
                                    <th class="py-3 px-4 border-b text-left">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($memberDuesStatus as $member): ?>
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="py-3 px-4">
                                            <div class="flex items-center">
                                                <?php if (!empty($member->profile_picture)): ?>
                                                    <img src="/icgc/uploads/members/<?= $member->profile_picture ?>" alt="<?= $member->first_name ?>" class="w-8 h-8 rounded-full mr-3">
                                                <?php else: ?>
                                                    <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                                                        <span class="text-gray-600 font-semibold text-sm"><?= substr($member->first_name, 0, 1) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <p class="font-medium"><?= htmlspecialchars($member->first_name . ' ' . $member->last_name) ?></p>
                                                    <p class="text-xs text-gray-500"><?= htmlspecialchars($member->role_in_group) ?></p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <?php if ($member->exemption_status === 'exempt'): ?>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
                                                    <i class="fas fa-user-shield mr-1"></i>Exempt
                                                </span>
                                            <?php elseif ($member->dues_status === 'paid_full'): ?>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle mr-1"></i>Paid Full
                                                </span>
                                                <div class="text-xs text-gray-500 mt-1">
                                                    <?= $member->currency ?> <?= number_format($member->payment_amount, 2) ?>
                                                    <br>on <?= date('M d, Y', strtotime($member->payment_date)) ?>
                                                </div>
                                            <?php elseif ($member->dues_status === 'paid_partial'): ?>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                                    <i class="fas fa-exclamation-triangle mr-1"></i>Partial (<?= $member->payment_percentage ?>%)
                                                </span>
                                                <div class="text-xs text-gray-500 mt-1">
                                                    <div class="flex justify-between">
                                                        <span>Paid: <?= $member->currency ?> <?= number_format($member->payment_amount, 2) ?></span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span>Remaining: <?= $member->currency ?> <?= number_format($member->remaining_amount, 2) ?></span>
                                                    </div>
                                                    <div class="text-blue-600 mt-1">
                                                        Paid on <?= date('M d, Y', strtotime($member->payment_date)) ?>
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                                                    <i class="fas fa-times-circle mr-1"></i>Unpaid
                                                </span>
                                                <div class="text-xs text-gray-500 mt-1">
                                                    Expected: <?= $member->currency ?> <?= number_format($member->expected_amount, 2) ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="py-3 px-4 text-sm">
                                            <?= $member->payment_amount ? $duesSettings->currency . ' ' . number_format($member->payment_amount, 2) : '-' ?>
                                        </td>
                                        <td class="py-3 px-4 text-sm">
                                            <?= $member->payment_date ? date('M j, Y', strtotime($member->payment_date)) : '-' ?>
                                        </td>
                                        <td class="py-3 px-4 text-sm">
                                            <?= $member->payment_method ? ucfirst(str_replace('_', ' ', $member->payment_method)) : '-' ?>
                                        </td>
                                        <td class="py-3 px-4">
                                            <?php if ($member->exemption_status !== 'exempt'): ?>
                                                <button onclick="recordPaymentForMember(<?= $member->member_id ?>, '<?= htmlspecialchars($member->first_name . ' ' . $member->last_name, ENT_QUOTES) ?>')"
                                                        class="text-blue-600 hover:text-blue-800 text-sm">
                                                    <i class="fas fa-money-bill mr-1"></i>
                                                    <?php if ($member->dues_status === 'paid_full'): ?>
                                                        Update Payment
                                                    <?php elseif ($member->dues_status === 'paid_partial'): ?>
                                                        Complete Payment
                                                    <?php else: ?>
                                                        Record Payment
                                                    <?php endif; ?>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">No Members Found</h4>
                        <p class="text-gray-600">No members found for the selected period</p>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-money-bill-wave text-4xl text-gray-400 mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Dues Setup</h4>
                    <p class="text-gray-600">Configure dues amount and frequency for this group</p>
                </div>
            <?php endif; ?>
            </div>
        </div>

<script>
    // Tab functionality
    function showTab(tabName) {
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => content.classList.add('hidden'));

        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.classList.remove('active', 'bg-green-600', 'text-white');
            button.classList.add('bg-white', 'text-gray-600', 'border', 'border-gray-200');
        });

        // Show selected tab content
        document.getElementById(tabName + '-content').classList.remove('hidden');

        // Add active class to selected tab button
        const activeButton = document.getElementById(tabName + '-tab');
        activeButton.classList.add('active', 'bg-green-600', 'text-white');
        activeButton.classList.remove('bg-white', 'text-gray-600', 'border', 'border-gray-200');

        // Update URL with tab parameter
        const url = new URL(window.location);
        url.searchParams.set('tab', tabName);
        window.history.replaceState({}, '', url);
    }

    // Initialize tab from URL parameter
    function initializeTabFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');

        if (tabParam && document.getElementById(tabParam + '-content')) {
            showTab(tabParam);
        } else {
            // Default to members tab
            showTab('members');
        }
    }

    // Initialize tab when page loads
    document.addEventListener('DOMContentLoaded', function() {
        initializeTabFromURL();

        // Check if there's a success message and we're on dues tab
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');

        if (tabParam === 'dues') {
            // Scroll to dues section if redirected after payment
            setTimeout(() => {
                const duesContent = document.getElementById('dues-content');
                if (duesContent && !duesContent.classList.contains('hidden')) {
                    duesContent.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 100);
        }
    });

    // Centralized Group Configuration
    <?php
    // Extract group data with fallback strategies
    $groupId = null;
    $groupName = 'Unknown Group';

    if (isset($group) && is_object($group)) {
        $groupId = (int)($group->group_id ?? $group->id ?? 0);
        $groupName = $group->group_name ?? 'Unknown Group';
    }

    // URL fallback if group data not available
    if (!$groupId) {
        $pathParts = explode('/', parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));
        $lastPart = end($pathParts);
        $groupId = is_numeric($lastPart) ? (int)$lastPart : 0;
    }
    ?>

    // Group Utility Object
    const GroupUtils = {
        config: {
            groupId: <?php echo $groupId ?: 'null'; ?>,
            groupName: '<?php echo addslashes($groupName); ?>'
        },

        getGroupId: function() {
            if (this.config.groupId) return this.config.groupId;

            // Fallback strategies
            const strategies = [
                () => {
                    const match = window.location.pathname.match(/\/icgc\/groups\/members\/(\d+)/);
                    return match ? parseInt(match[1]) : null;
                },
                () => {
                    const match = window.location.href.match(/\/members\/(\d+)/);
                    return match ? parseInt(match[1]) : null;
                },
                () => {
                    const urlParams = new URLSearchParams(window.location.search);
                    const id = urlParams.get('id');
                    return id && !isNaN(id) ? parseInt(id) : null;
                }
            ];

            for (const strategy of strategies) {
                const id = strategy();
                if (id) {
                    this.config.groupId = id;
                    return id;
                }
            }

            return null;
        }
    };

    // Legacy support - maintain GROUP_CONFIG for existing code
    const GROUP_CONFIG = GroupUtils.config;

    // Simple helper function to get group ID from URL
    function getGroupIdFromUrl() {
        const pathParts = window.location.pathname.split('/');
        const groupId = pathParts[pathParts.length - 1];
        return (!groupId || groupId === '' || isNaN(groupId)) ? null : parseInt(groupId);
    }

    // Initialize group configuration
    GroupUtils.getGroupId(); // Ensure group ID is resolved

    // Enhanced Member Management System for Large Datasets
    const MemberManager = {
        currentPage: <?php echo $currentFilters['page'] ?? 1; ?>,
        currentLimit: <?php echo $currentFilters['limit'] ?? 20; ?>,
        currentSearch: '<?php echo addslashes($currentFilters['search'] ?? ''); ?>',
        currentStatus: '<?php echo $currentFilters['status'] ?? 'active'; ?>',
        currentRole: '<?php echo addslashes($currentFilters['role'] ?? ''); ?>',
        currentSort: '<?php echo $currentFilters['sort'] ?? 'name'; ?>',
        currentOrder: '<?php echo $currentFilters['order'] ?? 'ASC'; ?>',
        isLoading: false,
        searchTimeout: null,

        init: function() {
            this.bindEvents();
            this.updateMemberCount();
        },

        bindEvents: function() {
            // Search input with debounce
            const searchInput = document.getElementById('member-search');
            if (searchInput) {
                searchInput.addEventListener('input', (e) => {
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = setTimeout(() => {
                        this.currentSearch = e.target.value;
                        this.currentPage = 1;
                        this.loadMembers();
                    }, 500);
                });
            }

            // Filter dropdowns
            const roleFilter = document.getElementById('role-filter');
            if (roleFilter) {
                roleFilter.addEventListener('change', (e) => {
                    this.currentRole = e.target.value;
                    this.currentPage = 1;
                    this.loadMembers();
                });
            }

            const statusFilter = document.getElementById('status-filter');
            if (statusFilter) {
                statusFilter.addEventListener('change', (e) => {
                    this.currentStatus = e.target.value;
                    this.currentPage = 1;
                    this.loadMembers();
                });
            }


        },

        loadMembers: function() {
            if (this.isLoading) return;

            this.isLoading = true;
            this.showLoading();

            const groupId = getGroupIdFromUrl();
            if (!groupId) {
                ErrorHandler.handleMissingId('Group ID');
                this.hideLoading();
                return;
            }

            const params = new URLSearchParams({
                ajax: 'members',
                page: this.currentPage,
                limit: this.currentLimit,
                search: this.currentSearch,
                status: this.currentStatus,
                role: this.currentRole,
                sort: this.currentSort,
                order: this.currentOrder,
                csrf_token: '<?php echo generate_csrf_token(); ?>'
            });

            fetch(`<?php echo url('groups/members/'); ?>${groupId}?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.updateTable(data.html);
                        this.updatePagination(data.pagination);
                        this.updateMemberCount(data.pagination);
                        this.updateURL();
                    } else {
                        ErrorHandler.show(data.error || 'Failed to load members');
                    }
                })
                .catch(error => {
                    console.error('Error loading members:', error);
                    ErrorHandler.show('Network error while loading members');
                })
                .finally(() => {
                    this.isLoading = false;
                    this.hideLoading();
                });
        },

        loadPage: function(page) {
            this.currentPage = page;
            this.loadMembers();
        },

        sort: function(field) {
            if (this.currentSort === field) {
                this.currentOrder = this.currentOrder === 'ASC' ? 'DESC' : 'ASC';
            } else {
                this.currentSort = field;
                this.currentOrder = 'ASC';
            }
            this.currentPage = 1;
            this.loadMembers();
            this.updateSortIcons();
        },

        updateTable: function(html) {
            const tbody = document.getElementById('members-tbody');
            if (tbody) {
                tbody.innerHTML = html;
            }
        },

        updatePagination: function(pagination) {
            // Update pagination container with new pagination HTML
            // This would be generated server-side in a real implementation
            console.log('Pagination updated:', pagination);
        },

        updateMemberCount: function(pagination = null) {
            const showingElement = document.getElementById('members-showing');
            const totalElement = document.getElementById('members-total');

            if (pagination) {
                const start = ((pagination.current_page - 1) * pagination.per_page) + 1;
                const end = Math.min(pagination.current_page * pagination.per_page, pagination.total);

                if (showingElement) showingElement.textContent = pagination.total > 0 ? `${start}-${end}` : '0';
                if (totalElement) totalElement.textContent = pagination.total;
            } else {
                // Initial load - count actual table rows
                const tableRows = document.querySelectorAll('#members-tbody tr');
                const totalFromElement = totalElement ? parseInt(totalElement.textContent) || 0 : 0;
                const showing = tableRows.length;

                if (showingElement) showingElement.textContent = showing;

                // Update total if it's 0 but we have rows
                if (totalFromElement === 0 && showing > 0 && totalElement) {
                    totalElement.textContent = showing;
                }
            }
        },

        updateSortIcons: function() {
            // Reset all sort icons
            document.querySelectorAll('[data-sort] i').forEach(icon => {
                icon.className = 'fas fa-sort ml-2 text-gray-400';
            });

            // Update current sort icon
            const currentSortHeader = document.querySelector(`[data-sort="${this.currentSort}"] i`);
            if (currentSortHeader) {
                currentSortHeader.className = `fas fa-sort-${this.currentOrder === 'ASC' ? 'up' : 'down'} ml-2 text-green-600`;
            }
        },

        updateURL: function() {
            const url = new URL(window.location);
            url.searchParams.set('page', this.currentPage);
            url.searchParams.set('limit', this.currentLimit);
            url.searchParams.set('search', this.currentSearch);
            url.searchParams.set('status', this.currentStatus);
            url.searchParams.set('role', this.currentRole);
            url.searchParams.set('sort', this.currentSort);
            url.searchParams.set('order', this.currentOrder);

            window.history.replaceState({}, '', url);
        },

        showLoading: function() {
            const loading = document.getElementById('members-loading');
            const table = document.getElementById('members-table-container');
            if (loading) loading.classList.remove('hidden');
            if (table) table.style.opacity = '0.5';
        },

        hideLoading: function() {
            const loading = document.getElementById('members-loading');
            const table = document.getElementById('members-table-container');
            if (loading) loading.classList.add('hidden');
            if (table) table.style.opacity = '1';
        }
    };

    // Export Members Function
    function exportMembers() {
        const groupId = getGroupIdFromUrl();
        if (!groupId) {
            ErrorHandler.handleMissingId('Group ID');
            return;
        }

        // Create export URL with current filters
        const params = new URLSearchParams({
            export: 'csv',
            search: MemberManager.currentSearch,
            status: MemberManager.currentStatus,
            role: MemberManager.currentRole,
            sort: MemberManager.currentSort,
            order: MemberManager.currentOrder
        });

        window.location.href = `<?php echo url('groups/export-members/'); ?>${groupId}?${params.toString()}`;
    }

    // Initialize Member Manager when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        MemberManager.init();

        // Update member count after a short delay to ensure table is loaded
        setTimeout(function() {
            MemberManager.updateMemberCount();
        }, 100);
    });

    // Enhanced member removal with centralized utilities
    function confirmRemoveMember(memberId, memberName) {
        const groupId = getGroupIdFromUrl();

        if (!groupId) {
            ErrorHandler.handleMissingId('Group ID');
            return;
        }

        if (!memberId) {
            ErrorHandler.show('Member ID not found.');
            return;
        }

        if (confirm(`Are you sure you want to remove ${memberName} from this group?`)) {
            FormUtils.submitPost(`<?php echo url('groups/remove-member/'); ?>${groupId}/${memberId}`);
        }
    }

    // Centralized Utilities
    const FormUtils = {
        submitPost: function(url, data = {}) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = url;

            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = '<?php echo generate_csrf_token(); ?>';
            form.appendChild(csrfInput);

            // Add data as hidden inputs if provided
            Object.entries(data).forEach(([key, value]) => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }
    };

    // Error Handler Utility
    const ErrorHandler = {
        show: function(message, type = 'error') {
            // Try to use notification system if available
            if (window.showNotification && typeof window.showNotification === 'function') {
                window.showNotification(message, type);
            } else {
                // Fallback to alert
                alert(message);
            }
        },

        handleMissingId: function(idType = 'ID') {
            this.show(`Error: Could not determine ${idType}. Please refresh the page and try again.`);
        }
    };

    // Reusable Modal Management System
    const ModalManager = {
        open: function(modalId, options = {}) {
            const modal = document.getElementById(modalId);
            if (!modal) return;

            modal.classList.remove('hidden');
            modal.classList.add('flex');

            if (options.title) {
                const titleElement = modal.querySelector('[data-modal-title]');
                if (titleElement) titleElement.textContent = options.title;
            }

            if (options.formAction) {
                const form = modal.querySelector('form');
                if (form) form.action = options.formAction;
            }

            if (options.resetForm) {
                const form = modal.querySelector('form');
                if (form) form.reset();
            }

            if (options.callback) options.callback();
        },

        close: function(modalId) {
            const modal = document.getElementById(modalId);
            if (!modal) return;

            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
    };

    // Schedule Modal Functions
    function openScheduleModal() {
        ModalManager.open('scheduleModal', {
            callback: function() {
                // Pre-fill form if editing existing schedule
                <?php if (isset($meetingSchedule) && $meetingSchedule): ?>
                document.querySelector('select[name="meeting_day"]').value = '<?php echo $meetingSchedule->meeting_day; ?>';
                document.querySelector('input[name="meeting_time"]').value = '<?php echo $meetingSchedule->meeting_time; ?>';
                document.querySelector('input[name="meeting_location"]').value = '<?php echo htmlspecialchars($meetingSchedule->meeting_location ?? ''); ?>';
                document.querySelector('textarea[name="description"]').value = '<?php echo htmlspecialchars($meetingSchedule->description ?? ''); ?>';
                document.querySelector('input[name="duration"]').value = '<?php echo $meetingSchedule->duration ?? 60; ?>';
                <?php endif; ?>
            }
        });
    }

    function closeScheduleModal() {
        ModalManager.close('scheduleModal');
    }

    // Bulletproof delete schedule function with multiple fallback methods
    function deleteScheduleNew() {
        const currentPath = window.location.pathname;
        const pathParts = currentPath.split('/');
        let groupId = null;

        // Method 1: Find the group ID after 'members'
        const membersIndex = pathParts.indexOf('members');
        if (membersIndex !== -1 && pathParts[membersIndex + 1]) {
            groupId = pathParts[membersIndex + 1];
        }

        // Method 2: Fallback - get last numeric part
        if (!groupId || isNaN(groupId)) {
            for (let i = pathParts.length - 1; i >= 0; i--) {
                if (pathParts[i] && !isNaN(pathParts[i]) && pathParts[i] !== '') {
                    groupId = pathParts[i];
                    break;
                }
            }
        }

        if (!groupId || isNaN(groupId)) {
            alert('Error: Could not determine group ID from the current page URL.');
            return;
        }

        // Convert to integer and confirm deletion
        groupId = parseInt(groupId);

        if (confirm('Are you sure you want to delete the meeting schedule? This will remove the regular meeting time for this group.')) {
            const deleteUrl = '<?php echo url('groups/delete-schedule/'); ?>' + groupId;
            window.location.href = deleteUrl;
        }
    }

    // Legacy compatibility - redirect to new function
    function deleteSchedule() {
        deleteScheduleNew();
    }

    // Announcement functions
    function showAnnouncementModal() {
        ModalManager.open('announcementModal', {
            title: 'New Announcement',
            formAction: '<?php echo url('groups/create-announcement/' . ($group->group_id ?? 1)); ?>',
            resetForm: true
        });
    }

    function editAnnouncement(id, title, content, type, expiresAt) {
        ModalManager.open('announcementModal', {
            title: 'Edit Announcement',
            formAction: '<?php echo url('groups/update-announcement/'); ?>' + id,
            callback: function() {
                document.querySelector('input[name="title"]').value = title;
                document.querySelector('textarea[name="content"]').value = content;
                document.querySelector('select[name="announcement_type"]').value = type;
                document.querySelector('input[name="expires_at"]').value = expiresAt || '';
            }
        });
    }

    function closeAnnouncementModal() {
        ModalManager.close('announcementModal');
    }

    function deleteAnnouncement(id) {
        if (window.GroupsCommon) {
            window.GroupsCommon.deleteAnnouncement(id);
        } else {
            // Fallback
            if (confirm('Are you sure you want to delete this announcement?')) {
                window.location.href = '<?php echo url('groups/delete-announcement/'); ?>' + id;
            }
        }
    }

    // Attendance functions
    function showAttendanceModal() {
        ModalManager.open('attendanceModal', {
            title: 'Record Meeting Attendance',
            formAction: '<?php echo url('groups/create-meeting/' . ($group->group_id ?? 1)); ?>',
            resetForm: true,
            callback: function() {
                // Set default date and time
                const now = new Date();
                const today = now.toISOString().split('T')[0];
                const currentTime = now.toTimeString().slice(0, 5);

                document.querySelector('input[name="meeting_date"]').value = today;
                document.querySelector('input[name="meeting_time"]').value = currentTime;
            }
        });
    }

    function editMeeting(id, topic, date, time, location, notes) {
        ModalManager.open('attendanceModal', {
            title: 'Record Attendance - ' + topic,
            formAction: '<?php echo url('groups/create-meeting/' . ($group->group_id ?? 1)); ?>',
            callback: function() {
                document.querySelector('input[name="meeting_date"]').value = date;
                document.querySelector('input[name="meeting_time"]').value = time;
                document.querySelector('input[name="meeting_location"]').value = location || '';
                document.querySelector('input[name="meeting_topic"]').value = topic || '';
                document.querySelector('textarea[name="meeting_notes"]').value = notes || '';
            }
        });
    }

    function closeAttendanceModal() {
        ModalManager.close('attendanceModal');
    }

    function toggleAllAttendance(status) {
        const checkboxes = document.querySelectorAll('select[name^="attendance["]');
        checkboxes.forEach(select => {
            select.value = status;
        });
    }

    // Dues functions
    function showDuesSetupModal() {
        ModalManager.open('duesSetupModal', {
            callback: function() {
                // Pre-fill existing settings if available
                <?php if ($duesSettings): ?>
                    document.querySelector('input[name="dues_amount"]').value = '<?= $duesSettings->dues_amount ?>';
                    document.querySelector('select[name="dues_frequency"]').value = '<?= $duesSettings->dues_frequency ?>';
                    document.querySelector('input[name="due_date"]').value = '<?= $duesSettings->due_date ?>';
                    document.querySelector('select[name="currency"]').value = '<?= $duesSettings->currency ?>';
                <?php endif; ?>
            }
        });
    }

    function closeDuesSetupModal() {
        ModalManager.close('duesSetupModal');
    }

    function showPaymentModal() {
        ModalManager.open('paymentModal', {
            title: 'Record Dues Payment',
            resetForm: true,
            callback: function() {
                // Reset completing partial flag
                document.getElementById('completingPartial').value = 'false';

                // Set default values
                setToday();
                setCurrentMonth();
                <?php if ($duesSettings): ?>
                    document.querySelector('input[name="payment_amount"]').value = '<?= $duesSettings->dues_amount ?>';
                <?php endif; ?>

                // Hide member info initially
                document.getElementById('memberInfo').classList.add('hidden');

                // Reset payment method fields
                updatePaymentMethodFields();
            }
        });
    }

    function recordPaymentForMember(memberId, memberName) {
        showPaymentModal();

        // Find member data to check current payment status
        const memberData = <?= json_encode($memberDuesStatus) ?>;
        const currentMember = memberData.find(m => m.member_id == memberId);

        if (currentMember && currentMember.dues_status === 'paid_partial') {
            document.getElementById('paymentModalTitle').textContent = 'Complete Payment - ' + memberName;

            // Set the completing partial flag
            document.getElementById('completingPartial').value = 'true';

            // Show current payment info
            const currentPaymentInfo = document.createElement('div');
            currentPaymentInfo.id = 'currentPaymentInfo';
            currentPaymentInfo.className = 'bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4';
            currentPaymentInfo.innerHTML = `
                <div class="flex items-center mb-2">
                    <i class="fas fa-info-circle text-yellow-600 mr-2"></i>
                    <h4 class="font-semibold text-yellow-900">Current Payment Status</h4>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-yellow-700 font-medium">Already Paid:</span>
                        <span class="text-yellow-900">${currentMember.currency} ${parseFloat(currentMember.payment_amount).toFixed(2)}</span>
                    </div>
                    <div>
                        <span class="text-yellow-700 font-medium">Remaining:</span>
                        <span class="text-yellow-900">${currentMember.currency} ${parseFloat(currentMember.remaining_amount).toFixed(2)}</span>
                    </div>
                    <div>
                        <span class="text-yellow-700 font-medium">Percentage Paid:</span>
                        <span class="text-yellow-900">${currentMember.payment_percentage}%</span>
                    </div>
                    <div>
                        <span class="text-yellow-700 font-medium">Last Payment:</span>
                        <span class="text-yellow-900">${new Date(currentMember.payment_date).toLocaleDateString()}</span>
                    </div>
                </div>
                <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                    <p class="text-sm text-blue-800">
                        <i class="fas fa-lightbulb mr-1"></i>
                        <strong>Payment Options:</strong> Enter the remaining amount to complete the payment, or enter any amount for an additional partial payment.
                    </p>
                </div>
            `;

            // Insert after the dues settings info
            const duesInfo = document.querySelector('#paymentModal .bg-blue-50');
            if (duesInfo) {
                duesInfo.parentNode.insertBefore(currentPaymentInfo, duesInfo.nextSibling);
            }

            // Pre-fill with remaining amount
            document.querySelector('input[name="payment_amount"]').value = parseFloat(currentMember.remaining_amount).toFixed(2);

            // Add dynamic buttons for partial payment completion
            const partialButtons = document.getElementById('partialPaymentButtons');
            if (partialButtons) {
                partialButtons.innerHTML = `
                    <button type="button" onclick="setAmount(${parseFloat(currentMember.remaining_amount).toFixed(2)})"
                            class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded hover:bg-orange-200 transition-colors">
                        Complete Payment (${currentMember.currency} ${parseFloat(currentMember.remaining_amount).toFixed(2)})
                    </button>
                    <button type="button" onclick="setAmount(${(parseFloat(currentMember.remaining_amount) / 2).toFixed(2)})"
                            class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded hover:bg-yellow-200 transition-colors">
                        Half Remaining (${currentMember.currency} ${(parseFloat(currentMember.remaining_amount) / 2).toFixed(2)})
                    </button>
                `;
                partialButtons.classList.remove('hidden');

                // Hide the regular amount buttons
                const regularButtons = document.getElementById('amountButtons');
                if (regularButtons) {
                    regularButtons.classList.add('hidden');
                }
            }
        } else if (currentMember && currentMember.dues_status === 'paid_full') {
            document.getElementById('paymentModalTitle').textContent = 'Update Payment - ' + memberName;
        } else {
            document.getElementById('paymentModalTitle').textContent = 'Record Payment - ' + memberName;
        }

        document.querySelector('select[name="member_id"]').value = memberId;
        updateMemberInfo();
    }

    function closePaymentModal() {
        ModalManager.close('paymentModal');

        // Clean up current payment info if it exists
        const currentPaymentInfo = document.getElementById('currentPaymentInfo');
        if (currentPaymentInfo) {
            currentPaymentInfo.remove();
        }

        // Reset button visibility
        const partialButtons = document.getElementById('partialPaymentButtons');
        const regularButtons = document.getElementById('amountButtons');

        if (partialButtons) {
            partialButtons.classList.add('hidden');
            partialButtons.innerHTML = '';
        }

        if (regularButtons) {
            regularButtons.classList.remove('hidden');
        }
    }

    // Enhanced payment modal functions
    function updateMemberInfo() {
        const select = document.getElementById('memberSelect');
        const selectedOption = select.options[select.selectedIndex];
        const memberInfo = document.getElementById('memberInfo');

        if (selectedOption.value) {
            const name = selectedOption.dataset.name;
            const role = selectedOption.dataset.role;
            const picture = selectedOption.dataset.picture;

            document.getElementById('memberName').textContent = name;
            document.getElementById('memberRole').textContent = role;

            const avatar = document.getElementById('memberAvatar');
            const initial = document.getElementById('memberInitial');

            if (picture) {
                avatar.innerHTML = `<img src="/icgc/uploads/members/${picture}" alt="${name}" class="w-10 h-10 rounded-full">`;
            } else {
                initial.textContent = name.charAt(0).toUpperCase();
                avatar.innerHTML = '';
                avatar.appendChild(initial);
            }

            memberInfo.classList.remove('hidden');
        } else {
            memberInfo.classList.add('hidden');
        }
    }

    function setAmount(amount) {
        document.querySelector('input[name="payment_amount"]').value = amount.toFixed(2);
    }

    function setToday() {
        const today = new Date().toISOString().split('T')[0];
        document.querySelector('input[name="payment_date"]').value = today;
    }

    function setYesterday() {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        document.querySelector('input[name="payment_date"]').value = yesterday.toISOString().split('T')[0];
    }

    function setCurrentMonth() {
        const currentMonth = new Date().toISOString().slice(0, 7);
        document.querySelector('input[name="payment_period"]').value = currentMonth;
    }

    function setPreviousMonth() {
        const previousMonth = new Date();
        previousMonth.setMonth(previousMonth.getMonth() - 1);
        document.querySelector('input[name="payment_period"]').value = previousMonth.toISOString().slice(0, 7);
    }

    function updatePaymentMethodFields() {
        const method = document.getElementById('paymentMethodSelect').value;
        const referenceField = document.getElementById('paymentReference');
        const referenceHint = document.getElementById('referenceHint');

        switch(method) {
            case 'mobile_money':
                referenceField.placeholder = 'Mobile Money Transaction ID';
                referenceHint.textContent = 'Enter the mobile money transaction reference number';
                break;
            case 'bank_transfer':
                referenceField.placeholder = 'Bank Transfer Reference';
                referenceHint.textContent = 'Enter bank transfer reference or transaction ID';
                break;
            case 'cheque':
                referenceField.placeholder = 'Cheque Number';
                referenceHint.textContent = 'Enter the cheque number';
                break;
            case 'cash':
                referenceField.placeholder = 'Receipt Number (optional)';
                referenceHint.textContent = 'Enter receipt number if available';
                break;
            default:
                referenceField.placeholder = 'Transaction Reference';
                referenceHint.textContent = 'Enter transaction reference or receipt number';
        }
    }

    function changeDuesPeriod() {
        const period = document.getElementById('duesPeriodSelector').value;
        window.location.href = '<?php echo url('groups/members/' . ($group->group_id ?? 1)); ?>?period=' + period + '#dues';
    }
</script>

<!-- Schedule Modal -->
<div id="scheduleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-md mx-auto m-4">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-gray-900">
                <?php echo (isset($meetingSchedule) && $meetingSchedule) ? 'Edit Meeting Schedule' : 'Set Meeting Schedule'; ?>
            </h3>
            <button onclick="closeScheduleModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form action="<?php echo url('groups/save-schedule/' . ($group->group_id ?? 1)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar mr-2 text-green-600"></i>Meeting Day *
                    </label>
                    <select name="meeting_day" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                        <option value="">Select Day</option>
                        <option value="monday">Monday</option>
                        <option value="tuesday">Tuesday</option>
                        <option value="wednesday">Wednesday</option>
                        <option value="thursday">Thursday</option>
                        <option value="friday">Friday</option>
                        <option value="saturday">Saturday</option>
                        <option value="sunday">Sunday</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-clock mr-2 text-green-600"></i>Meeting Time *
                    </label>
                    <input type="time" name="meeting_time" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-clock mr-2 text-green-600"></i>Duration (minutes)
                    </label>
                    <input type="number" name="duration" min="15" max="480" value="60" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-map-marker-alt mr-2 text-green-600"></i>Meeting Location
                    </label>
                    <input type="text" name="meeting_location" placeholder="e.g., Church Hall, Room 101, Online" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-info-circle mr-2 text-green-600"></i>Description
                    </label>
                    <textarea name="description" rows="3" placeholder="Additional details about the meeting schedule" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"></textarea>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeScheduleModal()" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>Save Schedule
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Announcement Modal -->
<div id="announcementModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg mx-auto m-4">
        <div class="flex justify-between items-center mb-6">
            <h3 id="announcementModalTitle" class="text-xl font-semibold text-gray-900">New Announcement</h3>
            <button onclick="closeAnnouncementModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="announcementForm" method="POST">
            <?php echo csrf_field(); ?>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-heading mr-2 text-green-600"></i>Title *
                    </label>
                    <input type="text" name="title" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-align-left mr-2 text-green-600"></i>Content *
                    </label>
                    <textarea name="content" rows="4" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-tag mr-2 text-green-600"></i>Type
                    </label>
                    <select name="announcement_type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="general">General</option>
                        <option value="urgent">Urgent</option>
                        <option value="prayer_request">Prayer Request</option>
                        <option value="event">Event</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-times mr-2 text-green-600"></i>Expires At (Optional)
                    </label>
                    <input type="datetime-local" name="expires_at" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeAnnouncementModal()" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>Save Announcement
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-4xl mx-auto m-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h3 id="attendanceModalTitle" class="text-xl font-semibold text-gray-900">Record Meeting Attendance</h3>
            <button onclick="closeAttendanceModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="attendanceForm" method="POST">
            <?php echo csrf_field(); ?>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-day mr-2 text-green-600"></i>Meeting Date *
                    </label>
                    <input type="date" name="meeting_date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-clock mr-2 text-green-600"></i>Meeting Time *
                    </label>
                    <input type="time" name="meeting_time" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-map-marker-alt mr-2 text-green-600"></i>Location
                    </label>
                    <input type="text" name="meeting_location" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-heading mr-2 text-green-600"></i>Topic
                    </label>
                    <input type="text" name="meeting_topic" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>
            </div>

            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-sticky-note mr-2 text-green-600"></i>Meeting Notes
                </label>
                <textarea name="meeting_notes" rows="2" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"></textarea>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Member Attendance</h4>

                <div class="flex justify-end mb-4 space-x-2">
                    <button type="button" onclick="toggleAllAttendance('present')" class="px-3 py-1 bg-green-100 text-green-800 rounded hover:bg-green-200 text-sm">
                        All Present
                    </button>
                    <button type="button" onclick="toggleAllAttendance('absent')" class="px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200 text-sm">
                        All Absent
                    </button>
                    <button type="button" onclick="toggleAllAttendance('excused')" class="px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200 text-sm">
                        All Excused
                    </button>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="py-2 px-4 border-b text-left">Member</th>
                                <th class="py-2 px-4 border-b text-left">Status</th>
                                <th class="py-2 px-4 border-b text-left">Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($membersData['members'])): ?>
                                <?php foreach ($membersData['members'] as $member): ?>
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="py-2 px-4">
                                            <div class="flex items-center">
                                                <?php if (!empty($member->profile_picture)): ?>
                                                    <img src="/icgc/uploads/members/<?= $member->profile_picture ?>" alt="<?= $member->first_name ?>" class="w-8 h-8 rounded-full mr-3">
                                                <?php else: ?>
                                                    <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                                                        <span class="text-gray-600 font-semibold"><?= substr($member->first_name, 0, 1) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <p class="font-medium"><?= $member->first_name ?> <?= $member->last_name ?></p>
                                                    <p class="text-xs text-gray-500"><?= $member->role_in_group ?></p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-2 px-4">
                                            <select name="attendance[<?= $member->id ?>]" class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-green-500">
                                                <option value="present">Present</option>
                                                <option value="absent">Absent</option>
                                                <option value="late">Late</option>
                                                <option value="excused">Excused</option>
                                            </select>
                                        </td>
                                        <td class="py-2 px-4">
                                            <input type="text" name="notes[<?= $member->id ?>]" placeholder="Optional notes" class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-green-500">
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="3" class="py-4 px-4 text-center text-gray-500">No members in this group yet</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeAttendanceModal()" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>Save Attendance
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Dues Setup Modal -->
<div id="duesSetupModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg mx-auto m-4">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-gray-900">Setup Group Dues</h3>
            <button onclick="closeDuesSetupModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form method="POST" action="<?php echo url('groups/save-dues-settings/' . ($group->group_id ?? 1)); ?>">
            <?php echo csrf_field(); ?>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-money-bill mr-2 text-green-600"></i>Dues Amount *
                    </label>
                    <input type="number" name="dues_amount" step="0.01" min="0" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-alt mr-2 text-green-600"></i>Frequency *
                    </label>
                    <select name="dues_frequency" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                        <option value="weekly">Weekly</option>
                        <option value="monthly" selected>Monthly</option>
                        <option value="quarterly">Quarterly</option>
                        <option value="yearly">Yearly</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-day mr-2 text-green-600"></i>Due Date *
                    </label>
                    <input type="number" name="due_date" min="1" max="31" value="1" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                    <p class="text-xs text-gray-500 mt-1">Day of the month when dues are due (1-31)</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-coins mr-2 text-green-600"></i>Currency
                    </label>
                    <select name="currency" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="GHS" selected>GHS (Ghana Cedis)</option>
                        <option value="USD">USD (US Dollars)</option>
                        <option value="EUR">EUR (Euros)</option>
                        <option value="GBP">GBP (British Pounds)</option>
                    </select>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeDuesSetupModal()" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Payment Modal -->
<div id="paymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl mx-auto m-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h3 id="paymentModalTitle" class="text-xl font-semibold text-gray-900">Record Dues Payment</h3>
                <p class="text-sm text-gray-600 mt-1">Record payment for group member dues</p>
            </div>
            <button onclick="closePaymentModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Dues Settings Info -->
        <?php if ($duesSettings): ?>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-center mb-2">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    <h4 class="font-semibold text-blue-900">Current Dues Settings</h4>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="text-blue-700 font-medium">Amount:</span>
                        <span class="text-blue-900"><?= $duesSettings->currency ?> <?= number_format($duesSettings->dues_amount, 2) ?></span>
                    </div>
                    <div>
                        <span class="text-blue-700 font-medium">Frequency:</span>
                        <span class="text-blue-900"><?= ucfirst($duesSettings->dues_frequency) ?></span>
                    </div>
                    <div>
                        <span class="text-blue-700 font-medium">Due Date:</span>
                        <span class="text-blue-900"><?= ordinal($duesSettings->due_date) ?> of month</span>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <form id="paymentForm" method="POST" action="<?php echo url('groups/record-dues-payment/' . ($group->group_id ?? 1)); ?>">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="completing_partial" id="completingPartial" value="false">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-green-600"></i>Member *
                        </label>
                        <select id="memberSelect" name="member_id" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required onchange="updateMemberInfo()">
                            <option value="">Select Member</option>
                            <?php if (!empty($membersData['members'])): ?>
                                <?php foreach ($membersData['members'] as $member): ?>
                                    <option value="<?= $member->id ?>"
                                            data-name="<?= htmlspecialchars($member->first_name . ' ' . $member->last_name) ?>"
                                            data-role="<?= htmlspecialchars($member->role_in_group) ?>"
                                            data-picture="<?= $member->profile_picture ?>">
                                        <?= htmlspecialchars($member->first_name . ' ' . $member->last_name) ?> (<?= htmlspecialchars($member->role_in_group) ?>)
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>

                        <!-- Member Info Display -->
                        <div id="memberInfo" class="hidden mt-3 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div id="memberAvatar" class="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                                    <span id="memberInitial" class="text-gray-600 font-semibold"></span>
                                </div>
                                <div>
                                    <p id="memberName" class="font-medium text-gray-900"></p>
                                    <p id="memberRole" class="text-sm text-gray-600"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-money-bill mr-2 text-green-600"></i>Payment Amount *
                        </label>
                        <div class="relative">
                            <span class="absolute left-3 top-2 text-gray-500"><?= $duesSettings->currency ?? 'GHS' ?></span>
                            <input type="number" name="payment_amount" step="0.01" min="0"
                                   class="w-full border border-gray-300 rounded-lg pl-12 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                   placeholder="0.00" required>
                        </div>
                        <?php if ($duesSettings): ?>
                            <div class="mt-2 flex gap-2" id="amountButtons">
                                <button type="button" onclick="setAmount(<?= $duesSettings->dues_amount ?>)"
                                        class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded hover:bg-green-200 transition-colors">
                                    Full Amount (<?= $duesSettings->currency ?> <?= number_format($duesSettings->dues_amount, 2) ?>)
                                </button>
                                <button type="button" onclick="setAmount(<?= $duesSettings->dues_amount / 2 ?>)"
                                        class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200 transition-colors">
                                    Half Amount (<?= $duesSettings->currency ?> <?= number_format($duesSettings->dues_amount / 2, 2) ?>)
                                </button>
                            </div>
                            <!-- Dynamic buttons for partial payment completion will be added here -->
                            <div id="partialPaymentButtons" class="mt-2 flex gap-2 hidden"></div>
                        <?php endif; ?>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-calendar mr-2 text-green-600"></i>Payment Date *
                        </label>
                        <input type="date" name="payment_date"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               required>
                        <div class="mt-2 flex gap-2">
                            <button type="button" onclick="setToday()"
                                    class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded hover:bg-gray-200 transition-colors">
                                Today
                            </button>
                            <button type="button" onclick="setYesterday()"
                                    class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded hover:bg-gray-200 transition-colors">
                                Yesterday
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-calendar-alt mr-2 text-green-600"></i>Payment Period *
                        </label>
                        <input type="month" name="payment_period"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               required>
                        <p class="text-xs text-gray-500 mt-1">Month and year this payment is for</p>
                        <div class="mt-2 flex gap-2">
                            <button type="button" onclick="setCurrentMonth()"
                                    class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded hover:bg-gray-200 transition-colors">
                                Current Month
                            </button>
                            <button type="button" onclick="setPreviousMonth()"
                                    class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded hover:bg-gray-200 transition-colors">
                                Previous Month
                            </button>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-credit-card mr-2 text-green-600"></i>Payment Method *
                        </label>
                        <select name="payment_method" id="paymentMethodSelect" onchange="updatePaymentMethodFields()"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                            <option value="cash">💵 Cash</option>
                            <option value="mobile_money">📱 Mobile Money</option>
                            <option value="bank_transfer">🏦 Bank Transfer</option>
                            <option value="cheque">📄 Cheque</option>
                            <option value="other">🔧 Other</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-receipt mr-2 text-green-600"></i>Payment Reference
                        </label>
                        <input type="text" name="payment_reference" id="paymentReference"
                               placeholder="Transaction ID, Receipt number, etc."
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <p id="referenceHint" class="text-xs text-gray-500 mt-1">Enter transaction reference or receipt number</p>
                    </div>
                </div>
            </div>

            <!-- Full Width Fields -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-sticky-note mr-2 text-green-600"></i>Payment Notes
                </label>
                <textarea name="payment_notes" rows="3"
                          placeholder="Additional notes about this payment (optional)"
                          class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"></textarea>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center mt-8">
                <div class="text-sm text-gray-600">
                    <i class="fas fa-info-circle mr-1"></i>
                    All fields marked with * are required
                </div>
                <div class="flex space-x-3">
                    <button type="button" onclick="closePaymentModal()"
                            class="px-6 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>
                    <button type="submit" id="submitPaymentBtn"
                            class="px-8 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium">
                        <i class="fas fa-save mr-2"></i>Record Payment
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    // Enhanced JavaScript functions
    function exportMembers() {
        // Placeholder for export functionality
        alert('Export functionality will be implemented soon!');
    }

    // Payment form submission handler
    document.addEventListener('DOMContentLoaded', function() {
        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            paymentForm.addEventListener('submit', function(e) {
                const submitBtn = document.getElementById('submitPaymentBtn');
                if (submitBtn) {
                    // Show loading state
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                    submitBtn.disabled = true;
                    submitBtn.classList.add('opacity-75');
                }
            });
        }
    });
</script>

<script src="<?php echo url('assets/js/groups-common.js'); ?>"></script>

    </div> <!-- Close container -->
</div> <!-- Close background wrapper -->
