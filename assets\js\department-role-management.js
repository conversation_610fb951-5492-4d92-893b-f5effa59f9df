/**
 * Department and Role Management JavaScript
 * Shared functions for managing departments and roles
 */

// Centralized URL generation function
const getBaseUrl = () => {
    return window.BASE_URL || '/icgc/';
};

// Generate URL with proper path handling
const generateUrl = (path) => {
    const baseUrl = getBaseUrl();
    return baseUrl + path.replace(/^\/+/, '');
};

// Add new department
async function addDepartment(event) {
    event.preventDefault();
    
    const displayName = document.getElementById('newDeptDisplayName').value;
    const description = document.getElementById('newDeptDescription').value;
    const name = displayName.toLowerCase().replace(/[^a-z0-9]/g, '_');
    
    try {
        const response = await fetch(getBaseUrl() + 'api/departments.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                display_name: displayName,
                description: description
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('addDepartmentForm').reset();
            loadDepartmentList();
            loadDepartments(); // Refresh main dropdown
            showNotification('Department added successfully!', 'success');
        } else {
            showNotification(data.message || 'Error adding department', 'error');
        }
    } catch (error) {
        console.error('Error adding department:', error);
        showNotification('Error adding department', 'error');
    }
}

// Add new role
async function addRole(event) {
    event.preventDefault();
    
    const displayName = document.getElementById('newRoleDisplayName').value;
    const description = document.getElementById('newRoleDescription').value;
    const name = displayName.toLowerCase().replace(/[^a-z0-9]/g, '_');
    
    try {
        const response = await fetch(getBaseUrl() + 'api/roles.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                display_name: displayName,
                description: description
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('addRoleForm').reset();
            loadRoleList();
            loadRoles(); // Refresh main dropdown
            showNotification('Role added successfully!', 'success');
        } else {
            showNotification(data.message || 'Error adding role', 'error');
        }
    } catch (error) {
        console.error('Error adding role:', error);
        showNotification('Error adding role', 'error');
    }
}

// Edit department
function editDepartment(id, displayName, description) {
    const newDisplayName = prompt('Enter new display name:', displayName);
    if (newDisplayName && newDisplayName !== displayName) {
        updateDepartment(id, newDisplayName, description);
    }
}

// Edit role
function editRole(id, displayName, description) {
    const newDisplayName = prompt('Enter new display name:', displayName);
    if (newDisplayName && newDisplayName !== displayName) {
        updateRole(id, newDisplayName, description);
    }
}

// Update department
async function updateDepartment(id, displayName, description) {
    try {
        const response = await fetch(getBaseUrl() + 'api/departments.php', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: id,
                display_name: displayName,
                description: description
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            loadDepartmentList();
            loadDepartments(); // Refresh main dropdown
            showNotification('Department updated successfully!', 'success');
        } else {
            showNotification(data.message || 'Error updating department', 'error');
        }
    } catch (error) {
        console.error('Error updating department:', error);
        showNotification('Error updating department', 'error');
    }
}

// Update role
async function updateRole(id, displayName, description) {
    try {
        const response = await fetch(getBaseUrl() + 'api/roles.php', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: id,
                display_name: displayName,
                description: description
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            loadRoleList();
            loadRoles(); // Refresh main dropdown
            showNotification('Role updated successfully!', 'success');
        } else {
            showNotification(data.message || 'Error updating role', 'error');
        }
    } catch (error) {
        console.error('Error updating role:', error);
        showNotification('Error updating role', 'error');
    }
}

// Delete department
async function deleteDepartment(id) {
    if (!confirm('Are you sure you want to delete this department?')) {
        return;
    }
    
    try {
        const response = await fetch(getBaseUrl() + 'api/departments.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id })
        });
        
        const data = await response.json();
        
        if (data.success) {
            loadDepartmentList();
            loadDepartments(); // Refresh main dropdown
            showNotification('Department deleted successfully!', 'success');
        } else {
            showNotification(data.message || 'Error deleting department', 'error');
        }
    } catch (error) {
        console.error('Error deleting department:', error);
        showNotification('Error deleting department', 'error');
    }
}

// Delete role
async function deleteRole(id) {
    if (!confirm('Are you sure you want to delete this role?')) {
        return;
    }
    
    try {
        const response = await fetch(getBaseUrl() + 'api/roles.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id })
        });
        
        const data = await response.json();
        
        if (data.success) {
            loadRoleList();
            loadRoles(); // Refresh main dropdown
            showNotification('Role deleted successfully!', 'success');
        } else {
            showNotification(data.message || 'Error deleting role', 'error');
        }
    } catch (error) {
        console.error('Error deleting role:', error);
        showNotification('Error deleting role', 'error');
    }
}

// Show notification
function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 3000);
}
