<?php
/**
 * Development Environment Configuration
 * 
 * Configuration overrides for development environment.
 * 
 * @package Config
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

return [
    'app' => [
        'debug' => true,
        'name' => 'ICGC Finance System (Development)'
    ],

    'database' => [
        'host' => 'localhost',
        'name' => 'icgc_finance_dev',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    ],

    'security' => [
        'session_lifetime' => 7200, // 2 hours for development
        'max_login_attempts' => 10, // More lenient for development
        'lockout_duration' => 300, // 5 minutes
    ],

    'logging' => [
        'level' => 'debug',
        'file' => 'logs/dev.log',
        'audit_enabled' => true
    ],

    'cache' => [
        'enabled' => false, // Disable caching in development
        'ttl' => 60 // Short TTL for testing
    ],

    'email' => [
        'enabled' => false, // Disable emails in development
        'driver' => 'log' // Log emails instead of sending
    ],

    'finance' => [
        'dashboard_cache_ttl' => 0, // No caching for development
        'backup_enabled' => false // Disable backups in development
    ],

    'features' => [
        'audit_logging' => true,
        'advanced_reporting' => true,
        'email_notifications' => false,
        'api_access' => true, // Enable API for testing
        'mobile_app' => true, // Enable mobile features for testing
        'multi_currency' => true, // Enable for testing
        'budget_management' => true, // Enable for testing
        'automated_backups' => false,
        'two_factor_auth' => false,
        'ldap_integration' => false
    ]
];
