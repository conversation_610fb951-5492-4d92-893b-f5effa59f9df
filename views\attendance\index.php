<div class="page-content-centered">
    <!-- Enhanced Header Section with Modern Gradient Background -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8 border border-gray-100 relative">
        <!-- Decorative elements -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
            <div class="absolute -top-10 -right-10 w-40 h-40 bg-green-200 rounded-full opacity-20"></div>
            <div class="absolute top-20 -left-10 w-24 h-24 bg-blue-200 rounded-full opacity-20"></div>
            <div class="absolute -bottom-10 right-40 w-32 h-32 bg-yellow-200 rounded-full opacity-20"></div>
        </div>

        <div class="bg-gradient-to-r from-green-600 via-green-500 to-teal-500 p-6 text-white relative">
            <div class="flex flex-row justify-between items-center relative z-10">
                <div>
                    <div class="flex items-center mb-2">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3 shadow-md">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                        <h1 class="text-2xl font-bold">Attendance Dashboard</h1>
                    </div>
                    <p class="text-sm opacity-90 ml-13 pl-0.5">View and manage church attendance records</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance/bulk" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2.5 px-5 rounded-lg flex items-center justify-center transition-all duration-300 text-sm shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                        <i class="fas fa-users-cog mr-2"></i> Mark Attendance
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/qr" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2.5 px-5 rounded-lg flex items-center justify-center transition-all duration-300 text-sm shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                        <i class="fas fa-qrcode mr-2"></i> QR Attendance
                    </a>
                </div>
            </div>

            <!-- Floating decorative elements -->
            <div class="absolute top-6 right-20 animate-pulse opacity-30">
                <i class="fas fa-church text-2xl"></i>
            </div>
            <div class="absolute bottom-6 right-40 animate-pulse delay-300 opacity-30">
                <i class="fas fa-clipboard-check text-xl"></i>
            </div>
        </div>

        <div class="p-4 bg-white border-b border-gray-100">
            <div class="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
                <!-- Quick Links -->
                <div class="flex flex-wrap gap-2">
                    <a href="<?php echo BASE_URL; ?>attendance/view-by-date?date=<?php echo date('Y-m-d'); ?>" class="bg-blue-50 hover:bg-blue-100 text-blue-700 py-2 px-3 rounded-lg flex items-center transition-all duration-300 text-xs border border-blue-100 shadow-sm hover:shadow font-medium">
                        <i class="fas fa-calendar-day mr-1.5"></i> Today's Records
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/view-all" class="bg-gray-50 hover:bg-gray-100 text-gray-700 py-2 px-3 rounded-lg flex items-center transition-all duration-300 text-xs border border-gray-100 shadow-sm hover:shadow font-medium">
                        <i class="fas fa-list mr-1.5"></i> All Records
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/qr" class="bg-purple-50 hover:bg-purple-100 text-purple-700 py-2 px-3 rounded-lg flex items-center transition-all duration-300 text-xs border border-purple-100 shadow-sm hover:shadow font-medium">
                        <i class="fas fa-qrcode mr-1.5"></i> QR Attendance
                    </a>
                </div>

                <!-- Advanced Features -->
                <div class="flex flex-wrap gap-2">
                    <a href="<?php echo BASE_URL; ?>attendance/weekly-reports" class="bg-green-50 hover:bg-green-100 text-green-700 py-2 px-3 rounded-lg flex items-center transition-all duration-300 text-xs border border-green-100 shadow-sm hover:shadow font-medium">
                        <i class="fas fa-chart-bar mr-1.5"></i> Weekly Reports
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/patterns" class="bg-purple-50 hover:bg-purple-100 text-purple-700 py-2 px-3 rounded-lg flex items-center transition-all duration-300 text-xs border border-purple-100 shadow-sm hover:shadow font-medium">
                        <i class="fas fa-user-check mr-1.5"></i> Attendance Patterns
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/comparative-analysis" class="bg-blue-50 hover:bg-blue-100 text-blue-700 py-2 px-3 rounded-lg flex items-center transition-all duration-300 text-xs border border-blue-100 shadow-sm hover:shadow font-medium">
                        <i class="fas fa-chart-line mr-1.5"></i> Compare Periods
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/goals" class="bg-teal-50 hover:bg-teal-100 text-teal-700 py-2 px-3 rounded-lg flex items-center transition-all duration-300 text-xs border border-teal-100 shadow-sm hover:shadow font-medium">
                        <i class="fas fa-bullseye mr-1.5"></i> Goals
                    </a>
                    <a href="<?php echo BASE_URL; ?>attendance/reminders" class="bg-red-50 hover:bg-red-100 text-red-700 py-2 px-3 rounded-lg flex items-center transition-all duration-300 text-xs border border-red-100 shadow-sm hover:shadow font-medium">
                        <i class="fas fa-bell mr-1.5"></i> Reminders
                    </a>
                </div>
            </div>

            <div class="mt-3 text-xs text-gray-600 bg-gray-50 py-1.5 px-3 rounded-full border border-gray-200 shadow-sm font-medium flex items-center w-fit">
                <i class="fas fa-info-circle mr-1.5 text-blue-500"></i> Showing data for the most recent service
            </div>
        </div>
    </div>

    <!-- Service and Date Info - Enhanced Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8 border border-gray-100 transform transition-all duration-300 hover:shadow-lg">
        <div class="flex items-center p-5 border-b border-gray-100 bg-gradient-to-r from-blue-100 to-indigo-100 relative">
            <!-- Decorative elements -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-blue-200 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>

            <div class="p-3.5 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white mr-4 shadow-md relative z-10">
                <i class="fas fa-church text-lg"></i>
            </div>
            <div class="flex-1 relative z-10">
                <h2 class="text-xl font-bold text-gray-800"><?php echo isset($service['name']) ? $service['name'] : 'Latest Service'; ?></h2>
                <div class="flex flex-wrap items-center mt-1.5 text-gray-600">
                    <div class="flex items-center bg-white bg-opacity-60 px-2.5 py-1 rounded-full shadow-sm mr-2 mb-1">
                        <i class="fas fa-calendar-alt mr-1.5 text-blue-500"></i>
                        <span class="text-sm font-medium"><?php echo format_date($date); ?></span>
                    </div>

                    <?php if (isset($service['day_of_week']) && isset($service['time'])) : ?>
                    <div class="flex items-center bg-white bg-opacity-60 px-2.5 py-1 rounded-full shadow-sm mb-1">
                        <i class="fas fa-clock mr-1.5 text-blue-500"></i>
                        <span class="text-sm font-medium"><?php echo ucfirst($service['day_of_week']); ?> - <?php echo date('h:i A', strtotime($service['time'])); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="flex space-x-2 relative z-10">
                <a href="<?php echo BASE_URL; ?>attendance/view-by-date?date=<?php echo $date; ?>&service_id=<?php echo $service_id; ?>" class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-2 px-4 rounded-lg flex items-center text-xs transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <i class="fas fa-list-alt mr-1.5"></i> Details
                </a>
                <a href="<?php echo BASE_URL; ?>attendance/bulk?date=<?php echo $date; ?>&service_id=<?php echo $service_id; ?>" class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-2 px-4 rounded-lg flex items-center text-xs transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <i class="fas fa-edit mr-1.5"></i> Edit
                </a>
                <?php if (isset($service['name']) && $service['name'] == 'Sunday Morning Service'): ?>
                <a href="<?php echo BASE_URL; ?>attendance/bulk" class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white py-2 px-4 rounded-lg flex items-center text-xs transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <i class="fas fa-user-check mr-1.5"></i> Mark Attendance
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Attendance Summary - Enhanced Design with Beautiful Cards -->
    <div class="bg-white rounded-xl shadow-md mb-8 border border-gray-100 overflow-hidden">
        <div class="flex items-center justify-between p-5 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100">
            <div class="flex items-center">
                <div class="p-2.5 rounded-full bg-gradient-to-br from-green-500 to-teal-500 text-white mr-3 shadow-md">
                    <i class="fas fa-chart-pie text-lg"></i>
                </div>
                <h2 class="text-lg font-bold text-gray-800">Attendance Summary</h2>
            </div>
            <div class="text-xs bg-white px-3 py-1.5 rounded-full shadow-sm border border-gray-200 font-medium">
                <span class="font-bold text-green-600"><?php echo $total_marked; ?></span> members marked out of <span class="font-bold text-blue-600"><?php echo $total_active_members; ?></span> active members
            </div>
        </div>

        <!-- Overall Attendance Stats - Enhanced Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-6">
            <!-- Total Marked -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-100 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105 group">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-xs text-gray-500 uppercase tracking-wider font-medium">Total Marked</p>
                        <h3 class="text-2xl font-bold text-gray-800"><?php echo $total_marked; ?></h3>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="w-full bg-white h-2 rounded-full shadow-inner overflow-hidden">
                        <div class="h-full bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full" style="width: <?php echo $coverage_rate; ?>%"></div>
                    </div>
                    <span class="ml-3 text-xs font-bold px-2 py-1 rounded-full bg-blue-100 text-blue-700 shadow-sm">
                        <?php echo $coverage_rate; ?>%
                    </span>
                </div>
            </div>

            <!-- Present -->
            <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-100 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105 group">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-xs text-gray-500 uppercase tracking-wider font-medium">Present</p>
                        <h3 class="text-2xl font-bold text-gray-800"><?php echo $present_count; ?></h3>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="w-full bg-white h-2 rounded-full shadow-inner overflow-hidden">
                        <div class="h-full bg-gradient-to-r from-green-400 to-emerald-500 rounded-full" style="width: <?php echo $total_marked > 0 ? round($present_count / $total_marked * 100) : 0; ?>%"></div>
                    </div>
                    <span class="ml-3 text-xs font-bold px-2 py-1 rounded-full bg-green-100 text-green-700 shadow-sm">
                        <?php echo $total_marked > 0 ? round($present_count / $total_marked * 100) : 0; ?>%
                    </span>
                </div>
            </div>

            <!-- Absent -->
            <div class="bg-gradient-to-br from-red-50 to-pink-50 rounded-xl p-5 border border-red-100 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105 group">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-gradient-to-r from-red-500 to-pink-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                        <i class="fas fa-times-circle text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-xs text-gray-500 uppercase tracking-wider font-medium">Absent</p>
                        <h3 class="text-2xl font-bold text-gray-800"><?php echo $absent_count; ?></h3>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="w-full bg-white h-2 rounded-full shadow-inner overflow-hidden">
                        <div class="h-full bg-gradient-to-r from-red-400 to-pink-500 rounded-full" style="width: <?php echo $total_marked > 0 ? round($absent_count / $total_marked * 100) : 0; ?>%"></div>
                    </div>
                    <span class="ml-3 text-xs font-bold px-2 py-1 rounded-full bg-red-100 text-red-700 shadow-sm">
                        <?php echo $total_marked > 0 ? round($absent_count / $total_marked * 100) : 0; ?>%
                    </span>
                </div>
            </div>

            <!-- Late -->
            <div class="bg-gradient-to-br from-yellow-50 to-amber-50 rounded-xl p-5 border border-yellow-100 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-105 group">
                <div class="flex items-center mb-4">
                    <div class="w-14 h-14 rounded-full bg-gradient-to-r from-yellow-500 to-amber-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-xs text-gray-500 uppercase tracking-wider font-medium">Late</p>
                        <h3 class="text-2xl font-bold text-gray-800"><?php echo $late_count; ?></h3>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="w-full bg-white h-2 rounded-full shadow-inner overflow-hidden">
                        <div class="h-full bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full" style="width: <?php echo $total_marked > 0 ? round($late_count / $total_marked * 100) : 0; ?>%"></div>
                    </div>
                    <span class="ml-3 text-xs font-bold px-2 py-1 rounded-full bg-yellow-100 text-yellow-700 shadow-sm">
                        <?php echo $total_marked > 0 ? round($late_count / $total_marked * 100) : 0; ?>%
                    </span>
                </div>
            </div>
        </div>

        <!-- Gender-based Attendance Stats - Enhanced Design -->
        <div class="p-6 border-t border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100">
            <div class="flex items-center justify-between mb-5">
                <div class="flex items-center">
                    <div class="p-2.5 rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 text-white mr-3 shadow-md">
                        <i class="fas fa-venus-mars text-lg"></i>
                    </div>
                    <h3 class="text-base font-bold text-gray-800">Gender-based Attendance</h3>
                </div>
                <div class="text-xs bg-white px-3 py-1.5 rounded-full shadow-sm border border-gray-200 font-medium">
                    <span class="text-blue-600 font-bold"><?php echo $gender_stats['male']['total']; ?></span> males /
                    <span class="text-pink-600 font-bold"><?php echo $gender_stats['female']['total']; ?></span> females
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Male Attendance - Enhanced Card -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-100 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-102 group">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center mr-3 shadow-md group-hover:shadow-lg transition-all duration-300">
                                <i class="fas fa-male text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-base font-bold text-gray-800">Male Attendance</h4>
                                <p class="text-xs text-gray-500 mt-0.5">Attendance statistics for male members</p>
                            </div>
                        </div>
                        <div class="text-sm bg-white px-3 py-1.5 rounded-full border border-blue-200 text-blue-700 shadow-sm font-bold">
                            <?php echo $gender_stats['male']['total']; ?>
                        </div>
                    </div>

                    <!-- Progress bars instead of boxes -->
                    <div class="space-y-4">
                        <!-- Present -->
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-1.5"></i>
                                    <span class="text-xs font-medium text-gray-700">Present</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm font-bold text-green-700 mr-1"><?php echo $gender_stats['male']['present']; ?></span>
                                    <span class="text-xs text-green-600 bg-green-100 px-1.5 py-0.5 rounded-full">
                                        <?php echo $gender_stats['male']['total'] > 0 ? round($gender_stats['male']['present'] / $gender_stats['male']['total'] * 100) : 0; ?>%
                                    </span>
                                </div>
                            </div>
                            <div class="w-full bg-white h-2.5 rounded-full shadow-inner overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-green-400 to-green-500 rounded-full" style="width: <?php echo $gender_stats['male']['total'] > 0 ? round($gender_stats['male']['present'] / $gender_stats['male']['total'] * 100) : 0; ?>%"></div>
                            </div>
                        </div>

                        <!-- Absent -->
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <div class="flex items-center">
                                    <i class="fas fa-times-circle text-red-500 mr-1.5"></i>
                                    <span class="text-xs font-medium text-gray-700">Absent</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm font-bold text-red-700 mr-1"><?php echo $gender_stats['male']['absent']; ?></span>
                                    <span class="text-xs text-red-600 bg-red-100 px-1.5 py-0.5 rounded-full">
                                        <?php echo $gender_stats['male']['total'] > 0 ? round($gender_stats['male']['absent'] / $gender_stats['male']['total'] * 100) : 0; ?>%
                                    </span>
                                </div>
                            </div>
                            <div class="w-full bg-white h-2.5 rounded-full shadow-inner overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-red-400 to-red-500 rounded-full" style="width: <?php echo $gender_stats['male']['total'] > 0 ? round($gender_stats['male']['absent'] / $gender_stats['male']['total'] * 100) : 0; ?>%"></div>
                            </div>
                        </div>

                        <!-- Late -->
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <div class="flex items-center">
                                    <i class="fas fa-clock text-yellow-500 mr-1.5"></i>
                                    <span class="text-xs font-medium text-gray-700">Late</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm font-bold text-yellow-700 mr-1"><?php echo $gender_stats['male']['late']; ?></span>
                                    <span class="text-xs text-yellow-600 bg-yellow-100 px-1.5 py-0.5 rounded-full">
                                        <?php echo $gender_stats['male']['total'] > 0 ? round($gender_stats['male']['late'] / $gender_stats['male']['total'] * 100) : 0; ?>%
                                    </span>
                                </div>
                            </div>
                            <div class="w-full bg-white h-2.5 rounded-full shadow-inner overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full" style="width: <?php echo $gender_stats['male']['total'] > 0 ? round($gender_stats['male']['late'] / $gender_stats['male']['total'] * 100) : 0; ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Female Attendance - Enhanced Card -->
                <div class="bg-gradient-to-br from-pink-50 to-purple-50 rounded-xl p-5 border border-pink-100 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-102 group">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-purple-600 flex items-center justify-center mr-3 shadow-md group-hover:shadow-lg transition-all duration-300">
                                <i class="fas fa-female text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-base font-bold text-gray-800">Female Attendance</h4>
                                <p class="text-xs text-gray-500 mt-0.5">Attendance statistics for female members</p>
                            </div>
                        </div>
                        <div class="text-sm bg-white px-3 py-1.5 rounded-full border border-pink-200 text-pink-700 shadow-sm font-bold">
                            <?php echo $gender_stats['female']['total']; ?>
                        </div>
                    </div>

                    <!-- Progress bars instead of boxes -->
                    <div class="space-y-4">
                        <!-- Present -->
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-1.5"></i>
                                    <span class="text-xs font-medium text-gray-700">Present</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm font-bold text-green-700 mr-1"><?php echo $gender_stats['female']['present']; ?></span>
                                    <span class="text-xs text-green-600 bg-green-100 px-1.5 py-0.5 rounded-full">
                                        <?php echo $gender_stats['female']['total'] > 0 ? round($gender_stats['female']['present'] / $gender_stats['female']['total'] * 100) : 0; ?>%
                                    </span>
                                </div>
                            </div>
                            <div class="w-full bg-white h-2.5 rounded-full shadow-inner overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-green-400 to-green-500 rounded-full" style="width: <?php echo $gender_stats['female']['total'] > 0 ? round($gender_stats['female']['present'] / $gender_stats['female']['total'] * 100) : 0; ?>%"></div>
                            </div>
                        </div>

                        <!-- Absent -->
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <div class="flex items-center">
                                    <i class="fas fa-times-circle text-red-500 mr-1.5"></i>
                                    <span class="text-xs font-medium text-gray-700">Absent</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm font-bold text-red-700 mr-1"><?php echo $gender_stats['female']['absent']; ?></span>
                                    <span class="text-xs text-red-600 bg-red-100 px-1.5 py-0.5 rounded-full">
                                        <?php echo $gender_stats['female']['total'] > 0 ? round($gender_stats['female']['absent'] / $gender_stats['female']['total'] * 100) : 0; ?>%
                                    </span>
                                </div>
                            </div>
                            <div class="w-full bg-white h-2.5 rounded-full shadow-inner overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-red-400 to-red-500 rounded-full" style="width: <?php echo $gender_stats['female']['total'] > 0 ? round($gender_stats['female']['absent'] / $gender_stats['female']['total'] * 100) : 0; ?>%"></div>
                            </div>
                        </div>

                        <!-- Late -->
                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <div class="flex items-center">
                                    <i class="fas fa-clock text-yellow-500 mr-1.5"></i>
                                    <span class="text-xs font-medium text-gray-700">Late</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm font-bold text-yellow-700 mr-1"><?php echo $gender_stats['female']['late']; ?></span>
                                    <span class="text-xs text-yellow-600 bg-yellow-100 px-1.5 py-0.5 rounded-full">
                                        <?php echo $gender_stats['female']['total'] > 0 ? round($gender_stats['female']['late'] / $gender_stats['female']['total'] * 100) : 0; ?>%
                                    </span>
                                </div>
                            </div>
                            <div class="w-full bg-white h-2.5 rounded-full shadow-inner overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full" style="width: <?php echo $gender_stats['female']['total'] > 0 ? round($gender_stats['female']['late'] / $gender_stats['female']['total'] * 100) : 0; ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Statistics - Enhanced Design with Cards -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8 border border-gray-100">
        <div class="flex items-center justify-between p-5 border-b border-gray-100 bg-gradient-to-r from-purple-100 to-indigo-100 relative">
            <!-- Decorative elements -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-purple-200 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>

            <div class="flex items-center relative z-10">
                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 text-white flex items-center justify-center mr-3 shadow-md">
                    <i class="fas fa-sitemap text-lg"></i>
                </div>
                <h2 class="text-lg font-bold text-gray-800">Department Attendance</h2>
            </div>
            <?php if (!empty($department_stats) && count($department_stats) > 5) : ?>
                <a href="<?php echo BASE_URL; ?>attendance/summary?date=<?php echo $date; ?>&service_id=<?php echo $service_id; ?>" class="text-xs bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white py-2 px-4 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5 relative z-10">
                    <i class="fas fa-chart-bar mr-1.5"></i> View All Departments
                </a>
            <?php endif; ?>
        </div>

        <div class="p-6">
            <?php if (empty($department_stats)) : ?>
                <div class="text-center py-8 text-gray-500">
                    <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-4 shadow-md">
                        <i class="fas fa-chart-pie text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-base font-medium">No department statistics available</p>
                    <p class="text-sm text-gray-400 mt-1">Statistics will appear once attendance is marked</p>
                </div>
            <?php else : ?>
                <!-- Department Cards Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
                    <?php $counter = 0; ?>
                    <?php foreach ($department_stats as $dept) : ?>
                        <?php if ($counter++ < 6) : ?>
                            <?php
                                // Determine card color based on attendance rate
                                $cardColorClass = $dept['rate'] >= 70 ? 'from-green-50 to-emerald-50 border-green-100' :
                                                ($dept['rate'] >= 50 ? 'from-yellow-50 to-amber-50 border-yellow-100' :
                                                'from-red-50 to-pink-50 border-red-100');

                                $progressColorClass = $dept['rate'] >= 70 ? 'from-green-400 to-emerald-500' :
                                                    ($dept['rate'] >= 50 ? 'from-yellow-400 to-amber-500' :
                                                    'from-red-400 to-pink-500');

                                $iconColorClass = $dept['rate'] >= 70 ? 'from-green-500 to-emerald-600' :
                                                ($dept['rate'] >= 50 ? 'from-yellow-500 to-amber-600' :
                                                'from-red-500 to-pink-600');
                            ?>
                            <div class="bg-gradient-to-br <?php echo $cardColorClass; ?> rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-102 group">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 rounded-full bg-gradient-to-r <?php echo $iconColorClass; ?> flex items-center justify-center mr-3 shadow-md group-hover:shadow-lg transition-all duration-300">
                                            <i class="fas fa-users text-white"></i>
                                        </div>
                                        <div>
                                            <h4 class="text-base font-bold text-gray-800"><?php echo $dept['name']; ?></h4>
                                            <p class="text-xs text-gray-500 mt-0.5">Total: <span class="font-medium"><?php echo $dept['total']; ?></span> members</p>
                                        </div>
                                    </div>
                                    <div class="text-lg font-bold <?php echo $dept['rate'] >= 70 ? 'text-green-600' : ($dept['rate'] >= 50 ? 'text-yellow-600' : 'text-red-600'); ?>">
                                        <?php echo $dept['rate']; ?>%
                                    </div>
                                </div>

                                <!-- Attendance Progress Bar -->
                                <div class="mb-4">
                                    <div class="w-full bg-white h-2.5 rounded-full shadow-inner overflow-hidden">
                                        <div class="h-full bg-gradient-to-r <?php echo $progressColorClass; ?> rounded-full" style="width: <?php echo $dept['rate']; ?>%"></div>
                                    </div>
                                </div>

                                <!-- Attendance Stats -->
                                <div class="grid grid-cols-3 gap-2">
                                    <div class="bg-white bg-opacity-70 rounded-lg p-2 text-center shadow-sm">
                                        <div class="text-xs text-gray-500 mb-1">Present</div>
                                        <div class="text-sm font-bold text-green-600"><?php echo $dept['present']; ?></div>
                                    </div>
                                    <div class="bg-white bg-opacity-70 rounded-lg p-2 text-center shadow-sm">
                                        <div class="text-xs text-gray-500 mb-1">Absent</div>
                                        <div class="text-sm font-bold text-red-600"><?php echo $dept['absent']; ?></div>
                                    </div>
                                    <div class="bg-white bg-opacity-70 rounded-lg p-2 text-center shadow-sm">
                                        <div class="text-xs text-gray-500 mb-1">Late</div>
                                        <div class="text-sm font-bold text-yellow-600"><?php echo $dept['late']; ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>

                <?php if (count($department_stats) > 6) : ?>
                <div class="mt-4 text-center">
                    <a href="<?php echo BASE_URL; ?>attendance/summary?date=<?php echo $date; ?>&service_id=<?php echo $service_id; ?>" class="inline-flex items-center text-sm text-purple-600 hover:text-purple-800 font-medium">
                        <span>View all <?php echo count($department_stats); ?> departments</span>
                        <i class="fas fa-arrow-right ml-1.5 text-xs"></i>
                    </a>
                </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Attendance Activity - New Section -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8 border border-gray-100">
        <div class="flex items-center justify-between p-5 border-b border-gray-100 bg-gradient-to-r from-blue-100 to-cyan-100 relative">
            <!-- Decorative elements -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-blue-200 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>

            <div class="flex items-center relative z-10">
                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-cyan-600 text-white flex items-center justify-center mr-3 shadow-md">
                    <i class="fas fa-history text-lg"></i>
                </div>
                <h2 class="text-lg font-bold text-gray-800">Recent Attendance Activity</h2>
            </div>
            <a href="<?php echo BASE_URL; ?>attendance/view-all" class="text-xs bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white py-2 px-4 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5 relative z-10">
                <i class="fas fa-list mr-1.5"></i> View All Records
            </a>
        </div>

        <div class="p-6">
            <?php if (empty($recent_services)) : ?>
                <div class="text-center py-8 text-gray-500">
                    <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-4 shadow-md">
                        <i class="fas fa-calendar-times text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-base font-medium">No recent attendance records</p>
                    <p class="text-sm text-gray-400 mt-1">Start marking attendance to see activity here</p>
                </div>
            <?php else : ?>
                <!-- Timeline of Recent Attendance -->
                <div class="relative">
                    <!-- Vertical Timeline Line -->
                    <div class="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                    <div class="space-y-6">
                        <?php foreach ($recent_services as $index => $service) : ?>
                            <div class="flex items-start relative">
                                <!-- Timeline Dot -->
                                <div class="absolute left-5 w-5 h-5 rounded-full bg-gradient-to-r from-blue-500 to-cyan-600 shadow-md transform -translate-x-1/2 flex items-center justify-center z-10">
                                    <div class="w-2 h-2 rounded-full bg-white"></div>
                                </div>

                                <!-- Timeline Content -->
                                <div class="ml-10 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl p-4 border border-blue-100 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-102 w-full">
                                    <div class="flex justify-between items-start mb-2">
                                        <div>
                                            <h4 class="text-base font-bold text-gray-800"><?php echo $service['name']; ?></h4>
                                            <div class="flex items-center text-sm text-gray-500 mt-0.5">
                                                <i class="fas fa-calendar-day mr-1.5 text-blue-500"></i>
                                                <span><?php echo format_date($service['date']); ?></span>
                                                <span class="mx-1.5 text-gray-300">|</span>
                                                <i class="fas fa-clock mr-1.5 text-blue-500"></i>
                                                <span><?php echo ucfirst($service['day_of_week']); ?> - <?php echo date('h:i A', strtotime($service['time'])); ?></span>
                                            </div>
                                        </div>
                                        <a href="<?php echo BASE_URL; ?>attendance/view-by-date?date=<?php echo $service['date']; ?>&service_id=<?php echo $service['id']; ?>" class="text-xs bg-white hover:bg-blue-50 text-blue-600 py-1 px-2 rounded-lg flex items-center border border-blue-200 shadow-sm transition-all duration-200">
                                            <i class="fas fa-eye mr-1"></i> View
                                        </a>
                                    </div>

                                    <!-- Attendance Stats -->
                                    <div class="grid grid-cols-4 gap-2 mt-3">
                                        <div class="bg-white rounded-lg p-2 text-center shadow-sm">
                                            <div class="text-xs text-gray-500 mb-1">Total</div>
                                            <div class="text-sm font-bold text-blue-600"><?php echo $service['total']; ?></div>
                                        </div>
                                        <div class="bg-white rounded-lg p-2 text-center shadow-sm">
                                            <div class="text-xs text-gray-500 mb-1">Present</div>
                                            <div class="text-sm font-bold text-green-600"><?php echo $service['present']; ?></div>
                                        </div>
                                        <div class="bg-white rounded-lg p-2 text-center shadow-sm">
                                            <div class="text-xs text-gray-500 mb-1">Absent</div>
                                            <div class="text-sm font-bold text-red-600"><?php echo $service['absent']; ?></div>
                                        </div>
                                        <div class="bg-white rounded-lg p-2 text-center shadow-sm">
                                            <div class="text-xs text-gray-500 mb-1">Late</div>
                                            <div class="text-sm font-bold text-yellow-600"><?php echo $service['late']; ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal - Modern Design -->
<div id="deleteModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 backdrop-blur-sm">
    <div class="relative top-20 mx-auto p-4 border border-gray-200 w-80 shadow-lg rounded-lg bg-white">
        <div class="absolute top-2 right-2">
            <button id="closeModal" class="text-gray-400 hover:text-gray-500 focus:outline-none p-1">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mt-2 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
            </div>
            <h3 class="text-base font-semibold text-gray-900 mt-3">Delete Attendance Record</h3>
            <div class="mt-2 px-4 py-2">
                <p class="text-xs text-gray-500">Are you sure you want to delete this attendance record? This action cannot be undone.</p>
            </div>
            <div class="flex justify-center mt-3 space-x-2">
                <button id="cancelDelete" class="bg-gray-100 hover:bg-gray-200 px-4 py-1.5 rounded text-gray-700 text-xs transition-colors duration-200">
                    <i class="fas fa-times mr-1"></i> Cancel
                </button>
                <a id="confirmDelete" href="#" class="bg-red-500 hover:bg-red-600 px-4 py-1.5 rounded text-white text-xs transition-colors duration-200">
                    <i class="fas fa-trash-alt mr-1"></i> Delete
                </a>
            </div>
        </div>
    </div>
</div>

<script>

    // Delete confirmation
    function confirmDelete(id) {
        const modal = document.getElementById('deleteModal');
        const confirmBtn = document.getElementById('confirmDelete');

        modal.classList.remove('hidden');
        confirmBtn.href = '<?php echo BASE_URL; ?>attendance/delete?id=' + id;

        // Close modal when cancel button is clicked
        document.getElementById('cancelDelete').addEventListener('click', function() {
            modal.classList.add('hidden');
        });

        // Close modal when X button is clicked
        document.getElementById('closeModal').addEventListener('click', function() {
            modal.classList.add('hidden');
        });

        // Close modal when clicking outside the modal
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });
    }

    // Add custom scrollbar styles
    document.head.insertAdjacentHTML('beforeend', `
        <style>
            .custom-scrollbar::-webkit-scrollbar {
                width: 6px;
            }
            .custom-scrollbar::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 10px;
            }
            .custom-scrollbar::-webkit-scrollbar-thumb {
                background: #d1d5db;
                border-radius: 10px;
            }
            .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                background: #9ca3af;
            }
        </style>
    `);
</script>
