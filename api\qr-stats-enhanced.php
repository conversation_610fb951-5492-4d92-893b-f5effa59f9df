<?php
/**
 * Enhanced QR Stats API for Full-Page Dashboard
 * Provides detailed statistics and member data for real-time attendance dashboard
 */

// Set headers for JSON response
header('Content-Type: application/json');

// CRITICAL SECURITY FIX: Restrict CORS to application domain only
// The wildcard '*' is dangerous and allows any website to access this API
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false || 
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
    // If origin is not allowed, we don't set the CORS header
    // This will cause the browser to block the request
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET');

// Security headers
header('X-Frame-Options: SAMEORIGIN');
header('X-XSS-Protection: 1; mode=block');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: strict-origin-when-cross-origin');

// Include bootstrap to ensure proper initialization
require_once dirname(__DIR__) . '/bootstrap.php';

try {
    // Check if token is provided
    if (!isset($_GET['token']) || empty($_GET['token'])) {
        echo json_encode(['success' => false, 'error' => 'QR session token is required']);
        exit;
    }

    $token = sanitize($_GET['token']);

    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();

    // Get QR session by token
    $query = "SELECT * FROM attendance_qr_sessions WHERE token = :token LIMIT 1";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':token', $token);
    $stmt->execute();
    $qr_session = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$qr_session) {
        echo json_encode(['success' => false, 'error' => 'Invalid QR session token']);
        exit;
    }

    // Get session details
    $attendance_date = $qr_session['attendance_date'];
    $service_id = $qr_session['service_id'];

    // Get comprehensive attendance statistics
    $stats = getAttendanceStatistics($conn, $attendance_date, $service_id);
    
    // Get detailed member list
    $members = getAttendanceMembersList($conn, $attendance_date, $service_id);

    // Return comprehensive response
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'members' => $members,
        'session_info' => [
            'service_id' => $service_id,
            'attendance_date' => $attendance_date,
            'expires_at' => $qr_session['expires_at']
        ]
    ]);

} catch (Exception $e) {
    error_log("Enhanced QR Stats API Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load attendance data: ' . $e->getMessage()
    ]);
}

/**
 * Get comprehensive attendance statistics
 */
function getAttendanceStatistics($conn, $attendance_date, $service_id) {
    // Get total counts by status
    $status_query = "SELECT 
                        status,
                        COUNT(*) as count
                     FROM attendance a
                     JOIN members m ON a.member_id = m.id
                     WHERE a.attendance_date = :date
                     AND a.service_id = :service_id
                     GROUP BY status";
    
    $stmt = $conn->prepare($status_query);
    $stmt->bindParam(':date', $attendance_date);
    $stmt->bindParam(':service_id', $service_id);
    $stmt->execute();
    $status_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get gender breakdown
    $gender_query = "SELECT 
                        m.gender,
                        a.status,
                        COUNT(*) as count
                     FROM attendance a
                     JOIN members m ON a.member_id = m.id
                     WHERE a.attendance_date = :date
                     AND a.service_id = :service_id
                     GROUP BY m.gender, a.status";
    
    $stmt = $conn->prepare($gender_query);
    $stmt->bindParam(':date', $attendance_date);
    $stmt->bindParam(':service_id', $service_id);
    $stmt->execute();
    $gender_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get department breakdown
    $dept_query = "SELECT 
                      m.department,
                      COUNT(*) as count
                   FROM attendance a
                   JOIN members m ON a.member_id = m.id
                   WHERE a.attendance_date = :date
                   AND a.service_id = :service_id
                   AND a.status IN ('present', 'late')
                   GROUP BY m.department
                   ORDER BY count DESC";
    
    $stmt = $conn->prepare($dept_query);
    $stmt->bindParam(':date', $attendance_date);
    $stmt->bindParam(':service_id', $service_id);
    $stmt->execute();
    $dept_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Process the data
    $stats = [
        'total_count' => 0,
        'present_count' => 0,
        'late_count' => 0,
        'absent_count' => 0,
        'male_count' => 0,
        'female_count' => 0,
        'male_present' => 0,
        'male_late' => 0,
        'female_present' => 0,
        'female_late' => 0,
        'departments' => []
    ];

    // Process status counts
    foreach ($status_counts as $status) {
        switch ($status['status']) {
            case 'present':
                $stats['present_count'] = (int)$status['count'];
                break;
            case 'late':
                $stats['late_count'] = (int)$status['count'];
                break;
            case 'absent':
                $stats['absent_count'] = (int)$status['count'];
                break;
        }
    }

    // Calculate total present (present + late)
    $stats['total_count'] = $stats['present_count'] + $stats['late_count'];

    // Process gender counts
    foreach ($gender_counts as $gender) {
        $count = (int)$gender['count'];
        $genderType = $gender['gender'];
        $status = $gender['status'];

        if ($status === 'present' || $status === 'late') {
            if ($genderType === 'male') {
                $stats['male_count'] += $count;
                if ($status === 'present') {
                    $stats['male_present'] = $count;
                } else {
                    $stats['male_late'] = $count;
                }
            } elseif ($genderType === 'female') {
                $stats['female_count'] += $count;
                if ($status === 'present') {
                    $stats['female_present'] = $count;
                } else {
                    $stats['female_late'] = $count;
                }
            }
        }
    }

    // Process department counts
    foreach ($dept_counts as $dept) {
        $stats['departments'][] = [
            'name' => ucfirst(str_replace('_', ' ', $dept['department'])),
            'count' => (int)$dept['count']
        ];
    }

    return $stats;
}

/**
 * Get detailed members list with attendance
 */
function getAttendanceMembersList($conn, $attendance_date, $service_id) {
    $query = "SELECT 
                m.id,
                m.first_name,
                m.last_name,
                m.phone_number,
                m.gender,
                m.department,
                a.status,
                a.created_at
              FROM attendance a
              JOIN members m ON a.member_id = m.id
              WHERE a.attendance_date = :date
              AND a.service_id = :service_id
              AND a.status IN ('present', 'late')
              ORDER BY a.created_at DESC";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(':date', $attendance_date);
    $stmt->bindParam(':service_id', $service_id);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $members = [];
    foreach ($results as $row) {
        $members[] = [
            'id' => (int)$row['id'],
            'name' => $row['first_name'] . ' ' . $row['last_name'],
            'phone' => $row['phone_number'] ?: 'N/A',
            'gender' => $row['gender'],
            'department' => ucfirst(str_replace('_', ' ', $row['department'])),
            'status' => $row['status'],
            'time' => date('g:i A', strtotime($row['created_at'])),
            'timestamp' => $row['created_at']
        ];
    }

    return $members;
}
?>
