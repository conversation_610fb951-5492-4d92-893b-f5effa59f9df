<?php
/**
 * Generic Category Dashboard View
 * Reusable dashboard for any finance category
 */

// Ensure we have the required data
$analytics = $analytics ?? [];
$memberTracking = $memberTracking ?? [];
$trends = $trends ?? [];
$recentTransactions = $recentTransactions ?? [];
$categoryConfig = $categoryConfig ?? [];
$category = $_GET['category'] ?? '';
$type = $_GET['type'] ?? 'income';

// Get category display information
$categoryLabel = $categoryConfig['label'] ?? ucwords(str_replace('_', ' ', $category));
$categoryIcon = $categoryConfig['icon'] ?? 'fas fa-chart-bar';
$categoryDescription = $categoryConfig['description'] ?? 'Track and analyze ' . strtolower($categoryLabel);

// Determine color scheme based on type
$colorScheme = $type === 'expense' ? 'red' : 'green';
$gradientFrom = $type === 'expense' ? 'from-red-600' : 'from-green-600';
$gradientTo = $type === 'expense' ? 'to-red-700' : 'to-green-700';
$bgGradient = $type === 'expense' ? 'from-red-50' : 'from-green-50';
?>

<div class="min-h-screen bg-gradient-to-br <?php echo $bgGradient; ?> to-blue-50">
    <!-- Header Section -->
    <div class="bg-gradient-to-r <?php echo $gradientFrom . ' ' . $gradientTo; ?> text-white py-8 px-6 rounded-b-3xl shadow-xl mb-8">
        <div class="max-w-7xl mx-auto">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <button onclick="history.back()" class="text-<?php echo $colorScheme; ?>-100 hover:text-white mr-3 transition-colors bg-transparent border-none cursor-pointer">
                            <i class="fas fa-arrow-left text-lg"></i>
                        </button>
                        <h1 class="text-3xl font-bold">
                            <i class="<?php echo $categoryIcon; ?> mr-2"></i>
                            <?php echo htmlspecialchars($categoryLabel); ?> Dashboard
                        </h1>
                    </div>
                    <p class="text-<?php echo $colorScheme; ?>-100 text-lg"><?php echo htmlspecialchars($categoryDescription); ?></p>
                </div>
                <div class="flex gap-3">
                    <?php
                    // Determine the correct tab based on type
                    $tabParam = '';
                    if ($type === 'expense') {
                        $tabParam = '?tab=expenses';
                    } else {
                        // For income, check if it's member payment or general income
                        // Default to general-income for most cases
                        $tabParam = '?tab=general-income';
                    }
                    ?>
                    <a href="<?php echo BASE_URL; ?>finance/add<?php echo $tabParam; ?>" class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-plus mr-2"></i>
                        <span class="hidden sm:inline">Add <?php echo $type === 'expense' ? 'Expense' : 'Income'; ?></span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>finance/enhanced-reports" class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-chart-bar mr-2"></i>
                        <span class="hidden sm:inline">Reports</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 pb-8">
        <!-- Analytics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- This Month Total -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-<?php echo $colorScheme; ?>-400 to-<?php echo $colorScheme; ?>-500 text-white">
                        <i class="<?php echo $categoryIcon; ?> text-xl"></i>
                    </div>
                    <?php if (isset($analytics['amount_growth']) && $analytics['amount_growth'] != 0): ?>
                        <span class="text-sm font-medium px-2 py-1 rounded-full <?php echo $analytics['amount_growth'] > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                            <i class="fas fa-<?php echo $analytics['amount_growth'] > 0 ? 'arrow-up' : 'arrow-down'; ?> mr-1"></i>
                            <?php echo abs($analytics['amount_growth']); ?>%
                        </span>
                    <?php endif; ?>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    GH₵ <?php echo number_format($analytics['current_month']['total_amount'] ?? 0, 2); ?>
                </h3>
                <p class="text-gray-600 text-sm">This Month's Total</p>
                <p class="text-xs text-gray-500 mt-1">
                    <?php echo $analytics['current_month']['total_transactions'] ?? 0; ?> transactions
                </p>
            </div>

            <!-- Average Amount -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 text-white">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    GH₵ <?php echo number_format($analytics['current_month']['average_amount'] ?? 0, 2); ?>
                </h3>
                <p class="text-gray-600 text-sm">Average Amount</p>
                <p class="text-xs text-gray-500 mt-1">This month</p>
            </div>

            <!-- Contributors/Transactions -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-purple-400 to-purple-500 text-white">
                        <i class="fas fa-<?php echo $type === 'income' && isset($analytics['current_month']['unique_contributors']) ? 'users' : 'list'; ?> text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    <?php 
                    if ($type === 'income' && isset($analytics['current_month']['unique_contributors'])) {
                        echo $analytics['current_month']['unique_contributors'];
                    } else {
                        echo $analytics['current_month']['total_transactions'] ?? 0;
                    }
                    ?>
                </h3>
                <p class="text-gray-600 text-sm">
                    <?php echo $type === 'income' && isset($analytics['current_month']['unique_contributors']) ? 'Contributors' : 'Transactions'; ?>
                </p>
                <p class="text-xs text-gray-500 mt-1">This month</p>
            </div>

            <!-- Year to Date -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-orange-400 to-orange-500 text-white">
                        <i class="fas fa-calendar-alt text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    GH₵ <?php echo number_format($analytics['ytd']['ytd_amount'] ?? 0, 2); ?>
                </h3>
                <p class="text-gray-600 text-sm">Year to Date</p>
                <p class="text-xs text-gray-500 mt-1">
                    <?php echo $analytics['ytd']['ytd_transactions'] ?? 0; ?> transactions
                </p>
            </div>
        </div>

        <!-- Charts and Tables Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Trends Chart -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">Monthly Trends</h3>
                    <div class="flex items-center space-x-2">
                        <span class="w-3 h-3 bg-<?php echo $colorScheme; ?>-500 rounded-full"></span>
                        <span class="text-sm text-gray-600"><?php echo ucfirst($type); ?> Amount</span>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="categoryTrendsChart"></canvas>
                </div>
            </div>

            <!-- Top Contributors/Recent Activity -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">
                        <?php echo ($type === 'income' && !empty($memberTracking)) ? 'Top Contributors' : 'Recent Activity'; ?>
                    </h3>
                    <a href="<?php echo BASE_URL; ?>finance/transactions?category=<?php echo urlencode($category); ?>" class="text-<?php echo $colorScheme; ?>-600 hover:text-<?php echo $colorScheme; ?>-700 text-sm font-medium">
                        View All <i class="fas fa-chevron-right ml-1"></i>
                    </a>
                </div>
                <div class="space-y-4">
                    <?php if ($type === 'income' && !empty($memberTracking)): ?>
                        <!-- Member tracking for income categories -->
                        <?php foreach (array_slice($memberTracking, 0, 5) as $index => $member): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-<?php echo $colorScheme; ?>-100 text-<?php echo $colorScheme; ?>-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                                        <?php echo $index + 1; ?>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">
                                            <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?php echo $member['total_payments']; ?> payments
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-<?php echo $colorScheme; ?>-600">
                                        GH₵ <?php echo number_format($member['total_amount'], 2); ?>
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        Avg: GH₵ <?php echo number_format($member['average_amount'], 2); ?>
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php elseif (!empty($recentTransactions)): ?>
                        <!-- Recent transactions for expense categories or when no member tracking -->
                        <?php foreach (array_slice($recentTransactions, 0, 5) as $transaction): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-<?php echo $colorScheme; ?>-100 text-<?php echo $colorScheme; ?>-600 rounded-full flex items-center justify-center text-sm">
                                        <i class="<?php echo $categoryIcon; ?>"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="font-medium text-gray-800">
                                            <?php echo htmlspecialchars($transaction['description'] ?? 'Transaction'); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?php echo format_date($transaction['transaction_date']); ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-<?php echo $type === 'expense' ? 'red' : $colorScheme; ?>-600">
                                        GH₵ <?php echo number_format($transaction['amount'], 2); ?>
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="<?php echo $categoryIcon; ?> text-gray-300 text-4xl mb-3"></i>
                            <p class="text-sm">No <?php echo strtolower($categoryLabel); ?> records found</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Transactions Table -->
        <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-bold text-gray-800">Recent <?php echo htmlspecialchars($categoryLabel); ?> Transactions</h3>
                <a href="<?php echo BASE_URL; ?>finance/transactions?category=<?php echo urlencode($category); ?>" class="text-<?php echo $colorScheme; ?>-600 hover:text-<?php echo $colorScheme; ?>-700 text-sm font-medium">
                    View All <i class="fas fa-chevron-right ml-1"></i>
                </a>
            </div>
            
            <?php if (!empty($recentTransactions)): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr class="bg-gradient-to-r from-gray-50 to-white">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Date</th>
                                <?php if ($type === 'income'): ?>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Member</th>
                                <?php endif; ?>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Description</th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($recentTransactions as $transaction): ?>
                                <tr class="hover:bg-gray-50 transition-all duration-200">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo format_date($transaction['transaction_date']); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($transaction['transaction_date'])); ?></div>
                                    </td>
                                    <?php if ($type === 'income'): ?>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($transaction['member_name'] ?? 'General'); ?>
                                            </div>
                                            <?php if (!empty($transaction['phone_number'])): ?>
                                                <div class="text-xs text-gray-500"><?php echo htmlspecialchars($transaction['phone_number']); ?></div>
                                            <?php endif; ?>
                                        </td>
                                    <?php endif; ?>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="text-sm font-medium text-<?php echo $type === 'expense' ? 'red' : $colorScheme; ?>-600">
                                            GH₵ <?php echo number_format($transaction['amount'], 2); ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="text-sm text-gray-900 max-w-xs truncate">
                                            <?php echo htmlspecialchars($transaction['description'] ?? ucfirst($category) . ' transaction'); ?>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-center">
                                        <div class="flex items-center justify-center space-x-2">
                                            <a href="<?php echo BASE_URL; ?>finance/edit?id=<?php echo $transaction['id']; ?>" 
                                               class="bg-indigo-50 hover:bg-indigo-100 text-indigo-600 hover:text-indigo-900 p-1.5 rounded-full transition-colors duration-200" 
                                               title="Edit Transaction">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-8 text-gray-500">
                    <i class="<?php echo $categoryIcon; ?> text-gray-300 text-4xl mb-3"></i>
                    <p class="text-sm">No <?php echo strtolower($categoryLabel); ?> transactions found</p>
                    <a href="<?php echo BASE_URL; ?>finance/add<?php echo $tabParam; ?>" class="mt-3 inline-block bg-gradient-to-r from-<?php echo $colorScheme; ?>-500 to-<?php echo $colorScheme; ?>-600 hover:from-<?php echo $colorScheme; ?>-600 hover:to-<?php echo $colorScheme; ?>-700 text-white py-2 px-4 rounded-lg text-sm shadow-md hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-plus-circle mr-2"></i> Record First <?php echo ucfirst($type); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Chart.js Script -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Category Trends Chart
    const trendsCtx = document.getElementById('categoryTrendsChart').getContext('2d');
    const trendsData = <?php echo json_encode($trends); ?>;
    const colorScheme = '<?php echo $colorScheme; ?>';
    
    // Set chart colors based on category type
    const chartColor = colorScheme === 'red' ? 'rgb(239, 68, 68)' : 'rgb(34, 197, 94)';
    const chartBgColor = colorScheme === 'red' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(34, 197, 94, 0.1)';
    
    new Chart(trendsCtx, {
        type: 'line',
        data: {
            labels: trendsData.map(item => item.month_label),
            datasets: [{
                label: '<?php echo ucfirst($type); ?> Amount (GH₵)',
                data: trendsData.map(item => parseFloat(item.total_amount)),
                borderColor: chartColor,
                backgroundColor: chartBgColor,
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'GH₵ ' + value.toLocaleString();
                        }
                    }
                }
            },
            elements: {
                point: {
                    radius: 6,
                    hoverRadius: 8
                }
            }
        }
    });
});
</script>
