<?php
/**
 * Centralized Input Sanitization
 * Consolidates all sanitization functionality into a single, consistent implementation
 */

class InputSanitizer {
    
    /**
     * Sanitize string input
     * 
     * @param string|array $input Input to sanitize
     * @return string|array Sanitized input
     */
    public static function sanitizeString($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeString'], $input);
        }
        
        if (!is_string($input)) {
            return $input;
        }
        
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Trim whitespace
        $input = trim($input);
        
        // Convert special characters to HTML entities
        return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
    
    /**
     * Sanitize rich text input (allows some HTML tags)
     * 
     * @param string $input Raw HTML input
     * @param array $allowedTags Allowed HTML tags
     * @return string Sanitized HTML
     */
    public static function sanitizeRichText(string $input, array $allowedTags = ['p', 'br', 'strong', 'em', 'u']): string {
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Convert allowed tags array to string format for strip_tags
        $allowedTagsString = '<' . implode('><', $allowedTags) . '>';
        
        // Strip tags except allowed ones
        $input = strip_tags($input, $allowedTagsString);
        
        // Trim whitespace
        return trim($input);
    }
    
    /**
     * Sanitize numeric amount
     * 
     * @param mixed $input Raw amount input
     * @return float Sanitized amount
     */
    public static function sanitizeAmount($input): float {
        // Remove any non-numeric characters except decimal point
        $cleaned = preg_replace('/[^0-9.]/', '', (string)$input);
        
        // Convert to float and round to 2 decimal places
        return round((float)$cleaned, 2);
    }
    
    /**
     * Sanitize boolean input
     * 
     * @param mixed $input Raw boolean input
     * @return bool Sanitized boolean
     */
    public static function sanitizeBoolean($input): bool {
        if (is_bool($input)) {
            return $input;
        }
        
        if (is_string($input)) {
            $input = strtolower(trim($input));
            return in_array($input, ['1', 'true', 'yes', 'on']);
        }
        
        return (bool)$input;
    }
    
    /**
     * Sanitize email input
     * 
     * @param string $input Email input
     * @return string Sanitized email
     */
    public static function sanitizeEmail(string $input): string {
        return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
    }
    
    /**
     * Sanitize phone number
     * 
     * @param string $input Phone number input
     * @return string Sanitized phone number
     */
    public static function sanitizePhone(string $input): string {
        // Remove all non-numeric characters except + and spaces
        return preg_replace('/[^0-9+\s-]/', '', trim($input));
    }
    
    /**
     * Sanitize integer input
     * 
     * @param mixed $input Integer input
     * @return int Sanitized integer
     */
    public static function sanitizeInt($input): int {
        return (int) filter_var($input, FILTER_SANITIZE_NUMBER_INT);
    }
}

// Backward compatibility function is defined in helpers/functions.php
?>
