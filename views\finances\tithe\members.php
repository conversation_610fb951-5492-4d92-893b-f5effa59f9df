<?php
/**
 * Member Tithe Tracking Page
 * Comprehensive member management with advanced filtering and pagination
 */

// Get current filter values
$currentSearch = htmlspecialchars($_GET['search'] ?? '');
$currentSort = $_GET['sort'] ?? 'total_amount_desc';
$currentActive = $_GET['active'] ?? 'all';
$currentDateRange = $_GET['date_range'] ?? 'all_time';
$currentAmountRange = $_GET['amount_range'] ?? 'all';
?>

<div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-green-600 to-green-700 text-white py-6 px-6 shadow-xl">
        <div class="max-w-7xl mx-auto">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <a href="<?php echo BASE_URL; ?>finance/dashboard/tithe" class="text-green-100 hover:text-white mr-3 transition-colors">
                            <i class="fas fa-arrow-left text-lg"></i>
                        </a>
                        <h1 class="text-2xl font-bold">
                            <i class="fas fa-users mr-2"></i>
                            Member Tithe Tracking
                        </h1>
                    </div>
                    <p class="text-green-100">Comprehensive member tithe management and analytics</p>
                </div>
                <div class="flex gap-3">
                    <a href="<?php echo BASE_URL; ?>finance/tithe/members/export?<?php echo http_build_query($_GET); ?>" 
                       class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-download mr-2"></i>
                        <span class="hidden sm:inline">Export</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>finance/add" 
                       class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-plus mr-2"></i>
                        <span class="hidden sm:inline">Add Tithe</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-green-400 to-green-500 text-white">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    <?php echo number_format($stats['total_members'] ?? 0); ?>
                </h3>
                <p class="text-gray-600 text-sm">Total Members</p>
            </div>

            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 text-white">
                        <i class="fas fa-hand-holding-usd text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    GH₵ <?php echo number_format($stats['total_amount'] ?? 0, 2); ?>
                </h3>
                <p class="text-gray-600 text-sm">Total Amount</p>
            </div>

            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-purple-400 to-purple-500 text-white">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    GH₵ <?php echo number_format($stats['average_payment'] ?? 0, 2); ?>
                </h3>
                <p class="text-gray-600 text-sm">Average Payment</p>
            </div>

            <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-3 rounded-full bg-gradient-to-r from-orange-400 to-orange-500 text-white">
                        <i class="fas fa-receipt text-xl"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1">
                    <?php echo number_format($stats['total_payments'] ?? 0); ?>
                </h3>
                <p class="text-gray-600 text-sm">Total Payments</p>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 mb-8">
            <form method="GET" action="<?php echo BASE_URL; ?>finance/tithe/members" class="space-y-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-800">
                        <i class="fas fa-filter mr-2"></i>Advanced Filters
                    </h3>
                    <button type="button" onclick="clearFilters()" class="text-gray-500 hover:text-gray-700 text-sm">
                        <i class="fas fa-times mr-1"></i>Clear All
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search Members</label>
                        <div class="relative">
                            <input type="text" name="search" value="<?php echo $currentSearch; ?>" 
                                   placeholder="Name, phone, email..." 
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>

                    <!-- Sort -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                        <select name="sort" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="total_amount_desc" <?php echo $currentSort === 'total_amount_desc' ? 'selected' : ''; ?>>Highest Amount</option>
                            <option value="total_amount_asc" <?php echo $currentSort === 'total_amount_asc' ? 'selected' : ''; ?>>Lowest Amount</option>
                            <option value="payments_desc" <?php echo $currentSort === 'payments_desc' ? 'selected' : ''; ?>>Most Payments</option>
                            <option value="recent" <?php echo $currentSort === 'recent' ? 'selected' : ''; ?>>Most Recent</option>
                            <option value="name_asc" <?php echo $currentSort === 'name_asc' ? 'selected' : ''; ?>>Name A-Z</option>
                        </select>
                    </div>

                    <!-- Activity -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Activity</label>
                        <select name="active" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="all" <?php echo $currentActive === 'all' ? 'selected' : ''; ?>>All Members</option>
                            <option value="active" <?php echo $currentActive === 'active' ? 'selected' : ''; ?>>Active (3 months)</option>
                            <option value="inactive" <?php echo $currentActive === 'inactive' ? 'selected' : ''; ?>>Inactive (3+ months)</option>
                        </select>
                    </div>

                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                        <select name="date_range" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="all_time" <?php echo $currentDateRange === 'all_time' ? 'selected' : ''; ?>>All Time</option>
                            <option value="this_month" <?php echo $currentDateRange === 'this_month' ? 'selected' : ''; ?>>This Month</option>
                            <option value="last_3_months" <?php echo $currentDateRange === 'last_3_months' ? 'selected' : ''; ?>>Last 3 Months</option>
                            <option value="this_year" <?php echo $currentDateRange === 'this_year' ? 'selected' : ''; ?>>This Year</option>
                            <option value="last_year" <?php echo $currentDateRange === 'last_year' ? 'selected' : ''; ?>>Last Year</option>
                        </select>
                    </div>

                    <!-- Amount Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Amount Range</label>
                        <select name="amount_range" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="all" <?php echo $currentAmountRange === 'all' ? 'selected' : ''; ?>>All Amounts</option>
                            <option value="under_100" <?php echo $currentAmountRange === 'under_100' ? 'selected' : ''; ?>>Under GH₵ 100</option>
                            <option value="100_500" <?php echo $currentAmountRange === '100_500' ? 'selected' : ''; ?>>GH₵ 100 - 500</option>
                            <option value="500_1000" <?php echo $currentAmountRange === '500_1000' ? 'selected' : ''; ?>>GH₵ 500 - 1,000</option>
                            <option value="over_1000" <?php echo $currentAmountRange === 'over_1000' ? 'selected' : ''; ?>>Over GH₵ 1,000</option>
                        </select>
                    </div>
                </div>

                <div class="flex justify-end pt-4">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-search mr-2"></i>Apply Filters
                    </button>
                </div>
            </form>
        </div>

        <!-- Results Summary -->
        <div class="bg-white rounded-xl shadow-md p-4 border border-gray-100 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">
                        Showing <strong><?php echo count($members); ?></strong> of <strong><?php echo number_format($totalMembers); ?></strong> members
                    </span>
                    <?php if (!empty($currentSearch)): ?>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                            Search: "<?php echo $currentSearch; ?>"
                        </span>
                    <?php endif; ?>
                </div>
                <div class="text-sm text-gray-500">
                    Page <?php echo $currentPage; ?> of <?php echo $totalPages; ?>
                </div>
            </div>
        </div>

        <!-- Members List -->
        <div class="bg-white rounded-xl shadow-md border border-gray-100 mb-8">
            <?php if (!empty($members)): ?>
                <div class="divide-y divide-gray-200">
                    <?php foreach ($members as $member): ?>
                        <div class="member-card p-6 hover:bg-gray-50 transition-all duration-200" 
                             data-member-id="<?php echo $member['id']; ?>">
                            
                            <!-- Member Header -->
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                        <?php if (!empty($member['profile_picture'])): ?>
                                            <img src="<?php echo BASE_URL . $member['profile_picture']; ?>" 
                                                 alt="Profile" class="w-12 h-12 rounded-full object-cover">
                                        <?php else: ?>
                                            <span class="text-green-600 font-bold text-lg">
                                                <?php echo strtoupper(substr($member['first_name'], 0, 1) . substr($member['last_name'], 0, 1)); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-800 text-lg">
                                            <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                                        </h4>
                                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                                            <?php if (!empty($member['phone_number'])): ?>
                                                <span><i class="fas fa-phone mr-1"></i><?php echo htmlspecialchars($member['phone_number']); ?></span>
                                            <?php endif; ?>
                                            <?php if (!empty($member['email'])): ?>
                                                <span><i class="fas fa-envelope mr-1"></i><?php echo htmlspecialchars($member['email']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="toggleMemberHistory(<?php echo $member['id']; ?>)" 
                                        class="text-green-600 hover:text-green-700 font-medium text-sm flex items-center">
                                    <span class="mr-1">View History</span>
                                    <i class="fas fa-chevron-down transition-transform duration-200" id="chevron-<?php echo $member['id']; ?>"></i>
                                </button>
                            </div>

                            <!-- Member Stats -->
                            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                                <div class="text-center p-3 bg-green-50 rounded-lg">
                                    <p class="text-xl font-bold text-green-600">
                                        GH₵ <?php echo number_format($member['total_amount'], 2); ?>
                                    </p>
                                    <p class="text-xs text-gray-600">Total Tithes</p>
                                </div>
                                <div class="text-center p-3 bg-blue-50 rounded-lg">
                                    <p class="text-xl font-bold text-blue-600">
                                        <?php echo $member['total_payments']; ?>
                                    </p>
                                    <p class="text-xs text-gray-600">Payments</p>
                                </div>
                                <div class="text-center p-3 bg-purple-50 rounded-lg">
                                    <p class="text-xl font-bold text-purple-600">
                                        GH₵ <?php echo number_format($member['average_amount'], 2); ?>
                                    </p>
                                    <p class="text-xs text-gray-600">Average</p>
                                </div>
                                <div class="text-center p-3 bg-orange-50 rounded-lg">
                                    <p class="text-sm font-bold text-orange-600">
                                        <?php echo format_date($member['last_payment_date']); ?>
                                    </p>
                                    <p class="text-xs text-gray-600">Last Payment</p>
                                </div>
                                <div class="text-center p-3 bg-gray-50 rounded-lg">
                                    <p class="text-sm font-bold text-gray-600">
                                        <?php echo $member['days_since_last_payment']; ?> days
                                    </p>
                                    <p class="text-xs text-gray-600">Days Ago</p>
                                </div>
                            </div>

                            <!-- Payment History (Initially Hidden) -->
                            <div id="history-<?php echo $member['id']; ?>" class="hidden mt-4 border-t pt-4">
                                <div class="flex items-center justify-between mb-3">
                                    <h5 class="font-medium text-gray-700">Payment History</h5>
                                </div>
                                <div id="history-content-<?php echo $member['id']; ?>" class="space-y-2">
                                    <!-- Payment history will be loaded here via AJAX -->
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-12 text-gray-500">
                    <i class="fas fa-users text-gray-300 text-6xl mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-600 mb-2">No Members Found</h4>
                    <p class="text-sm text-gray-500 mb-4">Try adjusting your filters or search criteria</p>
                    <button onclick="clearFilters()" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg">
                        <i class="fas fa-times mr-2"></i>Clear Filters
                    </button>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="flex justify-center">
                <nav class="flex items-center space-x-2">
                    <?php if ($currentPage > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $currentPage - 1])); ?>" 
                           class="px-3 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>

                    <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                           class="px-3 py-2 border rounded-lg <?php echo $i === $currentPage ? 'bg-green-600 text-white border-green-600' : 'border-gray-300 text-gray-600 hover:bg-gray-50'; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($currentPage < $totalPages): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $currentPage + 1])); ?>" 
                           class="px-3 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Clear all filters
function clearFilters() {
    window.location.href = '<?php echo BASE_URL; ?>finance/tithe/members';
}

// Toggle member payment history
function toggleMemberHistory(memberId) {
    const historyDiv = document.getElementById(`history-${memberId}`);
    const chevron = document.getElementById(`chevron-${memberId}`);
    const historyContent = document.getElementById(`history-content-${memberId}`);
    
    if (!historyDiv || !chevron || !historyContent) {
        console.error('Required elements not found for member:', memberId);
        return;
    }
    
    if (historyDiv.classList.contains('hidden')) {
        // Show history
        historyDiv.classList.remove('hidden');
        chevron.classList.add('rotate-180');
        
        // Load payment history if not already loaded
        const currentContent = historyContent.innerHTML.trim();
        const needsLoading = currentContent === '' || 
                           currentContent.includes('Payment history will be loaded here') ||
                           currentContent.includes('Loading...');
        
        if (needsLoading) {
            loadMemberPaymentHistory(memberId);
        }
    } else {
        // Hide history
        historyDiv.classList.add('hidden');
        chevron.classList.remove('rotate-180');
    }
}

// Load member payment history with pagination
function loadMemberPaymentHistory(memberId, page = 1) {
    const historyContent = document.getElementById(`history-content-${memberId}`);
    
    if (!historyContent) {
        console.error('History content element not found for member:', memberId);
        return;
    }
    
    // Show loading state
    if (page === 1) {
        historyContent.innerHTML = '<div class="text-center py-4"><div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500 mx-auto"></div><p class="mt-2 text-gray-600 text-sm">Loading payment history...</p></div>';
    }
    
    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>finance/tithe/members/history?member_id=${memberId}&page=${page}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.payments && data.payments.length > 0) {
                let html = '';
                
                if (page === 1) {
                    // Add summary header for first page
                    html += `
                        <div class="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-green-800">Payment History</span>
                                <span class="text-lg font-bold text-green-600">${data.total} total payments</span>
                            </div>
                        </div>
                        <div class="space-y-3" id="payments-list-${memberId}">
                    `;
                }
                
                // Add each payment
                data.payments.forEach((payment, index) => {
                    const amount = parseFloat(payment.amount || 0);
                    const formattedAmount = 'GH₵ ' + amount.toLocaleString('en-US', {minimumFractionDigits: 2});
                    
                    html += `
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors border border-gray-200">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-hand-holding-usd text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-800">${formattedAmount}</p>
                                    <p class="text-sm text-gray-500">${payment.formatted_date || payment.transaction_date}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-gray-600 mb-1">${payment.description || 'Tithe payment'}</p>
                                <a href="<?php echo BASE_URL; ?>finance/edit?id=${payment.id}" 
                                   class="text-xs text-green-600 hover:text-green-700 font-medium">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </a>
                            </div>
                        </div>
                    `;
                });
                
                if (page === 1) {
                    html += '</div>';
                    
                    // Add load more button if there are more pages
                    if (data.hasMore) {
                        html += `
                            <div class="text-center mt-4">
                                <button onclick="loadMorePayments(${memberId}, ${data.page + 1})" 
                                        class="bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-plus mr-1"></i>Load More Payments
                                </button>
                            </div>
                        `;
                    }
                    
                    historyContent.innerHTML = html;
                } else {
                    // Append to existing list
                    const paymentsList = document.getElementById(`payments-list-${memberId}`);
                    if (paymentsList) {
                        paymentsList.insertAdjacentHTML('beforeend', html);
                        
                        // Update load more button
                        const loadMoreBtn = historyContent.querySelector('button[onclick*="loadMorePayments"]');
                        if (loadMoreBtn) {
                            if (data.hasMore) {
                                loadMoreBtn.setAttribute('onclick', `loadMorePayments(${memberId}, ${data.page + 1})`);
                            } else {
                                loadMoreBtn.remove();
                            }
                        }
                    }
                }
                
            } else {
                // No payments found
                historyContent.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-receipt text-gray-300 text-3xl mb-3"></i>
                        <p class="font-medium">No payment history found</p>
                        <p class="text-sm">This member hasn't made any tithe payments yet.</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading payment history:', error);
            historyContent.innerHTML = `
                <div class="text-center py-8 text-red-500">
                    <i class="fas fa-exclamation-triangle text-red-300 text-3xl mb-3"></i>
                    <p class="font-medium">Error loading payment history</p>
                    <p class="text-sm">${error.message}</p>
                    <button onclick="loadMemberPaymentHistory(${memberId})" 
                            class="mt-3 px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors">
                        <i class="fas fa-redo mr-1"></i>Try Again
                    </button>
                </div>
            `;
        });
}

// Load more payments (for pagination)
function loadMorePayments(memberId, page) {
    loadMemberPaymentHistory(memberId, page);
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const selects = form.querySelectorAll('select');
    
    selects.forEach(select => {
        select.addEventListener('change', function() {
            form.submit();
        });
    });
    
    // Submit on Enter in search field
    const searchInput = form.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                form.submit();
            }
        });
    }
});
</script>

<style>
.member-card {
    transition: all 0.3s ease;
}

.member-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rotate-180 {
    transform: rotate(180deg);
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
