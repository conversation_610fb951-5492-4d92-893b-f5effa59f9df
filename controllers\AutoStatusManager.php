<?php
/**
 * Automatic Status Manager for Church Programs
 * Handles automatic status transitions based on dates
 */

class AutoStatusManager {
    private $db;
    private $logFile;
    
    public function __construct($database) {
        $this->db = $database;
        $this->logFile = __DIR__ . '/../logs/auto_status.log';
        
        // Create logs directory if it doesn't exist
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * Main function to update all program statuses
     */
    public function updateProgramStatuses() {
        $this->log("Starting automatic status update process");
        
        try {
            $programs = $this->getProgramsForStatusUpdate();
            $updatedCount = 0;
            
            foreach ($programs as $program) {
                $newStatus = $this->calculateStatus($program);
                
                if ($newStatus !== $program['status']) {
                    $this->updateProgramStatus($program['id'], $newStatus, $program['status']);
                    $this->logStatusChange($program['id'], $program['title'], $program['status'], $newStatus);
                    $this->notifyCoordinator($program, $program['status'], $newStatus);
                    $updatedCount++;
                }
            }
            
            $this->log("Status update completed. Updated {$updatedCount} programs");
            return $updatedCount;
            
        } catch (Exception $e) {
            $this->log("Error in status update: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get programs that need status evaluation
     */
    private function getProgramsForStatusUpdate() {
        $query = "
            SELECT p.*,
                   CONCAT(m.first_name, ' ', m.last_name) as coordinator_name,
                   m.email as coordinator_email
            FROM church_programs p
            LEFT JOIN members m ON p.coordinator_id = m.id
            WHERE p.auto_status_enabled = 1
            AND p.status IN ('planned', 'in_progress')
            ORDER BY p.start_date ASC
        ";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Calculate what the status should be based on dates
     */
    private function calculateStatus($program) {
        $today = date('Y-m-d');
        $startDate = $program['start_date'];
        $endDate = $program['end_date'];
        $currentStatus = $program['status'];
        
        // Don't auto-update manually set statuses
        if (in_array($currentStatus, ['cancelled', 'postponed'])) {
            return $currentStatus;
        }
        
        // Determine status based on dates
        if ($today < $startDate) {
            return 'planned';
        } elseif ($today >= $startDate && $today <= $endDate) {
            return 'in_progress';
        } else {
            return 'completed';
        }
    }
    
    /**
     * Update program status in database
     */
    private function updateProgramStatus($programId, $newStatus, $oldStatus) {
        $query = "
            UPDATE church_programs
            SET status = ?,
                last_status_update = NOW(),
                status_updated_by = 'system'
            WHERE id = ?
        ";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([$newStatus, $programId]);
    }
    
    /**
     * Log status changes for audit trail
     */
    private function logStatusChange($programId, $programTitle, $oldStatus, $newStatus) {
        $query = "
            INSERT INTO program_status_log (program_id, old_status, new_status, changed_by, changed_at, change_type)
            VALUES (?, ?, ?, 'system', NOW(), 'automatic')
        ";
        
        try {
            $stmt = $this->db->prepare($query);
            $stmt->execute([$programId, $oldStatus, $newStatus]);
        } catch (Exception $e) {
            $this->log("Error logging status change: " . $e->getMessage());
        }
        
        $this->log("Status changed for '{$programTitle}' (ID: {$programId}): {$oldStatus} → {$newStatus}");
    }
    
    /**
     * Send notification to program coordinator
     */
    private function notifyCoordinator($program, $oldStatus, $newStatus) {
        if (empty($program['coordinator_email'])) {
            return;
        }
        
        $subject = $this->getNotificationSubject($program['title'], $newStatus);
        $message = $this->getNotificationMessage($program, $oldStatus, $newStatus);
        
        // Simple email notification (you can enhance this with a proper email service)
        // Disabled for now - uncomment when SMTP is configured
        /*
        $headers = "From: Church Management System <<EMAIL>>\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";

        mail($program['coordinator_email'], $subject, $message, $headers);
        */
        
        $this->log("Notification sent to {$program['coordinator_name']} ({$program['coordinator_email']})");
    }
    
    /**
     * Generate notification subject
     */
    private function getNotificationSubject($programTitle, $newStatus) {
        $statusMessages = [
            'in_progress' => '🟢 Program Started',
            'completed' => '✅ Program Completed'
        ];
        
        $prefix = $statusMessages[$newStatus] ?? '📅 Status Updated';
        return "{$prefix}: {$programTitle}";
    }
    
    /**
     * Generate notification message
     */
    private function getNotificationMessage($program, $oldStatus, $newStatus) {
        $statusEmojis = [
            'planned' => '📅',
            'in_progress' => '🟢',
            'completed' => '✅',
            'cancelled' => '❌',
            'postponed' => '⏸️'
        ];
        
        $oldEmoji = $statusEmojis[$oldStatus] ?? '📅';
        $newEmoji = $statusEmojis[$newStatus] ?? '📅';
        
        $messages = [
            'planned_to_in_progress' => "
                <h3>{$newEmoji} Program Started!</h3>
                <p>Your program <strong>{$program['title']}</strong> has automatically started and is now <strong>IN PROGRESS</strong>.</p>
                <p><strong>Program Details:</strong></p>
                <ul>
                    <li>Start Date: {$program['start_date']}</li>
                    <li>End Date: {$program['end_date']}</li>
                    <li>Location: {$program['location']}</li>
                </ul>
                <p>Please ensure all preparations are complete and the program runs smoothly.</p>
            ",
            'in_progress_to_completed' => "
                <h3>{$newEmoji} Program Completed!</h3>
                <p>Your program <strong>{$program['title']}</strong> has been automatically marked as <strong>COMPLETED</strong>.</p>
                <p><strong>Program Details:</strong></p>
                <ul>
                    <li>Start Date: {$program['start_date']}</li>
                    <li>End Date: {$program['end_date']}</li>
                    <li>Duration: " . $this->calculateDuration($program['start_date'], $program['end_date']) . "</li>
                </ul>
                <p>Thank you for your coordination. Please review the program and provide any feedback if needed.</p>
            "
        ];
        
        $messageKey = "{$oldStatus}_to_{$newStatus}";
        $message = $messages[$messageKey] ?? "
            <h3>{$newEmoji} Status Updated</h3>
            <p>The status of your program <strong>{$program['title']}</strong> has been updated from <strong>{$oldEmoji} " . ucfirst($oldStatus) . "</strong> to <strong>{$newEmoji} " . ucfirst($newStatus) . "</strong>.</p>
        ";
        
        return "
            <html>
            <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;'>
                    {$message}
                    <hr style='margin: 20px 0; border: none; border-top: 1px solid #eee;'>
                    <p style='font-size: 12px; color: #666;'>
                        This is an automatic notification from the Church Management System.<br>
                        Time: " . date('Y-m-d H:i:s') . "
                    </p>
                </div>
            </body>
            </html>
        ";
    }
    
    /**
     * Calculate program duration
     */
    private function calculateDuration($startDate, $endDate) {
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);
        $diff = $start->diff($end);
        
        if ($diff->days == 0) {
            return "1 day";
        } else {
            return ($diff->days + 1) . " days";
        }
    }
    
    /**
     * Log messages to file
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Get programs starting today (for immediate notifications)
     */
    public function getProgramsStartingToday() {
        $query = "
            SELECT p.*,
                   CONCAT(m.first_name, ' ', m.last_name) as coordinator_name,
                   m.email as coordinator_email
            FROM church_programs p
            LEFT JOIN members m ON p.coordinator_id = m.id
            WHERE p.start_date = CURDATE()
            AND p.status = 'planned'
            AND p.auto_status_enabled = 1
        ";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get recent automatic status changes for dashboard
     */
    public function getRecentAutoUpdates($limit = 10) {
        $query = "
            SELECT psl.*, p.title as program_title
            FROM program_status_log psl
            JOIN church_programs p ON psl.program_id = p.id
            WHERE psl.change_type = 'automatic'
            ORDER BY psl.changed_at DESC
            LIMIT " . intval($limit) . "
        ";

        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
