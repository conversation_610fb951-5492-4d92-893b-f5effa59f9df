<?php
/**
 * Edit Program View - Church Program & Activities Planner
 */

// Header configuration
$header_title = 'Edit Program';
$header_subtitle = 'Update program details and information';
$header_icon = 'fas fa-edit';
$header_width = 'w-[90%] mx-auto';

// Custom navigation for edit page
$navigation_buttons = [
    'back' => [
        'url' => BASE_URL . 'programs',
        'text' => 'Back to Programs',
        'icon' => 'fas fa-arrow-left',
        'style' => 'bg-gray-100 hover:bg-gray-200 text-gray-700'
    ],
    'view' => [
        'url' => BASE_URL . 'programs/show?id=' . ($program['id'] ?? ''),
        'text' => 'View Program',
        'icon' => 'fas fa-eye',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ],
    'dashboard' => [
        'url' => BASE_URL . 'programs/dashboard',
        'text' => 'Dashboard',
        'icon' => 'fas fa-chart-pie',
        'style' => 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
    ]
];

// Include shared header and form components
include 'components/header.php';
include 'components/form_elements.php';
?>

    <!-- Edit Program Form -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <form action="<?php echo BASE_URL; ?>programs/update" method="POST" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
            <input type="hidden" name="id" value="<?php echo $program['id']; ?>">
            
            <div class="p-8">
                <!-- Basic Information -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-primary"></i>
                            Basic Information
                        </h3>
                        <p class="text-gray-600 text-sm mt-1">Update the core details of your program</p>
                    </div>

                    <!-- Title and Description -->
                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Program Title <span class="text-red-500">*</span></label>
                            <input type="text" id="title" name="title" required
                                   value="<?php echo htmlspecialchars($program['title']); ?>"
                                   placeholder="Enter program title"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea id="description" name="description" rows="4"
                                      placeholder="Describe the program, its purpose, and what participants can expect"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"><?php echo htmlspecialchars($program['description']); ?></textarea>
                        </div>
                    </div>

                    <!-- Category and Department -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">Program Category <span class="text-red-500">*</span></label>
                            <select id="category_id" name="category_id" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="">Select a category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo ($program['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div>
                            <label for="department_id" class="block text-sm font-medium text-gray-700 mb-2">Ministry Department</label>
                            <select id="department_id" name="department_id"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="">Select a department</option>
                                <?php foreach ($departments as $department): ?>
                                    <option value="<?php echo $department['id']; ?>" <?php echo ($program['department_id'] == $department['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($department['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Schedule Information -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-calendar mr-2 text-primary"></i>
                            Schedule Information
                        </h3>
                        <p class="text-gray-600 text-sm mt-1">Set the dates and times for your program</p>
                    </div>

                    <!-- Dates -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Start Date <span class="text-red-500">*</span></label>
                            <input type="date" id="start_date" name="start_date" required
                                   value="<?php echo $program['start_date']; ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>

                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">End Date <span class="text-red-500">*</span></label>
                            <input type="date" id="end_date" name="end_date" required
                                   value="<?php echo $program['end_date']; ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>
                    </div>

                    <!-- Times -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="start_time" class="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                            <input type="time" id="start_time" name="start_time"
                                   value="<?php echo $program['start_time']; ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>

                        <div>
                            <label for="end_time" class="block text-sm font-medium text-gray-700 mb-2">End Time</label>
                            <input type="time" id="end_time" name="end_time"
                                   value="<?php echo $program['end_time']; ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>
                    </div>
                </div>

                <!-- Coordinator -->
                <div>
                    <label for="coordinator_search" class="block text-sm font-medium text-gray-700 mb-2">Program Coordinator</label>
                    <div class="relative">
                        <input type="text" 
                               id="coordinator_search" 
                               placeholder="Search for a coordinator by name..."
                               autocomplete="off"
                               value="<?php echo htmlspecialchars($program['coordinator_name'] ?? ''); ?>"
                               class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <!-- Hidden input to store the selected coordinator ID -->
                        <input type="hidden" id="coordinator_id" name="coordinator_id" value="<?php echo $program['coordinator_id']; ?>">
                    </div>
                    
                    <!-- Dropdown for search results -->
                    <div id="coordinator_dropdown" class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                        <div id="coordinator_results" class="py-1">
                            <!-- Search results will be populated here -->
                        </div>
                    </div>
                    
                    <!-- Selected coordinator display -->
                    <div id="selected_coordinator" class="<?php echo !empty($program['coordinator_name']) ? '' : 'hidden'; ?> mt-2 p-3 bg-primary bg-opacity-10 border border-primary border-opacity-20 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900" id="selected_coordinator_name"><?php echo htmlspecialchars($program['coordinator_name'] ?? ''); ?></p>
                                    <p class="text-sm text-gray-600">Selected as Program Coordinator</p>
                                </div>
                            </div>
                            <button type="button" onclick="clearCoordinatorSelection()" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Location -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input type="text" id="location" name="location"
                           value="<?php echo htmlspecialchars($program['location']); ?>"
                           placeholder="Church sanctuary, fellowship hall, etc."
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                </div>
                <!-- Program Settings -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-cogs mr-2 text-primary"></i>
                            Program Settings
                        </h3>
                        <p class="text-gray-600 text-sm mt-1">Configure program status, priority, and other settings</p>
                    </div>

                    <!-- Status and Priority -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                Status
                                <?php if (($program['status_updated_by'] ?? '') === 'system'): ?>
                                    <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Auto-Updated</span>
                                <?php endif; ?>
                            </label>
                            <select id="status" name="status"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="planned" <?php echo ($program['status'] == 'planned') ? 'selected' : ''; ?>>Planned</option>
                                <option value="in_progress" <?php echo ($program['status'] == 'in_progress') ? 'selected' : ''; ?>>In Progress</option>
                                <option value="completed" <?php echo ($program['status'] == 'completed') ? 'selected' : ''; ?>>Completed</option>
                                <option value="cancelled" <?php echo ($program['status'] == 'cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                                <option value="postponed" <?php echo ($program['status'] == 'postponed') ? 'selected' : ''; ?>>Postponed</option>
                            </select>

                            <!-- Auto Status Controls -->
                            <div class="mt-3 p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="auto_status_enabled" value="1"
                                                   <?php echo (($program['auto_status_enabled'] ?? 1) == 1) ? 'checked' : ''; ?>
                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700">Enable automatic status updates</span>
                                        </label>
                                        <p class="text-xs text-gray-500 mt-1">System will automatically update status based on program dates</p>
                                    </div>
                                    <?php if (!empty($program['last_status_update'])): ?>
                                        <div class="text-right">
                                            <p class="text-xs text-gray-500">Last updated:</p>
                                            <p class="text-xs text-gray-700"><?php echo date('M j, Y g:i A', strtotime($program['last_status_update'])); ?></p>
                                            <p class="text-xs text-gray-500">by <?php echo ucfirst($program['status_updated_by'] ?? 'user'); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                            <select id="priority" name="priority"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="low" <?php echo ($program['priority'] == 'low') ? 'selected' : ''; ?>>Low</option>
                                <option value="medium" <?php echo ($program['priority'] == 'medium') ? 'selected' : ''; ?>>Medium</option>
                                <option value="high" <?php echo ($program['priority'] == 'high') ? 'selected' : ''; ?>>High</option>
                                <option value="critical" <?php echo ($program['priority'] == 'critical') ? 'selected' : ''; ?>>Critical</option>
                            </select>
                        </div>
                    </div>

                    <!-- Budget and Attendance -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="budget_allocated" class="block text-sm font-medium text-gray-700 mb-2">Budget Allocated</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 text-sm">$</span>
                                </div>
                                <input type="number" id="budget_allocated" name="budget_allocated"
                                       value="<?php echo $program['budget_allocated']; ?>"
                                       min="0" step="0.01" placeholder="0.00"
                                       class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                            </div>
                        </div>

                        <div>
                            <label for="expected_attendance" class="block text-sm font-medium text-gray-700 mb-2">Expected Attendance</label>
                            <input type="number" id="expected_attendance" name="expected_attendance"
                                   value="<?php echo $program['expected_attendance']; ?>"
                                   min="1" placeholder="Number of expected participants"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>
                    </div>

                    <!-- Registration Settings -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" id="requires_registration" name="requires_registration" value="1"
                                       <?php echo $program['requires_registration'] ? 'checked' : ''; ?>
                                       onchange="toggleMaxParticipants()"
                                       class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2">
                                <label for="requires_registration" class="text-sm font-medium text-gray-700">Requires registration</label>
                            </div>
                        </div>

                        <div id="max_participants_group" style="display: <?php echo $program['requires_registration'] ? 'block' : 'none'; ?>;">
                            <label for="max_participants" class="block text-sm font-medium text-gray-700 mb-2">Maximum Participants</label>
                            <input type="number" id="max_participants" name="max_participants"
                                   value="<?php echo $program['max_participants']; ?>"
                                   min="1" placeholder="Leave blank for unlimited"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>
                    </div>
                </div>

                <!-- Invited Guests (Optional) -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <i class="fas fa-user-friends mr-2 text-primary"></i>
                                    Invited Guests
                                    <span class="ml-2 text-sm text-gray-500 font-normal">(Optional)</span>
                                </h3>
                                <p class="text-gray-600 text-sm mt-1">Add special guests, speakers, or ministry leaders if this program involves invited guests</p>
                            </div>
                            <button type="button" id="toggle_guests" onclick="toggleGuestsSection()"
                                    class="inline-flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors">
                                <i class="fas fa-plus mr-1" id="guests_toggle_icon"></i>
                                <span id="guests_toggle_text">Add Guests</span>
                            </button>
                        </div>
                    </div>

                    <!-- Guest List Container -->
                    <div id="guests_section" class="<?php echo empty($guests) ? 'hidden' : ''; ?>">
                        <div id="guests_container" class="space-y-4">
                        <?php if (!empty($guests)): ?>
                            <?php foreach ($guests as $index => $guest): ?>
                                <div class="guest-entry bg-gray-50 rounded-lg p-4 border border-gray-200">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="font-medium text-gray-800 flex items-center">
                                            <i class="fas fa-user mr-2 text-primary"></i>
                                            Guest #<?php echo $index + 1; ?>
                                        </h4>
                                        <button type="button" onclick="removeGuest(this)" class="text-red-500 hover:text-red-700 transition-colors duration-200" <?php echo count($guests) <= 1 ? 'style="display: none;"' : ''; ?>>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <input type="hidden" name="guests[<?php echo $index; ?>][id]" value="<?php echo $guest['id']; ?>">

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Guest Name</label>
                                            <input type="text" name="guests[<?php echo $index; ?>][name]"
                                                   value="<?php echo htmlspecialchars($guest['guest_name']); ?>"
                                                   placeholder="Enter guest's full name"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Role/Title</label>
                                            <input type="text" name="guests[<?php echo $index; ?>][role]"
                                                   value="<?php echo htmlspecialchars($guest['guest_role']); ?>"
                                                   placeholder="e.g., Guest Speaker, Worship Leader, Pastor"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                                            <input type="email" name="guests[<?php echo $index; ?>][email]"
                                                   value="<?php echo htmlspecialchars($guest['contact_email']); ?>"
                                                   placeholder="<EMAIL>"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                                            <input type="tel" name="guests[<?php echo $index; ?>][phone]"
                                                   value="<?php echo htmlspecialchars($guest['contact_phone']); ?>"
                                                   placeholder="(*************"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Organization/Church</label>
                                        <input type="text" name="guests[<?php echo $index; ?>][organization]"
                                               value="<?php echo htmlspecialchars($guest['organization']); ?>"
                                               placeholder="Guest's home church or organization"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                    </div>

                                    <div class="mt-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Special Notes</label>
                                        <textarea name="guests[<?php echo $index; ?>][notes]" rows="2"
                                                  placeholder="Any special requirements, dietary restrictions, or notes about this guest"
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"><?php echo htmlspecialchars($guest['special_notes']); ?></textarea>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <!-- Default empty guest entry if no guests exist -->
                            <div class="guest-entry bg-gray-50 rounded-lg p-4 border border-gray-200">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-medium text-gray-800 flex items-center">
                                        <i class="fas fa-user mr-2 text-primary"></i>
                                        Guest #1
                                    </h4>
                                    <button type="button" onclick="removeGuest(this)" class="text-red-500 hover:text-red-700 transition-colors duration-200" style="display: none;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Guest Name</label>
                                        <input type="text" name="guests[0][name]"
                                               placeholder="Enter guest's full name"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Role/Title</label>
                                        <input type="text" name="guests[0][role]"
                                               placeholder="e.g., Guest Speaker, Worship Leader, Pastor"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                                        <input type="email" name="guests[0][email]"
                                               placeholder="<EMAIL>"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                                        <input type="tel" name="guests[0][phone]"
                                               placeholder="(*************"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Organization/Church</label>
                                    <input type="text" name="guests[0][organization]"
                                           placeholder="Guest's home church or organization"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                </div>

                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Special Notes</label>
                                    <textarea name="guests[0][notes]" rows="2"
                                              placeholder="Any special requirements, dietary restrictions, or notes about this guest"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"></textarea>
                                </div>
                            </div>
                        <?php endif; ?>
                        </div>

                        <!-- Add Guest Button -->
                        <div class="flex justify-center mt-4">
                            <button type="button" onclick="addGuest()" class="inline-flex items-center px-4 py-2 bg-secondary hover:bg-secondary-dark text-gray-800 font-medium rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i> Add Another Guest
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Additional Notes -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-sticky-note mr-2 text-primary"></i>
                            Additional Information
                        </h3>
                        <p class="text-gray-600 text-sm mt-1">Any extra details or special requirements</p>
                    </div>

                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                        <textarea id="notes" name="notes" rows="4"
                                  placeholder="Any additional notes, special requirements, or instructions"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"><?php echo htmlspecialchars($program['notes']); ?></textarea>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                    <button type="submit" class="flex-1 bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        <i class="fas fa-save mr-2"></i> Update Program
                    </button>

                    <a href="<?php echo BASE_URL; ?>programs/show?id=<?php echo $program['id']; ?>" class="flex-1 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-700 font-semibold py-4 px-8 rounded-lg transition-all duration-300 text-center">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Registration toggle function
function toggleMaxParticipants() {
    const checkbox = document.getElementById('requires_registration');
    const maxParticipantsGroup = document.getElementById('max_participants_group');

    if (checkbox.checked) {
        maxParticipantsGroup.style.display = 'block';
    } else {
        maxParticipantsGroup.style.display = 'none';
        document.getElementById('max_participants').value = '';
    }
}

// Coordinator Search Functionality (same as create page)
let coordinatorMembers = [];
let coordinatorSearchTimeout;
let guestCounter = <?php echo count($guests); ?>;

// Load all members for coordinator search
function loadCoordinatorMembers() {
    coordinatorMembers = [
        <?php foreach ($members as $member): ?>
        {
            id: <?php echo $member['id']; ?>,
            name: "<?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>",
            first_name: "<?php echo htmlspecialchars($member['first_name']); ?>",
            last_name: "<?php echo htmlspecialchars($member['last_name']); ?>",
            email: "<?php echo htmlspecialchars($member['email'] ?? ''); ?>"
        },
        <?php endforeach; ?>
    ];
}

// Initialize coordinator search
function initializeCoordinatorSearch() {
    loadCoordinatorMembers();

    const searchInput = document.getElementById('coordinator_search');
    const dropdown = document.getElementById('coordinator_dropdown');
    const resultsContainer = document.getElementById('coordinator_results');

    // Search input event listener
    searchInput.addEventListener('input', function() {
        clearTimeout(coordinatorSearchTimeout);
        const query = this.value.trim();

        if (query.length < 2) {
            hideCoordinatorDropdown();
            return;
        }

        coordinatorSearchTimeout = setTimeout(() => {
            searchCoordinators(query);
        }, 300);
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.relative')) {
            hideCoordinatorDropdown();
        }
    });

    // Handle keyboard navigation
    searchInput.addEventListener('keydown', function(event) {
        const items = dropdown.querySelectorAll('.coordinator-item');
        const activeItem = dropdown.querySelector('.coordinator-item.active');
        let currentIndex = Array.from(items).indexOf(activeItem);

        switch(event.key) {
            case 'ArrowDown':
                event.preventDefault();
                currentIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                setActiveCoordinatorItem(items, currentIndex);
                break;
            case 'ArrowUp':
                event.preventDefault();
                currentIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                setActiveCoordinatorItem(items, currentIndex);
                break;
            case 'Enter':
                event.preventDefault();
                if (activeItem) {
                    selectCoordinator(activeItem.dataset.id, activeItem.dataset.name);
                }
                break;
            case 'Escape':
                hideCoordinatorDropdown();
                break;
        }
    });
}

// Search coordinators
function searchCoordinators(query) {
    const results = coordinatorMembers.filter(member =>
        member.name.toLowerCase().includes(query.toLowerCase()) ||
        member.first_name.toLowerCase().includes(query.toLowerCase()) ||
        member.last_name.toLowerCase().includes(query.toLowerCase()) ||
        (member.email && member.email.toLowerCase().includes(query.toLowerCase()))
    );

    displayCoordinatorResults(results);
}

// Display search results
function displayCoordinatorResults(results) {
    const resultsContainer = document.getElementById('coordinator_results');
    const dropdown = document.getElementById('coordinator_dropdown');

    if (results.length === 0) {
        resultsContainer.innerHTML = `
            <div class="px-4 py-3 text-sm text-gray-500 text-center">
                <i class="fas fa-search text-gray-400 mb-2"></i>
                <p>No coordinators found</p>
            </div>
        `;
    } else {
        resultsContainer.innerHTML = results.map(member => `
            <div class="coordinator-item px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150"
                 data-id="${member.id}"
                 data-name="${member.name}"
                 onclick="selectCoordinator(${member.id}, '${member.name}')">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-primary text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="font-medium text-gray-900 truncate">${member.name}</p>
                        ${member.email ? `<p class="text-sm text-gray-500 truncate">${member.email}</p>` : ''}
                    </div>
                </div>
            </div>
        `).join('');
    }

    showCoordinatorDropdown();
}

// Set active item for keyboard navigation
function setActiveCoordinatorItem(items, index) {
    items.forEach(item => item.classList.remove('active', 'bg-primary', 'bg-opacity-10'));
    if (items[index]) {
        items[index].classList.add('active', 'bg-primary', 'bg-opacity-10');
    }
}

// Select coordinator
function selectCoordinator(id, name) {
    document.getElementById('coordinator_id').value = id;
    document.getElementById('coordinator_search').value = name;
    document.getElementById('selected_coordinator_name').textContent = name;

    // Show selected coordinator display
    document.getElementById('selected_coordinator').classList.remove('hidden');

    hideCoordinatorDropdown();
}

// Clear coordinator selection
function clearCoordinatorSelection() {
    document.getElementById('coordinator_id').value = '';
    document.getElementById('coordinator_search').value = '';
    document.getElementById('selected_coordinator').classList.add('hidden');
    hideCoordinatorDropdown();
}

// Show dropdown
function showCoordinatorDropdown() {
    document.getElementById('coordinator_dropdown').classList.remove('hidden');
}

// Hide dropdown
function hideCoordinatorDropdown() {
    document.getElementById('coordinator_dropdown').classList.add('hidden');
}

// Guest Management Functions
function toggleGuestsSection() {
    const guestsSection = document.getElementById('guests_section');
    const toggleIcon = document.getElementById('guests_toggle_icon');
    const toggleText = document.getElementById('guests_toggle_text');

    if (guestsSection.classList.contains('hidden')) {
        // Show guests section
        guestsSection.classList.remove('hidden');
        toggleIcon.className = 'fas fa-minus mr-1';
        toggleText.textContent = 'Hide Guests';

        // If no guests exist, add one
        if (document.querySelectorAll('.guest-entry').length === 0) {
            addGuest();
        }
    } else {
        // Hide guests section
        guestsSection.classList.add('hidden');
        toggleIcon.className = 'fas fa-plus mr-1';
        toggleText.textContent = 'Add Guests';
    }
}

function addGuest() {
    const container = document.getElementById('guests_container');
    const guestEntry = document.createElement('div');
    guestEntry.className = 'guest-entry bg-gray-50 rounded-lg p-4 border border-gray-200';

    guestEntry.innerHTML = `
        <div class="flex items-center justify-between mb-4">
            <h4 class="font-medium text-gray-800 flex items-center">
                <i class="fas fa-user mr-2 text-primary"></i>
                Guest #${guestCounter + 1}
            </h4>
            <button type="button" onclick="removeGuest(this)" class="text-red-500 hover:text-red-700 transition-colors duration-200">
                <i class="fas fa-trash"></i>
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Guest Name</label>
                <input type="text" name="guests[${guestCounter}][name]"
                       placeholder="Enter guest's full name"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Role/Title</label>
                <input type="text" name="guests[${guestCounter}][role]"
                       placeholder="e.g., Guest Speaker, Worship Leader, Pastor"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                <input type="email" name="guests[${guestCounter}][email]"
                       placeholder="<EMAIL>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                <input type="tel" name="guests[${guestCounter}][phone]"
                       placeholder="(*************"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
            </div>
        </div>

        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Organization/Church</label>
            <input type="text" name="guests[${guestCounter}][organization]"
                   placeholder="Guest's home church or organization"
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
        </div>

        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Special Notes</label>
            <textarea name="guests[${guestCounter}][notes]" rows="2"
                      placeholder="Any special requirements, dietary restrictions, or notes about this guest"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"></textarea>
        </div>
    `;

    container.appendChild(guestEntry);
    guestCounter++;

    // Show remove button for all guests if there's more than one
    updateRemoveButtons();

    // Smooth scroll to the new guest entry
    guestEntry.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function removeGuest(button) {
    const guestEntry = button.closest('.guest-entry');
    const container = document.getElementById('guests_container');

    // Add fade out animation
    guestEntry.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
    guestEntry.style.opacity = '0';
    guestEntry.style.transform = 'translateX(-20px)';

    setTimeout(() => {
        container.removeChild(guestEntry);
        updateRemoveButtons();
        renumberGuests();
    }, 300);
}

function updateRemoveButtons() {
    const guestEntries = document.querySelectorAll('.guest-entry');
    guestEntries.forEach((entry, index) => {
        const removeButton = entry.querySelector('button[onclick*="removeGuest"]');
        if (guestEntries.length > 1) {
            removeButton.style.display = 'block';
        } else {
            removeButton.style.display = 'none';
        }
    });
}

function renumberGuests() {
    const guestEntries = document.querySelectorAll('.guest-entry');
    guestEntries.forEach((entry, index) => {
        const header = entry.querySelector('h4');
        header.innerHTML = `<i class="fas fa-user mr-2 text-primary"></i>Guest #${index + 1}`;

        // Update input names to maintain proper indexing
        const inputs = entry.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            const name = input.getAttribute('name');
            if (name && name.includes('guests[')) {
                const newName = name.replace(/guests\[\d+\]/, `guests[${index}]`);
                input.setAttribute('name', newName);
            }
        });
    });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCoordinatorSearch();
    updateRemoveButtons();

    // Set initial toggle state based on whether guests exist
    const hasGuests = <?php echo !empty($guests) ? 'true' : 'false'; ?>;
    const toggleIcon = document.getElementById('guests_toggle_icon');
    const toggleText = document.getElementById('guests_toggle_text');

    if (hasGuests) {
        toggleIcon.className = 'fas fa-minus mr-1';
        toggleText.textContent = 'Hide Guests';
    } else {
        toggleIcon.className = 'fas fa-plus mr-1';
        toggleText.textContent = 'Add Guests';
    }
});
</script>
