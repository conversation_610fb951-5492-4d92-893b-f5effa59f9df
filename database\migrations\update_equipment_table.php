<?php
// Define application root directory
define('APP_ROOT', dirname(dirname(__DIR__)));

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database configuration
require_once APP_ROOT . '/config/database.php';

// Create database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>Equipment Table Update</h1>";

try {
    // Check if equipment table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'equipment'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>Equipment table does not exist!</p>";
        exit;
    }
    
    // Check if equipment table has condition column
    $stmt = $conn->query("DESCRIBE equipment");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasCondition = false;
    $hasEquipmentCondition = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] == 'condition') {
            $hasCondition = true;
        }
        if ($column['Field'] == 'equipment_condition') {
            $hasEquipmentCondition = true;
        }
    }
    
    echo "<h2>Current Table Structure</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        foreach ($column as $key => $value) {
            echo "<td>" . ($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Begin transaction
    $conn->beginTransaction();
    
    if ($hasCondition && !$hasEquipmentCondition) {
        // Rename condition column to equipment_condition
        echo "<p>Renaming 'condition' column to 'equipment_condition'...</p>";
        $stmt = $conn->prepare("ALTER TABLE equipment CHANGE `condition` equipment_condition VARCHAR(50)");
        $stmt->execute();
        echo "<p style='color: green;'>Column renamed successfully!</p>";
    } else if (!$hasCondition && !$hasEquipmentCondition) {
        // Add equipment_condition column
        echo "<p>Adding 'equipment_condition' column...</p>";
        $stmt = $conn->prepare("ALTER TABLE equipment ADD equipment_condition VARCHAR(50) AFTER purchase_price");
        $stmt->execute();
        echo "<p style='color: green;'>Column added successfully!</p>";
    } else if ($hasCondition && $hasEquipmentCondition) {
        // Both columns exist, copy data from condition to equipment_condition
        echo "<p>Both 'condition' and 'equipment_condition' columns exist. Copying data...</p>";
        $stmt = $conn->prepare("UPDATE equipment SET equipment_condition = `condition` WHERE equipment_condition IS NULL");
        $stmt->execute();
        echo "<p style='color: green;'>Data copied successfully!</p>";
        
        // Drop condition column
        echo "<p>Dropping 'condition' column...</p>";
        $stmt = $conn->prepare("ALTER TABLE equipment DROP COLUMN `condition`");
        $stmt->execute();
        echo "<p style='color: green;'>Column dropped successfully!</p>";
    } else {
        echo "<p style='color: green;'>Table structure is already correct with 'equipment_condition' column.</p>";
    }
    
    // Commit transaction
    $conn->commit();
    
    // Check updated structure
    $stmt = $conn->query("DESCRIBE equipment");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Updated Table Structure</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        foreach ($column as $key => $value) {
            echo "<td>" . ($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<p style='color: green;'>Equipment table update completed successfully!</p>";
    echo "<p><a href='equipment/add' style='padding: 10px; background-color: #4CBF26; color: white; text-decoration: none; border-radius: 5px;'>Go to Add Equipment Form</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
