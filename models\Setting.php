<?php
/**
 * Setting Model
 */

class Setting {
    // Database connection and table name
    private $conn;
    private $table_name = "settings";

    // Object properties
    public $id;
    public $setting_key;
    public $setting_value;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     * 
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all settings
     *
     * @return PDOStatement
     */
    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY setting_key ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get all settings as associative array
     *
     * @return array
     */
    public function getAllAsArray() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY setting_key ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        $settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }

        return $settings;
    }

    /**
     * Get setting by key
     *
     * @param string $key
     * @return string|null
     */
    public function getByKey($key) {
        $query = "SELECT setting_value FROM " . $this->table_name . " WHERE setting_key = :key LIMIT 0,1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':key', $key);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row ? $row['setting_value'] : null;
    }

    /**
     * Update setting (legacy method - use updateSetting for upsert functionality)
     *
     * @param string $key
     * @param string $value
     * @return bool
     * @deprecated Use updateSetting() instead for better upsert functionality
     */
    public function update($key, $value) {
        return $this->updateSetting($key, $value);
    }

    /**
     * Create setting
     * 
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (setting_key, setting_value, created_at, updated_at)
                  VALUES
                  (:setting_key, :setting_value, :created_at, :updated_at)";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize inputs
        $this->setting_key = htmlspecialchars(strip_tags($this->setting_key));
        $this->setting_value = htmlspecialchars(strip_tags($this->setting_value));
        $this->created_at = htmlspecialchars(strip_tags($this->created_at));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));
        
        // Bind parameters
        $stmt->bindParam(':setting_key', $this->setting_key);
        $stmt->bindParam(':setting_value', $this->setting_value);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);
        
        // Execute query
        if ($stmt->execute()) {
            return true;
        }
        
        return false;
    }

    /**
     * Update or create a setting (upsert operation)
     *
     * This is the primary method for setting values. It will update
     * an existing setting or create a new one if it doesn't exist.
     *
     * @param string $key Setting key
     * @param string $value Setting value
     * @return bool True on success, false on failure
     */
    public function updateSetting($key, $value) {
        // Check if setting exists
        $checkQuery = "SELECT id FROM " . $this->table_name . " WHERE setting_key = :setting_key LIMIT 1";
        $checkStmt = $this->conn->prepare($checkQuery);
        $checkStmt->bindParam(':setting_key', $key);
        $checkStmt->execute();

        $timestamp = date('Y-m-d H:i:s');

        if ($checkStmt->rowCount() > 0) {
            // Update existing setting
            $query = "UPDATE " . $this->table_name . " SET setting_value = :setting_value, updated_at = :updated_at WHERE setting_key = :setting_key";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':setting_value', $value);
            $stmt->bindParam(':updated_at', $timestamp);
            $stmt->bindParam(':setting_key', $key);
        } else {
            // Create new setting
            $query = "INSERT INTO " . $this->table_name . " (setting_key, setting_value, created_at, updated_at) VALUES (:setting_key, :setting_value, :created_at, :updated_at)";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':setting_key', $key);
            $stmt->bindParam(':setting_value', $value);
            $stmt->bindParam(':created_at', $timestamp);
            $stmt->bindParam(':updated_at', $timestamp);
        }

        return $stmt->execute();
    }

    /**
     * Get a specific setting value by key
     *
     * @param string $key
     * @return string|null
     */
    public function getValue($key) {
        $query = "SELECT setting_value FROM " . $this->table_name . " WHERE setting_key = :setting_key LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':setting_key', $key);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row ? $row['setting_value'] : null;
    }
}
