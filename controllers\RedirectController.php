<?php
/**
 * Redirect Controller
 * 
 * Handles legacy route redirects during the migration to RESTful routes.
 * This controller provides backward compatibility while we transition
 * from old URL patterns to new RESTful patterns.
 */

class RedirectController {
    
    /**
     * Redirect legacy member view routes to RESTful pattern
     * From: /members/view/{id} → To: /members/{id}
     */
    public function memberView($id = null) {
        if ($id) {
            $this->permanentRedirect("members/$id");
        } else {
            $this->permanentRedirect('members');
        }
    }
    
    /**
     * Redirect legacy member view with query parameter
     * From: /members/view?id={id} → To: /members/{id}
     */
    public function memberViewQuery() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("members/$id");
        } else {
            $this->permanentRedirect('members');
        }
    }
    
    /**
     * Redirect legacy member edit routes to RESTful pattern
     * From: /members/edit/{id} → To: /members/{id}/edit
     */
    public function memberEdit($id = null) {
        if ($id) {
            $this->permanentRedirect("members/$id/edit");
        } else {
            $this->permanentRedirect('members');
        }
    }
    
    /**
     * Redirect legacy member edit with query parameter
     * From: /members/edit?id={id} → To: /members/{id}/edit
     */
    public function memberEditQuery() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("members/$id/edit");
        } else {
            $this->permanentRedirect('members');
        }
    }
    
    /**
     * Redirect legacy member update to RESTful pattern
     * From: POST /members/update → To: PUT /members/{id}
     */
    public function memberUpdate() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;
        
        if ($id && is_numeric($id)) {
            // For POST requests, we need to handle the form submission
            // Convert POST to PUT by adding _method parameter
            $_POST['_method'] = 'PUT';
            
            // Forward to the actual update method
            require_once 'controllers/MemberController.php';
            $memberController = new MemberController();
            $memberController->update($id);
        } else {
            set_flash_message('Invalid member ID for update', 'danger');
            redirect('members');
        }
    }
    
    /**
     * Redirect legacy member delete to RESTful pattern
     * From: POST /members/delete → To: DELETE /members/{id}
     */
    public function memberDelete() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;
        
        if ($id && is_numeric($id)) {
            // For POST requests, we need to handle the form submission
            // Convert POST to DELETE by adding _method parameter
            $_POST['_method'] = 'DELETE';
            
            // Forward to the actual delete method
            require_once 'controllers/MemberController.php';
            $memberController = new MemberController();
            $memberController->delete($id);
        } else {
            set_flash_message('Invalid member ID for deletion', 'danger');
            redirect('members');
        }
    }
    
    /**
     * Redirect legacy group routes
     * From: /groups/members/{id} → To: /groups/{id}/members
     */
    public function groupMembers($id = null) {
        if ($id) {
            $this->permanentRedirect("groups/$id/members");
        } else {
            $this->permanentRedirect('groups');
        }
    }
    
    /**
     * Redirect legacy group edit routes
     * From: /groups/edit/{id} → To: /groups/{id}/edit
     */
    public function groupEdit($id = null) {
        if ($id) {
            $this->permanentRedirect("groups/$id/edit");
        } else {
            $this->permanentRedirect('groups');
        }
    }
    
    /**
     * Redirect legacy visitor routes
     * From: /visitors/view?id={id} → To: /visitors/{id}
     */
    public function visitorView() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("visitors/$id");
        } else {
            $this->permanentRedirect('visitors');
        }
    }

    /**
     * Redirect legacy visitor edit with query parameter
     * From: /visitors/edit?id={id} → To: /visitors/{id}/edit
     */
    public function visitorEdit() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("visitors/$id/edit");
        } else {
            $this->permanentRedirect('visitors');
        }
    }

    /**
     * Redirect legacy visitor store to RESTful pattern
     * From: POST /visitors/store → To: POST /visitors
     */
    public function visitorStore() {
        // Forward to the actual store method
        require_once 'controllers/VisitorController.php';
        $visitorController = new VisitorController();
        $visitorController->store();
    }

    /**
     * Redirect legacy visitor update to RESTful pattern
     * From: POST /visitors/update → To: PUT /visitors/{id}
     */
    public function visitorUpdate() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;

        if ($id && is_numeric($id)) {
            // Convert POST to PUT by adding _method parameter
            $_POST['_method'] = 'PUT';

            // Forward to the actual update method
            require_once 'controllers/VisitorController.php';
            $visitorController = new VisitorController();
            $visitorController->update($id);
        } else {
            set_flash_message('Invalid visitor ID for update', 'danger');
            redirect('visitors');
        }
    }

    /**
     * Redirect legacy SMS routes
     * From: POST /sms/send → To: POST /sms
     */
    public function smsSend() {
        // Forward to the actual send method
        require_once 'controllers/SmsController.php';
        $smsController = new SmsController();
        $smsController->send();
    }

    /**
     * Redirect legacy SMS view with query parameter
     * From: /sms/view?id={id} → To: /sms/messages/{id}
     */
    public function smsView() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("sms/messages/$id");
        } else {
            $this->permanentRedirect('sms');
        }
    }

    /**
     * Redirect legacy SMS delete to RESTful pattern
     * From: POST /sms/delete → To: DELETE /sms/messages/{id}
     */
    public function smsDelete() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;

        if ($id && is_numeric($id)) {
            // Convert POST to DELETE by adding _method parameter
            $_POST['_method'] = 'DELETE';

            // Forward to the actual delete method
            require_once 'controllers/SmsController.php';
            $smsController = new SmsController();
            $smsController->delete($id);
        } else {
            set_flash_message('Invalid message ID for deletion', 'danger');
            redirect('sms');
        }
    }
    
    /**
     * Redirect legacy attendance routes
     * From: /attendance/add → To: /attendance/qr
     */
    public function attendanceAdd() {
        $this->permanentRedirect('attendance/qr');
    }

    /**
     * Redirect legacy attendance view by date
     * From: /attendance/view-by-date → To: /attendance/date
     */
    public function attendanceViewByDate() {
        $this->permanentRedirect('attendance/date');
    }

    /**
     * Redirect legacy attendance member with query parameter
     * From: /attendance/member?id={id} → To: /attendance/members/{id}
     */
    public function attendanceMember() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("attendance/members/$id");
        } else {
            $this->permanentRedirect('attendance');
        }
    }

    /**
     * Redirect legacy attendance edit with query parameter
     * From: /attendance/edit?id={id} → To: /attendance/{id}/edit
     */
    public function attendanceEdit() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("attendance/$id/edit");
        } else {
            $this->permanentRedirect('attendance');
        }
    }

    /**
     * Redirect legacy attendance update to RESTful pattern
     * From: POST /attendance/update → To: PUT /attendance/{id}
     */
    public function attendanceUpdate() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;

        if ($id && is_numeric($id)) {
            // Convert POST to PUT by adding _method parameter
            $_POST['_method'] = 'PUT';

            // Forward to the actual update method
            require_once 'controllers/AttendanceController.php';
            $attendanceController = new AttendanceController();
            $attendanceController->update($id);
        } else {
            set_flash_message('Invalid attendance ID for update', 'danger');
            redirect('attendance');
        }
    }
    
    /**
     * Redirect legacy finance routes
     * From: /finance/add (without 's') → To: /finances/add (with 's')
     */
    public function financeAdd() {
        $this->permanentRedirect('finances/add');
    }
    
    /**
     * Redirect legacy equipment routes
     * From: /equipment/add → To: /equipment/create
     */
    public function equipmentAdd() {
        $this->permanentRedirect('equipment/create');
    }

    /**
     * Redirect legacy equipment view with query parameter
     * From: /equipment/view?id={id} → To: /equipment/{id}
     */
    public function equipmentView() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("equipment/$id");
        } else {
            $this->permanentRedirect('equipment');
        }
    }

    /**
     * Redirect legacy equipment edit with query parameter
     * From: /equipment/edit?id={id} → To: /equipment/{id}/edit
     */
    public function equipmentEdit() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("equipment/$id/edit");
        } else {
            $this->permanentRedirect('equipment');
        }
    }

    /**
     * Redirect legacy equipment store to RESTful pattern
     * From: POST /equipment/store → To: POST /equipment
     */
    public function equipmentStore() {
        // Forward to the actual store method
        require_once 'controllers/EquipmentController.php';
        $equipmentController = new EquipmentController();
        $equipmentController->store();
    }

    /**
     * Redirect legacy equipment update to RESTful pattern
     * From: POST /equipment/update → To: PUT /equipment/{id}
     */
    public function equipmentUpdate() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;

        if ($id && is_numeric($id)) {
            // Convert POST to PUT by adding _method parameter
            $_POST['_method'] = 'PUT';

            // Forward to the actual update method
            require_once 'controllers/EquipmentController.php';
            $equipmentController = new EquipmentController();
            $equipmentController->update($id);
        } else {
            set_flash_message('Invalid equipment ID for update', 'danger');
            redirect('equipment');
        }
    }

    /**
     * Redirect legacy equipment delete to RESTful pattern
     * From: POST /equipment/delete → To: DELETE /equipment/{id}
     */
    public function equipmentDelete() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;

        if ($id && is_numeric($id)) {
            // Convert POST to DELETE by adding _method parameter
            $_POST['_method'] = 'DELETE';

            // Forward to the actual delete method
            require_once 'controllers/EquipmentController.php';
            $equipmentController = new EquipmentController();
            $equipmentController->delete($id);
        } else {
            set_flash_message('Invalid equipment ID for deletion', 'danger');
            redirect('equipment');
        }
    }

    /**
     * Redirect legacy equipment QR code with query parameter
     * From: /equipment/qrcode?id={id} → To: /equipment/{id}/qrcode
     */
    public function equipmentQrCode() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("equipment/$id/qrcode");
        } else {
            $this->permanentRedirect('equipment');
        }
    }
    
    /**
     * Redirect legacy user routes
     * From: /users/add → To: /users/create
     */
    public function userAdd() {
        $this->permanentRedirect('users/create');
    }
    
    /**
     * Redirect legacy service routes
     * From: /services/add → To: /services/create
     */
    public function serviceAdd() {
        $this->permanentRedirect('services/create');
    }

    /**
     * Redirect legacy service edit routes
     * From: /services/edit/{id} → To: /services/{id}/edit
     */
    public function serviceEdit($id = null) {
        if ($id) {
            $this->permanentRedirect("services/$id/edit");
        } else {
            $this->permanentRedirect('services');
        }
    }

    /**
     * Redirect legacy service store to RESTful pattern
     * From: POST /services/store → To: POST /services
     */
    public function serviceStore() {
        // Forward to the actual store method
        require_once 'controllers/ServiceController.php';
        $serviceController = new ServiceController();
        $serviceController->store();
    }

    /**
     * Redirect legacy service update to RESTful pattern
     * From: POST /services/update → To: PUT /services/{id}
     */
    public function serviceUpdate() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;

        if ($id && is_numeric($id)) {
            // Convert POST to PUT by adding _method parameter
            $_POST['_method'] = 'PUT';

            // Forward to the actual update method
            require_once 'controllers/ServiceController.php';
            $serviceController = new ServiceController();
            $serviceController->update($id);
        } else {
            set_flash_message('Invalid service ID for update', 'danger');
            redirect('services');
        }
    }

    /**
     * Redirect legacy service delete to RESTful pattern
     * From: POST /services/delete → To: DELETE /services/{id}
     */
    public function serviceDelete() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;

        if ($id && is_numeric($id)) {
            // Convert POST to DELETE by adding _method parameter
            $_POST['_method'] = 'DELETE';

            // Forward to the actual delete method
            require_once 'controllers/ServiceController.php';
            $serviceController = new ServiceController();
            $serviceController->delete($id);
        } else {
            set_flash_message('Invalid service ID for deletion', 'danger');
            redirect('services');
        }
    }
    
    /**
     * Perform a permanent redirect (301)
     * 
     * @param string $path The path to redirect to
     */
    private function permanentRedirect($path) {
        $url = url($path);
        
        // Log the redirect for monitoring
        error_log("LEGACY_REDIRECT: {$_SERVER['REQUEST_URI']} → $url");
        
        // Set flash message to inform about URL change
        set_flash_message('You have been redirected to the new URL format.', 'info');
        
        // Perform 301 redirect
        header("HTTP/1.1 301 Moved Permanently");
        header("Location: $url");
        exit;
    }
    
    /**
     * Perform a temporary redirect (302) for testing
     * 
     * @param string $path The path to redirect to
     */
    private function temporaryRedirect($path) {
        $url = url($path);
        
        // Log the redirect for monitoring
        error_log("LEGACY_REDIRECT_TEMP: {$_SERVER['REQUEST_URI']} → $url");
        
        // Perform 302 redirect
        header("Location: $url");
        exit;
    }
    
    /**
     * Handle unknown legacy routes
     */
    public function notFound() {
        // Log the unknown route
        error_log("LEGACY_ROUTE_NOT_FOUND: {$_SERVER['REQUEST_URI']}");

        set_flash_message('The requested page has been moved or no longer exists.', 'warning');
        redirect('dashboard');
    }

    // ===================================================================
    // PROGRAMS LEGACY REDIRECTS
    // ===================================================================

    /**
     * Redirect legacy programs list route
     * From: /programs/list → To: /programs
     */
    public function programsList() {
        $this->permanentRedirect('programs');
    }

    /**
     * Redirect legacy programs store route
     * From: POST /programs/store → To: POST /programs
     */
    public function programsStore() {
        $this->forwardPost('programs');
    }

    /**
     * Redirect legacy programs show route
     * From: /programs/show?id={id} → To: /programs/{id}
     */
    public function programsShow() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("programs/$id");
        } else {
            $this->permanentRedirect('programs');
        }
    }

    /**
     * Redirect legacy programs edit route
     * From: /programs/edit?id={id} → To: /programs/{id}/edit
     */
    public function programsEdit() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("programs/$id/edit");
        } else {
            $this->permanentRedirect('programs');
        }
    }

    /**
     * Redirect legacy programs update route
     * From: POST /programs/update → To: PUT /programs/{id}
     */
    public function programsUpdate() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->forwardPut("programs/$id");
        } else {
            set_flash_message('Invalid program ID for update.', 'danger');
            redirect('programs');
        }
    }

    /**
     * Redirect legacy programs delete route
     * From: POST /programs/delete → To: DELETE /programs/{id}
     */
    public function programsDelete() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->forwardDelete("programs/$id");
        } else {
            set_flash_message('Invalid program ID for deletion.', 'danger');
            redirect('programs');
        }
    }

    /**
     * Redirect legacy programs update status route
     * From: POST /programs/update-status → To: PUT /programs/{id}/status
     */
    public function programsUpdateStatus() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->forwardPut("programs/$id/status");
        } else {
            set_flash_message('Invalid program ID for status update.', 'danger');
            redirect('programs');
        }
    }

    // ===================================================================
    // FINANCE LEGACY REDIRECTS (finance → finances)
    // ===================================================================

    /**
     * Redirect legacy finance index route
     * From: /finance → To: /finances
     */
    public function financeIndex() {
        $this->permanentRedirect('finances');
    }

    /**
     * Redirect legacy finance categories route
     * From: /finance/categories → To: /finances/categories
     */
    public function financeCategories() {
        $this->permanentRedirect('finances/categories');
    }

    /**
     * Redirect legacy finance edit route
     * From: /finance/edit → To: /finances/edit
     */
    public function financeEdit() {
        // Preserve query parameters
        $queryString = $_SERVER['QUERY_STRING'] ?? '';
        $url = 'finances/edit' . ($queryString ? '?' . $queryString : '');
        $this->permanentRedirect($url);
    }

    /**
     * Redirect legacy finance store route
     * From: POST /finance/store → To: POST /finances/store
     */
    public function financeStore() {
        $this->forwardPost('finances/store');
    }

    /**
     * Redirect legacy finance update route
     * From: POST /finance/update → To: POST /finances/update
     */
    public function financeUpdate() {
        $this->forwardPost('finances/update');
    }

    /**
     * Redirect legacy finance delete route
     * From: POST /finance/delete → To: POST /finances/delete
     */
    public function financeDelete() {
        $this->forwardPost('finances/delete');
    }

    /**
     * Redirect legacy finance dashboard tithe route
     * From: /finance/dashboard/tithe → To: /finances/dashboard/tithe
     */
    public function financeDashboardTithe() {
        $this->permanentRedirect('finances/dashboard/tithe');
    }

    /**
     * Redirect legacy finance dashboard pledge route
     * From: /finance/dashboard/pledge → To: /finances/dashboard/pledge
     */
    public function financeDashboardPledge() {
        $this->permanentRedirect('finances/dashboard/pledge');
    }

    /**
     * Redirect legacy finance dashboard pastor application route
     * From: /finance/dashboard/pastor-application → To: /finances/dashboard/pastor-application
     */
    public function financeDashboardPastorApp() {
        $this->permanentRedirect('finances/dashboard/pastor-application');
    }

    /**
     * Redirect legacy finance dashboard category route
     * From: /finance/dashboard/category → To: /finances/dashboard/category
     */
    public function financeDashboardCategory() {
        // Preserve query parameters
        $queryString = $_SERVER['QUERY_STRING'] ?? '';
        $url = 'finances/dashboard/category' . ($queryString ? '?' . $queryString : '');
        $this->permanentRedirect($url);
    }

    /**
     * Redirect legacy finance member tithe history route
     * From: /finance/member-tithe-history → To: /finances/member-tithe-history
     */
    public function financeMemberTitheHistory() {
        // Preserve query parameters
        $queryString = $_SERVER['QUERY_STRING'] ?? '';
        $url = 'finances/member-tithe-history' . ($queryString ? '?' . $queryString : '');
        $this->permanentRedirect($url);
    }

    // ===================================================================
    // USERS LEGACY REDIRECTS (RPC → RESTful)
    // ===================================================================

    /**
     * Redirect legacy users add route
     * From: /users/add → To: /users/create
     */
    public function usersAdd() {
        $this->permanentRedirect('users/create');
    }

    /**
     * Redirect legacy users edit route
     * From: /users/edit?id=123 → To: /users/123/edit
     */
    public function usersEdit() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect('users/' . $id . '/edit');
        } else {
            $this->permanentRedirect('users');
        }
    }

    // ===================================================================
    // MINISTRY DEPARTMENTS LEGACY REDIRECTS (RPC → RESTful)
    // ===================================================================

    /**
     * Redirect legacy ministry department edit route
     * From: /ministry-departments/edit?id=123 → To: /ministry-departments/123/edit
     */
    public function ministryDepartmentEdit() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect('ministry-departments/' . $id . '/edit');
        } else {
            $this->permanentRedirect('ministry-departments');
        }
    }

    // ===================================================================
    // WELFARE LEGACY REDIRECTS (RPC → RESTful)
    // ===================================================================

    /**
     * Redirect legacy welfare add route
     * From: /welfare/add → To: /welfare/create
     */
    public function welfareAdd() {
        $this->permanentRedirect('welfare/create');
    }

    // ===================================================================
    // CATEGORIES LEGACY REDIRECTS
    // ===================================================================

    /**
     * Redirect legacy categories store route
     * From: POST /categories/store → To: POST /categories
     */
    public function categoriesStore() {
        $this->forwardPost('categories');
    }

    /**
     * Redirect legacy categories edit route
     * From: /categories/edit?id={id} → To: /categories/{id}/edit
     */
    public function categoriesEdit() {
        $id = $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->permanentRedirect("categories/$id/edit");
        } else {
            $this->permanentRedirect('categories');
        }
    }

    /**
     * Redirect legacy categories update route
     * From: POST /categories/update → To: PUT /categories/{id}
     */
    public function categoriesUpdate() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->forwardPut("categories/$id");
        } else {
            set_flash_message('Invalid category ID for update.', 'danger');
            redirect('categories');
        }
    }

    /**
     * Redirect legacy categories delete route
     * From: POST /categories/delete → To: DELETE /categories/{id}
     */
    public function categoriesDelete() {
        $id = $_POST['id'] ?? $_GET['id'] ?? null;
        if ($id && is_numeric($id)) {
            $this->forwardDelete("categories/$id");
        } else {
            set_flash_message('Invalid category ID for deletion.', 'danger');
            redirect('categories');
        }
    }
}
