# ICGC Emmanuel Temple Database Setup

This directory contains the database schema and setup scripts for the ICGC Emmanuel Temple Church Management System.

## Files

- `schema.sql` - The SQL schema file containing all table definitions and initial data
- `create_db.php` - Core PHP script that creates the database and imports the schema
- `setup_cli.php` - Command-line interface for database setup

## Setup Options

### Web Interface

The easiest way to set up the database is to use the web interface:

1. Navigate to `http://localhost/icgc/database_setup.php` in your browser
2. Enter your database credentials (or use the defaults)
3. Click "Create Database & Import Schema"
4. Follow the on-screen instructions

### Command Line

You can also set up the database from the command line:

```bash
# Navigate to the project root directory
cd /path/to/icgc

# Run the setup script with default settings
php database/setup_cli.php

# Or specify custom database settings
php database/setup_cli.php --host=localhost --name=icgc_db --user=root --pass=yourpassword
```

### Manual Setup

If you prefer to set up the database manually:

1. Create a database named `icgc_db` (or your preferred name)
2. Import the schema file:
   ```
   mysql -u username -p icgc_db < database/schema.sql
   ```
3. Update the database connection settings in `config/database.php`

## Default Admin Account

After setup, you can log in with the default admin account:

- Username: `admin`
- Password: `admin123`

**Important:** Change the default password immediately after your first login.

## Troubleshooting

If you encounter any issues during setup:

1. Make sure your MySQL server is running
2. Verify that the provided database credentials are correct
3. Ensure the web server has write permissions to the necessary directories
4. Check the PHP error logs for more detailed error messages
