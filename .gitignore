# ICGC Church Management System - Production .gitignore

# ===================================================================
# SECURITY: Debug and Test Files (NEVER COMMIT TO PRODUCTION)
# ===================================================================

# Debug files
debug_*.php
*_debug.php
debug.php
debug.html

# Test files  
test_*.php
*_test.php
test.php
test.html

# Temporary files
temp_*.php
tmp_*.php
*_temp.php
*_tmp.php

# Fix files (should be integrated, not left as separate files)
fix_*.php
*_fix.php
emergency_*.php
quick_*.php

# Verification files
verify_*.php
*_verify.php
verification_*.php
*_verification.php

# Diagnostic files
diagnose_*.php
*_diagnostic.php
inspect_*.php
*_inspect.php

# Comprehensive test files
comprehensive_*.php
*_comprehensive.php
final_*.php
*_final.php

# Standalone test files
standalone_*.php
*_standalone.php
simple_*.php (when used for testing)

# Recovery files
recovery_*.php
*_recovery.php

# ===================================================================
# ENVIRONMENT & CONFIGURATION
# ===================================================================

# Environment files
.env
.env.local
.env.production
.env.staging
.env.development

# Configuration overrides
config/local.php
config/production.php
config/development.php

# ===================================================================
# LOGS & CACHE
# ===================================================================

# Log files
logs/*.log
*.log
error_log
access_log

# Cache files
cache/*
!cache/.gitkeep
tmp/*
!tmp/.gitkeep

# ===================================================================
# UPLOADS & USER DATA
# ===================================================================

# User uploads (configure based on your backup strategy)
uploads/members/*
uploads/profile_pictures/*
!uploads/members/.gitkeep
!uploads/profile_pictures/.gitkeep

# Backup files
backups/*
*.sql
*.dump

# ===================================================================
# VENDOR & DEPENDENCIES
# ===================================================================

# Composer
vendor/
composer.phar

# Node modules (if using)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===================================================================
# IDE & EDITOR FILES
# ===================================================================

# PhpStorm
.idea/
*.iml

# VS Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# ===================================================================
# OPERATING SYSTEM FILES
# ===================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*

# Linux
*~
.directory

# ===================================================================
# SECURITY SENSITIVE FILES
# ===================================================================

# Database dumps with real data
*_production.sql
*_live.sql
*_backup.sql

# API keys and secrets
api_keys.php
secrets.php
credentials.php

# SSL certificates
*.pem
*.key
*.crt
*.csr

# ===================================================================
# DEVELOPMENT TOOLS
# ===================================================================

# PHPUnit
phpunit.xml
.phpunit.result.cache

# Xdebug
xdebug.log

# PHP CS Fixer
.php_cs.cache

# Psalm
psalm.xml

# ===================================================================
# DEPLOYMENT FILES
# ===================================================================

# Deployment scripts (should be in separate repo)
deploy.php
deploy.sh
deployment/

# Docker (if used)
docker-compose.override.yml
.dockerignore

# ===================================================================
# DOCUMENTATION ARTIFACTS
# ===================================================================

# Generated documentation
docs/generated/
phpdoc/

# Markdown artifacts
*.md~ 

# ===================================================================
# CUSTOM PROJECT EXCLUSIONS
# ===================================================================

# Add any project-specific files that should not be in production
# Example:
# custom_debug_tools/
# development_utilities/
