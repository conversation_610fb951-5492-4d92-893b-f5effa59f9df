<?php
/**
 * Children Medical Information Model
 * Manages medical information for children in the ministry
 */

class ChildrenMedicalInfo {
    // Database connection and table name
    private $conn;
    private $table_name = "children_medical_info";

    // Object properties
    public $id;
    public $child_id;
    public $allergies;
    public $medical_conditions;
    public $medications;
    public $special_needs;
    public $emergency_medical_contact;
    public $emergency_medical_phone;
    public $doctor_name;
    public $doctor_phone;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create medical information
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET child_id = :child_id,
                      allergies = :allergies,
                      medical_conditions = :medical_conditions,
                      medications = :medications,
                      special_needs = :special_needs,
                      emergency_medical_contact = :emergency_medical_contact,
                      emergency_medical_phone = :emergency_medical_phone,
                      doctor_name = :doctor_name,
                      doctor_phone = :doctor_phone,
                      created_at = :created_at,
                      updated_at = :updated_at";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->child_id = htmlspecialchars(strip_tags($this->child_id));
        $this->allergies = htmlspecialchars(strip_tags($this->allergies));
        $this->medical_conditions = htmlspecialchars(strip_tags($this->medical_conditions));
        $this->medications = htmlspecialchars(strip_tags($this->medications));
        $this->special_needs = htmlspecialchars(strip_tags($this->special_needs));
        $this->emergency_medical_contact = htmlspecialchars(strip_tags($this->emergency_medical_contact));
        $this->emergency_medical_phone = htmlspecialchars(strip_tags($this->emergency_medical_phone));
        $this->doctor_name = htmlspecialchars(strip_tags($this->doctor_name));
        $this->doctor_phone = htmlspecialchars(strip_tags($this->doctor_phone));

        // Bind data
        $stmt->bindParam(':child_id', $this->child_id);
        $stmt->bindParam(':allergies', $this->allergies);
        $stmt->bindParam(':medical_conditions', $this->medical_conditions);
        $stmt->bindParam(':medications', $this->medications);
        $stmt->bindParam(':special_needs', $this->special_needs);
        $stmt->bindParam(':emergency_medical_contact', $this->emergency_medical_contact);
        $stmt->bindParam(':emergency_medical_phone', $this->emergency_medical_phone);
        $stmt->bindParam(':doctor_name', $this->doctor_name);
        $stmt->bindParam(':doctor_phone', $this->doctor_phone);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Get medical information by child ID
     *
     * @param int $child_id
     * @return array|false
     */
    public function getByChildId($child_id) {
        $query = "SELECT cmi.*, 
                         m.first_name, m.last_name, m.date_of_birth,
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
                  FROM " . $this->table_name . " cmi
                  JOIN members m ON cmi.child_id = m.id
                  WHERE cmi.child_id = :child_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get all children with medical information
     *
     * @return PDOStatement
     */
    public function getAllWithMedicalInfo() {
        $query = "SELECT cmi.*, 
                         m.first_name, m.last_name, m.date_of_birth, m.profile_picture,
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
                  FROM " . $this->table_name . " cmi
                  JOIN members m ON cmi.child_id = m.id
                  WHERE m.member_status = 'active'
                  ORDER BY m.last_name, m.first_name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get children with specific medical conditions
     *
     * @param string $condition_type (allergies, medical_conditions, medications, special_needs)
     * @return PDOStatement
     */
    public function getChildrenWithConditions($condition_type = 'allergies') {
        $valid_types = ['allergies', 'medical_conditions', 'medications', 'special_needs'];
        
        if (!in_array($condition_type, $valid_types)) {
            $condition_type = 'allergies';
        }

        $query = "SELECT cmi.*, 
                         m.first_name, m.last_name, m.date_of_birth, m.profile_picture,
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
                  FROM " . $this->table_name . " cmi
                  JOIN members m ON cmi.child_id = m.id
                  WHERE cmi.{$condition_type} IS NOT NULL 
                  AND cmi.{$condition_type} != ''
                  AND m.member_status = 'active'
                  ORDER BY m.last_name, m.first_name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Update medical information
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET allergies = :allergies,
                      medical_conditions = :medical_conditions,
                      medications = :medications,
                      special_needs = :special_needs,
                      emergency_medical_contact = :emergency_medical_contact,
                      emergency_medical_phone = :emergency_medical_phone,
                      doctor_name = :doctor_name,
                      doctor_phone = :doctor_phone,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->allergies = htmlspecialchars(strip_tags($this->allergies));
        $this->medical_conditions = htmlspecialchars(strip_tags($this->medical_conditions));
        $this->medications = htmlspecialchars(strip_tags($this->medications));
        $this->special_needs = htmlspecialchars(strip_tags($this->special_needs));
        $this->emergency_medical_contact = htmlspecialchars(strip_tags($this->emergency_medical_contact));
        $this->emergency_medical_phone = htmlspecialchars(strip_tags($this->emergency_medical_phone));
        $this->doctor_name = htmlspecialchars(strip_tags($this->doctor_name));
        $this->doctor_phone = htmlspecialchars(strip_tags($this->doctor_phone));

        // Bind data
        $stmt->bindParam(':allergies', $this->allergies);
        $stmt->bindParam(':medical_conditions', $this->medical_conditions);
        $stmt->bindParam(':medications', $this->medications);
        $stmt->bindParam(':special_needs', $this->special_needs);
        $stmt->bindParam(':emergency_medical_contact', $this->emergency_medical_contact);
        $stmt->bindParam(':emergency_medical_phone', $this->emergency_medical_phone);
        $stmt->bindParam(':doctor_name', $this->doctor_name);
        $stmt->bindParam(':doctor_phone', $this->doctor_phone);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Delete medical information
     *
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Check if medical information exists for child
     *
     * @param int $child_id
     * @return bool
     */
    public function existsForChild($child_id) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE child_id = :child_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    /**
     * Get medical information statistics
     *
     * @return array
     */
    public function getMedicalStats() {
        $query = "SELECT 
                    COUNT(*) as total_children_with_medical_info,
                    COUNT(CASE WHEN allergies IS NOT NULL AND allergies != '' THEN 1 END) as children_with_allergies,
                    COUNT(CASE WHEN medical_conditions IS NOT NULL AND medical_conditions != '' THEN 1 END) as children_with_conditions,
                    COUNT(CASE WHEN medications IS NOT NULL AND medications != '' THEN 1 END) as children_with_medications,
                    COUNT(CASE WHEN special_needs IS NOT NULL AND special_needs != '' THEN 1 END) as children_with_special_needs
                  FROM " . $this->table_name . " cmi
                  JOIN members m ON cmi.child_id = m.id
                  WHERE m.member_status = 'active'";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create or update medical information
     *
     * @return bool
     */
    public function createOrUpdate() {
        if ($this->existsForChild($this->child_id)) {
            // Get existing record ID
            $query = "SELECT id FROM " . $this->table_name . " WHERE child_id = :child_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':child_id', $this->child_id);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->id = $result['id'];

            return $this->update();
        } else {
            return $this->create();
        }
    }
}
