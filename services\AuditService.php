<?php
/**
 * Audit Service
 * 
 * Handles audit logging and tracking of user actions.
 * Provides comprehensive audit trail for compliance and debugging.
 * 
 * @package Services
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

class AuditService
{
    /**
     * @var PDO Database connection
     */
    private $db;

    /**
     * @var string Current user ID
     */
    private $currentUserId;

    /**
     * @var string Current user IP
     */
    private $currentUserIp;

    /**
     * AuditService constructor
     * 
     * @param PDO $db Database connection
     */
    public function __construct($db)
    {
        $this->db = $db;
        $this->currentUserId = $_SESSION['user_id'] ?? null;
        $this->currentUserIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        // Create audit table if it doesn't exist
        $this->createAuditTableIfNotExists();
    }

    /**
     * Log category creation
     * 
     * @param int $categoryId Created category ID
     * @param array $categoryData Category data
     * @return void
     */
    public function logCategoryCreation(int $categoryId, array $categoryData): void
    {
        $this->logAction('category_created', 'custom_finance_categories', $categoryId, [
            'action' => 'CREATE',
            'new_data' => $categoryData,
            'category_name' => $categoryData['name'] ?? 'unknown',
            'category_type' => $categoryData['category_type'] ?? 'unknown'
        ]);
    }

    /**
     * Log category update
     * 
     * @param int $categoryId Updated category ID
     * @param object $oldData Old category data
     * @param array $newData New category data
     * @return void
     */
    public function logCategoryUpdate(int $categoryId, object $oldData, array $newData): void
    {
        // Calculate what changed
        $changes = [];
        foreach ($newData as $field => $newValue) {
            $oldValue = $oldData->$field ?? null;
            if ($oldValue != $newValue) {
                $changes[$field] = [
                    'old' => $oldValue,
                    'new' => $newValue
                ];
            }
        }
        
        $this->logAction('category_updated', 'custom_finance_categories', $categoryId, [
            'action' => 'UPDATE',
            'changes' => $changes,
            'category_name' => $oldData->name ?? 'unknown',
            'category_type' => $oldData->category_type ?? 'unknown'
        ]);
    }

    /**
     * Log category deletion
     * 
     * @param int $categoryId Deleted category ID
     * @param object $categoryData Category data before deletion
     * @return void
     */
    public function logCategoryDeletion(int $categoryId, object $categoryData): void
    {
        $this->logAction('category_deleted', 'custom_finance_categories', $categoryId, [
            'action' => 'DELETE',
            'deleted_data' => (array)$categoryData,
            'category_name' => $categoryData->name ?? 'unknown',
            'category_type' => $categoryData->category_type ?? 'unknown'
        ]);
    }

    /**
     * Log category status change
     * 
     * @param int $categoryId Category ID
     * @param bool $oldStatus Old status
     * @param bool $newStatus New status
     * @return void
     */
    public function logCategoryStatusChange(int $categoryId, bool $oldStatus, bool $newStatus): void
    {
        $action = $newStatus ? 'category_activated' : 'category_deactivated';
        
        $this->logAction($action, 'custom_finance_categories', $categoryId, [
            'action' => 'STATUS_CHANGE',
            'old_status' => $oldStatus,
            'new_status' => $newStatus
        ]);
    }

    /**
     * Log transaction creation
     * 
     * @param int $transactionId Created transaction ID
     * @param array $transactionData Transaction data
     * @return void
     */
    public function logTransactionCreation(int $transactionId, array $transactionData): void
    {
        $this->logAction('transaction_created', 'finances', $transactionId, [
            'action' => 'CREATE',
            'new_data' => $transactionData,
            'amount' => $transactionData['amount'] ?? 0,
            'category' => $transactionData['category'] ?? 'unknown'
        ]);
    }

    /**
     * Log transaction update
     * 
     * @param int $transactionId Updated transaction ID
     * @param object $oldData Old transaction data
     * @param array $newData New transaction data
     * @return void
     */
    public function logTransactionUpdate(int $transactionId, object $oldData, array $newData): void
    {
        // Calculate what changed
        $changes = [];
        foreach ($newData as $field => $newValue) {
            $oldValue = $oldData->$field ?? null;
            if ($oldValue != $newValue) {
                $changes[$field] = [
                    'old' => $oldValue,
                    'new' => $newValue
                ];
            }
        }
        
        $this->logAction('transaction_updated', 'finances', $transactionId, [
            'action' => 'UPDATE',
            'changes' => $changes,
            'amount' => $oldData->amount ?? 0,
            'category' => $oldData->category ?? 'unknown'
        ]);
    }

    /**
     * Log transaction deletion
     * 
     * @param int $transactionId Deleted transaction ID
     * @param object $transactionData Transaction data before deletion
     * @return void
     */
    public function logTransactionDeletion(int $transactionId, object $transactionData): void
    {
        $this->logAction('transaction_deleted', 'finances', $transactionId, [
            'action' => 'DELETE',
            'deleted_data' => (array)$transactionData,
            'amount' => $transactionData->amount ?? 0,
            'category' => $transactionData->category ?? 'unknown'
        ]);
    }

    /**
     * Log user login
     * 
     * @param int $userId User ID
     * @param bool $successful Whether login was successful
     * @return void
     */
    public function logUserLogin(int $userId, bool $successful = true): void
    {
        $action = $successful ? 'user_login_success' : 'user_login_failed';
        
        $this->logAction($action, 'users', $userId, [
            'action' => 'LOGIN',
            'successful' => $successful,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    }

    /**
     * Log user logout
     * 
     * @param int $userId User ID
     * @return void
     */
    public function logUserLogout(int $userId): void
    {
        $this->logAction('user_logout', 'users', $userId, [
            'action' => 'LOGOUT'
        ]);
    }

    /**
     * Log security event
     * 
     * @param string $eventType Type of security event
     * @param array $details Event details
     * @return void
     */
    public function logSecurityEvent(string $eventType, array $details = []): void
    {
        $this->logAction($eventType, 'security', null, array_merge([
            'action' => 'SECURITY_EVENT',
            'event_type' => $eventType
        ], $details));
    }

    /**
     * Get audit logs for a specific record
     * 
     * @param string $tableName Table name
     * @param int $recordId Record ID
     * @param int $limit Number of logs to retrieve
     * @return array Audit logs
     */
    public function getAuditLogs(string $tableName, int $recordId, int $limit = 50): array
    {
        try {
            $query = "SELECT * FROM audit_logs 
                     WHERE table_name = ? AND record_id = ? 
                     ORDER BY created_at DESC 
                     LIMIT ?";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$tableName, $recordId, $limit]);
            
            return $stmt->fetchAll(PDO::FETCH_OBJ);
        } catch (Exception $e) {
            error_log("AuditService::getAuditLogs failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get audit logs for a user
     * 
     * @param int $userId User ID
     * @param int $limit Number of logs to retrieve
     * @return array Audit logs
     */
    public function getUserAuditLogs(int $userId, int $limit = 100): array
    {
        try {
            $query = "SELECT * FROM audit_logs 
                     WHERE user_id = ? 
                     ORDER BY created_at DESC 
                     LIMIT ?";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$userId, $limit]);
            
            return $stmt->fetchAll(PDO::FETCH_OBJ);
        } catch (Exception $e) {
            error_log("AuditService::getUserAuditLogs failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent audit logs
     * 
     * @param int $limit Number of logs to retrieve
     * @param string|null $actionType Filter by action type
     * @return array Audit logs
     */
    public function getRecentAuditLogs(int $limit = 100, ?string $actionType = null): array
    {
        try {
            $query = "SELECT * FROM audit_logs";
            $params = [];
            
            if ($actionType) {
                $query .= " WHERE action_type = ?";
                $params[] = $actionType;
            }
            
            $query .= " ORDER BY created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_OBJ);
        } catch (Exception $e) {
            error_log("AuditService::getRecentAuditLogs failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Log a generic action
     * 
     * @param string $actionType Type of action
     * @param string $tableName Table name
     * @param int|null $recordId Record ID
     * @param array $details Additional details
     * @return void
     */
    private function logAction(string $actionType, string $tableName, ?int $recordId, array $details = []): void
    {
        try {
            $query = "INSERT INTO audit_logs 
                     (action_type, table_name, record_id, user_id, ip_address, details, created_at) 
                     VALUES (?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([
                $actionType,
                $tableName,
                $recordId,
                $this->currentUserId,
                $this->currentUserIp,
                json_encode($details)
            ]);
        } catch (Exception $e) {
            // Log to error log if audit logging fails
            error_log("AuditService::logAction failed: " . $e->getMessage());
        }
    }

    /**
     * Create audit table if it doesn't exist
     * 
     * @return void
     */
    private function createAuditTableIfNotExists(): void
    {
        try {
            $query = "CREATE TABLE IF NOT EXISTS audit_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                action_type VARCHAR(100) NOT NULL,
                table_name VARCHAR(100) NOT NULL,
                record_id INT NULL,
                user_id INT NULL,
                ip_address VARCHAR(45) NOT NULL,
                details JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_action_type (action_type),
                INDEX idx_table_record (table_name, record_id),
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->db->exec($query);
        } catch (Exception $e) {
            error_log("AuditService::createAuditTableIfNotExists failed: " . $e->getMessage());
        }
    }
}
