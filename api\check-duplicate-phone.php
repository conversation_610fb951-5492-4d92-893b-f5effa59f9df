<?php
/**
 * API endpoint to check if a phone number is already registered
 * Used for duplicate detection and member lookup during registration
 *
 * Supports both duplicate checking and emergency contact intelligence
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/Member.php';
require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../utils/PhoneNumberUtils.php';

// Set JSON response header
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'error' => 'Method not allowed',
        'message' => 'Only POST requests are allowed'
    ]);
    exit;
}

try {
    // Get and validate JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(400);
        echo json_encode([
            'error' => 'Invalid JSON',
            'message' => 'Request body must be valid JSON'
        ]);
        exit;
    }

    if (!isset($input['phone']) || empty(trim($input['phone']))) {
        http_response_code(400);
        echo json_encode([
            'error' => 'Phone number is required',
            'message' => 'Please provide a phone number to check'
        ]);
        exit;
    }

    $phone = sanitize($input['phone']);

    // Normalize phone number for consistent lookup
    $normalizedPhone = PhoneNumberUtils::normalize($phone);

    // Initialize database connection (reuse connection)
    static $database = null;
    static $member = null;

    if ($database === null) {
        $database = new Database();
        $member = new Member($database->getConnection());
    }

    // Check if member exists with this phone number (try both original and normalized)
    $existingMember = $member->getByPhoneNumber($phone);

    // If not found with original, try normalized version
    if (!$existingMember && $normalizedPhone !== $phone) {
        $existingMember = $member->getByPhoneNumber($normalizedPhone);
    }

    if ($existingMember) {
        echo json_encode([
            'exists' => true,
            'member' => [
                'id' => $existingMember->id,
                'first_name' => $existingMember->first_name,
                'last_name' => $existingMember->last_name,
                'phone_number' => $existingMember->phone_number,
                'normalized_phone' => $normalizedPhone
            ],
            'message' => 'Member found with this phone number'
        ]);
    } else {
        echo json_encode([
            'exists' => false,
            'normalized_phone' => $normalizedPhone,
            'message' => 'No member found with this phone number'
        ]);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => 'An error occurred while checking the phone number'
    ]);
    error_log('Check duplicate phone API error: ' . $e->getMessage());
}
?>
