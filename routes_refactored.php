<?php
/**
 * Refactored Application Routes (RESTful)
 *
 * This file demonstrates a clean, RESTful routing structure that addresses
 * the Gemini Phase 4 review recommendations:
 *
 * Key Improvements:
 * - Uses standard HTTP verbs (GET, POST, PUT, DELETE)
 * - Removes redundant path segments like /view/ and /edit/
 * - Standardizes on plural resource names
 * - Removes legacy query-param routes
 * - Consolidates redundant route definitions
 * - Maintains security features (CSRF, role-based access)
 * - Provides backward compatibility during migration
 */

return [
    // --- Authentication (Already well-structured) ---
    ['GET', '/^login$/', 'AuthController', 'loginPage', ['auth' => false]],
    ['POST', '/^auth\/login$/', 'AuthController', 'login', ['auth' => false, 'csrf' => true]],
    ['GET', '/^logout$/', 'AuthController', 'logout', ['auth' => false]],

    // --- Dashboard ---
    ['GET', '/^dashboard$/', 'DashboardController', 'index'],

    // --- Members (RESTful) ---
    ['GET',    '/^members$/', 'MemberController', 'index'], // List all members
    ['GET',    '/^members\/create$/', 'MemberController', 'create'], // Show form to create a new member
    ['POST',   '/^members$/', 'MemberController', 'store', ['csrf' => true]], // Create a new member
    ['GET',    '/^members\/(\d+)$/', 'MemberController', 'show'], // Show a specific member
    ['GET',    '/^members\/(\d+)\/edit$/', 'MemberController', 'edit'], // Show form to edit a member
    ['PUT',    '/^members\/(\d+)$/', 'MemberController', 'update', ['csrf' => true]], // Update a specific member
    ['DELETE', '/^members\/(\d+)$/', 'MemberController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete a specific member
    
    // Member-specific actions
    ['GET',    '/^members\/search$/', 'MemberController', 'searchMembers'], // Search members
    ['GET',    '/^members\/export$/', 'MemberController', 'export'], // Export members
    ['GET',    '/^members\/import$/', 'MemberController', 'importPage', ['role' => 'admin']], // Import page
    ['POST',   '/^members\/import$/', 'MemberController', 'import', ['role' => 'admin', 'csrf' => true]], // Process import
    ['GET',    '/^members\/birthdays$/', 'MemberController', 'birthdays'], // Members with birthdays
    
    // Member API endpoints
    ['GET',    '/^api\/members$/', 'MemberController', 'getAllActiveMembers'], // API: Get all active members
    ['GET',    '/^api\/members\/details$/', 'MemberController', 'getMemberDetails'], // API: Get member details
    ['GET',    '/^api\/members\/family$/', 'MemberController', 'getFamilyMembers'], // API: Get family members

    // --- Groups (RESTful) ---
    ['GET',    '/^groups$/', 'GroupsController', 'index'], // List all groups
    ['GET',    '/^groups\/create$/', 'GroupsController', 'add'], // Show form to create a group
    ['POST',   '/^groups$/', 'GroupsController', 'create', ['csrf' => true]], // Create a new group
    ['GET',    '/^groups\/(\d+)$/', 'GroupsController', 'show'], // Show a specific group's details
    ['GET',    '/^groups\/(\d+)\/edit$/', 'GroupsController', 'edit'], // Show form to edit a group
    ['PUT',    '/^groups\/(\d+)$/', 'GroupsController', 'update', ['csrf' => true]], // Update a specific group
    ['DELETE', '/^groups\/(\d+)$/', 'GroupsController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete a specific group

    // Group Members (Nested Resource)
    ['GET',    '/^groups\/(\d+)\/members$/', 'GroupsController', 'members'], // List members of a group
    ['GET',    '/^groups\/(\d+)\/members\/add$/', 'GroupsController', 'addMembers'], // Show form to add members
    ['POST',   '/^groups\/(\d+)\/members$/', 'GroupsController', 'addMembersProcess', ['csrf' => true]], // Add members to a group
    ['DELETE', '/^groups\/(\d+)\/members\/(\d+)$/', 'GroupsController', 'removeMember', ['csrf' => true]], // Remove a member from a group
    ['PATCH',  '/^groups\/(\d+)\/members\/(\d+)\/role$/', 'GroupsController', 'updateRole', ['csrf' => true]], // Update member's role in group

    // Group Schedule (Nested Resource)
    ['POST',   '/^groups\/(\d+)\/schedule$/', 'GroupsController', 'saveSchedule', ['csrf' => true]],
    ['DELETE', '/^groups\/(\d+)\/schedule$/', 'GroupsController', 'deleteSchedule', ['csrf' => true]],

    // --- Attendance (Consolidated) ---
    ['GET',    '/^attendance$/', 'AttendanceController', 'index'], // Main attendance page
    ['GET',    '/^attendance\/date$/', 'AttendanceController', 'viewByDate'], // View by date
    ['GET',    '/^attendance\/summary$/', 'AttendanceController', 'summary'], // Attendance summary
    ['GET',    '/^attendance\/members\/(\d+)$/', 'AttendanceController', 'memberAttendance'], // Member attendance history
    ['GET',    '/^attendance\/(\d+)\/edit$/', 'AttendanceController', 'edit'], // Edit attendance record
    ['PUT',    '/^attendance\/(\d+)$/', 'AttendanceController', 'update', ['csrf' => true]], // Update attendance
    ['DELETE', '/^attendance\/(\d+)$/', 'AttendanceController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete attendance

    // QR Code Attendance (Consolidated)
    ['GET',    '/^attendance\/qr$/', 'AttendanceController', 'qrIndex'], // QR attendance main page
    ['POST',   '/^attendance\/qr\/sessions$/', 'AttendanceController', 'qrGenerate', ['csrf' => true]], // Generate QR session
    ['GET',    '/^attendance\/qr\/display$/', 'AttendanceController', 'qrDisplay'], // Display QR code
    
    // Public QR routes (token-based authentication)
    ['GET',    '/^attendance\/qr\/scan$/', 'AttendanceController', 'qrScan', ['token_auth' => true]], // QR scan page
    ['POST',   '/^attendance\/qr\/mark$/', 'AttendanceController', 'qrMark', ['token_auth' => true]], // Mark attendance via QR
    ['POST',   '/^attendance\/qr\/family$/', 'AttendanceController', 'qrGetFamily', ['token_auth' => true]], // Get family for QR
    ['POST',   '/^attendance\/qr\/mark-family$/', 'AttendanceController', 'qrMarkFamily', ['token_auth' => true]], // Mark family attendance
    ['GET',    '/^attendance\/qr\/stats$/', 'AttendanceController', 'qrStats', ['token_auth' => true]], // QR stats
    ['GET',    '/^attendance\/qr\/dashboard$/', 'AttendanceController', 'attendanceStats', ['token_auth' => true]], // QR dashboard

    // --- Services (RESTful) ---
    ['GET',    '/^services$/', 'ServiceController', 'index'], // List all services
    ['GET',    '/^services\/create$/', 'ServiceController', 'create', ['role' => 'admin']], // Show form to create service
    ['POST',   '/^services$/', 'ServiceController', 'store', ['role' => 'admin', 'csrf' => true]], // Create service
    ['GET',    '/^services\/(\d+)\/edit$/', 'ServiceController', 'edit', ['role' => 'admin']], // Edit service form
    ['PUT',    '/^services\/(\d+)$/', 'ServiceController', 'update', ['role' => 'admin', 'csrf' => true]], // Update service
    ['DELETE', '/^services\/(\d+)$/', 'ServiceController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete service

    // --- Finances (RESTful with Role Protection) ---
    ['GET',    '/^finances$/', 'FinanceController', 'index', ['role' => 'staff']], // List transactions
    ['GET',    '/^finances\/create$/', 'FinanceController', 'create', ['role' => 'staff']], // Create transaction form
    ['POST',   '/^finances$/', 'FinanceController', 'store', ['role' => 'staff', 'csrf' => true]], // Store transaction
    ['GET',    '/^finances\/(\d+)\/edit$/', 'FinanceController', 'edit', ['role' => 'staff']], // Edit transaction form
    ['PUT',    '/^finances\/(\d+)$/', 'FinanceController', 'update', ['role' => 'staff', 'csrf' => true]], // Update transaction
    ['DELETE', '/^finances\/(\d+)$/', 'FinanceController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete transaction
    
    // Finance reports and management
    ['GET',    '/^finances\/reports$/', 'FinanceController', 'report', ['role' => 'staff']], // Financial reports
    ['GET',    '/^finances\/tithes$/', 'FinanceController', 'tithes', ['role' => 'staff']], // Tithe management
    ['GET',    '/^finances\/welfare$/', 'FinanceController', 'welfare', ['role' => 'staff']], // Welfare management
    ['GET',    '/^finances\/archived$/', 'FinanceController', 'archived', ['role' => 'admin']], // Archived records
    ['POST',   '/^finances\/archive$/', 'FinanceController', 'archiveOld', ['role' => 'admin', 'csrf' => true]], // Archive old records

    // --- Equipment (RESTful) ---
    ['GET',    '/^equipment$/', 'EquipmentController', 'index'], // List equipment
    ['GET',    '/^equipment\/create$/', 'EquipmentController', 'create'], // Create equipment form
    ['POST',   '/^equipment$/', 'EquipmentController', 'store', ['csrf' => true]], // Store equipment
    ['GET',    '/^equipment\/(\d+)$/', 'EquipmentController', 'view'], // View equipment details
    ['GET',    '/^equipment\/(\d+)\/edit$/', 'EquipmentController', 'edit'], // Edit equipment form
    ['PUT',    '/^equipment\/(\d+)$/', 'EquipmentController', 'update', ['csrf' => true]], // Update equipment
    ['DELETE', '/^equipment\/(\d+)$/', 'EquipmentController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete equipment
    ['GET',    '/^equipment\/maintenance$/', 'EquipmentController', 'maintenance'], // Maintenance schedule

    // --- Visitors (RESTful) ---
    ['GET',    '/^visitors$/', 'VisitorController', 'index'], // List visitors
    ['GET',    '/^visitors\/create$/', 'VisitorController', 'create'], // Create visitor form
    ['POST',   '/^visitors$/', 'VisitorController', 'store', ['csrf' => true]], // Store visitor
    ['GET',    '/^visitors\/(\d+)$/', 'VisitorController', 'show'], // View visitor details
    ['GET',    '/^visitors\/(\d+)\/edit$/', 'VisitorController', 'edit'], // Edit visitor form
    ['PUT',    '/^visitors\/(\d+)$/', 'VisitorController', 'update', ['csrf' => true]], // Update visitor
    ['DELETE', '/^visitors\/(\d+)$/', 'VisitorController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete visitor
    
    // Visitor follow-up
    ['GET',    '/^visitors\/(\d+)\/follow-up$/', 'VisitorController', 'followUp'], // Follow-up page
    ['POST',   '/^visitors\/(\d+)\/follow-up$/', 'VisitorController', 'storeFollowUp', ['csrf' => true]], // Store follow-up
    ['DELETE', '/^visitors\/(\d+)\/follow-up\/(\d+)$/', 'VisitorController', 'deleteFollowUp', ['csrf' => true]], // Delete follow-up
    ['POST',   '/^visitors\/(\d+)\/convert$/', 'VisitorController', 'convert', ['csrf' => true]], // Convert to member

    // --- Users (RESTful with Admin Protection) ---
    ['GET',    '/^users$/', 'UserController', 'index', ['role' => 'admin']], // List users
    ['GET',    '/^users\/create$/', 'UserController', 'create', ['role' => 'admin']], // Create user form
    ['POST',   '/^users$/', 'UserController', 'store', ['role' => 'admin', 'csrf' => true]], // Store user
    ['GET',    '/^users\/(\d+)\/edit$/', 'UserController', 'edit', ['role' => 'admin']], // Edit user form
    ['PUT',    '/^users\/(\d+)$/', 'UserController', 'update', ['role' => 'admin', 'csrf' => true]], // Update user
    ['DELETE', '/^users\/(\d+)$/', 'UserController', 'delete', ['role' => 'admin', 'csrf' => true]], // Delete user
    ['PATCH',  '/^users\/(\d+)\/status$/', 'UserController', 'toggleStatus', ['role' => 'admin', 'csrf' => true]], // Toggle user status

    // --- Settings (Admin Only) ---
    ['GET',    '/^settings$/', 'SettingsController', 'index', ['role' => 'admin']], // Settings page
    ['PUT',    '/^settings$/', 'SettingsController', 'update', ['role' => 'admin', 'csrf' => true]], // Update settings
    ['POST',   '/^settings\/backup$/', 'SettingsController', 'backup', ['role' => 'admin', 'csrf' => true]], // Create backup
    ['POST',   '/^settings\/restore$/', 'SettingsController', 'restore', ['role' => 'admin', 'csrf' => true]], // Restore backup

    // --- SMS ---
    ['GET',    '/^sms$/', 'SmsController', 'index'], // SMS dashboard
    ['GET',    '/^sms\/create$/', 'SmsController', 'create'], // Create SMS form
    ['POST',   '/^sms$/', 'SmsController', 'send', ['csrf' => true]], // Send SMS

    // --- Children Ministry ---
    ['GET',    '/^children$/', 'ChildrenMinistryController', 'index'], // Children ministry dashboard
    ['GET',    '/^children\/families$/', 'ChildrenMinistryController', 'families'], // Family management
    ['POST',   '/^children\/families$/', 'ChildrenMinistryController', 'createFamily', ['csrf' => true]], // Create family
    ['DELETE', '/^children\/families\/(\d+)$/', 'ChildrenMinistryController', 'deleteFamily', ['csrf' => true]], // Delete family

    // --- Birthdays ---
    ['GET',    '/^birthdays$/', 'BirthdayController', 'index'], // Birthday dashboard

    // --- Profile ---
    ['GET',    '/^profile$/', 'ProfileController', 'index'], // User profile
    ['PUT',    '/^profile$/', 'ProfileController', 'update', ['csrf' => true]], // Update profile

    // --- Legacy Route Redirects (Temporary - for migration) ---
    ['GET',    '/^members\/view\/(\d+)$/', 'RedirectController', 'memberView'], // Redirect to /members/{id}
    ['GET',    '/^members\/view$/', 'RedirectController', 'memberViewQuery'], // Redirect ?id= to /members/{id}
    ['GET',    '/^members\/edit\/(\d+)$/', 'RedirectController', 'memberEdit'], // Redirect to /members/{id}/edit
    ['GET',    '/^members\/edit$/', 'RedirectController', 'memberEditQuery'], // Redirect ?id= to /members/{id}/edit
    ['POST',   '/^members\/update$/', 'RedirectController', 'memberUpdate'], // Redirect to PUT /members/{id}
    ['POST',   '/^members\/delete$/', 'RedirectController', 'memberDelete'], // Redirect to DELETE /members/{id}

    // Default route (must be last)
    ['GET', '/^$/', 'DashboardController', 'index'], // Empty request goes to dashboard
];
