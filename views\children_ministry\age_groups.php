<?php
/**
 * Age Groups Management View
 */

// Ensure we have the required data
if (!isset($age_groups)) {
    $age_groups = [];
}
?>

<div class="page-content-centered fade-in">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-100">
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-8 text-white relative overflow-hidden">
            <div class="absolute right-0 top-0 opacity-10">
                <i class="fas fa-layer-group text-9xl transform -rotate-12 translate-x-8 -translate-y-8"></i>
            </div>
            <h1 class="text-3xl font-bold">Age Groups Management</h1>
            <p class="mt-2 opacity-90 max-w-2xl">Configure and manage age-based groupings for effective children's ministry organization.</p>
        </div>
        <div class="p-6 bg-white">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-3 mb-4 md:mb-0">
                    <a href="<?php echo BASE_URL; ?>children-ministry" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 px-5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-md font-medium">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                    </a>
                    <button onclick="openAddAgeGroupModal()" class="bg-gradient-to-r from-primary to-primary-dark text-white py-3 px-5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-md font-medium">
                        <i class="fas fa-plus mr-2"></i> Add Age Group
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Age Groups Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <?php foreach ($age_groups as $group): ?>
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-primary">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800"><?php echo htmlspecialchars($group['name']); ?></h3>
                    <span class="<?php echo $group['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?> px-2 py-1 rounded-full text-xs font-medium">
                        <?php echo $group['is_active'] ? 'Active' : 'Inactive'; ?>
                    </span>
                </div>
                
                <div class="space-y-2 text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>Age Range:</span>
                        <span class="font-medium"><?php echo $group['min_age']; ?>-<?php echo $group['max_age']; ?> years</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Children:</span>
                        <span class="font-medium text-primary"><?php echo number_format($group['children_count']); ?></span>
                    </div>
                </div>
                
                <?php if ($group['description']): ?>
                    <p class="text-sm text-gray-600 mt-3"><?php echo htmlspecialchars($group['description']); ?></p>
                <?php endif; ?>
                
                <div class="mt-4 flex space-x-2">
                    <button onclick="editAgeGroup(<?php echo $group['id']; ?>)"
                            class="flex-1 bg-gradient-to-r from-secondary to-secondary-light text-gray-800 text-center py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md">
                        <i class="fas fa-edit mr-1"></i>
                        Edit
                    </button>
                    <a href="<?php echo BASE_URL; ?>children-ministry/children?age_group=<?php echo $group['id']; ?>"
                       class="flex-1 bg-gradient-to-r from-primary to-primary-dark text-white text-center py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md">
                        <i class="fas fa-users mr-1"></i>
                        View Children
                    </a>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Age Groups Table -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Age Groups Details</h3>
        
        <?php if (empty($age_groups)): ?>
            <div class="text-center py-8">
                <div class="text-gray-400 mb-4">
                    <i class="fas fa-layer-group text-6xl"></i>
                </div>
                <h4 class="text-xl font-semibold text-gray-600 mb-2">No Age Groups Found</h4>
                <p class="text-gray-500 mb-4">Create age groups to organize children by their ages.</p>
                <button onclick="openAddAgeGroupModal()" class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-lg inline-flex items-center">
                    <i class="fas fa-plus mr-2"></i>
                    Create First Age Group
                </button>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age Range</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Children Count</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($age_groups as $group): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($group['name']); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $group['min_age']; ?>-<?php echo $group['max_age']; ?> years</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo number_format($group['children_count']); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $group['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                        <?php echo $group['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 max-w-xs truncate"><?php echo htmlspecialchars($group['description'] ?? 'No description'); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="editAgeGroup(<?php echo $group['id']; ?>)"
                                                class="text-primary hover:text-primary-dark" title="Edit Age Group">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="<?php echo BASE_URL; ?>children-ministry/children?age_group=<?php echo $group['id']; ?>"
                                           class="text-blue-600 hover:text-blue-900" title="View Children">
                                            <i class="fas fa-users"></i>
                                        </a>
                                        <button onclick="toggleAgeGroupStatus(<?php echo $group['id']; ?>, <?php echo $group['is_active'] ? 'false' : 'true'; ?>)"
                                                class="text-gray-600 hover:text-gray-900" title="<?php echo $group['is_active'] ? 'Deactivate' : 'Activate'; ?>">
                                            <i class="fas fa-<?php echo $group['is_active'] ? 'eye-slash' : 'eye'; ?>"></i>
                                        </button>
                                        <button onclick="deleteAgeGroup(<?php echo $group['id']; ?>, '<?php echo htmlspecialchars($group['name']); ?>')"
                                                class="text-red-600 hover:text-red-900" title="Delete Age Group">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Age Group Modal -->
<div id="addAgeGroupModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Add Age Group</h3>
                <button onclick="closeAddAgeGroupModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form action="<?php echo url('children-ministry/age-groups'); ?>" method="POST" class="space-y-4">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="age_group_id" name="id" value="">

                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                        Group Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="name" name="name" required
                           placeholder="e.g., Nursery, Preschool, Elementary"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="min_age" class="block text-sm font-medium text-gray-700 mb-1">
                            Min Age <span class="text-red-500">*</span>
                        </label>
                        <input type="number" id="min_age" name="min_age" required min="0" max="17"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div>
                        <label for="max_age" class="block text-sm font-medium text-gray-700 mb-1">
                            Max Age <span class="text-red-500">*</span>
                        </label>
                        <input type="number" id="max_age" name="max_age" required min="0" max="17"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea id="description" name="description" rows="3" 
                              placeholder="Brief description of this age group..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"></textarea>
                </div>

                <div class="flex items-center">
                    <label class="flex items-center">
                        <input type="checkbox" id="is_active" name="is_active" value="1" checked class="rounded border-gray-300 text-primary focus:ring-primary">
                        <span class="ml-2 text-sm text-gray-700">Active</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeAddAgeGroupModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-md">
                        Create Age Group
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openAddAgeGroupModal() {
    // Reset form for new entry
    document.getElementById('age_group_id').value = '';
    document.querySelector('#addAgeGroupModal form').reset();

    // Reset modal title and button text for new entry
    document.querySelector('#addAgeGroupModal h3').textContent = 'Add Age Group';
    document.querySelector('#addAgeGroupModal button[type="submit"]').innerHTML = '<i class="fas fa-plus mr-2"></i>Create Age Group';

    document.getElementById('addAgeGroupModal').classList.remove('hidden');
}

function testFormData() {
    console.log('=== TESTING FORM DATA ===');

    const form = document.querySelector('#addAgeGroupModal form');
    const formData = new FormData(form);

    console.log('Form elements:');
    console.log('ID field:', document.getElementById('age_group_id').value);
    console.log('Name field:', document.getElementById('name').value);
    console.log('Min Age field:', document.getElementById('min_age').value);
    console.log('Max Age field:', document.getElementById('max_age').value);
    console.log('Description field:', document.getElementById('description').value);
    console.log('Is Active field:', document.getElementById('is_active').checked);

    console.log('FormData entries:');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: "${value}"`);
    }

    // Check if required fields have values
    const requiredFields = ['name', 'min_age', 'max_age'];
    const missingFields = [];

    requiredFields.forEach(field => {
        const value = formData.get(field);
        if (!value || value.toString().trim() === '') {
            missingFields.push(field);
        }
    });

    if (missingFields.length > 0) {
        alert('❌ Missing fields: ' + missingFields.join(', '));
    } else {
        alert('✅ All required fields have values!');
    }
}

function closeAddAgeGroupModal() {
    document.getElementById('addAgeGroupModal').classList.add('hidden');
    // Reset form
    document.querySelector('#addAgeGroupModal form').reset();
    document.getElementById('age_group_id').value = '';
}

async function editAgeGroup(groupId) {
    try {
        // Fetch data from server
        const response = await fetch(`/icgc/children-ministry/age-groups/${groupId}`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.error) {
            throw new Error(data.error);
        }

        // Populate and show modal
        populateAndShowModal(data);

    } catch (error) {
        alert('Failed to load age group: ' + error.message);
    }
}

function populateAndShowModal(data) {
    // Set modal for edit mode
    document.querySelector('#addAgeGroupModal h3').textContent = 'Edit Age Group';
    document.querySelector('#addAgeGroupModal button[type="submit"]').innerHTML = '<i class="fas fa-save mr-2"></i>Update Age Group';

    // Populate form fields
    document.getElementById('age_group_id').value = data.id;
    document.getElementById('name').value = data.name;
    document.getElementById('min_age').value = data.min_age;
    document.getElementById('max_age').value = data.max_age;
    document.getElementById('description').value = data.description || '';
    document.getElementById('is_active').checked = data.is_active == 1;

    // Show modal
    document.getElementById('addAgeGroupModal').classList.remove('hidden');
}

function deleteAgeGroup(groupId, groupName) {
    if (confirm(`Are you sure you want to delete the age group "${groupName}"?\n\nThis action cannot be undone and will only work if no children are assigned to this group.`)) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/icgc/children-ministry/age-groups/${groupId}`;

        // Add method override for DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generate_csrf_token(); ?>';
        form.appendChild(csrfInput);

        // Add age group ID
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = groupId;
        form.appendChild(idInput);

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    }
}

function toggleAgeGroupStatus(groupId, newStatus) {
    const action = newStatus === 'true' ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this age group?`)) {
        // Create a form to submit the status change
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/icgc/children-ministry/age-groups/${groupId}/toggle-status`;

        // Add method override for PATCH
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'PATCH';
        form.appendChild(methodInput);

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generate_csrf_token(); ?>';
        form.appendChild(csrfInput);

        // Add age group ID
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = groupId;
        form.appendChild(idInput);

        // Add status
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = newStatus;
        form.appendChild(statusInput);

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    }
}

// Validate age range
document.getElementById('min_age').addEventListener('change', function() {
    const maxAgeInput = document.getElementById('max_age');
    const minAge = parseInt(this.value);
    
    if (maxAgeInput.value && parseInt(maxAgeInput.value) < minAge) {
        maxAgeInput.value = minAge;
    }
    maxAgeInput.min = minAge;
});

document.getElementById('max_age').addEventListener('change', function() {
    const minAgeInput = document.getElementById('min_age');
    const maxAge = parseInt(this.value);
    
    if (minAgeInput.value && parseInt(minAgeInput.value) > maxAge) {
        minAgeInput.value = maxAge;
    }
    minAgeInput.max = maxAge;
});

// Close modal when clicking outside
document.getElementById('addAgeGroupModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAddAgeGroupModal();
    }
});
</script>
