<div class="container mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Departments</h1>
    </div>

    <!-- Departments Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php foreach ($departments as $dept_key => $dept_members) : ?>
            <?php if ($dept_key !== 'none') : ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="bg-primary-light p-4 text-white">
                        <h2 class="text-xl font-semibold"><?php echo $department_names[$dept_key]; ?></h2>
                        <p class="text-sm mt-1"><?php echo count($dept_members); ?> members</p>
                    </div>
                    <div class="p-4">
                        <?php if (empty($dept_members)) : ?>
                            <p class="text-gray-500 text-center py-4">No members in this department</p>
                        <?php else : ?>
                            <ul class="divide-y divide-gray-200">
                                <?php 
                                    // Show only first 5 members
                                    $display_members = array_slice($dept_members, 0, 5);
                                    foreach ($display_members as $member) : 
                                ?>
                                    <li class="py-2">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8">
                                                <?php if (!empty($member['profile_picture'])) : ?>
                                                    <img class="h-8 w-8 rounded-full" src="<?php echo BASE_URL . 'uploads/' . $member['profile_picture']; ?>" alt="<?php echo $member['first_name']; ?>">
                                                <?php else : ?>
                                                    <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 text-xs font-bold">
                                                        <?php echo strtoupper(substr($member['first_name'], 0, 1) . substr($member['last_name'], 0, 1)); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900"><?php echo $member['first_name'] . ' ' . $member['last_name']; ?></p>
                                                <p class="text-xs text-gray-500"><?php echo ucfirst($member['role']); ?></p>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                            
                            <?php if (count($dept_members) > 5) : ?>
                                <div class="mt-4 text-center">
                                    <a href="<?php echo BASE_URL; ?>departments/view?department=<?php echo $dept_key; ?>" class="text-primary hover:underline text-sm">
                                        View all <?php echo count($dept_members); ?> members
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 border-t">
                        <a href="<?php echo BASE_URL; ?>departments/view?department=<?php echo $dept_key; ?>" class="text-primary hover:underline text-sm flex items-center justify-center">
                            <i class="fas fa-users mr-2"></i> Manage Department
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>

    <!-- Members with No Department -->
    <div class="mt-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Members with No Department</h2>
        
        <?php if (empty($departments['none'])) : ?>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <p class="text-gray-500">All members have been assigned to departments</p>
            </div>
        <?php else : ?>
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($departments['none'] as $member) : ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <?php if (!empty($member['profile_picture'])) : ?>
                                                    <img class="h-10 w-10 rounded-full" src="<?php echo BASE_URL . 'uploads/' . $member['profile_picture']; ?>" alt="<?php echo $member['first_name']; ?>">
                                                <?php else : ?>
                                                    <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 text-sm font-bold">
                                                        <?php echo strtoupper(substr($member['first_name'], 0, 1) . substr($member['last_name'], 0, 1)); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><?php echo $member['first_name'] . ' ' . $member['last_name']; ?></div>
                                                <div class="text-sm text-gray-500"><?php echo ucfirst($member['gender']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo $member['phone_number']; ?></div>
                                        <div class="text-sm text-gray-500"><?php echo $member['email'] ?: 'No email'; ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo ucfirst($member['role']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="<?php echo BASE_URL; ?>members/edit?id=<?php echo $member['id']; ?>" class="text-indigo-600 hover:text-indigo-900">
                                            Assign Department
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
