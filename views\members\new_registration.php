<?php
/**
 * New Member Registration Form
 */

// Set page title
$page_title = 'Add New Member - ICGC Emmanuel Temple';
$active_page = 'members';

// Start output buffering
ob_start();
?>

<div class="container mx-auto px-4 fade-in">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 flex items-center">
            <span class="bg-primary-light bg-opacity-20 text-primary p-2 rounded-full mr-3">
                <i class="fas fa-user-plus"></i>
            </span>
            Add New Member
        </h1>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>members" class="flex items-center px-4 py-2 bg-primary bg-opacity-10 text-primary rounded-lg hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105 shadow-sm">
                <i class="fas fa-arrow-left mr-2"></i> Back to Members
            </a>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-lg p-6 max-w-4xl mx-auto border-t-4 border-primary relative overflow-hidden">
        <!-- Decorative elements -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-primary bg-opacity-5 rounded-full -mt-20 -mr-20"></div>
        <div class="absolute bottom-0 left-0 w-40 h-40 bg-primary bg-opacity-5 rounded-full -mb-20 -ml-20"></div>

        <div class="mb-6 pb-3 border-b border-gray-200 relative z-10">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-light text-white mr-4 shadow-md">
                        <i class="fas fa-user-plus text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-700">Add New Member Information</h3>
                        <p class="text-sm text-gray-500 mt-1">Please fill in the member details below</p>
                    </div>
                </div>

                <!-- Data Quality Indicator -->
                <div class="flex items-center space-x-4">
                    <div id="data-quality-indicator" class="flex items-center bg-gray-50 rounded-lg px-3 py-2">
                        <div class="flex items-center mr-3">
                            <div id="quality-icon" class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                            <span id="quality-text" class="text-sm font-medium text-gray-600">Data Quality</span>
                        </div>
                        <div id="completeness-score" class="text-sm font-bold text-gray-800">0%</div>
                    </div>
                    <div id="quality-suggestions" class="hidden">
                        <button type="button" onclick="showQualitySuggestions()" class="text-blue-600 hover:text-blue-800 text-sm">
                            <i class="fas fa-lightbulb mr-1"></i>Improve
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php if (isset($visitor_data)): ?>
            <!-- Conversion Banner -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-plus text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">Converting Visitor to Member</h3>
                        <p class="text-sm text-blue-700 mt-1">
                            Converting visitor: <strong><?php echo htmlspecialchars($visitor_data['first_name'] . ' ' . $visitor_data['last_name']); ?></strong>
                            <br>Contact information has been pre-filled from visitor record.
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <form action="<?php echo BASE_URL; ?>members/store" method="POST" enctype="multipart/form-data" class="space-y-8 relative z-10" onsubmit="return showValidationSummary()">
            <?php echo csrf_field(); ?>
            <?php if (isset($visitor_data)): ?>
                <input type="hidden" name="from_visitor_conversion" value="<?php echo htmlspecialchars($visitor_data['visitor_id']); ?>">
            <?php endif; ?>
            <!-- Personal Information -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <span class="bg-primary-light bg-opacity-20 text-primary p-2 rounded-lg mr-3">
                        <i class="fas fa-user-circle"></i>
                    </span>
                    Personal Information
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name <span class="text-red-500">*</span></label>
                        <input type="text" id="first_name" name="first_name" required
                               value="<?php echo isset($visitor_data) ? htmlspecialchars($visitor_data['first_name']) : ''; ?>"
                               oninput="validateName(this); suggestNameCorrections(this)"
                               onblur="formatName(this)"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        <div id="first_name_error" class="text-red-500 text-xs mt-1 hidden"></div>
                    </div>
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name <span class="text-red-500">*</span></label>
                        <input type="text" id="last_name" name="last_name" required
                               value="<?php echo isset($visitor_data) ? htmlspecialchars($visitor_data['last_name']) : ''; ?>"
                               oninput="validateName(this); suggestNameCorrections(this)"
                               onblur="formatName(this)"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        <div id="last_name_error" class="text-red-500 text-xs mt-1 hidden"></div>
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                            Email Address
                            <span class="text-gray-500 text-xs font-normal">(Optional but recommended)</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" id="email" name="email"
                                   value="<?php echo isset($visitor_data) ? htmlspecialchars($visitor_data['email']) : ''; ?>"
                                   placeholder="<EMAIL>"
                                   oninput="validateEmail(this); checkDuplicateEmail(this)"
                                   onblur="checkEmailTypos(this)"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                        <div id="email_error" class="text-red-500 text-xs mt-1 hidden"></div>
                        <div id="email_suggestion" class="text-blue-600 text-xs mt-1 hidden cursor-pointer" onclick="acceptEmailSuggestion()"></div>

                    </div>
                    <div>
                        <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-1">Phone Number <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-phone text-gray-400"></i>
                            </div>
                            <input type="tel" id="phone_number" name="phone_number" required
                                   value="<?php echo isset($visitor_data) ? htmlspecialchars($visitor_data['phone_number']) : ''; ?>"
                                   placeholder="e.g., 0244123456, +233244123456"
                                   oninput="formatPhoneNumber(this); validatePhoneLength(this); checkDuplicatePhone(this)"
                                   onblur="validatePhoneNumber(this)"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                        <div id="phone_number_error" class="text-red-500 text-xs mt-1 hidden"></div>
                        <div id="phone_number_warning" class="text-yellow-600 text-xs mt-1 hidden"></div>
                        <div id="phone_number_suggestion" class="text-blue-600 text-xs mt-1 hidden"></div>

                    </div>
                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-alt text-gray-400"></i>
                            </div>
                            <input type="text" id="date_of_birth" name="date_of_birth"
                                   placeholder="DD-MM-YYYY (e.g., 15-03-1990)"
                                   oninput="formatDateInput(this)"
                                   onchange="validateDateOfBirth(this)"
                                   maxlength="10"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                        <div id="date_of_birth_error" class="text-red-500 text-xs mt-1 hidden"></div>
                        <div id="date_of_birth_warning" class="text-yellow-600 text-xs mt-1 hidden"></div>
                        <div id="date_of_birth_suggestion" class="text-blue-600 text-xs mt-1 hidden"></div>
                    </div>
                    <div>
                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">Gender <span class="text-red-500">*</span></label>
                        <select id="gender" name="gender" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                    </div>
                    <div>
                        <label for="marital_status" class="block text-sm font-medium text-gray-700 mb-1">Marital Status</label>
                        <select id="marital_status" name="marital_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="">Select Marital Status</option>
                            <option value="single">Single</option>
                            <option value="married">Married</option>
                            <option value="divorced">Divorced</option>
                            <option value="widowed">Widowed</option>
                        </select>
                    </div>
                    <div>
                        <label for="occupation" class="block text-sm font-medium text-gray-700 mb-1">Occupation</label>
                        <input type="text" id="occupation" name="occupation" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                    </div>
                    <div>
                        <label for="school" class="block text-sm font-medium text-gray-700 mb-1">School/Institution</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-graduation-cap text-gray-400"></i>
                            </div>
                            <input type="text" id="school" name="school" placeholder="Enter school or institution name" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>

                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location/Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-map-marker-alt text-gray-400"></i>
                            </div>
                            <input type="text" id="location" name="location"
                                   value="<?php echo isset($visitor_data) ? htmlspecialchars($visitor_data['address']) : ''; ?>"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm">
                        </div>
                    </div>
                    <div class="md:col-span-3 bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <label for="profile_picture" class="block text-sm font-medium text-gray-700 mb-3 flex items-center">
                            <i class="fas fa-camera text-primary mr-2"></i> Profile Picture
                        </label>
                        <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
                            <div class="w-32 h-32 border-2 border-dashed border-primary-light rounded-full flex items-center justify-center bg-white shadow-sm overflow-hidden">
                                <img id="profile_preview" src="<?php echo BASE_URL; ?>assets/images/default-avatar.png" alt="Profile Preview" class="max-w-full max-h-full hidden">
                                <span id="profile_placeholder" class="text-gray-400"><i class="fas fa-user text-5xl"></i></span>
                            </div>
                            <div class="flex-1">
                                <div class="relative bg-white rounded-md shadow-sm border border-gray-300 px-3 py-2">
                                    <input type="file" id="profile_picture" name="profile_picture" accept="image/*" class="w-full focus:outline-none focus:ring-primary focus:border-primary">
                                </div>
                                <p class="mt-2 text-sm text-gray-500 flex items-center">
                                    <i class="fas fa-info-circle mr-1"></i> Accepted formats: JPG, PNG or GIF (Max. 2MB)
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emergency Contact Information -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <i class="fas fa-phone-alt text-primary mr-2"></i>
                    Emergency Contact Information
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Name</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-user-friends text-gray-400"></i>
                            </div>
                            <input type="text" id="emergency_contact_name" name="emergency_contact_name"
                                   placeholder="Full name of emergency contact"
                                   oninput="validateEmergencyContactName(this)"
                                   onblur="formatEmergencyContactName(this)"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm">
                        </div>
                        <div id="emergency_contact_name_error" class="text-red-500 text-xs mt-1 hidden"></div>
                    </div>
                    <div>
                        <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Phone</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-phone-alt text-gray-400"></i>
                            </div>
                            <input type="tel" id="emergency_contact_phone" name="emergency_contact_phone"
                                   placeholder="e.g., 0244123456, +233244123456"
                                   oninput="formatPhoneNumber(this); validatePhoneLength(this); validateEmergencyContactPhone(this)"
                                   onblur="validateEmergencyContactPhoneNumber(this); checkEmergencyContactPhone(this)"
                                   class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary shadow-sm">
                        </div>
                        <div id="emergency_contact_phone_error" class="text-red-500 text-xs mt-1 hidden"></div>
                        <div id="emergency_contact_phone_warning" class="text-yellow-600 text-xs mt-1 hidden"></div>
                        <div id="emergency_contact_phone_suggestion" class="text-blue-600 text-xs mt-1 hidden cursor-pointer" onclick="acceptEmergencyContactSuggestion()"></div>

                    </div>
                </div>
            </div>

            <!-- Church Information -->
            <div>
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center pb-2 border-b border-gray-200">
                    <span class="bg-secondary-light bg-opacity-20 text-secondary p-2 rounded-lg mr-3">
                        <i class="fas fa-church"></i>
                    </span>
                    Church Information
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
                    <div>
                        <label for="baptism_status" class="block text-sm font-medium text-gray-700 mb-1">Baptism Status</label>
                        <select id="baptism_status" name="baptism_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="not_baptized">Not Baptized</option>
                            <option value="baptized">Baptized</option>
                            <option value="scheduled">Scheduled</option>
                        </select>
                    </div>
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                        <div class="flex gap-2">
                            <select id="department" name="department" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                                <option value="">Loading departments...</option>
                            </select>
                            <button type="button" onclick="openDepartmentManager()" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg border border-gray-300 transition-colors duration-200 flex items-center" title="Manage Departments">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <div class="flex gap-2">
                            <select id="role" name="role" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                                <option value="">Loading roles...</option>
                            </select>
                            <button type="button" onclick="openRoleManager()" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg border border-gray-300 transition-colors duration-200 flex items-center" title="Manage Roles">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label for="membership_date" class="block text-sm font-medium text-gray-700 mb-1">Membership Date</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-check text-gray-400"></i>
                            </div>
                            <input type="date" id="membership_date" name="membership_date" value="<?php echo date('Y-m-d'); ?>" class="w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm">
                        </div>
                    </div>
                    <div>
                        <label for="member_status" class="block text-sm font-medium text-gray-700 mb-1">Member Status <span class="text-red-500">*</span></label>
                        <select id="member_status" name="member_status" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm bg-white">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="transferred">Transferred</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Children Registration Section -->
            <div id="children-registration-section">
                <div class="flex items-center justify-between mb-4 pb-2 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                        <span class="bg-green-100 text-green-600 p-2 rounded-lg mr-3">
                            <i class="fas fa-child"></i>
                        </span>
                        Register Children
                        <span class="ml-2 text-sm font-normal text-gray-500">(Optional - Add children under 18)</span>
                    </h2>
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" id="has-children-toggle" onchange="toggleChildrenSection()" class="mr-2 rounded">
                            <span class="text-sm text-gray-700">I have children to register</span>
                        </label>
                    </div>
                </div>



                <div id="children-form-container" class="bg-white border border-gray-200 rounded-lg p-4 hidden">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-medium text-gray-800 flex items-center">
                            <i class="fas fa-users text-green-600 mr-2"></i>
                            Children Information
                        </h4>
                        <button type="button" onclick="addChild()" class="bg-gradient-to-r from-green-500 to-green-600 text-white px-3 py-2 rounded-lg text-sm font-medium hover:shadow-md transition-all duration-200">
                            <i class="fas fa-plus mr-1"></i>Add Child
                        </button>
                    </div>

                    <div id="children-container" class="space-y-4">
                        <!-- Children will be added here dynamically -->
                    </div>

                    <div id="no-children-msg" class="text-center text-gray-500 py-4">
                        <i class="fas fa-child text-2xl mb-2"></i>
                        <p class="text-sm">No children added yet. Click "Add Child" to register children under 18.</p>
                    </div>
                </div>

                <div id="no-children-selected" class="text-center text-gray-500 py-8 border-2 border-dashed border-gray-300 rounded-lg">
                    <i class="fas fa-child text-3xl mb-3 text-gray-400"></i>
                    <p class="text-lg font-medium mb-2">Children Registration</p>
                    <p class="text-sm">Check the box above if you have children under 18 to register with this member.</p>
                </div>
            </div>


            <div class="flex justify-end space-x-4 pt-6 mt-4 border-t border-gray-200">
                <a href="<?php echo BASE_URL; ?>members" class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200 flex items-center">
                    <i class="fas fa-times mr-2"></i> Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200 flex items-center">
                    <i class="fas fa-save mr-2"></i>
                    Save Member
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Phone Number Utilities -->
<script>
    // Set global variable for JavaScript phone utilities
    <?php
    try {
        require_once 'utils/PhoneNumberUtils.php';
        $currentCountry = PhoneNumberUtils::getDefaultCountry();
        echo "window.DEFAULT_PHONE_COUNTRY = '$currentCountry';";
    } catch (Exception $e) {
        echo "window.DEFAULT_PHONE_COUNTRY = 'GH';";
    }
    ?>
</script>
<script src="<?php echo BASE_URL; ?>assets/js/phone-utils.js"></script>

<!-- JavaScript for enhanced member registration -->
<script>
// Simplified registration - only children registration needed

document.addEventListener('DOMContentLoaded', function() {
    // Profile picture preview
    const profileInput = document.getElementById('profile_picture');
    const profilePreview = document.getElementById('profile_preview');
    const profilePlaceholder = document.getElementById('profile_placeholder');

    profileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                profilePreview.src = e.target.result;
                profilePreview.classList.remove('hidden');
                profilePlaceholder.classList.add('hidden');
            }

            reader.readAsDataURL(this.files[0]);
        }
    });
});





// Simplified registration - only children registration needed

// Children Registration Functions
let childCount = 0;

function addChild() {
    childCount++;
    const childHtml = createChildFormHTML(childCount);
    addFormEntry('children-container', 'no-children-msg', childHtml);
}

function removeChild(index) {
    removeFormEntry(`.child-entry[data-index="${index}"]`, 'children-container', 'no-children-msg');

    // Reindex remaining children to ensure sequential numbering
    reindexChildren();
}

function reindexChildren() {
    const childEntries = document.querySelectorAll('.child-entry');
    let newIndex = 1;

    childEntries.forEach(entry => {
        // Update data-index attribute
        entry.setAttribute('data-index', newIndex);

        // Update child title
        const title = entry.querySelector('h4');
        if (title) {
            title.textContent = `Child ${newIndex}`;
        }

        // Update all form field names
        const inputs = entry.querySelectorAll('input, select');
        inputs.forEach(input => {
            const name = input.getAttribute('name');
            if (name && name.includes('children[')) {
                // Replace the index in the name attribute
                const newName = name.replace(/children\[\d+\]/, `children[${newIndex}]`);
                input.setAttribute('name', newName);
            }
        });

        // Update remove button onclick
        const removeBtn = entry.querySelector('button[onclick*="removeChild"]');
        if (removeBtn) {
            removeBtn.setAttribute('onclick', `removeChild(${newIndex})`);
        }

        newIndex++;
    });

    // Update childCount to match the actual number of children
    childCount = newIndex - 1;
}

function createChildFormHTML(count) {
    return `
        <div class="child-entry bg-green-50 p-4 rounded-lg border border-green-200" data-index="${count}">
            <div class="flex justify-between items-center mb-3">
                <h4 class="font-medium text-green-800">Child ${count}</h4>
                <button type="button" onclick="removeChild(${count})" class="text-red-500 hover:text-red-700 transition-colors">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                ${createInputField('text', `children[${count}][first_name]`, 'First Name *', true, 'green')}
                ${createInputField('text', `children[${count}][last_name]`, 'Last Name *', true, 'green')}
                ${createDateField(`children[${count}][date_of_birth]`, 'Date of Birth *', `validateChildAge(${count})`, 'green')}
                ${createSelectField(`children[${count}][gender]`, 'Gender *', [
                    {value: '', text: 'Select Gender'},
                    {value: 'male', text: 'Male'},
                    {value: 'female', text: 'Female'}
                ], true, 'green')}
                ${createInputField('text', `children[${count}][school]`, 'School', false, 'green', 'Enter school name')}
                ${createSelectField(`children[${count}][department]`, 'Department', [
                    {value: 'children', text: 'Children'},
                    {value: 'new_breed', text: 'New Breed'},
                    {value: 'none', text: 'None'}
                ], false, 'green')}
            </div>

            <!-- Age Warning Container -->
            <div id="age-warning-${count}" class="mt-3" style="display: none;">
                <div class="bg-orange-100 border border-orange-400 text-orange-700 px-4 py-3 rounded">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span><strong>Note:</strong> This person is 18 or older. Consider registering them as a separate adult member instead.</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function validateChildAge(childIndex) {
    const dobInput = document.querySelector(`input[name="children[${childIndex}][date_of_birth]"]`);
    const childContainer = dobInput.closest('.child-entry');

    // Create or get message containers
    let errorDiv = childContainer.querySelector('.child-age-error');
    let warningDiv = childContainer.querySelector('.child-age-warning');
    let suggestionDiv = childContainer.querySelector('.child-age-suggestion');

    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'child-age-error text-red-500 text-xs mt-2 p-2 bg-red-50 border border-red-200 rounded hidden';
        dobInput.parentNode.appendChild(errorDiv);
    }

    if (!warningDiv) {
        warningDiv = document.createElement('div');
        warningDiv.className = 'child-age-warning text-yellow-600 text-xs mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded hidden';
        dobInput.parentNode.appendChild(warningDiv);
    }

    if (!suggestionDiv) {
        suggestionDiv = document.createElement('div');
        suggestionDiv.className = 'child-age-suggestion text-blue-600 text-xs mt-2 p-2 bg-blue-50 border border-blue-200 rounded hidden cursor-pointer';
        dobInput.parentNode.appendChild(suggestionDiv);
    }

    // Clear all messages
    errorDiv.classList.add('hidden');
    warningDiv.classList.add('hidden');
    suggestionDiv.classList.add('hidden');
    dobInput.classList.remove('border-red-500', 'border-yellow-500');
    dobInput.classList.add('border-gray-300');

    if (!dobInput.value) {
        return true;
    }

    // Validate DD-MM-YYYY format for child
    const datePattern = /^(\d{2})-(\d{2})-(\d{4})$/;
    const match = dobInput.value.match(datePattern);

    if (!match) {
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i><strong>Invalid Format:</strong> Please enter date in DD-MM-YYYY format (e.g., 15-03-2010)`;
        errorDiv.classList.remove('hidden');
        dobInput.classList.add('border-red-500');
        return false;
    }

    const day = parseInt(match[1]);
    const month = parseInt(match[2]);
    const year = parseInt(match[3]);

    // Validate date components
    if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > new Date().getFullYear()) {
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i><strong>Invalid Date:</strong> Please enter a valid date (DD-MM-YYYY)`;
        errorDiv.classList.remove('hidden');
        dobInput.classList.add('border-red-500');
        return false;
    }

    // Create date object and validate
    const birthDate = new Date(year, month - 1, day);
    if (birthDate.getDate() !== day || birthDate.getMonth() !== (month - 1) || birthDate.getFullYear() !== year) {
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i><strong>Invalid Date:</strong> Please enter a valid date (e.g., 29-02 only exists in leap years)`;
        errorDiv.classList.remove('hidden');
        dobInput.classList.add('border-red-500');
        return false;
    }

    const age = calculateAgeFromDDMMYYYY(day, month, year);
    const parentName = getParentName();

    // Hard limit: Prevent children over 18
    if (age >= 18) {
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i><strong>Age Restriction:</strong> This person is ${age} years old. Children registration is only for those under 18. Please register them as a separate adult member.`;
        errorDiv.classList.remove('hidden');
        dobInput.classList.add('border-red-500');

        // Add suggestion to remove and register separately
        suggestionDiv.innerHTML = `<i class="fas fa-user-plus mr-1"></i><strong>Suggestion:</strong> Remove this entry and create a separate adult registration for this person. <span class="underline">Click here for guidance</span>`;
        suggestionDiv.classList.remove('hidden');
        suggestionDiv.onclick = () => showAdultRegistrationGuidance(age);

        return false;
    }

    // Smart redirect for 16+ year olds
    if (age >= 16) {
        warningDiv.innerHTML = `<i class="fas fa-exclamation-triangle mr-1"></i><strong>Age Notice:</strong> This person is ${age} years old. They can be registered as a child or as a separate adult. Consider their maturity and independence level.`;
        warningDiv.classList.remove('hidden');
        dobInput.classList.add('border-yellow-500');

        suggestionDiv.innerHTML = `<i class="fas fa-question-circle mr-1"></i><strong>Options:</strong> Keep as child registration or <span class="underline">register as adult instead</span>`;
        suggestionDiv.classList.remove('hidden');
        suggestionDiv.onclick = () => showRegistrationOptions(age, childIndex);

        return true;
    }

    // Relationship preview for valid children
    if (age < 16 && parentName) {
        suggestionDiv.innerHTML = `<i class="fas fa-link mr-1 text-green-500"></i><strong>Relationship Preview:</strong> This will create a parent-child relationship between <strong>${parentName}</strong> and this child (${age} years old).`;
        suggestionDiv.classList.remove('hidden');
        suggestionDiv.classList.add('text-green-600');
        suggestionDiv.onclick = null; // Remove click handler
    }

    return true;
}

function getParentName() {
    const firstName = document.getElementById('first_name').value.trim();
    const lastName = document.getElementById('last_name').value.trim();

    if (firstName && lastName) {
        return `${firstName} ${lastName}`;
    } else if (firstName) {
        return firstName;
    } else if (lastName) {
        return lastName;
    }

    return 'the parent member';
}

function showAdultRegistrationGuidance(age) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4">
            <div class="flex items-center mb-4">
                <i class="fas fa-user-plus text-blue-500 text-2xl mr-3"></i>
                <h3 class="text-lg font-semibold">Adult Registration Required</h3>
            </div>
            <p class="text-gray-700 mb-4">
                Since this person is ${age} years old, they need to be registered as an adult member rather than a child.
            </p>
            <div class="bg-blue-50 border border-blue-200 rounded p-3 mb-4">
                <h4 class="font-medium text-blue-800 mb-2">Next Steps:</h4>
                <ol class="text-sm text-blue-700 space-y-1">
                    <li>1. Remove this entry from children section</li>
                    <li>2. Complete the current member registration</li>
                    <li>3. Create a separate registration for the ${age}-year-old</li>
                    <li>4. Link them as family members if needed</li>
                </ol>
            </div>
            <div class="flex justify-end space-x-3">
                <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-600 hover:text-gray-800">
                    Got it
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function showRegistrationOptions(age, childIndex) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4">
            <div class="flex items-center mb-4">
                <i class="fas fa-question-circle text-yellow-500 text-2xl mr-3"></i>
                <h3 class="text-lg font-semibold">Registration Options</h3>
            </div>
            <p class="text-gray-700 mb-4">
                This person is ${age} years old. How would you like to register them?
            </p>
            <div class="space-y-3">
                <button onclick="keepAsChild(${childIndex}); this.closest('.fixed').remove()"
                        class="w-full p-3 text-left border border-green-200 rounded hover:bg-green-50">
                    <div class="font-medium text-green-700">Keep as Child</div>
                    <div class="text-sm text-green-600">Register under parent's account</div>
                </button>
                <button onclick="suggestAdultRegistration(${age}); this.closest('.fixed').remove()"
                        class="w-full p-3 text-left border border-blue-200 rounded hover:bg-blue-50">
                    <div class="font-medium text-blue-700">Register as Adult</div>
                    <div class="text-sm text-blue-600">Create separate adult registration</div>
                </button>
            </div>
            <div class="flex justify-end mt-4">
                <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-600 hover:text-gray-800">
                    Cancel
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function keepAsChild(childIndex) {
    // Just close the modal and keep the child entry
    const dobInput = document.querySelector(`input[name="children[${childIndex}][date_of_birth]"]`);
    const childContainer = dobInput.closest('.child-entry');
    const warningDiv = childContainer.querySelector('.child-age-warning');
    const suggestionDiv = childContainer.querySelector('.child-age-suggestion');

    if (warningDiv) warningDiv.classList.add('hidden');
    if (suggestionDiv) {
        suggestionDiv.innerHTML = `<i class="fas fa-check-circle mr-1 text-green-500"></i>Keeping as child registration.`;
        suggestionDiv.classList.remove('hidden');
        suggestionDiv.classList.add('text-green-600');

        setTimeout(() => {
            suggestionDiv.classList.add('hidden');
        }, 3000);
    }
}

function suggestAdultRegistration(age) {
    showAdultRegistrationGuidance(age);
}

function calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }

    return age;
}

// Simplified form management - only children forms needed

// Utility Functions for Form Management
function addFormEntry(containerId, noItemsMsgId, html) {
    const container = document.getElementById(containerId);
    const noItemsMsg = document.getElementById(noItemsMsgId);

    container.insertAdjacentHTML('beforeend', html);
    noItemsMsg.classList.add('hidden');
}

function removeFormEntry(selector, containerId, noItemsMsgId) {
    const element = document.querySelector(selector);
    if (element) {
        element.remove();

        const container = document.getElementById(containerId);
        const noItemsMsg = document.getElementById(noItemsMsgId);

        if (container.children.length === 0) {
            noItemsMsg.classList.remove('hidden');
        }
    }
}

function createInputField(type, name, label, required = false, colorScheme = 'blue', placeholder = '') {
    const requiredMark = required ? '<span class="text-red-500">*</span>' : '';
    const requiredAttr = required ? 'required' : '';
    const placeholderAttr = placeholder ? `placeholder="${placeholder}"` : '';

    // Add name validation for name fields
    const isNameField = name.includes('first_name') || name.includes('last_name');
    const nameValidation = isNameField ? 'oninput="validateChildName(this)" onblur="formatChildName(this)"' : '';
    const errorId = name.replace(/[\[\]]/g, '_').replace(/__/g, '_') + '_error';

    return `
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">${label} ${requiredMark}</label>
            <input type="${type}" name="${name}" ${requiredAttr} ${placeholderAttr} ${nameValidation}
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-${colorScheme}-500 transition-colors">
            ${isNameField ? `<div id="${errorId}" class="text-red-500 text-xs mt-1 hidden"></div>` : ''}
        </div>
    `;
}

function createSelectField(name, label, options, required = false, colorScheme = 'blue') {
    const requiredMark = required ? '<span class="text-red-500">*</span>' : '';
    const requiredAttr = required ? 'required' : '';

    const optionsHTML = options.map(option =>
        `<option value="${option.value}">${option.text}</option>`
    ).join('');

    return `
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">${label} ${requiredMark}</label>
            <select name="${name}" ${requiredAttr}
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-${colorScheme}-500 transition-colors">
                ${optionsHTML}
            </select>
        </div>
    `;
}

function createDateField(name, label, onchange = '', colorScheme = 'blue') {
    const onchangeAttr = onchange ? `onchange="${onchange}"` : '';

    return `
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">${label} <span class="text-red-500">*</span></label>
            <input type="text" name="${name}" required ${onchangeAttr}
                   placeholder="DD-MM-YYYY (e.g., 15-03-2010)"
                   oninput="formatDateInput(this)"
                   maxlength="10"
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-${colorScheme}-500 transition-colors">
        </div>
    `;
}

// Define BASE_URL for JavaScript
const BASE_URL = '<?php echo BASE_URL; ?>';

// Name validation functions
function validateName(input) {
    const value = input.value;
    const errorDiv = document.getElementById(input.id + '_error');
    let isValid = true;
    let errorMessage = '';

    // Remove numbers and special characters (except spaces, hyphens, apostrophes)
    const cleanValue = value.replace(/[^a-zA-Z\s\-']/g, '');
    if (cleanValue !== value) {
        input.value = cleanValue;
    }

    // Check minimum length (2 characters excluding spaces)
    const nameWithoutSpaces = cleanValue.replace(/\s/g, '');
    if (nameWithoutSpaces.length > 0 && nameWithoutSpaces.length < 2) {
        isValid = false;
        errorMessage = 'Name must be at least 2 characters long';
    }

    // Check for numbers or invalid special characters
    if (/[0-9!@#$%^&*()_+=\[\]{}|\\:";.<>?/~`]/.test(value)) {
        isValid = false;
        errorMessage = 'Name cannot contain numbers or special characters';
    }

    // Show/hide error message
    if (isValid) {
        errorDiv.classList.add('hidden');
        input.classList.remove('border-red-500');
        input.classList.add('border-gray-300');
    } else {
        errorDiv.textContent = errorMessage;
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        input.classList.remove('border-gray-300');
    }

    return isValid;
}

function formatName(input) {
    let value = input.value;

    // Trim extra spaces
    value = value.replace(/\s+/g, ' ').trim();

    // Auto-capitalize first letters
    value = value.split(' ').map(word => {
        if (word.length > 0) {
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
        return word;
    }).join(' ');

    input.value = value;

    // Revalidate after formatting
    validateName(input);
}

// Children name validation functions
function validateChildName(input) {
    const value = input.value;
    const errorId = input.name.replace(/[\[\]]/g, '_').replace(/__/g, '_') + '_error';
    const errorDiv = document.getElementById(errorId);
    let isValid = true;
    let errorMessage = '';

    // Remove numbers and special characters (except spaces, hyphens, apostrophes)
    const cleanValue = value.replace(/[^a-zA-Z\s\-']/g, '');
    if (cleanValue !== value) {
        input.value = cleanValue;
    }

    // Check minimum length (2 characters excluding spaces)
    const nameWithoutSpaces = cleanValue.replace(/\s/g, '');
    if (nameWithoutSpaces.length > 0 && nameWithoutSpaces.length < 2) {
        isValid = false;
        errorMessage = 'Name must be at least 2 characters long';
    }

    // Check for numbers or invalid special characters
    if (/[0-9!@#$%^&*()_+=\[\]{}|\\:";.<>?/~`]/.test(value)) {
        isValid = false;
        errorMessage = 'Name cannot contain numbers or special characters';
    }

    // Show/hide error message
    if (errorDiv) {
        if (isValid) {
            errorDiv.classList.add('hidden');
            input.classList.remove('border-red-500');
            input.classList.add('border-gray-300');
        } else {
            errorDiv.textContent = errorMessage;
            errorDiv.classList.remove('hidden');
            input.classList.add('border-red-500');
            input.classList.remove('border-gray-300');
        }
    }

    return isValid;
}

function formatChildName(input) {
    let value = input.value;

    // Trim extra spaces
    value = value.replace(/\s+/g, ' ').trim();

    // Auto-capitalize first letters
    value = value.split(' ').map(word => {
        if (word.length > 0) {
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
        return word;
    }).join(' ');

    input.value = value;

    // Revalidate after formatting
    validateChildName(input);
}

// Email validation functions
let emailSuggestion = '';

function validateEmail(input) {
    const value = input.value.trim();
    const errorDiv = document.getElementById('email_error');
    const suggestionDiv = document.getElementById('email_suggestion');
    let isValid = true;
    let errorMessage = '';

    // Hide suggestion when typing
    suggestionDiv.classList.add('hidden');

    // If empty, it's valid (optional field)
    if (value === '') {
        errorDiv.classList.add('hidden');
        input.classList.remove('border-red-500');
        input.classList.add('border-gray-300');
        return true;
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address';
    }

    // Show/hide error message
    if (isValid) {
        errorDiv.classList.add('hidden');
        input.classList.remove('border-red-500');
        input.classList.add('border-gray-300');
    } else {
        errorDiv.textContent = errorMessage;
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        input.classList.remove('border-gray-300');
    }

    return isValid;
}

function checkEmailTypos(input) {
    const value = input.value.trim();
    const suggestionDiv = document.getElementById('email_suggestion');

    if (value === '') {
        suggestionDiv.classList.add('hidden');
        return;
    }

    // Common email domain typos
    const commonTypos = {
        'gmail.co': 'gmail.com',
        'gmail.con': 'gmail.com',
        'gmial.com': 'gmail.com',
        'gmai.com': 'gmail.com',
        'yahoo.co': 'yahoo.com',
        'yahoo.con': 'yahoo.com',
        'yahooo.com': 'yahoo.com',
        'hotmail.co': 'hotmail.com',
        'hotmail.con': 'hotmail.com',
        'outlook.co': 'outlook.com',
        'outlook.con': 'outlook.com'
    };

    const emailParts = value.split('@');
    if (emailParts.length === 2) {
        const domain = emailParts[1].toLowerCase();

        for (const typo in commonTypos) {
            if (domain === typo) {
                const correctedEmail = emailParts[0] + '@' + commonTypos[typo];
                emailSuggestion = correctedEmail;
                suggestionDiv.innerHTML = `<i class="fas fa-lightbulb mr-1"></i>Did you mean: <strong>${correctedEmail}</strong>? Click to use this.`;
                suggestionDiv.classList.remove('hidden');
                return;
            }
        }
    }

    suggestionDiv.classList.add('hidden');
}

function acceptEmailSuggestion() {
    const emailInput = document.getElementById('email');
    const suggestionDiv = document.getElementById('email_suggestion');

    if (emailSuggestion) {
        emailInput.value = emailSuggestion;
        suggestionDiv.classList.add('hidden');
        validateEmail(emailInput);
    }
}

// Date of Birth validation functions
function validateDateOfBirth(input) {
    const value = input.value;
    const errorDiv = document.getElementById('date_of_birth_error');
    const warningDiv = document.getElementById('date_of_birth_warning');
    const suggestionDiv = document.getElementById('date_of_birth_suggestion');

    // Clear all messages
    errorDiv.classList.add('hidden');
    warningDiv.classList.add('hidden');
    suggestionDiv.classList.add('hidden');
    input.classList.remove('border-red-500', 'border-yellow-500');
    input.classList.add('border-gray-300');

    if (!value) {
        return true; // Optional field
    }

    // Validate DD-MM-YYYY format
    const datePattern = /^(\d{2})-(\d{2})-(\d{4})$/;
    const match = value.match(datePattern);

    if (!match) {
        errorDiv.textContent = 'Please enter date in DD-MM-YYYY format (e.g., 15-03-1990)';
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        return false;
    }

    const day = parseInt(match[1]);
    const month = parseInt(match[2]);
    const year = parseInt(match[3]);

    // Validate date components
    if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > new Date().getFullYear()) {
        errorDiv.textContent = 'Please enter a valid date (DD-MM-YYYY)';
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        return false;
    }

    // Create date object (month is 0-indexed in JavaScript)
    const birthDate = new Date(year, month - 1, day);
    const today = new Date();

    // Check if the date is valid (handles invalid dates like 31-02-2020)
    if (birthDate.getDate() !== day || birthDate.getMonth() !== (month - 1) || birthDate.getFullYear() !== year) {
        errorDiv.textContent = 'Please enter a valid date (e.g., 29-02 only exists in leap years)';
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        return false;
    }

    const age = calculateAgeFromDDMMYYYY(day, month, year);

    // Check for future dates
    if (birthDate > today) {
        errorDiv.textContent = 'Date of birth cannot be in the future';
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        return false;
    }

    // Check maximum age (200 years)
    if (age > 200) {
        errorDiv.textContent = 'Age cannot exceed 200 years. Please check the date.';
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        return false;
    }

    // Age-based suggestions and warnings
    if (age < 18) {
        if (age < 16) {
            suggestionDiv.innerHTML = `<i class="fas fa-info-circle mr-1"></i>This person is ${age} years old. Consider using the <strong>Children Registration</strong> section below for better organization.`;
            suggestionDiv.classList.remove('hidden');
        } else {
            warningDiv.innerHTML = `<i class="fas fa-exclamation-triangle mr-1"></i>This person is ${age} years old. They can register as an adult or be added as a child.`;
            warningDiv.classList.remove('hidden');
            input.classList.add('border-yellow-500');
        }
    } else if (age >= 18) {
        // Valid adult age - show confirmation
        suggestionDiv.innerHTML = `<i class="fas fa-check-circle mr-1 text-green-500"></i>Adult registration (Age: ${age} years)`;
        suggestionDiv.classList.remove('hidden');
        suggestionDiv.classList.add('text-green-600');
    }

    return true;
}

// Enhanced calculateAge function with more precision
function calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }

    return age;
}

// Date formatting function for DD-MM-YYYY format
function formatDateInput(input) {
    let value = input.value.replace(/\D/g, ''); // Remove non-digits

    if (value.length >= 2) {
        value = value.substring(0, 2) + '-' + value.substring(2);
    }
    if (value.length >= 5) {
        value = value.substring(0, 5) + '-' + value.substring(5, 9);
    }

    input.value = value;
}

// Calculate age from DD-MM-YYYY format
function calculateAgeFromDDMMYYYY(day, month, year) {
    const today = new Date();
    const birthDate = new Date(year, month - 1, day); // month is 0-indexed
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
}

// Phone number length validation based on country patterns
function validatePhoneLength(input) {
    const value = input.value.replace(/\D/g, ''); // Get only digits
    const errorDiv = input.id === 'phone_number' ?
        document.getElementById('phone_number_error') :
        document.getElementById('emergency_contact_phone_error');

    if (!value) {
        return true; // Empty is okay for validation
    }

    // Get country patterns from PhoneNumberUtils if available
    let maxLength = 15; // Default international max
    let minLength = 7;  // Default international min

    // Try to detect country and get specific limits
    if (window.PhoneNumberUtils) {
        const detectedCountry = window.PhoneNumberUtils.detectCountryFromNumber(value);
        if (detectedCountry && window.PhoneNumberUtils.countryPatterns[detectedCountry]) {
            const pattern = window.PhoneNumberUtils.countryPatterns[detectedCountry];
            if (pattern.length) {
                if (Array.isArray(pattern.length)) {
                    minLength = Math.min(...pattern.length);
                    maxLength = Math.max(...pattern.length);
                } else {
                    minLength = pattern.length - 1; // Allow 1 digit less
                    maxLength = pattern.length + 1; // Allow 1 digit more
                }
            }
        }
    }

    // Country-specific validation
    if (value.length > 0) {
        // Togo: 8 digits (90123456)
        if (value.startsWith('9') && value.length >= 8) {
            if (value.length > 8) {
                showPhoneLengthError(input, errorDiv, 'Togo numbers should be 8 digits (e.g., 90123456)');
                return false;
            }
        }
        // Ghana: 9 digits (0244123456)
        else if ((value.startsWith('0') || value.startsWith('2') || value.startsWith('5')) && value.length >= 9) {
            if (value.length > 10) {
                showPhoneLengthError(input, errorDiv, 'Ghana numbers should be 9-10 digits (e.g., 0244123456)');
                return false;
            }
        }
        // UK: 10-11 digits
        else if (value.startsWith('07') || value.startsWith('44')) {
            if (value.length > 11) {
                showPhoneLengthError(input, errorDiv, 'UK numbers should be 10-11 digits (e.g., 07578662999)');
                return false;
            }
        }
        // US/Canada: 10 digits
        else if (value.startsWith('1') || (value.length === 10 && value.match(/^[2-9]/))) {
            if (value.length > 11) {
                showPhoneLengthError(input, errorDiv, 'US/Canada numbers should be 10 digits (e.g., 2125551234)');
                return false;
            }
        }
        // General validation for other countries
        else if (value.length > 15) {
            showPhoneLengthError(input, errorDiv, 'Phone number is too long (maximum 15 digits)');
            return false;
        }
    }

    // Clear any previous length errors
    clearPhoneLengthError(input, errorDiv);
    return true;
}

function showPhoneLengthError(input, errorDiv, message) {
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
    input.classList.add('border-red-500');
}

function clearPhoneLengthError(input, errorDiv) {
    // Only clear if it's a length-related error
    if (errorDiv.textContent.includes('digits') || errorDiv.textContent.includes('too long')) {
        errorDiv.classList.add('hidden');
        input.classList.remove('border-red-500');
    }
}

// Emergency Contact validation functions
let emergencyContactSuggestion = null;

function validateEmergencyContactName(input) {
    const value = input.value;
    const errorDiv = document.getElementById('emergency_contact_name_error');

    // Use the same name validation as regular names
    return validateNameField(input, errorDiv);
}

function formatEmergencyContactName(input) {
    // Use the same formatting as regular names
    formatNameField(input);
}

function validateEmergencyContactPhoneNumber(input) {
    const value = input.value.trim();
    const errorDiv = document.getElementById('emergency_contact_phone_error');
    const warningDiv = document.getElementById('emergency_contact_phone_warning');

    // Clear previous messages
    errorDiv.classList.add('hidden');
    warningDiv.classList.add('hidden');
    input.classList.remove('border-red-500', 'border-yellow-500');
    input.classList.add('border-gray-300');

    if (value === '') {
        return true; // Optional field
    }

    // Use the same phone validation logic as main phone field
    const phoneRegex = /^[\+]?[\d\s\-\(\)]{10,15}$/;
    if (!phoneRegex.test(value)) {
        errorDiv.textContent = 'Please enter a valid emergency contact phone number';
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        return false;
    }

    return true;
}

function validateEmergencyContactPhone(input) {
    const value = input.value.trim();
    const warningDiv = document.getElementById('emergency_contact_phone_warning');
    const suggestionDiv = document.getElementById('emergency_contact_phone_suggestion');

    // Clear previous messages
    warningDiv.classList.add('hidden');
    suggestionDiv.classList.add('hidden');
    input.classList.remove('border-yellow-500');

    if (value === '') {
        return true; // Optional field
    }

    // Check if same as member's phone (compare normalized versions)
    const memberPhone = document.getElementById('phone_number').value.trim();
    if (value === memberPhone && memberPhone !== '') {
        warningDiv.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>Emergency contact phone is the same as member\'s phone. Consider using a different contact.';
        warningDiv.classList.remove('hidden');
        input.classList.add('border-yellow-500');
        return false;
    }

    return true;
}

async function checkEmergencyContactPhone(input) {
    const value = input.value.trim();
    const suggestionDiv = document.getElementById('emergency_contact_phone_suggestion');

    if (value === '') {
        suggestionDiv.classList.add('hidden');
        return;
    }

    try {
        // Check if phone matches existing member (manual suggestion only - no auto-relationships)
        const response = await fetch(`${BASE_URL}api/check-member-phone`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone: value })
        });

        if (response.ok) {
            const data = await response.json();
            if (data.exists) {
                emergencyContactSuggestion = data.member;
                suggestionDiv.innerHTML = `<i class="fas fa-user-check mr-1"></i>Found member: <strong>${data.member.first_name} ${data.member.last_name}</strong>. Click to use as emergency contact.`;
                suggestionDiv.classList.remove('hidden');
            } else {
                suggestionDiv.classList.add('hidden');
            }
        }
    } catch (error) {
        console.log('Could not check member phone:', error);
        suggestionDiv.classList.add('hidden');
    }
}

function acceptEmergencyContactSuggestion() {
    const nameInput = document.getElementById('emergency_contact_name');
    const suggestionDiv = document.getElementById('emergency_contact_phone_suggestion');

    if (emergencyContactSuggestion) {
        nameInput.value = `${emergencyContactSuggestion.first_name} ${emergencyContactSuggestion.last_name}`;
        suggestionDiv.classList.add('hidden');

        // Show relationship suggestion
        suggestionDiv.innerHTML = '<i class="fas fa-info-circle mr-1"></i>Consider specifying the relationship (e.g., spouse, parent, sibling) in the name field.';
        suggestionDiv.classList.remove('hidden');

        setTimeout(() => {
            suggestionDiv.classList.add('hidden');
        }, 5000);
    }
}

// Helper functions for name validation (reusable)
function validateNameField(input, errorDiv) {
    const value = input.value;
    let isValid = true;
    let errorMessage = '';

    // Remove numbers and special characters (except spaces, hyphens, apostrophes)
    const cleanValue = value.replace(/[^a-zA-Z\s\-']/g, '');
    if (cleanValue !== value) {
        input.value = cleanValue;
    }

    // Check minimum length (2 characters excluding spaces)
    const nameWithoutSpaces = cleanValue.replace(/\s/g, '');
    if (nameWithoutSpaces.length > 0 && nameWithoutSpaces.length < 2) {
        isValid = false;
        errorMessage = 'Name must be at least 2 characters long';
    }

    // Show/hide error message
    if (isValid) {
        errorDiv.classList.add('hidden');
        input.classList.remove('border-red-500');
        input.classList.add('border-gray-300');
    } else {
        errorDiv.textContent = errorMessage;
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        input.classList.remove('border-gray-300');
    }

    return isValid;
}

function formatNameField(input) {
    let value = input.value;

    // Trim extra spaces
    value = value.replace(/\s+/g, ' ').trim();

    // Auto-capitalize first letters
    value = value.split(' ').map(word => {
        if (word.length > 0) {
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
        return word;
    }).join(' ');

    input.value = value;
}

// Real-time validation functions
let phoneFormatTimeout;
let duplicateCheckTimeout;

function formatPhoneNumber(input) {
    // Use the PhoneNumberUtils for proper formatting
    if (window.PhoneNumberUtils) {
        const normalized = window.PhoneNumberUtils.normalize(input.value);
        if (normalized && normalized !== input.value) {
            input.value = normalized;
        }
    } else {
        // Fallback: just clean the input without assuming country
        let value = input.value.replace(/[^\d\+\-\(\)\s]/g, ''); // Keep basic formatting chars
        input.value = value;
    }
}

function validatePhoneNumber(input) {
    const value = input.value.trim();
    const errorDiv = document.getElementById('phone_number_error');
    const warningDiv = document.getElementById('phone_number_warning');

    // Clear previous messages
    errorDiv.classList.add('hidden');
    warningDiv.classList.add('hidden');
    input.classList.remove('border-red-500', 'border-yellow-500');
    input.classList.add('border-gray-300');

    if (!value) {
        errorDiv.textContent = 'Phone number is required';
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        return false;
    }

    // Basic phone validation
    const phoneRegex = /^[\+]?[\d\s\-\(\)]{10,15}$/;
    if (!phoneRegex.test(value)) {
        errorDiv.textContent = 'Please enter a valid phone number';
        errorDiv.classList.remove('hidden');
        input.classList.add('border-red-500');
        return false;
    }

    return true;
}

async function checkDuplicatePhone(input) {
    const value = input.value.trim();
    const suggestionDiv = document.getElementById('phone_number_suggestion');

    // Clear previous timeout
    if (duplicateCheckTimeout) {
        clearTimeout(duplicateCheckTimeout);
    }

    // Hide suggestion while typing
    suggestionDiv.classList.add('hidden');

    if (value.length < 8) {
        return; // Too short to check
    }

    // Debounce the duplicate check
    duplicateCheckTimeout = setTimeout(async () => {
        try {
            const response = await fetch(`${BASE_URL}api/check-duplicate-phone`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ phone: value })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.exists) {
                    suggestionDiv.innerHTML = `<i class="fas fa-exclamation-triangle mr-1"></i>This phone number is already registered to <strong>${data.member.first_name} ${data.member.last_name}</strong>. Please use a different number.`;
                    suggestionDiv.classList.remove('hidden');
                    suggestionDiv.classList.add('text-yellow-600');
                    input.classList.add('border-yellow-500');
                } else {
                    suggestionDiv.classList.add('hidden');
                    input.classList.remove('border-yellow-500');
                }
            }
        } catch (error) {
            console.log('Could not check duplicate phone:', error);
        }
    }, 1000); // Wait 1 second after user stops typing
}

async function checkDuplicateEmail(input) {
    const value = input.value.trim();
    const suggestionDiv = document.getElementById('email_suggestion');

    if (!value || !value.includes('@')) {
        return; // Invalid or empty email
    }

    try {
        const response = await fetch(`${BASE_URL}api/check-duplicate-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: value })
        });

        if (response.ok) {
            const data = await response.json();
            if (data.exists) {
                suggestionDiv.innerHTML = `<i class="fas fa-exclamation-triangle mr-1"></i>This email is already registered to <strong>${data.member.first_name} ${data.member.last_name}</strong>. Please use a different email.`;
                suggestionDiv.classList.remove('hidden');
                suggestionDiv.classList.add('text-yellow-600');
                input.classList.add('border-yellow-500');
            } else {
                suggestionDiv.classList.add('hidden');
                input.classList.remove('border-yellow-500');
            }
        }
    } catch (error) {
        console.log('Could not check duplicate email:', error);
    }
}

// Name suggestion functions for common misspellings
const commonNameCorrections = {
    'jhon': 'John',
    'micheal': 'Michael',
    'recieve': 'receive',
    'seperate': 'separate',
    'definately': 'definitely',
    'occured': 'occurred',
    'neccessary': 'necessary',
    'accomodate': 'accommodate',
    'embarass': 'embarrass',
    'millenium': 'millennium'
};

function suggestNameCorrections(input) {
    const value = input.value.toLowerCase().trim();
    const words = value.split(' ');
    let hasSuggestion = false;
    let suggestions = [];

    words.forEach(word => {
        if (commonNameCorrections[word]) {
            suggestions.push(`"${word}" → "${commonNameCorrections[word]}"`);
            hasSuggestion = true;
        }
    });

    if (hasSuggestion) {
        const suggestionDiv = input.parentNode.querySelector('.name-suggestion') || createNameSuggestionDiv(input);
        suggestionDiv.innerHTML = `<i class="fas fa-lightbulb mr-1"></i>Suggestion: ${suggestions.join(', ')}. <span class="underline cursor-pointer" onclick="applyNameSuggestions('${input.id}')">Click to apply</span>`;
        suggestionDiv.classList.remove('hidden');
    }
}

function createNameSuggestionDiv(input) {
    const div = document.createElement('div');
    div.className = 'name-suggestion text-blue-600 text-xs mt-1 hidden';
    input.parentNode.appendChild(div);
    return div;
}

function applyNameSuggestions(inputId) {
    const input = document.getElementById(inputId);
    let value = input.value;

    Object.keys(commonNameCorrections).forEach(wrong => {
        const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
        value = value.replace(regex, commonNameCorrections[wrong]);
    });

    input.value = value;
    formatName(input);

    const suggestionDiv = input.parentNode.querySelector('.name-suggestion');
    if (suggestionDiv) {
        suggestionDiv.classList.add('hidden');
    }
}

// Smart Form Behavior Functions

// Progressive disclosure for children section
function toggleChildrenSection() {
    const checkbox = document.getElementById('has-children-toggle');
    const formContainer = document.getElementById('children-form-container');
    const noChildrenMsg = document.getElementById('no-children-selected');

    if (checkbox.checked) {
        formContainer.classList.remove('hidden');
        noChildrenMsg.classList.add('hidden');

        // Auto-add first child if none exist
        const childrenContainer = document.getElementById('children-container');
        if (childrenContainer.children.length === 0) {
            addChild();
        }
    } else {
        formContainer.classList.add('hidden');
        noChildrenMsg.classList.remove('hidden');

        // Clear all children when hiding section
        const childrenContainer = document.getElementById('children-container');
        childrenContainer.innerHTML = '';
        const noChildrenMsgInside = document.getElementById('no-children-msg');
        if (noChildrenMsgInside) {
            noChildrenMsgInside.classList.remove('hidden');
        }
    }

    // Save form state
    saveFormData();
}

// Auto-save form data to localStorage
function saveFormData() {
    const formData = {};
    const form = document.querySelector('form');
    const formElements = form.querySelectorAll('input, select, textarea');

    formElements.forEach(element => {
        if (element.type === 'checkbox') {
            formData[element.name || element.id] = element.checked;
        } else if (element.type === 'radio') {
            if (element.checked) {
                formData[element.name] = element.value;
            }
        } else {
            formData[element.name || element.id] = element.value;
        }
    });

    // Save to localStorage with timestamp
    const saveData = {
        data: formData,
        timestamp: new Date().toISOString(),
        url: window.location.href
    };

    localStorage.setItem('member_registration_draft', JSON.stringify(saveData));
}

// Load saved form data
function loadSavedFormData() {
    const savedData = localStorage.getItem('member_registration_draft');

    if (savedData) {
        try {
            const parsed = JSON.parse(savedData);
            const saveTime = new Date(parsed.timestamp);
            const now = new Date();
            const hoursDiff = (now - saveTime) / (1000 * 60 * 60);

            // Only restore if saved within last 24 hours
            if (hoursDiff < 24) {
                showRestoreDataPrompt(parsed.data);
            } else {
                // Clear old data
                localStorage.removeItem('member_registration_draft');
            }
        } catch (error) {
            console.log('Error loading saved data:', error);
            localStorage.removeItem('member_registration_draft');
        }
    }
}

function showRestoreDataPrompt(savedData) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4">
            <div class="flex items-center mb-4">
                <i class="fas fa-save text-blue-500 text-2xl mr-3"></i>
                <h3 class="text-lg font-semibold">Restore Previous Data?</h3>
            </div>
            <p class="text-gray-700 mb-4">
                We found previously entered data for this form. Would you like to restore it?
            </p>
            <div class="flex justify-end space-x-3">
                <button onclick="clearSavedData(); this.closest('.fixed').remove()"
                        class="px-4 py-2 text-gray-600 hover:text-gray-800">
                    Start Fresh
                </button>
                <button onclick="restoreFormData(${JSON.stringify(savedData).replace(/"/g, '&quot;')}); this.closest('.fixed').remove()"
                        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    Restore Data
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function restoreFormData(savedData) {
    Object.keys(savedData).forEach(key => {
        const element = document.querySelector(`[name="${key}"], #${key}`);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = savedData[key];
                if (key === 'has-children-toggle') {
                    toggleChildrenSection();
                }
            } else if (element.type === 'radio') {
                if (element.value === savedData[key]) {
                    element.checked = true;
                }
            } else {
                element.value = savedData[key];
            }
        }
    });
}

function clearSavedData() {
    localStorage.removeItem('member_registration_draft');
}

// Validation summary before submission
function showValidationSummary() {
    const errors = [];
    const warnings = [];

    // Check required fields
    const requiredFields = document.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            const label = field.closest('div').querySelector('label')?.textContent || field.name;
            errors.push(`${label} is required`);
        }
    });

    // Check for validation errors
    const errorDivs = document.querySelectorAll('[id$="_error"]:not(.hidden)');
    errorDivs.forEach(div => {
        if (div.textContent.trim()) {
            errors.push(div.textContent);
        }
    });

    // Check for warnings
    const warningDivs = document.querySelectorAll('[id$="_warning"]:not(.hidden)');
    warningDivs.forEach(div => {
        if (div.textContent.trim()) {
            warnings.push(div.textContent);
        }
    });

    if (errors.length > 0 || warnings.length > 0) {
        showValidationModal(errors, warnings);
        return false;
    }

    return true;
}

function showValidationModal(errors, warnings) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

    let content = `
        <div class="bg-white rounded-lg p-6 max-w-lg mx-4 max-h-96 overflow-y-auto">
            <div class="flex items-center mb-4">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl mr-3"></i>
                <h3 class="text-lg font-semibold">Form Validation Summary</h3>
            </div>
    `;

    if (errors.length > 0) {
        content += `
            <div class="mb-4">
                <h4 class="font-medium text-red-700 mb-2">Errors (must be fixed):</h4>
                <ul class="list-disc list-inside text-sm text-red-600 space-y-1">
                    ${errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    if (warnings.length > 0) {
        content += `
            <div class="mb-4">
                <h4 class="font-medium text-yellow-700 mb-2">Warnings (please review):</h4>
                <ul class="list-disc list-inside text-sm text-yellow-600 space-y-1">
                    ${warnings.map(warning => `<li>${warning}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    content += `
            <div class="flex justify-end space-x-3">
                <button onclick="this.closest('.fixed').remove()"
                        class="px-4 py-2 text-gray-600 hover:text-gray-800">
                    Review Form
                </button>
                ${warnings.length > 0 && errors.length === 0 ? `
                <button onclick="submitFormAnyway(); this.closest('.fixed').remove()"
                        class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    Submit Anyway
                </button>
                ` : ''}
            </div>
        </div>
    `;

    modal.innerHTML = content;
    document.body.appendChild(modal);
}

function submitFormAnyway() {
    const form = document.querySelector('form');
    form.submit();
}

// Auto-save on input changes
function setupAutoSave() {
    const form = document.querySelector('form');
    let saveTimeout;

    form.addEventListener('input', () => {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(saveFormData, 2000); // Save 2 seconds after user stops typing
    });

    form.addEventListener('change', saveFormData); // Save immediately on select/checkbox changes
}

// Data Quality Indicators
function calculateDataQuality() {
    const requiredFields = [
        'first_name', 'last_name', 'phone_number', 'gender', 'member_status'
    ];

    const optionalFields = [
        'email', 'date_of_birth', 'marital_status', 'location', 'occupation',
        'school', 'emergency_contact_name', 'emergency_contact_phone',
        'baptism_status', 'department', 'role', 'membership_date'
    ];

    let requiredCompleted = 0;
    let optionalCompleted = 0;
    let qualityIssues = [];

    // Check required fields
    requiredFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field && field.value.trim()) {
            requiredCompleted++;
        }
    });

    // Check optional fields
    optionalFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field && field.value.trim()) {
            optionalCompleted++;
        }
    });

    // Check for quality issues
    const emailField = document.querySelector('[name="email"]');
    if (!emailField.value.trim()) {
        qualityIssues.push('Adding an email helps with communication');
    }

    const dobField = document.querySelector('[name="date_of_birth"]');
    if (!dobField.value.trim()) {
        qualityIssues.push('Date of birth helps with age-appropriate ministry placement');
    }

    const emergencyContactField = document.querySelector('[name="emergency_contact_name"]');
    if (!emergencyContactField.value.trim()) {
        qualityIssues.push('Emergency contact information is important for safety');
    }

    const locationField = document.querySelector('[name="location"]');
    if (!locationField.value.trim()) {
        qualityIssues.push('Location helps with local ministry and communication');
    }

    // Calculate completeness percentage
    const totalOptionalFields = optionalFields.length;
    const completenessPercentage = Math.round((optionalCompleted / totalOptionalFields) * 100);

    // Determine quality level
    let qualityLevel = 'poor';
    let qualityColor = 'bg-red-400';
    let qualityText = 'Needs Improvement';

    if (completenessPercentage >= 80) {
        qualityLevel = 'excellent';
        qualityColor = 'bg-green-400';
        qualityText = 'Excellent';
    } else if (completenessPercentage >= 60) {
        qualityLevel = 'good';
        qualityColor = 'bg-blue-400';
        qualityText = 'Good';
    } else if (completenessPercentage >= 40) {
        qualityLevel = 'fair';
        qualityColor = 'bg-yellow-400';
        qualityText = 'Fair';
    }

    // Update UI
    updateDataQualityIndicator(completenessPercentage, qualityColor, qualityText, qualityIssues);

    return {
        completeness: completenessPercentage,
        level: qualityLevel,
        issues: qualityIssues,
        requiredComplete: requiredCompleted === requiredFields.length
    };
}

function updateDataQualityIndicator(percentage, color, text, issues) {
    const qualityIcon = document.getElementById('quality-icon');
    const qualityText = document.getElementById('quality-text');
    const completenessScore = document.getElementById('completeness-score');
    const qualitySuggestions = document.getElementById('quality-suggestions');

    qualityIcon.className = `w-3 h-3 rounded-full ${color} mr-2`;
    qualityText.textContent = text;
    completenessScore.textContent = `${percentage}%`;

    if (issues.length > 0) {
        qualitySuggestions.classList.remove('hidden');
    } else {
        qualitySuggestions.classList.add('hidden');
    }

    // Store issues for suggestions modal
    window.currentQualityIssues = issues;
}

function showQualitySuggestions() {
    const issues = window.currentQualityIssues || [];

    if (issues.length === 0) {
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4">
            <div class="flex items-center mb-4">
                <i class="fas fa-chart-line text-blue-500 text-2xl mr-3"></i>
                <h3 class="text-lg font-semibold">Data Quality Suggestions</h3>
            </div>
            <p class="text-gray-700 mb-4">
                Consider adding the following information to improve the member profile:
            </p>
            <ul class="list-disc list-inside text-sm text-gray-600 space-y-2 mb-4">
                ${issues.map(issue => `<li>${issue}</li>`).join('')}
            </ul>
            <div class="bg-blue-50 border border-blue-200 rounded p-3 mb-4">
                <p class="text-sm text-blue-700">
                    <i class="fas fa-info-circle mr-1"></i>
                    Complete profiles help us serve members better and improve communication.
                </p>
            </div>
            <div class="flex justify-end">
                <button onclick="this.closest('.fixed').remove()"
                        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    Got it
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

// Setup data quality monitoring
function setupDataQualityMonitoring() {
    const form = document.querySelector('form');
    let qualityTimeout;

    form.addEventListener('input', () => {
        clearTimeout(qualityTimeout);
        qualityTimeout = setTimeout(calculateDataQuality, 500);
    });

    form.addEventListener('change', calculateDataQuality);

    // Initial calculation
    setTimeout(calculateDataQuality, 1000);
}

// Load departments and roles when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadDepartments();
    loadRoles();
    loadSavedFormData();
    setupAutoSave();
    setupDataQualityMonitoring();
});

// Load departments from API
async function loadDepartments() {
    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/departments.php');
        const data = await response.json();

        const departmentSelect = document.getElementById('department');
        departmentSelect.innerHTML = '<option value="">Select Department</option>';

        if (data.success && data.data) {
            data.data.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.name;
                option.textContent = dept.display_name;
                departmentSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading departments:', error);
    }
}

// Load roles from API
async function loadRoles() {
    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/roles.php');
        const data = await response.json();

        const roleSelect = document.getElementById('role');
        roleSelect.innerHTML = '<option value="">Select Role</option>';

        if (data.success && data.data) {
            data.data.forEach(role => {
                const option = document.createElement('option');
                option.value = role.name;
                option.textContent = role.display_name;
                roleSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading roles:', error);
    }
}

// Open department manager modal
function openDepartmentManager() {
    document.getElementById('departmentModal').classList.remove('hidden');
    loadDepartmentList();
}

// Open role manager modal
function openRoleManager() {
    document.getElementById('roleModal').classList.remove('hidden');
    loadRoleList();
}

// Close modals
function closeDepartmentModal() {
    document.getElementById('departmentModal').classList.add('hidden');
}

function closeRoleModal() {
    document.getElementById('roleModal').classList.add('hidden');
}

// Load department list for management
async function loadDepartmentList() {
    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/departments.php');
        const data = await response.json();

        const tbody = document.getElementById('departmentTableBody');
        tbody.innerHTML = '';

        if (data.success && data.data) {
            data.data.forEach(dept => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-4 py-2 border-b">${dept.display_name}</td>
                    <td class="px-4 py-2 border-b text-sm text-gray-600">${dept.description || 'No description'}</td>
                    <td class="px-4 py-2 border-b">
                        <button onclick="editDepartment(${dept.id}, '${dept.display_name}', '${dept.description || ''}')" class="text-blue-600 hover:text-blue-800 mr-2">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${dept.name !== 'none' ? `<button onclick="deleteDepartment(${dept.id})" class="text-red-600 hover:text-red-800"><i class="fas fa-trash"></i></button>` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
    } catch (error) {
        console.error('Error loading department list:', error);
    }
}

// Load role list for management
async function loadRoleList() {
    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/roles.php');
        const data = await response.json();

        const tbody = document.getElementById('roleTableBody');
        tbody.innerHTML = '';

        if (data.success && data.data) {
            data.data.forEach(role => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-4 py-2 border-b">${role.display_name}</td>
                    <td class="px-4 py-2 border-b text-sm text-gray-600">${role.description || 'No description'}</td>
                    <td class="px-4 py-2 border-b">
                        <button onclick="editRole(${role.id}, '${role.display_name}', '${role.description || ''}')" class="text-blue-600 hover:text-blue-800 mr-2">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${role.name !== 'member' ? `<button onclick="deleteRole(${role.id})" class="text-red-600 hover:text-red-800"><i class="fas fa-trash"></i></button>` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
    } catch (error) {
        console.error('Error loading role list:', error);
    }
}

// Add new department
async function addDepartment(event) {
    event.preventDefault();

    const displayName = document.getElementById('newDeptDisplayName').value.trim();
    const description = document.getElementById('newDeptDescription').value.trim();

    // Validate required field
    if (!displayName) {
        alert('Display Name is required');
        document.getElementById('newDeptDisplayName').focus();
        return;
    }

    const name = displayName.toLowerCase().replace(/[^a-z0-9]/g, '_');

    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/departments.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                display_name: displayName,
                description: description
            })
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('addDepartmentForm').reset();
            loadDepartmentList();
            loadDepartments(); // Refresh main dropdown
            showNotification('Department added successfully!', 'success');
        } else {
            showNotification(data.message || 'Error adding department', 'error');
        }
    } catch (error) {
        console.error('Error adding department:', error);
        showNotification('Error adding department', 'error');
    }
}

// Add new role
async function addRole(event) {
    event.preventDefault();

    const displayName = document.getElementById('newRoleDisplayName').value.trim();
    const description = document.getElementById('newRoleDescription').value.trim();

    // Validate required field
    if (!displayName) {
        alert('Display Name is required');
        document.getElementById('newRoleDisplayName').focus();
        return;
    }

    const name = displayName.toLowerCase().replace(/[^a-z0-9]/g, '_');

    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/roles.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                display_name: displayName,
                description: description
            })
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('addRoleForm').reset();
            loadRoleList();
            loadRoles(); // Refresh main dropdown
            showNotification('Role added successfully!', 'success');
        } else {
            showNotification(data.message || 'Error adding role', 'error');
        }
    } catch (error) {
        console.error('Error adding role:', error);
        showNotification('Error adding role', 'error');
    }
}

// Edit department
function editDepartment(id, displayName, description) {
    const newDisplayName = prompt('Enter new display name:', displayName);
    if (newDisplayName && newDisplayName !== displayName) {
        updateDepartment(id, newDisplayName, description);
    }
}

// Edit role
function editRole(id, displayName, description) {
    const newDisplayName = prompt('Enter new display name:', displayName);
    if (newDisplayName && newDisplayName !== displayName) {
        updateRole(id, newDisplayName, description);
    }
}

// Update department
async function updateDepartment(id, displayName, description) {
    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/departments.php', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: id,
                display_name: displayName,
                description: description
            })
        });

        const data = await response.json();

        if (data.success) {
            loadDepartmentList();
            loadDepartments(); // Refresh main dropdown
            showNotification('Department updated successfully!', 'success');
        } else {
            showNotification(data.message || 'Error updating department', 'error');
        }
    } catch (error) {
        console.error('Error updating department:', error);
        showNotification('Error updating department', 'error');
    }
}

// Update role
async function updateRole(id, displayName, description) {
    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/roles.php', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: id,
                display_name: displayName,
                description: description
            })
        });

        const data = await response.json();

        if (data.success) {
            loadRoleList();
            loadRoles(); // Refresh main dropdown
            showNotification('Role updated successfully!', 'success');
        } else {
            showNotification(data.message || 'Error updating role', 'error');
        }
    } catch (error) {
        console.error('Error updating role:', error);
        showNotification('Error updating role', 'error');
    }
}

// Delete department
async function deleteDepartment(id) {
    if (!confirm('Are you sure you want to delete this department?')) {
        return;
    }

    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/departments.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id })
        });

        const data = await response.json();

        if (data.success) {
            loadDepartmentList();
            loadDepartments(); // Refresh main dropdown
            showNotification('Department deleted successfully!', 'success');
        } else {
            showNotification(data.message || 'Error deleting department', 'error');
        }
    } catch (error) {
        console.error('Error deleting department:', error);
        showNotification('Error deleting department', 'error');
    }
}

// Delete role
async function deleteRole(id) {
    if (!confirm('Are you sure you want to delete this role?')) {
        return;
    }

    try {
        const response = await fetch('<?php echo BASE_URL; ?>api/roles.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id })
        });

        const data = await response.json();

        if (data.success) {
            loadRoleList();
            loadRoles(); // Refresh main dropdown
            showNotification('Role deleted successfully!', 'success');
        } else {
            showNotification(data.message || 'Error deleting role', 'error');
        }
    } catch (error) {
        console.error('Error deleting role:', error);
        showNotification('Error deleting role', 'error');
    }
}

// Show notification
function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        document.body.removeChild(notification);
    }, 3000);
}
</script>

<!-- Department Management Modal -->
<div id="departmentModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Manage Departments</h3>
                <button onclick="closeDepartmentModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Add New Department Form -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-md font-medium mb-3">Add New Department</h4>
                <form id="addDepartmentForm" onsubmit="addDepartment(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Display Name <span class="text-red-500">*</span></label>
                            <input type="text" id="newDeptDisplayName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <input type="text" id="newDeptDescription" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark">
                            <i class="fas fa-plus mr-2"></i>Add Department
                        </button>
                    </div>
                </form>
            </div>

            <!-- Departments List -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="px-4 py-2 text-left">Name</th>
                            <th class="px-4 py-2 text-left">Description</th>
                            <th class="px-4 py-2 text-left">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="departmentTableBody">
                        <!-- Departments will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Role Management Modal -->
<div id="roleModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Manage Roles</h3>
                <button onclick="closeRoleModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Add New Role Form -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-md font-medium mb-3">Add New Role</h4>
                <form id="addRoleForm" onsubmit="addRole(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Display Name <span class="text-red-500">*</span></label>
                            <input type="text" id="newRoleDisplayName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <input type="text" id="newRoleDescription" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark">
                            <i class="fas fa-plus mr-2"></i>Add Role
                        </button>
                    </div>
                </form>
            </div>

            <!-- Roles List -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="px-4 py-2 text-left">Name</th>
                            <th class="px-4 py-2 text-left">Description</th>
                            <th class="px-4 py-2 text-left">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="roleTableBody">
                        <!-- Roles will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Get the contents of the output buffer
$content = ob_get_clean();

// Include the layout template
include 'views/layouts/main.php';
?>
