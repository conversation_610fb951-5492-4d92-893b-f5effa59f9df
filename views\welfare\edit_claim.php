<?php
// Prevent direct access
if (!defined('BASE_URL')) {
    exit('Direct access not permitted');
}
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Edit Welfare Claim</h1>
            <p class="text-gray-600 mt-1">Update welfare claim details</p>
        </div>
        <a href="<?php echo BASE_URL; ?>welfare" 
           class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Welfare
        </a>
    </div>

    <!-- Edit Claim Form -->
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Claim Information</h2>
                <p class="text-sm text-gray-600">Update the claim details below</p>
            </div>
            
            <form method="POST" action="<?php echo BASE_URL; ?>welfare/edit-claim/<?php echo $claim['id']; ?>" class="p-6 space-y-6">
                <input type="hidden" name="claim_id" value="<?php echo $claim['id']; ?>">
                
                <!-- Member Selection -->
                <div>
                    <label for="member_id" class="block text-sm font-medium text-gray-700 mb-2">Member *</label>
                    <select name="member_id" id="member_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <option value="">Select Member</option>
                        <?php foreach ($members as $member): ?>
                            <option value="<?php echo $member['id']; ?>" 
                                    <?php echo ($member['id'] == $claim['member_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Claim Amount -->
                <div>
                    <label for="claim_amount" class="block text-sm font-medium text-gray-700 mb-2">Claim Amount (₵) *</label>
                    <input type="number" name="claim_amount" id="claim_amount" step="0.01" min="0" required
                           value="<?php echo htmlspecialchars($claim['claim_amount']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                </div>

                <!-- Claim Reason -->
                <div>
                    <label for="claim_reason" class="block text-sm font-medium text-gray-700 mb-2">Reason for Claim *</label>
                    <textarea name="claim_reason" id="claim_reason" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                              placeholder="Describe the reason for this welfare claim..."><?php echo htmlspecialchars($claim['claim_reason']); ?></textarea>
                </div>

                <!-- Claim Date -->
                <div>
                    <label for="claim_date" class="block text-sm font-medium text-gray-700 mb-2">Claim Date *</label>
                    <input type="date" name="claim_date" id="claim_date" required
                           value="<?php echo htmlspecialchars($claim['claim_date']); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
                    <select name="status" id="status" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <option value="">Select Status</option>
                        <option value="pending" <?php echo ($claim['status'] == 'pending') ? 'selected' : ''; ?>>Pending</option>
                        <option value="approved" <?php echo ($claim['status'] == 'approved') ? 'selected' : ''; ?>>Approved</option>
                        <option value="disbursed" <?php echo ($claim['status'] == 'disbursed') ? 'selected' : ''; ?>>Disbursed</option>
                        <option value="rejected" <?php echo ($claim['status'] == 'rejected') ? 'selected' : ''; ?>>Rejected</option>
                    </select>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea name="notes" id="notes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                              placeholder="Additional notes about this claim..."><?php echo htmlspecialchars($claim['notes'] ?? ''); ?></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="<?php echo BASE_URL; ?>welfare" 
                       class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-save mr-2"></i>
                        Update Claim
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set max date to today
    const claimDateInput = document.getElementById('claim_date');
    const today = new Date().toISOString().split('T')[0];
    claimDateInput.setAttribute('max', today);
});
</script>
