<?php
/**
 * Standalone Child Registration Success Page
 */

// Set page variables
$page_title = $page_title ?? "Registration Successful - Children's Ministry";
$active_page = 'children_ministry';

// Start output buffering
ob_start();
?>

<div class="max-w-4xl mx-auto px-4 py-8">
    <!-- Success Header -->
    <div class="bg-white rounded-lg border border-gray-200 p-8 mb-8 text-center">
        <div class="w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-6">
            <i class="fas fa-check text-3xl text-green-600"></i>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Registration Successful!</h1>
        <p class="text-gray-600 text-lg">
            Your child has been successfully registered for our Children's Ministry programs.
        </p>
    </div>

    <!-- Registration Details -->
    <?php if (isset($member)): ?>
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden mb-8">
        <div class="bg-blue-50 px-6 py-4 border-b border-blue-200">
            <h3 class="text-lg font-semibold text-blue-900">Registration Details</h3>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Child Information -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-child mr-2 text-blue-600"></i>
                        Child Information
                    </h4>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Name</label>
                            <p class="text-gray-900 font-medium"><?php echo htmlspecialchars($member->first_name . ' ' . $member->last_name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Date of Birth</label>
                            <p class="text-gray-900"><?php echo date('F j, Y', strtotime($member->date_of_birth)); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Age</label>
                            <p class="text-gray-900"><?php echo floor((time() - strtotime($member->date_of_birth)) / (365.25 * 24 * 3600)); ?> years old</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Gender</label>
                            <p class="text-gray-900 capitalize"><?php echo htmlspecialchars($member->gender); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Registration ID</label>
                            <p class="text-gray-900 font-mono"><?php echo $member->id; ?></p>
                        </div>
                    </div>
                </div>

                <!-- Guardian Information -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-user-shield mr-2 text-green-600"></i>
                        Guardian Information
                    </h4>

                    <?php if (!empty($guardians)): ?>
                        <?php foreach ($guardians as $guardian): ?>
                        <div class="border border-gray-200 rounded-lg p-4 mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="font-medium text-gray-900">
                                    <?php echo htmlspecialchars($guardian['first_name'] . ' ' . $guardian['last_name']); ?>
                                </h5>
                                <?php if ($guardian['is_primary']): ?>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">Primary Guardian</span>
                                <?php endif; ?>
                            </div>
                            <div class="space-y-1 text-sm">
                                <p class="text-gray-600">
                                    <i class="fas fa-phone mr-1"></i>
                                    <?php echo htmlspecialchars($guardian['phone_number']); ?>
                                </p>
                                <?php if (!empty($guardian['email'])): ?>
                                <p class="text-gray-600">
                                    <i class="fas fa-envelope mr-1"></i>
                                    <?php echo htmlspecialchars($guardian['email']); ?>
                                </p>
                                <?php endif; ?>
                                <?php if (!empty($guardian['address'])): ?>
                                <p class="text-gray-600">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <?php echo htmlspecialchars($guardian['address']); ?>
                                </p>
                                <?php endif; ?>
                                <?php if (!empty($guardian['occupation'])): ?>
                                <p class="text-gray-600">
                                    <i class="fas fa-briefcase mr-1"></i>
                                    <?php echo htmlspecialchars($guardian['occupation']); ?>
                                </p>
                                <?php endif; ?>
                                <p class="text-gray-600">
                                    <i class="fas fa-heart mr-1"></i>
                                    <?php echo ucfirst(str_replace('_', ' ', htmlspecialchars($guardian['relationship_type']))); ?>
                                </p>
                                <?php if ($guardian['can_pickup']): ?>
                                <p class="text-green-600 text-xs">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Authorized for pickup
                                </p>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php elseif (!empty($primary_guardian)): ?>
                        <!-- Fallback to primary guardian if guardians array is empty -->
                        <div class="border border-gray-200 rounded-lg p-4 mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="font-medium text-gray-900">
                                    <?php echo htmlspecialchars($primary_guardian['first_name'] . ' ' . $primary_guardian['last_name']); ?>
                                </h5>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">Primary Guardian</span>
                            </div>
                            <div class="space-y-1 text-sm">
                                <p class="text-gray-600">
                                    <i class="fas fa-phone mr-1"></i>
                                    <?php echo htmlspecialchars($primary_guardian['phone_number']); ?>
                                </p>
                                <?php if (!empty($primary_guardian['email'])): ?>
                                <p class="text-gray-600">
                                    <i class="fas fa-envelope mr-1"></i>
                                    <?php echo htmlspecialchars($primary_guardian['email']); ?>
                                </p>
                                <?php endif; ?>
                                <?php if (!empty($primary_guardian['address'])): ?>
                                <p class="text-gray-600">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <?php echo htmlspecialchars($primary_guardian['address']); ?>
                                </p>
                                <?php endif; ?>
                                <p class="text-gray-600">
                                    <i class="fas fa-heart mr-1"></i>
                                    <?php echo ucfirst(str_replace('_', ' ', htmlspecialchars($primary_guardian['relationship_type']))); ?>
                                </p>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Fallback to emergency contact information from member record -->
                        <div class="border border-gray-200 rounded-lg p-4 mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="font-medium text-gray-900"><?php echo htmlspecialchars($member->emergency_contact_name); ?></h5>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">Emergency Contact</span>
                            </div>
                            <div class="space-y-1 text-sm">
                                <p class="text-gray-600">
                                    <i class="fas fa-phone mr-1"></i>
                                    <?php echo htmlspecialchars($member->emergency_contact_phone); ?>
                                </p>
                                <?php if (!empty($member->location)): ?>
                                <p class="text-gray-600">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <?php echo htmlspecialchars($member->location); ?>
                                </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Next Steps -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden mb-8">
        <div class="bg-green-50 px-6 py-4 border-b border-green-200">
            <h3 class="text-lg font-semibold text-green-900 flex items-center">
                <i class="fas fa-list-check mr-2"></i>
                Next Steps
            </h3>
        </div>
        
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-blue-600 font-semibold text-sm">1</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Add Medical Information</h4>
                        <p class="text-gray-600 text-sm mt-1">
                            Complete your child's medical information including allergies, medications, and special needs for their safety.
                        </p>
                        <a href="<?php echo BASE_URL; ?>children-ministry/medical-info?child_id=<?php echo $member->id ?? ''; ?>" 
                           class="inline-flex items-center mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium">
                            <i class="fas fa-heartbeat mr-1"></i>
                            Add Medical Information
                        </a>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-blue-600 font-semibold text-sm">2</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Attend Children's Ministry</h4>
                        <p class="text-gray-600 text-sm mt-1">
                            Bring your child to our next children's ministry service. Our staff will help with the check-in process.
                        </p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-blue-600 font-semibold text-sm">3</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Connect with Our Team</h4>
                        <p class="text-gray-600 text-sm mt-1">
                            Our children's ministry team will reach out to welcome you and answer any questions you may have.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row justify-center gap-4">
        <a href="<?php echo BASE_URL; ?>children-ministry" 
           class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium transition-colors">
            <i class="fas fa-home mr-2"></i>
            Children's Ministry Dashboard
        </a>
        <a href="<?php echo BASE_URL; ?>children-ministry/medical-info?child_id=<?php echo $member->id ?? ''; ?>" 
           class="inline-flex items-center justify-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium transition-colors">
            <i class="fas fa-heartbeat mr-2"></i>
            Add Medical Information
        </a>
        <a href="<?php echo BASE_URL; ?>children-ministry/standalone-registration" 
           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
            <i class="fas fa-plus mr-2"></i>
            Register Another Child
        </a>
    </div>
</div>

<script>
// Auto-scroll to top on page load
window.addEventListener('load', function() {
    window.scrollTo(0, 0);
});

// Show success animation
document.addEventListener('DOMContentLoaded', function() {
    const successIcon = document.querySelector('.fa-check');
    if (successIcon) {
        successIcon.style.transform = 'scale(0)';
        setTimeout(() => {
            successIcon.style.transition = 'transform 0.5s ease-out';
            successIcon.style.transform = 'scale(1)';
        }, 300);
    }
});
</script>

<?php
// Get the contents of the output buffer
$content = ob_get_clean();

// Load layout
require_once 'views/layouts/main.php';
?>
