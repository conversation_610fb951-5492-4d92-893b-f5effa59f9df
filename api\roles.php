<?php
/**
 * API endpoint for role management
 */

header('Content-Type: application/json');

// CRITICAL SECURITY FIX: Restrict CORS to application domain only
// The wildcard '*' is dangerous and allows any website to access this API
$allowed_origins = [
    'http://localhost',
    'http://localhost:8000',
    'http://127.0.0.1',
    'http://127.0.0.1:8000'
];

// Add production domain from environment variable if available
if ($production_url = getenv('APP_URL')) {
    $allowed_origins[] = rtrim($production_url, '/');
}

// Get the origin of the request
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

// Only allow requests from approved origins
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    // For same-origin requests (no Origin header), allow localhost
    if (empty($origin) && (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false || 
                          strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false)) {
        header('Access-Control-Allow-Origin: http://' . $_SERVER['HTTP_HOST']);
    }
    // If origin is not allowed, we don't set the CORS header
    // This will cause the browser to block the request
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../helpers/functions.php';

// Start session for CSRF protection
session_start();

$database = new Database();
$conn = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            // Get all roles
            $stmt = $conn->prepare("SELECT * FROM roles WHERE is_active = 1 ORDER BY sort_order, display_name");
            $stmt->execute();
            $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'data' => $roles
            ]);
            break;
            
        case 'POST':
            // Create new role
            if (!isset($input['name']) || !isset($input['display_name'])) {
                throw new Exception('Name and display name are required');
            }
            
            $name = sanitize($input['name']);
            $display_name = sanitize($input['display_name']);
            $description = sanitize($input['description'] ?? '');
            $sort_order = intval($input['sort_order'] ?? 999);
            
            // Check if role already exists
            $stmt = $conn->prepare("SELECT id FROM roles WHERE name = ? OR display_name = ?");
            $stmt->execute([$name, $display_name]);
            if ($stmt->rowCount() > 0) {
                throw new Exception('Role with this name already exists');
            }
            
            $stmt = $conn->prepare("INSERT INTO roles (name, display_name, description, sort_order) VALUES (?, ?, ?, ?)");
            $stmt->execute([$name, $display_name, $description, $sort_order]);

            $role_id = $conn->lastInsertId();

            // Automatically update ENUM constraints to include new role
            try {
                $stmt = $conn->prepare("SELECT name FROM roles WHERE is_active = 1 ORDER BY name");
                $stmt->execute();
                $all_roles = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $role_enum_values = "'" . implode("','", $all_roles) . "'";
                $enum_sql = "ALTER TABLE members MODIFY COLUMN role ENUM($role_enum_values) DEFAULT 'member'";
                $conn->prepare($enum_sql)->execute();
            } catch (Exception $e) {
                error_log("Failed to update role ENUM: " . $e->getMessage());
            }

            // Get the created role
            $stmt = $conn->prepare("SELECT * FROM roles WHERE id = ?");
            $stmt->execute([$role_id]);
            $role = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'message' => 'Role created successfully',
                'data' => $role
            ]);
            break;
            
        case 'PUT':
            // Update role
            if (!isset($input['id'])) {
                throw new Exception('Role ID is required');
            }
            
            $id = intval($input['id']);
            $display_name = sanitize($input['display_name']);
            $description = sanitize($input['description'] ?? '');
            $sort_order = intval($input['sort_order'] ?? 0);
            
            // Check if role exists
            $stmt = $conn->prepare("SELECT id FROM roles WHERE id = ?");
            $stmt->execute([$id]);
            if ($stmt->rowCount() === 0) {
                throw new Exception('Role not found');
            }
            
            $stmt = $conn->prepare("UPDATE roles SET display_name = ?, description = ?, sort_order = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$display_name, $description, $sort_order, $id]);
            
            // Get the updated role
            $stmt = $conn->prepare("SELECT * FROM roles WHERE id = ?");
            $stmt->execute([$id]);
            $role = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'message' => 'Role updated successfully',
                'data' => $role
            ]);
            break;
            
        case 'DELETE':
            // Delete role (soft delete by setting is_active = 0)
            if (!isset($input['id'])) {
                throw new Exception('Role ID is required');
            }
            
            $id = intval($input['id']);
            
            // Check if role exists
            $stmt = $conn->prepare("SELECT name FROM roles WHERE id = ?");
            $stmt->execute([$id]);
            $role = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$role) {
                throw new Exception('Role not found');
            }
            
            // Don't allow deletion of 'member' role
            if ($role['name'] === 'member') {
                throw new Exception('Cannot delete the default "Member" role');
            }
            
            // Check if any members are using this role
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM members WHERE role = ?");
            $stmt->execute([$role['name']]);
            $member_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($member_count > 0) {
                throw new Exception("Cannot delete role. $member_count members are currently assigned to this role.");
            }
            
            // Soft delete the role
            $stmt = $conn->prepare("UPDATE roles SET is_active = 0, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$id]);

            // Automatically update ENUM constraints to remove deactivated role
            try {
                $stmt = $conn->prepare("SELECT name FROM roles WHERE is_active = 1 ORDER BY name");
                $stmt->execute();
                $all_roles = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $role_enum_values = "'" . implode("','", $all_roles) . "'";
                $enum_sql = "ALTER TABLE members MODIFY COLUMN role ENUM($role_enum_values) DEFAULT 'member'";
                $conn->prepare($enum_sql)->execute();
            } catch (Exception $e) {
                error_log("Failed to update role ENUM after deletion: " . $e->getMessage());
            }

            echo json_encode([
                'success' => true,
                'message' => 'Role deleted successfully'
            ]);
            break;
            
        default:
            throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
