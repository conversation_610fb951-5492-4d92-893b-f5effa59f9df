<?php
/**
 * Children Age Group Model
 * Manages age-based groupings for children's ministry
 */

class ChildrenAgeGroup {
    // Database connection and table name
    private $conn;
    private $table_name = "children_age_groups";

    // Object properties
    public $id;
    public $name;
    public $min_age;
    public $max_age;
    public $description;
    public $is_active;
    public $created_at;
    public $updated_at;

    // Error handling
    public $error;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create age group
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET name = :name,
                      min_age = :min_age,
                      max_age = :max_age,
                      description = :description,
                      is_active = :is_active,
                      created_at = :created_at,
                      updated_at = :updated_at";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->min_age = htmlspecialchars(strip_tags($this->min_age));
        $this->max_age = htmlspecialchars(strip_tags($this->max_age));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->is_active = $this->is_active ? 1 : 0;

        // Bind data
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':min_age', $this->min_age);
        $stmt->bindParam(':max_age', $this->max_age);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':is_active', $this->is_active);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Get all age groups
     *
     * @param bool $active_only
     * @return PDOStatement
     */
    public function getAll($active_only = true) {
        $query = "SELECT * FROM " . $this->table_name;
        
        if ($active_only) {
            $query .= " WHERE is_active = 1";
        }
        
        $query .= " ORDER BY min_age ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get age group by ID
     *
     * @param int $id
     * @return array|false
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get age group for a specific age
     *
     * @param int $age
     * @return array|false
     */
    public function getByAge($age) {
        $query = "SELECT * FROM " . $this->table_name . "
                  WHERE :age BETWEEN min_age AND max_age
                  AND is_active = 1
                  ORDER BY min_age ASC
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':age', $age);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get children count by age group
     *
     * @return PDOStatement
     */
    public function getChildrenCountByAgeGroup() {
        $query = "SELECT ag.*, 
                         COUNT(m.id) as children_count
                  FROM " . $this->table_name . " ag
                  LEFT JOIN members m ON (
                      TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) BETWEEN ag.min_age AND ag.max_age
                      AND m.member_status = 'active'
                  )
                  WHERE ag.is_active = 1
                  GROUP BY ag.id
                  ORDER BY ag.min_age ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get children in specific age group
     *
     * @param int $age_group_id
     * @return PDOStatement
     */
    public function getChildrenInAgeGroup($age_group_id) {
        $query = "SELECT m.*, 
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                         ag.name as age_group_name
                  FROM members m
                  JOIN " . $this->table_name . " ag ON (
                      TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) BETWEEN ag.min_age AND ag.max_age
                  )
                  WHERE ag.id = :age_group_id
                  AND m.member_status = 'active'
                  ORDER BY m.created_at DESC, m.date_of_birth DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':age_group_id', $age_group_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Update age group
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET name = :name,
                      min_age = :min_age,
                      max_age = :max_age,
                      description = :description,
                      is_active = :is_active,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->min_age = htmlspecialchars(strip_tags($this->min_age));
        $this->max_age = htmlspecialchars(strip_tags($this->max_age));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->is_active = $this->is_active ? 1 : 0;

        // Bind data
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':min_age', $this->min_age);
        $stmt->bindParam(':max_age', $this->max_age);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':is_active', $this->is_active);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Delete age group
     *
     * @return bool
     */
    public function delete() {
        try {
            // Clear any previous errors
            $this->error = null;

            // Check if age group has children that fall within its age range
            $check_query = "SELECT COUNT(*) as count FROM members m
                           JOIN " . $this->table_name . " ag ON (
                               TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) BETWEEN ag.min_age AND ag.max_age
                           )
                           WHERE ag.id = :id
                           AND m.member_status = 'active'
                           AND m.date_of_birth IS NOT NULL";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(':id', $this->id);
            $check_stmt->execute();
            $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

            if ($result['count'] > 0) {
                $this->error = 'Cannot delete age group. It currently has ' . $result['count'] . ' children whose ages fall within this group\'s range.';
                return false; // Cannot delete age group with children in age range
            }

            // Safe to delete - no children assigned
            $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $this->id);

            if ($stmt->execute()) {
                return true;
            } else {
                $this->error = 'Error deleting age group. Please try again.';
                return false;
            }
        } catch (Exception $e) {
            $this->error = 'Database error: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * Check if age range overlaps with existing groups
     *
     * @param int $min_age
     * @param int $max_age
     * @param int $exclude_id
     * @return bool
     */
    public function hasOverlappingRange($min_age, $max_age, $exclude_id = null) {
        $query = "SELECT id FROM " . $this->table_name . "
                  WHERE (
                      (:min_age BETWEEN min_age AND max_age) OR
                      (:max_age BETWEEN min_age AND max_age) OR
                      (min_age BETWEEN :min_age AND :max_age) OR
                      (max_age BETWEEN :min_age AND :max_age)
                  )";
        
        if ($exclude_id) {
            $query .= " AND id != :exclude_id";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':min_age', $min_age);
        $stmt->bindParam(':max_age', $max_age);
        
        if ($exclude_id) {
            $stmt->bindParam(':exclude_id', $exclude_id);
        }
        
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    /**
     * Get age group statistics
     *
     * @return array
     */
    public function getAgeGroupStats() {
        $query = "SELECT 
                    COUNT(*) as total_age_groups,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_groups,
                    MIN(min_age) as youngest_age,
                    MAX(max_age) as oldest_age
                  FROM " . $this->table_name;

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

}
