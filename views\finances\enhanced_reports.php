<!-- Enhanced Finance Reports -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-green-50 to-blue-50">
    <div class="container mx-auto px-4 py-8">
        
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Financial Reports</h1>
                    <p class="text-gray-600">Comprehensive financial analytics and insights</p>
                </div>
                <div class="bg-gradient-to-r from-green-100 to-blue-100 p-4 rounded-lg">
                    <i class="fas fa-chart-bar text-2xl text-green-600"></i>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Total Income -->
            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-100 text-sm">Total Income</p>
                        <p class="text-2xl font-bold">GH₵ <?= number_format($total_income ?? 0, 2) ?></p>
                    </div>
                    <div class="bg-white/20 p-3 rounded-lg">
                        <i class="fas fa-arrow-up text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Expenses -->
            <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl p-6 text-white shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-red-100 text-sm">Total Expenses</p>
                        <p class="text-2xl font-bold">GH₵ <?= number_format($total_expenses ?? 0, 2) ?></p>
                    </div>
                    <div class="bg-white/20 p-3 rounded-lg">
                        <i class="fas fa-arrow-down text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Net Balance -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-100 text-sm">Net Balance</p>
                        <p class="text-2xl font-bold">GH₵ <?= number_format(($total_income ?? 0) - ($total_expenses ?? 0), 2) ?></p>
                    </div>
                    <div class="bg-white/20 p-3 rounded-lg">
                        <i class="fas fa-balance-scale text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Transactions -->
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-6 text-white shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-100 text-sm">Total Transactions</p>
                        <p class="text-2xl font-bold"><?= number_format($total_transactions ?? 0) ?></p>
                    </div>
                    <div class="bg-white/20 p-3 rounded-lg">
                        <i class="fas fa-receipt text-xl"></i>
                    </div>
                </div>
            </div>

        </div>

        <!-- Charts and Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            
            <!-- Income by Category -->
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Income by Category</h3>
                <div class="space-y-3">
                    <?php if (isset($income_by_category) && !empty($income_by_category)): ?>
                        <?php foreach ($income_by_category as $category): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                                    <span class="font-medium capitalize"><?= htmlspecialchars($category->category) ?></span>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-green-600">GH₵ <?= number_format($category->total, 2) ?></p>
                                    <p class="text-xs text-gray-500"><?= $category->count ?> transactions</p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-gray-500 text-center py-8">No income data available</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Expenses by Subcategory -->
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Expenses by Type</h3>
                <div class="space-y-3">
                    <?php if (isset($expenses_by_subcategory) && !empty($expenses_by_subcategory)): ?>
                        <?php foreach ($expenses_by_subcategory as $subcategory): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
                                    <span class="font-medium"><?= htmlspecialchars($subcategory->subcategory ?: 'General') ?></span>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-red-600">GH₵ <?= number_format($subcategory->total, 2) ?></p>
                                    <p class="text-xs text-gray-500"><?= $subcategory->count ?> transactions</p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-gray-500 text-center py-8">No expense data available</p>
                    <?php endif; ?>
                </div>
            </div>

        </div>

        <!-- Payment Methods Analysis -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            
            <!-- Payment Methods -->
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Payment Methods</h3>
                <div class="space-y-3">
                    <?php if (isset($payment_methods) && !empty($payment_methods)): ?>
                        <?php foreach ($payment_methods as $method): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                                    <span class="font-medium capitalize"><?= str_replace('_', ' ', htmlspecialchars($method->payment_method)) ?></span>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-blue-600">GH₵ <?= number_format($method->total, 2) ?></p>
                                    <p class="text-xs text-gray-500"><?= $method->count ?> transactions</p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-gray-500 text-center py-8">No payment method data available</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Monthly Trends -->
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Monthly Trends</h3>
                <div class="space-y-3">
                    <?php if (isset($monthly_trends) && !empty($monthly_trends)): ?>
                        <?php foreach ($monthly_trends as $month): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-purple-500 rounded-full mr-3"></div>
                                    <span class="font-medium"><?= date('F Y', strtotime($month->month . '-01')) ?></span>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-purple-600">GH₵ <?= number_format($month->net_amount, 2) ?></p>
                                    <p class="text-xs text-gray-500"><?= $month->transaction_count ?> transactions</p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-gray-500 text-center py-8">No monthly data available</p>
                    <?php endif; ?>
                </div>
            </div>

        </div>

        <!-- Recent Transactions -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-xl font-bold text-gray-800">Recent Transactions</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (isset($recent_transactions) && !empty($recent_transactions)): ?>
                            <?php foreach ($recent_transactions as $transaction): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?= date('M d, Y', strtotime($transaction->transaction_date)) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= htmlspecialchars($transaction->reference_number ?: 'N/A') ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            <?= $transaction->transaction_type === 'income' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                            <i class="fas fa-arrow-<?= $transaction->transaction_type === 'income' ? 'up' : 'down' ?> mr-1"></i>
                                            <?= ucfirst($transaction->transaction_type) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                                        <?= htmlspecialchars(str_replace('_', ' ', $transaction->category)) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium
                                        <?= $transaction->transaction_type === 'income' ? 'text-green-600' : 'text-red-600' ?>">
                                        GH₵ <?= number_format($transaction->amount, 2) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize">
                                        <?= htmlspecialchars(str_replace('_', ' ', $transaction->payment_method ?: 'N/A')) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                    No transactions found
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>

<style>
/* Enhanced report styles */
.hover\:bg-gray-50:hover {
    background-color: #f9fafb;
}

/* Responsive table */
@media (max-width: 768px) {
    .overflow-x-auto {
        -webkit-overflow-scrolling: touch;
    }
}

/* Animation for cards */
.bg-white {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.bg-white:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
</style>
