<?php
/**
 * Member Controller
 */

require_once 'config/database.php';
require_once 'models/Member.php';
require_once 'utils/validation.php';
require_once 'helpers/functions.php';
require_once 'utils/helpers.php';

class MemberController {
    private $database;
    private $member;
    private $setting;

    /**
     * Constructor
     */
    public function __construct() {
        // Start session if not already started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        try {
            $this->database = new Database();
            $this->member = new Member($this->database->getConnection());
            $this->setting = new Setting($this->database->getConnection());
        } catch (Exception $e) {
            error_log("MemberController constructor error: " . $e->getMessage());
            $this->setting = null;
        }
    }

    /**
     * Display members list with pagination and search
     *
     * @return void
     */
    public function index() {
        try {
            // Get pagination and search parameters
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 25; // Members per page
            $search = trim($_GET['search'] ?? '');
            $department = $_GET['department'] ?? '';
            $status = $_GET['status'] ?? 'active';

            // Calculate offset
            $offset = ($page - 1) * $limit;

            // Get filtered members with pagination
            $members_data = $this->getMembersWithPagination($limit, $offset, $search, $department, $status);
            $members = $members_data['members'];
            $total_members = $members_data['total'];
            $total_pages = ceil($total_members / $limit);

            // Get member count for stats
            $member_count = $this->member->getCount();

            // Get departments for filter dropdown
            $departments = $this->getUniqueDepartments();

            // Get department styling data
            $department_styles = $this->getDepartmentStyles();

            // Set page variables
            $page_title = getPageTitle('Members Directory');
            $active_page = 'members';

            // Pagination data
            $pagination = [
                'current_page' => $page,
                'total_pages' => $total_pages,
                'total_members' => $total_members,
                'limit' => $limit,
                'has_prev' => $page > 1,
                'has_next' => $page < $total_pages,
                'prev_page' => $page - 1,
                'next_page' => $page + 1
            ];

            // Load view
            require_once 'views/members/index.php';

        } catch (Exception $e) {
            error_log("Members index error: " . $e->getMessage());
            set_flash_message('Unable to load members. Please try again.', 'danger');
            redirect('dashboard');
        }
    }

    /**
     * Display member details
     *
     * @param int|null $id Member ID (from URL parameter or $_GET)
     * @return void
     */
    public function show($id = null) {
        try {
            // Get ID from parameter or fallback to $_GET for backward compatibility
            if ($id === null) {
                $id = $_GET['id'] ?? null;
            }

            // Convert to integer and validate
            $id = (int)$id;

            // Check if ID is valid (greater than 0)
            if ($id <= 0) {
                set_flash_message('Member ID is required.', 'danger');
                redirect('members');
            }

            // Sanitize the ID
            $id = sanitize($id);

            // Get member by ID
            if (!$this->member->getById($id)) {
                set_flash_message('Member not found.', 'danger');
                redirect('members');
            }

            // Load children data
            require_once 'models/FamilyRelationship.php';
            $familyRelationship = new FamilyRelationship($this->database->getConnection());

            // Get relationships where this member is the parent
            $parent_relationships = $familyRelationship->getChildrenByParent($id)->fetchAll(PDO::FETCH_ASSOC);

            // Get relationships where this member is the child
            $child_relationships = $familyRelationship->getParentsByChild($id)->fetchAll(PDO::FETCH_ASSOC);

            // Load complete parent data with optimized query
            $parents_data = [];
            if (!empty($child_relationships)) {
                $parent_ids = array_column($child_relationships, 'parent_id');
                $parents_data = $this->getMembersByIds($parent_ids, $child_relationships, 'parent');
            }

            // Load complete children data with optimized query
            $children_data = [];
            if (!empty($parent_relationships)) {
                $child_ids = array_column($parent_relationships, 'child_id');
                $children_data = $this->getMembersByIds($child_ids, $parent_relationships, 'child');
            }

            // Load siblings data if this member is a child
            $siblings_data = [];
            if (!empty($child_relationships)) {
                $siblings_relationships = $familyRelationship->getSiblingsByChild($id)->fetchAll(PDO::FETCH_ASSOC);
                if (!empty($siblings_relationships)) {
                    $sibling_ids = array_column($siblings_relationships, 'child_id');
                    $siblings_data = $this->getMembersByIds($sibling_ids, $siblings_relationships, 'child');
                }
            }

            // Determine member type for contextual display
            require_once 'models/ChildrenMinistry.php';
            $childrenMinistry = new ChildrenMinistry($this->database->getConnection());
            $max_child_age = $childrenMinistry->getMaxChildAge();

            $member_age = 0;
            if ($this->member->date_of_birth) {
                $birth_date = new DateTime($this->member->date_of_birth);
                $today = new DateTime();
                $member_age = $today->diff($birth_date)->y;
            }

            // Determine if this member is a child or adult
            $is_child = ($member_age <= $max_child_age) || !empty($child_relationships);

            // Emergency contact fallback removed - only show real family relationships from database

            // Get department and role display names
            $this->member->department_display_name = $this->getDepartmentDisplayName($this->member->department);
            $this->member->role_display_name = $this->getRoleDisplayName($this->member->role);

            // Set page variables
            $page_title = $this->member->first_name . ' ' . $this->member->last_name . ' - Member Details';
            $active_page = 'members';

            // Make relationship data available to the view
            $this->parents_data = $parents_data;
            $this->children_data = $children_data;
            $this->siblings_data = $siblings_data;

            // Load view
            require_once 'views/members/show.php';

        } catch (Exception $e) {
            error_log("Member show error: " . $e->getMessage());
            set_flash_message('Unable to load member details. Please try again.', 'danger');
            redirect('members');
        }
    }

    /**
     * Optimized method to get multiple members by IDs
     *
     * @param array $ids
     * @param array $relationships
     * @param string $type
     * @return array
     */
    private function getMembersByIds($ids, $relationships, $type) {
        if (empty($ids)) {
            return [];
        }

        // Create placeholders for IN clause
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';

        $query = "SELECT id, first_name, last_name, email, phone_number, date_of_birth,
                         gender, school, department, role, member_status,
                         TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) as age
                  FROM members
                  WHERE id IN ($placeholders)";

        $stmt = $this->database->getConnection()->prepare($query);
        $stmt->execute($ids);
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Map members with relationship data
        $result = [];
        foreach ($members as $member) {
            $member_data = [
                'id' => $member['id'],
                'first_name' => $member['first_name'],
                'last_name' => $member['last_name'],
                'email' => $member['email'] ?? '',
                'phone_number' => $member['phone_number'] ?? '',
                'department' => $member['department'] ?? ($type === 'child' ? 'children' : 'none'),
                'role' => $member['role'] ?? 'member',
                'member_status' => $member['member_status'] ?? 'active'
            ];

            // Add type-specific fields
            if ($type === 'child') {
                $member_data['date_of_birth'] = $member['date_of_birth'];
                $member_data['gender'] = $member['gender'];
                $member_data['school'] = $member['school'] ?? '';
                $member_data['age'] = $member['age'] ?? 0;
            } else {
                $member_data['parent_id'] = $member['id']; // For compatibility
            }

            $result[] = $member_data;
        }

        return $result;
    }

    /**
     * Get members with pagination and filtering
     *
     * @param int $limit
     * @param int $offset
     * @param string $search
     * @param string $department
     * @param string $status
     * @return array
     */
    private function getMembersWithPagination($limit, $offset, $search = '', $department = '', $status = 'active') {
        $conn = $this->database->getConnection();

        // Build WHERE clause
        $where_conditions = [];
        $params = [];

        // Status filter
        if (!empty($status) && $status !== 'all') {
            $where_conditions[] = "m.member_status = :status";
            $params[':status'] = $status;
        }

        // Department filter
        if (!empty($department) && $department !== 'all') {
            $where_conditions[] = "m.department = :department";
            $params[':department'] = $department;
        }

        // Search filter
        if (!empty($search)) {
            $where_conditions[] = "(m.first_name LIKE :search OR m.last_name LIKE :search
                                   OR m.email LIKE :search OR m.phone_number LIKE :search
                                   OR CONCAT(m.first_name, ' ', m.last_name) LIKE :search)";
            $params[':search'] = "%{$search}%";
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM members m {$where_clause}";
        $count_stmt = $conn->prepare($count_query);
        $count_stmt->execute($params);
        $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Get members with pagination
        $query = "SELECT m.id, m.first_name, m.last_name, m.email, m.phone_number,
                         m.date_of_birth, m.gender, m.marital_status, m.location,
                         m.department, m.role, m.member_status, m.created_at,
                         TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
                  FROM members m
                  {$where_clause}
                  ORDER BY m.created_at DESC, m.last_name, m.first_name
                  LIMIT :limit OFFSET :offset";

        $stmt = $conn->prepare($query);

        // Bind pagination parameters
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'members' => $members,
            'total' => $total
        ];
    }

    /**
     * Get unique departments for filter dropdown
     *
     * @return array
     */
    private function getUniqueDepartments() {
        $query = "SELECT DISTINCT department FROM members
                  WHERE department IS NOT NULL AND department != ''
                  ORDER BY department";
        $stmt = $this->database->getConnection()->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * AJAX endpoint for member search
     *
     * @return void
     */
    public function searchMembers() {
        header('Content-Type: application/json');

        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                http_response_code(405);
                echo json_encode(['error' => 'Method not allowed']);
                return;
            }

            // Get search parameters
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 25;
            $search = trim($_GET['search'] ?? '');
            $department = $_GET['department'] ?? '';
            $status = $_GET['status'] ?? 'active';
            $offset = ($page - 1) * $limit;

            // Get filtered members
            $members_data = $this->getMembersWithPagination($limit, $offset, $search, $department, $status);
            $total_pages = ceil($members_data['total'] / $limit);

            // Format response
            $response = [
                'success' => true,
                'members' => $members_data['members'],
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $total_pages,
                    'total_members' => $members_data['total'],
                    'limit' => $limit,
                    'has_prev' => $page > 1,
                    'has_next' => $page < $total_pages
                ]
            ];

            echo json_encode($response);

        } catch (Exception $e) {
            error_log("Member search error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Search failed. Please try again.', 'debug' => $e->getMessage()]);
        }
    }



    /**
     * Display member registration form
     *
     * @return void
     */
    public function create() {
        // Check if this is a visitor conversion
        $visitor_data = null;
        $from_visitor = isset($_GET['from_visitor']) ? sanitize($_GET['from_visitor']) : null;

        if ($from_visitor && isset($_SESSION['convert_visitor_data'])) {
            $visitor_data = $_SESSION['convert_visitor_data'];

            // Verify the visitor ID matches
            if ($visitor_data['visitor_id'] == $from_visitor) {
                // Set page title to indicate conversion
                $page_title = 'Convert Visitor to Member - ICGC Emmanuel Temple';
            } else {
                // Clear invalid session data
                unset($_SESSION['convert_visitor_data']);
                $visitor_data = null;
            }
        }

        // Load view
        require_once 'views/members/new_registration.php';
    }

    /**
     * Store new member
     *
     * @return void
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Invalid form submission.', 'danger');
                redirect('members/add');
                exit;
            }
            
            // Set member properties (model will handle all validation)
            $this->member->first_name = sanitize($_POST['first_name'] ?? '');
            $this->member->last_name = sanitize($_POST['last_name'] ?? '');
            $this->member->email = !empty($_POST['email']) ? sanitize($_POST['email']) : null;
            $this->member->phone_number = sanitize($_POST['phone_number']);
            $this->member->date_of_birth = $this->convertDateFormat($_POST['date_of_birth'] ?? null);
            $this->member->gender = sanitize($_POST['gender']);
            $this->member->marital_status = sanitize($_POST['marital_status'] ?? '');
            $this->member->location = sanitize($_POST['location'] ?? '');
            $this->member->emergency_contact_name = sanitize($_POST['emergency_contact_name'] ?? '');
            $this->member->emergency_contact_phone = sanitize($_POST['emergency_contact_phone'] ?? '');
            $this->member->baptism_status = sanitize($_POST['baptism_status'] ?? 'not_baptized');
            $this->member->department = sanitize($_POST['department'] ?? 'none');
            $this->member->role = sanitize($_POST['role'] ?? 'member');
            $this->member->membership_date = sanitize($_POST['membership_date'] ?? date('Y-m-d'));
            $this->member->occupation = sanitize($_POST['occupation'] ?? '');
            $this->member->school = sanitize($_POST['school'] ?? '');
            $this->member->member_status = sanitize($_POST['member_status']);

            // Handle profile picture upload
            if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = 'uploads/profile_pictures/';

                // Create directory if it doesn't exist
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                // Generate unique filename
                $filename = uniqid() . '_' . basename($_FILES['profile_picture']['name']);
                $target_file = $upload_dir . $filename;

                // Securely check file type
                $finfo = new finfo(FILEINFO_MIME_TYPE);
                $file_type = $finfo->file($_FILES['profile_picture']['tmp_name']);
                $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];

                // Also check file extension as a fallback
                $file_extension = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];


                if (!in_array($file_type, $allowed_types) || !in_array($file_extension, $allowed_extensions)) {
                    set_flash_message('Only JPG, PNG, and GIF files are allowed.', 'danger');
                    redirect('members/add');
                }

                // Move uploaded file
                if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $target_file)) {
                    $this->member->profile_picture = $target_file;
                } else {
                    set_flash_message('Failed to upload profile picture.', 'danger');
                    redirect('members/add');
                }
            } else {
                $this->member->profile_picture = '';
            }

            // Set timestamps
            $this->member->created_at = date('Y-m-d H:i:s');
            $this->member->updated_at = date('Y-m-d H:i:s');

            // Create member (model will handle all validation)
            if ($this->member->create()) {
                $member_id = $this->member->id; // Use the member's ID property instead of lastInsertId

                // Handle visitor conversion if this is from a visitor
                if (isset($_POST['from_visitor_conversion']) && !empty($_POST['from_visitor_conversion'])) {
                    $visitor_id = sanitize($_POST['from_visitor_conversion']);

                    // Create conversion record
                    $database = new Database();
                    $conn = $database->getConnection();
                    $conversion = new VisitorConversion($conn);

                    // Set conversion properties
                    $conversion->visitor_id = $visitor_id;
                    $conversion->member_id = $member_id;
                    $conversion->conversion_date = date('Y-m-d');
                    $conversion->notes = 'Converted via member registration form';

                    if ($conversion->create()) {
                        // Clear the visitor session data
                        unset($_SESSION['convert_visitor_data']);

                        // Update success message to indicate conversion
                        $success_message = 'Visitor successfully converted to member!';
                    } else {
                        // Log the conversion failure but don't fail the member creation
                        error_log("Failed to create conversion record for visitor $visitor_id to member $member_id");
                        $success_message = 'Member added successfully, but conversion record failed.';
                    }
                } else {
                    $success_message = 'Member added successfully.';
                }

                // Handle children registration if provided
                $children_registered = 0;
                if (isset($_POST['children']) && is_array($_POST['children'])) {
                    $children_registered = $this->registerChildren($member_id, $_POST['children']);
                }

                // Auto-detection removed - use manual parent-child assignment instead

                // Determine smart redirect based on member age
                $redirect_url = $this->determineSmartRedirect($member_id, $children_registered);

                if ($children_registered > 0) {
                    $success_message .= " Registered {$children_registered} child(ren).";
                }

                set_flash_message($success_message, 'success');
                redirect($redirect_url);
            } else {
                // Get detailed error message from model
                $errorMessage = $this->member->error ?? 'Failed to add member.';
                set_flash_message($errorMessage, 'danger');
                redirect('members/add');
            }
        } else {
            // Not a POST request, redirect to create form
            redirect('members/add');
        }
    }

    /**
     * Display edit member form
     *
     * @param int|null $id Member ID (from URL parameter or $_GET)
     * @return void
     */
    public function edit($id = null) {
        // Get ID from parameter or fallback to $_GET for backward compatibility
        if ($id === null) {
            $id = $_GET['id'] ?? null;
        }

        // Check if ID is set
        if (empty($id)) {
            set_flash_message('Member ID is required.', 'danger');
            redirect('members');
        }

        // Sanitize the ID
        $id = sanitize($id);

        // Get member by ID
        if (!$this->member->getById($id)) {
            set_flash_message('Member not found.', 'danger');
            redirect('members');
        }

        // Load children data
        require_once 'models/FamilyRelationship.php';
        $familyRelationship = new FamilyRelationship($this->database->getConnection());

        // Get relationships where this member is the parent
        $parent_relationships = $familyRelationship->getChildrenByParent($id)->fetchAll(PDO::FETCH_ASSOC);

        // Get relationships where this member is the child
        $child_relationships = $familyRelationship->getParentsByChild($id)->fetchAll(PDO::FETCH_ASSOC);

        // If this member is a child (under configured age or has parents), redirect to parent's edit page
        $member_age = 0;
        if ($this->member->date_of_birth) {
            $birth_date = new DateTime($this->member->date_of_birth);
            $today = new DateTime();
            $member_age = $today->diff($birth_date)->y;
        }

        // Use configurable child age threshold for consistency
        try {
            require_once 'models/ChildrenMinistry.php';
            $childrenMinistry = new ChildrenMinistry($this->database->getConnection());
            $max_child_age = $childrenMinistry->getMaxChildAge();
        } catch (Exception $e) {
            // Fallback to default age if children ministry is not configured
            $max_child_age = 17;
            error_log("Children ministry settings not available, using default age: " . $e->getMessage());
        }

        // Check if this is an "assign parent" action for a standalone child
        $is_assign_parent = isset($_GET['assign_parent']) && $_GET['assign_parent'] == '1';

        // Check if this member is a child with assigned parents
        // Only redirect if there are actual parent relationships AND we're not already focused on this child AND not assigning parent
        if (!empty($child_relationships) && !isset($_GET['child_focus']) && !$is_assign_parent) {
            // Find the parent and redirect to parent's edit page with child focus
            $parent_id = $child_relationships[0]['parent_id'];
            header("Location: " . BASE_URL . "members/edit?id=" . $parent_id . "&child_focus=" . $id);
            exit();
        }

        // Load complete children data for editing - Universal approach
        $children_data = [];



        // Method 1: Check parent-child relationships where this member is parent
        if (!empty($parent_relationships)) {
            foreach ($parent_relationships as $relationship) {

                $child_member = new Member($this->database->getConnection());
                if ($child_member->getById($relationship['child_id'])) {
                    // Calculate age for the child
                    $child_age = 0;
                    if ($child_member->date_of_birth) {
                        try {
                            $birth_date = new DateTime($child_member->date_of_birth);
                            $today = new DateTime();
                            $child_age = $today->diff($birth_date)->y;
                        } catch (Exception $e) {
                            // Handle invalid date format gracefully
                            error_log("Invalid date format for child member ID {$child_member->id}: {$child_member->date_of_birth}");
                        }
                    }

                    $children_data[] = [
                        'id' => $child_member->id,
                        'first_name' => $child_member->first_name,
                        'last_name' => $child_member->last_name,
                        'date_of_birth' => $child_member->date_of_birth,
                        'gender' => $child_member->gender,
                        'school' => $child_member->school ?? '',
                        'department' => $child_member->department ?? 'children',
                        'age' => $child_age
                    ];

                }
            }
        }



        // Simple approach: Only get children through family_relationships table
        // No complex auto-detection or phone number matching

        // Make member data available to the view
        $member = $this->member;
        $database = $this->database;
        // Ensure children_data is available to the view
        // $children_data is already set above

        // Load view
        require_once 'views/members/edit.php';
    }

    /**
     * Update member
     *
     * @param int|null $id Member ID (from URL parameter for RESTful routes)
     * @return void
     */
    public function update($id = null) {
        try {
            // Check if form is submitted (POST or PUT)
            if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'PUT') {
            // Verify CSRF token
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Invalid form submission.', 'danger');
                redirect('members');
                exit;
            }

            // Get ID from URL parameter (RESTful) or POST data (legacy)
            if ($id === null) {
                // Legacy: Get ID from POST data
                if (!isset($_POST['id']) || empty($_POST['id'])) {
                    set_flash_message('Member ID is required.', 'danger');
                    redirect('members');
                    return;
                }
                $id = $_POST['id'];
            }

            // Get member by ID
            $id = sanitize($_POST['id']);
            if (!$this->member->getById($id)) {
                set_flash_message('Member not found.', 'danger');
                redirect('members');
            }

            // Set member properties (model will handle all validation)
            $this->member->first_name = sanitize($_POST['first_name']);
            $this->member->last_name = sanitize($_POST['last_name']);
            // Handle email - ensure it's NULL if empty to avoid constraint issues
            $email_input = trim($_POST['email'] ?? '');
            $this->member->email = !empty($email_input) ? sanitize($email_input) : null;
            $this->member->phone_number = sanitize($_POST['phone_number']);
            $this->member->date_of_birth = $this->convertDateFormat($_POST['date_of_birth'] ?? null);
            $this->member->gender = sanitize($_POST['gender']);
            $this->member->marital_status = sanitize($_POST['marital_status'] ?? '');
            $this->member->location = sanitize($_POST['location'] ?? '');
            $this->member->emergency_contact_name = sanitize($_POST['emergency_contact_name'] ?? '');
            $this->member->emergency_contact_phone = sanitize($_POST['emergency_contact_phone'] ?? '');
            $this->member->baptism_status = sanitize($_POST['baptism_status'] ?? 'not_baptized');
            $this->member->department = sanitize($_POST['department'] ?? 'none');
            $this->member->role = sanitize($_POST['role'] ?? 'member');
            $this->member->membership_date = sanitize($_POST['membership_date'] ?? date('Y-m-d'));
            $this->member->occupation = sanitize($_POST['occupation'] ?? '');
            $this->member->school = sanitize($_POST['school'] ?? '');
            $this->member->member_status = sanitize($_POST['member_status']);

            // Handle profile picture upload
            if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = 'uploads/profile_pictures/';

                // Create directory if it doesn't exist
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                // Get the file size
                $file_size = $_FILES['profile_picture']['size'];
                $max_size = 2 * 1024 * 1024; // 2MB in bytes
                
                if ($file_size > $max_size) {
                    set_flash_message('Profile picture size must be less than 2MB.', 'danger');
                    redirect('members/edit?id=' . $id);
                }

                // Generate unique filename
                $filename = uniqid() . '_' . basename($_FILES['profile_picture']['name']);
                $target_file = $upload_dir . $filename;

                // Securely check file type using multiple methods
                // 1. Check MIME type using finfo
                $finfo = new finfo(FILEINFO_MIME_TYPE);
                $file_type = $finfo->file($_FILES['profile_picture']['tmp_name']);
                
                // 2. Check file extension
                $file_extension = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                
                // Define allowed types
                $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];

                if (!in_array($file_type, $allowed_types) || !in_array($file_extension, $allowed_extensions)) {
                    set_flash_message('Only JPG, PNG, and GIF files are allowed.', 'danger');
                    redirect('members/edit?id=' . $id);
                }
                
                // Additional check: validate image dimensions
                $image_info = getimagesize($_FILES['profile_picture']['tmp_name']);
                if ($image_info === false) {
                    set_flash_message('Invalid image file.', 'danger');
                    redirect('members/edit?id=' . $id);
                }
                
                // Move uploaded file to a secure location
                if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $target_file)) {
                    // Delete old profile picture if exists
                    if (!empty($this->member->profile_picture) && file_exists($this->member->profile_picture)) {
                        unlink($this->member->profile_picture);
                    }

                    $this->member->profile_picture = $target_file;
                    $this->member->updateProfilePicture();
                } else {
                    set_flash_message('Failed to upload profile picture.', 'danger');
                    redirect('members/edit?id=' . $id);
                }
            }

            // Update member (model will handle all validation)
            if ($this->member->update()) {
                // Simplified - only children registration handling

                // Handle existing children updates if provided
                try {
                    if (isset($_POST['existing_children']) && is_array($_POST['existing_children'])) {
                        foreach ($_POST['existing_children'] as $child_id => $child_data) {
                            if (!empty($child_data['first_name']) && !empty($child_data['last_name']) && !empty($child_data['id'])) {
                                // Update existing child member
                                $child_member = new Member($this->database->getConnection());
                                if ($child_member->getById($child_data['id'])) {
                                    $child_member->first_name = sanitize($child_data['first_name']);
                                    $child_member->last_name = sanitize($child_data['last_name']);
                                    $child_member->date_of_birth = sanitize($child_data['date_of_birth'] ?? null);
                                    $child_member->gender = sanitize($child_data['gender']);
                                    $child_member->school = sanitize($child_data['school'] ?? '');
                                    $child_member->department = sanitize($child_data['department'] ?? 'children');

                                    $child_member->update();

                                    // Ensure family relationship exists
                                    require_once 'models/FamilyRelationship.php';
                                    $familyRelationship = new FamilyRelationship($this->database->getConnection());

                                    // Check if relationship already exists
                                    if (!$familyRelationship->relationshipExists($id, $child_data['id'], 'parent')) {
                                        // Create the family relationship using the reliable method
                                        $this->createParentChildRelationship($id, $child_data['id']);
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception $e) {
                    error_log("Existing children update error: " . $e->getMessage());
                    // Continue with member update even if children updates fail
                }

                // Handle new children registration if provided - simplified approach
                if (isset($_POST['children']) && is_array($_POST['children'])) {
                    foreach ($_POST['children'] as $child_data) {
                        if (!empty($child_data['first_name']) && !empty($child_data['last_name'])) {
                            $children_registered = $this->registerChildren($id, [$child_data]);
                        }
                    }
                }

                // Handle child deletion if provided
                $deleted_children_count = 0;
                if (isset($_POST['delete_children']) && is_array($_POST['delete_children'])) {
                    foreach ($_POST['delete_children'] as $child_id => $delete) {
                        if ($delete == '1') {
                            // Delete the child member and family relationship
                            $child_member = new Member($this->database->getConnection());
                            if ($child_member->getById($child_id)) {
                                // Delete family relationship first
                                require_once 'models/FamilyRelationship.php';
                                $familyRelationship = new FamilyRelationship($this->database->getConnection());
                                $familyRelationship->deleteByChildId($child_id);

                                // Delete the child member
                                if ($child_member->delete()) {
                                    $deleted_children_count++;
                                }
                            }
                        }
                    }
                }

                // Handle parent assignment for children
                if (isset($_POST['parent_id'])) {
                    $this->handleParentAssignment($id, $_POST['parent_id']);
                }

                // Auto-detection removed - use manual parent-child assignment instead

                // Create success message with deletion info
                $success_message = 'Member updated successfully.';
                if ($deleted_children_count > 0) {
                    $success_message .= " $deleted_children_count child" . ($deleted_children_count > 1 ? 'ren' : '') . " deleted.";
                }
                set_flash_message($success_message, 'success');
                redirect('members');

            } else {
                // Get detailed error message from model
                $errorMessage = $this->member->error ?? 'Failed to update member.';
                set_flash_message($errorMessage, 'danger');
                redirect('members/edit?id=' . $id);
            }

            } else {
                // Not a POST request, redirect to members list
                redirect('members');
            }
        } catch (Exception $e) {
            // Enhanced error logging with more context
            $error_context = [
                'user_id' => $_SESSION['user_id'] ?? 'unknown',
                'member_id' => $_POST['id'] ?? 'unknown',
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'post_data_keys' => array_keys($_POST),
                'timestamp' => date('Y-m-d H:i:s')
            ];

            // Log to application error log (bypass broken error handler)
            $log_message = "Member update error: " . $e->getMessage() . " | Context: " . json_encode($error_context);
            file_put_contents(__DIR__ . '/../logs/member_update_errors.log', date('Y-m-d H:i:s') . " - " . $log_message . "\n", FILE_APPEND | LOCK_EX);

            // Provide more specific error messages based on the error type
            $user_message = 'An error occurred while updating the member. Please try again.';

            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                if (strpos($e->getMessage(), 'email') !== false) {
                    $user_message = 'This email address is already in use by another member. Please use a different email.';
                } elseif (strpos($e->getMessage(), 'phone') !== false) {
                    $user_message = 'This phone number is already in use by another member. Please use a different phone number.';
                }
            } elseif (strpos($e->getMessage(), 'Data too long') !== false) {
                $user_message = 'One of the fields contains too much text. Please shorten your input and try again.';
            } elseif (strpos($e->getMessage(), 'cannot be null') !== false) {
                $user_message = 'A required field is missing. Please fill in all required fields and try again.';
            }

            set_flash_message($user_message, 'danger');

            // Try to redirect back to edit page if we have an ID
            if (isset($_POST['id']) && !empty($_POST['id'])) {
                redirect('members/edit?id=' . $_POST['id']);
            } else {
                redirect('members');
            }
        }
    }

    /**
     * Delete member
     *
     * @param int|null $id Member ID (from URL parameter for RESTful routes)
     * @return void
     */
    public function delete($id = null) {
        // Get ID from URL parameter (RESTful) or POST data (legacy)
        if ($id === null) {
            // Legacy: Get ID from POST form data
            if (!isset($_POST['id']) || empty($_POST['id'])) {
                set_flash_message('Member ID is required.', 'danger');
                redirect('members');
                return;
            }
            $id = $_POST['id'];
        }

        // CSRF validation for POST requests
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Invalid CSRF token.', 'danger');
                redirect('members');
                return;
            }
        }

        // Sanitize and validate ID
        $id = sanitize($id);
        if (!$this->member->getById($id)) {
            set_flash_message('Member not found.', 'danger');
            redirect('members');
            return;
        }

        // Delete profile picture if exists
        if (!empty($this->member->profile_picture) && file_exists($this->member->profile_picture)) {
            unlink($this->member->profile_picture);
        }

        // Delete member
        if ($this->member->delete()) {
            set_flash_message('Member deleted successfully.', 'success');
        } else {
            set_flash_message('Failed to delete member.', 'danger');
        }

        redirect('members');
    }

    /**
     * Search members
     *
     * @return void
     */
    public function search() {
        // Check if search term is set
        if (!isset($_GET['search']) || empty($_GET['search'])) {
            redirect('members');
        }

        // Search members
        $search = sanitize($_GET['search']);
        $stmt = $this->member->search($search);
        $members = $stmt->fetchAll();

        // Load view
        require_once 'views/members/search.php';
    }

    /**
     * Filter members by department
     *
     * @return void
     */
    public function filterByDepartment() {
        // Check if department is set
        if (!isset($_GET['department']) || empty($_GET['department'])) {
            redirect('members');
        }

        // Filter members
        $department = sanitize($_GET['department']);
        $stmt = $this->member->getByDepartment($department);
        $members = $stmt->fetchAll();

        // Load view
        require_once 'views/members/filter.php';
    }

    /**
     * Filter members by role
     *
     * @return void
     */
    public function filterByRole() {
        // Check if role is set
        if (!isset($_GET['role']) || empty($_GET['role'])) {
            redirect('members');
        }

        // Filter members
        $role = sanitize($_GET['role']);
        $stmt = $this->member->getByRole($role);
        $members = $stmt->fetchAll();

        // Load view
        require_once 'views/members/filter.php';
    }

    /**
     * Filter members by status
     *
     * @return void
     */
    public function filterByStatus() {
        // Check if status is set
        if (!isset($_GET['status']) || empty($_GET['status'])) {
            redirect('members');
        }

        // Filter members
        $status = sanitize($_GET['status']);
        $stmt = $this->member->getByStatus($status);
        $members = $stmt->fetchAll();

        // Load view
        require_once 'views/members/filter.php';
    }

    /**
     * Display members with birthdays this month
     *
     * @return void
     */
    public function birthdays() {
        // Get members with birthdays this month
        $stmt = $this->member->getBirthdaysThisMonth();
        $members = $stmt->fetchAll();

        // Load view
        require_once 'views/members/birthdays.php';
    }

    /**
     * Get attendance statistics for a member
     *
     * @param int $memberId The member ID
     * @return array Attendance statistics
     */
    public function getAttendanceStats($memberId) {
        // Connect to database
        $conn = $this->database->getConnection();

        // Get current month
        $currentMonth = date('Y-m');

        // Get attendance count for current month
        $monthlyQuery = "SELECT COUNT(*) as count FROM attendance
                         WHERE member_id = :member_id
                         AND DATE_FORMAT(attendance_date, '%Y-%m') = :current_month";
        $monthlyStmt = $conn->prepare($monthlyQuery);
        $monthlyStmt->bindParam(':member_id', $memberId);
        $monthlyStmt->bindParam(':current_month', $currentMonth);
        $monthlyStmt->execute();
        $monthlyResult = $monthlyStmt->fetch(PDO::FETCH_ASSOC);

        // Get present count
        $presentQuery = "SELECT COUNT(*) as count FROM attendance
                        WHERE member_id = :member_id AND status = 'present'";
        $presentStmt = $conn->prepare($presentQuery);
        $presentStmt->bindParam(':member_id', $memberId);
        $presentStmt->execute();
        $presentResult = $presentStmt->fetch(PDO::FETCH_ASSOC);

        // Get absent count
        $absentQuery = "SELECT COUNT(*) as count FROM attendance
                       WHERE member_id = :member_id AND status = 'absent'";
        $absentStmt = $conn->prepare($absentQuery);
        $absentStmt->bindParam(':member_id', $memberId);
        $absentStmt->execute();
        $absentResult = $absentStmt->fetch(PDO::FETCH_ASSOC);

        return [
            'this_month' => $monthlyResult['count'] ?? 0,
            'present' => $presentResult['count'] ?? 0,
            'absent' => $absentResult['count'] ?? 0
        ];
    }

    /**
     * Get recent attendance records for a member
     *
     * @param int $memberId The member ID
     * @param int $limit Number of records to return
     * @return array Recent attendance records
     */
    public function getRecentAttendance($memberId, $limit = 5) {
        // Connect to database
        $conn = $this->database->getConnection();

        // Get recent attendance records
        $query = "SELECT ar.*, s.name as service_name
                 FROM attendance ar
                 LEFT JOIN services s ON ar.service_id = s.id
                 WHERE ar.member_id = :member_id
                 ORDER BY ar.attendance_date DESC
                 LIMIT :limit";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':member_id', $memberId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Find potential family members for smart suggestions (API endpoint)
     *
     * @return void
     */
    public function findFamilyMembers() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            exit;
        }

        $firstName = $_GET['first_name'] ?? '';
        $lastName = $_GET['last_name'] ?? '';
        $memberType = $_GET['member_type'] ?? '';

        if (empty($firstName) || empty($lastName) || empty($memberType)) {
            echo json_encode([
                'success' => false,
                'error' => 'Missing required parameters'
            ]);
            exit;
        }

        try {
            $conn = $this->database->getConnection();

            // Determine age criteria based on member type
            if ($memberType === 'child') {
                // If registering a child, find potential parents (18+)
                $ageCondition = "TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) >= 18";
                $oppositeType = 'adults';
            } else {
                // If registering an adult, find potential children (<18)
                $ageCondition = "TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) < 18";
                $oppositeType = 'children';
            }

            // Find potential family members based on surname and other factors
            $query = "SELECT
                        m.id,
                        m.first_name,
                        m.last_name,
                        m.phone_number,
                        m.email,
                        m.address,
                        TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age,
                        CASE
                            WHEN m.last_name = :last_name AND m.address LIKE CONCAT('%', :address_search, '%') THEN 95
                            WHEN m.last_name = :last_name THEN 85
                            WHEN m.address LIKE CONCAT('%', :address_search2, '%') THEN 70
                            ELSE 60
                        END as confidence_score,
                        CASE
                            WHEN m.last_name = :last_name AND m.address LIKE CONCAT('%', :address_search3, '%') THEN 'Same surname and similar address'
                            WHEN m.last_name = :last_name THEN 'Same surname'
                            WHEN m.address LIKE CONCAT('%', :address_search4, '%') THEN 'Similar address'
                            ELSE 'Potential match'
                        END as reason
                      FROM members m
                      WHERE m.member_status = 'active'
                      AND {$ageCondition}
                      AND (
                          m.last_name = :last_name
                          OR m.address LIKE CONCAT('%', :address_search5, '%')
                      )
                      ORDER BY confidence_score DESC, m.last_name, m.first_name
                      LIMIT 10";

            $stmt = $conn->prepare($query);

            // For address matching, we'll use a simple approach
            // In a real implementation, you might want more sophisticated address matching
            $addressSearch = $lastName; // Simple approach - use surname as address indicator

            $stmt->bindParam(':last_name', $lastName);
            $stmt->bindParam(':address_search', $addressSearch);
            $stmt->bindParam(':address_search2', $addressSearch);
            $stmt->bindParam(':address_search3', $addressSearch);
            $stmt->bindParam(':address_search4', $addressSearch);
            $stmt->bindParam(':address_search5', $addressSearch);

            $stmt->execute();
            $suggestions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'suggestions' => $suggestions,
                'member_type' => $memberType,
                'opposite_type' => $oppositeType
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ]);
        }

        exit;
    }

    /**
     * Get all active members for dropdowns (API endpoint)
     *
     * @return void
     */
    public function getAllActiveMembers() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            exit;
        }

        try {
            $conn = $this->database->getConnection();

            $query = "SELECT
                        m.id,
                        m.first_name,
                        m.last_name,
                        m.phone_number,
                        m.email,
                        TIMESTAMPDIFF(YEAR, m.date_of_birth, CURDATE()) as age
                      FROM members m
                      WHERE m.member_status = 'active'
                      ORDER BY m.last_name, m.first_name";

            $stmt = $conn->prepare($query);
            $stmt->execute();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'members' => $members
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ]);
        }

        exit;
    }

    // Simplified member management - only children registration needed

    /**
     * Determine smart redirect URL based on member characteristics
     *
     * @param int $member_id The newly created member ID
     * @param int $children_registered Number of children registered
     * @return string Redirect URL
     */
    private function determineSmartRedirect($member_id, $children_registered) {
        try {
            // Get member details
            $conn = $this->database->getConnection();
            $query = "SELECT *, TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) as age FROM members WHERE id = :id";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':id', $member_id);
            $stmt->execute();
            $member = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$member) {
                return 'members'; // Default fallback
            }

            $age = $member['age'];
            $member_type = $_POST['member_type'] ?? '';

            // Smart routing logic
            if ($age <= 17) {
                // Child member - check if they have parent connections
                if ($children_registered > 0) {
                    // Child with parent connections - go to families page to see the family structure
                    return 'children-ministry/families';
                } else {
                    // Child without connections - go to children's ministry to potentially add connections
                    return 'children-ministry';
                }
            } else {
                // Adult member - check if they registered children
                if ($children_registered > 0) {
                    // Adult with children - go to families page to see the family structure
                    return 'children-ministry/families';
                } else {
                    // Adult without children - go to regular members page
                    return 'members';
                }
            }

        } catch (Exception $e) {
            // On error, default to members page
            return 'members';
        }
    }

    /**
     * Register children for a parent member
     *
     * @param int $parent_id
     * @param array $children_data
     * @return int Number of children registered
     */
    private function registerChildren($parent_id, $children_data) {
        $children_registered = 0;

        // Register children for the parent

        try {
            foreach ($children_data as $index => $child_data) {
                // Skip empty entries
                if (empty($child_data['first_name']) || empty($child_data['last_name'])) {
                    continue;
                }

                // Validate child age - hard limit for children over 18
                if (!empty($child_data['date_of_birth'])) {
                    $birthDate = new DateTime($child_data['date_of_birth']);
                    $today = new DateTime();
                    $age = $today->diff($birthDate)->y;

                    if ($age >= 18) {
                        error_log("Attempted to register child over 18 years old (Age: $age). Skipping child: " . $child_data['first_name'] . " " . $child_data['last_name']);
                        continue; // Skip this child - they should be registered as adults
                    }
                }

                // Validate child names using the same validation as adults
                $childValidation = new Validation($child_data);
                $childValidation->required('first_name')
                              ->name('first_name')
                              ->required('last_name')
                              ->name('last_name')
                              ->dateOfBirth('date_of_birth');

                if ($childValidation->fails()) {
                    error_log("Child validation failed for: " . $child_data['first_name'] . " " . $child_data['last_name']);
                    continue; // Skip invalid child data
                }

                // Create a new member instance for the child
                $child_member = new Member($this->database->getConnection());

                // Set child properties using validated data
                $this->setChildMemberProperties($child_member, $childValidation->getData(), $this->member);

                // Create the child member
                if ($child_member->create()) {
                    $child_id = $child_member->id; // Use the member's ID property instead of lastInsertId

                    // Create parent-child relationship
                    $this->createParentChildRelationship($parent_id, $child_id);

                    $children_registered++;
                } else {
                    error_log("Failed to create child: " . $child_data['first_name'] . " " . $child_data['last_name'] . " - " . ($child_member->error ?? 'Unknown error'));
                }
            }
        } catch (Exception $e) {
            error_log("Error registering children: " . $e->getMessage());
        }

        return $children_registered;
    }

    /**
     * Create parent-child relationship - simplified version
     *
     * @param int $parent_id
     * @param int $child_id
     * @return bool
     */
    private function createParentChildRelationship($parent_id, $child_id) {
        try {
            require_once 'models/FamilyRelationship.php';
            $familyRelationship = new FamilyRelationship($this->database->getConnection());

            // Check if relationship already exists
            if ($familyRelationship->relationshipExists($parent_id, $child_id, 'parent')) {
                return true; // Already exists
            }

            // Create the relationship using the model
            $relationship_data = [
                'parent_id' => $parent_id,
                'child_id' => $child_id,
                'relationship_type' => 'parent',
                'is_primary' => 1,
                'can_pickup' => 1,
                'notes' => 'Manually created',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            return $familyRelationship->create($relationship_data);

        } catch (Exception $e) {
            error_log("Error creating parent-child relationship: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create parent member for a standalone child
     *
     * @return void
     */
    public function createParentForChild() {
        try {
            // Basic validation
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                set_flash_message('Invalid request method.', 'danger');
                redirect('members');
                return;
            }

            if (!isset($_POST['assign_parent_for_child']) || empty($_POST['assign_parent_for_child'])) {
                set_flash_message('Child ID is required for parent assignment.', 'danger');
                redirect('members');
                return;
            }

            // CSRF validation
            if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
                set_flash_message('Security validation failed. Please try again.', 'danger');
                $child_id = sanitize($_POST['assign_parent_for_child']);
                redirect("members/edit/$child_id?assign_parent=1");
                return;
            }

            $child_id = sanitize($_POST['assign_parent_for_child']);

            // Simple validation - check essential required fields only
            $required_fields = ['first_name', 'last_name', 'phone_number'];
            $missing_fields = [];

            foreach ($required_fields as $field) {
                if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
                    $missing_fields[] = ucfirst(str_replace('_', ' ', $field));
                }
            }

            if (!empty($missing_fields)) {
                $error_message = 'Please fill in the required fields: ' . implode(', ', $missing_fields);

                // Check if this is an AJAX request
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    // Return JSON error response for AJAX
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => $error_message,
                        'errors' => $missing_fields
                    ]);
                    exit;
                } else {
                    // Traditional redirect for non-AJAX requests
                    set_flash_message($error_message, 'danger');
                    redirect("members/edit/$child_id?assign_parent=1");
                    return;
                }
            }

            // Validate gender if provided
            if (!empty($_POST['gender']) && !in_array($_POST['gender'], ['male', 'female'])) {
                set_flash_message('Please select a valid gender.', 'danger');
                redirect("members/edit/$child_id?assign_parent=1");
                return;
            }

            // Validate date of birth if provided
            if (!empty($_POST['date_of_birth'])) {
                $date_input = trim($_POST['date_of_birth']);
                // Try to parse DD-MM-YYYY format
                $date = DateTime::createFromFormat('d-m-Y', $date_input);
                if (!$date || $date->format('d-m-Y') !== $date_input) {
                    set_flash_message('Please enter a valid date of birth in DD-MM-YYYY format.', 'danger');
                    redirect("members/edit/$child_id?assign_parent=1");
                    return;
                }
            }

            // Check for existing member with same phone number or email
            $phone_number = sanitize($_POST['phone_number']);
            $email = !empty($_POST['email']) ? sanitize($_POST['email']) : null;

            $existing_member = $this->findExistingMemberByPhoneOrEmail($phone_number, $email);
            if ($existing_member) {
                // Member already exists, just create the relationship
                $this->createParentChildRelationship($existing_member['id'], $child_id);
                $this->updateChildEmergencyContact($child_id, $existing_member['id']);

                set_flash_message('Found existing member and successfully assigned as parent!', 'success');
                redirect("members/view/{$existing_member['id']}");
                return;
            }

            // Create parent member manually
            $this->member->first_name = sanitize($_POST['first_name']);
            $this->member->last_name = sanitize($_POST['last_name']);

            // Handle email - set to NULL if empty to avoid unique constraint issues
            $email_input = trim($_POST['email'] ?? '');
            $this->member->email = !empty($email_input) ? sanitize($email_input) : null;

            $this->member->phone_number = sanitize($_POST['phone_number']);

            // Handle date of birth - set to NULL if empty
            $date_input = trim($_POST['date_of_birth'] ?? '');
            $this->member->date_of_birth = !empty($date_input) ? $this->convertDateFormat($date_input) : null;

            // Handle gender - set to empty string if not provided
            $this->member->gender = !empty($_POST['gender']) ? sanitize($_POST['gender']) : '';
            $this->member->marital_status = sanitize($_POST['marital_status'] ?? '');
            $this->member->location = sanitize($_POST['location'] ?? '');
            $this->member->emergency_contact_name = sanitize($_POST['emergency_contact_name'] ?? '');
            $this->member->emergency_contact_phone = sanitize($_POST['emergency_contact_phone'] ?? '');
            $this->member->baptism_status = sanitize($_POST['baptism_status'] ?? 'not_baptized');
            $this->member->department = sanitize($_POST['department'] ?? 'none');
            $this->member->role = 'member';
            $this->member->membership_date = date('Y-m-d');
            $this->member->occupation = sanitize($_POST['occupation'] ?? '');
            $this->member->school = sanitize($_POST['school'] ?? '');
            $this->member->member_status = 'active';
            $this->member->profile_picture = '';
            $this->member->created_at = date('Y-m-d H:i:s');
            $this->member->updated_at = date('Y-m-d H:i:s');

            if ($this->member->create()) {
                $parent_id = $this->member->id; // Use the member's ID property instead of lastInsertId

                // Create parent-child relationship
                $this->createParentChildRelationship($parent_id, $child_id);

                // Update child's emergency contact to match parent
                $this->updateChildEmergencyContact($child_id, $parent_id);

                // Check if this is an AJAX request
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    // Return JSON response for AJAX
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => 'Parent successfully created and assigned to child!',
                        'parent_id' => $parent_id,
                        'child_id' => $child_id
                    ]);
                    exit;
                } else {
                    // Traditional redirect for non-AJAX requests
                    set_flash_message('Parent successfully created and assigned to child!', 'success');
                    redirect("members/view/$parent_id");
                }
            } else {
                // Check if this is an AJAX request
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    // Return JSON error response for AJAX
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => 'Failed to create parent member.',
                        'errors' => ['Database error occurred while creating parent member.']
                    ]);
                    exit;
                } else {
                    // Traditional redirect for non-AJAX requests
                    set_flash_message('Failed to create parent member.', 'danger');
                    redirect("members/edit/$child_id?assign_parent=1");
                }
            }

        } catch (Exception $e) {
            error_log("Error creating parent for child: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                // Return JSON error response for AJAX
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'An unexpected error occurred. Please try again.',
                    'errors' => ['System error: ' . $e->getMessage()]
                ]);
                exit;
            } else {
                // Traditional redirect for non-AJAX requests
                set_flash_message('An unexpected error occurred. Please try again.', 'danger');

                // Try to redirect back to the child edit page if we have the child ID
                $child_id = $_POST['assign_parent_for_child'] ?? null;
                if ($child_id) {
                    redirect("members/edit/$child_id?assign_parent=1");
                } else {
                    redirect('members');
                }
            }
        }
    }

    /**
     * Find existing member by phone number or email
     *
     * @param string $phone_number
     * @param string|null $email
     * @return array|null
     */
    private function findExistingMemberByPhoneOrEmail($phone_number, $email = null) {
        try {
            if (!empty($email)) {
                // Check both phone and email
                $query = "SELECT id, first_name, last_name, phone_number, email
                         FROM members
                         WHERE (phone_number = ? OR email = ?)
                         AND member_status = 'active'
                         LIMIT 1";

                $stmt = $this->database->getConnection()->prepare($query);
                $stmt->execute([$phone_number, $email]);
            } else {
                // Check only phone number
                $query = "SELECT id, first_name, last_name, phone_number, email
                         FROM members
                         WHERE phone_number = ?
                         AND member_status = 'active'
                         LIMIT 1";

                $stmt = $this->database->getConnection()->prepare($query);
                $stmt->execute([$phone_number]);
            }

            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error finding existing member: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update child's emergency contact information to match parent
     * This is a manual helper function - only called when explicitly assigning parents
     * Does not create relationships automatically
     *
     * @param int $child_id
     * @param int $parent_id
     * @return void
     */
    private function updateChildEmergencyContact($child_id, $parent_id) {
        try {
            $parent_member = new Member($this->database->getConnection());
            if ($parent_member->getById($parent_id)) {
                $child_member = new Member($this->database->getConnection());
                if ($child_member->getById($child_id)) {
                    $child_member->emergency_contact_name = $parent_member->first_name . ' ' . $parent_member->last_name;
                    $child_member->emergency_contact_phone = $parent_member->phone_number;
                    $child_member->location = $parent_member->location; // Inherit parent's location
                    $child_member->update();
                }
            }
        } catch (Exception $e) {
            error_log("Error updating child emergency contact: " . $e->getMessage());
        }
    }

    /**
     * Handle parent assignment for a child member
     *
     * @param int $child_id
     * @param string $parent_id
     * @return void
     */
    private function handleParentAssignment($child_id, $parent_id) {
        try {
            require_once 'models/FamilyRelationship.php';
            $familyRelationship = new FamilyRelationship($this->database->getConnection());

            // Remove existing parent relationships for this child
            $familyRelationship->deleteByChild($child_id);

            // If a parent is selected, create the new relationship
            if (!empty($parent_id)) {
                $this->createParentChildRelationship($parent_id, $child_id);
            }

        } catch (Exception $e) {
            error_log("Error handling parent assignment: " . $e->getMessage());
        }
    }

    /**
     * Set member properties from data array
     *
     * @param array $data
     * @param string $profile_picture_path
     * @param bool $is_child
     * @return void
     */
    private function setMemberProperties($data, $profile_picture_path = '', $is_child = false) {
        $this->member->first_name = sanitize($data['first_name']);
        $this->member->last_name = sanitize($data['last_name']);
        $this->member->email = !empty($data['email']) ? sanitize($data['email']) : null;
        $this->member->phone_number = sanitize($data['phone_number'] ?? '');
        $this->member->date_of_birth = $this->convertDateFormat($data['date_of_birth'] ?? null);
        $this->member->gender = sanitize($data['gender']);
        $this->member->marital_status = sanitize($data['marital_status'] ?? ($is_child ? 'single' : ''));
        $this->member->location = sanitize($data['location'] ?? '');
        $this->member->emergency_contact_name = sanitize($data['emergency_contact_name'] ?? '');
        $this->member->emergency_contact_phone = sanitize($data['emergency_contact_phone'] ?? '');
        $this->member->baptism_status = sanitize($data['baptism_status'] ?? 'not_baptized');
        $this->member->department = sanitize($data['department'] ?? ($is_child ? 'children' : 'none'));
        $this->member->role = sanitize($data['role'] ?? 'member');
        $this->member->membership_date = sanitize($data['membership_date'] ?? date('Y-m-d'));
        $this->member->occupation = sanitize($data['occupation'] ?? '');
        $this->member->school = sanitize($data['school'] ?? '');
        $this->member->member_status = sanitize($data['member_status'] ?? 'active');
        $this->member->profile_picture = $profile_picture_path;
        $this->member->created_at = date('Y-m-d H:i:s');
        $this->member->updated_at = date('Y-m-d H:i:s');
    }

    /**
     * Set child member properties from data array
     *
     * @param Member $child_member
     * @param array $child_data
     * @param Member $parent_member
     * @return void
     */
    private function setChildMemberProperties($child_member, $child_data, $parent_member) {
        $child_member->first_name = sanitize($child_data['first_name']);
        $child_member->last_name = sanitize($child_data['last_name']);
        $child_member->email = null; // Children typically don't have email - use NULL to avoid UNIQUE constraint issues
        $child_member->phone_number = ''; // Children typically don't have phone
        $child_member->date_of_birth = $this->convertDateFormat($child_data['date_of_birth']);
        $child_member->gender = $child_data['gender'];
        $child_member->marital_status = 'single'; // Children are single
        $child_member->location = $parent_member->location; // Inherit parent's location
        $child_member->emergency_contact_name = $parent_member->first_name . ' ' . $parent_member->last_name;
        $child_member->emergency_contact_phone = $parent_member->phone_number;
        $child_member->baptism_status = 'not_baptized'; // Default for children
        $child_member->department = $child_data['department'] ?? 'children';
        $child_member->role = 'member';
        $child_member->membership_date = date('Y-m-d');
        $child_member->occupation = ''; // Children don't have occupation
        $child_member->school = sanitize($child_data['school'] ?? '');
        $child_member->member_status = 'active';
        $child_member->profile_picture = '';
        $child_member->created_at = date('Y-m-d H:i:s');
        $child_member->updated_at = date('Y-m-d H:i:s');
    }

    /**
     * Convert date format from DD-MM-YYYY to YYYY-MM-DD for MySQL
     *
     * @param string|null $date_string
     * @return string|null
     */
    private function convertDateFormat($date_string) {
        if (empty($date_string)) {
            return null;
        }

        // Remove any extra whitespace
        $date_string = trim($date_string);

        // Handle invalid MySQL date values
        if ($date_string === '0000-00-00') {
            return null;
        }

        // If already in YYYY-MM-DD format, return as is
        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_string)) {
            return $date_string;
        }

        // Convert DD-MM-YYYY to YYYY-MM-DD
        if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $date_string, $matches)) {
            $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            $year = $matches[3];

            // Validate the date
            if (checkdate($month, $day, $year)) {
                return "$year-$month-$day";
            }
        }

        // Convert DD/MM/YYYY to YYYY-MM-DD
        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $date_string, $matches)) {
            $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            $year = $matches[3];

            // Validate the date
            if (checkdate($month, $day, $year)) {
                return "$year-$month-$day";
            }
        }

        // If we can't parse the date, return null to avoid database errors
        error_log("Could not parse date format: " . $date_string);
        return null;
    }

    /**
     * Get department display name from database
     *
     * @param string $department_name
     * @return string
     */
    private function getDepartmentDisplayName($department_name) {
        if (empty($department_name)) {
            return 'No Department';
        }

        try {
            $stmt = $this->database->getConnection()->prepare("SELECT display_name FROM departments WHERE name = ? AND is_active = 1");
            $stmt->execute([$department_name]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result ? $result['display_name'] : ucfirst(str_replace('_', ' ', $department_name));
        } catch (Exception $e) {
            error_log("Error getting department display name: " . $e->getMessage());
            return ucfirst(str_replace('_', ' ', $department_name));
        }
    }

    /**
     * Get role display name from database
     *
     * @param string $role_name
     * @return string
     */
    private function getRoleDisplayName($role_name) {
        if (empty($role_name)) {
            return 'Member';
        }

        try {
            $stmt = $this->database->getConnection()->prepare("SELECT display_name FROM roles WHERE name = ? AND is_active = 1");
            $stmt->execute([$role_name]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result ? $result['display_name'] : ucfirst(str_replace('_', ' ', $role_name));
        } catch (Exception $e) {
            error_log("Error getting role display name: " . $e->getMessage());
            return ucfirst(str_replace('_', ' ', $role_name));
        }
    }

    /**
     * Get department styling data
     *
     * @return array
     */
    private function getDepartmentStyles() {
        try {
            $stmt = $this->database->getConnection()->prepare("SELECT name, display_name FROM departments WHERE is_active = 1");
            $stmt->execute();
            $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $styles = [];
            $color_classes = [
                'bg-purple-100 text-purple-800',
                'bg-blue-100 text-blue-800',
                'bg-indigo-100 text-indigo-800',
                'bg-green-100 text-green-800',
                'bg-pink-100 text-pink-800',
                'bg-yellow-100 text-yellow-800',
                'bg-red-100 text-red-800',
                'bg-teal-100 text-teal-800',
                'bg-orange-100 text-orange-800',
                'bg-cyan-100 text-cyan-800',
                'bg-lime-100 text-lime-800',
                'bg-emerald-100 text-emerald-800',
                'bg-violet-100 text-violet-800',
                'bg-fuchsia-100 text-fuchsia-800',
                'bg-gray-100 text-gray-800'
            ];

            $index = 0;
            foreach ($departments as $dept) {
                $styles[$dept['name']] = [
                    $dept['display_name'],
                    $color_classes[$index % count($color_classes)]
                ];
                $index++;
            }

            // Add default for unknown departments
            $styles['none'] = ['None', 'bg-gray-100 text-gray-800'];

            return $styles;
        } catch (Exception $e) {
            error_log("Error getting department styles: " . $e->getMessage());
            return ['none' => ['None', 'bg-gray-100 text-gray-800']];
        }
    }

}
