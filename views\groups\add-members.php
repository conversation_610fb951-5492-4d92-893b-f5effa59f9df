<!-- CSRF Token Meta Tag -->
<meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">

<!-- Enhanced Background -->
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-green-50">
    <div class="container mx-auto px-4 py-8">
        <!-- Enhanced Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <div class="flex-1">
                    <div class="flex items-center gap-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>groups/members/<?php echo $group->group_id; ?>" class="text-green-600 hover:text-green-800 transition-colors p-2 rounded-lg hover:bg-green-50">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-800 mb-1">Add Members to Group</h1>
                            <div class="flex items-center gap-4 text-sm text-gray-600">
                                <span class="flex items-center gap-1 bg-green-50 px-3 py-1 rounded-full">
                                    <i class="fas fa-users text-green-600"></i>
                                    <?php echo htmlspecialchars($group->group_name); ?>
                                </span>
                                <span class="flex items-center gap-1 bg-blue-50 px-3 py-1 rounded-full">
                                    <i class="fas fa-user-plus text-blue-600"></i>
                                    Add New Members
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php if ($group->group_description): ?>
                        <div class="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border-l-4 border-green-500 mt-3">
                            <p class="text-gray-700"><?php echo htmlspecialchars($group->group_description); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="flex gap-3">
                    <a href="/icgc/groups/members/<?php echo $group->group_id; ?>" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Group
                    </a>
                </div>
            </div>
        </div>

        <?php if (function_exists('flash')): ?>
            <?php flash('group_message'); ?>
        <?php endif; ?>

        <!-- Enhanced Available Members Section -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8 border border-gray-100">
            <div class="bg-gradient-to-r from-green-50 to-blue-50 p-6 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h2 class="text-xl font-bold text-gray-800 mb-1">Available Members</h2>
                        <p class="text-gray-600 text-sm">Select members to add to this group and assign their roles</p>
                    </div>
                    <div class="flex items-center gap-2 text-sm">
                        <span class="bg-white px-3 py-1 rounded-full text-gray-700 font-medium" id="memberCount">
                            <i class="fas fa-users mr-1 text-blue-600"></i>
                            <?php echo isset($pagination) ? $pagination['total_records'] : count($availableMembers); ?> Adults Available
                        </span>
                        <span class="bg-blue-100 px-3 py-1 rounded-full text-blue-700 font-medium text-xs">
                            <i class="fas fa-user-graduate mr-1"></i>
                            18+ Only
                        </span>
                        <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                        <span class="bg-green-100 px-3 py-1 rounded-full text-green-700 font-medium text-xs">
                            <i class="fas fa-file-alt mr-1"></i>
                            Page <?php echo $pagination['current_page']; ?> of <?php echo $pagination['total_pages']; ?>
                        </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Enhanced Search and Filter -->
            <div class="bg-gray-50 p-6 border-b border-gray-200">
                <div class="flex flex-col lg:flex-row gap-4">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-search mr-2 text-green-600"></i>Search Members
                        </label>
                        <input type="text" id="searchInput" placeholder="Search by name, phone, or email..."
                               value="<?php echo htmlspecialchars($search ?? ''); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white shadow-sm">
                    </div>
                    <div class="lg:w-48">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-filter mr-2 text-blue-600"></i>Filter by Gender
                        </label>
                        <select id="genderFilter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white shadow-sm">
                            <option value="">All Genders</option>
                            <option value="Male" <?php echo ($gender ?? '') === 'Male' ? 'selected' : ''; ?>>👨 Male</option>
                            <option value="Female" <?php echo ($gender ?? '') === 'Female' ? 'selected' : ''; ?>>👩 Female</option>
                        </select>
                    </div>
                    <div class="lg:w-48">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-list mr-2 text-purple-600"></i>Members per Page
                        </label>
                        <select id="perPageSelect" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white shadow-sm">
                            <option value="10">10 per page</option>
                            <option value="20" selected>20 per page</option>
                            <option value="30">30 per page</option>
                            <option value="50">50 per page</option>
                        </select>
                    </div>
                </div>
            </div>
            </div>

        <form action="<?php echo url('groups/add-members-process/' . $group->group_id); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gradient-to-r from-gray-100 to-gray-200">
                        <tr>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <input type="checkbox" id="selectAll" class="mr-3 h-5 w-5 text-green-600 focus:ring-green-500 rounded border-2 border-gray-300">
                                    <label for="selectAll" class="flex items-center cursor-pointer">
                                        <i class="fas fa-check-square mr-2 text-green-600"></i>Select All
                                    </label>
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <i class="fas fa-user mr-2 text-green-600"></i>Member
                            </th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <i class="fas fa-envelope mr-2 text-blue-600"></i>Contact
                            </th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <i class="fas fa-venus-mars mr-2 text-purple-600"></i>Gender
                            </th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                <i class="fas fa-birthday-cake mr-2 text-orange-600"></i>Age
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-100" id="membersTable">
                        <?php if (empty($availableMembers)): ?>
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Available Members</h3>
                                    <p class="text-gray-500">All members are already part of this group or no members exist in the system.</p>
                                </div>
                            </td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($availableMembers as $member): ?>
                            <tr class="member-row hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50 transition-all duration-200" data-name="<?php echo strtolower($member->first_name . ' ' . $member->last_name); ?>" data-phone="<?php echo $member->phone_number; ?>" data-email="<?php echo strtolower($member->email); ?>" data-gender="<?php echo $member->gender; ?>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <input type="checkbox" name="member_ids[]" value="<?php echo $member->id; ?>" class="member-checkbox h-5 w-5 text-green-600 focus:ring-green-500 rounded border-2 border-gray-300">
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-12 w-12">
                                            <?php if (!empty($member->profile_picture)): ?>
                                                <img class="h-12 w-12 rounded-full object-cover border-2 border-green-200 shadow-md" src="/icgc/uploads/members/<?php echo $member->profile_picture; ?>" alt="<?php echo $member->first_name; ?>">
                                            <?php else: ?>
                                                <div class="h-12 w-12 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center text-white font-bold text-lg shadow-md border-2 border-green-200">
                                                    <?php echo substr($member->first_name, 0, 1) . substr($member->last_name, 0, 1); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-bold text-gray-900"><?php echo $member->first_name . ' ' . $member->last_name; ?></div>
                                            <div class="text-xs text-gray-500 flex items-center gap-2">
                                                <span class="bg-gray-100 px-2 py-1 rounded-full">ID: <?php echo $member->id; ?></span>
                                                <?php if (!empty($member->occupation)): ?>
                                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full"><?php echo htmlspecialchars($member->occupation); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="space-y-1">
                                        <div class="flex items-center text-sm text-gray-900">
                                            <i class="fas fa-phone text-green-600 mr-2"></i>
                                            <?php echo htmlspecialchars($member->phone_number); ?>
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <i class="fas fa-envelope text-blue-600 mr-2"></i>
                                            <?php echo htmlspecialchars($member->email); ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold <?php echo $member->gender === 'Male' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'; ?> border shadow-sm">
                                        <i class="fas <?php echo $member->gender === 'Male' ? 'fa-mars' : 'fa-venus'; ?> mr-1"></i>
                                        <?php echo $member->gender; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-birthday-cake text-orange-600 mr-2"></i>
                                        <span class="bg-orange-50 px-2 py-1 rounded-full text-orange-800 font-medium text-xs">
                                            <?php
                                                if (!empty($member->date_of_birth)) {
                                                    $birthdate = new DateTime($member->date_of_birth);
                                                    $today = new DateTime();
                                                    $age = $birthdate->diff($today)->y;
                                                    echo $age . ' years';
                                                } else {
                                                    echo 'N/A';
                                                }
                                            ?>
                                        </span>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Loading Indicator -->
            <div id="loadingIndicator" class="hidden p-6 text-center">
                <div class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mr-3"></div>
                    <span class="text-gray-600">Loading members...</span>
                </div>
            </div>

            <!-- Pagination Controls -->
            <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
            <div class="bg-gray-50 px-6 py-4 border-t border-gray-200" id="paginationControls">
                <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                    <div class="text-sm text-gray-700">
                        Showing <?php echo (($pagination['current_page'] - 1) * $pagination['per_page']) + 1; ?> to
                        <?php echo min($pagination['current_page'] * $pagination['per_page'], $pagination['total_records']); ?>
                        of <?php echo $pagination['total_records']; ?> members
                    </div>
                    <div class="flex items-center gap-2">
                        <?php if ($pagination['has_prev']): ?>
                            <button onclick="loadPage(1)" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button onclick="loadPage(<?php echo $pagination['prev_page']; ?>)" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-angle-left"></i>
                            </button>
                        <?php endif; ?>

                        <?php
                        $start = max(1, $pagination['current_page'] - 2);
                        $end = min($pagination['total_pages'], $pagination['current_page'] + 2);

                        for ($i = $start; $i <= $end; $i++):
                        ?>
                            <button onclick="loadPage(<?php echo $i; ?>)"
                                    class="px-3 py-2 text-sm border rounded-lg transition-colors <?php echo $i === $pagination['current_page'] ? 'bg-green-600 text-white border-green-600' : 'bg-white border-gray-300 hover:bg-gray-50'; ?>">
                                <?php echo $i; ?>
                            </button>
                        <?php endfor; ?>

                        <?php if ($pagination['has_next']): ?>
                            <button onclick="loadPage(<?php echo $pagination['next_page']; ?>)" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button onclick="loadPage(<?php echo $pagination['total_pages']; ?>)" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Enhanced Footer -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-t border-gray-200">
                <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <div class="flex items-center gap-4">
                        <div class="text-sm text-gray-700 bg-white px-4 py-2 rounded-lg shadow-sm border">
                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                            <span id="selectedCount" class="font-bold text-green-600">0</span> members selected
                        </div>
                        <button type="button" onclick="selectAllVisible()" class="text-sm bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-check-double mr-1"></i>Select All Visible
                        </button>
                        <button type="button" onclick="clearSelection()" class="text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times mr-1"></i>Clear Selection
                        </button>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-gray-700">
                                <i class="fas fa-user-tag mr-1 text-purple-600"></i>Role:
                            </label>
                            <select name="role_in_group" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white shadow-sm">
                                <option value="member">👤 Regular Member</option>
                                <option value="leader">👑 Leader</option>
                                <option value="secretary">📝 Secretary</option>
                                <option value="treasurer">💰 Treasurer</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center">
                            <i class="fas fa-user-plus mr-2"></i>Add Selected Members
                        </button>
                    </div>
                </div>
            </div>
        </form>
        </div>
    </div> <!-- Close container -->
</div> <!-- Close background wrapper -->

<script>
    let currentPage = <?php echo isset($pagination) ? $pagination['current_page'] : 1; ?>;
    let currentPageSize = 20; // Default page size
    let isLoading = false;
    let searchTimeout;
    const csrfToken = '<?php echo generate_csrf_token(); ?>';

    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const genderFilter = document.getElementById('genderFilter');
        const selectAll = document.getElementById('selectAll');
        const selectedCountElement = document.getElementById('selectedCount');

        // Function to update selected count
        function updateSelectedCount() {
            const selectedCount = document.querySelectorAll('.member-checkbox:checked').length;
            selectedCountElement.textContent = selectedCount;
        }

        // Function to load members via AJAX
        function loadMembers(page = 1, search = '', gender = '') {
            if (isLoading) return;

            isLoading = true;
            showLoading();

            const groupId = <?php echo $group->group_id; ?>;
            const params = new URLSearchParams({
                ajax: 'members',
                page: page,
                search: search,
                gender: gender,
                per_page: currentPageSize,
                csrf_token: csrfToken
            });

            fetch(`<?php echo url('groups/add-members'); ?>/${groupId}?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateMembersTable(data.html);
                        updatePagination(data.pagination);
                        updateMemberCount(data.pagination);
                        currentPage = page;

                        // Re-initialize event listeners for new content
                        initializeCheckboxEvents();
                    } else {
                        console.error('Error loading members:', data.error);
                        alert('Failed to load members. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Network error:', error);
                    alert('Network error while loading members. Please check your connection.');
                })
                .finally(() => {
                    isLoading = false;
                    hideLoading();
                });
        }

        // Function to show loading indicator
        function showLoading() {
            document.getElementById('loadingIndicator').classList.remove('hidden');
            document.getElementById('membersTable').style.opacity = '0.5';
        }

        // Function to hide loading indicator
        function hideLoading() {
            document.getElementById('loadingIndicator').classList.add('hidden');
            document.getElementById('membersTable').style.opacity = '1';
        }

        // Function to update members table
        function updateMembersTable(html) {
            document.getElementById('membersTable').innerHTML = html;
        }

        // Function to update pagination controls
        function updatePagination(pagination) {
            const paginationContainer = document.getElementById('paginationControls');
            if (!paginationContainer) return;

            if (pagination.total_pages <= 1) {
                paginationContainer.style.display = 'none';
                return;
            }

            paginationContainer.style.display = 'block';

            // Update pagination info
            const start = ((pagination.current_page - 1) * pagination.per_page) + 1;
            const end = Math.min(pagination.current_page * pagination.per_page, pagination.total_records);

            const infoText = `Showing ${start} to ${end} of ${pagination.total_records} members`;
            paginationContainer.querySelector('.text-sm.text-gray-700').textContent = infoText;
        }

        // Function to update member count
        function updateMemberCount(pagination) {
            const memberCountElement = document.getElementById('memberCount');
            if (memberCountElement) {
                memberCountElement.innerHTML = `<i class="fas fa-users mr-1 text-blue-600"></i>${pagination.total_records} Adults Available`;
            }
        }

        // Function to initialize checkbox events
        function initializeCheckboxEvents() {
            const selectAll = document.getElementById('selectAll');
            const memberCheckboxes = document.querySelectorAll('.member-checkbox');

            // Clear previous state
            selectAll.checked = false;
            selectAll.indeterminate = false;

            // Select all checkbox
            selectAll.addEventListener('change', function() {
                const isChecked = this.checked;
                memberCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                updateSelectedCount();
            });

            // Individual checkbox events
            memberCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectedCount();

                    const allChecked = Array.from(memberCheckboxes).every(cb => cb.checked);
                    const someChecked = Array.from(memberCheckboxes).some(cb => cb.checked);

                    selectAll.checked = allChecked;
                    selectAll.indeterminate = someChecked && !allChecked;
                });
            });

            updateSelectedCount();
        }

        // Search input event with debouncing
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const search = this.value.trim();
                const gender = genderFilter.value;
                loadMembers(1, search, gender);
            }, 500); // 500ms delay
        });

        // Gender filter event
        genderFilter.addEventListener('change', function() {
            const search = searchInput.value.trim();
            const gender = this.value;
            loadMembers(1, search, gender);
        });

        // Per page selector event
        const perPageSelect = document.getElementById('perPageSelect');
        perPageSelect.addEventListener('change', function() {
            const search = searchInput.value.trim();
            const gender = genderFilter.value;
            // Update the global page size and reload from page 1
            currentPageSize = parseInt(this.value);
            loadMembers(1, search, gender);
        });

        // Initialize checkbox events for initial load
        initializeCheckboxEvents();

        // Make functions globally available
        window.loadPage = function(page) {
            const search = searchInput.value.trim();
            const gender = genderFilter.value;
            loadMembers(page, search, gender);
        };

        window.updateSelectedCount = updateSelectedCount;
    });

    // Enhanced JavaScript functions
    function clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('genderFilter').value = '';
        document.getElementById('perPageSelect').value = '20';
        currentPageSize = 20;

        // Reload members with cleared filters
        loadPage(1);
    }

    function selectAllVisible() {
        const memberCheckboxes = document.querySelectorAll('.member-checkbox');
        memberCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });

        document.getElementById('selectAll').checked = true;
        document.getElementById('selectAll').indeterminate = false;
        updateSelectedCount();
    }

    function clearSelection() {
        const memberCheckboxes = document.querySelectorAll('.member-checkbox');
        memberCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        document.getElementById('selectAll').checked = false;
        document.getElementById('selectAll').indeterminate = false;
        updateSelectedCount();
    }
</script>
