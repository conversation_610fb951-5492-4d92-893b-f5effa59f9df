<?php
require_once 'helpers/functions.php';
$page_title = getPageTitle("Welfare Management");
$active_page = "welfare";
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Monthly Welfare Management</h1>
            <p class="text-gray-600 mt-1">Track monthly welfare dues and member claims</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>welfare/add"
               class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                <i class="fas fa-coins mr-2"></i>
                Record Dues
            </a>
            <a href="<?php echo BASE_URL; ?>welfare/claim"
               class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                <i class="fas fa-hand-holding-heart mr-2"></i>
                Record Claim
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php
    $flash_message = get_flash_message();
    if ($flash_message):
        $alert_class = '';
        switch ($flash_message['type']) {
            case 'success':
                $alert_class = 'bg-green-100 border-green-500 text-green-700';
                break;
            case 'danger':
                $alert_class = 'bg-red-100 border-red-500 text-red-700';
                break;
            case 'warning':
                $alert_class = 'bg-yellow-100 border-yellow-500 text-yellow-700';
                break;
            default:
                $alert_class = 'bg-blue-100 border-blue-500 text-blue-700';
        }
    ?>
    <div class="<?php echo $alert_class; ?> border-l-4 p-4 mb-6 rounded">
        <div class="flex">
            <div class="flex-shrink-0">
                <?php if ($flash_message['type'] === 'success'): ?>
                    <i class="fas fa-check-circle"></i>
                <?php elseif ($flash_message['type'] === 'danger'): ?>
                    <i class="fas fa-exclamation-circle"></i>
                <?php elseif ($flash_message['type'] === 'warning'): ?>
                    <i class="fas fa-exclamation-triangle"></i>
                <?php else: ?>
                    <i class="fas fa-info-circle"></i>
                <?php endif; ?>
            </div>
            <div class="ml-3">
                <p class="text-sm"><?php echo htmlspecialchars($flash_message['message']); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <!-- Total Monthly Dues Collected -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-emerald-100">
                    <i class="fas fa-coins text-emerald-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Collected</p>
                    <p class="text-2xl font-bold text-emerald-700">₵<?php echo number_format($welfareFunds['total_collected'], 2); ?></p>
                </div>
            </div>
        </div>

        <!-- Total Disbursed -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100">
                    <i class="fas fa-hand-holding-heart text-orange-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Disbursed</p>
                    <p class="text-2xl font-bold text-orange-700">₵<?php echo number_format($welfareFunds['total_disbursed'], 2); ?></p>
                </div>
            </div>
        </div>

        <!-- Available Balance -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="fas fa-wallet text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Available Balance</p>
                    <p class="text-2xl font-bold text-blue-700">₵<?php echo number_format($welfareFunds['available_balance'], 2); ?></p>
                </div>
            </div>
        </div>

        <!-- Members Who Paid This Month -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <i class="fas fa-user-check text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Paid This Month</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['members_paid']); ?></p>
                </div>
            </div>
        </div>

        <!-- Members Who Claimed This Month -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <i class="fas fa-users text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Claimed This Month</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['members_claimed']); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Welfare Tracking -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Members Who Paid This Month -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Members Who Paid This Month</h2>
                        <p class="text-sm text-gray-600"><?php echo date('F Y'); ?> • <?php echo isset($totalPaidCount) ? $totalPaidCount : 'N/A'; ?> total payments</p>
                    </div>
                    <?php if (isset($totalPaidCount) && $totalPaidCount > 1): ?>
                        <a href="<?php echo BASE_URL; ?>welfare/payments-this-month"
                           class="text-emerald-600 hover:text-emerald-700 text-sm font-medium">
                            View All (<?php echo $totalPaidCount; ?>)
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="p-6">
                <?php if (empty($membersWhoPaid)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-coins text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500">No payments recorded this month</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-3 max-h-80 overflow-y-auto">
                        <?php foreach ($membersWhoPaid as $member): ?>
                            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                <div class="flex items-center flex-1">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-green-600 text-sm"></i>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <p class="font-medium text-gray-900 text-sm">
                                            <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                                        </p>
                                        <p class="text-xs text-gray-600">
                                            <?php echo date('M d', strtotime($member['payment_date'])); ?> -
                                            <?php echo ucfirst(str_replace('_', ' ', $member['payment_method'])); ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="text-right">
                                        <p class="font-semibold text-green-700 text-sm">₵<?php echo number_format($member['amount'], 2); ?></p>
                                    </div>
                                    <div class="flex flex-col space-y-1">
                                        <!-- View History Link -->
                                        <a href="<?php echo BASE_URL; ?>welfare/history/<?php echo $member['member_id']; ?>"
                                           class="text-emerald-600 hover:text-emerald-800 text-xs font-medium"
                                           title="View Member History">
                                            <i class="fas fa-history mr-1"></i>History
                                        </a>
                                        <!-- Action Buttons -->
                                        <div class="flex space-x-1">
                                            <a href="<?php echo BASE_URL; ?>welfare/edit-payment/<?php echo $member['payment_id']; ?>"
                                               class="text-blue-600 hover:text-blue-800 p-1 rounded transition-colors"
                                               title="Edit Payment">
                                                <i class="fas fa-edit text-xs"></i>
                                            </a>
                                            <button onclick="deletePayment(<?php echo $member['payment_id']; ?>, '<?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>')"
                                                    class="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
                                                    title="Delete Payment">
                                                <i class="fas fa-trash text-xs"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <?php if ($totalPaidCount > count($membersWhoPaid)): ?>
                        <div class="mt-4 text-center">
                            <p class="text-sm text-gray-500 mb-2">
                                Showing <?php echo count($membersWhoPaid); ?> of <?php echo $totalPaidCount; ?> payments
                            </p>
                            <a href="<?php echo BASE_URL; ?>welfare/payments-this-month"
                               class="inline-flex items-center px-4 py-2 bg-emerald-50 hover:bg-emerald-100 text-emerald-700 rounded-lg text-sm font-medium transition-colors">
                                <i class="fas fa-list mr-2"></i>
                                View All Payments
                            </a>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Members Who Claimed This Month -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Members Who Claimed This Month</h2>
                        <p class="text-sm text-gray-600"><?php echo date('F Y'); ?> • <?php echo isset($totalClaimedCount) ? $totalClaimedCount : 'N/A'; ?> total claims</p>
                    </div>
                    <?php if (isset($totalClaimedCount) && $totalClaimedCount > 0): ?>
                        <a href="<?php echo BASE_URL; ?>welfare/claims-this-month"
                           class="text-orange-600 hover:text-orange-700 text-sm font-medium">
                            View All (<?php echo $totalClaimedCount; ?>)
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="p-6">
                <?php if (empty($membersWhoClaimed)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-hand-holding-heart text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500">No claims this month</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-3 max-h-80 overflow-y-auto">
                        <?php foreach ($membersWhoClaimed as $member): ?>
                            <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                                <div class="flex items-center flex-1">
                                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-hand-holding-heart text-orange-600 text-sm"></i>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <p class="font-medium text-gray-900 text-sm">
                                            <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                                        </p>
                                        <p class="text-xs text-gray-600">
                                            <?php echo date('M d', strtotime($member['claim_date'])); ?> -
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                                <?php
                                                switch($member['status']) {
                                                    case 'disbursed': echo 'bg-green-100 text-green-800'; break;
                                                    case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                    case 'approved': echo 'bg-blue-100 text-blue-800'; break;
                                                    default: echo 'bg-gray-100 text-gray-800';
                                                }
                                                ?>">
                                                <?php echo ucfirst($member['status']); ?>
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="text-right">
                                        <p class="font-semibold text-orange-700 text-sm">₵<?php echo number_format($member['claim_amount'], 2); ?></p>
                                    </div>
                                    <div class="flex flex-col space-y-1">
                                        <!-- View History Link -->
                                        <a href="<?php echo BASE_URL; ?>welfare/history/<?php echo $member['member_id']; ?>"
                                           class="text-orange-600 hover:text-orange-800 text-xs font-medium"
                                           title="View Member History">
                                            <i class="fas fa-history mr-1"></i>History
                                        </a>
                                        <!-- Action Buttons -->
                                        <div class="flex space-x-1">
                                            <a href="<?php echo BASE_URL; ?>welfare/edit-claim/<?php echo $member['claim_id']; ?>"
                                               class="text-blue-600 hover:text-blue-800 p-1 rounded transition-colors"
                                               title="Edit Claim">
                                                <i class="fas fa-edit text-xs"></i>
                                            </a>
                                            <button onclick="deleteClaim(<?php echo $member['claim_id']; ?>, '<?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>')"
                                                    class="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
                                                    title="Delete Claim">
                                                <i class="fas fa-trash text-xs"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <?php if ($totalClaimedCount > count($membersWhoClaimed)): ?>
                        <div class="mt-4 text-center">
                            <p class="text-sm text-gray-500 mb-2">
                                Showing <?php echo count($membersWhoClaimed); ?> of <?php echo $totalClaimedCount; ?> claims
                            </p>
                            <a href="<?php echo BASE_URL; ?>welfare/claims-this-month"
                               class="inline-flex items-center px-4 py-2 bg-orange-50 hover:bg-orange-100 text-orange-700 rounded-lg text-sm font-medium transition-colors">
                                <i class="fas fa-list mr-2"></i>
                                View All Claims
                            </a>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions & Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
            </div>
            <div class="p-6 space-y-3">
                <a href="<?php echo BASE_URL; ?>welfare/add"
                   class="w-full flex items-center p-3 bg-emerald-50 hover:bg-emerald-100 rounded-lg transition-colors">
                    <i class="fas fa-coins text-emerald-600 mr-3"></i>
                    <span class="font-medium text-emerald-700">Record Monthly Dues</span>
                </a>
                <a href="<?php echo BASE_URL; ?>welfare/claim"
                   class="w-full flex items-center p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
                    <i class="fas fa-hand-holding-heart text-orange-600 mr-3"></i>
                    <span class="font-medium text-orange-700">Record Welfare Claim</span>
                </a>
                <a href="<?php echo BASE_URL; ?>welfare/history"
                   class="w-full flex items-center p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                    <i class="fas fa-history text-blue-600 mr-3"></i>
                    <span class="font-medium text-blue-700">Member History</span>
                </a>
                <a href="<?php echo BASE_URL; ?>welfare/reports"
                   class="w-full flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                    <i class="fas fa-chart-bar text-gray-600 mr-3"></i>
                    <span class="font-medium text-gray-700">View Reports</span>
                </a>
            </div>
        </div>

        <!-- Top Recipients -->
        <?php if (!empty($top_recipients)): ?>
        <div class="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Top Recipients (<?php echo date('Y'); ?>)</h2>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <?php foreach ($top_recipients as $index => $recipient): ?>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium text-gray-600">
                                    <?php echo $index + 1; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-medium text-gray-900 text-sm">
                                        <?php echo htmlspecialchars($recipient['first_name'] . ' ' . $recipient['last_name']); ?>
                                    </p>
                                    <p class="text-xs text-gray-500"><?php echo $recipient['payment_count']; ?> payments</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900 text-sm">₵<?php echo number_format($recipient['total_received'], 2); ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Payment Modal -->
<div id="deletePaymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete Payment</h3>
                </div>
                <p class="text-gray-600 mb-6">
                    Are you sure you want to delete the payment for <span id="paymentMemberName" class="font-medium"></span>?
                    This action cannot be undone.
                </p>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeDeletePaymentModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors">
                        Cancel
                    </button>
                    <form id="deletePaymentForm" method="POST" action="<?php echo BASE_URL; ?>welfare/delete-payment" class="inline">
                        <input type="hidden" name="payment_id" id="deletePaymentId">
                        <button type="submit"
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Delete Payment
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Claim Modal -->
<div id="deleteClaimModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete Claim</h3>
                </div>
                <p class="text-gray-600 mb-6">
                    Are you sure you want to delete the claim for <span id="claimMemberName" class="font-medium"></span>?
                    This action cannot be undone.
                </p>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeDeleteClaimModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors">
                        Cancel
                    </button>
                    <form id="deleteClaimForm" method="POST" action="<?php echo BASE_URL; ?>welfare/delete-claim" class="inline">
                        <input type="hidden" name="claim_id" id="deleteClaimId">
                        <button type="submit"
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Delete Claim
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Delete Payment Functions
function deletePayment(paymentId, memberName) {
    document.getElementById('deletePaymentId').value = paymentId;
    document.getElementById('paymentMemberName').textContent = memberName;
    document.getElementById('deletePaymentModal').classList.remove('hidden');
}

function closeDeletePaymentModal() {
    document.getElementById('deletePaymentModal').classList.add('hidden');
}

// Delete Claim Functions
function deleteClaim(claimId, memberName) {
    document.getElementById('deleteClaimId').value = claimId;
    document.getElementById('claimMemberName').textContent = memberName;
    document.getElementById('deleteClaimModal').classList.remove('hidden');
}

function closeDeleteClaimModal() {
    document.getElementById('deleteClaimModal').classList.add('hidden');
}

// Close modals when clicking outside
document.getElementById('deletePaymentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeletePaymentModal();
    }
});

document.getElementById('deleteClaimModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteClaimModal();
    }
});

// Close modals with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDeletePaymentModal();
        closeDeleteClaimModal();
    }
});
</script>