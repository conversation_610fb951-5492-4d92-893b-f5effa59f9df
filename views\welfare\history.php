
<!-- Enhanced <PERSON> Header -->
<div class="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl border border-emerald-100 mb-6">
    <div class="px-6 py-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="flex items-center mb-4 lg:mb-0">
                <a href="<?php echo BASE_URL; ?>welfare"
                   class="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-white shadow-sm border border-gray-200 text-gray-600 hover:text-emerald-600 hover:border-emerald-200 transition-all duration-200 mr-4">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="text-2xl lg:text-3xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-history text-emerald-600 mr-3"></i>
                        Member Welfare History
                    </h1>
                    <p class="text-gray-600 mt-1">Comprehensive welfare assistance tracking and member history</p>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="flex flex-wrap gap-4">
                <div class="bg-white rounded-lg px-4 py-3 shadow-sm border border-gray-200">
                    <div class="text-sm text-gray-500">Total Members</div>
                    <div class="text-xl font-bold text-gray-900"><?php echo count($members_with_welfare ?? []); ?></div>
                </div>
                <div class="bg-white rounded-lg px-4 py-3 shadow-sm border border-gray-200">
                    <div class="text-sm text-gray-500">This Month</div>
                    <div class="text-xl font-bold text-emerald-600"><?php echo date('M Y'); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Search and Filter Section -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="p-4">
        <div class="flex flex-col lg:flex-row lg:items-center gap-4">
            <!-- Quick Search -->
            <div class="flex-1">
                <div class="relative">
                    <input type="text" id="quickSearch"
                           placeholder="Search members by name, phone, or ID..."
                           class="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="flex flex-wrap gap-3">
                <select id="activityFilter" class="px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white min-w-[140px]">
                    <option value="">All Activity</option>
                    <option value="active">Recent Activity</option>
                    <option value="payments-only">Payments Only</option>
                    <option value="claims-only">Claims Only</option>
                    <option value="inactive">No Recent Activity</option>
                </select>

                <select id="sortBy" class="px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 bg-white min-w-[140px]">
                    <option value="name">Sort by Name</option>
                    <option value="total-paid">Sort by Paid</option>
                    <option value="total-claimed">Sort by Claimed</option>
                    <option value="last-activity">Sort by Activity</option>
                </select>

                <button id="clearFilters"
                        class="px-4 py-2.5 text-gray-600 hover:text-gray-800 hover:bg-gray-50 border border-gray-300 rounded-lg transition-all duration-200 flex items-center">
                    <i class="fas fa-times mr-2"></i>
                    Clear
                </button>
            </div>

            <!-- View Toggle -->
            <div class="flex bg-gray-100 rounded-lg p-1">
                <button id="tableViewBtn"
                        class="px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 bg-white text-gray-900 shadow-sm">
                    <i class="fas fa-table mr-1"></i>
                    Table
                </button>
                <button id="cardViewBtn"
                        class="px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-900">
                    <i class="fas fa-th-large mr-1"></i>
                    Cards
                </button>
            </div>
        </div>

        <!-- Results Summary -->
        <div class="flex items-center justify-between pt-3 border-t border-gray-100">
            <div class="text-sm text-gray-600">
                Showing <span id="visibleCount" class="font-medium text-gray-900"><?php echo count($members_with_welfare ?? []); ?></span>
                of <span id="totalCount" class="font-medium text-gray-900"><?php echo count($members_with_welfare ?? []); ?></span> members
            </div>
            <div class="text-sm text-gray-500">
                <i class="fas fa-info-circle mr-1"></i>
                Click member names to view detailed history
            </div>
        </div>
    </div>
</div>

<!-- Members with Welfare History -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <?php if (empty($members_with_welfare)): ?>
        <div class="text-center py-12 px-6">
            <i class="fas fa-hand-holding-heart text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Welfare History</h3>
            <p class="text-gray-500 mb-6">No welfare payments have been recorded yet.</p>
            <a href="<?php echo BASE_URL; ?>welfare/add" 
               class="bg-emerald-500 hover:bg-emerald-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Record First Payment
            </a>
        </div>
    <?php else: ?>
        
        <!-- Enhanced Table View -->
        <div id="tableView" class="overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead class="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors rounded-tl-lg" onclick="sortTable('name')">
                                <div class="flex items-center">
                                    <i class="fas fa-user mr-2 text-gray-500"></i>
                                    Member
                                    <i class="fas fa-sort ml-2 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors" onclick="sortTable('total_paid')">
                                <div class="flex items-center">
                                    <i class="fas fa-coins mr-2 text-emerald-500"></i>
                                    Paid
                                    <i class="fas fa-sort ml-2 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors" onclick="sortTable('total_claimed')">
                                <div class="flex items-center">
                                    <i class="fas fa-hand-holding-heart mr-2 text-orange-500"></i>
                                    Claimed
                                    <i class="fas fa-sort ml-2 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors" onclick="sortTable('balance')">
                                <div class="flex items-center">
                                    <i class="fas fa-balance-scale mr-2 text-blue-500"></i>
                                    Balance
                                    <i class="fas fa-sort ml-2 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors" onclick="sortTable('last_activity')">
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-2 text-gray-500"></i>
                                    Last Activity
                                    <i class="fas fa-sort ml-2 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-3 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider rounded-tr-lg">
                                <i class="fas fa-cog mr-1 text-gray-500"></i>
                                Actions
                            </th>
                        </tr>
                    </thead>
                <tbody class="bg-white divide-y divide-gray-100">
                    <?php foreach ($members_with_welfare as $member):
                        $balance = $member['total_paid'] - $member['total_claimed'];
                        $balanceClass = $balance >= 0 ? 'text-emerald-700' : 'text-red-700';
                        $balanceIcon = $balance >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                    ?>
                        <tr class="hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-200 member-row group"
                            data-name="<?php echo strtolower($member['first_name'] . ' ' . $member['last_name']); ?>"
                            data-phone="<?php echo strtolower($member['phone_number'] ?? ''); ?>"
                            data-total-paid="<?php echo $member['total_paid']; ?>"
                            data-total-claimed="<?php echo $member['total_claimed']; ?>"
                            data-payment-count="<?php echo $member['payment_count']; ?>"
                            data-claim-count="<?php echo $member['claim_count']; ?>"
                            data-last-activity="<?php echo $member['last_activity_date']; ?>">

                            <!-- Member Info -->
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full flex items-center justify-center mr-3 shadow-sm">
                                        <span class="text-white font-bold text-xs">
                                            <?php echo substr($member['first_name'], 0, 1) . substr($member['last_name'], 0, 1); ?>
                                        </span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 group-hover:text-emerald-700 transition-colors">
                                            <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                                        </div>
                                        <?php if (!empty($member['phone_number'])): ?>
                                            <div class="text-xs text-gray-500"><?php echo htmlspecialchars($member['phone_number']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>

                            <!-- Total Paid -->
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-emerald-500 rounded-full mr-2"></div>
                                    <div>
                                        <div class="text-sm font-semibold text-emerald-700">₵<?php echo number_format($member['total_paid'], 2); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo $member['payment_count']; ?> payment<?php echo $member['payment_count'] != 1 ? 's' : ''; ?></div>
                                    </div>
                                </div>
                            </td>

                            <!-- Total Claimed -->
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
                                    <div>
                                        <div class="text-sm font-semibold text-orange-700">₵<?php echo number_format($member['total_claimed'], 2); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo $member['claim_count']; ?> claim<?php echo $member['claim_count'] != 1 ? 's' : ''; ?></div>
                                    </div>
                                </div>
                            </td>

                            <!-- Balance -->
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <i class="fas <?php echo $balanceIcon; ?> text-xs mr-2 <?php echo $balanceClass; ?>"></i>
                                    <div>
                                        <div class="text-sm font-semibold <?php echo $balanceClass; ?>">₵<?php echo number_format(abs($balance), 2); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo $balance >= 0 ? 'Credit' : 'Deficit'; ?></div>
                                    </div>
                                </div>
                            </td>

                            <!-- Last Activity -->
                            <td class="px-4 py-3">
                                <?php
                                if ($member['last_activity_date'] && $member['last_activity_date'] !== '1900-01-01') {
                                    $days_ago = floor((time() - strtotime($member['last_activity_date'])) / (60 * 60 * 24));
                                    if ($days_ago == 0) {
                                        echo '<div class="flex items-center">
                                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                                                <div>
                                                    <div class="text-sm font-medium text-green-600">Today</div>
                                                    <div class="text-xs text-gray-500">Active</div>
                                                </div>
                                              </div>';
                                    } elseif ($days_ago <= 7) {
                                        echo '<div class="flex items-center">
                                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                                <div>
                                                    <div class="text-sm font-medium text-green-600">' . $days_ago . ' day' . ($days_ago > 1 ? 's' : '') . ' ago</div>
                                                    <div class="text-xs text-gray-500">Recent</div>
                                                </div>
                                              </div>';
                                    } elseif ($days_ago <= 30) {
                                        echo '<div class="flex items-center">
                                                <div class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                                                <div>
                                                    <div class="text-sm font-medium text-yellow-600">' . $days_ago . ' days ago</div>
                                                    <div class="text-xs text-gray-500">Moderate</div>
                                                </div>
                                              </div>';
                                    } else {
                                        echo '<div class="flex items-center">
                                                <div class="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-600">' . date('M d, Y', strtotime($member['last_activity_date'])) . '</div>
                                                    <div class="text-xs text-gray-500">Old</div>
                                                </div>
                                              </div>';
                                    }
                                } else {
                                    echo '<div class="flex items-center">
                                            <div class="w-2 h-2 bg-gray-300 rounded-full mr-2"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-400">No activity</div>
                                                <div class="text-xs text-gray-400">Never</div>
                                            </div>
                                          </div>';
                                }
                                ?>
                            </td>

                            <!-- Actions -->
                            <td class="px-4 py-3 text-center">
                                <a href="<?php echo BASE_URL; ?>welfare/history/<?php echo $member['id']; ?>"
                                   class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-xs font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md group">
                                    <i class="fas fa-eye mr-1.5 group-hover:scale-110 transition-transform"></i>
                                    View Details
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Enhanced Card View -->
        <div id="cardView" class="hidden p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                <?php foreach ($members_with_welfare as $member):
                    $balance = $member['total_paid'] - $member['total_claimed'];
                    $balanceClass = $balance >= 0 ? 'text-emerald-600' : 'text-red-600';
                    $balanceBg = $balance >= 0 ? 'bg-emerald-50' : 'bg-red-50';
                ?>
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-emerald-200 transition-all duration-300 member-card group"
                         data-name="<?php echo strtolower($member['first_name'] . ' ' . $member['last_name']); ?>"
                         data-phone="<?php echo strtolower($member['phone_number'] ?? ''); ?>"
                         data-total-paid="<?php echo $member['total_paid']; ?>"
                         data-total-claimed="<?php echo $member['total_claimed']; ?>"
                         data-payment-count="<?php echo $member['payment_count']; ?>"
                         data-claim-count="<?php echo $member['claim_count']; ?>"
                         data-last-activity="<?php echo $member['last_activity_date']; ?>">

                        <!-- Card Header -->
                        <div class="p-5 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-xl flex items-center justify-center mr-4 shadow-sm group-hover:scale-105 transition-transform">
                                    <span class="text-white font-bold text-sm">
                                        <?php echo substr($member['first_name'], 0, 1) . substr($member['last_name'], 0, 1); ?>
                                    </span>
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 text-sm group-hover:text-emerald-700 transition-colors">
                                        <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                                    </h3>
                                    <?php if (!empty($member['phone_number'])): ?>
                                        <p class="text-xs text-gray-500 mt-0.5"><?php echo htmlspecialchars($member['phone_number']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Card Body -->
                        <div class="p-5">
                            <!-- Financial Summary -->
                            <div class="grid grid-cols-3 gap-3 mb-4">
                                <div class="text-center p-3 bg-emerald-50 rounded-lg">
                                    <div class="text-xs text-emerald-600 font-medium mb-1">Paid</div>
                                    <div class="text-sm font-bold text-emerald-700">₵<?php echo number_format($member['total_paid'], 0); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo $member['payment_count']; ?> times</div>
                                </div>
                                <div class="text-center p-3 bg-orange-50 rounded-lg">
                                    <div class="text-xs text-orange-600 font-medium mb-1">Claimed</div>
                                    <div class="text-sm font-bold text-orange-700">₵<?php echo number_format($member['total_claimed'], 0); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo $member['claim_count']; ?> times</div>
                                </div>
                                <div class="text-center p-3 <?php echo $balanceBg; ?> rounded-lg">
                                    <div class="text-xs <?php echo $balanceClass; ?> font-medium mb-1">Balance</div>
                                    <div class="text-sm font-bold <?php echo $balanceClass; ?>">₵<?php echo number_format(abs($balance), 0); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo $balance >= 0 ? 'Credit' : 'Deficit'; ?></div>
                                </div>
                            </div>

                            <!-- Last Activity -->
                            <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                                <div class="text-xs text-gray-600 font-medium mb-1">Last Activity</div>
                                <?php
                                if ($member['last_activity_date'] && $member['last_activity_date'] !== '1900-01-01') {
                                    $days_ago = floor((time() - strtotime($member['last_activity_date'])) / (60 * 60 * 24));
                                    if ($days_ago == 0) {
                                        echo '<div class="text-sm font-medium text-green-600">Today</div>';
                                    } elseif ($days_ago <= 7) {
                                        echo '<div class="text-sm font-medium text-green-600">' . $days_ago . ' day' . ($days_ago > 1 ? 's' : '') . ' ago</div>';
                                    } elseif ($days_ago <= 30) {
                                        echo '<div class="text-sm font-medium text-yellow-600">' . $days_ago . ' days ago</div>';
                                    } else {
                                        echo '<div class="text-sm font-medium text-gray-600">' . date('M d, Y', strtotime($member['last_activity_date'])) . '</div>';
                                    }
                                } else {
                                    echo '<div class="text-sm font-medium text-gray-400">No activity</div>';
                                }
                                ?>
                            </div>

                            <!-- Action Button -->
                            <a href="<?php echo BASE_URL; ?>welfare/history/<?php echo $member['id']; ?>"
                               class="w-full bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-center py-2.5 px-4 rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center group">
                                <i class="fas fa-eye mr-2 group-hover:scale-110 transition-transform"></i>
                                View Full History
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

    <?php endif; ?>
</div>

<!-- Pagination and Per-Page Controls -->
<?php if (!empty($members_with_welfare) && isset($pagination)): ?>
<div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">

        <!-- Results Info -->
        <div class="text-sm text-gray-600">
            Showing <span class="font-medium text-gray-900"><?php echo count($members_with_welfare); ?></span>
            of <span class="font-medium text-gray-900"><?php echo $pagination['total_count'] ?? 0; ?></span> members
            <?php if (isset($pagination['current_page'], $pagination['total_pages'])): ?>
                (Page <?php echo $pagination['current_page']; ?> of <?php echo $pagination['total_pages']; ?>)
            <?php endif; ?>
        </div>

        <!-- Per Page Selector -->
        <div class="flex items-center gap-2">
            <label for="perPage" class="text-sm text-gray-600">Show:</label>
            <select id="perPage" onchange="changePerPage(this.value)"
                    class="px-3 py-1.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <option value="20" <?php echo (isset($_GET['limit']) && $_GET['limit'] == 20) ? 'selected' : ''; ?>>20 per page</option>
                <option value="50" <?php echo (!isset($_GET['limit']) || $_GET['limit'] == 50) ? 'selected' : ''; ?>>50 per page</option>
                <option value="100" <?php echo (isset($_GET['limit']) && $_GET['limit'] == 100) ? 'selected' : ''; ?>>100 per page</option>
                <option value="200" <?php echo (isset($_GET['limit']) && $_GET['limit'] == 200) ? 'selected' : ''; ?>>200 per page</option>
                <option value="500" <?php echo (isset($_GET['limit']) && $_GET['limit'] == 500) ? 'selected' : ''; ?>>500 per page</option>
                <option value="1000" <?php echo (isset($_GET['limit']) && $_GET['limit'] == 1000) ? 'selected' : ''; ?>>1000 per page</option>
            </select>
        </div>

        <!-- Pagination Navigation -->
        <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
        <nav class="flex items-center space-x-2">
            <!-- Previous Page -->
            <?php if ($pagination['has_prev']): ?>
                <a href="?page=<?php echo $pagination['current_page'] - 1; ?><?php echo isset($_GET['limit']) ? '&limit=' . $_GET['limit'] : ''; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['filter']) ? '&filter=' . $_GET['filter'] : ''; ?>"
                   class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors flex items-center">
                    <i class="fas fa-chevron-left mr-1"></i>
                    Previous
                </a>
            <?php endif; ?>

            <!-- Page Numbers -->
            <?php
            $start_page = max(1, $pagination['current_page'] - 2);
            $end_page = min($pagination['total_pages'], $pagination['current_page'] + 2);

            for ($i = $start_page; $i <= $end_page; $i++):
            ?>
                <a href="?page=<?php echo $i; ?><?php echo isset($_GET['limit']) ? '&limit=' . $_GET['limit'] : ''; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['filter']) ? '&filter=' . $_GET['filter'] : ''; ?>"
                   class="px-3 py-2 <?php echo $i == $pagination['current_page'] ? 'bg-emerald-500 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'; ?> rounded-lg transition-colors">
                    <?php echo $i; ?>
                </a>
            <?php endfor; ?>

            <!-- Next Page -->
            <?php if ($pagination['has_next']): ?>
                <a href="?page=<?php echo $pagination['current_page'] + 1; ?><?php echo isset($_GET['limit']) ? '&limit=' . $_GET['limit'] : ''; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['filter']) ? '&filter=' . $_GET['filter'] : ''; ?>"
                   class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors flex items-center">
                    Next
                    <i class="fas fa-chevron-right ml-1"></i>
                </a>
            <?php endif; ?>
        </nav>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>



<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let currentView = 'table';
    let sortDirection = 'asc';
    let sortField = 'name';

    // Get all member elements
    const memberRows = document.querySelectorAll('.member-row');
    const memberCards = document.querySelectorAll('.member-card');

    // View switching elements
    const tableViewBtn = document.getElementById('tableViewBtn');
    const cardViewBtn = document.getElementById('cardViewBtn');
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');

    // Filter elements
    const quickSearch = document.getElementById('quickSearch');
    const activityFilter = document.getElementById('activityFilter');
    const sortBy = document.getElementById('sortBy');
    const clearFilters = document.getElementById('clearFilters');

    // Count elements
    const totalCount = document.getElementById('totalCount');
    const visibleCount = document.getElementById('visibleCount');

    // Initialize counts and setup
    updateCounts();
    setupEventListeners();

    // Enhanced view switching with smooth transitions
    function switchView(view) {
        currentView = view;

        // Add fade out effect
        if (tableView) tableView.style.opacity = '0.5';
        if (cardView) cardView.style.opacity = '0.5';

        setTimeout(() => {
            // Hide all views
            if (tableView) tableView.classList.add('hidden');
            if (cardView) cardView.classList.add('hidden');

            // Remove active classes
            [tableViewBtn, cardViewBtn].forEach(btn => {
                if (btn) {
                    btn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
                    btn.classList.add('text-gray-600', 'hover:text-gray-900');
                }
            });

            // Show selected view and activate button
            if (view === 'table' && tableView && tableViewBtn) {
                tableView.classList.remove('hidden');
                tableViewBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
                tableViewBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
            } else if (view === 'cards' && cardView && cardViewBtn) {
                cardView.classList.remove('hidden');
                cardViewBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
                cardViewBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
            }

            // Fade in effect
            setTimeout(() => {
                if (tableView) tableView.style.opacity = '1';
                if (cardView) cardView.style.opacity = '1';
            }, 100);

            // Apply current filters to new view
            applyFilters();
        }, 150);
    }

    // Enhanced filter functions with animations
    function applyFilters() {
        const searchTerm = quickSearch ? quickSearch.value.toLowerCase() : '';
        const activityValue = activityFilter ? activityFilter.value : '';
        const sortValue = sortBy ? sortBy.value : 'name';

        let visibleMembers = 0;
        const elements = currentView === 'table' ? memberRows : memberCards;

        // Apply filters with smooth animations
        elements.forEach((element, index) => {
            let show = true;

            // Search filter
            if (searchTerm) {
                const name = element.dataset.name || '';
                const phone = element.dataset.phone || '';
                if (!name.includes(searchTerm) && !phone.includes(searchTerm)) {
                    show = false;
                }
            }

            // Activity filter
            if (activityValue && show) {
                const paymentCount = parseInt(element.dataset.paymentCount) || 0;
                const claimCount = parseInt(element.dataset.claimCount) || 0;
                const lastActivity = element.dataset.lastActivity;

                switch(activityValue) {
                    case 'active':
                        const daysSinceActivity = lastActivity && lastActivity !== '1900-01-01' ?
                            Math.floor((Date.now() - new Date(lastActivity).getTime()) / (1000 * 60 * 60 * 24)) : 999;
                        if (daysSinceActivity > 30) show = false;
                        break;
                    case 'payments-only':
                        if (paymentCount === 0) show = false;
                        break;
                    case 'claims-only':
                        if (claimCount === 0) show = false;
                        break;
                    case 'inactive':
                        const inactiveDays = lastActivity && lastActivity !== '1900-01-01' ?
                            Math.floor((Date.now() - new Date(lastActivity).getTime()) / (1000 * 60 * 60 * 24)) : 999;
                        if (inactiveDays <= 30) show = false;
                        break;
                }
            }

            // Smooth show/hide with animation
            if (show) {
                element.style.display = '';
                element.style.opacity = '0';
                element.style.transform = 'translateY(10px)';
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                    element.style.transition = 'all 0.3s ease';
                }, index * 50); // Stagger animation
                visibleMembers++;
            } else {
                element.style.opacity = '0';
                element.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    element.style.display = 'none';
                }, 200);
            }
        });

        updateCounts(visibleMembers);
    }

    function updateCounts(visible = null) {
        const total = memberRows.length;
        const visibleCountValue = visible !== null ? visible : total;

        if (totalCount) totalCount.textContent = total;
        if (visibleCount) visibleCount.textContent = visibleCountValue;
    }

    // Clear filters with animation
    function clearAllFilters() {
        if (quickSearch) {
            quickSearch.value = '';
            quickSearch.focus();
        }
        if (activityFilter) activityFilter.value = '';
        if (sortBy) sortBy.value = 'name';

        // Add visual feedback
        const button = clearFilters;
        if (button) {
            button.innerHTML = '<i class="fas fa-check mr-2"></i>Cleared';
            button.classList.add('bg-green-100', 'text-green-700', 'border-green-300');
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-times mr-2"></i>Clear';
                button.classList.remove('bg-green-100', 'text-green-700', 'border-green-300');
            }, 1000);
        }

        applyFilters();
    }

    // Setup all event listeners
    function setupEventListeners() {
        // View switching
        if (tableViewBtn) tableViewBtn.addEventListener('click', () => switchView('table'));
        if (cardViewBtn) cardViewBtn.addEventListener('click', () => switchView('cards'));

        // Filtering
        if (quickSearch) {
            quickSearch.addEventListener('input', applyFilters);
            quickSearch.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    this.value = '';
                    applyFilters();
                }
            });
        }

        if (activityFilter) activityFilter.addEventListener('change', applyFilters);
        if (sortBy) sortBy.addEventListener('change', applyFilters);
        if (clearFilters) clearFilters.addEventListener('click', clearAllFilters);

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'f':
                        e.preventDefault();
                        if (quickSearch) quickSearch.focus();
                        break;
                    case '1':
                        e.preventDefault();
                        switchView('table');
                        break;
                    case '2':
                        e.preventDefault();
                        switchView('cards');
                        break;
                }
            }
        });
    }

    // Enhanced table sorting
    window.sortTable = function(field) {
        sortDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
        sortField = field;

        // Visual feedback on header
        document.querySelectorAll('th i.fa-sort').forEach(icon => {
            icon.className = 'fas fa-sort ml-2 text-gray-400';
        });

        const clickedHeader = event.target.closest('th');
        const sortIcon = clickedHeader.querySelector('i.fa-sort');
        if (sortIcon) {
            sortIcon.className = `fas fa-sort-${sortDirection === 'asc' ? 'up' : 'down'} ml-2 text-emerald-600`;
        }

        console.log(`Sorting by ${field} (${sortDirection})`);
        // Actual sorting would be implemented here
    };

    // Function to handle per-page changes
    window.changePerPage = function(newLimit) {
        const url = new URL(window.location);
        url.searchParams.set('limit', newLimit);
        url.searchParams.set('page', '1'); // Reset to first page when changing limit
        window.location.href = url.toString();
    };
});
</script>
