<?php // No header needed as it's included in the layout ?>

<div class="container mx-auto px-4 py-6 max-w-7xl">
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-blue-600 to-blue-500 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-white p-2 rounded-full mr-3">
                        <i class="fas fa-calendar-alt text-blue-500 text-xl"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-white">Maintenance Scheduling</h1>
                </div>
                <a href="<?php echo BASE_URL; ?>settings#database/system-maintenance" class="bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back to System Maintenance
                </a>
            </div>
        </div>

        <div class="p-6">
            <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6 rounded-r-md">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-500"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">About Maintenance Scheduling</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p>Schedule automatic maintenance tasks to keep your system running smoothly. Scheduled maintenance will run in the background at the specified time.</p>
                            <ul class="list-disc pl-5 space-y-1 mt-2">
                                <li>Choose between weekly, monthly, or quarterly schedules</li>
                                <li>Select which maintenance tasks to run automatically</li>
                                <li>All maintenance operations will be logged in the maintenance log</li>
                                <li>You will receive an email notification when scheduled maintenance completes</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <form action="<?php echo BASE_URL; ?>settings/process-schedule" method="POST" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Schedule Type -->
                    <div>
                        <label for="schedule_type" class="block text-sm font-medium text-gray-700 mb-1">Schedule Type</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-alt text-gray-400"></i>
                            </div>
                            <select id="schedule_type" name="schedule_type" required
                                class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 rounded-md py-3">
                                <option value="">Select Schedule Type</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="quarterly">Quarterly</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Schedule Day -->
                    <div id="daySelectContainer" class="hidden">
                        <label for="schedule_day" class="block text-sm font-medium text-gray-700 mb-1">Day of Week/Month</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-day text-gray-400"></i>
                            </div>
                            <select id="schedule_day" name="schedule_day"
                                class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 rounded-md py-3">
                                <!-- Options will be populated by JavaScript -->
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Schedule Time -->
                    <div>
                        <label for="schedule_time" class="block text-sm font-medium text-gray-700 mb-1">Time</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-clock text-gray-400"></i>
                            </div>
                            <input type="time" id="schedule_time" name="schedule_time" required
                                class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 rounded-md py-3"
                                value="00:00">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">Time</span>
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">
                            Maintenance will run at this time. Choose a time when the system is not heavily used.
                        </p>
                    </div>
                </div>

                <!-- Maintenance Types -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Maintenance Tasks to Run</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-sm transition-all duration-200">
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="maintenance_optimization" name="maintenance_types[]" type="checkbox" value="optimization"
                                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="maintenance_optimization" class="font-medium text-gray-700">Database Optimization</label>
                                    <p class="text-gray-500">Optimize database tables to improve performance and reduce storage space.</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-sm transition-all duration-200">
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="maintenance_archiving" name="maintenance_types[]" type="checkbox" value="archiving"
                                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="maintenance_archiving" class="font-medium text-gray-700">Data Archiving</label>
                                    <p class="text-gray-500">Archive old data to improve system performance while keeping it accessible.</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-sm transition-all duration-200">
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="maintenance_backup" name="maintenance_types[]" type="checkbox" value="backup"
                                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="maintenance_backup" class="font-medium text-gray-700">Database Backup</label>
                                    <p class="text-gray-500">Create a backup of your database for disaster recovery.</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-sm transition-all duration-200">
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="maintenance_cleanup" name="maintenance_types[]" type="checkbox" value="cleanup"
                                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="maintenance_cleanup" class="font-medium text-gray-700">Temporary File Cleanup</label>
                                    <p class="text-gray-500">Remove temporary files and logs to free up disk space.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Notifications -->
                <div class="bg-gray-50 p-4 rounded-md">
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="email_notifications" name="email_notifications" type="checkbox" checked
                                class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="email_notifications" class="font-medium text-gray-700">Send email notifications</label>
                            <p class="text-gray-500">Receive an email notification when scheduled maintenance completes.</p>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-600 py-2 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Save Schedule
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const scheduleTypeSelect = document.getElementById('schedule_type');
        const daySelectContainer = document.getElementById('daySelectContainer');
        const scheduleDaySelect = document.getElementById('schedule_day');

        // Show/hide day select based on schedule type
        scheduleTypeSelect.addEventListener('change', function() {
            const scheduleType = this.value;

            // Clear existing options
            scheduleDaySelect.innerHTML = '';

            if (scheduleType === 'weekly') {
                // Populate with days of the week
                const daysOfWeek = [
                    { value: 0, label: 'Sunday' },
                    { value: 1, label: 'Monday' },
                    { value: 2, label: 'Tuesday' },
                    { value: 3, label: 'Wednesday' },
                    { value: 4, label: 'Thursday' },
                    { value: 5, label: 'Friday' },
                    { value: 6, label: 'Saturday' }
                ];

                daysOfWeek.forEach(day => {
                    const option = document.createElement('option');
                    option.value = day.value;
                    option.textContent = day.label;
                    scheduleDaySelect.appendChild(option);
                });

                daySelectContainer.classList.remove('hidden');
            } else if (scheduleType === 'monthly') {
                // Populate with days of the month
                for (let i = 1; i <= 31; i++) {
                    const option = document.createElement('option');
                    option.value = i;
                    option.textContent = i;
                    scheduleDaySelect.appendChild(option);
                }

                daySelectContainer.classList.remove('hidden');
            } else if (scheduleType === 'quarterly') {
                // Populate with months (first month of each quarter)
                const quarterMonths = [
                    { value: 1, label: 'January (Q1)' },
                    { value: 4, label: 'April (Q2)' },
                    { value: 7, label: 'July (Q3)' },
                    { value: 10, label: 'October (Q4)' }
                ];

                quarterMonths.forEach(month => {
                    const option = document.createElement('option');
                    option.value = month.value;
                    option.textContent = month.label;
                    scheduleDaySelect.appendChild(option);
                });

                daySelectContainer.classList.remove('hidden');
            } else {
                daySelectContainer.classList.add('hidden');
            }
        });

        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            const maintenanceTypes = document.querySelectorAll('input[name="maintenance_types[]"]:checked');

            if (maintenanceTypes.length === 0) {
                event.preventDefault();
                alert('Please select at least one maintenance task to run.');
            }
        });
    });
</script>

<?php // No footer needed as it's included in the layout ?>
