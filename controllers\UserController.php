<?php
/**
 * User Controller
 */

require_once 'models/User.php';
require_once 'utils/validation.php';
require_once 'helpers/functions.php';
require_once 'controllers/BaseRestfulController.php';

class UserController extends BaseRestfulController {
    private $database;
    private $user;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->user = new User($this->database->getConnection());
    }



    /**
     * Show single user (RESTful show)
     *
     * @param int|null $id User ID from route parameter
     * @return void
     */
    public function show($id = null) {
        // Check authentication and authorization
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        if ($_SESSION['role'] !== 'admin') {
            set_flash_message('Access denied. Admin privileges required.', 'danger');
            redirect('dashboard');
            return;
        }

        $user_id = $this->getId($id, 'id', 'id');
        if (!$user_id) {
            $this->handleResponse(false, 'Invalid user ID', 'users');
            return;
        }

        $user = $this->user->getById($user_id);
        if (!$user) {
            $this->handleResponse(false, 'User not found', 'users');
            return;
        }

        // For AJAX requests, return JSON
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'data' => $user]);
            exit;
        }

        $active_page = 'users';
        $page_title = 'View User';

        require_once 'views/users/show.php';
    }

    /**
     * Redirect to settings page with users tab (legacy compatibility)
     *
     * @return void
     */
    public function redirectToSettings() {
        redirect('settings#users');
    }

    /**
     * Display users list
     *
     * @return void
     */
    public function index() {
        // Get all users
        $users = $this->user->getAll();

        // Set page title and active page
        $page_title = getPageTitle('Users');
        $active_page = 'users';

        // Start output buffering
        ob_start();

        // Load view - using the new view file
        require_once 'views/users/index_new.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display user form
     *
     * @return void
     */
    public function create() {
        // Set page title and active page
        $page_title = getPageTitle('Add User');
        $active_page = 'users';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/users/create.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Store user record
     *
     * @return void
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Check password confirmation (UI validation only)
            if ($_POST['password'] !== $_POST['confirm_password']) {
                set_flash_message('Passwords do not match', 'danger');
                $_SESSION['form_data'] = $_POST;
                redirect('users/add');
                exit;
            }

            // Set user properties (model will handle all validation)
            $this->user->username = sanitize($_POST['username']);
            $this->user->email = sanitize($_POST['email']);
            $this->user->password = $_POST['password'];
            $this->user->role = sanitize($_POST['role'] ?? 'staff');
            $this->user->status = 'active';

            // Create user record (model will handle all validation)
            if ($this->user->create()) {
                // Set success message
                set_flash_message('User added successfully', 'success');
                redirect('users');
            } else {
                // Get detailed error message from model
                $errorMessage = $this->user->error ?? 'Failed to add user';
                set_flash_message($errorMessage, 'danger');
                $_SESSION['form_data'] = $_POST;
                redirect('users/add');
            }
        } else {
            // Not a POST request, redirect to user form
            redirect('users/add');
        }
    }

    /**
     * Display edit user form
     *
     * @return void
     */
    public function edit() {
        // Check if ID is provided
        if (!isset($_GET['id'])) {
            set_flash_message('User ID is required', 'danger');
            redirect('users');
            exit;
        }

        // Get user by ID
        $id = (int) $_GET['id'];
        if (!$this->user->getById($id)) {
            set_flash_message('User not found', 'danger');
            redirect('users');
            exit;
        }

        // Set page title and active page
        $page_title = 'Edit User - ICGC Emmanuel Temple';
        $active_page = 'users';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/users/edit.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Update user record
     *
     * @return void
     */
    public function update($id = null) {
        // Support both RESTful (PUT) and legacy (POST) methods
        if (!$this->checkHttpMethod(['POST', 'PUT'])) {
            redirect('users');
            return;
        }

        if (!$this->validateCsrf('users')) {
            return;
        }

        // Get ID from multiple sources (supports both legacy and RESTful)
        $user_id = $this->getId($id, 'id', 'id');
        if (!$user_id) {
            $this->handleResponse(false, 'User ID is required', 'users');
            return;
        }

            // Get user by ID
            $id = (int) $_POST['id'];
            if (!$this->user->getById($id)) {
                set_flash_message('User not found', 'danger');
                redirect('users');
                exit;
            }

            // Set user properties (model will handle all validation)
            $this->user->id = $id;
            $this->user->username = sanitize($_POST['username']);
            $this->user->email = sanitize($_POST['email']);
            $this->user->role = sanitize($_POST['role'] ?? 'staff');
            $this->user->status = sanitize($_POST['status'] ?? 'active');

            // Update user record (model will handle all validation)
            if ($this->user->update()) {
                // Check if password is provided
                if (!empty($_POST['password'])) {
                    // Check password confirmation (UI validation only)
                    if ($_POST['password'] !== $_POST['confirm_password']) {
                        set_flash_message('Passwords do not match', 'danger');
                        $_SESSION['form_data'] = $_POST;
                        redirect('users/edit?id=' . $id);
                        exit;
                    }

                    // Update password (model will handle all validation)
                    if (!$this->user->updatePassword($_POST['password'])) {
                        $passwordError = $this->user->error ?? 'Failed to update password';
                        set_flash_message($passwordError, 'danger');
                        $_SESSION['form_data'] = $_POST;
                        redirect('users/edit?id=' . $id);
                        exit;
                    }
                }

                // Set success message
                set_flash_message('User updated successfully', 'success');
                redirect('users');
            } else {
                // Get detailed error message from model
                $errorMessage = $this->user->error ?? 'Failed to update user';
                set_flash_message($errorMessage, 'danger');
                $_SESSION['form_data'] = $_POST;
                redirect('users/edit?id=' . $id);
            }
    }

    /**
     * Toggle user status (active/blocked)
     *
     * @return void
     */
    public function toggleStatus() {
        // Debug log
        error_log('Toggle status called');

        // Check if ID is provided
        if (!isset($_GET['id'])) {
            error_log('No ID provided');
            set_flash_message('User ID is required', 'danger');
            redirect('users');
            exit;
        }

        // Get user by ID
        $id = (int) $_GET['id'];
        error_log('Toggling status for user ID: ' . $id);

        if (!$this->user->getById($id)) {
            error_log('User not found with ID: ' . $id);
            set_flash_message('User not found', 'danger');
            redirect('users');
            exit;
        }

        error_log('Current user status: ' . ($this->user->status ?? 'not set'));

        // Prevent blocking yourself
        if ($id == $_SESSION['user_id']) {
            error_log('Attempted to block own account');
            set_flash_message('You cannot block your own account', 'danger');
            redirect('users');
            exit;
        }

        // Toggle user status
        $this->user->id = $id;
        $current_status = $this->user->status;

        if ($this->user->toggleStatus()) {
            // Set success message
            $new_status = ($current_status === 'active') ? 'blocked' : 'activated';
            error_log('Status toggled successfully. New status: ' . $new_status);
            set_flash_message('User ' . $new_status . ' successfully', 'success');
        } else {
            // Set error message
            error_log('Failed to toggle status');
            set_flash_message('Failed to update user status', 'danger');
        }

        redirect('users');
    }

    /**
     * Delete user
     *
     * @return void
     */
    public function delete($id = null) {
        // Support both RESTful (DELETE) and legacy (POST) methods
        if (!$this->checkHttpMethod(['POST', 'DELETE'])) {
            redirect('users');
            return;
        }

        if (!$this->validateCsrf('users')) {
            return;
        }

        // Get ID from multiple sources (supports both legacy and RESTful)
        $user_id = $this->getId($id, 'id', 'id');
        if (!$user_id) {
            $this->handleResponse(false, 'User ID is required', 'users');
            return;
        }

        // Get user by ID
        $id = (int) $_GET['id'];
        if (!$this->user->getById($id)) {
            set_flash_message('User not found', 'danger');
            redirect('users');
            exit;
        }

        // Delete user (model will handle all business logic)
        $this->user->id = $id;
        if ($this->user->delete($_SESSION['user_id'])) {
            // Set success message
            set_flash_message('User deleted successfully', 'success');
        } else {
            // Get detailed error message from model
            $errorMessage = $this->user->error ?? 'Failed to delete user';
            set_flash_message($errorMessage, 'danger');
        }

        redirect('users');
    }
}
