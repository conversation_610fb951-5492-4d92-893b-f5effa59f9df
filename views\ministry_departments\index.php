<?php
/**
 * Ministry Departments Index View
 * Displays list of ministry departments with CRUD operations
 */
?>

<div class="max-w-7xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Ministry Departments</h1>
            <p class="text-gray-600 mt-1">Manage ministry departments and their assignments</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>programs"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Programs
            </a>
            <a href="<?php echo BASE_URL; ?>ministry-departments/create"
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Add Department
            </a>
        </div>
    </div>

    <!-- Content -->
    <?php if (empty($departments)): ?>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
            <i class="fas fa-info-circle text-blue-500 text-2xl mb-3"></i>
            <h3 class="text-lg font-medium text-blue-900 mb-2">No Ministry Departments Found</h3>
            <p class="text-blue-700 mb-4">Get started by creating your first ministry department.</p>
            <a href="<?php echo BASE_URL; ?>ministry-departments/create"
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Create First Department
            </a>
        </div>
    <?php else: ?>
        <!-- Departments Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php foreach ($departments as $department): ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <!-- Department Header -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-building text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($department['name']); ?></h3>
                                <p class="text-sm text-gray-500">ID: <?php echo htmlspecialchars($department['id']); ?></p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <?php if (isset($department['is_active']) && $department['is_active']): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Inactive
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Department Details -->
                    <div class="space-y-3 mb-4">
                        <?php if (!empty($department['description'])): ?>
                            <div>
                                <p class="text-sm text-gray-600">
                                    <?php
                                    $description = htmlspecialchars($department['description']);
                                    echo strlen($description) > 100 ? substr($description, 0, 100) . '...' : $description;
                                    ?>
                                </p>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($department['head_of_department'])): ?>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-user-tie w-4 mr-2"></i>
                                <span><?php echo htmlspecialchars($department['head_of_department']); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($department['created_at'])): ?>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-calendar w-4 mr-2"></i>
                                <span>Created <?php echo date('M j, Y', strtotime($department['created_at'])); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex space-x-2">
                            <a href="<?php echo BASE_URL; ?>ministry-departments/<?php echo $department['id']; ?>"
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <i class="fas fa-eye mr-1"></i>View
                            </a>
                            <a href="<?php echo BASE_URL; ?>ministry-departments/<?php echo $department['id']; ?>/edit"
                               class="text-emerald-600 hover:text-emerald-800 text-sm font-medium">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </a>
                        </div>
                        <button type="button"
                                class="text-red-600 hover:text-red-800 text-sm font-medium"
                                onclick="confirmDelete(<?php echo $department['id']; ?>, '<?php echo htmlspecialchars($department['name'], ENT_QUOTES); ?>')">
                            <i class="fas fa-trash mr-1"></i>Delete
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Confirm Delete</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete the department "<span id="departmentName" class="font-medium"></span>"?
                </p>
                <p class="text-sm text-red-600 mt-2">
                    <strong>Warning:</strong> This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="cancelDelete"
                        class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-600 transition-colors">
                    Cancel
                </button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit"
                            class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700 transition-colors">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(departmentId, departmentName) {
    document.getElementById('departmentName').textContent = departmentName;
    document.getElementById('deleteForm').action = '<?php echo BASE_URL; ?>ministry-departments/' + departmentId;
    document.getElementById('deleteModal').classList.remove('hidden');
}

// Close modal handlers
document.getElementById('cancelDelete').addEventListener('click', function() {
    document.getElementById('deleteModal').classList.add('hidden');
});

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
    }
});
</script>
