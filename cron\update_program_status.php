<?php
/**
 * Cron Job: Update Program Status
 * Run daily at 6:00 AM to update program statuses automatically
 * 
 * Crontab entry:
 * 0 6 * * * /usr/bin/php /path/to/icgc/cron/update_program_status.php
 */

// Set the working directory to the project root
$projectRoot = dirname(__DIR__);
chdir($projectRoot);

// Include required files
require_once 'config/database.php';
require_once 'controllers/AutoStatusManager.php';

// Set up error reporting for cron
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', $projectRoot . '/logs/cron_errors.log');

// Create logs directory if it doesn't exist
$logDir = $projectRoot . '/logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

/**
 * Log function for cron output
 */
function cronLog($message) {
    global $projectRoot;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] CRON: {$message}\n";
    
    // Log to file
    file_put_contents($projectRoot . '/logs/cron.log', $logMessage, FILE_APPEND | LOCK_EX);
    
    // Also output to console for cron email notifications
    echo $logMessage;
}

try {
    cronLog("Starting daily program status update");
    
    // Initialize database connection
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Failed to connect to database");
    }
    
    cronLog("Database connection established");
    
    // Initialize Auto Status Manager
    $autoStatusManager = new AutoStatusManager($db);
    
    // Update program statuses
    $updatedCount = $autoStatusManager->updateProgramStatuses();
    
    if ($updatedCount === false) {
        throw new Exception("Status update process failed");
    }
    
    cronLog("Status update completed successfully. Updated {$updatedCount} programs");
    
    // Additional checks for programs starting today
    $startingToday = $autoStatusManager->getProgramsStartingToday();
    if (!empty($startingToday)) {
        cronLog("Found " . count($startingToday) . " programs starting today");
        foreach ($startingToday as $program) {
            cronLog("Program starting today: {$program['title']} (ID: {$program['id']})");
        }
    }
    
    // Clean up old log entries (keep last 30 days)
    $cleanupQuery = "DELETE FROM program_status_log WHERE changed_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
    $stmt = $db->prepare($cleanupQuery);
    $stmt->execute();
    $deletedLogs = $stmt->rowCount();
    
    if ($deletedLogs > 0) {
        cronLog("Cleaned up {$deletedLogs} old log entries");
    }
    
    cronLog("Daily program status update completed successfully");
    
} catch (Exception $e) {
    cronLog("ERROR: " . $e->getMessage());
    cronLog("Stack trace: " . $e->getTraceAsString());
    
    // Send error notification email (optional)
    $errorSubject = "Church Management System - Cron Job Error";
    $errorMessage = "
        <h3>Cron Job Error Report</h3>
        <p><strong>Job:</strong> Daily Program Status Update</p>
        <p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>
        <p><strong>Error:</strong> {$e->getMessage()}</p>
        <p><strong>File:</strong> {$e->getFile()}</p>
        <p><strong>Line:</strong> {$e->getLine()}</p>
        <hr>
        <p>Please check the system logs and resolve this issue.</p>
    ";
    
    // Uncomment and configure if you want email notifications
    // mail('<EMAIL>', $errorSubject, $errorMessage, 'Content-Type: text/html');
    
    exit(1); // Exit with error code
}

// Memory usage reporting
$memoryUsage = memory_get_peak_usage(true);
$memoryMB = round($memoryUsage / 1024 / 1024, 2);
cronLog("Peak memory usage: {$memoryMB} MB");

cronLog("Cron job completed successfully");
exit(0); // Exit with success code
?>
