<?php
/**
 * Equipment Maintenance Model
 */

class EquipmentMaintenance {
    // Database connection and table name
    private $conn;
    private $table_name = "equipment_maintenance";

    // Object properties
    public $id;
    public $equipment_id;
    public $maintenance_date;
    public $description;
    public $cost;
    public $performed_by;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     *
     * @param PDO $db
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all maintenance records
     *
     * @return PDOStatement
     */
    public function getAll() {
        $query = "SELECT m.*, e.name as equipment_name 
                  FROM " . $this->table_name . " m
                  JOIN equipment e ON m.equipment_id = e.id
                  ORDER BY m.maintenance_date DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get maintenance records by equipment ID
     *
     * @param int $equipment_id
     * @return PDOStatement
     */
    public function getByEquipmentId($equipment_id) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE equipment_id = :equipment_id 
                  ORDER BY maintenance_date DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':equipment_id', $equipment_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get maintenance record by ID
     *
     * @param int $id
     * @return bool
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            $this->id = $row['id'];
            $this->equipment_id = $row['equipment_id'];
            $this->maintenance_date = $row['maintenance_date'];
            $this->description = $row['description'];
            $this->cost = $row['cost'];
            $this->performed_by = $row['performed_by'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];

            return true;
        }

        return false;
    }

    /**
     * Create maintenance record
     *
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (equipment_id, maintenance_date, description, cost,
                   performed_by, created_at, updated_at)
                  VALUES
                  (:equipment_id, :maintenance_date, :description, :cost,
                   :performed_by, :created_at, :updated_at)";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->equipment_id = htmlspecialchars(strip_tags($this->equipment_id));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->performed_by = htmlspecialchars(strip_tags($this->performed_by));
        $this->created_at = htmlspecialchars(strip_tags($this->created_at));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':equipment_id', $this->equipment_id);
        $stmt->bindParam(':maintenance_date', $this->maintenance_date);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':cost', $this->cost);
        $stmt->bindParam(':performed_by', $this->performed_by);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Update maintenance record
     *
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET equipment_id = :equipment_id,
                      maintenance_date = :maintenance_date,
                      description = :description,
                      cost = :cost,
                      performed_by = :performed_by,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->id = htmlspecialchars(strip_tags($this->id));
        $this->equipment_id = htmlspecialchars(strip_tags($this->equipment_id));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->performed_by = htmlspecialchars(strip_tags($this->performed_by));
        $this->updated_at = htmlspecialchars(strip_tags($this->updated_at));

        // Bind parameters
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':equipment_id', $this->equipment_id);
        $stmt->bindParam(':maintenance_date', $this->maintenance_date);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':cost', $this->cost);
        $stmt->bindParam(':performed_by', $this->performed_by);
        $stmt->bindParam(':updated_at', $this->updated_at);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Delete maintenance record
     *
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameter
        $stmt->bindParam(':id', $this->id);

        // Execute query
        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    /**
     * Get total maintenance cost for an equipment
     *
     * @param int $equipment_id
     * @return float
     */
    public function getTotalCostByEquipmentId($equipment_id) {
        $query = "SELECT SUM(cost) as total_cost FROM " . $this->table_name . " 
                  WHERE equipment_id = :equipment_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':equipment_id', $equipment_id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $row['total_cost'] ? $row['total_cost'] : 0;
    }

    /**
     * Get maintenance statistics
     *
     * @return array
     */
    public function getStatistics() {
        $stats = [];
        
        // Total maintenance records
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['total_records'] = $row['total'];
        
        // Total maintenance cost
        $query = "SELECT SUM(cost) as total_cost FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['total_cost'] = $row['total_cost'] ? $row['total_cost'] : 0;
        
        // Recent maintenance (last 30 days)
        $query = "SELECT COUNT(*) as recent FROM " . $this->table_name . " 
                  WHERE maintenance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['recent_maintenance'] = $row['recent'];
        
        return $stats;
    }

    /**
     * Get maintenance cost by month
     *
     * @param int $year
     * @return array
     */
    public function getCostByMonth($year = null) {
        if (!$year) {
            $year = date('Y');
        }
        
        $query = "SELECT MONTH(maintenance_date) as month, SUM(cost) as total_cost 
                  FROM " . $this->table_name . " 
                  WHERE YEAR(maintenance_date) = :year 
                  GROUP BY MONTH(maintenance_date) 
                  ORDER BY month";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':year', $year);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
