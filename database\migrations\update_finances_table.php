<?php
// Define application root directory
define('APP_ROOT', dirname(dirname(__DIR__)));

/**
 * <PERSON>ript to add member_id column to finances table
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once APP_ROOT . '/config/database.php';

echo "<h1>Updating Finances Table</h1>";

try {
    // Create database connection
    $db = new Database();
    $conn = $db->getConnection();
    
    // Check if member_id column already exists
    $query = "SHOW COLUMNS FROM finances LIKE 'member_id'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "<p>The member_id column already exists in the finances table.</p>";
    } else {
        // Add member_id column to finances table
        $query = "ALTER TABLE finances ADD COLUMN member_id INT AFTER transaction_date, 
                  ADD FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        
        echo "<p>Successfully added member_id column to finances table.</p>";
    }
    
    echo "<p>Database update completed successfully.</p>";
    echo "<p><a href='finance'>Go to Finance Page</a></p>";
    
} catch (PDOException $e) {
    echo "<h2>Error</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
