RewriteEngine On
RewriteBase /icgc/

# Redirect to index.php if the file or directory doesn't exist
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Prevent direct access to sensitive files
<FilesMatch "^\.">
    Order allow,deny
    <PERSON><PERSON> from all
</FilesMatch>

<FilesMatch "^(config|models|controllers|utils|database|scripts)">
    Order allow,deny
    Deny from all
</FilesMatch>

# PHP settings
php_flag display_errors off
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300
