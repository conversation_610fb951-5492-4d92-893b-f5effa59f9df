<?php
/**
 * Update SMS table to add 'partial' status
 */

// Include database configuration
require_once '../config/database.php';

// Create database connection
$database = new Database();
$conn = $database->getConnection();

// Set page title
echo "<h1>Update SMS Table</h1>";

try {
    // Check if the table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'sms_messages'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p>SMS table exists. Checking if update is needed...</p>";
        
        // Check if 'partial' status already exists
        $stmt = $conn->query("SHOW COLUMNS FROM `sms_messages` LIKE 'status'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($column && strpos($column['Type'], 'partial') === false) {
            echo "<p>Updating SMS table to add 'partial' status...</p>";
            
            // Alter the table to modify the status enum
            $sql = "ALTER TABLE `sms_messages` 
                    MODIFY COLUMN `status` ENUM('pending', 'sent', 'failed', 'partial') NOT NULL DEFAULT 'pending'";
            
            $conn->exec($sql);
            echo "<p style='color: green;'>✓ SMS table updated successfully!</p>";
        } else {
            echo "<p style='color: green;'>✓ SMS table already has 'partial' status. No update needed.</p>";
        }
    } else {
        // Check if 'sms' table exists (older name)
        $stmt = $conn->query("SHOW TABLES LIKE 'sms'");
        $oldTableExists = $stmt->rowCount() > 0;
        
        if ($oldTableExists) {
            echo "<p>Old SMS table exists. Renaming and updating...</p>";
            
            // Rename the table
            $sql = "RENAME TABLE `sms` TO `sms_messages`";
            $conn->exec($sql);
            
            // Update the status enum
            $sql = "ALTER TABLE `sms_messages` 
                    MODIFY COLUMN `status` ENUM('pending', 'sent', 'failed', 'partial') NOT NULL DEFAULT 'pending'";
            
            $conn->exec($sql);
            echo "<p style='color: green;'>✓ SMS table renamed and updated successfully!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ SMS table does not exist. Creating it now...</p>";
            
            // Create the SMS table
            $sql = "CREATE TABLE `sms_messages` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `message` TEXT NOT NULL,
                `recipients` TEXT NOT NULL,
                `sent_date` DATETIME NOT NULL,
                `status` ENUM('pending', 'sent', 'failed', 'partial') NOT NULL DEFAULT 'pending',
                `sent_by` INT,
                `created_at` DATETIME NOT NULL,
                `updated_at` DATETIME NOT NULL,
                FOREIGN KEY (`sent_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
            )";
            
            $conn->exec($sql);
            echo "<p style='color: green;'>✓ SMS table created successfully!</p>";
        }
    }
    
    echo "<p>SMS table update complete.</p>";
    echo "<p><a href='../index.php'>Return to Dashboard</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>
