<div class="container mx-auto max-w-6xl px-4">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-1">Equipment Inventory</h1>
            <p class="text-gray-600">Manage and track all church equipment and assets</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>equipment" class="bg-gray-500 hover:bg-gray-600 text-white py-2.5 px-4 rounded-md flex items-center shadow-sm transition duration-300 transform hover:scale-105">
                <i class="fas fa-arrow-left mr-2"></i> Back to Equipment
            </a>
            <a href="<?php echo BASE_URL; ?>equipment/add" class="bg-primary hover:bg-primary-dark text-white py-2.5 px-4 rounded-md flex items-center shadow-sm transition duration-300 transform hover:scale-105">
                <i class="fas fa-plus-circle mr-2"></i> Add Equipment
            </a>
        </div>
    </div>

    <!-- Equipment Inventory Table -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8 border border-gray-100 transition-all duration-300 hover:shadow-xl">
        <div class="p-4 bg-gradient-to-r from-gray-50 to-white border-b flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-clipboard-list text-primary mr-2"></i> Equipment Inventory
            </h2>
            <div class="flex space-x-3">
                <a href="javascript:void(0)" onclick="printInventory()" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md flex items-center text-sm shadow-sm transition duration-300 transform hover:scale-105">
                    <i class="fas fa-print mr-2"></i> Print Inventory
                </a>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gradient-to-r from-gray-50 to-white">
                    <tr>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Condition</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Date</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Price</th>
                        <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($equipments)) : ?>
                        <tr>
                            <td colspan="7" class="px-6 py-8 text-center text-sm text-gray-500">
                                <div class="flex flex-col items-center justify-center space-y-3">
                                    <i class="fas fa-clipboard-list text-gray-400 text-4xl"></i>
                                    <p>No equipment records found</p>
                                    <a href="<?php echo BASE_URL; ?>equipment/add" class="text-primary hover:text-primary-dark underline">Add your first equipment</a>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ($equipments as $equipment) : ?>
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo $equipment['name']; ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo ucfirst($equipment['category']); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($equipment['status'] === 'excellent') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Excellent</span>
                                    <?php elseif ($equipment['status'] === 'good') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Good</span>
                                    <?php elseif ($equipment['status'] === 'fair') : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Fair</span>
                                    <?php else : ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Poor</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $equipment['location'] ?? 'N/A'; ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $equipment['purchase_date'] ? format_date($equipment['purchase_date']) : 'N/A'; ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $equipment['purchase_price'] ? 'GH₵ ' . number_format($equipment['purchase_price'], 2) : 'N/A'; ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-3">
                                        <a href="<?php echo BASE_URL; ?>equipment/view?id=<?php echo $equipment['id']; ?>" class="text-blue-600 hover:text-blue-900 hover:scale-110 transition-transform duration-150" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>equipment/edit?id=<?php echo $equipment['id']; ?>" class="text-indigo-600 hover:text-indigo-900 hover:scale-110 transition-transform duration-150" title="Edit Equipment">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $equipment['id']; ?>)" class="text-red-600 hover:text-red-900 hover:scale-110 transition-transform duration-150" title="Delete Equipment">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    // Confirm delete with improved styling
    function confirmDelete(id) {
        // Create a custom confirmation dialog
        const overlay = document.createElement('div');
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

        const dialog = document.createElement('div');
        dialog.className = 'bg-white rounded-lg shadow-xl p-6 max-w-md mx-auto transform transition-all';
        dialog.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-5xl mb-4"></i>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Confirm Deletion</h3>
                <p class="text-gray-600 mb-6">Are you sure you want to delete this equipment? This action cannot be undone.</p>
                <div class="flex justify-center space-x-4">
                    <button id="cancel-btn" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md text-gray-800 font-medium transition-colors duration-150">Cancel</button>
                    <button id="confirm-btn" class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-md text-white font-medium transition-colors duration-150">Delete</button>
                </div>
            </div>
        `;

        overlay.appendChild(dialog);
        document.body.appendChild(overlay);

        // Add animation
        setTimeout(() => {
            dialog.classList.add('scale-100');
        }, 10);

        // Handle button clicks
        document.getElementById('cancel-btn').addEventListener('click', () => {
            document.body.removeChild(overlay);
        });

        document.getElementById('confirm-btn').addEventListener('click', () => {
            window.location.href = '<?php echo BASE_URL; ?>equipment/delete?id=' + id;
        });
    }

    // Print inventory with improved styling
    function printInventory() {
        const printContents = document.querySelector('.overflow-x-auto').innerHTML;
        const originalContents = document.body.innerHTML;

        document.body.innerHTML = `
            <div style="padding: 20px; font-family: Arial, sans-serif;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="font-size: 24px; color: #4CBF26; margin-bottom: 10px;">Equipment Inventory</h1>
                    <p style="color: #666; font-size: 14px;">
                        Generated on: <?php echo date('F j, Y, g:i a'); ?><br>
                        ICGC Emmanuel Temple
                    </p>
                </div>
                <div>
                    ${printContents}
                </div>
                <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #999;">
                    © <?php echo date('Y'); ?> ICGC Emmanuel Temple Church Management System
                </div>
            </div>
        `;

        window.print();
        document.body.innerHTML = originalContents;
    }

    // Add fade-in animation to the table when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const table = document.querySelector('.overflow-x-auto');
        if (table) {
            table.classList.add('animate-fade-in');
        }
    });
</script>

<style>
    .animate-fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
