<?php
/**
 * Show Category View
 * Display single category details
 */
?>

<div class="max-w-4xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center mb-6">
        <a href="<?php echo BASE_URL; ?>categories" class="text-gray-600 hover:text-gray-800 mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div class="flex-1">
            <h1 class="text-3xl font-bold text-gray-900">
                <?php echo htmlspecialchars($category['name']); ?>
            </h1>
            <p class="text-gray-600 mt-1">Category details and programs</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo BASE_URL; ?>categories/<?php echo $category['id']; ?>/edit" 
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-edit mr-2"></i>
                Edit
            </a>
        </div>
    </div>

    <!-- Category Details Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-start space-x-4">
            <!-- Category Icon -->
            <div class="w-16 h-16 rounded-full flex items-center justify-center"
                 style="background-color: <?php echo htmlspecialchars($category['color_code'] ?? '#6B7280'); ?>20;">
                <i class="<?php echo htmlspecialchars($category['icon'] ?? 'fas fa-folder'); ?> text-2xl"
                   style="color: <?php echo htmlspecialchars($category['color_code'] ?? '#6B7280'); ?>;"></i>
            </div>
            
            <!-- Category Info -->
            <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                    <h2 class="text-xl font-semibold text-gray-900">
                        <?php echo htmlspecialchars($category['name']); ?>
                    </h2>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        <?php echo $category['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                        <?php echo $category['is_active'] ? 'Active' : 'Inactive'; ?>
                    </span>
                </div>
                
                <?php if (!empty($category['description'])): ?>
                    <p class="text-gray-600 mb-4">
                        <?php echo htmlspecialchars($category['description']); ?>
                    </p>
                <?php endif; ?>
                
                <!-- Category Metadata -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
                    <div>
                        <span class="font-medium">Created:</span>
                        <?php echo date('M j, Y g:i A', strtotime($category['created_at'])); ?>
                    </div>
                    <div>
                        <span class="font-medium">Last Updated:</span>
                        <?php echo date('M j, Y g:i A', strtotime($category['updated_at'])); ?>
                    </div>
                    <div>
                        <span class="font-medium">Color:</span>
                        <span class="inline-block w-4 h-4 rounded ml-1" 
                              style="background-color: <?php echo htmlspecialchars($category['color_code'] ?? '#6B7280'); ?>;"></span>
                        <?php echo htmlspecialchars($category['color_code'] ?? '#6B7280'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Programs in this Category -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Programs in this Category</h3>
            <p class="text-sm text-gray-600 mt-1">All programs assigned to this category</p>
        </div>
        
        <div class="p-6">
            <?php if (!empty($programs)): ?>
                <div class="space-y-4">
                    <?php foreach ($programs as $program): ?>
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <h4 class="text-lg font-medium text-gray-900">
                                        <?php echo htmlspecialchars($program['title']); ?>
                                    </h4>
                                    <?php if (!empty($program['description'])): ?>
                                        <p class="text-gray-600 mt-1">
                                            <?php echo htmlspecialchars(substr($program['description'], 0, 150)); ?>
                                            <?php if (strlen($program['description']) > 150): ?>...<?php endif; ?>
                                        </p>
                                    <?php endif; ?>
                                    <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                        <span>
                                            <i class="fas fa-calendar mr-1"></i>
                                            <?php echo date('M j, Y', strtotime($program['start_date'])); ?>
                                        </span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            <?php 
                                            switch($program['status']) {
                                                case 'planned': echo 'bg-blue-100 text-blue-800'; break;
                                                case 'in_progress': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'completed': echo 'bg-green-100 text-green-800'; break;
                                                case 'cancelled': echo 'bg-red-100 text-red-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?php echo ucfirst($program['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <a href="<?php echo BASE_URL; ?>programs/<?php echo $program['id']; ?>" 
                                       class="text-emerald-600 hover:text-emerald-800 font-medium">
                                        View Program
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <!-- No Programs -->
                <div class="text-center py-8">
                    <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-list text-gray-400 text-xl"></i>
                    </div>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Programs Yet</h4>
                    <p class="text-gray-500 mb-4">This category doesn't have any programs assigned to it yet.</p>
                    <a href="<?php echo BASE_URL; ?>programs/create" 
                       class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Create Program
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
