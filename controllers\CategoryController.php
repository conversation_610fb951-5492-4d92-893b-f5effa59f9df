<?php
/**
 * Category Controller for Program Categories Management
 */

require_once 'models/ProgramCategory.php';
require_once 'utils/validation.php';
require_once 'controllers/BaseRestfulController.php';

class CategoryController extends BaseRestfulController {
    private $database;
    private $category;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $db = $this->database->getConnection();
        $this->category = new ProgramCategory($db);
    }

    /**
     * Display categories list
     */
    public function index() {
        $stmt = $this->category->getAll();
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $page_title = 'Program Categories - ICGC Emmanuel Temple';
        $active_page = 'programs';

        ob_start();
        require_once 'views/categories/index.php';
        $content = ob_get_clean();
        include 'views/layouts/main.php';
    }

    /**
     * Display create category form
     */
    public function create() {
        $page_title = 'Create Category - ICGC Emmanuel Temple';
        $active_page = 'programs';

        ob_start();
        require_once 'views/categories/create.php';
        $content = ob_get_clean();
        include 'views/layouts/main.php';
    }

    /**
     * Store new category
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('categories');
        }

        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            set_flash_message('Invalid security token. Please try again.', 'danger');
            redirect('categories/create');
        }

        $this->category->name = $_POST['name'] ?? '';
        $this->category->description = $_POST['description'] ?? '';
        $this->category->color_code = $_POST['color_code'] ?? '#3B82F6';
        $this->category->icon = $_POST['icon'] ?? 'fas fa-calendar';

        $errors = $this->category->validate();

        if (!empty($errors)) {
            set_flash_message('Please fix the following errors: ' . implode(', ', $errors), 'danger');
            redirect('categories/create');
        }

        if ($this->category->create()) {
            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Category created successfully!']);
                exit;
            }

            set_flash_message('Category created successfully!', 'success');
            redirect('categories');
        } else {
            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Error creating category. Please try again.']);
                exit;
            }

            set_flash_message('Error creating category. Please try again.', 'danger');
            redirect('categories/create');
        }
    }

    /**
     * Display edit category form
     */
    public function edit() {
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            set_flash_message('Category ID is required.', 'danger');
            redirect('categories');
        }

        $category_id = (int)$_GET['id'];
        $category = $this->category->getById($category_id);

        if (!$category) {
            set_flash_message('Category not found.', 'danger');
            redirect('categories');
        }

        $page_title = 'Edit Category: ' . $category['name'] . ' - ICGC Emmanuel Temple';
        $active_page = 'programs';

        ob_start();
        require_once 'views/categories/edit.php';
        $content = ob_get_clean();
        include 'views/layouts/main.php';
    }

    /**
     * Update category
     */
    public function update($id = null) {
        // Accept both POST (legacy) and PUT (RESTful) methods
        if (!$this->checkHttpMethod(['POST', 'PUT'])) {
            redirect('categories');
        }

        if (!$this->validateCsrf('categories')) {
            return;
        }

        // Get ID from multiple sources (supports both legacy and RESTful)
        $category_id = $this->getId($id, 'id', 'id');
        if (!$category_id) {
            $this->handleResponse(false, 'Category ID is required.', 'categories');
            return;
        }

        // Get the existing category to preserve is_active status
        $existingCategory = $this->category->getById($category_id);
        if (!$existingCategory) {
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Category not found.']);
                exit;
            }
            set_flash_message('Category not found.', 'danger');
            redirect('categories');
        }

        $this->category->id = $category_id;
        $this->category->name = $_POST['name'] ?? '';
        $this->category->description = $_POST['description'] ?? '';
        $this->category->color_code = $_POST['color_code'] ?? '#3B82F6';
        $this->category->icon = $_POST['icon'] ?? 'fas fa-calendar';
        // Preserve the existing is_active status
        $this->category->is_active = $existingCategory['is_active'] ?? 1;

        $errors = $this->category->validate();

        if (!empty($errors)) {
            set_flash_message('Please fix the following errors: ' . implode(', ', $errors), 'danger');
            redirect('categories/edit?id=' . $category_id);
        }

        // Attempt update and handle response using base class method
        if ($this->category->update()) {
            $this->handleResponse(true, 'Category updated successfully!', 'categories');
        } else {
            $this->handleResponse(false, 'Error updating category. Please try again.', 'categories/edit?id=' . $category_id);
        }
    }

    /**
     * Delete category
     */
    public function delete($id = null) {
        // Accept both POST (legacy) and DELETE (RESTful) methods
        if (!$this->checkHttpMethod(['POST', 'DELETE'])) {
            redirect('categories');
        }

        if (!$this->validateCsrf('categories')) {
            return;
        }

        // Get ID from multiple sources (supports both legacy and RESTful)
        $category_id = $this->getId($id, 'id', 'id');
        if (!$category_id) {
            $this->handleResponse(false, 'Category ID is required.', 'categories');
            return;
        }

        $this->category->id = $category_id;

        if ($this->category->delete()) {
            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Category deleted successfully!']);
                exit;
            }

            set_flash_message('Category deleted successfully!', 'success');
        } else {
            // Get error message from model
            $errorMessage = $this->category->error ?? 'Error deleting category. Please try again.';

            // Check if this is an AJAX request
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $errorMessage]);
                exit;
            }

            set_flash_message($errorMessage, 'danger');
        }

        redirect('categories');
    }

    /**
     * Toggle category status
     */
    public function toggleStatus() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('categories');
        }

        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            set_flash_message('Invalid security token. Please try again.', 'danger');
            redirect('categories');
        }

        $category_id = (int)($_POST['category_id'] ?? 0);
        $new_status = (int)($_POST['status'] ?? 0);

        if (empty($category_id)) {
            set_flash_message('Category ID is required.', 'danger');
            redirect('categories');
        }

        $this->category->id = $category_id;

        if ($this->category->toggleStatus($new_status)) {
            $status_text = $new_status ? 'activated' : 'deactivated';
            set_flash_message('Category ' . $status_text . ' successfully!', 'success');
        } else {
            set_flash_message('Error updating category status. Please try again.', 'danger');
        }

        redirect('categories');
    }

    /**
     * AJAX endpoint to get categories
     */
    public function getCategories() {
        header('Content-Type: application/json');
        
        $stmt = $this->category->getActive();
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode($categories);
        exit;
    }
}
