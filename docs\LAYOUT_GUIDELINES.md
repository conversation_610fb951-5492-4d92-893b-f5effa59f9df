# Layout Guidelines - ICGC Church Management System

## 🚨 IMPORTANT: Preventing the "Two-Column Layout" Issue

### The Problem
The "two-column layout" issue occurs when there's inconsistent container usage between the header and main content areas. This was caused by:

1. **Header using**: `container mx-auto px-4` (centered container)
2. **Main content using**: `p-6` (full-width padding)
3. **Page content using**: `container mx-auto px-4` (another centered container)

This created a visual mismatch that appeared as a two-column layout.

### The Solution
**FIXED**: The main layout header now uses consistent padding (`px-6`) instead of `container mx-auto px-4`.

**RULE**: Never use `container mx-auto px-4` in page content files.

## ✅ Correct Layout Structure

### Main Layout (views/layouts/main.php)
The main layout already provides:
```html
<main class="flex-1 p-6 overflow-y-auto">
    <?php echo $content; ?>
</main>
```

### Page Content Files
For page content, use one of these patterns:

#### 1. Standard Full-Width Content
```html
<!-- ✅ CORRECT -->
<div class="fade-in">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Page Title</h1>
        <!-- Page content -->
    </div>
</div>
```

#### 2. Centered Content with Max Width
```html
<!-- ✅ CORRECT -->
<div class="fade-in">
    <div class="page-content-centered">
        <h1 class="text-2xl font-bold text-gray-800">Page Title</h1>
        <!-- Page content -->
    </div>
</div>
```

#### 3. Narrow Content (Forms, etc.)
```html
<!-- ✅ CORRECT -->
<div class="fade-in">
    <div class="page-content-narrow">
        <h1 class="text-2xl font-bold text-gray-800">Form Title</h1>
        <!-- Form content -->
    </div>
</div>
```

## ❌ What NOT to Do

### Incorrect Layout
```html
<!-- ❌ WRONG - This causes the two-column issue -->
<div class="container mx-auto px-4 fade-in">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Page Title</h1>
        <!-- This will create double containers -->
    </div>
</div>
```

## 🎨 Available CSS Helper Classes

### Defined in assets/css/style.css:

1. **`.page-content`** - Full-width content within layout
2. **`.page-content-centered`** - Centered content with max-width: 1200px
3. **`.page-content-narrow`** - Narrow content with max-width: 800px

## 📋 Checklist for New Pages

When creating new pages:

- [ ] ✅ Use `<div class="fade-in">` as the main wrapper
- [ ] ✅ Choose appropriate content width class if needed
- [ ] ❌ **NEVER** use `container mx-auto px-4` in page content
- [ ] ✅ Test the page to ensure single-column layout
- [ ] ✅ Check responsive behavior on mobile devices

## 🔧 Quick Fix for Existing Pages

If you encounter the two-column issue:

1. **Find the problematic div:**
   ```html
   <div class="container mx-auto px-4 fade-in">
   ```

2. **Replace with:**
   ```html
   <div class="fade-in">
   ```

3. **Test the page** to confirm the fix

## 📱 Responsive Considerations

The main layout already handles:
- Sidebar collapse on mobile
- Proper spacing adjustments
- Content overflow handling

Your page content should focus on:
- Content-specific responsive behavior
- Form field layouts
- Table responsiveness
- Card grid layouts

## 🎯 Examples of Correct Implementation

### Finance Add Page (Fixed)
```html
<div class="fade-in">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Add Financial Transaction</h1>
        <a href="<?php echo BASE_URL; ?>finance" class="...">Back to Finance</a>
    </div>
    <!-- Rest of content -->
</div>
```

### Dashboard Page
```html
<div class="fade-in">
    <div class="page-content-centered">
        <!-- Dashboard widgets and content -->
    </div>
</div>
```

### Settings Form
```html
<div class="fade-in">
    <div class="page-content-narrow">
        <!-- Form content -->
    </div>
</div>
```

## 🚀 Benefits of Following These Guidelines

1. **Consistent Layout**: All pages have uniform spacing and behavior
2. **No Layout Issues**: Prevents the recurring two-column problem
3. **Responsive Design**: Proper mobile and desktop experience
4. **Maintainable Code**: Clear structure for future developers
5. **Professional Appearance**: Clean, modern layout throughout

---

**Remember**: The main layout handles the container logic. Your page content should focus on the actual content, not container management.
