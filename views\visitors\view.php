<div class="container mx-auto max-w-6xl">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-1">Visitor Details</h1>
            <p class="text-sm text-gray-600">
                <i class="fas fa-calendar-alt mr-1"></i> First visit: <?php echo date('F j, Y', strtotime($this->visitor->first_visit_date)); ?>
            </p>
        </div>
        <div class="flex space-x-2">
            <a href="<?php echo url('visitors'); ?>" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Visitors
            </a>
            <a href="<?php echo url('visitors/' . $this->visitor->id . '/edit'); ?>" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md flex items-center">
                <i class="fas fa-edit mr-2"></i> Edit
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Visitor Information -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b bg-gray-50">
                    <h2 class="text-lg font-semibold text-gray-800">Visitor Information</h2>
                </div>
                <div class="p-6">
                    <div class="flex flex-col md:flex-row md:items-center mb-6">
                        <div class="flex-shrink-0 h-24 w-24 bg-gray-200 rounded-full flex items-center justify-center mb-4 md:mb-0 md:mr-6">
                            <span class="text-gray-500 text-2xl font-medium"><?php echo substr($this->visitor->first_name, 0, 1) . substr($this->visitor->last_name, 0, 1); ?></span>
                        </div>
                        <div>
                            <h3 class="text-xl font-medium text-gray-900"><?php echo $this->visitor->first_name . ' ' . $this->visitor->last_name; ?></h3>
                            <div class="mt-1 flex items-center">
                                <?php
                                $status_class = '';
                                $status_bg = '';
                                switch ($this->visitor->visitor_status) {
                                    case 'new':
                                        $status_class = 'text-blue-800';
                                        $status_bg = 'bg-blue-100';
                                        break;
                                    case 'in_follow_up':
                                        $status_class = 'text-yellow-800';
                                        $status_bg = 'bg-yellow-100';
                                        break;
                                    case 'converted':
                                        $status_class = 'text-green-800';
                                        $status_bg = 'bg-green-100';
                                        break;
                                    case 'inactive':
                                        $status_class = 'text-gray-800';
                                        $status_bg = 'bg-gray-100';
                                        break;
                                }
                                ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_bg . ' ' . $status_class; ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $this->visitor->visitor_status)); ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Contact Information</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <?php echo $this->visitor->phone_number; ?>
                                    </div>
                                </div>
                                
                                <?php if (!empty($this->visitor->email)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <?php echo $this->visitor->email; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($this->visitor->address)): ?>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <?php echo $this->visitor->address; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Visit Information</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <span class="font-medium">First Visit:</span> <?php echo date('F j, Y', strtotime($this->visitor->first_visit_date)); ?>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <span class="font-medium">Heard From:</span> <?php echo ucfirst(str_replace('_', ' ', $this->visitor->how_did_they_hear)); ?>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-5 w-5 text-gray-400">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="ml-3 text-sm text-gray-900">
                                        <span class="font-medium">Registered:</span> <?php echo date('F j, Y', strtotime($this->visitor->created_at)); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (!empty($this->visitor->notes)): ?>
                    <div class="mt-6">
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Notes</h4>
                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700">
                            <?php echo nl2br($this->visitor->notes); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($is_converted): ?>
                    <div class="mt-6 bg-green-50 p-4 rounded-md border border-green-200">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-check text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-green-800">Converted to Member</h4>
                                <p class="text-sm text-green-700">
                                    Converted on <?php echo date('F j, Y', strtotime($conversion_details['conversion_date'])); ?> as 
                                    <a href="<?php echo url('members/' . $conversion_details['member_id']); ?>" class="font-medium underline">
                                        <?php echo $conversion_details['member_name']; ?>
                                    </a>
                                </p>
                            </div>
                        </div>
                        <?php if (!empty($conversion_details['notes'])): ?>
                        <div class="mt-2 pl-14">
                            <p class="text-sm text-green-700"><?php echo nl2br($conversion_details['notes']); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mt-6 flex justify-end space-x-2">
                        <?php if (!$is_converted): ?>
                        <a href="<?php echo url('visitors/' . $this->visitor->id . '/convert'); ?>" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md flex items-center">
                            <i class="fas fa-user-check mr-2"></i> Convert to Member
                        </a>
                        <?php endif; ?>
                        
                        <a href="<?php echo url('visitors/' . $this->visitor->id . '/follow-up'); ?>" class="bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-md flex items-center">
                            <i class="fas fa-phone mr-2"></i> Add Follow-up
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Follow-up History -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden mt-6">
                <div class="p-4 border-b bg-gray-50 flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-800">Follow-up History</h2>
                    <a href="<?php echo url('visitors/' . $this->visitor->id . '/follow-up'); ?>" class="text-primary hover:text-primary-dark text-sm">
                        <i class="fas fa-plus-circle mr-1"></i> Add New
                    </a>
                </div>
                <div class="p-4">
                    <?php if (count($followups) > 0): ?>
                        <div class="space-y-4">
                            <?php foreach ($followups as $followup): ?>
                                <div class="border-l-4 border-yellow-500 pl-4 py-3">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="flex items-center">
                                                <span class="text-sm font-medium text-gray-900">
                                                    <?php echo ucfirst(str_replace('_', ' ', $followup['follow_up_type'])); ?> Follow-up
                                                </span>
                                                <span class="ml-2 text-xs text-gray-500">
                                                    <?php echo date('M d, Y', strtotime($followup['follow_up_date'])); ?>
                                                </span>
                                            </div>
                                            <?php if (!empty($followup['notes'])): ?>
                                                <p class="text-sm text-gray-700 mt-1"><?php echo nl2br($followup['notes']); ?></p>
                                            <?php endif; ?>
                                            <?php if (!empty($followup['username'])): ?>
                                                <p class="text-xs text-gray-500 mt-2">Conducted by: <?php echo $followup['username']; ?></p>
                                            <?php endif; ?>
                                        </div>
                                        <a href="javascript:void(0)" onclick="confirmDeleteFollowUp(<?php echo $followup['id']; ?>, <?php echo $this->visitor->id; ?>)" class="text-red-500 hover:text-red-700">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-6 text-gray-500">
                            <i class="fas fa-phone-slash text-3xl mb-2"></i>
                            <p>No follow-ups recorded yet</p>
                            <a href="<?php echo url('visitors/' . $this->visitor->id . '/follow-up'); ?>" class="text-primary hover:text-primary-dark mt-2 inline-block">
                                <i class="fas fa-plus-circle mr-1"></i> Add First Follow-up
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b bg-gray-50">
                    <h2 class="text-lg font-semibold text-gray-800">Quick Actions</h2>
                </div>
                <div class="p-4">
                    <div class="space-y-3">
                        <a href="<?php echo url('visitors/' . $this->visitor->id . '/edit'); ?>" class="flex items-center p-3 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors">
                            <div class="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-edit text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-blue-900">Edit Visitor</h4>
                                <p class="text-xs text-blue-700">Update visitor information</p>
                            </div>
                        </a>
                        
                        <a href="<?php echo url('visitors/' . $this->visitor->id . '/follow-up'); ?>" class="flex items-center p-3 bg-yellow-50 hover:bg-yellow-100 rounded-md transition-colors">
                            <div class="flex-shrink-0 h-10 w-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-phone text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-yellow-900">Add Follow-up</h4>
                                <p class="text-xs text-yellow-700">Record a follow-up interaction</p>
                            </div>
                        </a>
                        
                        <?php if (!$is_converted): ?>
                        <a href="<?php echo url('visitors/' . $this->visitor->id . '/convert'); ?>" class="flex items-center p-3 bg-green-50 hover:bg-green-100 rounded-md transition-colors">
                            <div class="flex-shrink-0 h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-check text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-green-900">Convert to Member</h4>
                                <p class="text-xs text-green-700">Register as a church member</p>
                            </div>
                        </a>
                        <?php endif; ?>
                        
                        <a href="<?php echo BASE_URL; ?>sms/create?phone=<?php echo urlencode($this->visitor->phone_number); ?>" class="flex items-center p-3 bg-purple-50 hover:bg-purple-100 rounded-md transition-colors">
                            <div class="flex-shrink-0 h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-sms text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-purple-900">Send SMS</h4>
                                <p class="text-xs text-purple-700">Send a text message to this visitor</p>
                            </div>
                        </a>
                        
                        <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $this->visitor->id; ?>)" class="flex items-center p-3 bg-red-50 hover:bg-red-100 rounded-md transition-colors">
                            <div class="flex-shrink-0 h-10 w-10 bg-red-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-trash text-red-600"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-sm font-medium text-red-900">Delete Visitor</h4>
                                <p class="text-xs text-red-700">Permanently remove this visitor</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
        <p class="text-gray-700 mb-4">Are you sure you want to delete this visitor? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button id="cancel-delete" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">Cancel</button>
            <a id="confirm-delete" href="" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">Delete</a>
        </div>
    </div>
</div>

<!-- Delete Follow-up Confirmation Modal -->
<div id="delete-followup-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
        <p class="text-gray-700 mb-4">Are you sure you want to delete this follow-up record? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button id="cancel-delete-followup" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">Cancel</button>
            <a id="confirm-delete-followup" href="" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">Delete</a>
        </div>
    </div>
</div>

<script>
    // Delete visitor confirmation
    function confirmDelete(id) {
        const modal = document.getElementById('delete-modal');
        const confirmBtn = document.getElementById('confirm-delete');
        
        modal.classList.remove('hidden');
        confirmBtn.href = '<?php echo url('visitors/'); ?>' + id + '/delete';

        document.getElementById('cancel-delete').addEventListener('click', function() {
            modal.classList.add('hidden');
        });
    }

    // Delete follow-up confirmation
    function confirmDeleteFollowUp(id, visitorId) {
        const modal = document.getElementById('delete-followup-modal');
        const confirmBtn = document.getElementById('confirm-delete-followup');

        modal.classList.remove('hidden');
        confirmBtn.href = '<?php echo url('visitors/'); ?>' + visitorId + '/follow-up/' + id + '/delete';
        
        document.getElementById('cancel-delete-followup').addEventListener('click', function() {
            modal.classList.add('hidden');
        });
    }
</script>
