<?php
/**
 * Security Service
 * 
 * Handles security-related operations including input sanitization,
 * CSRF protection, and security validation.
 * 
 * @package Services
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

class SecurityService
{
    /**
     * @var array Allowed HTML tags for rich text fields
     */
    private $allowedTags = '<p><br><strong><em><u><ol><ul><li>';

    /**
     * Sanitize category data
     * 
     * @param array $data Raw category data
     * @return array Sanitized category data
     */
    public function sanitizeCategoryData(array $data): array
    {
        $sanitized = [];
        
        // Sanitize string fields
        $stringFields = ['name', 'label', 'description', 'slug', 'icon', 'dashboard_route'];
        foreach ($stringFields as $field) {
            if (isset($data[$field])) {
                $sanitized[$field] = $this->sanitizeString($data[$field]);
            }
        }
        
        // Sanitize category type (whitelist)
        if (isset($data['category_type'])) {
            $allowedTypes = ['member_payments', 'general_income', 'expenses'];
            if (in_array($data['category_type'], $allowedTypes)) {
                $sanitized['category_type'] = $data['category_type'];
            }
        }
        
        // Sanitize boolean fields
        $booleanFields = ['is_active', 'requires_member', 'is_core'];
        foreach ($booleanFields as $field) {
            if (isset($data[$field])) {
                $sanitized[$field] = $this->sanitizeBoolean($data[$field]);
            }
        }
        
        return $sanitized;
    }

    /**
     * Sanitize transaction data
     * 
     * @param array $data Raw transaction data
     * @return array Sanitized transaction data
     */
    public function sanitizeTransactionData(array $data): array
    {
        $sanitized = [];
        
        // Sanitize amount
        if (isset($data['amount'])) {
            $sanitized['amount'] = $this->sanitizeAmount($data['amount']);
        }
        
        // Sanitize string fields
        $stringFields = ['category', 'description', 'member_name', 'reference'];
        foreach ($stringFields as $field) {
            if (isset($data[$field])) {
                $sanitized[$field] = $this->sanitizeString($data[$field]);
            }
        }
        
        // Sanitize payment method (whitelist)
        if (isset($data['payment_method'])) {
            $allowedMethods = ['cash', 'check', 'bank_transfer', 'mobile_money', 'card', 'other'];
            if (in_array($data['payment_method'], $allowedMethods)) {
                $sanitized['payment_method'] = $data['payment_method'];
            }
        }
        
        // Sanitize date
        if (isset($data['transaction_date'])) {
            $sanitized['transaction_date'] = $this->sanitizeDate($data['transaction_date']);
        }
        
        return $sanitized;
    }

    /**
     * Sanitize string input
     * 
     * @param string $input Raw string input
     * @return string Sanitized string
     */
    public function sanitizeString(string $input): string
    {
        // Remove any null bytes
        $input = str_replace("\0", '', $input);
        
        // Trim whitespace
        $input = trim($input);
        
        // Convert special characters to HTML entities
        return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }

    /**
     * Sanitize rich text input (allows some HTML tags)
     * 
     * @param string $input Raw HTML input
     * @return string Sanitized HTML
     */
    public function sanitizeRichText(string $input): string
    {
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Strip tags except allowed ones
        $input = strip_tags($input, $this->allowedTags);
        
        // Trim whitespace
        return trim($input);
    }

    /**
     * Sanitize numeric amount
     * 
     * @param mixed $input Raw amount input
     * @return float Sanitized amount
     */
    public function sanitizeAmount($input): float
    {
        // Remove any non-numeric characters except decimal point
        $cleaned = preg_replace('/[^0-9.]/', '', (string)$input);
        
        // Convert to float and round to 2 decimal places
        return round((float)$cleaned, 2);
    }

    /**
     * Sanitize boolean input
     * 
     * @param mixed $input Raw boolean input
     * @return bool Sanitized boolean
     */
    public function sanitizeBoolean($input): bool
    {
        if (is_bool($input)) {
            return $input;
        }
        
        if (is_string($input)) {
            $input = strtolower(trim($input));
            return in_array($input, ['1', 'true', 'yes', 'on']);
        }
        
        return (bool)$input;
    }

    /**
     * Sanitize date input
     * 
     * @param string $input Raw date input
     * @return string Sanitized date in Y-m-d format
     */
    public function sanitizeDate(string $input): string
    {
        // Try to parse the date
        $date = DateTime::createFromFormat('Y-m-d', trim($input));
        
        if ($date && $date->format('Y-m-d') === trim($input)) {
            return $date->format('Y-m-d');
        }
        
        // If parsing fails, try other common formats
        $formats = ['m/d/Y', 'd/m/Y', 'Y-m-d H:i:s', 'm-d-Y'];
        foreach ($formats as $format) {
            $date = DateTime::createFromFormat($format, trim($input));
            if ($date) {
                return $date->format('Y-m-d');
            }
        }
        
        // If all parsing fails, return current date
        return date('Y-m-d');
    }

    /**
     * Generate CSRF token
     * 
     * @return string CSRF token
     */
    public function generateCSRFToken(): string
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }

    /**
     * Validate CSRF token
     * 
     * @param string $token Token to validate
     * @return bool Whether token is valid
     */
    public function validateCSRFToken(string $token): bool
    {
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * Generate secure random string
     * 
     * @param int $length Length of random string
     * @return string Random string
     */
    public function generateRandomString(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Hash password securely
     * 
     * @param string $password Plain text password
     * @return string Hashed password
     */
    public function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ]);
    }

    /**
     * Verify password against hash
     * 
     * @param string $password Plain text password
     * @param string $hash Hashed password
     * @return bool Whether password is correct
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Escape output for HTML context
     * 
     * @param string $output Raw output
     * @return string Escaped output
     */
    public function escapeHtml(string $output): string
    {
        return htmlspecialchars($output, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }

    /**
     * Escape output for JavaScript context
     * 
     * @param string $output Raw output
     * @return string Escaped output
     */
    public function escapeJs(string $output): string
    {
        return json_encode($output, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
    }

    /**
     * Escape output for URL context
     * 
     * @param string $output Raw output
     * @return string Escaped output
     */
    public function escapeUrl(string $output): string
    {
        return urlencode($output);
    }

    /**
     * Validate file upload
     * 
     * @param array $file $_FILES array element
     * @param array $allowedTypes Allowed MIME types
     * @param int $maxSize Maximum file size in bytes
     * @return bool Whether file is valid
     */
    public function validateFileUpload(array $file, array $allowedTypes = [], int $maxSize = 5242880): bool
    {
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return false;
        }
        
        // Check file size
        if ($file['size'] > $maxSize) {
            return false;
        }
        
        // Check MIME type if specified
        if (!empty($allowedTypes)) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);
            
            if (!in_array($mimeType, $allowedTypes)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Rate limiting check
     * 
     * @param string $identifier Unique identifier (IP, user ID, etc.)
     * @param int $maxAttempts Maximum attempts allowed
     * @param int $timeWindow Time window in seconds
     * @return bool Whether request is allowed
     */
    public function checkRateLimit(string $identifier, int $maxAttempts = 10, int $timeWindow = 3600): bool
    {
        $key = 'rate_limit_' . md5($identifier);
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = [
                'attempts' => 1,
                'reset_time' => time() + $timeWindow
            ];
            return true;
        }
        
        $data = $_SESSION[$key];
        
        // Reset if time window has passed
        if (time() > $data['reset_time']) {
            $_SESSION[$key] = [
                'attempts' => 1,
                'reset_time' => time() + $timeWindow
            ];
            return true;
        }
        
        // Check if limit exceeded
        if ($data['attempts'] >= $maxAttempts) {
            return false;
        }
        
        // Increment attempts
        $_SESSION[$key]['attempts']++;
        return true;
    }

    /**
     * Log security event
     * 
     * @param string $event Event description
     * @param array $context Additional context
     * @return void
     */
    public function logSecurityEvent(string $event, array $context = []): void
    {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'user_id' => $_SESSION['user_id'] ?? null,
            'context' => $context
        ];
        
        // Log to file (you might want to use a proper logging library)
        error_log('SECURITY: ' . json_encode($logEntry));
    }
}
