<div class="container mx-auto px-4 py-6 max-w-4xl">
    <!-- Header with Icon -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div class="mb-4 md:mb-0">
            <h1 class="text-3xl font-bold text-gray-800 mb-1">Welfare History: <?php echo $member->first_name . ' ' . $member->last_name; ?></h1>
            <p class="text-gray-600">View all welfare transactions for this member</p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <a href="<?php echo BASE_URL; ?>finance/welfare" class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 py-2 px-4 rounded-lg flex items-center justify-center shadow-sm hover:shadow transition-all duration-300">
                <i class="fas fa-arrow-left mr-2 text-primary text-sm"></i> Back to Welfare Tracking
            </a>
            <a href="<?php echo BASE_URL; ?>finance/add" class="bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white py-2 px-4 rounded-lg flex items-center justify-center shadow-sm hover:shadow transition-all duration-300">
                <i class="fas fa-plus-circle mr-2 text-sm"></i> Add Welfare Payment
            </a>
        </div>
    </div>

    <!-- Member Info Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 mb-8">
        <div class="md:flex">
            <div class="md:flex-shrink-0 p-5 bg-white flex items-center justify-center border-r border-gray-100">
                <div class="text-center">
                    <div class="h-24 w-24 mx-auto flex items-center justify-center relative">
                        <div class="absolute inset-0 rounded-full border-2 border-dotted border-gray-300 animate-pulse-slow"></div>
                        <div class="h-20 w-20 rounded-full overflow-hidden border-3 border-white shadow-md z-10">
                            <?php if (!empty($member->profile_picture) && file_exists($member->profile_picture)): ?>
                                <img src="<?php echo BASE_URL . $member->profile_picture; ?>" alt="<?php echo $member->first_name . ' ' . $member->last_name; ?>" class="w-full h-full object-cover">
                            <?php else: ?>
                                <div class="w-full h-full flex items-center justify-center bg-gray-100">
                                    <span class="text-primary text-2xl font-bold"><?php echo substr($member->first_name, 0, 1) . substr($member->last_name, 0, 1); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <h3 class="mt-3 text-lg font-bold text-gray-800"><?php echo $member->first_name . ' ' . $member->last_name; ?></h3>
                    <p class="text-gray-500 text-xs"><?php echo ucfirst($member->member_status); ?> Member</p>
                </div>
            </div>
            <div class="p-5 md:p-6 flex-1">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <h4 class="text-xs font-medium text-gray-500 mb-1">Contact Information</h4>
                        <p class="text-gray-800 text-sm mb-1"><i class="fas fa-phone-alt text-primary mr-2 text-xs"></i> <?php echo $member->phone_number; ?></p>
                        <p class="text-gray-800 text-sm"><i class="fas fa-envelope text-primary mr-2 text-xs"></i> <?php echo $member->email ?: 'Not provided'; ?></p>
                    </div>
                    <div>
                        <h4 class="text-xs font-medium text-gray-500 mb-1">Membership Details</h4>
                        <p class="text-gray-800 text-sm mb-1"><i class="fas fa-calendar-alt text-primary mr-2 text-xs"></i> Member since <?php echo format_date($member->membership_date); ?></p>
                        <p class="text-gray-800 text-sm"><i class="fas fa-users text-primary mr-2 text-xs"></i> <?php echo ucfirst($member->department); ?> Department</p>
                    </div>
                    <div>
                        <h4 class="text-xs font-medium text-gray-500 mb-1">Welfare Summary</h4>
                        <p class="text-gray-800 text-sm mb-1"><i class="fas fa-money-bill-wave text-primary mr-2 text-xs"></i> Total: GH₵ <?php echo number_format($total_welfare, 2); ?></p>
                        <p class="text-gray-800 text-sm"><i class="fas fa-calendar-check text-primary mr-2 text-xs"></i> <?php echo count($welfare_payments); ?> Transactions</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Welfare Transactions Table -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 mb-8">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center">
                <div class="p-2.5 rounded-full bg-gray-100 text-primary mr-3 shadow-sm border border-gray-200">
                    <i class="fas fa-history text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-800">Welfare Transaction History</h3>
                    <p class="text-sm text-gray-500 mt-1">All welfare transactions for <?php echo $member->first_name . ' ' . $member->last_name; ?></p>
                </div>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recorded By</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($welfare_payments)) : ?>
                        <tr>
                            <td colspan="5" class="px-6 py-10 text-center">
                                <div class="flex flex-col items-center justify-center text-gray-500 py-6">
                                    <p class="text-lg font-bold text-gray-700 mb-2">No Welfare Records Found</p>
                                    <p class="text-gray-500 max-w-md mb-5 text-sm">There are no welfare records for this member yet. You can add a new welfare record using the "Add Welfare Payment" button.</p>
                                    <a href="<?php echo BASE_URL; ?>finance/add" class="bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white py-2 px-4 rounded-lg flex items-center justify-center shadow-sm hover:shadow transition-all duration-300 text-sm">
                                        <i class="fas fa-plus-circle mr-2 text-sm"></i> Add Welfare Record
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php else : ?>
                        <?php $rowIndex = 0; ?>
                        <?php foreach ($welfare_payments as $payment) : ?>
                            <?php $rowIndex++; ?>
                            <tr class="<?php echo $rowIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'; ?> hover:bg-blue-50 transition-all duration-200 group">
                                <!-- Date Column -->
                                <td class="px-6 py-3 whitespace-nowrap">
                                    <div class="text-sm font-semibold text-gray-900"><?php echo format_date($payment['transaction_date']); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($payment['created_at'])); ?></div>
                                </td>

                                <!-- Amount Column -->
                                <td class="px-6 py-3 whitespace-nowrap">
                                    <div class="text-sm font-semibold text-green-600">GH₵ <?php echo number_format($payment['amount'], 2); ?></div>
                                </td>

                                <!-- Description Column -->
                                <td class="px-6 py-3">
                                    <div class="text-sm text-gray-900"><?php echo $payment['description'] ?: 'No description provided'; ?></div>
                                </td>

                                <!-- Recorded By Column -->
                                <td class="px-6 py-3 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo $payment['recorded_by_name'] ?: 'System'; ?></div>
                                </td>

                                <!-- Actions Column -->
                                <td class="px-6 py-3 whitespace-nowrap text-center">
                                    <div class="flex items-center justify-center space-x-2">
                                        <a href="<?php echo BASE_URL; ?>finance/edit?id=<?php echo $payment['id']; ?>" class="bg-gray-50 hover:bg-gray-100 text-primary p-1.5 rounded-lg transition-all duration-200 border border-gray-200 hover:border-primary" title="Edit Transaction">
                                            <i class="fas fa-edit text-sm"></i>
                                        </a>
                                        <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $payment['id']; ?>)" class="bg-gray-50 hover:bg-red-50 text-gray-500 hover:text-red-500 p-1.5 rounded-lg transition-all duration-200 border border-gray-200 hover:border-red-300" title="Delete Transaction">
                                            <i class="fas fa-trash-alt text-sm"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden opacity-0 transition-opacity duration-300">
    <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-md w-full transform scale-95 transition-transform duration-300">
        <div class="p-6">
            <div class="flex items-center justify-center mb-4 text-red-500">
                <i class="fas fa-exclamation-triangle text-5xl"></i>
            </div>
            <h3 class="text-xl font-bold text-center text-gray-900 mb-2">Confirm Deletion</h3>
            <p class="text-gray-600 text-center mb-6">Are you sure you want to delete this welfare record? This action cannot be undone.</p>
            <div class="flex justify-center space-x-4">
                <button id="cancelDelete" class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-200">
                    Cancel
                </button>
                <a id="confirmDelete" href="#" class="px-4 py-2 bg-white border-2 border-red-500 text-red-500 hover:bg-red-500 hover:text-white rounded-lg transition-all duration-200">
                    Delete
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Delete confirmation functionality
    window.confirmDelete = function(id) {
        const modal = document.getElementById('deleteModal');
        const modalContent = document.getElementById('modalContent');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('cancelDelete');

        modal.classList.remove('hidden');
        setTimeout(() => {
            modal.classList.add('opacity-100');
            modalContent.classList.add('scale-100');
        }, 10);

        confirmBtn.href = '<?php echo BASE_URL; ?>finance/delete?id=' + id + '&redirect=memberWelfare?id=<?php echo $member_id; ?>';

        const closeModal = function() {
            modal.classList.remove('opacity-100');
            modalContent.classList.remove('scale-100');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        };

        cancelBtn.addEventListener('click', closeModal);
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
    };
</script>

<style>
    @keyframes pulse-slow {
        0%, 100% {
            opacity: 0.5;
            transform: scale(1);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.05);
        }
    }
    .animate-pulse-slow {
        animation: pulse-slow 3s ease-in-out infinite;
    }
</style>
