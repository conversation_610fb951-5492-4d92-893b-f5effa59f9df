<?php
/**
 * Enhanced Database Creation Script for ICGC <PERSON>
 * 
 * This script creates the icgc_db database and imports the schema
 * with improved error handling and verification
 */

// Default database connection parameters
$host = 'localhost';
$username = 'root';
$password = '';
$db_name = 'icgc_db';

// Override with POST parameters if provided
if (isset($_POST['db_host']) && !empty($_POST['db_host'])) {
    $host = $_POST['db_host'];
}
if (isset($_POST['db_username']) && !empty($_POST['db_username'])) {
    $username = $_POST['db_username'];
}
if (isset($_POST['db_password'])) {
    $password = $_POST['db_password'];
}
if (isset($_POST['db_name']) && !empty($_POST['db_name'])) {
    $db_name = $_POST['db_name'];
}

// Initialize results array
$results = [
    'success' => false,
    'messages' => [],
    'errors' => []
];

try {
    // Connect to MySQL server without specifying a database
    $conn = new PDO("mysql:host=$host", $username, $password);
    
    // Set the PDO error mode to exception
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database
    $sql = "CREATE DATABASE IF NOT EXISTS `$db_name`";
    $conn->exec($sql);
    $results['messages'][] = "Database '$db_name' created successfully or already exists.";
    
    // Connect to the newly created database
    $conn = new PDO("mysql:host=$host;dbname=$db_name", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read the schema file
    $schemaFile = file_get_contents(__DIR__ . '/schema.sql');
    if (!$schemaFile) {
        throw new Exception("Could not read schema file. Please check that 'database/schema.sql' exists and is readable.");
    }
    
    // Split the schema file into individual statements
    $statements = explode(';', $schemaFile);
    $tableCount = 0;
    
    // Execute each statement
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            // Skip the CREATE DATABASE and USE statements as we've already created and connected to the database
            if (strpos($statement, 'CREATE DATABASE') === false && strpos($statement, "USE `$db_name`") === false && strpos($statement, "USE $db_name") === false) {
                $conn->exec($statement);
                
                // Count created tables
                if (strpos($statement, 'CREATE TABLE') !== false) {
                    $tableCount++;
                }
            }
        }
    }
    
    // Verify tables were created
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($tables) > 0) {
        $results['messages'][] = "Successfully created " . count($tables) . " tables:";
        $results['messages'][] = implode(", ", $tables);
    } else {
        $results['errors'][] = "No tables were created. Please check the schema file.";
    }
    
    // Check if default admin user exists
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $adminExists = (int)$stmt->fetchColumn();
    
    if ($adminExists > 0) {
        $results['messages'][] = "Default admin user is set up (username: admin).";
    } else {
        $results['errors'][] = "Default admin user was not created. You may need to manually create an admin user.";
    }
    
    // Update database configuration file if it exists
    $configFile = __DIR__ . '/../config/database.php';
    if (file_exists($configFile) && is_writable($configFile)) {
        $configContent = file_get_contents($configFile);
        
        // Update database configuration
        $configContent = preg_replace('/private \$host = \'(.*?)\';/', "private \$host = '$host';", $configContent);
        $configContent = preg_replace('/private \$db_name = \'(.*?)\';/', "private \$db_name = '$db_name';", $configContent);
        $configContent = preg_replace('/private \$username = \'(.*?)\';/', "private \$username = '$username';", $configContent);
        $configContent = preg_replace('/private \$password = \'(.*?)\';/', "private \$password = '$password';", $configContent);
        
        file_put_contents($configFile, $configContent);
        $results['messages'][] = "Database configuration file updated successfully.";
    }
    
    $results['success'] = true;
    $results['messages'][] = "Database setup completed successfully.";
    $results['messages'][] = "You can now access the application at <a href='http://localhost/icgc/'>http://localhost/icgc/</a>";
    
} catch(PDOException $e) {
    $results['errors'][] = "Database Error: " . $e->getMessage();
} catch(Exception $e) {
    $results['errors'][] = "Error: " . $e->getMessage();
}

// Return results as JSON if requested
if (isset($_GET['json'])) {
    header('Content-Type: application/json');
    echo json_encode($results);
    exit;
}

// Otherwise, return formatted HTML
foreach ($results['messages'] as $message) {
    echo "<p class='success-message'>✓ $message</p>";
}

foreach ($results['errors'] as $error) {
    echo "<p class='error-message'>✗ $error</p>";
}

return $results;
