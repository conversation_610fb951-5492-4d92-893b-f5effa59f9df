<?php

/**
 * Base RESTful Controller
 * Provides common patterns for RESTful controllers while maintaining backward compatibility
 */
abstract class BaseRestfulController {
    
    /**
     * Check if request method matches expected methods (supports both legacy and RESTful)
     * 
     * @param array $allowedMethods Array of allowed HTTP methods
     * @return bool
     */
    protected function checkHttpMethod(array $allowedMethods): bool {
        return in_array($_SERVER['REQUEST_METHOD'], $allowedMethods);
    }
    
    /**
     * Get ID from multiple sources (RESTful route param, POST, or GET)
     * Supports both legacy and RESTful parameter passing
     * 
     * @param mixed $routeParam ID from route parameter (RESTful)
     * @param string $postKey Key to check in $_POST (legacy)
     * @param string $getKey Key to check in $_GET (legacy)
     * @return int|null
     */
    protected function getId($routeParam = null, string $postKey = 'id', string $getKey = 'id'): ?int {
        // Priority 1: Route parameter (RESTful)
        if ($routeParam !== null && is_numeric($routeParam)) {
            return (int)$routeParam;
        }
        
        // Priority 2: POST data (legacy forms)
        if (isset($_POST[$postKey]) && is_numeric($_POST[$postKey])) {
            return (int)$_POST[$postKey];
        }
        
        // Priority 3: GET parameter (legacy links)
        if (isset($_GET[$getKey]) && is_numeric($_GET[$getKey])) {
            return (int)$_GET[$getKey];
        }
        
        return null;
    }
    
    /**
     * Validate CSRF token with consistent error handling
     * 
     * @param string $redirectUrl Where to redirect on failure
     * @param string $errorMessage Error message to display
     * @return bool
     */
    protected function validateCsrf(string $redirectUrl = '', string $errorMessage = 'Invalid security token. Please try again.'): bool {
        $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? '';
        
        if (!verify_csrf_token($token)) {
            set_flash_message($errorMessage, 'danger');
            if ($redirectUrl) {
                redirect($redirectUrl);
                exit;
            }
            return false;
        }
        
        return true;
    }
    
    /**
     * Handle both AJAX and regular responses
     * 
     * @param bool $success Whether operation was successful
     * @param string $message Success/error message
     * @param string $redirectUrl Where to redirect for non-AJAX requests
     * @param array $data Additional data for AJAX responses
     */
    protected function handleResponse(bool $success, string $message, string $redirectUrl = '', array $data = []): void {
        // Check if this is an AJAX request
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => $success,
                'message' => $message,
                'data' => $data
            ]);
            exit;
        }
        
        // Regular request - use flash message and redirect
        $messageType = $success ? 'success' : 'danger';
        set_flash_message($message, $messageType);
        
        if ($redirectUrl) {
            redirect($redirectUrl);
        }
    }
    
    /**
     * Safely get and validate required fields from POST data
     * 
     * @param array $requiredFields Array of required field names
     * @param string $redirectUrl Where to redirect on validation failure
     * @return array|null Returns sanitized data or null on failure
     */
    protected function validateRequiredFields(array $requiredFields, string $redirectUrl = ''): ?array {
        $data = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($_POST[$field]) || trim($_POST[$field]) === '') {
                $this->handleResponse(false, "Field '{$field}' is required.", $redirectUrl);
                return null;
            }
            $data[$field] = trim($_POST[$field]);
        }
        
        return $data;
    }
    
    /**
     * Check if user has required permissions
     * 
     * @param string $requiredRole Required role/permission
     * @param string $redirectUrl Where to redirect if unauthorized
     * @return bool
     */
    protected function checkPermission(string $requiredRole = '', string $redirectUrl = ''): bool {
        // Basic authentication check
        if (!isset($_SESSION['user_id'])) {
            set_flash_message('Please log in to access this page.', 'danger');
            redirect('login');
            return false;
        }
        
        // Role-based check (if specified)
        if ($requiredRole && (!isset($_SESSION['role']) || $_SESSION['role'] !== $requiredRole)) {
            set_flash_message('You do not have permission to perform this action.', 'danger');
            if ($redirectUrl) {
                redirect($redirectUrl);
            }
            return false;
        }
        
        return true;
    }
}
?>
