<?php
/**
 * Cron Job: Check Starting Programs
 * Run hourly during business hours to check for programs starting today
 * 
 * Crontab entry:
 * 0 8-18 * * * /usr/bin/php /path/to/icgc/cron/check_starting_programs.php
 */

// Set the working directory to the project root
$projectRoot = dirname(__DIR__);
chdir($projectRoot);

// Include required files
require_once 'config/database.php';
require_once 'controllers/AutoStatusManager.php';

// Set up error reporting for cron
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', $projectRoot . '/logs/cron_errors.log');

/**
 * Log function for cron output
 */
function cronLog($message) {
    global $projectRoot;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] HOURLY_CHECK: {$message}\n";
    
    // Log to file
    file_put_contents($projectRoot . '/logs/cron.log', $logMessage, FILE_APPEND | LOCK_EX);
    
    // Also output to console
    echo $logMessage;
}

try {
    cronLog("Starting hourly check for programs");
    
    // Initialize database connection
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Failed to connect to database");
    }
    
    // Initialize Auto Status Manager
    $autoStatusManager = new AutoStatusManager($db);
    
    // Check for programs that should be starting now
    $currentHour = date('H');
    $query = "
        SELECT p.*,
               CONCAT(m.first_name, ' ', m.last_name) as coordinator_name,
               m.email as coordinator_email,
               TIME(p.start_time) as program_start_time
        FROM church_programs p
        LEFT JOIN members m ON p.coordinator_id = m.id
        WHERE p.start_date = CURDATE()
        AND p.status = 'planned'
        AND p.auto_status_enabled = 1
        AND p.start_time IS NOT NULL
        AND HOUR(p.start_time) = ?
    ";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$currentHour]);
    $programsStartingNow = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($programsStartingNow)) {
        cronLog("Found " . count($programsStartingNow) . " programs starting at {$currentHour}:00");
        
        foreach ($programsStartingNow as $program) {
            // Update status to in_progress
            $updateQuery = "
                UPDATE church_programs
                SET status = 'in_progress',
                    last_status_update = NOW(),
                    status_updated_by = 'system'
                WHERE id = ?
            ";
            
            $updateStmt = $db->prepare($updateQuery);
            $updateStmt->execute([$program['id']]);
            
            // Log the change
            $logQuery = "
                INSERT INTO program_status_log (program_id, old_status, new_status, changed_by, changed_at, change_type)
                VALUES (?, 'planned', 'in_progress', 'system', NOW(), 'automatic')
            ";
            
            $logStmt = $db->prepare($logQuery);
            $logStmt->execute([$program['id']]);
            
            cronLog("Updated program '{$program['title']}' (ID: {$program['id']}) to IN PROGRESS");
            
            // Send immediate notification
            if (!empty($program['coordinator_email'])) {
                $subject = "🟢 Program Starting Now: {$program['title']}";
                $message = "
                    <html>
                    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                        <div style='max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;'>
                            <h3>🟢 Your Program is Starting Now!</h3>
                            <p>Your program <strong>{$program['title']}</strong> is scheduled to start at <strong>" . date('g:i A', strtotime($program['start_time'])) . "</strong> and has been automatically marked as <strong>IN PROGRESS</strong>.</p>
                            
                            <div style='background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                                <h4 style='margin: 0 0 10px 0; color: #495057;'>Program Details:</h4>
                                <ul style='margin: 0; padding-left: 20px;'>
                                    <li><strong>Start Time:</strong> " . date('g:i A', strtotime($program['start_time'])) . "</li>
                                    <li><strong>Location:</strong> {$program['location']}</li>
                                    <li><strong>Duration:</strong> " . date('g:i A', strtotime($program['start_time'])) . " - " . date('g:i A', strtotime($program['end_time'])) . "</li>
                                </ul>
                            </div>
                            
                            <p style='color: #28a745; font-weight: bold;'>✅ All systems are ready. Have a great program!</p>
                            
                            <hr style='margin: 20px 0; border: none; border-top: 1px solid #eee;'>
                            <p style='font-size: 12px; color: #666;'>
                                This is an automatic notification from the Church Management System.<br>
                                Time: " . date('Y-m-d H:i:s') . "
                            </p>
                        </div>
                    </body>
                    </html>
                ";
                
                $headers = "From: Church Management System <<EMAIL>>\r\n";
                $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
                
                mail($program['coordinator_email'], $subject, $message, $headers);
                
                cronLog("Sent start notification to {$program['coordinator_name']} ({$program['coordinator_email']})");
            }
        }
    } else {
        cronLog("No programs starting at {$currentHour}:00");
    }
    
    // Check for programs ending this hour
    $query = "
        SELECT p.*,
               CONCAT(m.first_name, ' ', m.last_name) as coordinator_name,
               m.email as coordinator_email
        FROM church_programs p
        LEFT JOIN members m ON p.coordinator_id = m.id
        WHERE p.end_date = CURDATE()
        AND p.status = 'in_progress'
        AND p.auto_status_enabled = 1
        AND p.end_time IS NOT NULL
        AND HOUR(p.end_time) = ?
    ";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$currentHour]);
    $programsEndingNow = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($programsEndingNow)) {
        cronLog("Found " . count($programsEndingNow) . " programs ending at {$currentHour}:00");
        
        foreach ($programsEndingNow as $program) {
            // Update status to completed
            $updateQuery = "
                UPDATE church_programs
                SET status = 'completed',
                    last_status_update = NOW(),
                    status_updated_by = 'system'
                WHERE id = ?
            ";
            
            $updateStmt = $db->prepare($updateQuery);
            $updateStmt->execute([$program['id']]);
            
            // Log the change
            $logQuery = "
                INSERT INTO program_status_log (program_id, old_status, new_status, changed_by, changed_at, change_type)
                VALUES (?, 'in_progress', 'completed', 'system', NOW(), 'automatic')
            ";
            
            $logStmt = $db->prepare($logQuery);
            $logStmt->execute([$program['id']]);
            
            cronLog("Updated program '{$program['title']}' (ID: {$program['id']}) to COMPLETED");
            
            // Send completion notification
            if (!empty($program['coordinator_email'])) {
                $subject = "✅ Program Completed: {$program['title']}";
                $message = "
                    <html>
                    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                        <div style='max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;'>
                            <h3>✅ Program Successfully Completed!</h3>
                            <p>Your program <strong>{$program['title']}</strong> has been automatically marked as <strong>COMPLETED</strong>.</p>
                            
                            <div style='background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                                <h4 style='margin: 0 0 10px 0; color: #495057;'>Program Summary:</h4>
                                <ul style='margin: 0; padding-left: 20px;'>
                                    <li><strong>Duration:</strong> " . date('g:i A', strtotime($program['start_time'])) . " - " . date('g:i A', strtotime($program['end_time'])) . "</li>
                                    <li><strong>Location:</strong> {$program['location']}</li>
                                    <li><strong>Status:</strong> <span style='color: #28a745; font-weight: bold;'>COMPLETED</span></li>
                                </ul>
                            </div>
                            
                            <p>Thank you for your coordination and leadership. We hope the program was successful!</p>
                            
                            <hr style='margin: 20px 0; border: none; border-top: 1px solid #eee;'>
                            <p style='font-size: 12px; color: #666;'>
                                This is an automatic notification from the Church Management System.<br>
                                Time: " . date('Y-m-d H:i:s') . "
                            </p>
                        </div>
                    </body>
                    </html>
                ";
                
                $headers = "From: Church Management System <<EMAIL>>\r\n";
                $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
                
                mail($program['coordinator_email'], $subject, $message, $headers);
                
                cronLog("Sent completion notification to {$program['coordinator_name']} ({$program['coordinator_email']})");
            }
        }
    }
    
    cronLog("Hourly check completed successfully");
    
} catch (Exception $e) {
    cronLog("ERROR: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
