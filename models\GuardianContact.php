<?php
/**
 * Guardian Contact Model
 * Manages non-member parent/guardian information for standalone child registration
 */

class GuardianContact {
    // Database connection and table name
    private $conn;
    private $table_name = "guardian_contacts";

    // Object properties
    public $id;
    public $first_name;
    public $last_name;
    public $email;
    public $phone_number;
    public $relationship_to_child;
    public $address;
    public $occupation;
    public $is_primary;
    public $can_pickup;
    public $consent_given;
    public $consent_date;
    public $consent_form_path;
    public $emergency_priority;
    public $notes;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create guardian contact
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET first_name = :first_name,
                      last_name = :last_name,
                      email = :email,
                      phone_number = :phone_number,
                      relationship_to_child = :relationship_to_child,
                      address = :address,
                      occupation = :occupation,
                      is_primary = :is_primary,
                      can_pickup = :can_pickup,
                      consent_given = :consent_given,
                      consent_date = :consent_date,
                      consent_form_path = :consent_form_path,
                      emergency_priority = :emergency_priority,
                      notes = :notes,
                      created_at = :created_at,
                      updated_at = :updated_at";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->phone_number = htmlspecialchars(strip_tags($this->phone_number));
        $this->relationship_to_child = htmlspecialchars(strip_tags($this->relationship_to_child));
        $this->address = htmlspecialchars(strip_tags($this->address));
        $this->occupation = htmlspecialchars(strip_tags($this->occupation));
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Bind values
        $stmt->bindParam(':first_name', $this->first_name);
        $stmt->bindParam(':last_name', $this->last_name);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':phone_number', $this->phone_number);
        $stmt->bindParam(':relationship_to_child', $this->relationship_to_child);
        $stmt->bindParam(':address', $this->address);
        $stmt->bindParam(':occupation', $this->occupation);
        $stmt->bindParam(':is_primary', $this->is_primary, PDO::PARAM_BOOL);
        $stmt->bindParam(':can_pickup', $this->can_pickup, PDO::PARAM_BOOL);
        $stmt->bindParam(':consent_given', $this->consent_given, PDO::PARAM_BOOL);
        $stmt->bindParam(':consent_date', $this->consent_date);
        $stmt->bindParam(':consent_form_path', $this->consent_form_path);
        $stmt->bindParam(':emergency_priority', $this->emergency_priority);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Read guardian contact by ID
     */
    public function read() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 0,1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            $this->first_name = $row['first_name'];
            $this->last_name = $row['last_name'];
            $this->email = $row['email'];
            $this->phone_number = $row['phone_number'];
            $this->relationship_to_child = $row['relationship_to_child'];
            $this->address = $row['address'];
            $this->occupation = $row['occupation'];
            $this->is_primary = $row['is_primary'];
            $this->can_pickup = $row['can_pickup'];
            $this->consent_given = $row['consent_given'];
            $this->consent_date = $row['consent_date'];
            $this->consent_form_path = $row['consent_form_path'];
            $this->emergency_priority = $row['emergency_priority'];
            $this->notes = $row['notes'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }

        return false;
    }

    /**
     * Update guardian contact
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET first_name = :first_name,
                      last_name = :last_name,
                      email = :email,
                      phone_number = :phone_number,
                      relationship_to_child = :relationship_to_child,
                      address = :address,
                      occupation = :occupation,
                      is_primary = :is_primary,
                      can_pickup = :can_pickup,
                      consent_given = :consent_given,
                      consent_date = :consent_date,
                      consent_form_path = :consent_form_path,
                      emergency_priority = :emergency_priority,
                      notes = :notes,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->phone_number = htmlspecialchars(strip_tags($this->phone_number));
        $this->relationship_to_child = htmlspecialchars(strip_tags($this->relationship_to_child));
        $this->address = htmlspecialchars(strip_tags($this->address));
        $this->occupation = htmlspecialchars(strip_tags($this->occupation));
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        $this->updated_at = date('Y-m-d H:i:s');

        // Bind values
        $stmt->bindParam(':first_name', $this->first_name);
        $stmt->bindParam(':last_name', $this->last_name);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':phone_number', $this->phone_number);
        $stmt->bindParam(':relationship_to_child', $this->relationship_to_child);
        $stmt->bindParam(':address', $this->address);
        $stmt->bindParam(':occupation', $this->occupation);
        $stmt->bindParam(':is_primary', $this->is_primary, PDO::PARAM_BOOL);
        $stmt->bindParam(':can_pickup', $this->can_pickup, PDO::PARAM_BOOL);
        $stmt->bindParam(':consent_given', $this->consent_given, PDO::PARAM_BOOL);
        $stmt->bindParam(':consent_date', $this->consent_date);
        $stmt->bindParam(':consent_form_path', $this->consent_form_path);
        $stmt->bindParam(':emergency_priority', $this->emergency_priority);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    /**
     * Delete guardian contact
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        return $stmt->execute();
    }

    /**
     * Check if guardian contact exists by phone
     */
    public function existsByPhone($phone) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE phone_number = :phone LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':phone', $phone);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Check if guardian contact exists by email
     */
    public function existsByEmail($email) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get all guardian contacts
     */
    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY last_name, first_name";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Search guardian contacts by name or phone
     */
    public function search($search_term) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE first_name LIKE :search 
                     OR last_name LIKE :search 
                     OR phone_number LIKE :search 
                     OR email LIKE :search
                  ORDER BY last_name, first_name";
        
        $stmt = $this->conn->prepare($query);
        $search_param = "%{$search_term}%";
        $stmt->bindParam(':search', $search_param);
        $stmt->execute();
        
        return $stmt;
    }

    /**
     * Get guardian contacts for a specific child
     */
    public function getByChildId($child_id) {
        $query = "SELECT gc.*, cgr.relationship_type, cgr.is_primary, cgr.can_pickup, cgr.emergency_priority
                  FROM " . $this->table_name . " gc
                  JOIN child_guardian_relationships cgr ON gc.id = cgr.guardian_contact_id
                  WHERE cgr.child_id = :child_id
                  ORDER BY cgr.is_primary DESC, cgr.emergency_priority ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Validate guardian contact data
     */
    public function validate() {
        $errors = [];

        if (empty($this->first_name)) {
            $errors[] = "First name is required";
        }

        if (empty($this->last_name)) {
            $errors[] = "Last name is required";
        }

        if (empty($this->phone_number)) {
            $errors[] = "Phone number is required";
        } elseif (!preg_match('/^[\+]?[1-9][\d]{0,15}$/', $this->phone_number)) {
            $errors[] = "Invalid phone number format";
        }

        if (!empty($this->email) && !filter_var($this->email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Invalid email format";
        }

        if (empty($this->relationship_to_child)) {
            $errors[] = "Relationship to child is required";
        }

        return $errors;
    }

    /**
     * Get primary guardian for a child
     */
    public function getPrimaryGuardianForChild($child_id) {
        $query = "SELECT gc.*, cgr.relationship_type
                  FROM " . $this->table_name . " gc
                  JOIN child_guardian_relationships cgr ON gc.id = cgr.guardian_contact_id
                  WHERE cgr.child_id = :child_id AND cgr.is_primary = 1
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get emergency contacts for a child
     */
    public function getEmergencyContactsForChild($child_id) {
        $query = "SELECT gc.*, cgr.emergency_priority
                  FROM " . $this->table_name . " gc
                  JOIN child_guardian_relationships cgr ON gc.id = cgr.guardian_contact_id
                  WHERE cgr.child_id = :child_id
                    AND cgr.relationship_type IN ('parent', 'guardian', 'emergency_contact')
                  ORDER BY cgr.emergency_priority ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get authorized pickup persons for a child
     */
    public function getAuthorizedPickupForChild($child_id) {
        $query = "SELECT gc.*, cgr.relationship_type
                  FROM " . $this->table_name . " gc
                  JOIN child_guardian_relationships cgr ON gc.id = cgr.guardian_contact_id
                  WHERE cgr.child_id = :child_id AND cgr.can_pickup = 1
                  ORDER BY cgr.is_primary DESC, gc.last_name, gc.first_name";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();

        return $stmt;
    }
}
