<?php
/**
 * Phone Number Configuration Settings
 * 
 * This file allows churches to configure their default country
 * for phone number normalization based on their location.
 */

// Default country configuration
// Set this to your church's primary country code
// Common codes: GH (Ghana), GB (UK), US (United States), CA (Canada), NG (Nigeria), etc.
define('DEFAULT_PHONE_COUNTRY', 'GH');

// Alternative: You can also set this dynamically based on user settings
// or church configuration stored in the database

/**
 * Available country codes and their details:
 * 
 * Africa:
 * - GH: Ghana (+233)
 * - NG: Nigeria (+234) 
 * - KE: Kenya (+254)
 * - ZA: South Africa (+27)
 * 
 * Europe:
 * - GB: United Kingdom (+44)
 * - DE: Germany (+49)
 * - FR: France (+33)
 * 
 * North America:
 * - US: United States (+1)
 * - CA: Canada (+1)
 * 
 * Asia:
 * - IN: India (+91)
 * - CN: China (+86)
 * 
 * Oceania:
 * - AU: Australia (+61)
 */

/**
 * Instructions for churches:
 * 
 * 1. For churches in Ghana: Keep DEFAULT_PHONE_COUNTRY as 'GH'
 * 2. For churches in UK: Change to 'GB'
 * 3. For churches in US: Change to 'US'
 * 4. For churches in Canada: Change to 'CA'
 * 5. For other countries: Use the appropriate 2-letter country code
 * 
 * This ensures that local phone numbers (without country codes) 
 * are correctly normalized to the right international format.
 */

/**
 * Example configurations:
 * 
 * // For a church in London, UK
 * define('DEFAULT_PHONE_COUNTRY', 'GB');
 * 
 * // For a church in New York, US
 * define('DEFAULT_PHONE_COUNTRY', 'US');
 * 
 * // For a church in Lagos, Nigeria
 * define('DEFAULT_PHONE_COUNTRY', 'NG');
 */
?>
