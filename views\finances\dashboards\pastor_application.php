<?php
/**
 * Pastor Application Dashboard - Complete Rewrite
 * Comprehensive pastor application payment tracking and analytics
 */

// Ensure we have the required data
$analytics = $analytics ?? [];
$memberTracking = $memberTracking ?? [];
$trends = $trends ?? [];
$recentPayments = $recentPayments ?? [];
$categoryInfo = $categoryInfo ?? null;
?>

<div class="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white py-8 px-6 rounded-b-3xl shadow-xl mb-8">
        <div class="max-w-7xl mx-auto">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <a href="<?php echo BASE_URL; ?>finance" class="text-purple-100 hover:text-white mr-3 transition-colors">
                            <i class="fas fa-arrow-left text-lg"></i>
                        </a>
                        <h1 class="text-3xl font-bold">⛪ Pastor Application Dashboard</h1>
                    </div>
                    <p class="text-purple-100 text-lg">Comprehensive pastor application payment tracking and analytics</p>
                </div>
                <div class="flex gap-3">
                    <a href="<?php echo BASE_URL; ?>finance/add" 
                       class="bg-white text-purple-600 px-6 py-3 rounded-lg hover:bg-purple-50 transition-all duration-200 flex items-center shadow-md">
                        <i class="fas fa-plus mr-2"></i>
                        <span>Add Payment</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>finance/categories" 
                       class="bg-purple-800 text-white px-6 py-3 rounded-lg hover:bg-purple-900 transition-all duration-200 flex items-center shadow-md">
                        <i class="fas fa-cog mr-2"></i>
                        <span>Settings</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 pb-8">
        <!-- Analytics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Collected -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Total Collected</p>
                        <p class="text-2xl font-bold text-purple-600">
                            GH₵ <?php echo number_format($analytics['total_amount'] ?? 0, 2); ?>
                        </p>
                    </div>
                    <div class="p-3 rounded-full bg-purple-100">
                        <i class="fas fa-money-bill-wave text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-green-600 font-medium">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <?php echo $analytics['growth_percentage'] ?? 0; ?>%
                    </span>
                    <span class="text-gray-500 ml-2">from last month</span>
                </div>
            </div>

            <!-- Total Applications -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Total Applications</p>
                        <p class="text-2xl font-bold text-indigo-600">
                            <?php echo number_format($analytics['total_count'] ?? 0); ?>
                        </p>
                    </div>
                    <div class="p-3 rounded-full bg-indigo-100">
                        <i class="fas fa-users text-indigo-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-blue-600 font-medium">
                        <i class="fas fa-plus mr-1"></i>
                        <?php echo $analytics['new_this_month'] ?? 0; ?>
                    </span>
                    <span class="text-gray-500 ml-2">this month</span>
                </div>
            </div>

            <!-- Average Payment -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Average Payment</p>
                        <p class="text-2xl font-bold text-green-600">
                            GH₵ <?php echo number_format($analytics['average_amount'] ?? 0, 2); ?>
                        </p>
                    </div>
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-gray-600">
                        <i class="fas fa-calculator mr-1"></i>
                        Per application
                    </span>
                </div>
            </div>

            <!-- This Month -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">This Month</p>
                        <p class="text-2xl font-bold text-orange-600">
                            GH₵ <?php echo number_format($analytics['current_month_amount'] ?? 0, 2); ?>
                        </p>
                    </div>
                    <div class="p-3 rounded-full bg-orange-100">
                        <i class="fas fa-calendar text-orange-600 text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-orange-600 font-medium">
                        <?php echo $analytics['current_month_count'] ?? 0; ?>
                    </span>
                    <span class="text-gray-500 ml-2">applications</span>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Monthly Trends Chart -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">Monthly Trends</h3>
                    <div class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        Last 12 months
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="trendsChart"></canvas>
                </div>
            </div>

            <!-- Payment Distribution -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">Payment Distribution</h3>
                    <div class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        Current year
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="distributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Applications -->
        <div class="bg-white rounded-xl shadow-md border border-gray-100 mb-8">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">Recent Pastor Applications</h3>
                        <p class="text-gray-600 text-sm">Latest application payments and member details</p>
                    </div>
                    <a href="<?php echo BASE_URL; ?>finance/add" 
                       class="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center shadow-md hover:shadow-lg">
                        <i class="fas fa-plus mr-2"></i>
                        <span>Add Application</span>
                    </a>
                </div>

                <?php if (!empty($recentPayments)): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($recentPayments as $payment): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                                    <i class="fas fa-user text-purple-600"></i>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($payment->member_name ?? 'Unknown Member'); ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    ID: <?php echo $payment->member_id ?? 'N/A'; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            GH₵ <?php echo number_format($payment->amount ?? 0, 2); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?php echo ucfirst(str_replace('_', ' ', $payment->payment_method ?? 'cash')); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('M j, Y', strtotime($payment->transaction_date ?? 'now')); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Completed
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <div class="mx-auto h-24 w-24 text-gray-300 mb-4">
                            <i class="fas fa-file-invoice-dollar text-6xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Pastor Applications Yet</h3>
                        <p class="text-gray-500 mb-6">Start tracking pastor application payments by adding the first entry.</p>
                        <a href="<?php echo BASE_URL; ?>finance/add" 
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                            <i class="fas fa-plus mr-2"></i>
                            Add First Application
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Export Section -->
        <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-bold text-gray-800 mb-2">Export Data</h3>
                    <p class="text-gray-600 text-sm">Download pastor application payment data for analysis</p>
                </div>
                <div class="flex gap-3">
                    <button onclick="exportData('csv')" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-file-csv mr-2"></i>
                        Export CSV
                    </button>
                    <button onclick="exportData('excel')" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                        <i class="fas fa-file-excel mr-2"></i>
                        Export Excel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart initialization
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Monthly Trends Chart
    const trendsCtx = document.getElementById('trendsChart');
    if (trendsCtx) {
        new Chart(trendsCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_column($trends, 'month') ?? []); ?>,
                datasets: [{
                    label: 'Pastor Applications',
                    data: <?php echo json_encode(array_column($trends, 'amount') ?? []); ?>,
                    borderColor: 'rgb(147, 51, 234)',
                    backgroundColor: 'rgba(147, 51, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'GH₵ ' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    // Distribution Chart
    const distributionCtx = document.getElementById('distributionChart');
    if (distributionCtx) {
        new Chart(distributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Cash', 'Bank Transfer', 'Mobile Money', 'Cheque'],
                datasets: [{
                    data: [
                        <?php echo $analytics['cash_amount'] ?? 0; ?>,
                        <?php echo $analytics['bank_amount'] ?? 0; ?>,
                        <?php echo $analytics['mobile_amount'] ?? 0; ?>,
                        <?php echo $analytics['cheque_amount'] ?? 0; ?>
                    ],
                    backgroundColor: [
                        '#10B981',
                        '#3B82F6',
                        '#F59E0B',
                        '#EF4444'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// Export functionality
function exportData(format) {
    const url = `<?php echo BASE_URL; ?>finance/export/pastor-application?format=${format}`;
    window.open(url, '_blank');
}
</script>
