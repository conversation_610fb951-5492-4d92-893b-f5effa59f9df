<?php
$page_title = "Record Welfare Claim";
$active_page = "welfare";
?>

<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex items-center mb-6">
        <a href="<?php echo BASE_URL; ?>welfare" class="text-gray-600 hover:text-gray-800 mr-4">
            <i class="fas fa-arrow-left text-xl"></i>
        </a>
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Record Welfare Claim</h1>
            <p class="text-gray-600 mt-1">Record a member's welfare assistance claim</p>
        </div>
    </div>

    <!-- Claim Form -->
    <!-- Available Funds Alert -->
    <div class="max-w-4xl mx-auto mb-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Available Welfare Funds</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <span class="font-medium">Total Collected:</span>
                                <span class="ml-1 font-bold">₵<?php echo number_format($welfareFunds['total_collected'], 2); ?></span>
                            </div>
                            <div>
                                <span class="font-medium">Total Disbursed:</span>
                                <span class="ml-1 font-bold">₵<?php echo number_format($welfareFunds['total_disbursed'], 2); ?></span>
                            </div>
                            <div>
                                <span class="font-medium">Available Balance:</span>
                                <span class="ml-1 font-bold text-green-700">₵<?php echo number_format($welfareFunds['available_balance'], 2); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Welfare Claim Details</h2>
                <p class="text-sm text-gray-600 mt-1">Record assistance provided to a member in need</p>
            </div>
            
            <form action="<?php echo BASE_URL; ?>welfare/claim" method="POST" class="p-6">
                <?php echo csrf_field(); ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Member Selection -->
                    <div class="md:col-span-2">
                        <label for="member_search" class="block text-sm font-medium text-gray-700 mb-2">
                            Select Member <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <!-- Hidden input for form submission -->
                            <input type="hidden" name="member_id" id="member_id" required>
                            
                            <!-- Search input -->
                            <input type="text" 
                                   id="member_search" 
                                   placeholder="Search by name or phone number..."
                                   autocomplete="off"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                            
                            <!-- Search results dropdown -->
                            <div id="member_dropdown" 
                                 class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                                <div id="member_results" class="py-1">
                                    <!-- Results will be populated here -->
                                </div>
                                <div id="no_results" class="px-3 py-2 text-gray-500 text-sm hidden">
                                    No members found
                                </div>
                            </div>
                        </div>
                        
                        <!-- Selected member display -->
                        <div id="selected_member" class="mt-2 hidden">
                            <div class="flex items-center p-3 bg-orange-50 border border-orange-200 rounded-lg">
                                <div class="flex-1">
                                    <p class="font-medium text-orange-900" id="selected_member_name"></p>
                                    <p class="text-sm text-orange-700" id="selected_member_phone"></p>
                                </div>
                                <button type="button" 
                                        onclick="clearMemberSelection()" 
                                        class="text-orange-600 hover:text-orange-800">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Claim Amount -->
                    <div>
                        <label for="claim_amount" class="block text-sm font-medium text-gray-700 mb-2">
                            Claim Amount (₵) <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="number" name="claim_amount" id="claim_amount" step="0.01" min="0.01"
                                   max="<?php echo $welfareFunds['available_balance']; ?>" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                   placeholder="0.00">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-sm">Max: ₵<?php echo number_format($welfareFunds['available_balance'], 2); ?></span>
                            </div>
                        </div>
                        <div id="amount_error" class="mt-1 text-sm text-red-600 hidden"></div>
                        <div id="amount_warning" class="mt-1 text-sm text-amber-600 hidden"></div>
                    </div>

                    <!-- Claim Date -->
                    <div>
                        <label for="claim_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Claim Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="claim_date" id="claim_date" required
                               value="<?php echo date('Y-m-d'); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    </div>

                    <!-- Claim Reason -->
                    <div class="md:col-span-2">
                        <label for="claim_reason" class="block text-sm font-medium text-gray-700 mb-2">
                            Reason for Welfare Claim <span class="text-red-500">*</span>
                        </label>
                        <textarea name="claim_reason" id="claim_reason" rows="4" required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                  placeholder="Describe why this member needs welfare assistance..."></textarea>
                    </div>

                    <!-- Additional Notes -->
                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Additional Notes
                        </label>
                        <textarea name="notes" id="notes" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                  placeholder="Any additional notes or comments..."></textarea>
                    </div>
                </div>

                <!-- Important Notice -->
                <div class="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-amber-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-amber-800">Important Notice</h3>
                            <div class="mt-2 text-sm text-amber-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Each member can only claim welfare assistance once per month</li>
                                    <li>Claims cannot exceed available welfare fund balance</li>
                                    <li>Claims are automatically approved and disbursed from the welfare fund</li>
                                    <li>Please ensure the member genuinely needs assistance</li>
                                    <li>All claims reduce the available welfare fund balance</li>
                                    <li>All claims are tracked and reported monthly</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                    <a href="<?php echo BASE_URL; ?>welfare" 
                       class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-hand-holding-heart mr-2"></i>
                        Record Claim
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Member data for search functionality
const members = <?php echo json_encode($members); ?>;

// Enhanced member search functionality
document.addEventListener('DOMContentLoaded', function() {
    const memberSearch = document.getElementById('member_search');
    const memberDropdown = document.getElementById('member_dropdown');
    const memberResults = document.getElementById('member_results');
    const noResults = document.getElementById('no_results');
    const memberIdInput = document.getElementById('member_id');
    const selectedMemberDiv = document.getElementById('selected_member');
    
    // Search functionality
    memberSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();
        
        if (searchTerm.length === 0) {
            memberDropdown.classList.add('hidden');
            return;
        }
        
        // Filter members based on search term
        const filteredMembers = members.filter(member => {
            const fullName = `${member.first_name} ${member.last_name}`.toLowerCase();
            const phone = member.phone_number ? member.phone_number.toLowerCase() : '';
            return fullName.includes(searchTerm) || phone.includes(searchTerm);
        });
        
        // Display results
        displaySearchResults(filteredMembers);
    });
    
    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!memberSearch.contains(e.target) && !memberDropdown.contains(e.target)) {
            memberDropdown.classList.add('hidden');
        }
    });
    
    // Show dropdown when focusing on search input
    memberSearch.addEventListener('focus', function() {
        if (this.value.trim().length > 0) {
            memberDropdown.classList.remove('hidden');
        }
    });
    
    function displaySearchResults(filteredMembers) {
        memberResults.innerHTML = '';
        
        if (filteredMembers.length === 0) {
            noResults.classList.remove('hidden');
            memberResults.classList.add('hidden');
        } else {
            noResults.classList.add('hidden');
            memberResults.classList.remove('hidden');
            
            filteredMembers.forEach(member => {
                const resultItem = document.createElement('div');
                resultItem.className = 'px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0';
                resultItem.innerHTML = `
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-orange-600 font-medium text-sm">
                                ${member.first_name.charAt(0)}${member.last_name.charAt(0)}
                            </span>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">${member.first_name} ${member.last_name}</p>
                            ${member.phone_number ? `<p class="text-sm text-gray-600">${member.phone_number}</p>` : ''}
                        </div>
                    </div>
                `;
                
                resultItem.addEventListener('click', function() {
                    selectMember(member);
                });
                
                memberResults.appendChild(resultItem);
            });
        }
        
        memberDropdown.classList.remove('hidden');
    }
    
    function selectMember(member) {
        // Set hidden input value
        memberIdInput.value = member.id;
        
        // Clear search input
        memberSearch.value = '';
        
        // Hide dropdown
        memberDropdown.classList.add('hidden');
        
        // Show selected member
        document.getElementById('selected_member_name').textContent = `${member.first_name} ${member.last_name}`;
        document.getElementById('selected_member_phone').textContent = member.phone_number || 'No phone number';
        selectedMemberDiv.classList.remove('hidden');
        
        // Hide search input
        memberSearch.style.display = 'none';
    }
    
    // Real-time amount validation
    const amountInput = document.getElementById('claim_amount');
    const amountError = document.getElementById('amount_error');
    const amountWarning = document.getElementById('amount_warning');
    const submitButton = document.querySelector('button[type="submit"]');
    const maxAmount = <?php echo $welfareFunds['available_balance']; ?>;

    if (amountInput) {
        amountInput.addEventListener('input', function() {
            const value = parseFloat(this.value) || 0;

            // Clear previous messages
            amountError.classList.add('hidden');
            amountWarning.classList.add('hidden');
            submitButton.disabled = false;
            submitButton.classList.remove('opacity-50', 'cursor-not-allowed');

            if (value < 0) {
                this.value = 0;
                return;
            }

            if (value > maxAmount) {
                amountError.textContent = `Amount exceeds available welfare funds (₵${maxAmount.toFixed(2)})`;
                amountError.classList.remove('hidden');
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                return;
            }

            if (value > maxAmount * 0.8) {
                amountWarning.textContent = `Warning: This claim will use ${((value/maxAmount)*100).toFixed(1)}% of available funds`;
                amountWarning.classList.remove('hidden');
            }

            if (value === 0) {
                amountError.textContent = 'Claim amount must be greater than zero';
                amountError.classList.remove('hidden');
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
        });
    }
    
    // Set max date to today
    const dateInput = document.getElementById('claim_date');
    if (dateInput) {
        dateInput.max = new Date().toISOString().split('T')[0];
    }
});

// Clear member selection function
function clearMemberSelection() {
    document.getElementById('member_id').value = '';
    document.getElementById('member_search').value = '';
    document.getElementById('member_search').style.display = 'block';
    document.getElementById('selected_member').classList.add('hidden');
    document.getElementById('member_dropdown').classList.add('hidden');
    document.getElementById('member_search').focus();
}
</script>
