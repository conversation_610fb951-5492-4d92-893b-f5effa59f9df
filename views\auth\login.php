<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php
    // Load settings for page title
    require_once 'models/Setting.php';
    require_once 'config/database.php';
    $database = new Database();
    $setting = new Setting($database->getConnection());
    $layout_settings = $setting->getAllAsArray();
    $church_name = $layout_settings['church_name'] ?? 'ICGC Emmanuel Temple';

    // Debug information - comment out in production
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
    ?>

    <title><?php echo htmlspecialchars($church_name); ?> - Login</title>
    
    <link rel="icon" href="<?php echo BASE_URL; ?>assets/images/icgc.png" type="image/png">
    <meta name="description" content="<?php echo htmlspecialchars($church_name); ?> Church Management System">
    <meta name="author" content="<?php echo htmlspecialchars($church_name); ?>">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#3F7D58',
                        'primary-light': '#5a9e76',
                        'primary-dark': '#2c5a3f',
                        'secondary': '#D3E671',
                        'secondary-light': '#e1ef9a',
                        'secondary-dark': '#b8c95c',
                    },
                    backgroundImage: {
                        // Exact gradient from the reference image
                        'sidebar-theme': 'linear-gradient(to bottom, #2D5A3D 0%, #3A6B4A 8%, #477C57 16%, #548D64 24%, #619E71 32%, #6EAF7E 40%, #7BC08B 48%, #88D198 56%, #95E2A5 64%, #A2F3B2 72%, #AFE2BF 80%, #BCE5CC 88%, #C9E8D9 96%, #D6EBE6 100%)',
                    }
                }
            }
        }
    </script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/style.css">
    <style>
        @keyframes pulse-subtle {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .logo-container {
            animation: pulse-subtle 3s infinite ease-in-out;
            position: relative;
        }
        .logo-container::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            background: linear-gradient(to right, rgba(63, 125, 88, 0.2), rgba(211, 230, 113, 0.2));
            z-index: -1;
            animation: pulse-subtle 3s infinite ease-in-out 1.5s;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center" style="background: linear-gradient(135deg, #f5f7fa 0%, #e4e8ec 100%);">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md border-t-4 border-primary" style="box-shadow: 0 10px 25px rgba(63, 125, 88, 0.1);">
        <div class="text-center mb-8">
            <div class="flex justify-center mb-4">
                <div class="logo-container w-28 h-28 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center shadow-lg">
                    <?php
                    // Use church logo for login page
                    $current_logo = $layout_settings['church_logo'] ?? '';
                    $logo_src = BASE_URL . 'assets/images/icgc.png'; // Default logo

                    if (!empty($current_logo) && file_exists($current_logo)) {
                        $logo_src = BASE_URL . ltrim($current_logo, '/') . '?v=' . filemtime($current_logo);
                    }
                    ?>
                    <img src="<?php echo $logo_src; ?>" alt="<?php echo htmlspecialchars($church_name); ?>" class="h-20">
                </div>
            </div>
            <h1 class="text-2xl font-bold text-primary-dark"><?php echo htmlspecialchars($church_name); ?></h1>
            <p class="text-gray-600">Login to your account</p>
        </div>

        <?php if (isset($_SESSION['flash_message'])): ?>
            <?php
            $type = isset($_SESSION['flash_message']['type']) ? $_SESSION['flash_message']['type'] : 'info';
            $bgColor = $type === 'danger' ? 'bg-red-100' : ($type === 'warning' ? 'bg-yellow-100' : ($type === 'success' ? 'bg-green-100' : 'bg-blue-100'));
            $borderColor = $type === 'danger' ? 'border-red-500' : ($type === 'warning' ? 'border-yellow-500' : ($type === 'success' ? 'border-green-500' : 'border-blue-500'));
            $textColor = $type === 'danger' ? 'text-red-700' : ($type === 'warning' ? 'text-yellow-700' : ($type === 'success' ? 'text-green-700' : 'text-blue-700'));
            ?>
            <div class="<?php echo $bgColor; ?> border-l-4 <?php echo $borderColor; ?> <?php echo $textColor; ?> p-4 mb-4" role="alert">
                <p><?php echo is_array($_SESSION['flash_message']) ? $_SESSION['flash_message']['message'] : $_SESSION['flash_message']; ?></p>
            </div>
            <?php unset($_SESSION['flash_message']); ?>
        <?php endif; ?>

        <?php
        // Add debugging info
        if (!function_exists('csrf_field')) {
            echo '<div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">';
            echo '<p>Error: csrf_field function not found!</p>';
            echo '</div>';
        }
        ?>

        <form action="<?php echo BASE_URL; ?>auth/login" method="POST" class="space-y-6">
            <?php
            // Generate CSRF token if not exists, otherwise use existing one
            if (!isset($_SESSION['csrf_token'])) {
                $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            }
            echo '<input type="hidden" name="csrf_token" value="' . $_SESSION['csrf_token'] . '">';
            ?>
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-envelope text-gray-400"></i>
                    </div>
                    <input type="email" id="email" name="email" required class="block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                </div>
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-lock text-gray-400"></i>
                    </div>
                    <input type="password" id="password" name="password" required class="block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember_me" name="remember_me" type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    <label for="remember_me" class="ml-2 block text-sm text-gray-700">Remember me</label>
                </div>

                <div class="text-sm">
                    <a href="<?php echo BASE_URL; ?>forgot-password" class="font-medium text-primary hover:text-primary-dark">Forgot your password?</a>
                </div>
            </div>

            <div>
                <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200">
                    <i class="fas fa-sign-in-alt mr-2"></i> Sign in
                </button>
            </div>
        </form>

        <div class="mt-6">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white text-primary"><?php echo htmlspecialchars($church_name); ?></span>
                </div>
            </div>
        </div>
    </div>
    <!-- JavaScript -->
    <script src="<?php echo BASE_URL; ?>assets/js/main.js"></script>
</body>
</html>
