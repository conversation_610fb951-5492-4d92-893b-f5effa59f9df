<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-primary">Create API Key</h1>
        <a href="<?php echo BASE_URL; ?>api-keys" class="text-primary hover:text-primary-dark transition">
            <i class="fas fa-arrow-left mr-2"></i> Back to API Keys
        </a>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden p-6">
        <form action="<?php echo BASE_URL; ?>api-keys/store" method="POST">
            <?php echo csrf_field(); ?>
            
            <div class="mb-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <input type="text" id="description" name="description" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                    placeholder="e.g., Dashboard Integration, Mobile App, Analytics Service">
                <p class="mt-1 text-sm text-gray-500">A descriptive name to identify this API key's purpose</p>
            </div>
            
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-gray-50 p-4 rounded border border-gray-200">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="permission-read" name="permissions[]" type="checkbox" value="read" checked
                                    class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="permission-read" class="font-medium text-gray-700">Read</label>
                                <p class="text-gray-500">Can read data from APIs</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded border border-gray-200">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="permission-attendance" name="permissions[]" type="checkbox" value="attendance"
                                    class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="permission-attendance" class="font-medium text-gray-700">Attendance</label>
                                <p class="text-gray-500">Can access attendance APIs</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded border border-gray-200">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="permission-dashboard" name="permissions[]" type="checkbox" value="dashboard"
                                    class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="permission-dashboard" class="font-medium text-gray-700">Dashboard</label>
                                <p class="text-gray-500">Can access dashboard data APIs</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded border border-gray-200">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="permission-members" name="permissions[]" type="checkbox" value="members"
                                    class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="permission-members" class="font-medium text-gray-700">Members</label>
                                <p class="text-gray-500">Can access member data APIs</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded border border-gray-200">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="permission-write" name="permissions[]" type="checkbox" value="write"
                                    class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="permission-write" class="font-medium text-gray-700">Write</label>
                                <p class="text-gray-500">Can create/update data through APIs</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Important</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>The API key will only be shown once after creation. Make sure to copy it somewhere safe.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end">
                <a href="<?php echo BASE_URL; ?>api-keys" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary mr-3">
                    Cancel
                </a>
                <button type="submit" class="bg-primary text-white py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    Create API Key
                </button>
            </div>
        </form>
    </div>
</div> 