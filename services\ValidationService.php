<?php
/**
 * Validation Service
 * 
 * Centralized validation logic for the finance system.
 * Provides comprehensive input validation and sanitization.
 * 
 * @package Services
 * <AUTHOR> Finance System
 * @version 1.0.0
 */

require_once 'exceptions/ValidationException.php';

class ValidationService
{
    /**
     * @var array Validation rules for different data types
     */
    private $validationRules = [
        'category_types' => ['member_payments', 'general_income', 'expenses'],
        'max_lengths' => [
            'name' => 100,
            'label' => 150,
            'description' => 500,
            'slug' => 100,
            'icon' => 50,
            'dashboard_route' => 255
        ],
        'required_fields' => [
            'create' => ['name', 'label', 'category_type'],
            'update' => [] // No required fields for updates
        ]
    ];

    /**
     * Validate category data
     * 
     * @param array $data Data to validate
     * @param int|null $categoryId Category ID for updates (null for creates)
     * @return bool True if valid
     * @throws ValidationException If validation fails
     */
    public function validateCategoryData(array $data, ?int $categoryId = null): bool
    {
        $errors = [];
        $isUpdate = $categoryId !== null;
        
        // Check required fields for creation
        if (!$isUpdate) {
            $requiredFields = $this->validationRules['required_fields']['create'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    $errors[] = "Field '{$field}' is required";
                }
            }
        }
        
        // Validate individual fields
        if (isset($data['name'])) {
            $nameErrors = $this->validateName($data['name']);
            $errors = array_merge($errors, $nameErrors);
        }
        
        if (isset($data['label'])) {
            $labelErrors = $this->validateLabel($data['label']);
            $errors = array_merge($errors, $labelErrors);
        }
        
        if (isset($data['category_type'])) {
            $typeErrors = $this->validateCategoryType($data['category_type']);
            $errors = array_merge($errors, $typeErrors);
        }
        
        if (isset($data['description'])) {
            $descErrors = $this->validateDescription($data['description']);
            $errors = array_merge($errors, $descErrors);
        }
        
        if (isset($data['slug'])) {
            $slugErrors = $this->validateSlug($data['slug']);
            $errors = array_merge($errors, $slugErrors);
        }
        
        if (isset($data['icon'])) {
            $iconErrors = $this->validateIcon($data['icon']);
            $errors = array_merge($errors, $iconErrors);
        }
        
        if (isset($data['is_active'])) {
            $activeErrors = $this->validateBoolean($data['is_active'], 'is_active');
            $errors = array_merge($errors, $activeErrors);
        }
        
        if (isset($data['requires_member'])) {
            $memberErrors = $this->validateBoolean($data['requires_member'], 'requires_member');
            $errors = array_merge($errors, $memberErrors);
        }
        
        // Throw exception if there are validation errors
        if (!empty($errors)) {
            throw new ValidationException('Validation failed: ' . implode(', ', $errors));
        }
        
        return true;
    }

    /**
     * Validate transaction data
     * 
     * @param array $data Transaction data to validate
     * @return bool True if valid
     * @throws ValidationException If validation fails
     */
    public function validateTransactionData(array $data): bool
    {
        $errors = [];
        
        // Required fields for transactions
        $requiredFields = ['amount', 'category', 'description', 'payment_method'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[] = "Field '{$field}' is required";
            }
        }
        
        // Validate amount
        if (isset($data['amount'])) {
            $amountErrors = $this->validateAmount($data['amount']);
            $errors = array_merge($errors, $amountErrors);
        }
        
        // Validate category
        if (isset($data['category'])) {
            $categoryErrors = $this->validateTransactionCategory($data['category']);
            $errors = array_merge($errors, $categoryErrors);
        }
        
        // Validate description
        if (isset($data['description'])) {
            $descErrors = $this->validateTransactionDescription($data['description']);
            $errors = array_merge($errors, $descErrors);
        }
        
        // Validate payment method
        if (isset($data['payment_method'])) {
            $methodErrors = $this->validatePaymentMethod($data['payment_method']);
            $errors = array_merge($errors, $methodErrors);
        }
        
        // Validate date if provided
        if (isset($data['transaction_date'])) {
            $dateErrors = $this->validateDate($data['transaction_date']);
            $errors = array_merge($errors, $dateErrors);
        }
        
        // Validate member name if provided
        if (isset($data['member_name'])) {
            $memberErrors = $this->validateMemberName($data['member_name']);
            $errors = array_merge($errors, $memberErrors);
        }
        
        if (!empty($errors)) {
            throw new ValidationException('Transaction validation failed: ' . implode(', ', $errors));
        }
        
        return true;
    }

    /**
     * Validate category name
     * 
     * @param string $name Category name
     * @return array Validation errors
     */
    private function validateName(string $name): array
    {
        $errors = [];
        
        // Check length
        if (strlen($name) > $this->validationRules['max_lengths']['name']) {
            $errors[] = "Name must be {$this->validationRules['max_lengths']['name']} characters or less";
        }
        
        // Check for empty
        if (trim($name) === '') {
            $errors[] = "Name cannot be empty";
        }
        
        // Check for valid characters (alphanumeric, spaces, underscores, hyphens)
        if (!preg_match('/^[a-zA-Z0-9\s_-]+$/', $name)) {
            $errors[] = "Name can only contain letters, numbers, spaces, underscores, and hyphens";
        }
        
        return $errors;
    }

    /**
     * Validate category label
     * 
     * @param string $label Category label
     * @return array Validation errors
     */
    private function validateLabel(string $label): array
    {
        $errors = [];
        
        if (strlen($label) > $this->validationRules['max_lengths']['label']) {
            $errors[] = "Label must be {$this->validationRules['max_lengths']['label']} characters or less";
        }
        
        if (trim($label) === '') {
            $errors[] = "Label cannot be empty";
        }
        
        return $errors;
    }

    /**
     * Validate category type
     * 
     * @param string $type Category type
     * @return array Validation errors
     */
    private function validateCategoryType(string $type): array
    {
        $errors = [];
        
        if (!in_array($type, $this->validationRules['category_types'])) {
            $errors[] = "Invalid category type. Must be one of: " . implode(', ', $this->validationRules['category_types']);
        }
        
        return $errors;
    }

    /**
     * Validate description
     * 
     * @param string $description Description
     * @return array Validation errors
     */
    private function validateDescription(string $description): array
    {
        $errors = [];
        
        if (strlen($description) > $this->validationRules['max_lengths']['description']) {
            $errors[] = "Description must be {$this->validationRules['max_lengths']['description']} characters or less";
        }
        
        return $errors;
    }

    /**
     * Validate slug
     * 
     * @param string $slug Slug
     * @return array Validation errors
     */
    private function validateSlug(string $slug): array
    {
        $errors = [];
        
        if (strlen($slug) > $this->validationRules['max_lengths']['slug']) {
            $errors[] = "Slug must be {$this->validationRules['max_lengths']['slug']} characters or less";
        }
        
        // Slug should only contain lowercase letters, numbers, and hyphens
        if (!preg_match('/^[a-z0-9-]+$/', $slug)) {
            $errors[] = "Slug can only contain lowercase letters, numbers, and hyphens";
        }
        
        return $errors;
    }

    /**
     * Validate icon
     * 
     * @param string $icon Icon class
     * @return array Validation errors
     */
    private function validateIcon(string $icon): array
    {
        $errors = [];
        
        if (strlen($icon) > $this->validationRules['max_lengths']['icon']) {
            $errors[] = "Icon must be {$this->validationRules['max_lengths']['icon']} characters or less";
        }
        
        // Basic check for FontAwesome icon format
        if (!preg_match('/^fa[srb]?\s+fa-[a-z0-9-]+$/', $icon)) {
            $errors[] = "Icon must be a valid FontAwesome class (e.g., 'fas fa-dollar-sign')";
        }
        
        return $errors;
    }

    /**
     * Validate boolean value
     * 
     * @param mixed $value Value to validate
     * @param string $fieldName Field name for error messages
     * @return array Validation errors
     */
    private function validateBoolean($value, string $fieldName): array
    {
        $errors = [];
        
        if (!is_bool($value) && !in_array($value, [0, 1, '0', '1', 'true', 'false'])) {
            $errors[] = "Field '{$fieldName}' must be a boolean value";
        }
        
        return $errors;
    }

    /**
     * Validate transaction amount
     * 
     * @param mixed $amount Amount to validate
     * @return array Validation errors
     */
    private function validateAmount($amount): array
    {
        $errors = [];
        
        if (!is_numeric($amount)) {
            $errors[] = "Amount must be a valid number";
        } elseif ($amount <= 0) {
            $errors[] = "Amount must be greater than zero";
        } elseif ($amount > 999999999.99) {
            $errors[] = "Amount cannot exceed 999,999,999.99";
        }
        
        return $errors;
    }

    /**
     * Validate transaction category
     * 
     * @param string $category Category name
     * @return array Validation errors
     */
    private function validateTransactionCategory(string $category): array
    {
        $errors = [];
        
        if (trim($category) === '') {
            $errors[] = "Category cannot be empty";
        }
        
        if (strlen($category) > 100) {
            $errors[] = "Category name must be 100 characters or less";
        }
        
        return $errors;
    }

    /**
     * Validate transaction description
     * 
     * @param string $description Description
     * @return array Validation errors
     */
    private function validateTransactionDescription(string $description): array
    {
        $errors = [];
        
        if (trim($description) === '') {
            $errors[] = "Description cannot be empty";
        }
        
        if (strlen($description) > 500) {
            $errors[] = "Description must be 500 characters or less";
        }
        
        return $errors;
    }

    /**
     * Validate payment method
     * 
     * @param string $method Payment method
     * @return array Validation errors
     */
    private function validatePaymentMethod(string $method): array
    {
        $errors = [];
        
        $validMethods = ['cash', 'check', 'bank_transfer', 'mobile_money', 'card', 'other'];
        
        if (!in_array($method, $validMethods)) {
            $errors[] = "Invalid payment method. Must be one of: " . implode(', ', $validMethods);
        }
        
        return $errors;
    }

    /**
     * Validate date
     * 
     * @param string $date Date string
     * @return array Validation errors
     */
    private function validateDate(string $date): array
    {
        $errors = [];
        
        $dateTime = DateTime::createFromFormat('Y-m-d', $date);
        if (!$dateTime || $dateTime->format('Y-m-d') !== $date) {
            $errors[] = "Date must be in YYYY-MM-DD format";
        }
        
        return $errors;
    }

    /**
     * Validate member name
     * 
     * @param string $name Member name
     * @return array Validation errors
     */
    private function validateMemberName(string $name): array
    {
        $errors = [];
        
        if (strlen($name) > 100) {
            $errors[] = "Member name must be 100 characters or less";
        }
        
        // Allow letters, spaces, apostrophes, and hyphens
        if (!preg_match('/^[a-zA-Z\s\'-]+$/', $name)) {
            $errors[] = "Member name can only contain letters, spaces, apostrophes, and hyphens";
        }
        
        return $errors;
    }

    /**
     * Sanitize string input
     * 
     * @param string $input Input string
     * @return string Sanitized string
     */
    public function sanitizeString(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Sanitize numeric input
     * 
     * @param mixed $input Input value
     * @return float|int Sanitized numeric value
     */
    public function sanitizeNumeric($input)
    {
        return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
    }

    /**
     * Sanitize boolean input
     * 
     * @param mixed $input Input value
     * @return bool Sanitized boolean value
     */
    public function sanitizeBoolean($input): bool
    {
        return filter_var($input, FILTER_VALIDATE_BOOLEAN);
    }
}
