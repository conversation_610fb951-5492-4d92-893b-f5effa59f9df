<!-- Custom Finance Categories Management -->
<div class="fade-in">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Manage Finance Categories</h1>
            <p class="text-gray-600 text-sm mt-1">Add and manage custom payment types for your finance system</p>
            <div class="mt-2 p-2 bg-blue-50 border border-blue-200 rounded">
                <p class="text-xs text-blue-700">
                    <i class="fas fa-info-circle mr-1"></i>
                    <strong>Toggle:</strong> Inactive categories are shown with reduced opacity and can be reactivated. Click toggle to activate/deactivate.
                </p>
            </div>
        </div>
        <div class="flex space-x-3">
            <button type="button" id="add-category-btn" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2"></i> Add Custom Category
            </button>
            <a href="<?php echo BASE_URL; ?>finance/add" class="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Finance
            </a>
        </div>
    </div>

    <!-- Form Errors -->
    <?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) : ?>
        <div class="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-r-md mb-6">
            <div class="flex items-center mb-2">
                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                <h4 class="font-bold">Please fix the following errors:</h4>
            </div>
            <ul class="list-disc ml-6 mt-2">
                <?php foreach ($_SESSION['errors'] as $error) : ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['errors']); ?>
    <?php endif; ?>

    <!-- Categories Tabs -->
    <div class="bg-white rounded-lg shadow-lg border-t-4 border-primary">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button type="button" class="category-tab active border-b-2 border-primary text-primary py-4 px-1 text-sm font-medium" data-tab="member-payments">
                    <i class="fas fa-users mr-2"></i>
                    Member Payments
                </button>
                <button type="button" class="category-tab border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium" data-tab="general-income">
                    <i class="fas fa-hand-holding-usd mr-2"></i>
                    General Income
                </button>
                <button type="button" class="category-tab border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium" data-tab="expenses">
                    <i class="fas fa-credit-card mr-2"></i>
                    Expenses
                </button>
            </nav>
        </div>

        <!-- Tab Contents -->
        <div class="p-6">
            <!-- Member Payments Tab -->
            <div id="member-payments-content" class="tab-content">
                <div class="mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Member Payment Categories</h3>
                    <p class="text-gray-600 text-sm">Categories that require member tracking for payments</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php if (empty($memberPaymentCategories)) : ?>
                        <div class="col-span-full text-center py-8">
                            <i class="fas fa-plus-circle text-gray-400 text-4xl mb-3"></i>
                            <p class="text-gray-500">No custom member payment categories yet.</p>
                            <button type="button" class="add-category-btn mt-3 text-primary hover:text-primary-dark" data-type="member_payments">
                                <i class="fas fa-plus mr-1"></i> Add your first category
                            </button>
                        </div>
                    <?php else : ?>
                        <?php foreach ($memberPaymentCategories as $category) : ?>
                            <div class="category-card bg-blue-50 border border-blue-200 rounded-lg p-4 <?php echo !$category->is_active ? 'opacity-60 bg-gray-50 border-gray-300' : ''; ?> <?php echo isset($category->is_core) && $category->is_core ? 'border-l-4 border-l-yellow-500' : ''; ?>">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center">
                                        <i class="<?php echo $category->icon; ?> text-blue-600 mr-2 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>"></i>
                                        <span class="font-medium text-blue-800 <?php echo !$category->is_active ? 'text-gray-500' : ''; ?>">
                                            <?php echo htmlspecialchars($category->label); ?>
                                            <?php if (isset($category->is_core) && $category->is_core): ?>
                                                <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full ml-2">Core</span>
                                            <?php endif; ?>
                                            <?php if (!$category->is_active): ?>
                                                <span class="text-xs text-red-500 font-normal ml-2">(Inactive)</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <?php if (isset($category->is_core) && $category->is_core): ?>
                                            <!-- Core categories have limited editing -->
                                            <span class="text-xs text-gray-500 italic mr-2">Protected Core Category</span>
                                            <button type="button" class="toggle-status-btn <?php echo $category->is_active ? 'text-green-600' : 'text-gray-400'; ?>" data-id="<?php echo $category->id; ?>" title="<?php echo $category->is_active ? 'Active - Click to deactivate' : 'Inactive - Click to activate'; ?>">
                                                <i class="fas fa-<?php echo $category->is_active ? 'toggle-on' : 'toggle-off'; ?>"></i>
                                            </button>
                                        <?php else: ?>
                                            <!-- Custom categories have full editing -->
                                            <button type="button" class="edit-category-btn text-blue-600 hover:text-blue-800 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>" data-id="<?php echo $category->id; ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="toggle-status-btn <?php echo $category->is_active ? 'text-green-600' : 'text-gray-400'; ?>" data-id="<?php echo $category->id; ?>" title="<?php echo $category->is_active ? 'Active - Click to deactivate' : 'Inactive - Click to activate'; ?>">
                                                <i class="fas fa-<?php echo $category->is_active ? 'toggle-on' : 'toggle-off'; ?>"></i>
                                            </button>
                                            <button type="button" class="delete-category-btn text-red-600 hover:text-red-800 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>" data-id="<?php echo $category->id; ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>"><?php echo htmlspecialchars($category->description); ?></p>
                                <div class="flex items-center justify-between text-xs">
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded <?php echo !$category->is_active ? 'bg-gray-100 text-gray-500' : ''; ?>">
                                        <?php echo $category->requires_member ? 'Requires Member' : 'No Member Required'; ?>
                                    </span>
                                    <span class="text-gray-500">Order: <?php echo $category->sort_order; ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="category-card border-2 border-dashed border-blue-300 rounded-lg p-4 flex items-center justify-center">
                            <button type="button" class="add-category-btn text-blue-600 hover:text-blue-800" data-type="member_payments">
                                <i class="fas fa-plus text-2xl mb-2"></i>
                                <p class="text-sm">Add Category</p>
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- General Income Tab -->
            <div id="general-income-content" class="tab-content hidden">
                <div class="mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">General Income Categories</h3>
                    <p class="text-gray-600 text-sm">Categories for general church income that don't require member tracking</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php if (empty($generalIncomeCategories)) : ?>
                        <div class="col-span-full text-center py-8">
                            <i class="fas fa-plus-circle text-gray-400 text-4xl mb-3"></i>
                            <p class="text-gray-500">No custom general income categories yet.</p>
                            <button type="button" class="add-category-btn mt-3 text-primary hover:text-primary-dark" data-type="general_income">
                                <i class="fas fa-plus mr-1"></i> Add your first category
                            </button>
                        </div>
                    <?php else : ?>
                        <?php foreach ($generalIncomeCategories as $category) : ?>
                            <div class="category-card bg-green-50 border border-green-200 rounded-lg p-4 <?php echo !$category->is_active ? 'opacity-60 bg-gray-50 border-gray-300' : ''; ?> <?php echo isset($category->is_core) && $category->is_core ? 'border-l-4 border-l-yellow-500' : ''; ?>">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center">
                                        <i class="<?php echo $category->icon; ?> text-green-600 mr-2 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>"></i>
                                        <span class="font-medium text-green-800 <?php echo !$category->is_active ? 'text-gray-500' : ''; ?>">
                                            <?php echo htmlspecialchars($category->label); ?>
                                            <?php if (isset($category->is_core) && $category->is_core): ?>
                                                <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full ml-2">Core</span>
                                            <?php endif; ?>
                                            <?php if (!$category->is_active): ?>
                                                <span class="text-xs text-red-500 font-normal ml-2">(Inactive)</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <?php if (isset($category->is_core) && $category->is_core): ?>
                                            <!-- Core categories have limited editing -->
                                            <span class="text-xs text-gray-500 italic mr-2">Protected Core Category</span>
                                            <button type="button" class="toggle-status-btn <?php echo $category->is_active ? 'text-green-600' : 'text-gray-400'; ?>" data-id="<?php echo $category->id; ?>" title="<?php echo $category->is_active ? 'Active - Click to deactivate' : 'Inactive - Click to activate'; ?>">
                                                <i class="fas fa-<?php echo $category->is_active ? 'toggle-on' : 'toggle-off'; ?>"></i>
                                            </button>
                                        <?php else: ?>
                                            <!-- Custom categories have full editing -->
                                            <button type="button" class="edit-category-btn text-green-600 hover:text-green-800 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>" data-id="<?php echo $category->id; ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="toggle-status-btn <?php echo $category->is_active ? 'text-green-600' : 'text-gray-400'; ?>" data-id="<?php echo $category->id; ?>" title="<?php echo $category->is_active ? 'Active - Click to deactivate' : 'Inactive - Click to activate'; ?>">
                                                <i class="fas fa-<?php echo $category->is_active ? 'toggle-on' : 'toggle-off'; ?>"></i>
                                            </button>
                                            <button type="button" class="delete-category-btn text-red-600 hover:text-red-800 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>" data-id="<?php echo $category->id; ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>"><?php echo htmlspecialchars($category->description); ?></p>
                                <div class="flex items-center justify-between text-xs">
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded <?php echo !$category->is_active ? 'bg-gray-100 text-gray-500' : ''; ?>">General Income</span>
                                    <span class="text-gray-500">Order: <?php echo $category->sort_order; ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="category-card border-2 border-dashed border-green-300 rounded-lg p-4 flex items-center justify-center">
                            <button type="button" class="add-category-btn text-green-600 hover:text-green-800" data-type="general_income">
                                <i class="fas fa-plus text-2xl mb-2"></i>
                                <p class="text-sm">Add Category</p>
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Expenses Tab -->
            <div id="expenses-content" class="tab-content hidden">
                <div class="mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Expense Categories</h3>
                    <p class="text-gray-600 text-sm">Categories for church expenses and operational costs</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php if (empty($expenseCategories)) : ?>
                        <div class="col-span-full text-center py-8">
                            <i class="fas fa-plus-circle text-gray-400 text-4xl mb-3"></i>
                            <p class="text-gray-500">No custom expense categories yet.</p>
                            <button type="button" class="add-category-btn mt-3 text-primary hover:text-primary-dark" data-type="expenses">
                                <i class="fas fa-plus mr-1"></i> Add your first category
                            </button>
                        </div>
                    <?php else : ?>
                        <?php foreach ($expenseCategories as $category) : ?>
                            <div class="category-card bg-red-50 border border-red-200 rounded-lg p-4 <?php echo !$category->is_active ? 'opacity-60 bg-gray-50 border-gray-300' : ''; ?> <?php echo isset($category->is_core) && $category->is_core ? 'border-l-4 border-l-yellow-500' : ''; ?>">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center">
                                        <i class="<?php echo $category->icon; ?> text-red-600 mr-2 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>"></i>
                                        <span class="font-medium text-red-800 <?php echo !$category->is_active ? 'text-gray-500' : ''; ?>">
                                            <?php echo htmlspecialchars($category->label); ?>
                                            <?php if (isset($category->is_core) && $category->is_core): ?>
                                                <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full ml-2">Core</span>
                                            <?php endif; ?>
                                            <?php if (!$category->is_active): ?>
                                                <span class="text-xs text-red-500 font-normal ml-2">(Inactive)</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <?php if (isset($category->is_core) && $category->is_core): ?>
                                            <!-- Core categories have limited editing -->
                                            <span class="text-xs text-gray-500 italic mr-2">Protected Core Category</span>
                                            <button type="button" class="toggle-status-btn <?php echo $category->is_active ? 'text-green-600' : 'text-gray-400'; ?>" data-id="<?php echo $category->id; ?>" title="<?php echo $category->is_active ? 'Active - Click to deactivate' : 'Inactive - Click to activate'; ?>">
                                                <i class="fas fa-<?php echo $category->is_active ? 'toggle-on' : 'toggle-off'; ?>"></i>
                                            </button>
                                        <?php else: ?>
                                            <!-- Custom categories have full editing -->
                                            <button type="button" class="edit-category-btn text-red-600 hover:text-red-800 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>" data-id="<?php echo $category->id; ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="toggle-status-btn <?php echo $category->is_active ? 'text-green-600' : 'text-gray-400'; ?>" data-id="<?php echo $category->id; ?>" title="<?php echo $category->is_active ? 'Active - Click to deactivate' : 'Inactive - Click to activate'; ?>">
                                                <i class="fas fa-<?php echo $category->is_active ? 'toggle-on' : 'toggle-off'; ?>"></i>
                                            </button>
                                            <button type="button" class="delete-category-btn text-red-600 hover:text-red-800 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>" data-id="<?php echo $category->id; ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-600 mb-2 <?php echo !$category->is_active ? 'text-gray-400' : ''; ?>"><?php echo htmlspecialchars($category->description); ?></p>
                                <div class="flex items-center justify-between text-xs">
                                    <span class="bg-red-100 text-red-800 px-2 py-1 rounded <?php echo !$category->is_active ? 'bg-gray-100 text-gray-500' : ''; ?>">Expense</span>
                                    <span class="text-gray-500">Order: <?php echo $category->sort_order; ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="category-card border-2 border-dashed border-red-300 rounded-lg p-4 flex items-center justify-center">
                            <button type="button" class="add-category-btn text-red-600 hover:text-red-800" data-type="expenses">
                                <i class="fas fa-plus text-2xl mb-2"></i>
                                <p class="text-sm">Add Category</p>
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Category Modal -->
<div id="categoryModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-4">
    <div class="relative bg-white rounded-xl shadow-2xl w-full max-w-lg transform transition-all duration-300 scale-95 opacity-0" id="modalContent">
        <!-- Modal Header -->
        <div class="bg-primary rounded-t-xl px-6 py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white" id="modalTitle">Add Custom Category</h3>
                <button type="button" id="closeModal" class="text-white hover:text-gray-200 transition-colors duration-200 p-1 rounded-full hover:bg-white hover:bg-opacity-20">
                    ×
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="px-6 py-6">
            <form id="categoryForm" method="POST" action="<?php echo BASE_URL; ?>finance/categories/store">
                <input type="hidden" id="categoryId" name="id">
                <input type="hidden" id="formAction" name="action" value="store">

                <div class="space-y-5">
                    <!-- Category Type -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-700 mb-3">
                            Category Type *
                        </label>
                        <select id="categoryType" name="category_type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white shadow-sm" required>
                            <option value="">Select category type</option>
                            <option value="member_payments">Member Payments</option>
                            <option value="general_income">General Income</option>
                            <option value="expenses">Expenses</option>
                        </select>
                    </div>

                    <!-- Category Name -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-700 mb-3">
                            Category Name *
                        </label>
                        <input type="text" id="categoryName" name="name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white shadow-sm" placeholder="e.g., building_fund" required>
                        <p class="text-xs text-gray-500 mt-2">
                            Use lowercase letters, numbers, and underscores only
                        </p>
                    </div>

                    <!-- Category Label -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-700 mb-3">
                            Display Label *
                        </label>
                        <input type="text" id="categoryLabel" name="label" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white shadow-sm" placeholder="e.g., Building Fund" required>
                        <p class="text-xs text-gray-500 mt-2">
                            This is what users will see in the form
                        </p>
                    </div>

                    <!-- Icon (hidden field with default value) -->
                    <input type="hidden" id="categoryIcon" name="icon" value="fas fa-circle">

                    <!-- Description -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-gray-700 mb-3">
                            Description
                        </label>
                        <textarea id="categoryDescription" name="description" rows="3" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white shadow-sm resize-none" placeholder="Brief description of this category..."></textarea>
                    </div>

                    <!-- Requires Member (only for member_payments) -->
                    <div id="requiresMemberDiv" class="form-group hidden">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" id="requiresMember" name="requires_member" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                <span class="ml-3 text-sm font-medium text-blue-800">
                                    Requires member selection
                                </span>
                            </label>
                            <p class="text-xs text-blue-600 mt-2 ml-7">
                                Check if this category requires tracking which member made the payment
                            </p>
                        </div>
                    </div>

                    <!-- Sort Order (for editing) -->
                    <div id="sortOrderDiv" class="form-group hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-3">
                            Sort Order
                        </label>
                        <input type="number" id="sortOrder" name="sort_order" min="0" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white shadow-sm">
                    </div>

                    <!-- Is Active (for editing) -->
                    <div id="isActiveDiv" class="form-group hidden">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" id="isActive" name="is_active" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" checked>
                                <span class="ml-3 text-sm font-medium text-green-800">
                                    Active
                                </span>
                            </label>
                            <p class="text-xs text-green-600 mt-2 ml-7">
                                Uncheck to hide this category from the finance form
                            </p>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-6 py-4 rounded-b-xl border-t border-gray-200">
            <div class="flex justify-end space-x-3">
                <button type="button" id="cancelBtn" class="px-6 py-2.5 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-colors duration-200 font-medium">
                    Cancel
                </button>
                <button type="submit" form="categoryForm" class="px-6 py-2.5 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 font-medium">
                    <span id="submitBtnText">Add Category</span>
                </button>
            </div>
        </div>
    </div>
</div>

<?php
// Clear form data from session
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>

<!-- Delete Confirmation Modal -->
<div id="delete-category-modal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-4">
    <div class="relative bg-white rounded-xl shadow-2xl w-full max-w-md transform transition-all duration-300 scale-95 opacity-0" id="deleteModalContent">
        <!-- Modal Header -->
        <div class="bg-red-600 rounded-t-xl px-6 py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Confirm Category Deletion
                </h3>
                <button type="button" id="closeDeleteModal" class="text-white hover:text-gray-200 transition-colors duration-200 p-1 rounded-full hover:bg-white hover:bg-opacity-20">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="px-6 py-6">
            <!-- Warning Section -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-red-500 mt-1 mr-3"></i>
                    <div>
                        <h4 class="text-red-800 font-semibold mb-2">Warning: This action cannot be undone!</h4>
                        <p class="text-red-700 text-sm mb-2">You are about to delete the category:</p>
                        <p class="text-red-900 font-bold text-lg" id="delete-category-name"></p>
                    </div>
                </div>
            </div>

            <!-- Information Section -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-yellow-600 mt-1 mr-3"></i>
                    <div class="text-yellow-800 text-sm">
                        <p class="mb-2"><strong>What will happen:</strong></p>
                        <ul class="list-disc list-inside space-y-1">
                            <li>If this category has transactions, it will be <strong>deactivated</strong> to preserve data</li>
                            <li>If this category has no transactions, it will be <strong>permanently deleted</strong></li>
                            <li>This action affects the category's visibility in the application</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Confirmation Input -->
            <div class="mb-4">
                <label for="delete-confirmation-input" class="block text-sm font-medium text-gray-700 mb-2">
                    To confirm deletion, type <span class="font-bold text-red-600">"delete"</span> in the box below:
                </label>
                <input type="text"
                       id="delete-confirmation-input"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                       placeholder="Type 'delete' to confirm"
                       autocomplete="off">
                <p class="text-xs text-gray-500 mt-1">This confirmation helps prevent accidental deletions.</p>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-6 py-4 rounded-b-xl border-t border-gray-200">
            <div class="flex justify-end space-x-3">
                <button type="button"
                        id="cancel-delete-btn"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>Cancel
                </button>
                <button type="button"
                        id="confirm-delete-btn"
                        disabled
                        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed">
                    <i class="fas fa-trash mr-2"></i>Delete Category
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.category-tab {
    transition: all 0.3s ease;
}

.category-tab.active {
    border-color: #3B82F6 !important;
    color: #3B82F6 !important;
}

.tab-content {
    transition: opacity 0.3s ease;
}

.tab-content.hidden {
    display: none;
}

.category-card {
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Enhanced Modal Styles */
#categoryModal.show #modalContent {
    transform: scale(1);
    opacity: 1;
}

.form-group {
    position: relative;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    background-color: white;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    background-color: white;
    border-color: #9CA3AF;
}

/* Standard checkboxes - no custom toggle styles needed */

/* Icon field is now hidden - no preview styles needed */

/* Enhanced Button Styles */
button[type="submit"] {
    background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

button[type="submit"]:hover {
    background: linear-gradient(135deg, #1D4ED8 0%, #1E40AF 100%);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
}

/* Form Field Focus Effects */
.form-group label {
    transition: color 0.2s ease;
}

.form-group:focus-within label {
    color: #3B82F6;
}

.form-group:focus-within i {
    color: #3B82F6;
}

/* Modal Animation */
#categoryModal {
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
}

/* Enhanced Form Styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.75rem;
    display: block;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: #9CA3AF;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group:focus-within label {
    color: #3B82F6;
}

/* Responsive Design */
@media (max-width: 640px) {
    #modalContent {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.75rem 1rem;
        padding-left: 2.5rem;
    }
}
</style>

<script>
// Category Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const categoryTabs = document.querySelectorAll('.category-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    // Modal elements
    const modal = document.getElementById('categoryModal');
    const modalContent = document.getElementById('modalContent');
    const modalTitle = document.getElementById('modalTitle');
    const categoryForm = document.getElementById('categoryForm');
    const closeModalBtn = document.getElementById('closeModal');
    const cancelBtn = document.getElementById('cancelBtn');
    const submitBtnText = document.getElementById('submitBtnText');

    // Icon field (hidden)
    const categoryIcon = document.getElementById('categoryIcon');

    // Form elements
    const categoryId = document.getElementById('categoryId');
    const formAction = document.getElementById('formAction');
    const categoryType = document.getElementById('categoryType');
    const categoryName = document.getElementById('categoryName');
    const categoryLabel = document.getElementById('categoryLabel');
    const categoryDescription = document.getElementById('categoryDescription');
    const requiresMember = document.getElementById('requiresMember');
    const requiresMemberDiv = document.getElementById('requiresMemberDiv');
    const sortOrder = document.getElementById('sortOrder');
    const sortOrderDiv = document.getElementById('sortOrderDiv');
    const isActive = document.getElementById('isActive');
    const isActiveDiv = document.getElementById('isActiveDiv');

    // Initialize tab switching
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            switchTab(targetTab);
        });
    });

    function switchTab(targetTab) {
        // Update tab buttons
        categoryTabs.forEach(tab => {
            tab.classList.remove('active', 'border-primary', 'text-primary');
            tab.classList.add('border-transparent', 'text-gray-500');
        });

        const activeTab = document.querySelector(`[data-tab="${targetTab}"]`);
        if (activeTab) {
            activeTab.classList.add('active', 'border-primary', 'text-primary');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
        }

        // Update tab contents
        tabContents.forEach(content => {
            content.classList.add('hidden');
        });

        const activeContent = document.getElementById(`${targetTab}-content`);
        if (activeContent) {
            activeContent.classList.remove('hidden');
        }
    }

    // Modal functionality
    function openModal(mode = 'add', categoryData = null) {
        if (mode === 'add') {
            modalTitle.textContent = 'Add Custom Category';
            submitBtnText.textContent = 'Add Category';
            formAction.value = 'store';
            categoryForm.action = '<?php echo BASE_URL; ?>finance/categories/store';
            resetForm();
        } else {
            modalTitle.textContent = 'Edit Custom Category';
            submitBtnText.textContent = 'Update Category';
            formAction.value = 'update';
            categoryForm.action = '<?php echo BASE_URL; ?>finance/categories/update';
            populateForm(categoryData);
        }

        modal.classList.remove('hidden');
        modal.classList.add('show');

        // Animate modal content
        setTimeout(() => {
            modalContent.style.transform = 'scale(1)';
            modalContent.style.opacity = '1';
        }, 10);

        // Focus first input
        setTimeout(() => {
            categoryType.focus();
        }, 300);
    }

    function closeModal() {
        modalContent.style.transform = 'scale(0.95)';
        modalContent.style.opacity = '0';

        setTimeout(() => {
            modal.classList.add('hidden');
            modal.classList.remove('show');
            resetForm();
        }, 300);
    }

    function resetForm() {
        categoryForm.reset();
        categoryId.value = '';
        requiresMemberDiv.classList.add('hidden');
        sortOrderDiv.classList.add('hidden');
        isActiveDiv.classList.add('hidden');
        requiresMember.checked = false;
        isActive.checked = true;

        // Reset toggle switches
        updateToggleSwitch(requiresMember, false);
        updateToggleSwitch(isActive, true);

        // Set default icon
        categoryIcon.value = 'fas fa-circle';

        // Reset auto-generation
        categoryName.dataset.autoGenerated = 'true';
    }

    function populateForm(data) {
        categoryId.value = data.id;
        categoryType.value = data.category_type;
        categoryName.value = data.name;
        categoryLabel.value = data.label;
        categoryIcon.value = data.icon;
        categoryDescription.value = data.description;
        requiresMember.checked = data.requires_member == 1;
        sortOrder.value = data.sort_order;
        isActive.checked = data.is_active == 1;

        // Update toggle switches
        updateToggleSwitch(requiresMember, data.requires_member == 1);
        updateToggleSwitch(isActive, data.is_active == 1);

        // Set icon value (hidden field)
        categoryIcon.value = data.icon || 'fas fa-circle';

        // Show additional fields for editing
        sortOrderDiv.classList.remove('hidden');
        isActiveDiv.classList.remove('hidden');

        // Show requires member for member payments
        if (data.category_type === 'member_payments') {
            requiresMemberDiv.classList.remove('hidden');
        }

        // Stop auto-generation since this is existing data
        categoryName.dataset.autoGenerated = 'false';
    }

    // Event listeners
    document.getElementById('add-category-btn').addEventListener('click', () => openModal('add'));

    document.querySelectorAll('.add-category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const type = this.dataset.type;
            openModal('add');
            categoryType.value = type;

            if (type === 'member_payments') {
                requiresMemberDiv.classList.remove('hidden');
                requiresMember.checked = true;
            }
        });
    });

    closeModalBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Category type change handler
    categoryType.addEventListener('change', function() {
        if (this.value === 'member_payments') {
            requiresMemberDiv.classList.remove('hidden');
            requiresMember.checked = true;
            updateToggleSwitch(requiresMember, true);
        } else {
            requiresMemberDiv.classList.add('hidden');
            requiresMember.checked = false;
            updateToggleSwitch(requiresMember, false);
        }
    });

    // Icon field is now hidden - no preview needed

    // Checkbox functionality (simplified for standard checkboxes)
    function updateToggleSwitch(checkbox, checked) {
        if (checkbox) {
            checkbox.checked = checked;
        }
    }

    // Standard checkboxes work automatically - no custom handling needed

    // Auto-generate category name from label
    categoryLabel.addEventListener('input', function() {
        if (!categoryName.value || categoryName.dataset.autoGenerated === 'true') {
            const generatedName = this.value
                .toLowerCase()
                .replace(/[^\w\s]/g, '') // Remove special characters except emojis
                .replace(/\s+/g, '_') // Replace spaces with underscores
                .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
                .substring(0, 50); // Limit length

            categoryName.value = generatedName;
            categoryName.dataset.autoGenerated = 'true';
        }
    });

    // Stop auto-generation when user manually edits name
    categoryName.addEventListener('input', function() {
        this.dataset.autoGenerated = 'false';
    });

    // Edit category buttons
    document.querySelectorAll('.edit-category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const categoryId = this.dataset.id;

            // Fetch category data
            fetch(`<?php echo BASE_URL; ?>finance/categories/get?id=${categoryId}`)
                .then(response => response.json())
                .then(data => {
                    openModal('edit', data);
                })
                .catch(error => {
                    console.error('Error fetching category data:', error);
                    alert('Error loading category data');
                });
        });
    });

    // Delete Modal Functionality
    const deleteModal = document.getElementById('delete-category-modal');
    const deleteModalContent = document.getElementById('deleteModalContent');
    const deleteCategoryName = document.getElementById('delete-category-name');
    const deleteConfirmationInput = document.getElementById('delete-confirmation-input');
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
    const closeDeleteModalBtn = document.getElementById('closeDeleteModal');

    function openDeleteModal(category) {
        deleteCategoryName.textContent = category.name;
        deleteConfirmationInput.value = '';
        confirmDeleteBtn.disabled = true;

        deleteModal.classList.remove('hidden');

        // Animate modal content
        setTimeout(() => {
            deleteModalContent.style.transform = 'scale(1)';
            deleteModalContent.style.opacity = '1';
        }, 10);

        // Focus the input
        setTimeout(() => {
            deleteConfirmationInput.focus();
        }, 300);
    }

    function closeDeleteModal() {
        deleteModalContent.style.transform = 'scale(0.95)';
        deleteModalContent.style.opacity = '0';

        setTimeout(() => {
            deleteModal.classList.add('hidden');
            deleteConfirmationInput.value = '';
            confirmDeleteBtn.disabled = true;
            categoryToDelete = null;
        }, 300);
    }

    function confirmDelete() {
        if (categoryToDelete && deleteConfirmationInput.value.toLowerCase() === 'delete') {
            window.location.href = `<?php echo BASE_URL; ?>finance/categories/delete?id=${categoryToDelete.id}`;
        }
    }

    // Delete confirmation input handler
    deleteConfirmationInput.addEventListener('input', function() {
        const inputValue = this.value.toLowerCase();
        confirmDeleteBtn.disabled = inputValue !== 'delete';

        if (inputValue === 'delete') {
            confirmDeleteBtn.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
            confirmDeleteBtn.classList.add('bg-red-600', 'hover:bg-red-700');
        } else {
            confirmDeleteBtn.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
            confirmDeleteBtn.classList.remove('bg-red-600', 'hover:bg-red-700');
        }
    });

    // Delete modal event listeners
    confirmDeleteBtn.addEventListener('click', confirmDelete);
    cancelDeleteBtn.addEventListener('click', closeDeleteModal);
    closeDeleteModalBtn.addEventListener('click', closeDeleteModal);

    // Close delete modal when clicking outside
    deleteModal.addEventListener('click', function(e) {
        if (e.target === deleteModal) {
            closeDeleteModal();
        }
    });

    // Handle Enter key in confirmation input
    deleteConfirmationInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && this.value.toLowerCase() === 'delete') {
            confirmDelete();
        }
    });

    // Toggle status buttons
    document.querySelectorAll('.toggle-status-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const categoryId = this.dataset.id;
            const icon = this.querySelector('i');

            const formData = new FormData();
            formData.append('id', categoryId);

            fetch(`<?php echo BASE_URL; ?>finance/categories/toggle-status`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message and refresh page to update visual indicators
                    const statusText = data.new_status ? 'activated' : 'deactivated';
                    alert(`Category ${statusText} successfully!`);

                    // Refresh the page to show updated visual indicators
                    window.location.reload();
                } else {
                    alert('Error updating category status: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error toggling status:', error);
                alert('Error updating category status');
            });
        });
    });

    // Delete category buttons
    let categoryToDelete = null;

    document.querySelectorAll('.delete-category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            categoryToDelete = {
                id: this.dataset.id,
                name: this.closest('.category-card').querySelector('.font-medium').textContent.trim().replace(' (Inactive)', '')
            };

            openDeleteModal(categoryToDelete);
        });
    });
});
</script>
