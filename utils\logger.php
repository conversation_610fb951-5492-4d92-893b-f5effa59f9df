<?php
/**
 * Comprehensive Logging System
 * Provides structured logging with proper levels and context
 */

class AppLogger {
    const EMERGENCY = 'emergency';
    const ALERT = 'alert';
    const CRITICAL = 'critical';
    const ERROR = 'error';
    const WARNING = 'warning';
    const NOTICE = 'notice';
    const INFO = 'info';
    const DEBUG = 'debug';

    private static $instance = null;
    private $logFile;
    private $logLevel;
    private $context = [];

    private function __construct() {
        $this->logFile = BASE_DIR . '/logs/app.log';
        $this->logLevel = defined('LOG_LEVEL') ? LOG_LEVEL : self::INFO;

        // Create logs directory if it doesn't exist
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Set global context for all log entries
     */
    public function setContext(array $context): void {
        $this->context = array_merge($this->context, $context);
    }

    /**
     * Log emergency message
     */
    public function emergency(string $message, array $context = []): void {
        $this->log(self::EMERGENCY, $message, $context);
    }

    /**
     * Log alert message
     */
    public function alert(string $message, array $context = []): void {
        $this->log(self::ALERT, $message, $context);
    }

    /**
     * Log critical message
     */
    public function critical(string $message, array $context = []): void {
        $this->log(self::CRITICAL, $message, $context);
    }

    /**
     * Log error message
     */
    public function error(string $message, array $context = []): void {
        $this->log(self::ERROR, $message, $context);
    }

    /**
     * Log warning message
     */
    public function warning(string $message, array $context = []): void {
        $this->log(self::WARNING, $message, $context);
    }

    /**
     * Log notice message
     */
    public function notice(string $message, array $context = []): void {
        $this->log(self::NOTICE, $message, $context);
    }

    /**
     * Log info message
     */
    public function info(string $message, array $context = []): void {
        $this->log(self::INFO, $message, $context);
    }

    /**
     * Log debug message
     */
    public function debug(string $message, array $context = []): void {
        $this->log(self::DEBUG, $message, $context);
    }

    /**
     * Log security event
     */
    public function security(string $message, array $context = []): void {
        $context['event_type'] = 'security';
        $context['ip'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $context['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $context['user_id'] = $_SESSION['user_id'] ?? null;

        $this->log(self::WARNING, $message, $context);
    }

    /**
     * Log database operation
     */
    public function database(string $message, array $context = []): void {
        $context['event_type'] = 'database';
        $this->log(self::INFO, $message, $context);
    }

    /**
     * Log API request
     */
    public function api(string $message, array $context = []): void {
        $context['event_type'] = 'api';
        $context['method'] = $_SERVER['REQUEST_METHOD'] ?? 'unknown';
        $context['uri'] = $_SERVER['REQUEST_URI'] ?? 'unknown';

        $this->log(self::INFO, $message, $context);
    }

    /**
     * Main logging method
     */
    private function log(string $level, string $message, array $context = []): void {
        // Check if we should log this level
        if (!$this->shouldLog($level)) {
            return;
        }

        // Merge contexts
        $fullContext = array_merge($this->context, $context);

        // Create log entry
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => strtoupper($level),
            'message' => $message,
            'context' => $fullContext,
            'memory_usage' => memory_get_usage(true),
            'execution_time' => microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true))
        ];

        // Format for file
        $logLine = json_encode($logEntry, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . PHP_EOL;

        // Write to file
        file_put_contents($this->logFile, $logLine, FILE_APPEND | LOCK_EX);

        // Also log to PHP error log for critical issues
        if (in_array($level, [self::EMERGENCY, self::ALERT, self::CRITICAL, self::ERROR])) {
            error_log("[$level] $message");
        }
    }

    /**
     * Check if we should log this level
     */
    private function shouldLog(string $level): bool {
        $levels = [
            self::EMERGENCY => 0,
            self::ALERT => 1,
            self::CRITICAL => 2,
            self::ERROR => 3,
            self::WARNING => 4,
            self::NOTICE => 5,
            self::INFO => 6,
            self::DEBUG => 7
        ];

        // Ensure logLevel is a valid string key
        $logLevel = is_string($this->logLevel) ? $this->logLevel : self::INFO;
        $currentLevel = $levels[$logLevel] ?? 6;
        $messageLevel = $levels[$level] ?? 6;

        return $messageLevel <= $currentLevel;
    }

    /**
     * Log exception with full context
     */
    public function logException(Throwable $exception, array $additionalContext = []): void {
        $context = array_merge([
            'exception_class' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'previous' => $exception->getPrevious() ? get_class($exception->getPrevious()) : null
        ], $additionalContext);

        $this->error($exception->getMessage(), $context);
    }
}

// Backward compatibility class
class Logger {
    private $appLogger;

    public function __construct() {
        $this->appLogger = AppLogger::getInstance();
    }

    public function info($message) {
        $this->appLogger->info($message);
    }

    public function error($message) {
        $this->appLogger->error($message);
    }

    public function warning($message) {
        $this->appLogger->warning($message);
    }

    public function debug($message) {
        $this->appLogger->debug($message);
    }
}

// Global logging functions for backward compatibility (only if not already defined)
if (!function_exists('log_info')) {
    function log_info(string $message, array $context = []): void {
        AppLogger::getInstance()->info($message, $context);
    }
}

if (!function_exists('log_error')) {
    function log_error(string $message, array $context = []): void {
        AppLogger::getInstance()->error($message, $context);
    }
}

if (!function_exists('log_warning')) {
    function log_warning(string $message, array $context = []): void {
        AppLogger::getInstance()->warning($message, $context);
    }
}

if (!function_exists('log_debug')) {
    function log_debug(string $message, array $context = []): void {
        AppLogger::getInstance()->debug($message, $context);
    }
}

if (!function_exists('log_security')) {
    function log_security(string $message, array $context = []): void {
        AppLogger::getInstance()->security($message, $context);
    }
}
