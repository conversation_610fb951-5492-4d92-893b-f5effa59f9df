
/* ICGC <PERSON> Final Theme Overrides */



/* Force white backgrounds everywhere else */
body, main, .container, .card:not(.gradient-card), .dashboard-card, .stats-card {
    background-color: white !important;
}

/* Ensure gradient cards keep their gradient backgrounds */
.gradient-card {
    background-color: transparent !important;
}

.bg-gray-50, .bg-gray-100, .bg-gray-200, .bg-gray-300 {
    background-color: white !important;
}

/* Tables with white background */
table, th, td {
    background-color: white !important;
}

/* Forms with white background */
.form-control, .form-select, .form-input {
    background-color: white !important;
}

/* Buttons can still use the theme color */
.btn-primary {
    background-color: #3F7D58 !important;
    border-color: #3F7D58 !important;
}

.btn-primary:hover {
    background-color: #2c5a3f !important;
    border-color: #2c5a3f !important;
}

/* ===== SIDEBAR STYLING REMOVED ===== */
/* All sidebar styles removed - using pure Tailwind CSS implementation */
