<?php
/**
 * Phone Number Utilities for International Support
 * 
 * Handles phone number normalization, validation, and formatting
 * for global church management system usage.
 */

class PhoneNumberUtils {

    /**
     * Default country for phone number normalization
     * Can be configured based on church location
     */
    private static $default_country = null;

    /**
     * Common country codes and their patterns
     */
    /**
     * Cached country patterns from JSON file
     */
    private static $json_patterns_cache = null;

    /**
     * Load country patterns from JSON file with fallback to inline patterns
     *
     * @return array Country patterns
     */
    private static function loadCountryPatterns() {
        if (self::$json_patterns_cache !== null) {
            return self::$json_patterns_cache;
        }

        // Try to load from JSON file first
        $json_file = __DIR__ . '/phone_patterns.json';
        if (file_exists($json_file)) {
            try {
                $json_content = file_get_contents($json_file);
                $json_data = json_decode($json_content, true);

                if ($json_data && isset($json_data['patterns'])) {
                    // Convert JSON format to internal format
                    $patterns = [];
                    foreach ($json_data['patterns'] as $country => $info) {
                        $patterns[$country] = [
                            'code' => $info['code'],
                            'pattern' => '/' . $info['pattern'] . '/',
                            'length' => $info['length']
                        ];
                    }

                    self::$json_patterns_cache = $patterns;
                    // Log successful JSON loading
                    if (class_exists('AppLogger')) {
                        AppLogger::getInstance()->info('PhoneNumberUtils: Successfully loaded patterns from JSON file', [
                            'patterns_count' => count($patterns),
                            'source' => 'JSON'
                        ]);
                    }
                    return $patterns;
                }
            } catch (Exception $e) {
                // Log JSON loading failure
                if (class_exists('AppLogger')) {
                    AppLogger::getInstance()->warning('PhoneNumberUtils: Failed to load JSON patterns, using fallback', [
                        'error' => $e->getMessage(),
                        'fallback' => 'inline_patterns'
                    ]);
                } else {
                    error_log('PhoneNumberUtils: Failed to load JSON patterns, using fallback: ' . $e->getMessage());
                }
            }
        }

        // Fallback to existing inline patterns
        self::$json_patterns_cache = self::getInlinePatterns();
        return self::$json_patterns_cache;
    }

    /**
     * Get inline patterns (fallback when JSON fails)
     *
     * @return array Inline country patterns
     */
    private static function getInlinePatterns() {
        return self::$country_codes;
    }

    private static $country_codes = [
        // Africa
        'DZ' => ['code' => '+213', 'pattern' => '/^0?([5-7]\d{8})$/', 'length' => 9],
        'AO' => ['code' => '+244', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'BJ' => ['code' => '+229', 'pattern' => '/^0?([2-9]\d{7})$/', 'length' => 8],
        'BW' => ['code' => '+267', 'pattern' => '/^0?([7]\d{7})$/', 'length' => 8],
        'BF' => ['code' => '+226', 'pattern' => '/^0?([7]\d{7})$/', 'length' => 8],
        'BI' => ['code' => '+257', 'pattern' => '/^0?([7-9]\d{7})$/', 'length' => 8],
        'CM' => ['code' => '+237', 'pattern' => '/^0?([6-7]\d{8})$/', 'length' => 9],
        'CV' => ['code' => '+238', 'pattern' => '/^0?([5-9]\d{6})$/', 'length' => 7],
        'CF' => ['code' => '+236', 'pattern' => '/^0?([7]\d{7})$/', 'length' => 8],
        'TD' => ['code' => '+235', 'pattern' => '/^0?([6-9]\d{7})$/', 'length' => 8],
        'KM' => ['code' => '+269', 'pattern' => '/^0?([3-7]\d{6})$/', 'length' => 7],
        'CG' => ['code' => '+242', 'pattern' => '/^0?([0-9]\d{7})$/', 'length' => 8],
        'CD' => ['code' => '+243', 'pattern' => '/^0?([8-9]\d{8})$/', 'length' => 9],
        'CI' => ['code' => '+225', 'pattern' => '/^0?([0-9]\d{7})$/', 'length' => 8],
        'DJ' => ['code' => '+253', 'pattern' => '/^0?([7]\d{7})$/', 'length' => 8],
        'EG' => ['code' => '+20', 'pattern' => '/^0?([1]\d{8,9})$/', 'length' => [9, 10]],
        'GQ' => ['code' => '+240', 'pattern' => '/^0?([2-9]\d{8})$/', 'length' => 9],
        'ER' => ['code' => '+291', 'pattern' => '/^0?([7]\d{6})$/', 'length' => 7],
        'ET' => ['code' => '+251', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'GA' => ['code' => '+241', 'pattern' => '/^0?([0-9]\d{7})$/', 'length' => 8],
        'GM' => ['code' => '+220', 'pattern' => '/^0?([2-9]\d{6})$/', 'length' => 7],
        'GH' => ['code' => '+233', 'pattern' => '/^0?([2-5]\d{8})$/', 'length' => 9],
        'GN' => ['code' => '+224', 'pattern' => '/^0?([6-7]\d{7})$/', 'length' => 8],
        'GW' => ['code' => '+245', 'pattern' => '/^0?([5-9]\d{6})$/', 'length' => 7],
        'KE' => ['code' => '+254', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'LS' => ['code' => '+266', 'pattern' => '/^0?([5-6]\d{7})$/', 'length' => 8],
        'LR' => ['code' => '+231', 'pattern' => '/^0?([4-9]\d{7})$/', 'length' => 8],
        'LY' => ['code' => '+218', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'MG' => ['code' => '+261', 'pattern' => '/^0?([3]\d{8})$/', 'length' => 9],
        'MW' => ['code' => '+265', 'pattern' => '/^0?([1-9]\d{7})$/', 'length' => 8],
        'ML' => ['code' => '+223', 'pattern' => '/^0?([6-9]\d{7})$/', 'length' => 8],
        'MR' => ['code' => '+222', 'pattern' => '/^0?([2-4]\d{7})$/', 'length' => 8],
        'MU' => ['code' => '+230', 'pattern' => '/^0?([5]\d{7})$/', 'length' => 8],
        'MA' => ['code' => '+212', 'pattern' => '/^0?([5-7]\d{8})$/', 'length' => 9],
        'MZ' => ['code' => '+258', 'pattern' => '/^0?([8]\d{8})$/', 'length' => 9],
        'NA' => ['code' => '+264', 'pattern' => '/^0?([6-8]\d{7})$/', 'length' => 8],
        'NE' => ['code' => '+227', 'pattern' => '/^0?([9]\d{7})$/', 'length' => 8],
        'NG' => ['code' => '+234', 'pattern' => '/^0?([7-9]\d{9})$/', 'length' => 10],
        'RW' => ['code' => '+250', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'ST' => ['code' => '+239', 'pattern' => '/^0?([9]\d{6})$/', 'length' => 7],
        'SN' => ['code' => '+221', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'SC' => ['code' => '+248', 'pattern' => '/^0?([2]\d{6})$/', 'length' => 7],
        'SL' => ['code' => '+232', 'pattern' => '/^0?([2-9]\d{7})$/', 'length' => 8],
        'SO' => ['code' => '+252', 'pattern' => '/^0?([6-9]\d{8})$/', 'length' => 9],
        'ZA' => ['code' => '+27', 'pattern' => '/^0?([6-8]\d{8})$/', 'length' => 9],
        'SS' => ['code' => '+211', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'SD' => ['code' => '+249', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'SZ' => ['code' => '+268', 'pattern' => '/^0?([7-8]\d{7})$/', 'length' => 8],
        'TZ' => ['code' => '+255', 'pattern' => '/^0?([6-7]\d{8})$/', 'length' => 9],
        'TG' => ['code' => '+228', 'pattern' => '/^0?([9]\d{7})$/', 'length' => 8],
        'TN' => ['code' => '+216', 'pattern' => '/^0?([2-9]\d{7})$/', 'length' => 8],
        'UG' => ['code' => '+256', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'ZM' => ['code' => '+260', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'ZW' => ['code' => '+263', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],

        // Asia
        'AF' => ['code' => '+93', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'AM' => ['code' => '+374', 'pattern' => '/^0?([9]\d{7})$/', 'length' => 8],
        'AZ' => ['code' => '+994', 'pattern' => '/^0?([5-7]\d{8})$/', 'length' => 9],
        'BH' => ['code' => '+973', 'pattern' => '/^0?([3-9]\d{7})$/', 'length' => 8],
        'BD' => ['code' => '+880', 'pattern' => '/^0?([1]\d{9})$/', 'length' => 10],
        'BT' => ['code' => '+975', 'pattern' => '/^0?([1-8]\d{7})$/', 'length' => 8],
        'BN' => ['code' => '+673', 'pattern' => '/^0?([2-8]\d{6})$/', 'length' => 7],
        'KH' => ['code' => '+855', 'pattern' => '/^0?([1-9]\d{7,8})$/', 'length' => [8, 9]],
        'CN' => ['code' => '+86', 'pattern' => '/^0?([1]\d{10})$/', 'length' => 11],
        'CY' => ['code' => '+357', 'pattern' => '/^0?([9]\d{7})$/', 'length' => 8],
        'GE' => ['code' => '+995', 'pattern' => '/^0?([5-9]\d{8})$/', 'length' => 9],
        'IN' => ['code' => '+91', 'pattern' => '/^0?([6-9]\d{9})$/', 'length' => 10],
        'ID' => ['code' => '+62', 'pattern' => '/^0?([8]\d{8,11})$/', 'length' => [9, 10, 11, 12]],
        'IR' => ['code' => '+98', 'pattern' => '/^0?([9]\d{9})$/', 'length' => 10],
        'IQ' => ['code' => '+964', 'pattern' => '/^0?([7]\d{9})$/', 'length' => 10],
        'IL' => ['code' => '+972', 'pattern' => '/^0?([5]\d{8})$/', 'length' => 9],
        'JP' => ['code' => '+81', 'pattern' => '/^0?([7-9]\d{9})$/', 'length' => 10],
        'JO' => ['code' => '+962', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'KZ' => ['code' => '+7', 'pattern' => '/^0?([7]\d{9})$/', 'length' => 10],
        'KW' => ['code' => '+965', 'pattern' => '/^0?([5-9]\d{7})$/', 'length' => 8],
        'KG' => ['code' => '+996', 'pattern' => '/^0?([5-9]\d{8})$/', 'length' => 9],
        'LA' => ['code' => '+856', 'pattern' => '/^0?([2]\d{8})$/', 'length' => 9],
        'LB' => ['code' => '+961', 'pattern' => '/^0?([3-9]\d{7})$/', 'length' => 8],
        'MY' => ['code' => '+60', 'pattern' => '/^0?([1]\d{8,9})$/', 'length' => [9, 10]],
        'MV' => ['code' => '+960', 'pattern' => '/^0?([7-9]\d{6})$/', 'length' => 7],
        'MN' => ['code' => '+976', 'pattern' => '/^0?([8-9]\d{7})$/', 'length' => 8],
        'MM' => ['code' => '+95', 'pattern' => '/^0?([9]\d{8,9})$/', 'length' => [9, 10]],
        'NP' => ['code' => '+977', 'pattern' => '/^0?([9]\d{9})$/', 'length' => 10],
        'KP' => ['code' => '+850', 'pattern' => '/^0?([1-9]\d{7,8})$/', 'length' => [8, 9]],
        'KR' => ['code' => '+82', 'pattern' => '/^0?([1]\d{8,9})$/', 'length' => [9, 10]],
        'OM' => ['code' => '+968', 'pattern' => '/^0?([7-9]\d{7})$/', 'length' => 8],
        'PK' => ['code' => '+92', 'pattern' => '/^0?([3]\d{9})$/', 'length' => 10],
        'PS' => ['code' => '+970', 'pattern' => '/^0?([5]\d{8})$/', 'length' => 9],
        'PH' => ['code' => '+63', 'pattern' => '/^0?([9]\d{9})$/', 'length' => 10],
        'QA' => ['code' => '+974', 'pattern' => '/^0?([3-7]\d{7})$/', 'length' => 8],
        'SA' => ['code' => '+966', 'pattern' => '/^0?([5]\d{8})$/', 'length' => 9],
        'SG' => ['code' => '+65', 'pattern' => '/^0?([8-9]\d{7})$/', 'length' => 8],
        'LK' => ['code' => '+94', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'SY' => ['code' => '+963', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'TW' => ['code' => '+886', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'TJ' => ['code' => '+992', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'TH' => ['code' => '+66', 'pattern' => '/^0?([6-9]\d{8})$/', 'length' => 9],
        'TL' => ['code' => '+670', 'pattern' => '/^0?([7]\d{7})$/', 'length' => 8],
        'TR' => ['code' => '+90', 'pattern' => '/^0?([5]\d{9})$/', 'length' => 10],
        'TM' => ['code' => '+993', 'pattern' => '/^0?([6]\d{7})$/', 'length' => 8],
        'AE' => ['code' => '+971', 'pattern' => '/^0?([5]\d{8})$/', 'length' => 9],
        'UZ' => ['code' => '+998', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'VN' => ['code' => '+84', 'pattern' => '/^0?([3-9]\d{8})$/', 'length' => 9],
        'YE' => ['code' => '+967', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],

        // Europe
        'AL' => ['code' => '+355', 'pattern' => '/^0?([6-7]\d{8})$/', 'length' => 9],
        'AD' => ['code' => '+376', 'pattern' => '/^0?([3-6]\d{5})$/', 'length' => 6],
        'AT' => ['code' => '+43', 'pattern' => '/^0?([6-7]\d{8,13})$/', 'length' => [9, 10, 11, 12, 13, 14]],
        'BY' => ['code' => '+375', 'pattern' => '/^0?([2-4]\d{8})$/', 'length' => 9],
        'BE' => ['code' => '+32', 'pattern' => '/^0?([4]\d{8})$/', 'length' => 9],
        'BA' => ['code' => '+387', 'pattern' => '/^0?([6]\d{8})$/', 'length' => 9],
        'BG' => ['code' => '+359', 'pattern' => '/^0?([8-9]\d{8})$/', 'length' => 9],
        'HR' => ['code' => '+385', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'CZ' => ['code' => '+420', 'pattern' => '/^0?([6-7]\d{8})$/', 'length' => 9],
        'DK' => ['code' => '+45', 'pattern' => '/^0?([2-9]\d{7})$/', 'length' => 8],
        'EE' => ['code' => '+372', 'pattern' => '/^0?([5]\d{7})$/', 'length' => 8],
        'FI' => ['code' => '+358', 'pattern' => '/^0?([4-5]\d{8})$/', 'length' => 9],
        'FR' => ['code' => '+33', 'pattern' => '/^0?([6-7]\d{8})$/', 'length' => 9],
        'DE' => ['code' => '+49', 'pattern' => '/^0?([1]\d{9,11})$/', 'length' => [10, 11, 12]],
        'GR' => ['code' => '+30', 'pattern' => '/^0?([6]\d{9})$/', 'length' => 10],
        'HU' => ['code' => '+36', 'pattern' => '/^0?([2-7]\d{8})$/', 'length' => 9],
        'IS' => ['code' => '+354', 'pattern' => '/^0?([6-8]\d{6})$/', 'length' => 7],
        'IE' => ['code' => '+353', 'pattern' => '/^0?([8]\d{8})$/', 'length' => 9],
        'IT' => ['code' => '+39', 'pattern' => '/^0?([3]\d{8,9})$/', 'length' => [9, 10]],
        'LV' => ['code' => '+371', 'pattern' => '/^0?([2]\d{7})$/', 'length' => 8],
        'LI' => ['code' => '+423', 'pattern' => '/^0?([6-7]\d{6})$/', 'length' => 7],
        'LT' => ['code' => '+370', 'pattern' => '/^0?([6]\d{7})$/', 'length' => 8],
        'LU' => ['code' => '+352', 'pattern' => '/^0?([6]\d{8})$/', 'length' => 9],
        'MK' => ['code' => '+389', 'pattern' => '/^0?([7]\d{7})$/', 'length' => 8],
        'MT' => ['code' => '+356', 'pattern' => '/^0?([7-9]\d{7})$/', 'length' => 8],
        'MD' => ['code' => '+373', 'pattern' => '/^0?([6-7]\d{7})$/', 'length' => 8],
        'MC' => ['code' => '+377', 'pattern' => '/^0?([6]\d{7})$/', 'length' => 8],
        'ME' => ['code' => '+382', 'pattern' => '/^0?([6]\d{7})$/', 'length' => 8],
        'NL' => ['code' => '+31', 'pattern' => '/^0?([6]\d{8})$/', 'length' => 9],
        'NO' => ['code' => '+47', 'pattern' => '/^0?([4-9]\d{7})$/', 'length' => 8],
        'PL' => ['code' => '+48', 'pattern' => '/^0?([5-9]\d{8})$/', 'length' => 9],
        'PT' => ['code' => '+351', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'RO' => ['code' => '+40', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'RU' => ['code' => '+7', 'pattern' => '/^0?([9]\d{9})$/', 'length' => 10],
        'SM' => ['code' => '+378', 'pattern' => '/^0?([6]\d{9})$/', 'length' => 10],
        'RS' => ['code' => '+381', 'pattern' => '/^0?([6]\d{8})$/', 'length' => 9],
        'SK' => ['code' => '+421', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'SI' => ['code' => '+386', 'pattern' => '/^0?([3-7]\d{7})$/', 'length' => 8],
        'ES' => ['code' => '+34', 'pattern' => '/^0?([6-7]\d{8})$/', 'length' => 9],
        'SE' => ['code' => '+46', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'CH' => ['code' => '+41', 'pattern' => '/^0?([7]\d{8})$/', 'length' => 9],
        'UA' => ['code' => '+380', 'pattern' => '/^0?([5-9]\d{8})$/', 'length' => 9],
        'GB' => ['code' => '+44', 'pattern' => '/^0?([7-8]\d{9}|[1-6]\d{8,9})$/', 'length' => [10, 11]],
        'VA' => ['code' => '+39', 'pattern' => '/^0?([3]\d{8,9})$/', 'length' => [9, 10]],

        // North America
        'US' => ['code' => '+1', 'pattern' => '/^1?([2-9]\d{9})$/', 'length' => 10],
        'CA' => ['code' => '+1', 'pattern' => '/^1?([2-9]\d{9})$/', 'length' => 10],
        'MX' => ['code' => '+52', 'pattern' => '/^0?([1]\d{9})$/', 'length' => 10],

        // Central America & Caribbean
        'BZ' => ['code' => '+501', 'pattern' => '/^0?([6]\d{6})$/', 'length' => 7],
        'CR' => ['code' => '+506', 'pattern' => '/^0?([6-8]\d{7})$/', 'length' => 8],
        'SV' => ['code' => '+503', 'pattern' => '/^0?([6-7]\d{7})$/', 'length' => 8],
        'GT' => ['code' => '+502', 'pattern' => '/^0?([4-5]\d{7})$/', 'length' => 8],
        'HN' => ['code' => '+504', 'pattern' => '/^0?([3-9]\d{7})$/', 'length' => 8],
        'NI' => ['code' => '+505', 'pattern' => '/^0?([8]\d{7})$/', 'length' => 8],
        'PA' => ['code' => '+507', 'pattern' => '/^0?([6]\d{7})$/', 'length' => 8],
        'AG' => ['code' => '+1268', 'pattern' => '/^0?([4-7]\d{6})$/', 'length' => 7],
        'BS' => ['code' => '+1242', 'pattern' => '/^0?([3-8]\d{6})$/', 'length' => 7],
        'BB' => ['code' => '+1246', 'pattern' => '/^0?([2-8]\d{6})$/', 'length' => 7],
        'CU' => ['code' => '+53', 'pattern' => '/^0?([5]\d{7})$/', 'length' => 8],
        'DM' => ['code' => '+1767', 'pattern' => '/^0?([2-8]\d{6})$/', 'length' => 7],
        'DO' => ['code' => '+1', 'pattern' => '/^1?([8]\d{9})$/', 'length' => 10],
        'GD' => ['code' => '+1473', 'pattern' => '/^0?([4]\d{6})$/', 'length' => 7],
        'HT' => ['code' => '+509', 'pattern' => '/^0?([3-4]\d{7})$/', 'length' => 8],
        'JM' => ['code' => '+1876', 'pattern' => '/^0?([2-9]\d{6})$/', 'length' => 7],
        'KN' => ['code' => '+1869', 'pattern' => '/^0?([4-7]\d{6})$/', 'length' => 7],
        'LC' => ['code' => '+1758', 'pattern' => '/^0?([4-8]\d{6})$/', 'length' => 7],
        'VC' => ['code' => '+1784', 'pattern' => '/^0?([4-5]\d{6})$/', 'length' => 7],
        'TT' => ['code' => '+1868', 'pattern' => '/^0?([2-8]\d{6})$/', 'length' => 7],

        // South America
        'AR' => ['code' => '+54', 'pattern' => '/^0?([9]\d{9})$/', 'length' => 10],
        'BO' => ['code' => '+591', 'pattern' => '/^0?([6-7]\d{7})$/', 'length' => 8],
        'BR' => ['code' => '+55', 'pattern' => '/^0?([1]\d{10})$/', 'length' => 11],
        'CL' => ['code' => '+56', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'CO' => ['code' => '+57', 'pattern' => '/^0?([3]\d{9})$/', 'length' => 10],
        'EC' => ['code' => '+593', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'FK' => ['code' => '+500', 'pattern' => '/^0?([5]\d{4})$/', 'length' => 5],
        'GF' => ['code' => '+594', 'pattern' => '/^0?([6]\d{8})$/', 'length' => 9],
        'GY' => ['code' => '+592', 'pattern' => '/^0?([6]\d{6})$/', 'length' => 7],
        'PY' => ['code' => '+595', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'PE' => ['code' => '+51', 'pattern' => '/^0?([9]\d{8})$/', 'length' => 9],
        'SR' => ['code' => '+597', 'pattern' => '/^0?([6-8]\d{6})$/', 'length' => 7],
        'UY' => ['code' => '+598', 'pattern' => '/^0?([9]\d{7})$/', 'length' => 8],
        'VE' => ['code' => '+58', 'pattern' => '/^0?([4]\d{9})$/', 'length' => 10],

        // Oceania
        'AU' => ['code' => '+61', 'pattern' => '/^0?([4]\d{8})$/', 'length' => 9],
        'FJ' => ['code' => '+679', 'pattern' => '/^0?([7-9]\d{6})$/', 'length' => 7],
        'KI' => ['code' => '+686', 'pattern' => '/^0?([7-8]\d{4})$/', 'length' => 5],
        'MH' => ['code' => '+692', 'pattern' => '/^0?([2-6]\d{6})$/', 'length' => 7],
        'FM' => ['code' => '+691', 'pattern' => '/^0?([3-9]\d{6})$/', 'length' => 7],
        'NR' => ['code' => '+674', 'pattern' => '/^0?([5-7]\d{6})$/', 'length' => 7],
        'NZ' => ['code' => '+64', 'pattern' => '/^0?([2]\d{7,9})$/', 'length' => [8, 9, 10]],
        'PW' => ['code' => '+680', 'pattern' => '/^0?([7]\d{6})$/', 'length' => 7],
        'PG' => ['code' => '+675', 'pattern' => '/^0?([7]\d{7})$/', 'length' => 8],
        'WS' => ['code' => '+685', 'pattern' => '/^0?([7]\d{6})$/', 'length' => 7],
        'SB' => ['code' => '+677', 'pattern' => '/^0?([7-9]\d{6})$/', 'length' => 7],
        'TO' => ['code' => '+676', 'pattern' => '/^0?([7-8]\d{6})$/', 'length' => 7],
        'TV' => ['code' => '+688', 'pattern' => '/^0?([9]\d{4})$/', 'length' => 5],
        'VU' => ['code' => '+678', 'pattern' => '/^0?([5-7]\d{6})$/', 'length' => 7],
    ];

    /**
     * Set the default country for phone number normalization
     *
     * @param string $country_code ISO 2-letter country code
     */
    public static function setDefaultCountry($country_code) {
        $country_patterns = self::loadCountryPatterns();
        if (isset($country_patterns[$country_code])) {
            self::$default_country = $country_code;
        }
    }

    /**
     * Clear cached data (for testing)
     */
    public static function clearCache() {
        self::$default_country = null;
        self::$json_patterns_cache = null;
    }

    /**
     * Get the current default country
     *
     * @return string ISO 2-letter country code
     */
    public static function getDefaultCountry() {
        if (self::$default_country === null) {
            self::initializeDefaultCountry();
        }
        return self::$default_country;
    }

    /**
     * Initialize default country from database settings
     */
    private static function initializeDefaultCountry() {
        try {
            // Try to load from database settings only if safe to do so
            // This prevents transaction conflicts during member updates
            if (self::isSafeToQueryDatabase()) {
                require_once __DIR__ . '/../config/database.php';
                $database = new Database();
                $conn = $database->getConnection();

                $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
                $stmt->execute(['default_phone_country']);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($result && isset(self::$country_codes[$result['setting_value']])) {
                    self::$default_country = $result['setting_value'];
                    return;
                }
            }
        } catch (Exception $e) {
            // Log error but continue with fallback
            error_log('PhoneNumberUtils: Could not load default country from database: ' . $e->getMessage());
        }

        // Try to load from configuration file as fallback
        $config_file = __DIR__ . '/../config/phone_settings.php';
        if (file_exists($config_file)) {
            require_once $config_file;
            if (defined('DEFAULT_PHONE_COUNTRY')) {
                self::$default_country = DEFAULT_PHONE_COUNTRY;
                return;
            }
        }

        // Final fallback to Ghana
        self::$default_country = 'GH';
    }

    /**
     * Check if it's safe to query the database (not in a transaction context)
     *
     * @return bool
     */
    private static function isSafeToQueryDatabase() {
        // Simple check to avoid database queries during member operations
        // This prevents transaction conflicts
        return !defined('MEMBER_UPDATE_IN_PROGRESS');
    }

    /**
     * Normalize phone number to international format
     * 
     * @param string $phone Raw phone number input
     * @param string $default_country Default country code (ISO 2-letter)
     * @return string Normalized phone number in +XXXXXXXXXXXX format
     */
    public static function normalize($phone, $default_country = null) {
        // Use configured default country if none specified
        if ($default_country === null) {
            $default_country = self::getDefaultCountry();
        }
        if (empty($phone)) {
            return '';
        }
        
        // Remove all non-numeric characters except +
        $clean = preg_replace('/[^\d+]/', '', $phone);
        
        // If already in international format with +
        if (substr($clean, 0, 1) === '+') {
            return self::validateAndFormat($clean);
        }
        
        // Try to detect country from number pattern
        $detected_country = self::detectCountry($clean);
        if ($detected_country) {
            $default_country = $detected_country;
        }
        
        // Get country info, fallback to configured default if country not found
        $country_info = self::$country_codes[$default_country] ?? self::$country_codes[self::getDefaultCountry()];
        
        // Handle local format (starting with 0)
        if (substr($clean, 0, 1) === '0') {
            $local_number = substr($clean, 1);
            return $country_info['code'] . $local_number;
        }
        
        // Check if number already contains country code without + (only if no country was detected by pattern)
        if (!$detected_country) {
            $country_patterns = self::loadCountryPatterns();
            foreach ($country_patterns as $country => $info) {
                $code_digits = substr($info['code'], 1); // Remove +
                if (substr($clean, 0, strlen($code_digits)) === $code_digits) {
                    // Validate length before assuming it's this country's code
                    $remaining_digits = substr($clean, strlen($code_digits));
                    $expected_length = is_array($info['length']) ? $info['length'] : [$info['length']];
                    $expected_remaining_lengths = array_map(function($len) use ($code_digits) {
                        return $len - strlen($code_digits);
                    }, $expected_length);

                    if (in_array(strlen($remaining_digits), $expected_remaining_lengths)) {
                        return $info['code'] . $remaining_digits;
                    }
                }
            }
        }

        // Handle local format without leading 0
        if (strlen($clean) <= 12) { // Reasonable phone number length
            return $country_info['code'] . $clean;
        }

        // If number is too long, assume it already has country code without +
        return '+' . $clean;
    }
    
    /**
     * Detect country from phone number pattern
     *
     * @param string $clean_number Clean numeric phone number
     * @return string|null Country code if detected
     */
    private static function detectCountry($clean_number) {
        // First check if number starts with country code (most reliable)
        // Order by code length (longest first) to avoid conflicts
        $countries_by_code_length = self::$country_codes;
        uasort($countries_by_code_length, function($a, $b) {
            return strlen($b['code']) - strlen($a['code']);
        });

        foreach ($countries_by_code_length as $country => $info) {
            $code_digits = substr($info['code'], 1); // Remove +
            if (substr($clean_number, 0, strlen($code_digits)) === $code_digits) {
                // Additional validation: check if remaining digits make sense for this country
                $remaining_digits = substr($clean_number, strlen($code_digits));
                $total_expected_length = $info['length'];

                // For countries with array of lengths, check if any match
                if (is_array($total_expected_length)) {
                    $expected_remaining_length = array_map(function($len) use ($code_digits) {
                        return $len - strlen($code_digits);
                    }, $total_expected_length);

                    if (in_array(strlen($remaining_digits), $expected_remaining_length)) {
                        return $country;
                    }
                } else {
                    $expected_remaining_length = $total_expected_length - strlen($code_digits);
                    if (strlen($remaining_digits) === $expected_remaining_length) {
                        return $country;
                    }
                }
            }
        }

        // Then check patterns for local numbers with intelligent scoring
        $country_scores = [];
        $country_patterns = self::loadCountryPatterns();

        foreach ($country_patterns as $country => $info) {
            if (preg_match($info['pattern'], $clean_number)) {
                $score = 0;

                // Length matching score
                if (is_array($info['length'])) {
                    if (in_array(strlen($clean_number), $info['length'])) {
                        $score += 10;
                    }
                } else {
                    if (strlen($clean_number) === $info['length']) {
                        $score += 10;
                    }
                }

                // UK mobile numbers starting with 07 get highest priority
                if ($country === 'GB' && preg_match('/^0?7\d{9}$/', $clean_number)) {
                    $score += 20;
                }

                // Ghana numbers starting with 02-05 get priority
                if ($country === 'GH' && preg_match('/^0?[2-5]\d{8}$/', $clean_number)) {
                    $score += 15;
                }

                // Togo numbers starting with 9 get priority
                if ($country === 'TG' && preg_match('/^0?9\d{7}$/', $clean_number)) {
                    $score += 18;
                }

                // US/Canada numbers get priority for 10-digit numbers
                if (($country === 'US' || $country === 'CA') && strlen($clean_number) === 10) {
                    $score += 12;
                }

                $country_scores[$country] = $score;
            }
        }

        // Return the country with the highest score
        if (!empty($country_scores)) {
            arsort($country_scores);
            return array_key_first($country_scores);
        }

        return null;
    }
    
    /**
     * Validate and format international number
     * 
     * @param string $international_number Number starting with +
     * @return string Formatted number
     */
    private static function validateAndFormat($international_number) {
        // Basic validation - must start with + and have reasonable length
        if (!preg_match('/^\+\d{7,15}$/', $international_number)) {
            // If invalid, return as-is but log warning
            error_log("Invalid international phone format: " . $international_number);
        }
        
        return $international_number;
    }
    
    /**
     * Format phone number for display
     * 
     * @param string $normalized_phone Normalized phone number
     * @param string $format Display format ('international', 'national', 'original')
     * @return string Formatted phone number
     */
    public static function formatForDisplay($normalized_phone, $format = 'international') {
        if (empty($normalized_phone) || substr($normalized_phone, 0, 1) !== '+') {
            return $normalized_phone;
        }
        
        switch ($format) {
            case 'international':
                return self::formatInternational($normalized_phone);
            case 'national':
                return self::formatNational($normalized_phone);
            default:
                return $normalized_phone;
        }
    }
    
    /**
     * Format for international display
     * 
     * @param string $normalized_phone
     * @return string
     */
    private static function formatInternational($normalized_phone) {
        // Ghana: +233 24 412 3456
        if (substr($normalized_phone, 0, 4) === '+233') {
            $number = substr($normalized_phone, 4);
            return '+233 ' . substr($number, 0, 2) . ' ' . substr($number, 2, 3) . ' ' . substr($number, 5);
        }
        
        // US/Canada: +****************
        if (substr($normalized_phone, 0, 2) === '+1') {
            $number = substr($normalized_phone, 2);
            return '+1 (' . substr($number, 0, 3) . ') ' . substr($number, 3, 3) . '-' . substr($number, 6);
        }
        
        // UK: +44 7123 456789
        if (substr($normalized_phone, 0, 3) === '+44') {
            $number = substr($normalized_phone, 3);
            return '+44 ' . substr($number, 0, 4) . ' ' . substr($number, 4);
        }
        
        // Default: +XXX XXX XXX XXXX
        $code_end = strpos($normalized_phone, ' ') ?: 4;
        $code = substr($normalized_phone, 0, $code_end);
        $number = substr($normalized_phone, $code_end);
        
        // Add spaces every 3 digits
        $formatted_number = chunk_split($number, 3, ' ');
        return $code . ' ' . trim($formatted_number);
    }
    
    /**
     * Format for national display
     * 
     * @param string $normalized_phone
     * @return string
     */
    private static function formatNational($normalized_phone) {
        // Convert back to national format
        $country_patterns = self::loadCountryPatterns();
        foreach ($country_patterns as $country => $info) {
            if (substr($normalized_phone, 0, strlen($info['code'])) === $info['code']) {
                $local_number = substr($normalized_phone, strlen($info['code']));
                return '0' . $local_number;
            }
        }
        
        return $normalized_phone;
    }
    
    /**
     * Validate phone number
     * 
     * @param string $phone Phone number to validate
     * @return array Validation result with status and message
     */
    public static function validate($phone) {
        if (empty($phone)) {
            return ['valid' => false, 'message' => 'Phone number is required'];
        }
        
        $normalized = self::normalize($phone);
        
        // Basic length check
        if (strlen($normalized) < 8 || strlen($normalized) > 17) {
            return ['valid' => false, 'message' => 'Phone number length is invalid'];
        }
        
        // Must start with + after normalization
        if (substr($normalized, 0, 1) !== '+') {
            return ['valid' => false, 'message' => 'Unable to normalize phone number'];
        }
        
        return ['valid' => true, 'message' => 'Valid phone number', 'normalized' => $normalized];
    }
    
    /**
     * Check if two phone numbers are the same (after normalization)
     * 
     * @param string $phone1
     * @param string $phone2
     * @param string $default_country
     * @return bool
     */
    public static function areEqual($phone1, $phone2, $default_country = 'GH') {
        $normalized1 = self::normalize($phone1, $default_country);
        $normalized2 = self::normalize($phone2, $default_country);
        
        return $normalized1 === $normalized2;
    }
    
    /**
     * Get supported countries with full names
     *
     * @return array Array of country codes and names with phone codes
     */
    public static function getSupportedCountries() {
        $country_names = [
            // Africa
            'DZ' => 'Algeria',
            'AO' => 'Angola',
            'BJ' => 'Benin',
            'BW' => 'Botswana',
            'BF' => 'Burkina Faso',
            'BI' => 'Burundi',
            'CM' => 'Cameroon',
            'CV' => 'Cape Verde',
            'CF' => 'Central African Republic',
            'TD' => 'Chad',
            'KM' => 'Comoros',
            'CG' => 'Congo',
            'CD' => 'Congo (Democratic Republic)',
            'CI' => 'Côte d\'Ivoire',
            'DJ' => 'Djibouti',
            'EG' => 'Egypt',
            'GQ' => 'Equatorial Guinea',
            'ER' => 'Eritrea',
            'ET' => 'Ethiopia',
            'GA' => 'Gabon',
            'GM' => 'Gambia',
            'GH' => 'Ghana',
            'GN' => 'Guinea',
            'GW' => 'Guinea-Bissau',
            'KE' => 'Kenya',
            'LS' => 'Lesotho',
            'LR' => 'Liberia',
            'LY' => 'Libya',
            'MG' => 'Madagascar',
            'MW' => 'Malawi',
            'ML' => 'Mali',
            'MR' => 'Mauritania',
            'MU' => 'Mauritius',
            'MA' => 'Morocco',
            'MZ' => 'Mozambique',
            'NA' => 'Namibia',
            'NE' => 'Niger',
            'NG' => 'Nigeria',
            'RW' => 'Rwanda',
            'ST' => 'São Tomé and Príncipe',
            'SN' => 'Senegal',
            'SC' => 'Seychelles',
            'SL' => 'Sierra Leone',
            'SO' => 'Somalia',
            'ZA' => 'South Africa',
            'SS' => 'South Sudan',
            'SD' => 'Sudan',
            'SZ' => 'Eswatini',
            'TZ' => 'Tanzania',
            'TG' => 'Togo',
            'TN' => 'Tunisia',
            'UG' => 'Uganda',
            'ZM' => 'Zambia',
            'ZW' => 'Zimbabwe',

            // Asia
            'AF' => 'Afghanistan',
            'AM' => 'Armenia',
            'AZ' => 'Azerbaijan',
            'BH' => 'Bahrain',
            'BD' => 'Bangladesh',
            'BT' => 'Bhutan',
            'BN' => 'Brunei',
            'KH' => 'Cambodia',
            'CN' => 'China',
            'CY' => 'Cyprus',
            'GE' => 'Georgia',
            'IN' => 'India',
            'ID' => 'Indonesia',
            'IR' => 'Iran',
            'IQ' => 'Iraq',
            'IL' => 'Israel',
            'JP' => 'Japan',
            'JO' => 'Jordan',
            'KZ' => 'Kazakhstan',
            'KW' => 'Kuwait',
            'KG' => 'Kyrgyzstan',
            'LA' => 'Laos',
            'LB' => 'Lebanon',
            'MY' => 'Malaysia',
            'MV' => 'Maldives',
            'MN' => 'Mongolia',
            'MM' => 'Myanmar',
            'NP' => 'Nepal',
            'KP' => 'North Korea',
            'KR' => 'South Korea',
            'OM' => 'Oman',
            'PK' => 'Pakistan',
            'PS' => 'Palestine',
            'PH' => 'Philippines',
            'QA' => 'Qatar',
            'SA' => 'Saudi Arabia',
            'SG' => 'Singapore',
            'LK' => 'Sri Lanka',
            'SY' => 'Syria',
            'TW' => 'Taiwan',
            'TJ' => 'Tajikistan',
            'TH' => 'Thailand',
            'TL' => 'Timor-Leste',
            'TR' => 'Turkey',
            'TM' => 'Turkmenistan',
            'AE' => 'United Arab Emirates',
            'UZ' => 'Uzbekistan',
            'VN' => 'Vietnam',
            'YE' => 'Yemen',

            // Europe
            'AL' => 'Albania',
            'AD' => 'Andorra',
            'AT' => 'Austria',
            'BY' => 'Belarus',
            'BE' => 'Belgium',
            'BA' => 'Bosnia and Herzegovina',
            'BG' => 'Bulgaria',
            'HR' => 'Croatia',
            'CZ' => 'Czech Republic',
            'DK' => 'Denmark',
            'EE' => 'Estonia',
            'FI' => 'Finland',
            'FR' => 'France',
            'DE' => 'Germany',
            'GR' => 'Greece',
            'HU' => 'Hungary',
            'IS' => 'Iceland',
            'IE' => 'Ireland',
            'IT' => 'Italy',
            'LV' => 'Latvia',
            'LI' => 'Liechtenstein',
            'LT' => 'Lithuania',
            'LU' => 'Luxembourg',
            'MK' => 'North Macedonia',
            'MT' => 'Malta',
            'MD' => 'Moldova',
            'MC' => 'Monaco',
            'ME' => 'Montenegro',
            'NL' => 'Netherlands',
            'NO' => 'Norway',
            'PL' => 'Poland',
            'PT' => 'Portugal',
            'RO' => 'Romania',
            'RU' => 'Russia',
            'SM' => 'San Marino',
            'RS' => 'Serbia',
            'SK' => 'Slovakia',
            'SI' => 'Slovenia',
            'ES' => 'Spain',
            'SE' => 'Sweden',
            'CH' => 'Switzerland',
            'UA' => 'Ukraine',
            'GB' => 'United Kingdom',
            'VA' => 'Vatican City',

            // North America
            'US' => 'United States',
            'CA' => 'Canada',
            'MX' => 'Mexico',

            // Central America & Caribbean
            'BZ' => 'Belize',
            'CR' => 'Costa Rica',
            'SV' => 'El Salvador',
            'GT' => 'Guatemala',
            'HN' => 'Honduras',
            'NI' => 'Nicaragua',
            'PA' => 'Panama',
            'AG' => 'Antigua and Barbuda',
            'BS' => 'Bahamas',
            'BB' => 'Barbados',
            'CU' => 'Cuba',
            'DM' => 'Dominica',
            'DO' => 'Dominican Republic',
            'GD' => 'Grenada',
            'HT' => 'Haiti',
            'JM' => 'Jamaica',
            'KN' => 'Saint Kitts and Nevis',
            'LC' => 'Saint Lucia',
            'VC' => 'Saint Vincent and the Grenadines',
            'TT' => 'Trinidad and Tobago',

            // South America
            'AR' => 'Argentina',
            'BO' => 'Bolivia',
            'BR' => 'Brazil',
            'CL' => 'Chile',
            'CO' => 'Colombia',
            'EC' => 'Ecuador',
            'FK' => 'Falkland Islands',
            'GF' => 'French Guiana',
            'GY' => 'Guyana',
            'PY' => 'Paraguay',
            'PE' => 'Peru',
            'SR' => 'Suriname',
            'UY' => 'Uruguay',
            'VE' => 'Venezuela',

            // Oceania
            'AU' => 'Australia',
            'FJ' => 'Fiji',
            'KI' => 'Kiribati',
            'MH' => 'Marshall Islands',
            'FM' => 'Micronesia',
            'NR' => 'Nauru',
            'NZ' => 'New Zealand',
            'PW' => 'Palau',
            'PG' => 'Papua New Guinea',
            'WS' => 'Samoa',
            'SB' => 'Solomon Islands',
            'TO' => 'Tonga',
            'TV' => 'Tuvalu',
            'VU' => 'Vanuatu',
        ];

        $countries = [];
        foreach (self::$country_codes as $code => $info) {
            $name = $country_names[$code] ?? $code;
            $countries[$code] = $name . ' (' . $info['code'] . ')';
        }

        // Sort by country name
        asort($countries);

        return $countries;
    }

    /**
     * Public method to detect country from phone number (for testing)
     *
     * @param string $phone Phone number to analyze
     * @return string|null Country code or null if not detected
     */
    public static function detectCountryFromNumber($phone) {
        $clean_number = preg_replace('/[^\d]/', '', $phone);
        return self::detectCountry($clean_number);
    }
}
?>
