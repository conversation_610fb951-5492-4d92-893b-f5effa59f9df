<div class="page-content-centered">
    <!-- Enhanced Header Section with Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-blue-600 to-cyan-700 p-6 text-white">
            <div class="flex flex-row justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Attendance Comparison</h1>
                    <p class="text-sm opacity-90 mt-1">Compare attendance between different time periods</p>
                </div>
                <div class="flex space-x-2">
                    <a href="<?php echo BASE_URL; ?>attendance" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Attendance
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Comparison Filter -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Comparison Settings</h2>
        </div>
        <div class="p-4">
            <form action="<?php echo BASE_URL; ?>attendance/comparative-analysis" method="GET" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Period 1 -->
                <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                    <h3 class="text-base font-medium text-blue-800 mb-3 flex items-center">
                        <div class="w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center mr-2 text-xs">1</div>
                        Period 1
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="period1_start" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <input type="date" id="period1_start" name="period1_start" value="<?php echo $period1_start; ?>" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 py-2 px-3">
                        </div>
                        <div>
                            <label for="period1_end" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                            <input type="date" id="period1_end" name="period1_end" value="<?php echo $period1_end; ?>" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 py-2 px-3">
                        </div>
                    </div>
                </div>

                <!-- Period 2 -->
                <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                    <h3 class="text-base font-medium text-green-800 mb-3 flex items-center">
                        <div class="w-6 h-6 rounded-full bg-green-500 text-white flex items-center justify-center mr-2 text-xs">2</div>
                        Period 2
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="period2_start" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <input type="date" id="period2_start" name="period2_start" value="<?php echo $period2_start; ?>" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 py-2 px-3">
                        </div>
                        <div>
                            <label for="period2_end" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                            <input type="date" id="period2_end" name="period2_end" value="<?php echo $period2_end; ?>" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50 py-2 px-3">
                        </div>
                    </div>
                </div>

                <!-- Service Filter -->
                <div class="md:col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="md:col-span-3">
                            <label for="service_id" class="block text-sm font-medium text-gray-700 mb-1">Service (Optional)</label>
                            <select id="service_id" name="service_id" class="w-full rounded-md border border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 py-2 px-3">
                                <option value="">All Services</option>
                                <?php foreach ($services as $service) : ?>
                                    <option value="<?php echo $service->id; ?>" <?php echo (isset($_GET['service_id']) && $_GET['service_id'] == $service->id) ? 'selected' : ''; ?>>
                                        <?php echo $service->name; ?> (<?php echo ucfirst($service->day_of_week); ?> - <?php echo date('h:i A', strtotime($service->time)); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200 w-full justify-center">
                                <i class="fas fa-sync-alt mr-2"></i> Compare Periods
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Comparison Presets -->
    <div class="flex flex-wrap gap-2 mb-6">
        <a href="<?php echo BASE_URL; ?>attendance/comparative-analysis?period1_start=<?php echo date('Y-m-d', strtotime('monday this week')); ?>&period1_end=<?php echo date('Y-m-d', strtotime('sunday this week')); ?>&period2_start=<?php echo date('Y-m-d', strtotime('monday last week')); ?>&period2_end=<?php echo date('Y-m-d', strtotime('sunday last week')); ?><?php echo isset($_GET['service_id']) ? '&service_id=' . $_GET['service_id'] : ''; ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 py-1.5 px-3 rounded-lg text-xs font-medium transition-all duration-200">
            This Week vs Last Week
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/comparative-analysis?period1_start=<?php echo date('Y-m-01'); ?>&period1_end=<?php echo date('Y-m-t'); ?>&period2_start=<?php echo date('Y-m-01', strtotime('-1 month')); ?>&period2_end=<?php echo date('Y-m-t', strtotime('-1 month')); ?><?php echo isset($_GET['service_id']) ? '&service_id=' . $_GET['service_id'] : ''; ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 py-1.5 px-3 rounded-lg text-xs font-medium transition-all duration-200">
            This Month vs Last Month
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/comparative-analysis?period1_start=<?php echo date('Y-m-01', strtotime('-2 months')); ?>&period1_end=<?php echo date('Y-m-t', strtotime('-2 months')); ?>&period2_start=<?php echo date('Y-m-01', strtotime('-3 months')); ?>&period2_end=<?php echo date('Y-m-t', strtotime('-3 months')); ?><?php echo isset($_GET['service_id']) ? '&service_id=' . $_GET['service_id'] : ''; ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 py-1.5 px-3 rounded-lg text-xs font-medium transition-all duration-200">
            2 Months Ago vs 3 Months Ago
        </a>
        <a href="<?php echo BASE_URL; ?>attendance/comparative-analysis?period1_start=<?php echo date('Y-01-01'); ?>&period1_end=<?php echo date('Y-03-31'); ?>&period2_start=<?php echo date('Y-04-01'); ?>&period2_end=<?php echo date('Y-06-30'); ?><?php echo isset($_GET['service_id']) ? '&service_id=' . $_GET['service_id'] : ''; ?>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 py-1.5 px-3 rounded-lg text-xs font-medium transition-all duration-200">
            Q1 vs Q2
        </a>
    </div>
    <!-- Comparison Results -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border border-gray-200">
        <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Comparison Results</h2>
            <p class="text-sm text-gray-600">
                Comparing <?php echo format_date($period1_start); ?> to <?php echo format_date($period1_end); ?>
                with <?php echo format_date($period2_start); ?> to <?php echo format_date($period2_end); ?>
                <?php if (isset($_GET['service_id']) && !empty($_GET['service_id'])) : ?>
                    <?php foreach ($services as $service) : ?>
                        <?php if ($service->id == $_GET['service_id']) : ?>
                            (<?php echo $service->name; ?>)
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </p>
        </div>
        <div class="p-6">
            <!-- Key Metrics Comparison -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Total Attendance -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h3 class="text-base font-semibold text-gray-800 mb-4">Total Attendance</h3>
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                            <span class="text-sm text-gray-600">Period 1</span>
                        </div>
                        <span class="text-lg font-bold text-blue-600"><?php echo $period1_data['total_attendance']; ?></span>
                    </div>
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                            <span class="text-sm text-gray-600">Period 2</span>
                        </div>
                        <span class="text-lg font-bold text-green-600"><?php echo $period2_data['total_attendance']; ?></span>
                    </div>
                    <div class="border-t border-gray-200 pt-4 mt-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600">Difference</span>
                            <?php
                                $difference = $comparison_data['total_attendance']['difference'];
                                $percentage = $comparison_data['total_attendance']['percentage'];
                                $is_positive = $difference >= 0;
                                $color_class = $is_positive ? 'text-green-600' : 'text-red-600';
                                $icon_class = $is_positive ? 'fa-arrow-up' : 'fa-arrow-down';
                            ?>
                            <div class="flex items-center <?php echo $color_class; ?>">
                                <i class="fas <?php echo $icon_class; ?> mr-1 text-xs"></i>
                                <span class="font-bold"><?php echo abs($difference); ?></span>
                                <span class="text-xs ml-1">(<?php echo $percentage; ?>%)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Average Attendance -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h3 class="text-base font-semibold text-gray-800 mb-4">Average Attendance</h3>
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                            <span class="text-sm text-gray-600">Period 1</span>
                        </div>
                        <span class="text-lg font-bold text-blue-600"><?php echo $period1_data['average_attendance']; ?></span>
                    </div>
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                            <span class="text-sm text-gray-600">Period 2</span>
                        </div>
                        <span class="text-lg font-bold text-green-600"><?php echo $period2_data['average_attendance']; ?></span>
                    </div>
                    <div class="border-t border-gray-200 pt-4 mt-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600">Difference</span>
                            <?php
                                $difference = $comparison_data['average_attendance']['difference'];
                                $percentage = $comparison_data['average_attendance']['percentage'];
                                $is_positive = $difference >= 0;
                                $color_class = $is_positive ? 'text-green-600' : 'text-red-600';
                                $icon_class = $is_positive ? 'fa-arrow-up' : 'fa-arrow-down';
                            ?>
                            <div class="flex items-center <?php echo $color_class; ?>">
                                <i class="fas <?php echo $icon_class; ?> mr-1 text-xs"></i>
                                <span class="font-bold"><?php echo abs($difference); ?></span>
                                <span class="text-xs ml-1">(<?php echo $percentage; ?>%)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Rate -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h3 class="text-base font-semibold text-gray-800 mb-4">Attendance Rate</h3>
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                            <span class="text-sm text-gray-600">Period 1</span>
                        </div>
                        <span class="text-lg font-bold text-blue-600"><?php echo $period1_data['attendance_rate']; ?>%</span>
                    </div>
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                            <span class="text-sm text-gray-600">Period 2</span>
                        </div>
                        <span class="text-lg font-bold text-green-600"><?php echo $period2_data['attendance_rate']; ?>%</span>
                    </div>
                    <div class="border-t border-gray-200 pt-4 mt-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600">Difference</span>
                            <?php
                                $difference = $comparison_data['attendance_rate']['difference'];
                                $percentage = $comparison_data['attendance_rate']['percentage'];
                                $is_positive = $difference >= 0;
                                $color_class = $is_positive ? 'text-green-600' : 'text-red-600';
                                $icon_class = $is_positive ? 'fa-arrow-up' : 'fa-arrow-down';
                            ?>
                            <div class="flex items-center <?php echo $color_class; ?>">
                                <i class="fas <?php echo $icon_class; ?> mr-1 text-xs"></i>
                                <span class="font-bold"><?php echo abs($difference); ?>%</span>
                                <span class="text-xs ml-1">(<?php echo $percentage; ?>%)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visual Comparison -->
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-8">
                <h3 class="text-base font-semibold text-gray-800 mb-4">Visual Comparison</h3>

                <!-- Attendance by Status -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Present Members -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i> Present Members
                        </h4>
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                                <span class="text-xs text-gray-600">Period 1</span>
                            </div>
                            <span class="text-sm font-bold text-blue-600"><?php echo $period1_data['total_present']; ?></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>

                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                <span class="text-xs text-gray-600">Period 2</span>
                            </div>
                            <span class="text-sm font-bold text-green-600"><?php echo $period2_data['total_present']; ?></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: <?php echo ($period2_data['total_present'] / max($period1_data['total_present'], $period2_data['total_present']) * 100); ?>%"></div>
                        </div>
                    </div>

                    <!-- Absent Members -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                            <i class="fas fa-times-circle text-red-500 mr-2"></i> Absent Members
                        </h4>
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                                <span class="text-xs text-gray-600">Period 1</span>
                            </div>
                            <span class="text-sm font-bold text-blue-600"><?php echo $period1_data['total_absent']; ?></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>

                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                <span class="text-xs text-gray-600">Period 2</span>
                            </div>
                            <span class="text-sm font-bold text-green-600"><?php echo $period2_data['total_absent']; ?></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: <?php echo ($period2_data['total_absent'] / max($period1_data['total_absent'], $period2_data['total_absent']) * 100); ?>%"></div>
                        </div>
                    </div>

                    <!-- Late Members -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                            <i class="fas fa-clock text-yellow-500 mr-2"></i> Late Members
                        </h4>
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                                <span class="text-xs text-gray-600">Period 1</span>
                            </div>
                            <span class="text-sm font-bold text-blue-600"><?php echo $period1_data['total_late']; ?></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>

                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                <span class="text-xs text-gray-600">Period 2</span>
                            </div>
                            <span class="text-sm font-bold text-green-600"><?php echo $period2_data['total_late']; ?></span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: <?php echo ($period2_data['total_late'] / max($period1_data['total_late'], $period2_data['total_late']) * 100); ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Insights and Recommendations -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 shadow-sm">
                <h3 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i> Insights and Recommendations
                </h3>

                <div class="space-y-4">
                    <?php if ($comparison_data['total_attendance']['difference'] > 0) : ?>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                <i class="fas fa-arrow-up text-green-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-800">Attendance Increase</h4>
                                <p class="text-sm text-gray-600 mt-1">Total attendance has increased by <?php echo abs($comparison_data['total_attendance']['difference']); ?> (<?php echo $comparison_data['total_attendance']['percentage']; ?>%) compared to the previous period. Continue with current engagement strategies.</p>
                            </div>
                        </div>
                    <?php else : ?>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
                                <i class="fas fa-arrow-down text-red-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-800">Attendance Decrease</h4>
                                <p class="text-sm text-gray-600 mt-1">Total attendance has decreased by <?php echo abs($comparison_data['total_attendance']['difference']); ?> (<?php echo abs($comparison_data['total_attendance']['percentage']); ?>%) compared to the previous period. Consider implementing engagement strategies to improve attendance.</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($comparison_data['attendance_rate']['difference'] > 0) : ?>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                <i class="fas fa-percentage text-green-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-800">Improved Attendance Rate</h4>
                                <p class="text-sm text-gray-600 mt-1">The attendance rate has improved by <?php echo abs($comparison_data['attendance_rate']['difference']); ?>%. This indicates better member engagement and commitment.</p>
                            </div>
                        </div>
                    <?php else : ?>
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                                <i class="fas fa-percentage text-yellow-600"></i>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-800">Declining Attendance Rate</h4>
                                <p class="text-sm text-gray-600 mt-1">The attendance rate has decreased by <?php echo abs($comparison_data['attendance_rate']['difference']); ?>%. Consider implementing follow-up strategies for absent members.</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <i class="fas fa-bullseye text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-800">Recommended Actions</h4>
                            <ul class="text-sm text-gray-600 mt-1 list-disc list-inside space-y-1">
                                <?php if ($comparison_data['total_attendance']['difference'] < 0) : ?>
                                    <li>Send reminders to members who have been absent</li>
                                    <li>Conduct a survey to understand reasons for absence</li>
                                <?php endif; ?>
                                <li>Set attendance goals for the next period</li>
                                <li>Recognize and celebrate consistent attendees</li>
                                <li>Continue monitoring attendance trends</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="flex justify-end mb-6">
        <div class="flex space-x-2">
            <button type="button" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                <i class="fas fa-file-excel mr-2"></i> Export to Excel
            </button>
            <button type="button" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg flex items-center text-sm transition-all duration-200">
                <i class="fas fa-file-pdf mr-2"></i> Export to PDF
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add any JavaScript functionality here
    });
</script>