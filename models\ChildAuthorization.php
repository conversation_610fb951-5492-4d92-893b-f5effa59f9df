<?php
/**
 * Child Authorization Model
 * Manages permissions and authorizations for children in ministry programs
 */

class ChildAuthorization {
    // Database connection and table name
    private $conn;
    private $table_name = "child_authorizations";

    // Object properties
    public $id;
    public $child_id;
    public $guardian_contact_id;
    public $member_id;
    public $authorization_type;
    public $is_authorized;
    public $authorization_date;
    public $expiry_date;
    public $signed_by;
    public $witness_name;
    public $notes;
    public $created_at;
    public $updated_at;

    /**
     * Constructor with $db as database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create authorization
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET child_id = :child_id,
                      guardian_contact_id = :guardian_contact_id,
                      member_id = :member_id,
                      authorization_type = :authorization_type,
                      is_authorized = :is_authorized,
                      authorization_date = :authorization_date,
                      expiry_date = :expiry_date,
                      signed_by = :signed_by,
                      witness_name = :witness_name,
                      notes = :notes,
                      created_at = :created_at,
                      updated_at = :updated_at";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->authorization_type = htmlspecialchars(strip_tags($this->authorization_type));
        $this->signed_by = htmlspecialchars(strip_tags($this->signed_by));
        $this->witness_name = htmlspecialchars(strip_tags($this->witness_name));
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Bind values
        $stmt->bindParam(':child_id', $this->child_id);
        $stmt->bindParam(':guardian_contact_id', $this->guardian_contact_id);
        $stmt->bindParam(':member_id', $this->member_id);
        $stmt->bindParam(':authorization_type', $this->authorization_type);
        $stmt->bindParam(':is_authorized', $this->is_authorized, PDO::PARAM_BOOL);
        $stmt->bindParam(':authorization_date', $this->authorization_date);
        $stmt->bindParam(':expiry_date', $this->expiry_date);
        $stmt->bindParam(':signed_by', $this->signed_by);
        $stmt->bindParam(':witness_name', $this->witness_name);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':created_at', $this->created_at);
        $stmt->bindParam(':updated_at', $this->updated_at);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Get authorizations for a child
     */
    public function getByChildId($child_id) {
        $query = "SELECT ca.*, 
                         gc.first_name as guardian_first_name, 
                         gc.last_name as guardian_last_name,
                         m.first_name as member_first_name,
                         m.last_name as member_last_name
                  FROM " . $this->table_name . " ca
                  LEFT JOIN guardian_contacts gc ON ca.guardian_contact_id = gc.id
                  LEFT JOIN members m ON ca.member_id = m.id
                  WHERE ca.child_id = :child_id
                  ORDER BY ca.authorization_type, ca.authorization_date DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->execute();
        
        return $stmt;
    }

    /**
     * Check if child has specific authorization
     */
    public function hasAuthorization($child_id, $authorization_type, $guardian_contact_id = null, $member_id = null) {
        $query = "SELECT id FROM " . $this->table_name . " 
                  WHERE child_id = :child_id 
                    AND authorization_type = :authorization_type 
                    AND is_authorized = 1
                    AND (expiry_date IS NULL OR expiry_date >= CURDATE())";
        
        $params = [
            ':child_id' => $child_id,
            ':authorization_type' => $authorization_type
        ];

        if ($guardian_contact_id) {
            $query .= " AND guardian_contact_id = :guardian_contact_id";
            $params[':guardian_contact_id'] = $guardian_contact_id;
        }

        if ($member_id) {
            $query .= " AND member_id = :member_id";
            $params[':member_id'] = $member_id;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetch(PDO::FETCH_ASSOC) !== false;
    }

    /**
     * Get all authorizations for a child by type
     */
    public function getAuthorizationsByType($child_id, $authorization_type) {
        $query = "SELECT ca.*, 
                         gc.first_name as guardian_first_name, 
                         gc.last_name as guardian_last_name,
                         gc.phone_number as guardian_phone,
                         m.first_name as member_first_name,
                         m.last_name as member_last_name,
                         m.phone_number as member_phone
                  FROM " . $this->table_name . " ca
                  LEFT JOIN guardian_contacts gc ON ca.guardian_contact_id = gc.id
                  LEFT JOIN members m ON ca.member_id = m.id
                  WHERE ca.child_id = :child_id 
                    AND ca.authorization_type = :authorization_type
                    AND ca.is_authorized = 1
                    AND (ca.expiry_date IS NULL OR ca.expiry_date >= CURDATE())
                  ORDER BY ca.authorization_date DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':child_id', $child_id);
        $stmt->bindParam(':authorization_type', $authorization_type);
        $stmt->execute();
        
        return $stmt;
    }

    /**
     * Update authorization
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET guardian_contact_id = :guardian_contact_id,
                      member_id = :member_id,
                      authorization_type = :authorization_type,
                      is_authorized = :is_authorized,
                      authorization_date = :authorization_date,
                      expiry_date = :expiry_date,
                      signed_by = :signed_by,
                      witness_name = :witness_name,
                      notes = :notes,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->authorization_type = htmlspecialchars(strip_tags($this->authorization_type));
        $this->signed_by = htmlspecialchars(strip_tags($this->signed_by));
        $this->witness_name = htmlspecialchars(strip_tags($this->witness_name));
        $this->notes = htmlspecialchars(strip_tags($this->notes));
        $this->updated_at = date('Y-m-d H:i:s');

        // Bind values
        $stmt->bindParam(':guardian_contact_id', $this->guardian_contact_id);
        $stmt->bindParam(':member_id', $this->member_id);
        $stmt->bindParam(':authorization_type', $this->authorization_type);
        $stmt->bindParam(':is_authorized', $this->is_authorized, PDO::PARAM_BOOL);
        $stmt->bindParam(':authorization_date', $this->authorization_date);
        $stmt->bindParam(':expiry_date', $this->expiry_date);
        $stmt->bindParam(':signed_by', $this->signed_by);
        $stmt->bindParam(':witness_name', $this->witness_name);
        $stmt->bindParam(':notes', $this->notes);
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    /**
     * Revoke authorization
     */
    public function revoke() {
        $query = "UPDATE " . $this->table_name . "
                  SET is_authorized = 0,
                      updated_at = :updated_at
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $this->updated_at = date('Y-m-d H:i:s');
        
        $stmt->bindParam(':updated_at', $this->updated_at);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    /**
     * Delete authorization
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        return $stmt->execute();
    }

    /**
     * Get expiring authorizations
     */
    public function getExpiringAuthorizations($days_ahead = 30) {
        $query = "SELECT ca.*, 
                         m.first_name as child_first_name,
                         m.last_name as child_last_name,
                         gc.first_name as guardian_first_name, 
                         gc.last_name as guardian_last_name,
                         gc.phone_number as guardian_phone
                  FROM " . $this->table_name . " ca
                  JOIN members m ON ca.child_id = m.id
                  LEFT JOIN guardian_contacts gc ON ca.guardian_contact_id = gc.id
                  WHERE ca.expiry_date IS NOT NULL 
                    AND ca.expiry_date <= DATE_ADD(CURDATE(), INTERVAL :days_ahead DAY)
                    AND ca.expiry_date >= CURDATE()
                    AND ca.is_authorized = 1
                  ORDER BY ca.expiry_date ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':days_ahead', $days_ahead);
        $stmt->execute();
        
        return $stmt;
    }

    /**
     * Create standard authorizations for a child
     */
    public function createStandardAuthorizations($child_id, $guardian_contact_id = null, $member_id = null, $signed_by = '') {
        $standard_authorizations = [
            'participation',
            'pickup',
            'emergency',
            'photo'
        ];

        $success_count = 0;
        $current_date = date('Y-m-d');
        $current_datetime = date('Y-m-d H:i:s');

        foreach ($standard_authorizations as $auth_type) {
            $this->child_id = $child_id;
            $this->guardian_contact_id = $guardian_contact_id;
            $this->member_id = $member_id;
            $this->authorization_type = $auth_type;
            $this->is_authorized = true;
            $this->authorization_date = $current_date;
            $this->expiry_date = null; // No expiry for standard authorizations
            $this->signed_by = $signed_by;
            $this->witness_name = '';
            $this->notes = 'Standard authorization created during registration';
            $this->created_at = $current_datetime;
            $this->updated_at = $current_datetime;

            if ($this->create()) {
                $success_count++;
            }
        }

        return $success_count === count($standard_authorizations);
    }
}
