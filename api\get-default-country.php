<?php
/**
 * API endpoint to get the default phone country setting
 */

require_once '../config/database.php';
require_once '../utils/helpers.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Get default country from database
    $database = new Database();
    $conn = $database->getConnection();
    
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute(['default_phone_country']);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $country = 'GH'; // Default fallback
    if ($result && !empty($result['setting_value'])) {
        $country = $result['setting_value'];
    }
    
    echo json_encode([
        'country' => $country,
        'success' => true
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'country' => 'GH' // Fallback
    ]);
    error_log('Get default country API error: ' . $e->getMessage());
}
?>
