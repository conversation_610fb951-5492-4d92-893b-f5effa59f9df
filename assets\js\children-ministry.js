/**
 * Children's Ministry JavaScript Functions
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Children Ministry JS loaded');
});





// Edit relationship function
function editRelationship(relationshipId) {
    // For now, show a placeholder
    showNotification('Edit functionality will be implemented soon', 'info');
    
    // TODO: Implement edit functionality
    // This would involve:
    // 1. Fetch relationship data
    // 2. Populate modal with existing data
    // 3. Change form action to update endpoint
    // 4. Open modal
}

// Delete relationship function
function deleteRelationship(relationshipId) {
    if (confirm('Are you sure you want to delete this family relationship? This action cannot be undone.')) {
        // TODO: Implement delete functionality
        fetch(`/icgc/children-ministry/delete-relationship`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: relationshipId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Relationship deleted successfully', 'success');
                // Reload the page to reflect changes
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error deleting relationship', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting relationship', 'error');
        });
    }
}

// Show notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${getNotificationClasses(type)}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${getNotificationIcon(type)} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-auto text-lg">&times;</button>
        </div>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Get notification CSS classes based on type
function getNotificationClasses(type) {
    switch (type) {
        case 'success':
            return 'bg-green-100 border border-green-400 text-green-700';
        case 'error':
            return 'bg-red-100 border border-red-400 text-red-700';
        case 'warning':
            return 'bg-yellow-100 border border-yellow-400 text-yellow-700';
        default:
            return 'bg-blue-100 border border-blue-400 text-blue-700';
    }
}

// Get notification icon based on type
function getNotificationIcon(type) {
    switch (type) {
        case 'success':
            return 'check-circle';
        case 'error':
            return 'exclamation-circle';
        case 'warning':
            return 'exclamation-triangle';
        default:
            return 'info-circle';
    }
}





// Export families functionality
function exportFamilies() {
    showNotification('Export functionality will be implemented soon', 'info');
    // TODO: Implement export functionality
}

// Advanced search functionality
function initializeAdvancedSearch() {
    const searchInput = document.getElementById('searchFamilies');
    const departmentFilter = document.getElementById('filterByDepartment');

    if (searchInput) {
        searchInput.addEventListener('input', filterFamilies);
    }

    if (departmentFilter) {
        departmentFilter.addEventListener('change', filterFamilies);
    }
}

// Filter families based on search and filters
function filterFamilies() {
    const searchTerm = document.getElementById('searchFamilies')?.value.toLowerCase() || '';
    const departmentFilter = document.getElementById('filterByDepartment')?.value || '';
    const rows = document.querySelectorAll('.family-row');

    let visibleCount = 0;

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const departmentMatch = !departmentFilter || text.includes(departmentFilter.toLowerCase());
        const searchMatch = !searchTerm || text.includes(searchTerm);

        if (departmentMatch && searchMatch) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Update results count if element exists
    const resultsCount = document.getElementById('resultsCount');
    if (resultsCount) {
        resultsCount.textContent = `Showing ${visibleCount} of ${rows.length} families`;
    }
}

// Clear all filters
function clearFilters() {
    const searchInput = document.getElementById('searchFamilies');
    const departmentFilter = document.getElementById('filterByDepartment');

    if (searchInput) searchInput.value = '';
    if (departmentFilter) departmentFilter.value = '';

    document.querySelectorAll('.family-row').forEach(row => {
        row.style.display = '';
    });

    showNotification('Filters cleared', 'success');
}



// Delete family function
function deleteFamily(parentId) {
    if (confirm('Are you sure you want to delete this entire family and all its relationships? This action cannot be undone.')) {
        // Show loading state
        const button = event.target.closest('button');
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        }

        // Get the base URL dynamically
        const baseUrl = '/icgc/';

        // Create form with CSRF token for proper security
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = baseUrl + 'children-ministry/delete-family';

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        form.appendChild(csrfInput);

        // Add parent ID
        const parentInput = document.createElement('input');
        parentInput.type = 'hidden';
        parentInput.name = 'parent_id';
        parentInput.value = parentId;
        form.appendChild(parentInput);

        document.body.appendChild(form);
        form.submit();
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showNotification(data.message || 'Family deleted successfully', 'success');

                // Remove the row from the table immediately for better UX
                const row = button.closest('tr');
                if (row) {
                    row.style.opacity = '0.5';
                    row.style.transition = 'opacity 0.3s ease-out';
                }

                // Reload the page to reflect changes
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification(data.message || 'Error deleting family', 'error');
                // Restore button
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-trash"></i>';
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting family: ' + error.message, 'error');
            // Restore button
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-trash"></i>';
            }
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Children Ministry JS loaded');
    initializeAdvancedSearch();
});
