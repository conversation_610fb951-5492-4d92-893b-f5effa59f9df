<?php
/**
 * Create API Keys Table Migration
 */

use Phinx\Migration\AbstractMigration;

class CreateApiKeysTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     */
    public function change()
    {
        $table = $this->table('api_keys');
        $table->addColumn('user_id', 'integer')
              ->addColumn('api_key', 'string', ['limit' => 64])
              ->addColumn('description', 'string', ['limit' => 255])
              ->addColumn('permissions', 'string', ['limit' => 255, 'default' => 'read'])
              ->addColumn('last_used', 'datetime', ['null' => true])
              ->addColumn('tenant_id', 'integer', ['null' => true])
              ->addColumn('status', 'enum', ['values' => ['active', 'disabled'], 'default' => 'active'])
              ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
              ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
              ->addIndex(['api_key'], ['unique' => true])
              ->addIndex(['user_id'])
              ->addIndex(['tenant_id'])
              ->create();
    }
} 