/**
 * Attendance Trends Chart with Period Selection
 * Standalone script for attendance trends chart
 */



// Wait for everything to be ready
function initAttendanceChart() {
    // Track performance
    if (window.chartPerformance) {
        window.chartPerformance.start('Attendance');
    }

    console.log('🚀 Initializing attendance chart...');
    
    // Check if Chart.js is available
    if (typeof Chart === 'undefined') {
        console.log('⏳ Chart.js not available, retrying...');
        setTimeout(initAttendanceChart, 500);
        return;
    }
    
    // Get elements
    const canvas = document.getElementById('attendance-trend-chart');
    const dropdown = document.getElementById('attendance-period');
    
    console.log('Canvas found:', !!canvas);
    console.log('Dropdown found:', !!dropdown);
    
    if (!canvas || !dropdown) {
        console.log('⏳ Elements not ready, retrying...');
        setTimeout(initAttendanceChart, 500);
        return;
    }
    
    console.log('✅ All elements ready, setting up attendance chart...');
    
    // Check if canvas already has a chart and destroy it
    try {
        const existingChart = Chart.getChart(canvas);
        if (existingChart) {
            console.log('🗑️ Destroying existing attendance chart on canvas');
            existingChart.destroy();
        }
    } catch (error) {
        console.log('⚠️ Error checking for existing attendance chart:', error);
    }
    
    let chart = null;
    
    function updateChart(period) {
        console.log('📊 Updating attendance chart for period:', period);
        
        // Show loading
        canvas.style.opacity = '0.5';
        canvas.style.transition = 'opacity 0.3s ease';
        
        fetch(`api/dashboard_data.php?action=attendance_trends&period=${period}`)
            .then(response => {
                console.log('📡 Attendance API Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('📈 Attendance data received:', data);

                // Validate data
                if (!Array.isArray(data.labels) || !Array.isArray(data.data)) {
                    console.error('❌ Invalid data format - labels or data is not an array');
                    return;
                }

                if (data.labels.length !== data.data.length) {
                    console.error('❌ Data mismatch - labels and data arrays have different lengths');
                    return;
                }

                if (data.success && data.labels && data.data) {
                    // Destroy existing chart
                    if (chart) {
                        chart.destroy();
                        chart = null;
                    }

                    // Create new chart
                    chart = new Chart(canvas, {
                        type: 'line',
                        data: {
                            labels: data.labels,
                            datasets: [{
                                label: 'Attendance',
                                data: data.data,
                                backgroundColor: 'rgba(63, 125, 88, 0.1)',
                                borderColor: '#3F7D58',
                                borderWidth: 3,
                                tension: 0.4,
                                fill: true,
                                pointBackgroundColor: '#3F7D58',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 6,
                                pointHoverRadius: 8
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(17, 24, 39, 0.95)',
                                    titleColor: '#ffffff',
                                    bodyColor: '#ffffff',
                                    borderColor: '#3F7D58',
                                    borderWidth: 1,
                                    cornerRadius: 8,
                                    displayColors: true,
                                    callbacks: {
                                        title: function(context) {
                                            return context[0].label;
                                        },
                                        label: function(context) {
                                            return `Attendance: ${context.parsed.y} members`;
                                        },
                                        footer: function(context) {
                                            if (data.data && data.data.length > 0) {
                                                const value = context[0].parsed.y;
                                                const avg = data.data.reduce((a, b) => a + b, 0) / data.data.length;
                                                const diff = value - avg;
                                                const trend = diff > 0 ? '↗️' : diff < 0 ? '↘️' : '➡️';
                                                return `${trend} ${Math.abs(diff).toFixed(0)} vs avg (${avg.toFixed(0)})`;
                                            }
                                            return '';
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: 'rgba(156, 163, 175, 0.2)',
                                        borderColor: '#e5e7eb'
                                    },
                                    ticks: {
                                        precision: 0,
                                        color: '#6b7280',
                                        font: {
                                            size: 11,
                                            weight: '500'
                                        },
                                        callback: function(value) {
                                            return value + ' members';
                                        }
                                    },
                                    border: {
                                        color: '#e5e7eb'
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    },
                                    ticks: {
                                        color: '#6b7280',
                                        font: {
                                            size: 10,
                                            weight: '500'
                                        },
                                        maxRotation: 45
                                    },
                                    border: {
                                        color: '#e5e7eb'
                                    }
                                }
                            },
                            interaction: {
                                intersect: false,
                                mode: 'index'
                            },
                            animation: {
                                duration: 1000,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                    
                    console.log('✅ Attendance chart created successfully');

                    // Track performance end
                    if (window.chartPerformance) {
                        window.chartPerformance.end('Attendance');
                    }

                    // Mark canvas as having a chart
                    canvas.chart = chart;

                    // Force chart update to ensure proper rendering
                    setTimeout(() => {
                        if (chart) {
                            chart.update('active');
                        }
                    }, 200);
                } else {
                    console.error('❌ Attendance API Error:', data.error || 'No data available');
                    console.error('❌ Data received:', data);
                }
                
                // Hide loading
                canvas.style.opacity = '1';
            })
            .catch(error => {
                console.error('❌ Attendance Network Error:', error);
                canvas.style.opacity = '1';
            });
    }
    
    // Load initial data
    updateChart('monthly');
    
    // Add event listener to dropdown
    dropdown.addEventListener('change', function() {
        console.log('🔄 Attendance dropdown changed to:', this.value);
        updateChart(this.value);
    });
    
    console.log('✅ Attendance chart setup complete');
}

// Initialize chart when DOM is ready - optimized for speed
document.addEventListener('DOMContentLoaded', function() {
    // Check if Chart.js is loaded, if not wait briefly
    if (typeof Chart !== 'undefined') {
        initAttendanceChart();
    } else {
        setTimeout(initAttendanceChart, 200);
    }
});
