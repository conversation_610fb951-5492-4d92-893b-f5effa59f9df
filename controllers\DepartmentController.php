<?php
/**
 * Department Controller
 */

require_once 'models/Member.php';
require_once 'utils/validation.php';
require_once 'helpers/functions.php';

class DepartmentController {
    private $database;
    private $member;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Database();
        $this->member = new Member($this->database->getConnection());
    }

    /**
     * Display departments list
     *
     * @return void
     */
    public function index() {
        // Get all active departments from database
        $database = new Database();
        $conn = $database->getConnection();

        $stmt = $conn->prepare("SELECT name, display_name FROM departments WHERE is_active = 1 ORDER BY sort_order, display_name");
        $stmt->execute();
        $dept_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Initialize departments array and names mapping
        $departments = [];
        $department_names = [];

        foreach ($dept_data as $dept) {
            $departments[$dept['name']] = [];
            $department_names[$dept['name']] = $dept['display_name'];
        }

        // Get all members
        $stmt = $this->member->getAll();
        $members = $stmt->fetchAll();

        // Group members by department
        foreach ($members as $member) {
            $department = $member['department'];
            if (isset($departments[$department])) {
                $departments[$department][] = $member;
            } else {
                // Handle members with departments not in the active list
                if (!isset($departments['unknown'])) {
                    $departments['unknown'] = [];
                    $department_names['unknown'] = 'Unknown Department';
                }
                $departments['unknown'][] = $member;
            }
        }

        // Set page title and active page
        $page_title = getPageTitle('Departments');
        $active_page = 'departments';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/departments/index.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }

    /**
     * Display department details
     *
     * @return void
     */
    public function view() {
        // Check if department is provided
        if (!isset($_GET['department'])) {
            redirect('departments');
            exit;
        }
        
        $department = sanitize($_GET['department']);
        
        // Get members by department
        $stmt = $this->member->getByDepartment($department);
        $members = $stmt->fetchAll();

        // Get department display name from database
        $database = new Database();
        $conn = $database->getConnection();

        $stmt = $conn->prepare("SELECT display_name FROM departments WHERE name = ? AND is_active = 1");
        $stmt->execute([$department]);
        $dept_result = $stmt->fetch(PDO::FETCH_ASSOC);

        // Check if department exists
        if (!$dept_result) {
            redirect('departments');
            exit;
        }

        $department_name = $dept_result['display_name'];

        // Set page title and active page
        $page_title = getPageTitle($department_name . ' Department');
        $active_page = 'departments';

        // Start output buffering
        ob_start();

        // Load view
        require_once 'views/departments/view.php';

        // Get the contents of the output buffer
        $content = ob_get_clean();

        // Include the layout template
        include 'views/layouts/main.php';
    }
}
